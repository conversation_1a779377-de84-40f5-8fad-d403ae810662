import { Entity, Column } from 'typeorm';
import { BaseEntity, PlatformType, AppScopeType } from '../../shared';

@Entity({
  name: 'apps',
  comment: '应用表',
  orderBy: {
    order: 'ASC'
  }
})
export class App extends BaseEntity {
  @Column({ comment: '应用名称编码', unique: true })
  name: string;

  @Column({ comment: '应用名称描述' })
  label: string;

  @Column({ comment: '排序', default: 0 })
  order: number;

  @Column({ comment: 'Logo URL', nullable: true })
  logo: string;

  @Column('enum', {
    comment: '平台类型',
    enum: PlatformType,
    default: PlatformType.Web
  })
  platform: PlatformType;

  @Column({ comment: '图标', nullable: true })
  icon: string;

  @Column({ name: 'is_base', comment: '是否基础应用', default: false })
  isBase: boolean;

  @Column({ name: 'is_access', comment: '是否开启权限控制', default: false })
  isAccess: boolean;

  @Column({ comment: '备注', nullable: true })
  notes: string;

  @Column({ name: 'user_id', comment: 'UserId', nullable: true })
  userId: string;

  @Column('enum', {
    enum: AppScopeType,
    comment: '应用访问权限范围',
    default: AppScopeType.Protected
  })
  scope: AppScopeType;
}
