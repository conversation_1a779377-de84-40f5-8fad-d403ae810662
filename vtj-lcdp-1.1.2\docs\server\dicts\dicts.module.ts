import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DictsService } from './dicts.service';
import { DictsController } from './dicts.controller';
import { Dict } from './entities/dict.entity';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([Dict])],
  controllers: [DictsController],
  providers: [DictsService],
  exports: [TypeOrmModule, DictsService]
})
export class DictsModule {}
