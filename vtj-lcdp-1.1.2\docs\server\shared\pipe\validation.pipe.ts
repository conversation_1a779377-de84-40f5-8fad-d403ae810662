import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException
} from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

@Injectable()
export class ValidationPipe implements PipeTransform<any> {
  async transform(value: any, metadata: ArgumentMetadata) {
    const metatype = metadata.metatype;
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }
    const obj = plainToClass(metatype, value);

    const errors = await validate(obj);

    if (errors.length > 0) {
      const errorMsg = errors.map((item) => {
        return Object.values(item.constraints);
      });
      throw new BadRequestException(errorMsg.flat());
    }

    return value;
  }

  private toValidate(metatype: any) {
    const types: any[] = [String, Boolean, Number, Array, Object];
    return !types.includes(metatype);
  }
}
