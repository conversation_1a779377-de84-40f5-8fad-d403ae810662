import {
  Controller,
  Post,
  Body,
  Get,
  Delete,
  Query,
  Param
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { QueryOrderDto } from './dto/query.order.dto';
import { QuickOrderDto } from './dto/quick.order.dto';
import { OrderStatus } from './interfaces/types';

@Controller('orders')
export class OrdersController {
  constructor(private readonly service: OrdersService) {}

  @Post('quick')
  quick(@Body() dto: QuickOrderDto) {
    return this.service.quickOrder(dto);
  }

  @Get()
  find(@Query() dto: QueryOrderDto) {
    return this.service.find(dto);
  }

  @Delete()
  remove(@Body() ids: string[]) {
    return this.service.remove(ids);
  }

  @Post('status/:id')
  status(@Param('id') id: string, @Body('status') status: string) {
    return this.service.updateStatus(id, status as OrderStatus);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }
}
