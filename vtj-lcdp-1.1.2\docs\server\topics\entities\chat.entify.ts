import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../shared';
import { ChatStatus } from '../types/constants';

@Entity({
  name: 'chats',
  comment: '对话记录',
  orderBy: {
    createdAt: 'ASC'
  }
})
export class Chat extends BaseEntity {
  @Index()
  @Column({ name: 'topic_id', comment: '话题Id' })
  topicId: string;

  @Index()
  @Column({ name: 'user_id', comment: '用户Id' })
  userId: string;

  @Column({ name: 'user_name', comment: '用户名' })
  userName: string;

  @Column({ type: 'text', comment: '用户提示词' })
  prompt: string;

  @Column({ type: 'enum', enum: ChatStatus, comment: '对话状态' })
  status: ChatStatus;

  @Column({ type: 'text', comment: '回复内容', nullable: true })
  content: string;

  @Column({ type: 'text', comment: '推理内容', nullable: true })
  reasoning: string;

  @Column('json', { comment: '代码DSL', nullable: true })
  dsl: object;

  @Column({ comment: '消耗token数', default: 0 })
  tokens: number;

  @Column({ comment: '错误信息', nullable: true })
  message: string;

  @Column({ comment: '深度思考用时，单位秒', default: 0 })
  thinking: number;
}
