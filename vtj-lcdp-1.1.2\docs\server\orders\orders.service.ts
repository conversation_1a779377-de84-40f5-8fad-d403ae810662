import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, MoreThanOrEqual, Like } from 'typeorm';
import { UsersService } from '../users/users.service';
import { Order } from './entities/order.entity';
import { OrderDto } from './dto/order.dto';
import { QueryOrderDto } from './dto/query.order.dto';
import { QuickOrderDto } from './dto/quick.order.dto';
import { dayjs } from '@vtj/node';
import { OrderStatus, QuickOrderTokenType } from './interfaces/types';
import { pager } from '../shared';
import { SettingsService } from '../settings/settings.service';

@Injectable()
export class OrdersService {
  constructor(
    @InjectRepository(Order) private repo: Repository<Order>,
    private users: UsersService,
    private settings: SettingsService
  ) {}
  async save(dto: OrderDto) {
    const order = new Order();
    order.id = dto.id;
    order.userId = dto.userId;
    order.status = dto.status;
    order.tokens = dto.tokens || 0;
    order.start = dto.start;
    order.end = dto.end;
    if (dto.month) {
      order.end = dayjs(dto.start).add(dto.month, 'month').toDate();
    }
    if (dto.userId) {
      const user = await this.users.getUserById(dto.userId);
      order.userName = user?.name;
    }

    return await this.repo.save(order);
  }

  getUserValidOrders(userId: string) {
    const now = new Date();
    return this.repo.find({
      where: {
        userId,
        status: OrderStatus.Completed,
        end: MoreThanOrEqual(now)
      },
      order: {
        end: 'DESC'
      }
    });
  }

  getUserNowValidOrders(userId: string) {
    const now = new Date();
    return this.repo.find({
      where: {
        userId,
        status: OrderStatus.Completed,
        start: LessThanOrEqual(now),
        end: MoreThanOrEqual(now)
      },
      order: {
        end: 'DESC'
      }
    });
  }

  async find(dto: QueryOrderDto) {
    const { page, limit, skip, take } = pager(dto);
    const { userName, status, userId } = dto;
    const [list, total] = await this.repo.findAndCount({
      skip,
      take,
      where: {
        userName: userName ? Like(`%${userName}%`) : undefined,
        userId,
        status
      },
      order: {
        createdAt: 'DESC'
      }
    });

    return {
      list,
      total,
      page,
      limit
    };
  }

  findOne(id: string) {
    return this.repo.findOneBy({ id });
  }

  async updateStatus(id: string, status: OrderStatus) {
    const row = await this.findOne(id);
    row.status = status;
    return this.repo.save(row);
  }

  async remove(id: string | string[]) {
    return this.repo.delete(id);
  }

  async quickOrder(dto: QuickOrderDto) {
    const { name: userName, type } = dto;
    const user = await this.users.getUserByName(userName.trim());
    if (!user) {
      throw new BadRequestException('用户不存在');
    }
    const orders = await this.getUserValidOrders(user.id);
    const order = new Order();
    order.userId = user.id;
    order.userName = user.name;
    order.status = OrderStatus.Completed;
    order.start = orders.length ? orders[0].end : new Date();
    order.end = dayjs(order.start).add(1, 'month').toDate();
    const settings = await this.settings.get();
    let maxTokens = 0;
    switch (type) {
      case QuickOrderTokenType.S:
        // 按配置，多少百万
        maxTokens = (settings?.max || 0) * 100 * 10000;
        break;
      case QuickOrderTokenType.M:
        // 一百万
        maxTokens = 100 * 10000;
        break;
      case QuickOrderTokenType.K:
        // 10 万
        maxTokens = 10 * 10000;
        break;
      case QuickOrderTokenType.MM:
        // 二百万
        maxTokens = 2 * 100 * 10000;
        break;
      case QuickOrderTokenType.MMM:
        // 三百万
        maxTokens = 3 * 100 * 10000;
        break;
      case QuickOrderTokenType.MMMMM:
        // 三百万
        maxTokens = 5 * 100 * 10000;
        break;
    }
    order.tokens = maxTokens;
    return await this.repo.save(order);
  }

  async createUserOrder(userId: string, userName: string) {
    // 删除所有未支付的订单
    await this.repo.delete({
      userId,
      status: OrderStatus.Pending
    });
    const order = new Order();
    order.userId = userId;
    order.userName = userName;
    order.status = OrderStatus.Pending;
    order.start = new Date();
    order.end = dayjs(order.start).add(1, 'month').toDate();
    const settings = await this.settings.get();
    const maxTokens = settings?.max || 0;
    order.tokens = maxTokens * 100 * 10000;
    return await this.repo.save(order);
  }

  async cancelUserOrder(id: string, userId: string) {
    const order = await this.repo.findOneBy({ id, userId });
    if (order) {
      order.status = OrderStatus.Canceled;
      return await this.repo.save(order);
    }
    return new BadRequestException('订单不存在');
  }

  async useTokens(userId: string, tokens: number) {
    const orders = await this.getUserNowValidOrders(userId);
    const order = orders.find((n) => n.tokens > 0);
    if (order) {
      order.tokens = Math.max(order.tokens - tokens, 0);
      return await this.repo.save(order);
    }
  }
}
