import {
  $l,
  al,
  id,
  nd,
  qc,
  rd,
  t0,
  td,
  yd
} from "./chunk-6QPGK3DF.js";
import {
  __export,
  __publicField
} from "./chunk-LK32TJAX.js";

// node_modules/@vtj/core/dist/index.mjs
var dist_exports = {};
__export(dist_exports, {
  BUILT_IN_COMPONENTS: () => Q,
  BUILT_IN_LIBRARAY_MAP: () => z,
  BUILT_IN_MATERIALS: () => q,
  BUILT_IN_NAME: () => W,
  BUILT_IN_TAGS: () => X,
  BUILT_IN_VUE: () => H,
  BUILT_IN_VUE_ROUTER: () => J,
  Base: () => ee,
  BlockModel: () => u,
  DirectiveModel: () => v,
  EVENT_BLOCK_CHANGE: () => r,
  EVENT_HISTORY_CHANGE: () => N,
  EVENT_HISTORY_LOAD: () => V,
  EVENT_NODE_CHANGE: () => h,
  EVENT_PROJECT_ACTIVED: () => D,
  EVENT_PROJECT_APIS_CHANGE: () => b,
  EVENT_PROJECT_BLOCKS_CHANGE: () => y,
  EVENT_PROJECT_CHANGE: () => l,
  EVENT_PROJECT_DEPS_CHANGE: () => B,
  EVENT_PROJECT_FILE_PUBLISH: () => $,
  EVENT_PROJECT_GEN_SOURCE: () => M,
  EVENT_PROJECT_META_CHANGE: () => w,
  EVENT_PROJECT_PAGES_CHANGE: () => g,
  EVENT_PROJECT_PUBLISH: () => j,
  EventModel: () => T,
  HistoryModel: () => te,
  NodeModel: () => _,
  ProjectModel: () => S,
  PropModel: () => x,
  Service: () => Z,
  VTJ_CORE_VERSION: () => Y,
  cloneDsl: () => L,
  createNodeFrom: () => ae,
  emitter: () => n,
  isBlock: () => ie,
  isBlockSchema: () => ne,
  isNode: () => se
});
var Y = "0.12.47";
var W = "BuiltIn";
var H = "VueMaterial";
var J = "VueRouterMaterial";
var q = [H, J];
var z = {
  vue: "Vue",
  "vue-router": "VueRouter"
};
var Q = {
  [H]: [
    "Transition",
    "TransitionGroup",
    "KeepAlive",
    "Teleport",
    "Suspense"
  ],
  [J]: ["RouterView", "RouterLink"]
};
var X = [
  "slot",
  "template",
  "component",
  "img",
  "div",
  "p",
  "h1",
  "h2",
  "h3",
  "span",
  "a"
];
var Z = class {
};
var ee = class {
  constructor() {
    __publicField(this, "listeners", []);
    __publicField(this, "isReady", false);
  }
  triggerReady() {
    this.isReady = true;
    for (const e of this.listeners)
      e();
    this.listeners = [];
  }
  ready(e) {
    this.isReady ? e() : this.listeners.push(e);
  }
  resetReady() {
    this.isReady = false;
  }
};
var x = class _x {
  constructor(e, t, i) {
    /**
     * 标识是否设置了值， 设置的值与默认值一致，表示未设置，在转换成dsl时会排查该属性
     */
    __publicField(this, "isUnset", false);
    this.name = e, this.value = t, this.defaultValue = i, this.setValue(t);
  }
  setValue(e) {
    this.value = e, this.isUnset = this.value === this.defaultValue;
  }
  getValue() {
    return this.value ?? this.defaultValue;
  }
  static toDsl(e = {}) {
    return Object.entries(e).reduce((t, [i, s]) => (s.isUnset || (t[i] = s.getValue()), t), {});
  }
  static parse(e = {}) {
    return Object.entries(e).reduce((t, [i, s]) => (t[i] = new _x(i, s), t), {});
  }
};
var T = class _T {
  constructor(e) {
    __publicField(this, "name");
    __publicField(this, "handler");
    __publicField(this, "modifiers", {});
    this.schema = e;
    const { name: t, handler: i } = this.schema;
    this.name = t, this.handler = i, this.update(e);
  }
  update(e) {
    Object.assign(this.schema, e);
    const { handler: t, modifiers: i = {} } = this.schema;
    this.handler = t, this.modifiers = i;
  }
  static toDsl(e) {
    return Object.entries(e).reduce((t, [i, s]) => {
      const { handler: a, modifiers: o } = s;
      return t[i] = { name: i, handler: a, modifiers: o }, t;
    }, {});
  }
  static parse(e = {}) {
    return Object.entries(e).reduce(
      (t, [i, s]) => (t[i] = new _T(s), t),
      {}
    );
  }
};
var v = class _v {
  constructor(e) {
    /**
     * 标识
     */
    __publicField(this, "id");
    /**
     * 指令名称
     */
    __publicField(this, "name", "");
    /**
     * 参数
     */
    __publicField(this, "arg");
    /**
     * 修饰符
     */
    __publicField(this, "modifiers");
    /**
     * 指令值
     */
    __publicField(this, "value");
    /**
     *  v-for 迭代器
     */
    __publicField(this, "iterator");
    this.schema = e, this.id = e.id || rd(), this.update(e);
  }
  update(e) {
    Object.assign(this.schema, e);
    const { name: t, arg: i, modifiers: s, value: a, iterator: o } = this.schema;
    this.name = t, this.arg = i, this.modifiers = s, this.value = a, this.iterator = o;
  }
  static parse(e = []) {
    return e.map((t) => new _v(t));
  }
  static toDsl(e = []) {
    return e.map((t) => {
      const { name: i, arg: s, modifiers: a, value: o, iterator: d, id: c } = t;
      return {
        id: c,
        name: i,
        arg: s,
        modifiers: a,
        value: o,
        iterator: d
      };
    });
  }
};
var h = "EVENT_NODE_CHANGE";
var __ = class __ {
  constructor(e, t = null) {
    /**
     * 标记
     */
    __publicField(this, "__VTJ_NODE__", true);
    /**
     * 锁定
     */
    __publicField(this, "locked", false);
    /**
     * 节点唯一标识
     */
    __publicField(this, "id");
    /**
     * 名称，即组件的名称或html的标签名
     */
    __publicField(this, "name");
    /**
     * 组件来源
     */
    __publicField(this, "from");
    /**
     * 是否不可见
     */
    __publicField(this, "invisible", false);
    /**
     * 子节点
     */
    __publicField(this, "children", "");
    /**
     * 放置在父组件的插槽
     */
    __publicField(this, "slot");
    /**
     * 节点属性
     */
    __publicField(this, "props", {});
    /**
     * 节点事件
     */
    __publicField(this, "events", {});
    /**
     * 指令
     */
    __publicField(this, "directives", []);
    /**
     * 销毁标识
     */
    __publicField(this, "disposed", false);
    this.parent = t;
    const { id: i = rd(), name: s, from: a = "" } = e;
    this.id = i, this.name = s, this.from = a, this.update(e, true), __.nodes[this.id] = this;
  }
  /**
   * 更新节点属性
   * @param schema
   * @param silent 是否静默，静默更新即不触发事件
   */
  update(e, t = false) {
    const {
      invisible: i = false,
      locked: s = false,
      children: a = [],
      slot: o,
      props: d = {},
      events: c = {},
      directives: p = []
    } = e;
    this.invisible = i, this.locked = s, this.setChildren(a, true), this.setSlot(o, true), this.props = x.parse(d), this.events = T.parse(c), this.directives = v.parse(p), t || n.emit(h, this);
  }
  /**
   * 设置子节点
   * @param children
   * @param silent
   */
  setChildren(e = "", t = false) {
    Array.isArray(e) ? this.children = e.map((i) => new __(i, this)) : this.children = e, t || n.emit(h, this);
  }
  /**
   * 设置节点放置的插槽
   * @param slot
   * @param silent
   */
  setSlot(e, t = false) {
    this.slot = typeof e == "string" ? { name: e, params: [] } : e, t || n.emit(h, this);
  }
  /**
   * 新增或更新属性
   * @param name
   * @param value
   * @param defaultValue
   * @param silent
   */
  setProp(e, t, i, s = false) {
    const a = this.props[e];
    a ? a.setValue(t) : this.props[e] = new x(e, t, i), s || n.emit(h, this);
  }
  /**
   * 删除属性
   * @param name
   * @param silent
   */
  removeProp(e, t = false) {
    delete this.props[e], t || n.emit(h, this);
  }
  /**
   * 获取属性值
   * @param name
   * @returns
   */
  getPropValue(e) {
    const t = this.props[e];
    if (t)
      return t.getValue();
  }
  /**
   * 新增或更新事件
   * @param scheam
   * @param silent
   */
  setEvent(e, t = false) {
    const i = this.events[e.name];
    i ? i.update(e) : this.events[e.name] = new T(e), t || n.emit(h, this);
  }
  /**
   * 删除事件
   * @param name
   * @param silent
   */
  removeEvent(e, t = false) {
    delete this.events[e], t || n.emit(h, this);
  }
  /**
   * 新增或更新指令
   * @param scheam
   * @param silent
   */
  setDirective(e, t = false) {
    const i = e instanceof v ? e : new v(e), s = this.directives.findIndex((a) => a.id === e.id);
    s >= 0 ? this.directives.splice(s, 1, i) : this.directives.push(i), t || n.emit(h, this);
  }
  /**
   * 删除指令
   * @param dirctive
   * @param silent
   */
  removeDirective(e, t = false) {
    const i = this.directives.findIndex(
      (s) => s === e || s.id === e.id
    );
    i >= 0 && this.directives.splice(i, 1), t || n.emit(h, this);
  }
  /**
   * 删除子节点
   * @param node
   * @param silent
   * @returns
   */
  removeChild(e, t = false) {
    const { children: i, disposed: s } = this;
    if (s || !Array.isArray(i)) return;
    const a = i.findIndex((o) => o === e);
    e.parent = null, i.splice(a, 1), t || n.emit(h, this);
  }
  /**
   * 追加子节点
   * @param node
   * @param silent
   * @returns
   */
  appendChild(e, t = false) {
    const { children: i, disposed: s } = this;
    s || (e.parent = this, Array.isArray(i) ? i.push(e) : this.children = [e], t || n.emit(h, this));
  }
  /**
   * 在当前节点的后面插入节点
   * @param node
   * @param silent
   * @returns
   */
  insertAfter(e, t = false) {
    if (!this.parent) return;
    const i = this.parent.children;
    if (Array.isArray(i)) {
      e.parent = this.parent;
      const s = i.indexOf(this);
      i.splice(s + 1, 0, e), t || n.emit(h, this.parent);
    }
  }
  /**
   * 在当前节点的前面插入节点
   * @param node
   * @param silent
   * @returns
   */
  insertBefore(e, t = false) {
    if (!this.parent) return;
    const i = this.parent.children;
    if (Array.isArray(i)) {
      e.parent = this.parent;
      const s = i.indexOf(this);
      i.splice(s, 0, e), t || n.emit(h, this.parent);
    }
  }
  movePrev(e = false) {
    const t = this.parent;
    if (!t) return;
    const i = t.children;
    if (Array.isArray(i)) {
      const s = i.indexOf(this);
      s > 0 && (i.splice(s, 1), i.splice(s - 1, 0, this), e || n.emit(h, t));
    }
  }
  moveNext(e = false) {
    const t = this.parent;
    if (!t) return;
    const i = t.children;
    if (Array.isArray(i)) {
      const s = i.indexOf(this);
      s > -1 && s < i.length - 1 && (i.splice(s, 1), i.splice(s + 1, 0, this), e || n.emit(h, t));
    }
  }
  /**
   * 获取DSL
   * @returns
   */
  toDsl() {
    const {
      id: e,
      name: t,
      from: i,
      invisible: s,
      locked: a,
      slot: o,
      children: d,
      props: c,
      directives: p,
      events: O
    } = this, E = Array.isArray(d) ? d.map((U) => U.toDsl()) : d;
    return {
      id: e,
      name: t,
      from: i,
      invisible: s,
      locked: a,
      slot: o,
      children: E,
      props: x.toDsl(c),
      directives: v.toDsl(p),
      events: T.toDsl(O)
    };
  }
  /**
   * 销毁
   * @param silent
   * @returns
   */
  dispose(e = false) {
    const { children: t, disposed: i } = this;
    i || (Array.isArray(t) && t.forEach((s) => s.dispose(true)), this.parent ? this.parent.removeChild(this, e) : e || n.emit(h, this), this.parent = null, this.disposed = true, delete __.nodes[this.id]);
  }
  lock(e = false) {
    if (this.locked = true, Array.isArray(this.children))
      for (const t of this.children)
        t.lock(true);
    e || n.emit(h, this);
  }
  unlock(e = false) {
    if (this.locked = false, Array.isArray(this.children))
      for (const t of this.children)
        t.unlock(true);
    e || n.emit(h, this);
  }
  setVisible(e, t = false) {
    if (this.invisible = !e, Array.isArray(this.children))
      for (const i of this.children)
        i.setVisible(e, true);
    t || n.emit(h, this);
  }
  isChild(e) {
    let t = false;
    if (Array.isArray(this.children)) {
      for (const i of this.children)
        if (e === i || e.id === i.id) {
          t = true;
          break;
        } else if (t = i.isChild(e), t)
          break;
    }
    return t;
  }
};
/**
 * 记录所有节点的实例
 */
__publicField(__, "nodes", {});
var _ = __;
var r = "EVENT_BLOCK_CHANGE";
var _u = class _u {
  constructor(e) {
    __publicField(this, "__VTJ_BLOCK__", true);
    __publicField(this, "id");
    __publicField(this, "name", "");
    __publicField(this, "inject", []);
    __publicField(this, "state", {});
    __publicField(this, "lifeCycles", {});
    __publicField(this, "methods", {});
    __publicField(this, "computed", {});
    __publicField(this, "watch", []);
    __publicField(this, "css", "");
    __publicField(this, "props", []);
    __publicField(this, "emits", []);
    __publicField(this, "slots", []);
    __publicField(this, "dataSources", {});
    __publicField(this, "nodes", []);
    __publicField(this, "locked", false);
    __publicField(this, "disposed", false);
    const { id: t } = e;
    this.id = t || rd(), this.update(e, true);
  }
  update(e, t = false) {
    for (const s of _u.normalAttrs) {
      const a = e[s];
      a !== void 0 && (this[s] = a);
    }
    const { nodes: i = [] } = e;
    i.length && (this.nodes = i.map((s) => new _(s))), t || n.emit(r, this);
  }
  /**
   * 获取DSL
   * @returns
   */
  toDsl(e) {
    const { __VTJ_BLOCK__: t, id: i, nodes: s } = this;
    return {
      ..._u.normalAttrs.reduce(
        (o, d) => (o[d] = this[d], o),
        {}
      ),
      __VTJ_BLOCK__: t,
      __VERSION__: e || td().toString(),
      id: i,
      nodes: s.map((o) => o.toDsl())
    };
  }
  /**
   * 销毁
   */
  dispose() {
    this.nodes.map((e) => e.dispose(true)), this.nodes = [], this.disposed = true;
  }
  /**
   * 设置通用函数属性
   * @param type
   * @param name
   * @param value
   * @param silent
   */
  setFunction(e, t, i, s = false) {
    this[e][t] = i, s || n.emit(r, this);
  }
  /**
   * 删除通用函数属性
   * @param type
   * @param name
   * @param silent
   */
  removeFunction(e, t, i = false) {
    delete this[e][t], i || n.emit(r, this);
  }
  /**
   * 设置状态
   * @param name
   * @param value
   * @param silent
   */
  setState(e, t, i = false) {
    this.state[e] = t, i || n.emit(r, this);
  }
  /**
   * 删除状态
   * @param name
   * @param silent
   */
  removeState(e, t = false) {
    delete this.state[e], t || n.emit(r, this);
  }
  /**
   * 更新CSS
   * @param content
   * @param silent
   */
  setCss(e, t = false) {
    this.css = e, t || n.emit(r, this);
  }
  /**
   * 新增或更新 watch
   * @param watch
   * @param silent
   */
  setWatch(e, t = false) {
    e.id = e.id || rd();
    const i = this.watch.findIndex(
      (s) => s.id && s.id === e.id || s === e
    );
    i > -1 ? this.watch.splice(i, 1, e) : this.watch.push(e), t || n.emit(r, this);
  }
  /**
   * 删除 watch
   * @param watch
   * @param silent
   */
  removeWatch(e, t = false) {
    const i = this.watch.findIndex(
      (s) => s.id && s.id === e.id || s === e
    );
    i > -1 && (this.watch.splice(i, 1), t || n.emit(r, this));
  }
  /**
   * 定义属性参数
   * @param prop
   * @param silent
   */
  setProp(e, t = false) {
    const i = this.props.findIndex(
      (s) => typeof s == "string" ? s === e.name : s.name === e.name
    );
    i > -1 ? this.props.splice(i, 1, e) : this.props.push(e), t || n.emit(r, this);
  }
  /**
   * 删除属性
   * @param prop
   * @param silent
   */
  removeProp(e, t = false) {
    const i = this.props.findIndex(
      (s) => typeof s == "string" ? s === e.name : s.name === e.name
    );
    i > -1 && (this.props.splice(i, 1), t || n.emit(r, this));
  }
  /**
   * 设置事件
   * @param emit
   * @param silent
   */
  setEmit(e, t = false) {
    const i = al(e) ? { name: e, params: [] } : e, s = this.emits.findIndex((a) => a === i.name || a === i);
    s > -1 ? this.emits.splice(s, 1, i) : this.emits.push(i), t || n.emit(r, this);
  }
  /**
   * 删除事件
   * @param emit
   * @param silent
   */
  removeEmit(e, t = false) {
    const i = this.emits.findIndex(
      (s) => al(s) ? s === e : s.name === e
    );
    i > -1 && (this.emits.splice(i, 1), t || n.emit(r, this));
  }
  /**
   * 设置插槽
   * @param slot
   * @param silent
   */
  setSlot(e, t = false) {
    const i = al(e) ? { name: e, params: [] } : e, s = this.slots.findIndex((a) => a === i.name || a === i);
    s > -1 ? this.slots.splice(s, 1, i) : this.slots.push(i), t || n.emit(r, this);
  }
  /**
   * 删除插槽
   * @param slot
   * @param silent
   */
  removeSlot(e, t = false) {
    const i = this.slots.findIndex(
      (s) => al(s) ? s === e : s.name === e
    );
    i > -1 && (this.slots.splice(i, 1), t || n.emit(r, this));
  }
  /**
   * 设置注入
   * @param inject
   * @param silent
   */
  setInject(e, t = false) {
    const i = this.inject.findIndex((s) => s.name === e.name);
    i > -1 ? this.inject.splice(i, 1, e) : this.inject.push(e), t || n.emit(r, this);
  }
  /**
   * 删除注入
   * @param inject
   * @param silent
   */
  removeInject(e, t = false) {
    const i = this.inject.findIndex((s) => s.name === e.name);
    i > -1 && (this.inject.splice(i, 1), t || n.emit(r, this));
  }
  /**
   * 设置数据源
   * @param source
   * @param silent
   */
  setDataSource(e, t = false) {
    this.dataSources[e.name] = e, t || n.emit(r, this);
  }
  /**
   * 删除数据源
   * @param name
   * @param silent
   */
  removeDataSource(e, t = false) {
    delete this.dataSources[e], t || n.emit(r, this);
  }
  insertAfter(e, t, i = false) {
    e.parent = null;
    const s = this.nodes.indexOf(t);
    this.nodes.splice(s + 1, 0, e), i || n.emit(r, this);
  }
  insertBefore(e, t, i = false) {
    e.parent = null;
    const s = this.nodes.indexOf(t);
    this.nodes.splice(s, 0, e), i || n.emit(r, this);
  }
  appendNode(e, t = false) {
    e.parent = null, this.nodes.push(e), t || n.emit(r, this);
  }
  /**
   * 添加节点
   * @param node
   * @param target
   * @param position
   * @param silent
   */
  addNode(e, t, i = "inner", s = false) {
    t ? ["left", "top"].includes(i) ? t.parent ? t.insertAfter(e, s) : this.insertBefore(e, t, s) : ["right", "bottom"].includes(i) ? t.parent ? t.insertAfter(e, s) : this.insertAfter(e, t, s) : t.appendChild(e, s) : this.appendNode(e, s);
  }
  __removeNode(e, t = false) {
    const i = this.nodes.findIndex((s) => s.id === e.id);
    i > -1 && (this.nodes.splice(i, 1), t || n.emit(r, this));
  }
  /**
   * 删除节点
   * @param node
   * @param silent
   */
  removeNode(e, t = false) {
    e.parent ? e.dispose(t) : (e.dispose(true), this.__removeNode(e, t));
  }
  /**
   * 移动节点
   * @param node
   * @param target
   * @param position
   * @param silent
   */
  move(e, t, i = "inner", s = false) {
    e.parent ? e.parent.removeChild(e, true) : this.__removeNode(e, true), this.addNode(e, t, i, s);
  }
  /**
   * 向前交换节点
   * @param node
   * @param silent
   */
  movePrev(e, t = false) {
    if (e.parent)
      e.movePrev(t);
    else {
      const i = this.nodes, s = i.indexOf(e);
      s > 0 && (i.splice(s, 1), i.splice(s - 1, 0, e), t || n.emit(r, this));
    }
  }
  /**
   * 向后交换节点
   * @param node
   * @param silent
   */
  moveNext(e, t = false) {
    if (e.parent)
      e.moveNext(t);
    else {
      const i = this.nodes, s = i.indexOf(e);
      s > -1 && s < i.length - 1 && (i.splice(s, 1), i.splice(s + 1, 0, e), t || n.emit(r, this));
    }
  }
  /**
   * 克隆节点
   * @param target
   * @param silent
   * @returns
   */
  cloneNode(e, t = false) {
    const i = L(e.toDsl()), s = new _(i);
    return this.addNode(s, e, "bottom", t), s;
  }
  lock(e = false) {
    this.locked = true;
    for (const t of this.nodes)
      t.lock(true);
    e || n.emit(r, this);
  }
  unlock(e = false) {
    this.locked = false;
    for (const t of this.nodes)
      t.unlock(true);
    e || n.emit(r, this);
  }
  isChild(e) {
    let t = false;
    for (const i of this.nodes)
      if (e === i || e.id === i.id) {
        t = true;
        break;
      } else if (t = i.isChild(e), t)
        break;
    return t;
  }
};
__publicField(_u, "normalAttrs", [
  "name",
  "locked",
  "inject",
  "state",
  "lifeCycles",
  "methods",
  "computed",
  "watch",
  "css",
  "props",
  "emits",
  "slots",
  "dataSources",
  "__TEMPLATE_ID__"
]);
var u = _u;
var l = "EVENT_PROJECT_CHANGE";
var D = "EVENT_PROJECT_ACTIVED";
var B = "EVENT_PROJECT_DEPS_CHANGE";
var g = "EVENT_PROJECT_PAGES_CHANGE";
var y = "EVENT_PROJECT_BLOCKS_CHANGE";
var b = "EVENT_PROJECT_APIS_CHANGE";
var w = "EVENT_PROJECT_META_CHANGE";
var j = "EVENT_PROJECT_PUBLISH";
var $ = "EVENT_PROJECT_FILE_PUBLISH";
var M = "EVENT_PROJECT_GEN_SOURCE";
var _S = class _S {
  constructor(e) {
    __publicField(this, "id", "");
    __publicField(this, "locked", "");
    __publicField(this, "platform", "web");
    __publicField(this, "name", "");
    __publicField(this, "description", "");
    __publicField(this, "homepage", "");
    __publicField(this, "dependencies", []);
    __publicField(this, "pages", []);
    __publicField(this, "blocks", []);
    __publicField(this, "apis", []);
    __publicField(this, "meta", []);
    __publicField(this, "currentFile", null);
    __publicField(this, "config", {});
    __publicField(this, "uniConfig", {});
    __publicField(this, "__BASE_PATH__", "/");
    __publicField(this, "__UID__", nd(true));
    const { id: t, __UID__: i } = e;
    this.id = t || rd(), this.__UID__ = i || nd(true), this.update(e, true);
  }
  update(e, t = false) {
    for (const i of _S.attrs) {
      const s = e[i];
      s && (this[i] = s);
    }
    t || n.emit(l, {
      model: this,
      type: "update",
      data: e
    });
  }
  isPageFile(e) {
    return e.type === "page";
  }
  cleanPagesDsl(e) {
    for (const t of e)
      delete t.dsl, t.children && t.children.length && this.cleanPagesDsl(t.children);
  }
  toDsl(e) {
    const { id: t } = this, i = _S.attrs.reduce(
      (s, a) => (s[a] = this[a], s),
      {}
    );
    return i.pages && (i.pages = i.pages.map((s) => qc({
      ...s,
      dsl: void 0
    })), this.cleanPagesDsl(i.pages)), i.blocks && (i.blocks = i.blocks.map((s) => qc({
      ...s,
      dsl: void 0
    }))), {
      __VTJ_PROJECT__: true,
      id: t,
      ...i
    };
  }
  /**
   * 打开文件
   * @param file
   * @param silent
   */
  active(e, t = false) {
    this.currentFile = e, t || n.emit(D, {
      model: this,
      type: "update",
      data: e
    });
  }
  /**
   * 关闭文件
   * @param silent
   */
  deactivate(e = false) {
    this.currentFile = null, e || n.emit(D, {
      model: this,
      type: "update",
      data: null
    });
  }
  /**
   * 新增或更新依赖
   * @param item
   * @param silent
   */
  setDeps(e, t = false) {
    const i = this.dependencies, s = i.findIndex((o) => o.package === e.package);
    let a;
    if (s > -1 ? (a = "update", i.splice(s, 1, {
      ...i[s],
      ...e
    })) : (a = "create", i.push(e)), !t) {
      const o = {
        model: this,
        type: a,
        data: e
      };
      n.emit(B, o), n.emit(l, o);
    }
  }
  /**
   * 删除依赖
   * @param item
   * @param silent
   */
  removeDeps(e, t = false) {
    const i = this.dependencies, s = i.findIndex((a) => a.package === e.package);
    if (s > -1 && i.splice(s, 1), !t) {
      const a = {
        model: this,
        type: "delete",
        data: e
      };
      n.emit(B, a), n.emit(l, a);
    }
  }
  /**
   * 根据页面id查找页面或目录
   * @param id
   * @returns
   */
  getPage(e) {
    const t = (i, s = []) => {
      for (const a of s) {
        if (a.id === i)
          return a;
        if (a.children && a.children.length) {
          const o = t(i, a.children);
          if (o)
            return o;
        }
      }
    };
    return t(e, this.pages);
  }
  /**
   * 查找全部页面，不含目录
   * @returns
   */
  getPages() {
    const e = (t = []) => {
      let i = [];
      for (const s of t)
        s.dir ? s.children && s.children.length && (i = i.concat(e(s.children))) : i.push(s);
      return i;
    };
    return e(this.pages);
  }
  /**
   * 新建页面
   * @param page
   * @param parentId
   * @param silent
   */
  async createPage(e, t, i = false) {
    if (e.id = e.raw ? e.name : e.id || rd(), e.type = "page", e.dir ? e.children = [] : e.dsl = e.dsl || new u({
      id: e.id,
      name: t0(e.name)
    }).toDsl(), t) {
      const s = this.getPage(t);
      s ? s.children ? s.children.push(e) : s.children = [e] : console.warn(`not found PageFile for id: ${t} `);
    } else
      this.pages.push(e);
    if (!i) {
      const s = {
        model: this,
        type: "create",
        data: e
      };
      n.emit(g, s), n.emit(l, s);
    }
    !this.currentFile && !e.dir && (await id(1e3), this.active(e, i));
  }
  /**
   * 更新页面
   * @param page
   * @param silent
   */
  updatePage(e, t = false) {
    const i = this.getPage(e.id);
    if (delete e.dsl, i ? Object.assign(i, e) : console.warn(`not found PageFile for id: ${e.id} `), !t) {
      const s = {
        model: this,
        type: "update",
        data: e
      };
      n.emit(g, s), n.emit(l, s);
    }
  }
  /**
   * 复制页面
   * @param page
   * @param parentId
   * @param silent
   */
  clonePage(e, t, i = false) {
    var _a;
    const s = rd(), a = `${e.name}Copy`, o = `${e.title}_副本`, d = new u({
      id: s,
      name: a
    }).toDsl(), c = $l({}, e, { id: s, name: a, title: o, dsl: d }), p = t ? ((_a = this.getPage(t)) == null ? void 0 : _a.children) || [] : this.pages, O = p.findIndex((E) => E.id === e.id);
    if (p.splice(O + 1, 0, c), !i) {
      const E = {
        model: this,
        type: "clone",
        data: {
          source: e,
          target: c
        }
      };
      n.emit(g, E), n.emit(l, E);
    }
  }
  async saveToBlock(e, t = false) {
    this.active(e, t), await id(1e3);
    const i = rd(), s = e.name, a = e.title, o = new u({
      ...e.dsl,
      id: i,
      name: s
    }).toDsl(), d = $l({}, e, {
      id: i,
      name: s,
      title: a,
      dsl: o,
      type: "block",
      fromType: "Schema"
    });
    if (this.blocks.push(d), !t) {
      const c = {
        model: this,
        type: "create",
        data: d
      };
      n.emit(y, c), n.emit(l, c);
    }
  }
  /**
   * 删除页面或目录
   * @param id
   * @param silent
   */
  removePage(e, t = false) {
    var _a;
    const i = this.getPage(e), s = (a, o) => {
      const d = o.findIndex((c) => c.id === a);
      if (d >= 0) {
        o.splice(d, 1);
        return;
      }
      for (const c of o)
        c.children && c.children.length && s(a, c.children);
    };
    if (s(e, this.pages), e === this.homepage && (this.homepage = ""), ((_a = this.currentFile) == null ? void 0 : _a.id) === e && this.deactivate(t), !t) {
      const a = {
        model: this,
        type: "delete",
        data: i
      };
      n.emit(g, a), n.emit(l, a);
    }
  }
  /**
   * 获取区块文件
   * @param id
   * @returns
   */
  getBlock(e) {
    return this.blocks.find((t) => t.id === e);
  }
  /**
   * 创建区块
   * @param block
   * @param silent
   */
  async createBlock(e, t = false) {
    const i = e.id || rd(), s = t0(e.name);
    e.id = i, e.type = "block", e.dsl = new u({ id: i, name: s }).toDsl(), this.blocks.push(e);
    const a = e.fromType || "Schema";
    if (!t) {
      const o = {
        model: this,
        type: "create",
        data: e
      };
      n.emit(y, o), n.emit(l, o);
    }
    !this.currentFile && a === "Schema" && (await id(1e3), this.active(e, t));
  }
  /**
   *
   * @param block 更新区块
   * @param silent
   */
  updateBlock(e, t = false) {
    const i = this.getBlock(e.id);
    if (i ? (Object.assign(i, e), i.dsl && (i.dsl.name = e.name)) : console.warn(`not found PageFile for id: ${e.id} `), !t) {
      const s = {
        model: this,
        type: "update",
        data: e
      };
      n.emit(y, s), n.emit(l, s);
    }
  }
  cloneBlock(e, t = false) {
    const i = rd(), s = `${e.name}Copy`, a = `${e.title}_副本`, o = new u({
      id: i,
      name: s
    }).toDsl(), d = $l({}, e, { id: i, name: s, title: a, dsl: o }), c = this.blocks.findIndex((p) => p.id === e.id);
    if (this.blocks.splice(c + 1, 0, d), !t) {
      const p = {
        model: this,
        type: "clone",
        data: {
          source: e,
          target: d
        }
      };
      n.emit(y, p), n.emit(l, p);
    }
  }
  /**
   * 删除区块
   * @param id
   * @param silent
   */
  removeBlock(e, t = false) {
    var _a;
    const i = this.getBlock(e), s = this.blocks, a = s.findIndex((o) => o.id === e);
    if (a > -1 ? (s.splice(a, 1), ((_a = this.currentFile) == null ? void 0 : _a.id) === e && this.deactivate(t)) : console.warn(`not found PageFile for id: ${e} `), !t) {
      const o = {
        model: this,
        type: "delete",
        data: i
      };
      n.emit(y, o), n.emit(l, o);
    }
  }
  /**
   * 检查是否存在名称的区块
   * @param name
   * @param excludes
   * @returns
   */
  existBlockName(e, t = []) {
    return this.blocks.some((i) => i.name === e && !t.includes(i.id));
  }
  /**
   * 检测是否存在名称的页面
   * @param name
   * @param excludes
   * @returns
   */
  existPageName(e, t = []) {
    return this.getPages().some((s) => s.name === e && !t.includes(s.id));
  }
  /**
   * 新增或更新api
   * @param item
   * @param silent
   */
  setApi(e, t = false) {
    const i = this.apis.find(
      (a) => a.name === e.name || a.id === e.id
    );
    let s;
    if (i ? (s = "update", Object.assign(i, e)) : (s = "create", e.id = rd(), this.apis.push(e)), !t) {
      const a = {
        model: this,
        type: s,
        data: e
      };
      n.emit(b, a), n.emit(l, a);
    }
  }
  /**
   * 删除api
   * @param name
   * @param silent
   */
  removeApi(e, t = false) {
    const i = this.apis.findIndex((s) => s.name === e || s.id === e);
    if (i > -1 ? this.apis.splice(i, 1) : console.warn(`not found Api for name: ${e} `), !t) {
      const s = {
        model: this,
        type: "delete",
        data: e
      };
      n.emit(b, s), n.emit(l, s);
    }
  }
  existApiName(e, t = []) {
    return this.apis.some((i) => i.name === e && !t.includes(i.id));
  }
  setMeta(e, t = false) {
    const i = this.meta.find(
      (a) => a.code === e.code || a.id === e.id
    );
    let s;
    if (i ? (s = "update", Object.assign(i, e)) : (s = "create", e.id = rd(), this.meta.push(e)), !t) {
      const a = {
        model: this,
        type: s,
        data: e
      };
      n.emit(w, a), n.emit(l, a);
    }
  }
  removeMeta(e, t = false) {
    const i = this.meta.findIndex((s) => s.code === e || s.id === e);
    if (i > -1 ? this.meta.splice(i, 1) : console.warn(`not found meta for name: ${name} `), !t) {
      const s = {
        model: this,
        type: "delete",
        data: e
      };
      n.emit(w, s), n.emit(l, s);
    }
  }
  existMetaCode(e, t = []) {
    return this.meta.some((i) => i.code === e && !t.includes(i.id));
  }
  setHomepage(e, t = false) {
    if (this.homepage = e, !t) {
      const i = {
        model: this,
        type: "update",
        data: e
      };
      n.emit(l, i);
    }
  }
  setConfig(e, t = false) {
    if (this.config = Object.assign(this.config, e), !t) {
      const i = {
        model: this,
        type: "update",
        data: e
      };
      n.emit(l, i);
    }
  }
  setUniConfig(e, t, i = false) {
    if (this.uniConfig = Object.assign(this.uniConfig, { [e]: t }), !i) {
      const s = {
        model: this,
        type: "update",
        data: this.uniConfig
      };
      n.emit(l, s);
    }
  }
  publish(e) {
    const t = {
      model: this,
      type: "publish",
      data: e || this
    };
    e ? n.emit($, t) : n.emit(j, t);
  }
  genSource() {
    const e = {
      model: this,
      type: "gen",
      data: null
    };
    n.emit(M, e);
  }
  lock(e) {
    this.locked = e;
    const t = {
      model: this,
      type: "update",
      data: e
    };
    n.emit(l, t);
  }
  unlock(e) {
    if (e !== this.locked)
      return;
    this.locked = "";
    const t = {
      model: this,
      type: "update",
      data: null
    };
    n.emit(l, t);
  }
};
__publicField(_S, "attrs", [
  "locked",
  "platform",
  "name",
  "homepage",
  "description",
  "dependencies",
  "pages",
  "blocks",
  "apis",
  "meta",
  "config",
  "uniConfig",
  "__BASE_PATH__",
  "__UID__"
]);
var S = _S;
var N = "EVENT_HISTORY_CHANGE";
var V = "EVENT_HISTORY_LOAD";
var te = class {
  constructor(e, t = {}) {
    __publicField(this, "options", { max: 50 });
    __publicField(this, "index", -1);
    __publicField(this, "id");
    __publicField(this, "items");
    Object.assign(this.options, t);
    const { id: i, items: s = [] } = e;
    this.id = i, this.items = s;
  }
  toDsl() {
    const { id: e, items: t } = this;
    return {
      id: e,
      items: t.map((i) => ({ id: i.id, label: i.label }))
    };
  }
  /**
   * 获取历史项
   * @param id
   * @returns
   */
  get(e) {
    return this.items.find((t) => t.id === e);
  }
  /**
   * 增加历史记录
   * @param dsl
   * @param silent
   */
  add(e, t = false) {
    const { max: i } = this.options, s = {
      id: rd(),
      label: (/* @__PURE__ */ new Date()).toLocaleString(),
      dsl: qc(e)
    };
    if (this.items.unshift(s), this.items.length > i) {
      const a = this.items.splice(i);
      t || n.emit(N, {
        model: this,
        type: "delete",
        data: a.map((o) => o.id)
      });
    }
    this.index = -1, t || n.emit(N, {
      model: this,
      type: "create",
      data: s
    });
  }
  /**
   * 删除历史记录
   * @param id
   * @param silent
   */
  remove(e, t = false) {
    const i = this.items.findIndex((s) => s.id === e);
    i > -1 ? (this.items.splice(i, 1), i === this.index ? this.index = -1 : this.index >= this.items.length && (this.index = this.items.length - 1)) : console.warn(`not found HistoryItem for id: ${e} `), t || n.emit(N, {
      model: this,
      type: "delete",
      data: [e]
    });
  }
  forward(e = false) {
    const { index: t, items: i } = this;
    if (t < 0) return;
    --this.index;
    const s = i[this.index];
    s && !e && n.emit(V, {
      model: this,
      type: "load",
      data: s
    });
  }
  backward(e = false) {
    const { index: t, items: i } = this;
    if (t >= i.length - 1) return;
    t < 0 && (this.index = 0), ++this.index;
    const s = i[this.index];
    s && !e && n.emit(V, {
      model: this,
      type: "load",
      data: s
    });
  }
  load(e, t = false) {
    const i = this.items.findIndex((s) => s.id === e);
    i >= 0 && (this.index = i, t || n.emit(V, {
      model: this,
      type: "load",
      data: this.items[i]
    }));
  }
  clear(e = false) {
    this.index = -1;
    const t = this.items.map((i) => i.id);
    this.items = [], e || n.emit(N, {
      model: this,
      type: "clear",
      data: t
    });
  }
};
var n = yd();
function ie(f) {
  return f instanceof u;
}
function se(f) {
  return f instanceof _;
}
function ne(f) {
  return !!f.__VTJ_BLOCK__;
}
function L(f) {
  const e = qc(f);
  return delete e.id, Array.isArray(e.children) && (e.children = e.children.map((t) => L(t))), e;
}
function ae(f) {
  const e = f.fromType || "Schema";
  return e === "Schema" ? {
    type: "Schema",
    id: f.id
  } : e === "UrlSchema" ? {
    type: "UrlSchema",
    url: (f.urls || "").split(",")[0] || ""
  } : e === "Plugin" ? {
    type: "Plugin",
    urls: (f.urls || "").split(","),
    library: f.library
  } : "";
}

export {
  Y,
  W,
  H,
  J,
  q,
  z,
  Q,
  X,
  Z,
  ee,
  x,
  T,
  v,
  h,
  _,
  r,
  u,
  l,
  D,
  B,
  g,
  y,
  b,
  w,
  j,
  $,
  M,
  S,
  N,
  V,
  te,
  n,
  ie,
  se,
  ne,
  L,
  ae,
  dist_exports
};
/*! Bundled license information:

@vtj/core/dist/index.mjs:
  (**!
   * Copyright (c) 2025, VTJ.PRO All rights reserved.
   * @name @vtj/core 
   * @<NAME_EMAIL> 
   * @version 0.12.47
   * @license <a href="https://vtj.pro/license.html">MIT License</a>
   *)
*/
//# sourceMappingURL=chunk-RK5LLB6U.js.map
