/**
 * 响应数据接口
 */
export interface IResponse {
  /**
   * 请求状态码
   */
  code: number;

  /**
   * 响应时间戳
   */
  timestamp: number;
  /**
   * 提示信息
   */
  message: string;
}

export interface IResponseSuccess<T = any> extends IResponse {
  /**
   * 是否成功
   */
  success: true;
  /**
   * 响应数据体
   */
  data: T;
}

export interface IResponseFail extends IResponse {
  /**
   * 是否成功
   */
  success: false;
  /**
   * 错误原因
   */
  cause?: any;

  /**
   * 调用栈
   */
  stack?: any;
}
