{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/cypher/cypher.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/cypher/cypher.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"`\", close: \"`\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: `.cypher`,\n  ignoreCase: true,\n  brackets: [\n    { open: \"{\", close: \"}\", token: \"delimiter.curly\" },\n    { open: \"[\", close: \"]\", token: \"delimiter.bracket\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    \"ALL\",\n    \"AND\",\n    \"AS\",\n    \"ASC\",\n    \"ASCENDING\",\n    \"BY\",\n    \"CALL\",\n    \"CASE\",\n    \"CONTAINS\",\n    \"CREATE\",\n    \"DELETE\",\n    \"DESC\",\n    \"DESCENDING\",\n    \"DETACH\",\n    \"DISTINCT\",\n    \"ELSE\",\n    \"END\",\n    \"ENDS\",\n    \"EXISTS\",\n    \"IN\",\n    \"IS\",\n    \"LIMIT\",\n    \"MANDATORY\",\n    \"MATCH\",\n    \"MERGE\",\n    \"NOT\",\n    \"ON\",\n    \"ON\",\n    \"OPTIONAL\",\n    \"OR\",\n    \"ORDER\",\n    \"REMOVE\",\n    \"RETURN\",\n    \"SET\",\n    \"SKIP\",\n    \"STARTS\",\n    \"THEN\",\n    \"UNION\",\n    \"UNWIND\",\n    \"WHEN\",\n    \"WHERE\",\n    \"WITH\",\n    \"XOR\",\n    \"YIELD\"\n  ],\n  builtinLiterals: [\"true\", \"TRUE\", \"false\", \"FALSE\", \"null\", \"NULL\"],\n  builtinFunctions: [\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"atan2\",\n    \"avg\",\n    \"ceil\",\n    \"coalesce\",\n    \"collect\",\n    \"cos\",\n    \"cot\",\n    \"count\",\n    \"degrees\",\n    \"e\",\n    \"endNode\",\n    \"exists\",\n    \"exp\",\n    \"floor\",\n    \"head\",\n    \"id\",\n    \"keys\",\n    \"labels\",\n    \"last\",\n    \"left\",\n    \"length\",\n    \"log\",\n    \"log10\",\n    \"lTrim\",\n    \"max\",\n    \"min\",\n    \"nodes\",\n    \"percentileCont\",\n    \"percentileDisc\",\n    \"pi\",\n    \"properties\",\n    \"radians\",\n    \"rand\",\n    \"range\",\n    \"relationships\",\n    \"replace\",\n    \"reverse\",\n    \"right\",\n    \"round\",\n    \"rTrim\",\n    \"sign\",\n    \"sin\",\n    \"size\",\n    \"split\",\n    \"sqrt\",\n    \"startNode\",\n    \"stDev\",\n    \"stDevP\",\n    \"substring\",\n    \"sum\",\n    \"tail\",\n    \"tan\",\n    \"timestamp\",\n    \"toBoolean\",\n    \"toFloat\",\n    \"toInteger\",\n    \"toLower\",\n    \"toString\",\n    \"toUpper\",\n    \"trim\",\n    \"type\"\n  ],\n  operators: [\n    // Math operators\n    \"+\",\n    \"-\",\n    \"*\",\n    \"/\",\n    \"%\",\n    \"^\",\n    // Comparison operators\n    \"=\",\n    \"<>\",\n    \"<\",\n    \">\",\n    \"<=\",\n    \">=\",\n    // Pattern operators\n    \"->\",\n    \"<-\",\n    \"-->\",\n    \"<--\"\n  ],\n  escapes: /\\\\(?:[tbnrf\\\\\"'`]|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  digits: /\\d+/,\n  octaldigits: /[0-7]+/,\n  hexdigits: /[0-9a-fA-F]+/,\n  tokenizer: {\n    root: [[/[{}[\\]()]/, \"@brackets\"], { include: \"common\" }],\n    common: [\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      // Cypher labels on nodes/relationships, e.g. (n:NodeLabel)-[e:RelationshipLabel]\n      [/:[a-zA-Z_][\\w]*/, \"type.identifier\"],\n      [\n        /[a-zA-Z_][\\w]*(?=\\()/,\n        {\n          cases: {\n            \"@builtinFunctions\": \"predefined.function\"\n          }\n        }\n      ],\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@builtinLiterals\": \"predefined.literal\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/`/, \"identifier.escape\", \"@identifierBacktick\"],\n      // delimiter and operator after number because of `.\\d` floats and `:` in labels\n      [/[;,.:|]/, \"delimiter\"],\n      [\n        /[<>=%+\\-*/^]+/,\n        {\n          cases: {\n            \"@operators\": \"delimiter\",\n            \"@default\": \"\"\n          }\n        }\n      ]\n    ],\n    numbers: [\n      [/-?(@digits)[eE](-?(@digits))?/, \"number.float\"],\n      [/-?(@digits)?\\.(@digits)([eE]-?(@digits))?/, \"number.float\"],\n      [/-?0x(@hexdigits)/, \"number.hex\"],\n      [/-?0(@octaldigits)/, \"number.octal\"],\n      [/-?(@digits)/, \"number\"]\n    ],\n    strings: [\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/'([^'\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@stringDouble\"],\n      [/'/, \"string\", \"@stringSingle\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ],\n    comment: [\n      [/\\/\\/.*/, \"comment\"],\n      [/[^/*]+/, \"comment\"],\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[/*]/, \"comment\"]\n    ],\n    stringDouble: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/\"/, \"string\", \"@pop\"]\n    ],\n    stringSingle: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string\"],\n      [/\\\\./, \"string.invalid\"],\n      [/'/, \"string\", \"@pop\"]\n    ],\n    identifierBacktick: [\n      [/[^\\\\`]+/, \"identifier.escape\"],\n      [/@escapes/, \"identifier.escape\"],\n      [/\\\\./, \"identifier.escape.invalid\"],\n      [/`/, \"identifier.escape\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,kBAAkB;AAAA,IAClD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,iBAAiB,CAAC,QAAQ,QAAQ,SAAS,SAAS,QAAQ,MAAM;AAAA,EAClE,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA;AAAA,IAET;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,WAAW;AAAA,EACX,WAAW;AAAA,IACT,MAAM,CAAC,CAAC,aAAa,WAAW,GAAG,EAAE,SAAS,SAAS,CAAC;AAAA,IACxD,QAAQ;AAAA,MACN,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA;AAAA,MAEtB,CAAC,mBAAmB,iBAAiB;AAAA,MACrC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,qBAAqB;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,oBAAoB;AAAA,YACpB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,KAAK,qBAAqB,qBAAqB;AAAA;AAAA,MAEhD,CAAC,WAAW,WAAW;AAAA,MACvB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,iCAAiC,cAAc;AAAA,MAChD,CAAC,6CAA6C,cAAc;AAAA,MAC5D,CAAC,oBAAoB,YAAY;AAAA,MACjC,CAAC,qBAAqB,cAAc;AAAA,MACpC,CAAC,eAAe,QAAQ;AAAA,IAC1B;AAAA,IACA,SAAS;AAAA,MACP,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,KAAK,UAAU,eAAe;AAAA,MAC/B,CAAC,KAAK,UAAU,eAAe;AAAA,IACjC;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,QAAQ,SAAS;AAAA,IACpB;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,OAAO,gBAAgB;AAAA,MACxB,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA,IACA,cAAc;AAAA,MACZ,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,OAAO,gBAAgB;AAAA,MACxB,CAAC,KAAK,UAAU,MAAM;AAAA,IACxB;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,WAAW,mBAAmB;AAAA,MAC/B,CAAC,YAAY,mBAAmB;AAAA,MAChC,CAAC,OAAO,2BAA2B;AAAA,MACnC,CAAC,KAAK,qBAAqB,MAAM;AAAA,IACnC;AAAA,EACF;AACF;", "names": []}