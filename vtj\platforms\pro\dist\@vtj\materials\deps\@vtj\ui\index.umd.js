(function(B,ie){typeof exports=="object"&&typeof module<"u"?ie(exports):typeof define=="function"&&define.amd?define(["exports"],ie):(B=typeof globalThis<"u"?globalThis:B||self,ie(B.VtjUI={}))})(this,function(B){"use strict";var Ai=Object.defineProperty;var Ri=(B,ie,Te)=>ie in B?Ai(B,ie,{enumerable:!0,configurable:!0,writable:!0,value:Te}):B[ie]=Te;var Ee=(B,ie,Te)=>Ri(B,typeof ie!="symbol"?ie+"":ie,Te);/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/ui 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const ie="0.12.70",Te={default:14,small:12,large:18},Wn={icon:{type:[String,Object]},color:{type:String},size:{type:[Number,String],default:"inherit"},src:{type:String},background:{type:String},padding:{type:Number},radius:{type:Number},hoverEffect:{type:Boolean}};function Ve(o){return typeof o=="string"?o:`${o}px`}function tt(o=0,t){if(typeof o=="number")return o;if(/(%|vh|vw)$/i.test(o)){const e=Number.parseInt(o);return Math.floor(t*e/100)}return Number.parseInt(o)}function Ut(o,t={}){const n={...t};return typeof o=="boolean"?n:Object.assign(n,o||{})}const Lt="__VTJ_UI_INSTALLED__",mt=Symbol("ADAPTER_KEY");function Vt(){const o=Vue.getCurrentInstance(),t=o==null?void 0:o.appContext.config.globalProperties.$adapter;return t||Vue.inject(mt,{})}const Yr={getCustom:async o=>VtjUtils.storage.get(o,{type:"local"}),saveCustom:async o=>{VtjUtils.storage.save(o.id,o,{type:"local"})}},Gr={install(o,t={}){const n=Object.assign(Yr,t),{components:e=[],fieldEditors:r={}}=n;Yo(r);for(const l of e)l.name&&o.component(l.name,l);[ElementPlus.ElMessage,ElementPlus.ElMessageBox,ElementPlus.ElNotification].forEach(l=>{o.use(l)}),o.config.globalProperties.$adapter=n,o.provide(mt,n)}},Jn=(o=[])=>({install:(n,e={})=>{n[Lt]||(n[Lt]=!0,o.forEach(r=>{r.name&&n.component(r.name,r)}),e.adapter&&n.provide(mt,e.adapter))}});let Ue;const Qn=new RegExp(`\\<script.*src=["'](?<src>[^"']+)`,"gm");let Wr=60*1e3;async function Jr(){var e;const o=await fetch("?t="+Date.now()).then(r=>r.text());Qn.lastIndex=0;let t=[],n;for(;n=Qn.exec(o);)(e=n.groups)!=null&&e.src&&t.push(n.groups.src);return t}async function Qr(){const o=await Jr();if(!Ue)return Ue=o,!1;let t=!1;Ue.length!==o.length&&(t=!0);for(let n=0;n<Ue.length;n++)if(Ue[n]!==o[n]){t=!0;break}return Ue=o,t}async function Zr(){await ElementPlus.ElMessageBox.confirm("系统发现新版本，请确认是否需要更新。如果确定，系统将会重新登录，请注意存档。",{title:"系统更新",type:"warning",closeOnClickModal:!1}).catch(()=>!1)&&(top||window).location.reload()}function Zn(o){setTimeout(async()=>{await Qr()&&await Zr(),Zn()},o||Wr)}function el(o){Zn(o)}const tl=["src"],de=Vue.defineComponent({name:"XIcon",__name:"Icon",props:Wn,setup(o){const t=o,n=Vue.useAttrs(),e=Vue.getCurrentInstance(),r=Vue.computed(()=>{if(!t.icon)return null;const i=e==null?void 0:e.appContext.app;return typeof t.icon=="object"?Vue.markRaw(t.icon):(i==null?void 0:i.component(t.icon))||t.icon}),l=Vue.computed(()=>{const i={"is-pointer":!!n.onClick,"is-hover-effect":t.hoverEffect};return!r.value&&t.icon&&(i[t.icon]=!0),i}),u=Vue.computed(()=>({"background-color":t.background,"border-radius":t.radius?Ve(t.radius):void 0,padding:t.padding?Ve(t.padding):void 0})),a=Vue.computed(()=>{var i,s;return typeof t.size=="number"?t.size:(s=(i=Te[t.size])!=null?i:Number.parseInt(t.size))!=null?s:void 0});return(i,s)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElIcon),{class:Vue.normalizeClass(["x-icon",l.value]),color:t.color,size:a.value,style:Vue.normalizeStyle(u.value)},{default:Vue.withCtx(()=>[Vue.renderSlot(i.$slots,"default",{},()=>[r.value?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(r.value),{key:0})):Vue.createCommentVNode("",!0),t.src?(Vue.openBlock(),Vue.createElementBlock("img",{key:1,src:t.src},null,8,tl)):Vue.createCommentVNode("",!0)])]),_:3},8,["class","color","size","style"]))}}),nl={class:"x-menu__wrapper"},ol={key:1,class:"x-menu__title"},rl={key:0,class:"x-menu__title"},eo=Vue.defineComponent({inheritAttrs:!1,name:"XMenuItem",__name:"MenuItem",props:{item:{},subMenu:{},defaultIcon:{}},setup(o){const t=o,n=Vue.getCurrentInstance(),e=u=>{if(u){if(typeof u=="string"){const a=n==null?void 0:n.appContext.app;return(a==null?void 0:a.component(u))||t.defaultIcon||u}return u}},r=u=>typeof u=="object"?u:{value:u},l=(u=[])=>u.filter(a=>!a.hidden);return(u,a)=>t.item.children?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElSubMenu),Vue.mergeProps({key:0,disabled:t.item.disabled},t.subMenu,{index:String(t.item.id)}),{title:Vue.withCtx(()=>[Vue.createElementVNode("div",nl,[t.item.icon?(Vue.openBlock(),Vue.createBlock(Vue.unref(de),{key:0,icon:e(t.item.icon)},null,8,["icon"])):Vue.createCommentVNode("",!0),t.item.title?(Vue.openBlock(),Vue.createElementBlock("span",ol,Vue.toDisplayString(t.item.title),1)):Vue.createCommentVNode("",!0),t.item.badge?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElBadge),Vue.mergeProps({key:2,class:"x-menu__badge"},r(t.item.badge)),{default:Vue.withCtx(()=>a[0]||(a[0]=[Vue.createElementVNode("i",null,null,-1)])),_:1,__:[0]},16)):Vue.createCommentVNode("",!0)])]),default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(l(t.item.children),i=>(Vue.openBlock(),Vue.createBlock(Vue.unref(eo),{key:i.id,item:i,subMenu:t.subMenu,defaultIcon:t.defaultIcon},null,8,["item","subMenu","defaultIcon"]))),128))]),_:1},16,["disabled","index"])):(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElMenuItem),{key:1,index:String(t.item.id),disabled:t.item.disabled},{title:Vue.withCtx(()=>[t.item.title?(Vue.openBlock(),Vue.createElementBlock("span",rl,Vue.toDisplayString(t.item.title),1)):Vue.createCommentVNode("",!0),t.item.badge?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElBadge),Vue.mergeProps({key:1,class:"x-menu__badge"},r(t.item.badge)),{default:Vue.withCtx(()=>a[1]||(a[1]=[Vue.createElementVNode("i",null,null,-1)])),_:1,__:[1]},16)):Vue.createCommentVNode("",!0)]),default:Vue.withCtx(()=>[t.item.icon?(Vue.openBlock(),Vue.createBlock(Vue.unref(de),{key:0,icon:e(t.item.icon)},null,8,["icon"])):Vue.createCommentVNode("",!0)]),_:1},8,["index","disabled"]))}}),Se=Vue.defineComponent({inheritAttrs:!1,name:"XMenu",__name:"Menu",props:{data:{default:()=>[]},subMenu:{},defaultIcon:{}},emits:["select"],setup(o,{emit:t}){const n=o,e=t,r=Vue.computed(()=>n.data.filter(a=>!a.hidden)),l=(a,i)=>{const s=(c,d=[])=>{var m;for(const p of d){if(p.id.toString()===c.toString())return p;if((m=p.children)!=null&&m.length){const f=s(c,p.children);if(f)return f}}};return s(i,a)},u=a=>{const i=l(r.value,a);i&&e("select",i)};return(a,i)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElMenu),Vue.mergeProps({class:"x-menu"},a.$attrs,{onSelect:u}),{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(r.value,s=>(Vue.openBlock(),Vue.createBlock(Vue.unref(eo),{item:{...s,icon:s.icon},defaultIcon:n.defaultIcon,subMenu:n.subMenu},null,8,["item","defaultIcon","subMenu"]))),256))]),_:1},16))}}),Ht={name:{type:String},label:{type:String},value:{type:[String,Number,Object,Array,Boolean]},icon:{type:[String,Object]},mode:{type:String,default:"button"},menus:{type:Array},tooltip:{type:[String,Object]},badge:{type:[String,Number,Object]},dropdown:{type:Object},button:{type:Object},disabled:{type:[Boolean,Function]},size:{type:String,default:"default"},type:{type:String},background:{type:String,default:"always"},circle:{type:Boolean},draggable:{type:Boolean}};function ll(o){return Vue.computed(()=>{const t=o.tooltip;if(t)return typeof t=="string"?{content:t}:t})}function al(o){return Vue.computed(()=>{const t=o.badge;if(t)return typeof t=="object"?t:{value:t}})}function ul(o){return Vue.computed(()=>{const{dropdown:t,menus:n}=o;if(n&&n.length>0)return Object.assign({popperClass:"x-action__menus",size:o.size},t||{})})}function to(o){const t=Vue.getCurrentInstance();return Vue.computed(()=>{const n=Vue.unref(o);if(n)if(typeof n=="string"||n.setup||n.render||Vue.isVNode(n)){const e=t==null?void 0:t.appContext.app;return{icon:typeof n=="string"&&(e==null?void 0:e.component(n))||n}}else return n;return null})}function De(o){const t=to(o);return t.value?Vue.markRaw(Vue.defineComponent({render:()=>Vue.h(de,t.value)})):void 0}function ht(o,t){return Vue.computed(()=>{const n=Vue.unref(o);return typeof n=="function"?n(t):!!n})}function no(o,t,n){const e=Vue.shallowRef(t),r=Vue.ref(!1),l=async a=>{const i=Vue.unref(o);return i?typeof i=="function"?await i(a):i:t},u=async a=>(r.value=!0,await l(a).then(i=>(e.value=i||t,r.value=!1,Vue.unref(e))));return Vue.watch(()=>o,()=>{u(n)},{immediate:!0}),n&&Vue.watch(n,()=>{u(n)}),{data:e,loading:r,loader:u}}function il(o=1e3){const t=Vue.ref(0);let n;const e=()=>{n=VtjUtils.rAF(()=>{++t.value,t.value<o&&e()})};return Vue.onMounted(e),Vue.onUnmounted(()=>{n&&VtjUtils.cAF(n)}),r=>t.value>=r}function oo(o=[]){const t=Vue.useSlots();return Object.keys(t).filter(n=>!o.includes(n))}const sl={key:0},cl=Vue.defineComponent({__name:"Trigger",props:Ht,emits:["click"],setup(o,{emit:t}){const n=o,e=t,r=De(Vue.toRef(n,"icon")),l=ht(Vue.toRef(n,"disabled")),u=Vue.computed(()=>({[`is-${n.mode}`]:!!n.mode,[`is-${n.type}`]:!!n.type,[`is-${n.size}`]:!!n.size&&n.size!=="default",[`is-background-${n.background}`]:n.mode==="icon"&&!!n.background,"is-disabled":!!n.disabled,"is-circle":!!n.circle})),a=i=>{if(i.preventDefault(),!l.value)return e("click"),!1};return(i,s)=>n.mode==="button"?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElButton),Vue.mergeProps({key:0,icon:Vue.unref(r),type:n.type,size:n.size,disabled:Vue.unref(l)},n.button,{onClick:a}),{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(i.label),1)]),_:1},16,["icon","type","size","disabled"])):(Vue.openBlock(),Vue.createElementBlock("div",{key:1,class:Vue.normalizeClass(["x-action__inner",u.value]),onClick:a},[(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(r)))),i.label?(Vue.openBlock(),Vue.createElementBlock("span",sl,Vue.toDisplayString(i.label),1)):Vue.createCommentVNode("",!0)],2))}}),dl=["draggable"],Q=Vue.defineComponent({name:"XAction",__name:"Action",props:Ht,emits:["click","command","dragstart","dragend"],setup(o,{emit:t}){const n=o,e=Vue.useSlots(),r=t,l=ll(n),u=al(n),a=ul(n),i=ht(Vue.toRef(n,"disabled")),s=Vue.computed(()=>!!n.draggable&&!i.value),c=Vue.computed(()=>({[`x-action--${n.mode}`]:!!n.mode})),d=()=>{i.value||r("click",Vue.toRaw(n))},m=b=>{var _;if(i.value)return;const w=(_=n.menus)==null?void 0:_.find(C=>C.command===b);w&&r("command",Vue.toRaw(w))},p=b=>{s&&r("dragstart",Vue.toRaw(n),b)},f=b=>{s&&r("dragend",Vue.toRaw(n),b)},V=b=>Vue.h(ElementPlus.ElBadge,u.value,()=>[].concat(b)),h=b=>Vue.h(ElementPlus.ElDropdown,{...a.value,disabled:i.value,onCommand:m},{default:()=>[b],dropdown:()=>[Vue.h(ElementPlus.ElDropdownMenu,()=>(n.menus||[]).map((w,_)=>Vue.h(ElementPlus.ElDropdownItem,w,()=>e.item?e.item({item:w,index:_}):w.label)))]}),v=b=>Vue.h(ElementPlus.ElTooltip,{...l.value,disabled:i.value},()=>[b]),y=Vue.computed(()=>{var _;let b=Vue.h(Vue.markRaw(cl),{...n,onClick:d});const w=(_=e.default)==null?void 0:_.call(e);return w&&w.length&&(b=w[0]),u.value&&(b=V(b)),a.value&&(b=h(b)),l.value&&(b=v(b)),b});return(b,w)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:Vue.normalizeClass(["x-action",c.value]),draggable:s.value,onDragstart:p,onDragend:f},[(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(y.value)))],42,dl))}}),ro={items:{type:Array},mode:{type:String,default:"button"},size:{type:String,default:"default"},type:{type:String},background:{type:String,default:"always"},circle:{type:Boolean},disabled:{type:[Boolean,Function]},tooltip:{type:Object},badge:{type:Object},dropdown:{type:Object},button:{type:Object}},ze=Vue.defineComponent({name:"XActionBar",__name:"ActionBar",props:ro,emits:["click","command"],setup(o,{emit:t}){const n=o,e=t,r=Vue.computed(()=>(n.items||[]).map(a=>a==="|"?a:{...a,badge:a.badge?{...n.badge,...typeof a.badge=="object"?a.badge||{}:{value:a.badge}}:void 0,tooltip:a.tooltip?{...n.tooltip,...typeof a.tooltip=="object"?a.tooltip||{}:{content:a.tooltip}}:void 0})),l=a=>{e("click",a)},u=(a,i)=>{e("command",a,i)};return(a,i)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:"x-action-bar",align:"center"},{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(r.value,s=>(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,null,[s==="|"?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElDivider),{key:0,direction:"vertical",class:"x-action-bar__divider"})):(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),Vue.mergeProps({key:1,mode:n.mode,size:n.size,type:n.type,circle:n.circle,background:n.background,button:n.button,dropdown:n.dropdown,disabled:Vue.unref(ht)(n.disabled,s).value},{ref_for:!0},s,{onClick:l,onCommand:c=>u(s,c)}),null,16,["mode","size","type","circle","background","button","dropdown","disabled","onCommand"]))],64))),256))]),_:1}))}}),lo={tag:{type:String,default:"div"},fit:{type:Boolean,default:!1},width:{type:[String,Number]},height:{type:[String,Number]},flex:{type:Boolean,default:!0},inline:{type:Boolean},direction:{type:String,default:"row"},wrap:{type:String,default:"nowrap"},justify:{type:String,default:"flex-start"},align:{type:String,default:"flex-start"},alignContent:{type:String,default:"stretch"},grow:{type:Boolean,default:!1},shrink:{type:Boolean,default:!1},alignSelf:{type:String,default:"auto"},overflow:{type:String},padding:{type:Boolean,default:!1},gap:{type:Boolean},autoPointer:{type:Boolean}},j=Vue.defineComponent({name:"XContainer",__name:"Container",props:lo,setup(o,{expose:t}){const n=o,e=Vue.useAttrs(),r=Vue.getCurrentInstance(),l=Vue.ref(),u=Vue.computed(()=>{var c;return(c=l.value)==null?void 0:c.$el}),a=Vue.computed(()=>{const c=r==null?void 0:r.parent;if(!c)return!1;const d=c.proxy;return d.$options.name==="XContainer"||!!d.flex}),i=Vue.computed(()=>{var c;return{"is-fit":n.fit,"is-flex":n.flex&&!n.inline,"is-inline-flex":n.flex&&n.inline,[`is-direction-${n.direction}`]:n.flex&&n.direction!=="row",[`is-wrap-${n.wrap}`]:n.flex&&n.wrap!=="nowrap",[`is-justify-${n.justify}`]:n.flex&&n.justify!=="flex-start",[`is-align-${n.align}`]:n.flex&&n.align!=="flex-start",[`is-align-content-${n.alignContent}`]:n.flex&&n.alignContent!=="stretch","is-grow":(c=n.grow)!=null?c:a.value,"is-shrink":n.shrink,[`is-align-self-${n.alignSelf}`]:a.value&&n.alignSelf!=="auto",[`is-overflow-${n.overflow}`]:!!n.overflow,"is-padding":!!n.padding,"is-pointer":n.autoPointer&&!!e.onClick,"is-gap":!!n.gap}}),s=Vue.computed(()=>{const{width:c,height:d,fit:m}=n;return m?null:{width:c?Ve(c):void 0,height:d?Ve(d):void 0}});return t({$vtjEl:u}),(c,d)=>(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(n.tag),{ref_key:"elRef",ref:l,class:Vue.normalizeClass(["x-container",i.value]),style:Vue.normalizeStyle(s.value)},{default:Vue.withCtx(()=>[Vue.renderSlot(c.$slots,"default")]),_:3},8,["class","style"]))}}),ao={size:{type:String,default:"default"},content:{type:String,default:""},subtitle:{type:String},icon:{type:[String,Object]},border:{type:Boolean},more:{type:Boolean}},fl={class:"x-header__content"},pl={key:0,class:"x-header__actions"},Xt=Vue.defineComponent({name:"XHeader",inheritAttrs:!1,__name:"Header",props:ao,setup(o){const t=o,n=De(Vue.toRef(t,"icon")),e=Vue.useAttrs(),r=Vue.computed(()=>({[`is-size-${t.size}`]:!!t.size&&t.size!=="default","is-border":t.border,"is-pointer":!!e.onClick}));return(l,u)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:Vue.normalizeClass(["x-header",r.value]),justify:"space-between",align:"center"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(j),{align:"center",class:Vue.normalizeClass(["x-header__wrapper",r.value]),grow:"",onClick:Vue.unref(e).onClick},{default:Vue.withCtx(()=>[Vue.unref(n)?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(n)),{key:0,class:"x-header__icon",onClick:Vue.unref(e).onClickIcon||Vue.unref(e).onClick},null,8,["onClick"])):Vue.createCommentVNode("",!0),Vue.createElementVNode("span",fl,[Vue.renderSlot(l.$slots,"default",{},()=>[Vue.createTextVNode(Vue.toDisplayString(t.content),1)])]),t.more?(Vue.openBlock(),Vue.createBlock(Vue.unref(de),{key:1,class:"x-header__more",icon:Vue.unref(VtjIcons.ArrowRight)},null,8,["icon"])):Vue.createCommentVNode("",!0),t.subtitle||l.$slots.subtitle?(Vue.openBlock(),Vue.createElementBlock("span",{key:2,class:"x-header__subtitle",onClick:u[0]||(u[0]=(...a)=>Vue.unref(e).onClick&&Vue.unref(e).onClick(...a))},[Vue.renderSlot(l.$slots,"subtitle",{},()=>[Vue.createTextVNode(Vue.toDisplayString(t.subtitle),1)])])):Vue.createCommentVNode("",!0)]),_:3},8,["class","onClick"]),l.$slots.actions?(Vue.openBlock(),Vue.createElementBlock("div",pl,[Vue.renderSlot(l.$slots,"actions")])):Vue.createCommentVNode("",!0)]),_:3},8,["class"]))}}),uo={badge:{type:Object},fit:{type:Boolean,default:!1},width:{type:[String,Number]},height:{type:[String,Number]},border:{type:Boolean,default:!0},radius:{type:Boolean,default:!0},card:{type:Boolean},size:{type:String},shadow:{type:String},header:{type:[String,Object]},body:{type:Object},footer:{type:Object}},Le=Vue.defineComponent({name:"XPanel",__name:"Panel",props:uo,setup(o,{expose:t}){const n=o,e=Vue.ref(),r=Vue.computed(()=>({"x-panel__badge-wrapper":!!n.badge,"x-panel--card":!!n.card,"x-panel--default":!n.card,[`is-${n.size}`]:!!n.size&&n.size!=="default","is-border":!!n.border,"is-radius":!!n.radius,[`is-shadow-${n.shadow}`]:!!n.shadow&&n.shadow!=="none"})),l=Vue.computed(()=>n.badge===null?null:n.badge?`is-badge-${n.badge.type}`:""),u=Vue.computed(()=>n.header===null||n.header===void 0?null:typeof n.header=="string"?{content:n.header,size:n.size}:{...n.header,size:n.size}),a=Vue.computed(()=>!!u.value),i=Vue.computed(()=>n.fit||n.height?"auto":void 0);return t({bodyRef:e}),(s,c)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:Vue.normalizeClass(["x-panel",r.value]),direction:"column",fit:n.fit,width:n.width,height:n.height},{default:Vue.withCtx(()=>[a.value||s.$slots.header?(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{key:0,flex:!1,class:"x-panel__header"},{default:Vue.withCtx(()=>[Vue.renderSlot(s.$slots,"header",{},()=>[Vue.createVNode(Vue.unref(Xt),Vue.normalizeProps(Vue.guardReactiveProps(u.value)),{default:Vue.withCtx(()=>[Vue.renderSlot(s.$slots,"title")]),actions:Vue.withCtx(()=>[Vue.renderSlot(s.$slots,"actions")]),_:3},16)])]),_:3})):Vue.createCommentVNode("",!0),Vue.createVNode(Vue.unref(j),Vue.mergeProps({ref_key:"bodyRef",ref:e,flex:!1,overflow:i.value,grow:"",padding:"",class:"x-panel__body"},n.body),{default:Vue.withCtx(()=>[Vue.renderSlot(s.$slots,"default")]),_:3},16,["overflow"]),s.$slots.footer?(Vue.openBlock(),Vue.createBlock(Vue.unref(j),Vue.mergeProps({key:1,flex:!1,padding:"",class:"x-panel__footer"},n.footer),{default:Vue.withCtx(()=>[Vue.renderSlot(s.$slots,"footer")]),_:3},16)):Vue.createCommentVNode("",!0),n.badge?(Vue.openBlock(),Vue.createElementBlock("div",{key:2,class:Vue.normalizeClass(["x-panel__badge",l.value])},Vue.toDisplayString(n.badge.text),3)):Vue.createCommentVNode("",!0)]),_:3},8,["class","fit","width","height"]))}}),io="user-select-none";class qt{constructor(t,n={}){Ee(this,"scope");Ee(this,"dragging",!1);this.el=t,this.options=n,this.scope=Vue.effectScope(),this.scope.run(()=>{this.init()})}getHandle(){const{selector:t,handle:n}=this.options;return t?this.el.querySelector(t):n}getTarget(){const{target:t="body"}=this.options;return typeof t=="string"?document.querySelector(t):Vue.unref(t)||document.body}init(){const{el:t,options:n}=this,{disabled:e,delay:r=150,onStart:l,onEnd:u}=n;if(e)return;let a=null;const i=this.getHandle(),s=this.getTarget();let c=t.getBoundingClientRect(),d=null;const{x:m,y:p}=VueUse.useDraggable(t,{initialValue:{x:c.x,y:c.y},...n,handle:i,onStart:(f,V)=>{document.body.classList.add(io),clearTimeout(a),a=setTimeout(()=>{this.dragging=!0,c=t.getBoundingClientRect(),d=s==null?void 0:s.getBoundingClientRect(),l&&l(f,V)},r)},onEnd:(f,V)=>{if(clearTimeout(a),document.body.classList.remove(io),this.dragging&&d){this.dragging=!1;const{x:h,y:v}=f,y=this.getPosition(d,c,h,v);u&&u(y,V),d=null}}});Vue.watch([m,p],()=>{if(this.dragging&&d){const f=this.getPosition(d,c,m.value,p.value);t.style.left=`${f.x}px`,t.style.top=`${f.y}px`}})}getPosition(t,n,e,r){const{edge:l=50}=this.options,u=-n.width+t.x+l,a=t.width+t.x-l,i=t.y,s=t.height+t.y-l,c=Math.min(a,Math.max(e,u)),d=Math.min(s,Math.max(r,i));return{x:c-t.x,y:d-t.y}}destory(){this.scope.stop()}}const so={mounted(o,t){const n=t.value||{},e=new qt(o,n);o.__draggable__=e},updated(o,t){const n=t.value||{};let e=o.__draggable__;e&&!VtjUtils.isEqual(e.options,n)&&(e.destory(),o.__draggable__=new qt(o,n))},unmounted(o){const t=o.__draggable__;t&&(t.destory(),o.__draggable__=null)}},Kt="user-select-none";class Yt{constructor(t,n={}){Ee(this,"scope");Ee(this,"resizing",Vue.ref(!1));Ee(this,"direction",Vue.ref(""));Ee(this,"MIE",null);Ee(this,"cleanMousedown");Ee(this,"cleanMouseup");this.el=t,this.options=n,this.scope=Vue.effectScope(),this.scope.run(()=>{this.init()})}init(){const{el:t,options:n}=this,{disabled:e,onStart:r,onEnd:l}=n;if(e)return;this.MIE=VueUse.useMouseInElement(t),this.cleanMousedown=VueUse.useEventListener(document,"mousedown",()=>{var i;(i=this.direction)!=null&&i.value&&this.MIE&&(this.resizing.value=!0,t.classList.add("is-resizing",`is-${this.direction.value}-resizing`),r&&r(this.direction.value,this.MIE))}),this.cleanMouseup=VueUse.useEventListener(document,"mouseup",()=>{var i;this.resizing.value&&((i=this.direction)!=null&&i.value)&&this.MIE&&(t.classList.remove("is-resizing",`is-${this.direction.value}-resizing`),l&&l(this.direction.value,this.MIE)),this.resizing.value=!1}),Vue.watch(this.direction,i=>{const s=document.body;s.style.cursor=i?`${i}-resize`:"",i?s.classList.add(Kt):s.classList.remove(Kt)});const{x:u,y:a}=this.MIE;Vue.watch([u,a],()=>{this.resizing.value?this.resize():this.direction.value=this.getDirection()})}resize(){const{MIE:t,direction:n,resizing:e,options:r,el:l}=this,u=(n==null?void 0:n.value)||"";if(!t||!e.value||!u)return;const{x:a,y:i,elementX:s,elementY:c,elementHeight:d,elementWidth:m}=t,{onResizing:p}=r,{minWidth:f=0,minHeight:V=0,maxWidth:h=99999,maxHeight:v=99999}=r;if(u.includes("e")){const y=Math.min(Math.max(s.value,f),h);l.style.width=`${y}px`}if(u.includes("s")){const y=Math.min(Math.max(c.value,V),v);l.style.height=`${y}px`}if(u.includes("w")){const y=Math.min(Math.max(m.value-s.value,f),h);l.style.width=`${y}px`,l.style.left=`${a.value}px`}if(u.includes("n")){const y=Math.min(Math.max(d.value-c.value,V),v);this.el.style.height=`${y}px`,this.el.style.top=`${i.value}px`}p&&p(u,t)}getDirection(){if(!this.MIE)return"";const{elementX:t,elementY:n,elementHeight:e,elementWidth:r,isOutside:l}=this.MIE;if(l.value)return"";const{dirs:u=["n","s","w","e"],edge:a=5}=this.options;let i="";return u.includes("n")&&n.value<=a?i+="n":u.includes("s")&&n.value>e.value-a&&(i+="s"),u.includes("w")&&t.value<=a?i+="w":u.includes("e")&&t.value>r.value-a&&(i+="e"),i}destory(){var n;const t=document.body;t.style.cursor="",t.classList.remove(Kt),this.cleanMousedown&&this.cleanMousedown(),this.cleanMouseup&&this.cleanMouseup(),(n=this.MIE)==null||n.stop(),this.scope.stop()}}const Gt={mounted(o,t){const n=t.value||{},e=new Yt(o,n);o.__resizable__=e},updated(o,t){const n=t.value||{};let e=o.__resizable__;e&&!VtjUtils.isEqual(e.options,n)&&(e.destory(),o.__resizable__=new Yt(o,n))},unmounted(o){const t=o.__resizable__;t&&(t.destory(),o.__resizable__=null)}},co={modelValue:{type:Boolean,default:!0},title:{type:String},subtitle:{type:String},icon:{type:[String,Object]},size:{type:String,default:"default"},width:{type:[Number,String],default:"70%"},height:{type:[Number,String],default:"70%"},left:{type:[Number,String]},top:{type:[Number,String]},modal:{type:Boolean,default:!0},draggable:{type:[Boolean,Object],default:!0},resizable:{type:[Boolean,Object]},closable:{type:Boolean,default:!0},maximizable:{type:Boolean,default:!1},minimizable:{type:Boolean,default:!1},mode:{type:String,default:"normal"},content:{type:Object},src:{type:String},componentInstance:{type:Object},beforeClose:{type:Function},submit:{type:[Boolean,String]},cancel:{type:[Boolean,String]},bodyPadding:{type:Boolean,default:!0},primary:{type:Boolean},pure:{type:Boolean},zIndex:{type:Number}};let He=1e3;function ml(o,t){const{width:n,height:e}=VueUse.useElementSize(t),r=Vue.reactive({mode:o.mode||"normal",wrapperWidth:0,wrapperHeight:0,width:0,height:0,top:0,left:0,zIndex:o.zIndex||++He,dragging:!1,resizing:!1});return Vue.watch([n,e],([l,u])=>{r.wrapperWidth=l,r.wrapperHeight=u,r.width=tt(o.width,l),r.height=tt(o.height,u),r.left=o.left?tt(o.left,l)-r.width/2:Math.max(Math.floor((l-r.width)/2),0),r.top=o.top?tt(o.top,u):Math.max(Math.floor((u-r.height)/2),0)}),Vue.watch(r,l=>{He=Math.max(l.zIndex,He,o.zIndex||1)}),{state:r,normal:Vue.computed(()=>r.mode==="normal"),maximized:Vue.computed(()=>r.mode==="maximized"),minimized:Vue.computed(()=>r.mode==="minimized")}}function Vl(o,t){const n=Vue.computed(()=>{const{width:a,height:i,top:s,left:c,zIndex:d}=t;return{width:`${a}px`,height:`${i}px`,top:`${s}px`,left:`${c}px`,zIndex:d}}),e=Vue.computed(()=>{const{zIndex:a}=t;return{zIndex:a}}),r=Vue.computed(()=>({[`is-${t.mode}`]:!!t.mode,"is-draggable":!!o.draggable,"is-resizable":!!o.resizable,"is-primary":!!o.primary,"is-pure":!!o.pure})),l=Vue.computed(()=>({[`is-${t.mode}`]:!!t.mode,"is-dragging":t.dragging,"is-resizing":t.resizing})),u=Vue.computed(()=>({zIndex:t.zIndex}));return{styles:n,classes:r,wrapperClass:l,modalStyle:u,zIndexStyle:e}}function hl(o,t,n){const e=c=>{t.mode=c,["maximized","minimized","normal"].includes(c)&&n(c),n("modeChange",c)},r=async()=>{(!o.beforeClose||await o.beforeClose())&&(n("update:modelValue",!1),n("close"),n("destroy"))};return{close:r,changeMode:e,show:()=>e("normal"),hide:()=>e("minimized"),active:c=>{const d=c.target.nodeName||"";["INPUT","TEXTAREA","RADIO","CHECKBOX"].includes(d.toUpperCase())||(t.zIndex=Math.max(t.zIndex,++He,o.zIndex||1))},submit:()=>n("submit"),cancel:()=>{n("cancel"),r()}}}function gl(o,t,n,e){return Vue.computed(()=>{var l;const r=typeof o.draggable=="boolean"?!o.draggable:!!((l=o.draggable)!=null&&l.disabled);return{...VtjUtils.isObject(o.draggable)?o.draggable:{},disabled:r,target:e,selector:".x-panel__header",onStart(u){t.dragging=!0,t.zIndex=Math.max(t.zIndex,++He),n("dragStart",u)},onMove(u){n("dragging",u)},onEnd(u){if(t.mode==="maximized")return;const{x:a,y:i}=u;t.left=a,t.top=i,t.dragging=!1,n("dragEnd",u)}}})}function vl(o,t,n){return Vue.computed(()=>{var r;const e=typeof o.resizable=="boolean"?!o.resizable:!!((r=o.resizable)!=null&&r.disabled);return{minWidth:200,minHeight:150,...VtjUtils.isObject(o.resizable)?o.resizable:{},disabled:e,dirs:["e","s","w"],onStart(l,u){t.resizing=!0,t.zIndex=Math.max(t.zIndex,++He),n("resizeStart",l,u)},onResizing(l,u){n("resizing",l,u)},onEnd(l,u){const a=document.body.getBoundingClientRect();t.left=u.elementPositionX.value-a.x,t.top=u.elementPositionY.value-a.y,t.width=u.elementWidth.value,t.height=u.elementHeight.value,t.resizing=!1,n("resizeEnd",l,u)}}})}function yl(o,t){const n=o.componentInstance;if(!n)return;let e=null;return Vue.onMounted(()=>{const r=Vue.unref(t),l=Vue.unref(r==null?void 0:r.bodyRef);e=n.$el,l&&l.$el&&l.$el.appendChild(e)}),Vue.onUnmounted(()=>{e&&e.parentNode&&e.parentNode.removeChild(e)}),{componentInstance:n}}const wl=["src"],Xe=Vue.defineComponent({name:"XDialog",__name:"Dialog",props:co,emits:["update:modelValue","open","close","destroy","maximized","minimized","normal","modeChange","dragStart","dragging","dragEnd","resizeStart","resizeEnd","resizing","submit","cancel"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.getCurrentInstance(),u=Vue.ref(),a=Vue.ref(),{state:i,maximized:s,minimized:c,normal:d}=ml(e,u),{styles:m,classes:p,wrapperClass:f,modalStyle:V,zIndexStyle:h}=Vl(e,i),{changeMode:v,active:y,close:b,show:w,hide:_,submit:C,cancel:g}=hl(e,i,r),x=gl(e,i,r,u),S=vl(e,i,r),I=yl(e,a);Vue.watchEffect(async()=>{e.modelValue?(await Vue.nextTick(),l&&(r("open",l),document.body.classList.add("x-dialog-visible"))):document.body.classList.remove("x-dialog-visible")}),Vue.onUnmounted(()=>{document.body.classList.remove("x-dialog-visible")});const z=Vue.computed(()=>{var N;return(N=a.value)==null?void 0:N.$el});return t({$vtjEl:z,panelRef:a,state:i,maximized:s,minimized:c,changeMode:v,show:w,hide:_,submit:C,cancel:g,close:b,componentInstance:I}),(N,T)=>e.modelValue?(Vue.openBlock(),Vue.createBlock(Vue.Teleport,{key:0,to:"body"},[Vue.createElementVNode("div",{ref_key:"wrapper",ref:u,class:Vue.normalizeClass(["x-dialog__wrapper",Vue.unref(f)]),style:Vue.normalizeStyle(Vue.unref(h))},[e.modal?(Vue.openBlock(),Vue.createElementBlock("div",{key:0,class:"x-dialog__modal",style:Vue.normalizeStyle(Vue.unref(V))},null,4)):Vue.createCommentVNode("",!0),Vue.withDirectives((Vue.openBlock(),Vue.createBlock(Vue.unref(Le),Vue.mergeProps({ref_key:"panelRef",ref:a,class:["x-dialog",Vue.unref(p)],card:"",shadow:"always",header:{icon:e.icon,subtitle:e.subtitle},width:"800px",height:"600px",footer:{justify:"space-between",flex:!0,align:"center"},style:Vue.unref(m),size:e.size,body:{padding:e.bodyPadding},onClick:Vue.unref(y)},N.$attrs),Vue.createSlots({title:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(e.title),1)]),actions:Vue.withCtx(()=>[Vue.renderSlot(N.$slots,"actions"),e.minimizable?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:0,icon:Vue.unref(VtjIcons.Minimize),size:e.size,mode:"icon",type:"primary",background:"hover",onClick:T[0]||(T[0]=A=>Vue.unref(v)("minimized"))},null,8,["icon","size"])):Vue.createCommentVNode("",!0),e.maximizable?(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,{key:1},[Vue.unref(d)?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:1,icon:Vue.unref(VtjIcons.Maximize),size:e.size,mode:"icon",type:"primary",background:"hover",onClick:T[2]||(T[2]=A=>Vue.unref(v)("maximized"))},null,8,["icon","size"])):(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:0,icon:Vue.unref(VtjIcons.Popup),size:e.size,mode:"icon",type:"primary",background:"hover",onClick:T[1]||(T[1]=A=>Vue.unref(v)("normal"))},null,8,["icon","size"]))],64)):Vue.createCommentVNode("",!0),e.closable?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:2,icon:Vue.unref(VtjIcons.RawClose),size:e.size,mode:"icon",type:"danger",background:"hover",onClick:Vue.unref(b)},null,8,["icon","size","onClick"])):Vue.createCommentVNode("",!0)]),default:Vue.withCtx(()=>[Vue.renderSlot(N.$slots,"default",{},()=>[e.content?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(e.content),{key:0})):Vue.createCommentVNode("",!0),e.src?(Vue.openBlock(),Vue.createElementBlock("iframe",{key:1,src:e.src,class:"x-dialog__frame"},null,8,wl)):Vue.createCommentVNode("",!0)])]),_:2},[e.cancel||e.submit||N.$slots.footer||N.$slots.extra||N.$slots.handle?{name:"footer",fn:Vue.withCtx(()=>[Vue.renderSlot(N.$slots,"footer",{},()=>[Vue.createVNode(Vue.unref(j),{align:"center"},{default:Vue.withCtx(()=>[Vue.renderSlot(N.$slots,"extra")]),_:3}),Vue.createVNode(Vue.unref(j),{align:"center"},{default:Vue.withCtx(()=>[Vue.renderSlot(N.$slots,"handle"),e.cancel?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElButton),{key:0,type:"default",size:e.size,onClick:Vue.unref(g)},{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(typeof e.cancel=="string"?e.cancel:"取消"),1)]),_:1},8,["size","onClick"])):Vue.createCommentVNode("",!0),e.submit?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElButton),{key:1,type:"primary",size:e.size,onClick:Vue.unref(C)},{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(typeof e.submit=="string"?e.submit:"确定"),1)]),_:1},8,["size","onClick"])):Vue.createCommentVNode("",!0)]),_:3})])]),key:"0"}:void 0]),1040,["header","class","style","size","body","onClick"])),[[Vue.unref(so),Vue.unref(x)],[Vue.unref(Gt),Vue.unref(S)]])],6)])):Vue.createCommentVNode("",!0)}});function gt(o,t){const n=document.createElement("div"),e=Vue.createVNode(Xe,o);e.appContext=t!=null?t:gt._context,Vue.render(e,n);const r=()=>{var l;Vue.render(null,n),(l=n.parentNode)==null||l.removeChild(n)};return e.props.onDestroy=()=>{r()},document.body.appendChild(n),{vnode:e,destroy:r}}const bl=Vue.defineComponent({__name:"Sidebar",props:{collapsed:{type:Boolean}},setup(o){const t=o;return(n,e)=>Vue.withDirectives((Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:Vue.normalizeClass(["x-mask-sidebar",{"is-collapsed":t.collapsed}]),grow:!1,flex:"",direction:"column"},{default:Vue.withCtx(()=>[Vue.renderSlot(n.$slots,"brand"),Vue.createVNode(Vue.unref(j),{class:"x-mask-sidebar__wrapper",flex:"",grow:"",direction:"column",justify:"space-between",align:"center"},{default:Vue.withCtx(()=>[Vue.renderSlot(n.$slots,"default"),e[0]||(e[0]=Vue.createElementVNode("div",{class:"x-mask-sidebar__helper"},null,-1))]),_:3,__:[0]})]),_:3},8,["class"])),[[Vue.unref(Gt),{dirs:["e"],disabled:t.collapsed,maxWidth:500,minWidth:200}]])}}),Cl=Vue.defineComponent({__name:"SwitchBar",props:{collasped:{type:Boolean,default:!1},favorite:{type:Boolean,default:!1},keyword:{}},emits:["update:collasped","update:keyword","update:favorite"],setup(o,{emit:t}){const n=o,e=t,r=Vue.ref(!1),l=Vue.ref(""),u=Vue.ref(!1),a=()=>{r.value=!0},i=()=>{l.value="",r.value=!1,e("update:keyword",l.value)},s=()=>{e("update:collasped",!n.collasped)},c=()=>{e("update:keyword",l.value)},d=()=>{e("update:favorite",u.value)};return(m,p)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:"x-mask-switch-bar",justify:"space-between",align:"center"},{default:Vue.withCtx(()=>[n.collasped?Vue.createCommentVNode("",!0):(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{key:0,class:"x-mask-switch-bar__left",grow:""},{default:Vue.withCtx(()=>[r.value?Vue.createCommentVNode("",!0):(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElSwitch),{key:0,modelValue:u.value,"onUpdate:modelValue":p[0]||(p[0]=f=>u.value=f),class:"x-mask-switch-bar__switch","active-icon":Vue.unref(VtjIcons.StarFilled),"inactive-icon":Vue.unref(VtjIcons.Menu),onChange:d},null,8,["modelValue","active-icon","inactive-icon"])),r.value?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElInput),{key:1,modelValue:l.value,"onUpdate:modelValue":p[1]||(p[1]=f=>l.value=f),class:"x-mask-switch-bar__input",size:"small",placeholder:"请输入查询关键字",onInput:c,"prefix-icon":Vue.unref(VtjIcons.Search)},{suffix:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(de),{icon:Vue.unref(VtjIcons.Close),onClick:i},null,8,["icon"])]),_:1},8,["modelValue","prefix-icon"])):Vue.createCommentVNode("",!0)]),_:1})),Vue.createVNode(Vue.unref(j),{class:Vue.normalizeClass(["x-mask-switch-bar__right",{"is-collasped":n.collasped}]),flex:"",align:"center",justify:"center"},{default:Vue.withCtx(()=>[!r.value&&!n.collasped?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:0,icon:Vue.unref(VtjIcons.Search),mode:"icon",background:"hover",size:"default",circle:"",onClick:a},null,8,["icon"])):Vue.createCommentVNode("",!0),n.collasped?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:1,icon:Vue.unref(VtjIcons.Expand),mode:"icon",size:"default",background:"hover",onClick:s,circle:""},null,8,["icon"])):Vue.createCommentVNode("",!0),n.collasped?Vue.createCommentVNode("",!0):(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:2,icon:Vue.unref(VtjIcons.Fold),mode:"icon",size:"default",background:"hover",circle:"",onClick:s},null,8,["icon"]))]),_:1},8,["class"])]),_:1}))}}),_l=["src"],kl={key:0},El=Vue.defineComponent({__name:"Brand",props:{collapsed:{type:Boolean,default:!1},logo:{},title:{},url:{}},setup(o){const t=o,n=VueRouter.useRouter(),e=()=>{t.url&&n.push(t.url)};return(r,l)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:Vue.normalizeClass(["x-mask-brand",{"is-collapsed":t.collapsed}]),align:"center"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(j),{class:"x-mask-brand__logo",flex:"",justify:"center",align:"center",onClick:e},{default:Vue.withCtx(()=>[Vue.renderSlot(r.$slots,"logo",{},()=>[t.logo?(Vue.openBlock(),Vue.createElementBlock("img",{key:0,src:t.logo},null,8,_l)):Vue.createCommentVNode("",!0)])]),_:3}),Vue.createVNode(Vue.unref(j),{class:"x-mask-brand__title",flex:"",align:"center"},{default:Vue.withCtx(()=>[t.title?(Vue.openBlock(),Vue.createElementBlock("span",kl,[Vue.renderSlot(r.$slots,"title",{},()=>[Vue.createTextVNode(Vue.toDisplayString(t.title),1)])])):Vue.createCommentVNode("",!0)]),_:3})]),_:3},8,["class"]))}}),vt="__favorites__",yt="__search__",Sl=Vue.defineComponent({__name:"Menu",props:{collapse:{type:Boolean,default:!1},keyword:{},favorite:{type:Boolean},favorites:{},flatMenus:{},menus:{},active:{}},emits:["select"],setup(o,{emit:t}){const n=o,e=Vue.computed(()=>n.menus||[]),r=t,l=Vue.computed(()=>{var s;return String((s=n.active)==null?void 0:s.id)}),u=Vue.computed(()=>{var s;return[{id:vt,title:"收藏",icon:VtjIcons.Star,children:(s=n.favorites)!=null&&s.length?n.favorites:[{id:vt+"empty",disabled:!0,title:"暂无收藏菜单"}]}]}),a=Vue.computed(()=>{const s=(n.keyword||"").trim(),c=s?(n.flatMenus||[]).filter(d=>{var m;return(m=d.title)==null?void 0:m.includes(s)}):[];return[{id:yt,title:"搜索",icon:VtjIcons.Search,children:c!=null&&c.length?c:[{id:yt+"empty",disabled:!0,title:"查询匹配不到菜单项"}]}]}),i=s=>{r("select",s)};return(s,c)=>(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,null,[Vue.withDirectives(Vue.createVNode(Vue.unref(j),{class:"x-mask-menu",grow:"",flex:!1,overflow:"auto"},{default:Vue.withCtx(()=>[Vue.withDirectives(Vue.createVNode(Vue.unref(Se),{subMenu:{popperClass:"x-mask-menu-popper",teleported:!0,showTimeout:200,hideTimeout:200},data:e.value,"default-icon":Vue.unref(VtjIcons.Document),collapse:!0,"collapse-transition":!1,"default-active":l.value,onSelect:i},null,8,["data","default-icon","default-active"]),[[Vue.vShow,n.collapse]]),Vue.withDirectives(Vue.createVNode(Vue.unref(Se),{subMenu:{popperClass:"x-mask-menu-popper",teleported:!0},data:e.value,"default-icon":Vue.unref(VtjIcons.Document),collapse:!1,"collapse-transition":!1,"default-active":l.value,onSelect:i},null,8,["data","default-icon","default-active"]),[[Vue.vShow,!n.collapse]]),e.value.length?Vue.createCommentVNode("",!0):(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElEmpty),{key:0,description:"暂无菜单数据"}))]),_:1},512),[[Vue.vShow,!n.favorite&&!n.keyword]]),Vue.withDirectives(Vue.createVNode(Vue.unref(j),{class:"x-mask-menu",grow:"",flex:!1,overflow:"auto"},{default:Vue.withCtx(()=>[Vue.withDirectives(Vue.createVNode(Vue.unref(Se),{class:"x-mask-menu__favorites",subMenu:{popperClass:"x-mask-menu-popper",teleported:!0,showTimeout:200,hideTimeout:200},data:u.value,"default-icon":Vue.unref(VtjIcons.Document),collapse:!0,"collapse-transition":!1,"default-active":l.value,"default-openeds":[vt],onSelect:i},null,8,["data","default-icon","default-active","default-openeds"]),[[Vue.vShow,n.collapse]]),Vue.withDirectives(Vue.createVNode(Vue.unref(Se),{class:"x-mask-menu__favorites",subMenu:{popperClass:"x-mask-menu-popper",teleported:!0,showTimeout:200,hideTimeout:200},data:u.value,"default-icon":Vue.unref(VtjIcons.Document),collapse:!1,"collapse-transition":!1,"default-active":l.value,"default-openeds":[vt],onSelect:i},null,8,["data","default-icon","default-active","default-openeds"]),[[Vue.vShow,!n.collapse]])]),_:1},512),[[Vue.vShow,n.favorite&&!n.keyword]]),n.keyword?Vue.withDirectives((Vue.openBlock(),Vue.createBlock(Vue.unref(j),{key:0,class:"x-mask-menu",grow:"",flex:!1,overflow:"auto"},{default:Vue.withCtx(()=>[Vue.withDirectives(Vue.createVNode(Vue.unref(Se),{class:"x-mask-menu__search",subMenu:{popperClass:"x-mask-menu-popper",teleported:!0,showTimeout:200,hideTimeout:200},data:a.value,"default-icon":Vue.unref(VtjIcons.Document),collapse:!0,"collapse-transition":!1,"default-active":l.value,"default-openeds":[yt],onSelect:i},null,8,["data","default-icon","default-active","default-openeds"]),[[Vue.vShow,n.collapse]]),Vue.withDirectives(Vue.createVNode(Vue.unref(Se),{class:"x-mask-menu__search",subMenu:{popperClass:"x-mask-menu-popper",teleported:!0,showTimeout:200,hideTimeout:200},data:a.value,"default-icon":Vue.unref(VtjIcons.Document),collapse:!1,"collapse-transition":!1,"default-active":l.value,"default-openeds":[yt],onSelect:i},null,8,["data","default-icon","default-active","default-openeds"]),[[Vue.vShow,!n.collapse]])]),_:1},512)),[[Vue.vShow,!!n.keyword]]):Vue.createCommentVNode("",!0)],64))}}),xl="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1711803009570'%20class='icon'%20viewBox='0%200%201280%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='1500'%20width='320'%20height='256'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3cpath%20d='M557.85%201023l-122-35.4c-12.8-3.6-20-17-16.4-29.8L692.45%2017.4c3.6-12.8%2017-20%2029.8-16.4l122%2035.4c12.8%203.6%2020%2017%2016.4%2029.8L587.65%201006.6c-3.8%2012.8-17%2020.2-29.8%2016.4z%20m-228-224.4l87-92.8c9.2-9.8%208.6-25.4-1.6-34.4L234.05%20512l181.2-159.4c10.2-9%2011-24.6%201.6-34.4l-87-92.8c-9-9.6-24.2-10.2-34-1L7.65%20494.4c-10.2%209.4-10.2%2025.6%200%2035l288.2%20270.2c9.8%209.2%2025%208.8%2034-1z%20m654.4%201.2l288.2-270.2c10.2-9.4%2010.2-25.6%200-35L984.25%20224.2c-9.6-9-24.8-8.6-34%201L863.25%20318c-9.2%209.8-8.6%2025.4%201.6%2034.4L1046.05%20512l-181.2%20159.4c-10.2%209-11%2024.6-1.6%2034.4l87%2092.8c9%209.8%2024.2%2010.2%2034%201.2z'%20fill='%230157fe'%20p-id='1501'%3e%3c/path%3e%3c/svg%3e",fo=140,po={logo:{type:String,default:xl},title:{type:String,default:"VTJ.PRO"},menus:{type:[Array,Function],default(){return[]}},favorites:{type:[Array,Function],default(){return[]}},menuAdapter:{type:Function},home:{type:[String,Object],default:"/"},tabs:{type:Number,default:20},actions:{type:Array},avatar:{type:String},theme:{type:Boolean},disabled:{type:Boolean},addFavorite:{type:Function},removeFavorite:{type:Function},userCardWidth:{type:Number,default:350},pure:{type:Boolean}},Wt=Symbol(),Jt="$mask",Bl={class:"x-mask-tabs__trigger"},Pl={key:1},Nl=["onDragstart","onDragend"],Tl={key:1},Dl=Vue.defineComponent({__name:"Tabs",props:{tabs:{},home:{},isActiveTab:{type:Function},value:{},favorites:{}},emits:["click","remove","refresh","toggleFavorite","dialog"],setup(o,{emit:t}){const n=o,e=t,r=c=>{const d=!!n.favorites.find(m=>{var p;return m===c.menu||m.id===((p=c.menu)==null?void 0:p.id)});return[{icon:VtjIcons.Refresh,label:"刷新",name:"refresh",value:c},"|",{icon:d?VtjIcons.StarFilled:VtjIcons.Star,label:"收藏",name:"favorite",value:c.menu,disabled:!c.menu||!!c.menu.hidden},"|",{icon:VtjIcons.CopyDocument,label:"弹窗",name:"dialog",value:c}]},l=c=>{const d=c.paneName;if(d===n.home.id){e("click",n.home);return}const m=n.tabs.find(p=>p.id===d);m&&e("click",m)},u=c=>{const d=n.tabs.find(m=>m.id===c);d&&e("remove",d)},a=c=>{switch(c.name){case"refresh":e("refresh",c.value);break;case"favorite":e("toggleFavorite",c.value);break;case"dialog":e("dialog",c.value);break}},i=(c,d)=>{d.dataTransfer&&(d.dataTransfer.setData("tab",c.id),d.target&&d.target.classList.add("is-dagging"))},s=(c,d)=>{d.target&&d.target.classList.remove("is-dagging")};return(c,d)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{ref:"tabsRef",class:"x-mask-tabs",height:"100%",grow:"",flex:"",justify:"flex-end",direction:"column"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElTabs),{type:"card","model-value":n.value,onTabRemove:u,onTabClick:l},{default:Vue.withCtx(()=>[n.home?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElTabPane),{key:0,name:n.home.id},{label:Vue.withCtx(()=>[Vue.createElementVNode("div",Bl,[n.home.icon?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(De)(n.home.icon)),{key:0})):Vue.createCommentVNode("",!0),n.home.title?(Vue.openBlock(),Vue.createElementBlock("span",Pl,Vue.toDisplayString(n.home.title),1)):Vue.createCommentVNode("",!0)])]),_:1},8,["name"])):Vue.createCommentVNode("",!0),(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(n.tabs,m=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElTabPane),{key:m.id,name:m.id,lazy:"",closable:""},{label:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElPopover),{"open-delay":500,placement:"bottom",trigger:"hover",width:"200px",disabled:m.id!==n.value},{reference:Vue.withCtx(()=>[Vue.createElementVNode("div",{class:"x-mask-tabs__trigger",draggable:"true",onDragstart:p=>i(m,p),onDragend:p=>s(m,p)},[m.icon?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(De)(m.icon)),{key:0})):Vue.createCommentVNode("",!0),m.title?(Vue.openBlock(),Vue.createElementBlock("span",Tl,Vue.toDisplayString(m.title),1)):Vue.createCommentVNode("",!0)],40,Nl)]),default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ze),{items:r(m),mode:"text",size:"small",type:"info",onClick:a},null,8,["items"])]),_:2},1032,["disabled"])]),_:2},1032,["name"]))),128))]),_:1},8,["model-value"])]),_:1},512))}}),zl=Vue.defineComponent({__name:"ThemeSwitch",setup(o){const t=VueUse.useDark(),n=Vue.computed(()=>t.value?VtjIcons.Sunny:VtjIcons.Moon),e=()=>{t.value=!t.value};return(r,l)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{icon:n.value,dropdown:{size:"small"},mode:"icon",circle:"",background:"hover",type:"primary",onClick:e},null,8,["icon"]))}}),Il={class:"x-mask-toolbar__menu-item"},Ml=Vue.defineComponent({__name:"Toolbar",props:{tabs:{default:()=>[]},actions:{},theme:{type:Boolean}},emits:["closeOtherTabs","closeAllTabs","closeTab","clickTab","actionClick","actionCommand"],setup(o,{emit:t}){const n=o,e=t,r=Vue.computed(()=>{const s=n.tabs.map((c,d)=>({divided:d===0,label:c.title,command:c}));return[{label:"关闭其他",command:"other"},{label:"关闭全部",command:"all"},...s]}),l=s=>{e("closeTab",s.command)},u=s=>{switch(s.command){case"all":e("closeAllTabs");break;case"other":e("closeOtherTabs");break;default:e("clickTab",s.command);break}},a=s=>{e("actionClick",s)},i=(s,c)=>{e("actionCommand",s,c)};return(s,c)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:"x-mask-toolbar",align:"center"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(Q),{icon:Vue.unref(VtjIcons.MoreFilled),menus:r.value,dropdown:{size:"small"},type:"primary",mode:"icon",circle:"",background:"hover",onCommand:u},{item:Vue.withCtx(({item:d})=>[Vue.createElementVNode("span",Il,[Vue.createTextVNode(Vue.toDisplayString(d.label)+" ",1),["other","all"].includes(d.command)?Vue.createCommentVNode("",!0):(Vue.openBlock(),Vue.createBlock(Vue.unref(de),{key:0,onClick:Vue.withModifiers(m=>l(d),["stop"]),icon:Vue.unref(VtjIcons.Close)},null,8,["onClick","icon"]))])]),_:1},8,["icon","menus"]),Vue.createVNode(Vue.unref(ElementPlus.ElDivider),{direction:"vertical"}),n.actions?(Vue.openBlock(),Vue.createBlock(Vue.unref(ze),{key:0,circle:"",mode:"icon",size:"default",background:"hover",type:"primary",items:s.actions,onClick:a,onCommand:i},null,8,["items"])):Vue.createCommentVNode("",!0),n.actions?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElDivider),{key:1,direction:"vertical"})):Vue.createCommentVNode("",!0),n.theme?(Vue.openBlock(),Vue.createBlock(zl,{key:2})):Vue.createCommentVNode("",!0),n.theme?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElDivider),{key:3,direction:"vertical"})):Vue.createCommentVNode("",!0),Vue.renderSlot(s.$slots,"default")]),_:3}))}}),Al=Vue.defineComponent({__name:"Avatar",props:{avatar:{},width:{default:350}},setup(o){const t=o;return(n,e)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElPopover),{width:t.width,"popper-class":"x-mask-avatar__popper",disabled:!n.$slots.default},{reference:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElAvatar),{class:"x-mask-avatar",shape:"circle",icon:Vue.unref(VtjIcons.UserFilled),src:t.avatar,size:26},null,8,["icon","src"])]),default:Vue.withCtx(()=>[n.$slots.default?Vue.renderSlot(n.$slots,"default",{key:0}):Vue.createCommentVNode("",!0)]),_:3},8,["width","disabled"]))}}),mo=Vue.defineComponent({__name:"Content",props:{createView:{type:Function},exclude:{},pure:{type:Boolean}},setup(o){const t=o;return(n,e)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:"x-mask__content",overflow:"auto",flex:!1,grow:"",padding:!1},{default:Vue.withCtx(()=>[Vue.createElementVNode("div",{class:Vue.normalizeClass(["x-mask__inner",{"is-pure":t.pure}])},[Vue.renderSlot(n.$slots,"default"),Vue.createVNode(Vue.unref(VueRouter.RouterView),null,{default:Vue.withCtx(({Component:r,route:l})=>[(Vue.openBlock(),Vue.createBlock(Vue.KeepAlive,{exclude:t.exclude},[r?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(t.createView(r,l)),{key:l.fullPath})):Vue.createCommentVNode("",!0)],1032,["exclude"]))]),_:1})],2)]),_:3}))}});function Rl(){const o=Vue.ref(!1),t=Vue.ref(""),n=Vue.ref(!1);return{collapsed:o,keyword:t,favorite:n}}function $l(o){const t=VtjUtils.uid();return Vue.computed(()=>{const n=o.home;return Object.assign({id:t,url:"/",name:"MaskHome",icon:VtjIcons.HomeFilled,closable:!1},typeof n=="string"?{url:n}:n||{})})}function Vo(o,t){let n=[];return o.forEach(e=>{e=t?t(e):e,e.children?n=n.concat(Vo(e.children,t)):n.push(e)}),n}function Fl(o,t){const n=VueRouter.useRouter(),e=Vue.shallowRef([]),r=Vue.shallowRef([]),l=Vue.computed(()=>Vo(e.value,o.menuAdapter)),u=Vue.computed(()=>VtjUtils.arrayToMap(l.value,"id")),a=Vue.ref(null),i=f=>{const V=typeof f=="object"?f.id:f,h=u.value.get(V);if(!h){console.warn("找不到菜单",f);return}const{type:v="route",url:y,title:b,icon:w}=h;if(!y){a.value=h,t("select",h);return}if(v==="route"){VtjUtils.isUrl(y)||y.startsWith("//")?window.open(y):(a.value=h,n.push(y).catch(_=>_));return}if(v==="window"){window.open(y);return}v==="dialog"&&gt({resizable:!0,bodyPadding:!1,width:"80%",height:"80%",title:b,icon:w,src:y})},s=async()=>{var f,V;e.value=typeof o.menus=="function"?await o.menus()||[]:(f=o.menus)!=null?f:[],r.value=typeof o.favorites=="function"?await o.favorites()||[]:(V=o.favorites)!=null?V:[]},c=f=>{r.value=[f,...r.value],o.addFavorite&&o.addFavorite(f)},d=f=>{r.value=r.value.filter(V=>V.id!==f.id),o.removeFavorite&&o.removeFavorite(f)},m=f=>!!r.value.find(V=>V===f||V.id===f.id),p=f=>{m(f)?d(f):c(f)};return Vue.watchEffect(s),{menus:e,favorites:r,flatMenus:l,active:a,select:i,toggleFavorite:p}}function Ol(o,t,n,e,r){const l=VueRouter.useRoute(),u=VueRouter.useRouter(),a={},i=Vue.ref([]),s=Vue.ref(),{width:c}=VueUse.useElementSize(s),d=Vue.computed(()=>Math.floor(c.value/fo)),m=Vue.computed(()=>i.value.slice(0,d.value).filter(M=>!M.dialog)),p=Vue.computed(()=>i.value.slice(d.value)),f=M=>n.value.find(k=>k.url===M),V=M=>l.fullPath===M.url,h=M=>r.value.id===M.id,v=M=>r.value.id===M?r.value:i.value.find(k=>k.id===M),y=Vue.ref(""),b=Vue.computed(()=>v(y.value)),w=M=>{u.push(M.url).catch(k=>k)},_=M=>{y.value=M.id,l.fullPath!==M.url&&w(M)},C=()=>{w(r.value)},g=M=>{const k=i.value.find(E=>E.url===M.url||E.id===M.id);k?_(k):(i.value.unshift(M),_(M))},x=async M=>{var we;const{url:k=l.fullPath,icon:E,title:D}=M||{},O=a[k],X=VtjUtils.uid(),ue=VtjUtils.upperFirstCamelCase(k);return{id:X,name:ue,url:k,icon:E,title:D||((we=f(l.path))==null?void 0:we.title)||"新建页签",closable:!0,menu:M,...O?await O():{}}},S=async(M,k)=>{if(!(!k&&!await ElementPlus.ElMessageBox.confirm("是否关闭页签","提示",{type:"warning"}).catch(()=>!1))){if(i.value=i.value.filter(E=>E.id!==M.id),y.value===M.id){const E=i.value[0];w(E||r.value)}return M}},I=M=>{const k=i.value.findIndex(E=>E.id===M.id);if(k>=0){const E=i.value[k];i.value.splice(k,1,Object.assign(E,M))}},z=async()=>{if(!await ElementPlus.ElMessageBox.confirm("是否关闭全部页签","提示",{type:"warning"}).catch(()=>!1))return;const k=i.value;return i.value=[],_(r.value),k},N=async()=>{if(!await ElementPlus.ElMessageBox.confirm("是否关闭其他页签","提示",{type:"warning"}).catch(()=>!1))return;const k=i.value.filter(E=>E.id!==y.value);return i.value=i.value.filter(E=>E.id===y.value),k},T=M=>{const k=i.value.filter(E=>E.id!==M.id);i.value=[M,...k],_(M)},A=async M=>{b.value&&b.value.closable&&(h(b.value)||(await S(b.value,!0),await VtjUtils.delay(0),M?u.push(M):u.push(r.value.url)))},Y=async()=>{await Vue.nextTick();const M=r.value.url===l.path,k=f(l.fullPath);if(M)y.value=r.value.id;else{const E=await x(k);g(E)}await Vue.nextTick(),e.value=k||null};return Vue.watch(n,Y),Vue.watch(l,Y,{immediate:!0}),Vue.provide(Wt,a),{tabRef:s,tabs:i,showTabs:m,currentTab:b,changeTab:w,removeTab:S,updateTab:I,addTab:g,home:r,tabValue:y,isCurrentTab:V,activeHome:C,activeTab:_,dropdownTabs:p,removeAllTabs:z,removeOtherTabs:N,moveToShow:T,closeCurrnetTab:A}}function jl(o,t){const n=new Map,e=Vue.ref([]),r=Vue.reactive({}),l=Vue.getCurrentInstance(),u={},{updateTab:a,isCurrentTab:i,activeHome:s,tabs:c}=o,d=(w,_)=>{const C=_.fullPath;if(n.has(C))return n.get(C);{const g=VtjUtils.upperFirstCamelCase(C),x={name:g,setup(){const S=Vue.computed(()=>r[C]||document.body),I=Vue.computed(()=>!r[C]),z=Vue.computed(()=>!e.value.includes(g));return Vue.provide(VueRouter.routeLocationKey,Vue.toRaw({..._})),()=>z.value?Vue.h(Vue.Teleport,{to:S.value,disabled:I.value},[Vue.h(w)]):null}};return n.set(C,x),x}},m=w=>{w.dialog=void 0,delete r[w.url];const _=u[w.id];_&&(_.destroy(),delete u[w.id],a(w))},p=(w=[])=>{w.forEach(_=>{m(_)})},f=async w=>{w.dialog={...w.dialog,pure:t.pure,onMinimized:()=>{m(w)},onClose:async()=>{m(w),c.value=c.value.filter(C=>C.id!==w.id)}},a(w);const _=gt({title:w.title,icon:w.icon,modal:!1,resizable:!0,draggable:!0,maximizable:!0,minimizable:!0,...w.dialog,onOpen(C){var g,x;r[w.url]=(x=(g=C.refs.panelRef)==null?void 0:g.bodyRef)==null?void 0:x.$el}},l==null?void 0:l.appContext);return await Vue.nextTick(),i(w)&&s(),u[w.id]=_,_},V=async w=>{e.value=[w.name],await Vue.nextTick(),e.value=[]},h=async w=>{e.value=w.map(_=>_.name),await Vue.nextTick(),e.value=[]},v=w=>(w.preventDefault(),!1),y=w=>{if(w.dataTransfer){const _=w.dataTransfer.getData("tab"),C=c.value.find(g=>g.id===_);if(C){const{clientX:g,clientY:x}=w;C.dialog={left:g,top:x},f(C)}}},b=w=>!!r[w];return VueUse.useEventListener(document,"dragover",v),VueUse.useEventListener(document,"drop",y),{createView:d,openDialog:f,refresh:V,exclude:e,cleanCache:h,hasDialog:b,closeDialog:m,closeDialogs:p}}const ho=Vue.defineComponent({name:"XMask",__name:"Mask",props:po,emits:["select","actionClick","actionCommand"],setup(o,{emit:t}){const n=o,e=t,r=$l(n),{collapsed:l,keyword:u,favorite:a}=Rl(),{menus:i,favorites:s,flatMenus:c,active:d,select:m,toggleFavorite:p}=Fl(n,e),{tabRef:f,showTabs:V,currentTab:h,changeTab:v,removeTab:y,tabs:b,updateTab:w,isCurrentTab:_,activeHome:C,removeAllTabs:g,removeOtherTabs:x,dropdownTabs:S,moveToShow:I,closeCurrnetTab:z}=Ol(n,e,c,d,r),{createView:N,openDialog:T,closeDialog:A,refresh:Y,exclude:M,cleanCache:k,closeDialogs:E}=jl({tabs:b,updateTab:w,isCurrentTab:_,activeHome:C},n),D=async H=>{await y(H)&&await k([H])},O=async()=>{const H=await g();H&&(E(H),await k(H))},X=async()=>{const H=await x();H&&(E(H),await k(H))},ue=H=>{e("actionClick",H)},we=(H,te)=>{e("actionCommand",H,te)},Yn=async H=>{const te=Vue.toValue(H||h);te&&(te.dialog&&A(te),await y(te,!0),await VtjUtils.delay(50))};return Vue.watch(h,H=>{H!=null&&H.dialog&&A(H)}),Vue.provide(Jt,{tabs:b,flatMenus:c,favorites:s,updateTab:w,active:d,currentTab:h,closeCurrnetTab:z,close:Yn}),(H,te)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:Vue.normalizeClass(["x-mask",{"is-disabled":n.disabled}]),fit:""},{default:Vue.withCtx(()=>[n.disabled?(Vue.openBlock(),Vue.createBlock(mo,{key:1,createView:Vue.unref(N),exclude:Vue.unref(M),pure:n.pure},{default:Vue.withCtx(()=>[H.$slots.default?Vue.renderSlot(H.$slots,"default",{key:0}):Vue.createCommentVNode("",!0)]),_:3},8,["createView","exclude","pure"])):(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,{key:0},[Vue.createVNode(bl,{collapsed:Vue.unref(l)},{brand:Vue.withCtx(()=>[Vue.createVNode(El,{logo:n.logo,title:n.title,url:Vue.unref(r).url,collapsed:Vue.unref(l)},null,8,["logo","title","url","collapsed"])]),default:Vue.withCtx(()=>[Vue.createVNode(Cl,{collasped:Vue.unref(l),"onUpdate:collasped":te[0]||(te[0]=Z=>Vue.isRef(l)?l.value=Z:null),favorite:Vue.unref(a),"onUpdate:favorite":te[1]||(te[1]=Z=>Vue.isRef(a)?a.value=Z:null),keyword:Vue.unref(u),"onUpdate:keyword":te[2]||(te[2]=Z=>Vue.isRef(u)?u.value=Z:null)},null,8,["collasped","favorite","keyword"]),Vue.createVNode(Sl,{collapse:Vue.unref(l),keyword:Vue.unref(u),favorite:Vue.unref(a),favorites:Vue.unref(s),flatMenus:Vue.unref(c),menus:Vue.unref(i),active:Vue.unref(d),onSelect:Vue.unref(m)},null,8,["collapse","keyword","favorite","favorites","flatMenus","menus","active","onSelect"])]),_:1},8,["collapsed"]),Vue.createVNode(Vue.unref(j),{class:"x-mask__main",grow:"",shrink:"",flex:"",direction:"column",overflow:"hidden"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(j),{class:"x-mask-topbar",justify:"space-between",align:"center"},{default:Vue.withCtx(()=>{var Z;return[Vue.createVNode(Dl,{ref_key:"tabRef",ref:f,favorites:Vue.unref(s),tabs:Vue.unref(V),home:Vue.unref(r),value:(Z=Vue.unref(h))==null?void 0:Z.id,onClick:Vue.unref(v),onToggleFavorite:Vue.unref(p),onRemove:D,onDialog:Vue.unref(T),onRefresh:Vue.unref(Y)},null,8,["favorites","tabs","home","value","onClick","onToggleFavorite","onDialog","onRefresh"]),Vue.createVNode(Ml,{tabs:Vue.unref(S),actions:n.actions,theme:n.theme,onCloseOtherTabs:X,onCloseAllTabs:O,onCloseTab:D,onClickTab:Vue.unref(I),onActionClick:ue,onActionCommand:we},{default:Vue.withCtx(()=>[Vue.createVNode(Al,{avatar:n.avatar,width:n.userCardWidth},Vue.createSlots({_:2},[H.$slots.user?{name:"default",fn:Vue.withCtx(()=>[Vue.renderSlot(H.$slots,"user")]),key:"0"}:void 0]),1032,["avatar","width"])]),_:3},8,["tabs","actions","theme","onClickTab"])]}),_:3}),Vue.createVNode(mo,{createView:Vue.unref(N),exclude:Vue.unref(M),pure:n.pure},{default:Vue.withCtx(()=>[H.$slots.default?Vue.renderSlot(H.$slots,"default",{key:0}):Vue.createCommentVNode("",!0)]),_:3},8,["createView","exclude","pure"])]),_:3})],64))]),_:3},8,["class"]))}});function Ul(o={}){const t=VueRouter.useRoute(),n=Vue.inject(Wt,null),e=Vue.inject(Jt,null);if(n){const l=typeof o=="function"?o:async()=>o;n[t.fullPath]=l}return{tab:Vue.computed(()=>e?e.tabs.value.find(l=>l.url===t.fullPath):null),mask:e}}const Qt=Vue.defineComponent({__name:"SelectEditor",props:{options:{default:()=>[]}},emits:["change"],setup(o,{emit:t}){const n=o,e=t,r=l=>{const u=n.options.filter(a=>Array.isArray(l)?l.includes(a.value):l===a.value);e("change",l,u)};return(l,u)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElSelect),Vue.mergeProps(l.$attrs,{onChange:r}),{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(n.options,(a,i)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElOption),Vue.mergeProps({key:`item_${i}_${a.value}`},{ref_for:!0},a),Vue.createSlots({_:2},[l.$slots.option?{name:"default",fn:Vue.withCtx(()=>[Vue.renderSlot(l.$slots,"option",{option:a})]),key:"0"}:void 0]),1040))),128))]),_:3},16))}}),Ll=Vue.defineComponent({__name:"CheckboxEditor",props:{options:{default:()=>[]},button:{type:Boolean,default:!1}},setup(o){const t=o,n=Vue.computed(()=>Vue.markRaw(t.button?ElementPlus.ElCheckboxButton:ElementPlus.ElCheckbox));return(e,r)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElCheckboxGroup),Vue.normalizeProps(Vue.guardReactiveProps(e.$attrs)),{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(t.options,(l,u)=>(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(n.value),Vue.mergeProps({key:`item_${u}_${l.value}`},{ref_for:!0},l),{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(l.label),1)]),_:2},1040))),128))]),_:1},16))}}),Hl=Vue.defineComponent({__name:"RadioEditor",props:{options:{default:()=>[]},button:{type:Boolean,default:!1}},setup(o){const t=o,n=Vue.computed(()=>Vue.markRaw(t.button?ElementPlus.ElRadioButton:ElementPlus.ElRadio));return(e,r)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElRadioGroup),Vue.normalizeProps(Vue.guardReactiveProps(e.$attrs)),{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(t.options,(l,u)=>(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(n.value),Vue.mergeProps({key:`item_${u}_${l.value}`},{ref_for:!0},l),{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(l.label),1)]),_:2},1040))),128))]),_:1},16))}}),Ie={filterResetMethod(o){const{options:t,column:n}=o;if(n.filterResetMethod){n.filterResetMethod(o);return}t.forEach(e=>{e.value=""})},filterRecoverMethod(o){const{option:t,column:n}=o;if(n.filterRecoverMethod){n.filterRecoverMethod(o);return}t.value=""},filterMethod(o){const{option:t,row:n,column:e}=o;if(e.filterMethod)return e.filterMethod(o);const{value:r}=t,l=n[e.field];return VtjUtils.isString(l)?l.indexOf(String(r))>-1:l===r}},Xl=Vue.defineComponent({name:"InputEdit",__name:"InputEdit",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,cellValue:r,onChange:l}=Fe(t.renderOpts,t.params);return(u,a)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElInput),Vue.mergeProps({size:"small",clearable:"",modelValue:Vue.unref(r),"onUpdate:modelValue":a[0]||(a[0]=i=>Vue.isRef(r)?r.value=i:null),onInput:Vue.unref(l)},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onInput"]))}}),ql={class:"x-grid__filter"},Zt=Vue.defineComponent({name:"InputFilter",__name:"InputFilter",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,state:r,load:l,onChange:u,onKeyup:a}=Mt(t.renderOpts,t.params);return l(),(i,s)=>(Vue.openBlock(),Vue.createElementBlock("div",ql,[Vue.createVNode(Vue.unref(ElementPlus.ElInput),Vue.mergeProps({size:"small",placeholder:"输入关键字回车筛选",clearable:"",modelValue:Vue.unref(r).option.value,"onUpdate:modelValue":s[0]||(s[0]=c=>Vue.unref(r).option.value=c),onInput:Vue.unref(u),onKeyup:Vue.withKeys(Vue.withModifiers(Vue.unref(a),["stop"]),["enter"])},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onInput","onKeyup"])]))}}),qe={autofocus:".el-input__inner",renderDefault(o,t){var r;const{row:n,column:e}=t;return[Vue.createTextVNode((r=n[e.field])!=null?r:"")]},renderEdit(o,t){return[Vue.h(Xl,{params:t,renderOpts:o})]},renderCell(o,t){var r;const{row:n,column:e}=t;return[Vue.createTextVNode((r=n[e.field])!=null?r:"")]}},Kl={cellClassName:"x-grid__edit",...qe,...Ie,renderFilter(o,t){return[Vue.h(Zt,{params:t,renderOpts:o})]}};/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function go(o,t){var n=Object.keys(o);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(o);t&&(e=e.filter(function(r){return Object.getOwnPropertyDescriptor(o,r).enumerable})),n.push.apply(n,e)}return n}function he(o){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?go(Object(n),!0).forEach(function(e){Yl(o,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(n)):go(Object(n)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(n,e))})}return o}function wt(o){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?wt=function(t){return typeof t}:wt=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wt(o)}function Yl(o,t,n){return t in o?Object.defineProperty(o,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):o[t]=n,o}function be(){return be=Object.assign||function(o){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(o[e]=n[e])}return o},be.apply(this,arguments)}function Gl(o,t){if(o==null)return{};var n={},e=Object.keys(o),r,l;for(l=0;l<e.length;l++)r=e[l],!(t.indexOf(r)>=0)&&(n[r]=o[r]);return n}function Wl(o,t){if(o==null)return{};var n=Gl(o,t),e,r;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(o);for(r=0;r<l.length;r++)e=l[r],!(t.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(o,e)&&(n[e]=o[e])}return n}var Jl="1.15.6";function Ce(o){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(o)}var _e=Ce(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),nt=Ce(/Edge/i),vo=Ce(/firefox/i),ot=Ce(/safari/i)&&!Ce(/chrome/i)&&!Ce(/android/i),en=Ce(/iP(ad|od|hone)/i),yo=Ce(/chrome/i)&&Ce(/android/i),wo={capture:!1,passive:!1};function L(o,t,n){o.addEventListener(t,n,!_e&&wo)}function U(o,t,n){o.removeEventListener(t,n,!_e&&wo)}function bt(o,t){if(t){if(t[0]===">"&&(t=t.substring(1)),o)try{if(o.matches)return o.matches(t);if(o.msMatchesSelector)return o.msMatchesSelector(t);if(o.webkitMatchesSelector)return o.webkitMatchesSelector(t)}catch(n){return!1}return!1}}function bo(o){return o.host&&o!==document&&o.host.nodeType?o.host:o.parentNode}function pe(o,t,n,e){if(o){n=n||document;do{if(t!=null&&(t[0]===">"?o.parentNode===n&&bt(o,t):bt(o,t))||e&&o===n)return o;if(o===n)break}while(o=bo(o))}return null}var Co=/\s+/g;function se(o,t,n){if(o&&t)if(o.classList)o.classList[n?"add":"remove"](t);else{var e=(" "+o.className+" ").replace(Co," ").replace(" "+t+" "," ");o.className=(e+(n?" "+t:"")).replace(Co," ")}}function R(o,t,n){var e=o&&o.style;if(e){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(o,""):o.currentStyle&&(n=o.currentStyle),t===void 0?n:n[t];!(t in e)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),e[t]=n+(typeof n=="string"?"":"px")}}function Ke(o,t){var n="";if(typeof o=="string")n=o;else do{var e=R(o,"transform");e&&e!=="none"&&(n=e+" "+n)}while(!t&&(o=o.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function _o(o,t,n){if(o){var e=o.getElementsByTagName(t),r=0,l=e.length;if(n)for(;r<l;r++)n(e[r],r);return e}return[]}function ge(){var o=document.scrollingElement;return o||document.documentElement}function W(o,t,n,e,r){if(!(!o.getBoundingClientRect&&o!==window)){var l,u,a,i,s,c,d;if(o!==window&&o.parentNode&&o!==ge()?(l=o.getBoundingClientRect(),u=l.top,a=l.left,i=l.bottom,s=l.right,c=l.height,d=l.width):(u=0,a=0,i=window.innerHeight,s=window.innerWidth,c=window.innerHeight,d=window.innerWidth),(t||n)&&o!==window&&(r=r||o.parentNode,!_e))do if(r&&r.getBoundingClientRect&&(R(r,"transform")!=="none"||n&&R(r,"position")!=="static")){var m=r.getBoundingClientRect();u-=m.top+parseInt(R(r,"border-top-width")),a-=m.left+parseInt(R(r,"border-left-width")),i=u+l.height,s=a+l.width;break}while(r=r.parentNode);if(e&&o!==window){var p=Ke(r||o),f=p&&p.a,V=p&&p.d;p&&(u/=V,a/=f,d/=f,c/=V,i=u+c,s=a+d)}return{top:u,left:a,bottom:i,right:s,width:d,height:c}}}function ko(o,t,n){for(var e=xe(o,!0),r=W(o)[t];e;){var l=W(e)[n],u=void 0;if(u=r>=l,!u)return e;if(e===ge())break;e=xe(e,!1)}return!1}function Ye(o,t,n,e){for(var r=0,l=0,u=o.children;l<u.length;){if(u[l].style.display!=="none"&&u[l]!==$.ghost&&(e||u[l]!==$.dragged)&&pe(u[l],n.draggable,o,!1)){if(r===t)return u[l];r++}l++}return null}function tn(o,t){for(var n=o.lastElementChild;n&&(n===$.ghost||R(n,"display")==="none"||t&&!bt(n,t));)n=n.previousElementSibling;return n||null}function fe(o,t){var n=0;if(!o||!o.parentNode)return-1;for(;o=o.previousElementSibling;)o.nodeName.toUpperCase()!=="TEMPLATE"&&o!==$.clone&&(!t||bt(o,t))&&n++;return n}function Eo(o){var t=0,n=0,e=ge();if(o)do{var r=Ke(o),l=r.a,u=r.d;t+=o.scrollLeft*l,n+=o.scrollTop*u}while(o!==e&&(o=o.parentNode));return[t,n]}function Ql(o,t){for(var n in o)if(o.hasOwnProperty(n)){for(var e in t)if(t.hasOwnProperty(e)&&t[e]===o[n][e])return Number(n)}return-1}function xe(o,t){if(!o||!o.getBoundingClientRect)return ge();var n=o,e=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=R(n);if(n.clientWidth<n.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return ge();if(e||t)return n;e=!0}}while(n=n.parentNode);return ge()}function Zl(o,t){if(o&&t)for(var n in t)t.hasOwnProperty(n)&&(o[n]=t[n]);return o}function nn(o,t){return Math.round(o.top)===Math.round(t.top)&&Math.round(o.left)===Math.round(t.left)&&Math.round(o.height)===Math.round(t.height)&&Math.round(o.width)===Math.round(t.width)}var rt;function So(o,t){return function(){if(!rt){var n=arguments,e=this;n.length===1?o.call(e,n[0]):o.apply(e,n),rt=setTimeout(function(){rt=void 0},t)}}}function ea(){clearTimeout(rt),rt=void 0}function xo(o,t,n){o.scrollLeft+=t,o.scrollTop+=n}function Bo(o){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(o).cloneNode(!0):n?n(o).clone(!0)[0]:o.cloneNode(!0)}function Po(o,t,n){var e={};return Array.from(o.children).forEach(function(r){var l,u,a,i;if(!(!pe(r,t.draggable,o,!1)||r.animated||r===n)){var s=W(r);e.left=Math.min((l=e.left)!==null&&l!==void 0?l:1/0,s.left),e.top=Math.min((u=e.top)!==null&&u!==void 0?u:1/0,s.top),e.right=Math.max((a=e.right)!==null&&a!==void 0?a:-1/0,s.right),e.bottom=Math.max((i=e.bottom)!==null&&i!==void 0?i:-1/0,s.bottom)}}),e.width=e.right-e.left,e.height=e.bottom-e.top,e.x=e.left,e.y=e.top,e}var le="Sortable"+new Date().getTime();function ta(){var o=[],t;return{captureAnimationState:function(){if(o=[],!!this.options.animation){var e=[].slice.call(this.el.children);e.forEach(function(r){if(!(R(r,"display")==="none"||r===$.ghost)){o.push({target:r,rect:W(r)});var l=he({},o[o.length-1].rect);if(r.thisAnimationDuration){var u=Ke(r,!0);u&&(l.top-=u.f,l.left-=u.e)}r.fromRect=l}})}},addAnimationState:function(e){o.push(e)},removeAnimationState:function(e){o.splice(Ql(o,{target:e}),1)},animateAll:function(e){var r=this;if(!this.options.animation){clearTimeout(t),typeof e=="function"&&e();return}var l=!1,u=0;o.forEach(function(a){var i=0,s=a.target,c=s.fromRect,d=W(s),m=s.prevFromRect,p=s.prevToRect,f=a.rect,V=Ke(s,!0);V&&(d.top-=V.f,d.left-=V.e),s.toRect=d,s.thisAnimationDuration&&nn(m,d)&&!nn(c,d)&&(f.top-d.top)/(f.left-d.left)===(c.top-d.top)/(c.left-d.left)&&(i=oa(f,m,p,r.options)),nn(d,c)||(s.prevFromRect=c,s.prevToRect=d,i||(i=r.options.animation),r.animate(s,f,d,i)),i&&(l=!0,u=Math.max(u,i),clearTimeout(s.animationResetTimer),s.animationResetTimer=setTimeout(function(){s.animationTime=0,s.prevFromRect=null,s.fromRect=null,s.prevToRect=null,s.thisAnimationDuration=null},i),s.thisAnimationDuration=i)}),clearTimeout(t),l?t=setTimeout(function(){typeof e=="function"&&e()},u):typeof e=="function"&&e(),o=[]},animate:function(e,r,l,u){if(u){R(e,"transition",""),R(e,"transform","");var a=Ke(this.el),i=a&&a.a,s=a&&a.d,c=(r.left-l.left)/(i||1),d=(r.top-l.top)/(s||1);e.animatingX=!!c,e.animatingY=!!d,R(e,"transform","translate3d("+c+"px,"+d+"px,0)"),this.forRepaintDummy=na(e),R(e,"transition","transform "+u+"ms"+(this.options.easing?" "+this.options.easing:"")),R(e,"transform","translate3d(0,0,0)"),typeof e.animated=="number"&&clearTimeout(e.animated),e.animated=setTimeout(function(){R(e,"transition",""),R(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1},u)}}}}function na(o){return o.offsetWidth}function oa(o,t,n,e){return Math.sqrt(Math.pow(t.top-o.top,2)+Math.pow(t.left-o.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*e.animation}var Ge=[],on={initializeByDefault:!0},lt={mount:function(t){for(var n in on)on.hasOwnProperty(n)&&!(n in t)&&(t[n]=on[n]);Ge.forEach(function(e){if(e.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),Ge.push(t)},pluginEvent:function(t,n,e){var r=this;this.eventCanceled=!1,e.cancel=function(){r.eventCanceled=!0};var l=t+"Global";Ge.forEach(function(u){n[u.pluginName]&&(n[u.pluginName][l]&&n[u.pluginName][l](he({sortable:n},e)),n.options[u.pluginName]&&n[u.pluginName][t]&&n[u.pluginName][t](he({sortable:n},e)))})},initializePlugins:function(t,n,e,r){Ge.forEach(function(a){var i=a.pluginName;if(!(!t.options[i]&&!a.initializeByDefault)){var s=new a(t,n,t.options);s.sortable=t,s.options=t.options,t[i]=s,be(e,s.defaults)}});for(var l in t.options)if(t.options.hasOwnProperty(l)){var u=this.modifyOption(t,l,t.options[l]);typeof u<"u"&&(t.options[l]=u)}},getEventProperties:function(t,n){var e={};return Ge.forEach(function(r){typeof r.eventProperties=="function"&&be(e,r.eventProperties.call(n[r.pluginName],t))}),e},modifyOption:function(t,n,e){var r;return Ge.forEach(function(l){t[l.pluginName]&&l.optionListeners&&typeof l.optionListeners[n]=="function"&&(r=l.optionListeners[n].call(t[l.pluginName],e))}),r}};function ra(o){var t=o.sortable,n=o.rootEl,e=o.name,r=o.targetEl,l=o.cloneEl,u=o.toEl,a=o.fromEl,i=o.oldIndex,s=o.newIndex,c=o.oldDraggableIndex,d=o.newDraggableIndex,m=o.originalEvent,p=o.putSortable,f=o.extraEventProperties;if(t=t||n&&n[le],!!t){var V,h=t.options,v="on"+e.charAt(0).toUpperCase()+e.substr(1);window.CustomEvent&&!_e&&!nt?V=new CustomEvent(e,{bubbles:!0,cancelable:!0}):(V=document.createEvent("Event"),V.initEvent(e,!0,!0)),V.to=u||n,V.from=a||n,V.item=r||n,V.clone=l,V.oldIndex=i,V.newIndex=s,V.oldDraggableIndex=c,V.newDraggableIndex=d,V.originalEvent=m,V.pullMode=p?p.lastPutMode:void 0;var y=he(he({},f),lt.getEventProperties(e,t));for(var b in y)V[b]=y[b];n&&n.dispatchEvent(V),h[v]&&h[v].call(t,V)}}var la=["evt"],ae=function(t,n){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=e.evt,l=Wl(e,la);lt.pluginEvent.bind($)(t,n,he({dragEl:P,parentEl:G,ghostEl:F,rootEl:q,nextEl:Me,lastDownEl:Ct,cloneEl:K,cloneHidden:Be,dragStarted:ut,putSortable:ee,activeSortable:$.active,originalEvent:r,oldIndex:We,oldDraggableIndex:at,newIndex:ce,newDraggableIndex:Pe,hideGhostForTarget:Ao,unhideGhostForTarget:Ro,cloneNowHidden:function(){Be=!0},cloneNowShown:function(){Be=!1},dispatchSortableEvent:function(a){re({sortable:n,name:a,originalEvent:r})}},l))};function re(o){ra(he({putSortable:ee,cloneEl:K,targetEl:P,rootEl:q,oldIndex:We,oldDraggableIndex:at,newIndex:ce,newDraggableIndex:Pe},o))}var P,G,F,q,Me,Ct,K,Be,We,ce,at,Pe,_t,ee,Je=!1,kt=!1,Et=[],Ae,me,rn,ln,No,To,ut,Qe,it,st=!1,St=!1,xt,ne,an=[],un=!1,Bt=[],Pt=typeof document<"u",Nt=en,Do=nt||_e?"cssFloat":"float",aa=Pt&&!yo&&!en&&"draggable"in document.createElement("div"),zo=function(){if(Pt){if(_e)return!1;var o=document.createElement("x");return o.style.cssText="pointer-events:auto",o.style.pointerEvents==="auto"}}(),Io=function(t,n){var e=R(t),r=parseInt(e.width)-parseInt(e.paddingLeft)-parseInt(e.paddingRight)-parseInt(e.borderLeftWidth)-parseInt(e.borderRightWidth),l=Ye(t,0,n),u=Ye(t,1,n),a=l&&R(l),i=u&&R(u),s=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+W(l).width,c=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+W(u).width;if(e.display==="flex")return e.flexDirection==="column"||e.flexDirection==="column-reverse"?"vertical":"horizontal";if(e.display==="grid")return e.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(l&&a.float&&a.float!=="none"){var d=a.float==="left"?"left":"right";return u&&(i.clear==="both"||i.clear===d)?"vertical":"horizontal"}return l&&(a.display==="block"||a.display==="flex"||a.display==="table"||a.display==="grid"||s>=r&&e[Do]==="none"||u&&e[Do]==="none"&&s+c>r)?"vertical":"horizontal"},ua=function(t,n,e){var r=e?t.left:t.top,l=e?t.right:t.bottom,u=e?t.width:t.height,a=e?n.left:n.top,i=e?n.right:n.bottom,s=e?n.width:n.height;return r===a||l===i||r+u/2===a+s/2},ia=function(t,n){var e;return Et.some(function(r){var l=r[le].options.emptyInsertThreshold;if(!(!l||tn(r))){var u=W(r),a=t>=u.left-l&&t<=u.right+l,i=n>=u.top-l&&n<=u.bottom+l;if(a&&i)return e=r}}),e},Mo=function(t){function n(l,u){return function(a,i,s,c){var d=a.options.group.name&&i.options.group.name&&a.options.group.name===i.options.group.name;if(l==null&&(u||d))return!0;if(l==null||l===!1)return!1;if(u&&l==="clone")return l;if(typeof l=="function")return n(l(a,i,s,c),u)(a,i,s,c);var m=(u?a:i).options.group.name;return l===!0||typeof l=="string"&&l===m||l.join&&l.indexOf(m)>-1}}var e={},r=t.group;(!r||wt(r)!="object")&&(r={name:r}),e.name=r.name,e.checkPull=n(r.pull,!0),e.checkPut=n(r.put),e.revertClone=r.revertClone,t.group=e},Ao=function(){!zo&&F&&R(F,"display","none")},Ro=function(){!zo&&F&&R(F,"display","")};Pt&&!yo&&document.addEventListener("click",function(o){if(kt)return o.preventDefault(),o.stopPropagation&&o.stopPropagation(),o.stopImmediatePropagation&&o.stopImmediatePropagation(),kt=!1,!1},!0);var Re=function(t){if(P){t=t.touches?t.touches[0]:t;var n=ia(t.clientX,t.clientY);if(n){var e={};for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);e.target=e.rootEl=n,e.preventDefault=void 0,e.stopPropagation=void 0,n[le]._onDragOver(e)}}},sa=function(t){P&&P.parentNode[le]._isOutsideThisEl(t.target)};function $(o,t){if(!(o&&o.nodeType&&o.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(o));this.el=o,this.options=t=be({},t),o[le]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(o.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Io(o,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(u,a){u.setData("Text",a.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:$.supportPointer!==!1&&"PointerEvent"in window&&(!ot||en),emptyInsertThreshold:5};lt.initializePlugins(this,o,n);for(var e in n)!(e in t)&&(t[e]=n[e]);Mo(t);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=t.forceFallback?!1:aa,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?L(o,"pointerdown",this._onTapStart):(L(o,"mousedown",this._onTapStart),L(o,"touchstart",this._onTapStart)),this.nativeDraggable&&(L(o,"dragover",this),L(o,"dragenter",this)),Et.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),be(this,ta())}$.prototype={constructor:$,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(Qe=null)},_getDirection:function(t,n){return typeof this.options.direction=="function"?this.options.direction.call(this,t,n,P):this.options.direction},_onTapStart:function(t){if(t.cancelable){var n=this,e=this.el,r=this.options,l=r.preventOnFilter,u=t.type,a=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,i=(a||t).target,s=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||i,c=r.filter;if(ga(e),!P&&!(/mousedown|pointerdown/.test(u)&&t.button!==0||r.disabled)&&!s.isContentEditable&&!(!this.nativeDraggable&&ot&&i&&i.tagName.toUpperCase()==="SELECT")&&(i=pe(i,r.draggable,e,!1),!(i&&i.animated)&&Ct!==i)){if(We=fe(i),at=fe(i,r.draggable),typeof c=="function"){if(c.call(this,t,i,this)){re({sortable:n,rootEl:s,name:"filter",targetEl:i,toEl:e,fromEl:e}),ae("filter",n,{evt:t}),l&&t.preventDefault();return}}else if(c&&(c=c.split(",").some(function(d){if(d=pe(s,d.trim(),e,!1),d)return re({sortable:n,rootEl:d,name:"filter",targetEl:i,fromEl:e,toEl:e}),ae("filter",n,{evt:t}),!0}),c)){l&&t.preventDefault();return}r.handle&&!pe(s,r.handle,e,!1)||this._prepareDragStart(t,a,i)}}},_prepareDragStart:function(t,n,e){var r=this,l=r.el,u=r.options,a=l.ownerDocument,i;if(e&&!P&&e.parentNode===l){var s=W(e);if(q=l,P=e,G=P.parentNode,Me=P.nextSibling,Ct=e,_t=u.group,$.dragged=P,Ae={target:P,clientX:(n||t).clientX,clientY:(n||t).clientY},No=Ae.clientX-s.left,To=Ae.clientY-s.top,this._lastX=(n||t).clientX,this._lastY=(n||t).clientY,P.style["will-change"]="all",i=function(){if(ae("delayEnded",r,{evt:t}),$.eventCanceled){r._onDrop();return}r._disableDelayedDragEvents(),!vo&&r.nativeDraggable&&(P.draggable=!0),r._triggerDragStart(t,n),re({sortable:r,name:"choose",originalEvent:t}),se(P,u.chosenClass,!0)},u.ignore.split(",").forEach(function(c){_o(P,c.trim(),sn)}),L(a,"dragover",Re),L(a,"mousemove",Re),L(a,"touchmove",Re),u.supportPointer?(L(a,"pointerup",r._onDrop),!this.nativeDraggable&&L(a,"pointercancel",r._onDrop)):(L(a,"mouseup",r._onDrop),L(a,"touchend",r._onDrop),L(a,"touchcancel",r._onDrop)),vo&&this.nativeDraggable&&(this.options.touchStartThreshold=4,P.draggable=!0),ae("delayStart",this,{evt:t}),u.delay&&(!u.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(nt||_e))){if($.eventCanceled){this._onDrop();return}u.supportPointer?(L(a,"pointerup",r._disableDelayedDrag),L(a,"pointercancel",r._disableDelayedDrag)):(L(a,"mouseup",r._disableDelayedDrag),L(a,"touchend",r._disableDelayedDrag),L(a,"touchcancel",r._disableDelayedDrag)),L(a,"mousemove",r._delayedDragTouchMoveHandler),L(a,"touchmove",r._delayedDragTouchMoveHandler),u.supportPointer&&L(a,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(i,u.delay)}else i()}},_delayedDragTouchMoveHandler:function(t){var n=t.touches?t.touches[0]:t;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){P&&sn(P),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;U(t,"mouseup",this._disableDelayedDrag),U(t,"touchend",this._disableDelayedDrag),U(t,"touchcancel",this._disableDelayedDrag),U(t,"pointerup",this._disableDelayedDrag),U(t,"pointercancel",this._disableDelayedDrag),U(t,"mousemove",this._delayedDragTouchMoveHandler),U(t,"touchmove",this._delayedDragTouchMoveHandler),U(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,n){n=n||t.pointerType=="touch"&&t,!this.nativeDraggable||n?this.options.supportPointer?L(document,"pointermove",this._onTouchMove):n?L(document,"touchmove",this._onTouchMove):L(document,"mousemove",this._onTouchMove):(L(P,"dragend",this),L(q,"dragstart",this._onDragStart));try{document.selection?Dt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(e){}},_dragStarted:function(t,n){if(Je=!1,q&&P){ae("dragStarted",this,{evt:n}),this.nativeDraggable&&L(document,"dragover",sa);var e=this.options;!t&&se(P,e.dragClass,!1),se(P,e.ghostClass,!0),$.active=this,t&&this._appendGhost(),re({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(me){this._lastX=me.clientX,this._lastY=me.clientY,Ao();for(var t=document.elementFromPoint(me.clientX,me.clientY),n=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(me.clientX,me.clientY),t!==n);)n=t;if(P.parentNode[le]._isOutsideThisEl(t),n)do{if(n[le]){var e=void 0;if(e=n[le]._onDragOver({clientX:me.clientX,clientY:me.clientY,target:t,rootEl:n}),e&&!this.options.dragoverBubble)break}t=n}while(n=bo(n));Ro()}},_onTouchMove:function(t){if(Ae){var n=this.options,e=n.fallbackTolerance,r=n.fallbackOffset,l=t.touches?t.touches[0]:t,u=F&&Ke(F,!0),a=F&&u&&u.a,i=F&&u&&u.d,s=Nt&&ne&&Eo(ne),c=(l.clientX-Ae.clientX+r.x)/(a||1)+(s?s[0]-an[0]:0)/(a||1),d=(l.clientY-Ae.clientY+r.y)/(i||1)+(s?s[1]-an[1]:0)/(i||1);if(!$.active&&!Je){if(e&&Math.max(Math.abs(l.clientX-this._lastX),Math.abs(l.clientY-this._lastY))<e)return;this._onDragStart(t,!0)}if(F){u?(u.e+=c-(rn||0),u.f+=d-(ln||0)):u={a:1,b:0,c:0,d:1,e:c,f:d};var m="matrix(".concat(u.a,",").concat(u.b,",").concat(u.c,",").concat(u.d,",").concat(u.e,",").concat(u.f,")");R(F,"webkitTransform",m),R(F,"mozTransform",m),R(F,"msTransform",m),R(F,"transform",m),rn=c,ln=d,me=l}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!F){var t=this.options.fallbackOnBody?document.body:q,n=W(P,!0,Nt,!0,t),e=this.options;if(Nt){for(ne=t;R(ne,"position")==="static"&&R(ne,"transform")==="none"&&ne!==document;)ne=ne.parentNode;ne!==document.body&&ne!==document.documentElement?(ne===document&&(ne=ge()),n.top+=ne.scrollTop,n.left+=ne.scrollLeft):ne=ge(),an=Eo(ne)}F=P.cloneNode(!0),se(F,e.ghostClass,!1),se(F,e.fallbackClass,!0),se(F,e.dragClass,!0),R(F,"transition",""),R(F,"transform",""),R(F,"box-sizing","border-box"),R(F,"margin",0),R(F,"top",n.top),R(F,"left",n.left),R(F,"width",n.width),R(F,"height",n.height),R(F,"opacity","0.8"),R(F,"position",Nt?"absolute":"fixed"),R(F,"zIndex","100000"),R(F,"pointerEvents","none"),$.ghost=F,t.appendChild(F),R(F,"transform-origin",No/parseInt(F.style.width)*100+"% "+To/parseInt(F.style.height)*100+"%")}},_onDragStart:function(t,n){var e=this,r=t.dataTransfer,l=e.options;if(ae("dragStart",this,{evt:t}),$.eventCanceled){this._onDrop();return}ae("setupClone",this),$.eventCanceled||(K=Bo(P),K.removeAttribute("id"),K.draggable=!1,K.style["will-change"]="",this._hideClone(),se(K,this.options.chosenClass,!1),$.clone=K),e.cloneId=Dt(function(){ae("clone",e),!$.eventCanceled&&(e.options.removeCloneOnHide||q.insertBefore(K,P),e._hideClone(),re({sortable:e,name:"clone"}))}),!n&&se(P,l.dragClass,!0),n?(kt=!0,e._loopId=setInterval(e._emulateDragOver,50)):(U(document,"mouseup",e._onDrop),U(document,"touchend",e._onDrop),U(document,"touchcancel",e._onDrop),r&&(r.effectAllowed="move",l.setData&&l.setData.call(e,r,P)),L(document,"drop",e),R(P,"transform","translateZ(0)")),Je=!0,e._dragStartId=Dt(e._dragStarted.bind(e,n,t)),L(document,"selectstart",e),ut=!0,window.getSelection().removeAllRanges(),ot&&R(document.body,"user-select","none")},_onDragOver:function(t){var n=this.el,e=t.target,r,l,u,a=this.options,i=a.group,s=$.active,c=_t===i,d=a.sort,m=ee||s,p,f=this,V=!1;if(un)return;function h(E,D){ae(E,f,he({evt:t,isOwner:c,axis:p?"vertical":"horizontal",revert:u,dragRect:r,targetRect:l,canSort:d,fromSortable:m,target:e,completed:y,onMove:function(X,ue){return Tt(q,n,P,r,X,W(X),t,ue)},changed:b},D))}function v(){h("dragOverAnimationCapture"),f.captureAnimationState(),f!==m&&m.captureAnimationState()}function y(E){return h("dragOverCompleted",{insertion:E}),E&&(c?s._hideClone():s._showClone(f),f!==m&&(se(P,ee?ee.options.ghostClass:s.options.ghostClass,!1),se(P,a.ghostClass,!0)),ee!==f&&f!==$.active?ee=f:f===$.active&&ee&&(ee=null),m===f&&(f._ignoreWhileAnimating=e),f.animateAll(function(){h("dragOverAnimationComplete"),f._ignoreWhileAnimating=null}),f!==m&&(m.animateAll(),m._ignoreWhileAnimating=null)),(e===P&&!P.animated||e===n&&!e.animated)&&(Qe=null),!a.dragoverBubble&&!t.rootEl&&e!==document&&(P.parentNode[le]._isOutsideThisEl(t.target),!E&&Re(t)),!a.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),V=!0}function b(){ce=fe(P),Pe=fe(P,a.draggable),re({sortable:f,name:"change",toEl:n,newIndex:ce,newDraggableIndex:Pe,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),e=pe(e,a.draggable,n,!0),h("dragOver"),$.eventCanceled)return V;if(P.contains(t.target)||e.animated&&e.animatingX&&e.animatingY||f._ignoreWhileAnimating===e)return y(!1);if(kt=!1,s&&!a.disabled&&(c?d||(u=G!==q):ee===this||(this.lastPutMode=_t.checkPull(this,s,P,t))&&i.checkPut(this,s,P,t))){if(p=this._getDirection(t,e)==="vertical",r=W(P),h("dragOverValid"),$.eventCanceled)return V;if(u)return G=q,v(),this._hideClone(),h("revert"),$.eventCanceled||(Me?q.insertBefore(P,Me):q.appendChild(P)),y(!0);var w=tn(n,a.draggable);if(!w||pa(t,p,this)&&!w.animated){if(w===P)return y(!1);if(w&&n===t.target&&(e=w),e&&(l=W(e)),Tt(q,n,P,r,e,l,t,!!e)!==!1)return v(),w&&w.nextSibling?n.insertBefore(P,w.nextSibling):n.appendChild(P),G=n,b(),y(!0)}else if(w&&fa(t,p,this)){var _=Ye(n,0,a,!0);if(_===P)return y(!1);if(e=_,l=W(e),Tt(q,n,P,r,e,l,t,!1)!==!1)return v(),n.insertBefore(P,_),G=n,b(),y(!0)}else if(e.parentNode===n){l=W(e);var C=0,g,x=P.parentNode!==n,S=!ua(P.animated&&P.toRect||r,e.animated&&e.toRect||l,p),I=p?"top":"left",z=ko(e,"top","top")||ko(P,"top","top"),N=z?z.scrollTop:void 0;Qe!==e&&(g=l[I],st=!1,St=!S&&a.invertSwap||x),C=ma(t,e,l,p,S?1:a.swapThreshold,a.invertedSwapThreshold==null?a.swapThreshold:a.invertedSwapThreshold,St,Qe===e);var T;if(C!==0){var A=fe(P);do A-=C,T=G.children[A];while(T&&(R(T,"display")==="none"||T===F))}if(C===0||T===e)return y(!1);Qe=e,it=C;var Y=e.nextElementSibling,M=!1;M=C===1;var k=Tt(q,n,P,r,e,l,t,M);if(k!==!1)return(k===1||k===-1)&&(M=k===1),un=!0,setTimeout(da,30),v(),M&&!Y?n.appendChild(P):e.parentNode.insertBefore(P,M?Y:e),z&&xo(z,0,N-z.scrollTop),G=P.parentNode,g!==void 0&&!St&&(xt=Math.abs(g-W(e)[I])),b(),y(!0)}if(n.contains(P))return y(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){U(document,"mousemove",this._onTouchMove),U(document,"touchmove",this._onTouchMove),U(document,"pointermove",this._onTouchMove),U(document,"dragover",Re),U(document,"mousemove",Re),U(document,"touchmove",Re)},_offUpEvents:function(){var t=this.el.ownerDocument;U(t,"mouseup",this._onDrop),U(t,"touchend",this._onDrop),U(t,"pointerup",this._onDrop),U(t,"pointercancel",this._onDrop),U(t,"touchcancel",this._onDrop),U(document,"selectstart",this)},_onDrop:function(t){var n=this.el,e=this.options;if(ce=fe(P),Pe=fe(P,e.draggable),ae("drop",this,{evt:t}),G=P&&P.parentNode,ce=fe(P),Pe=fe(P,e.draggable),$.eventCanceled){this._nulling();return}Je=!1,St=!1,st=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),cn(this.cloneId),cn(this._dragStartId),this.nativeDraggable&&(U(document,"drop",this),U(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),ot&&R(document.body,"user-select",""),R(P,"transform",""),t&&(ut&&(t.cancelable&&t.preventDefault(),!e.dropBubble&&t.stopPropagation()),F&&F.parentNode&&F.parentNode.removeChild(F),(q===G||ee&&ee.lastPutMode!=="clone")&&K&&K.parentNode&&K.parentNode.removeChild(K),P&&(this.nativeDraggable&&U(P,"dragend",this),sn(P),P.style["will-change"]="",ut&&!Je&&se(P,ee?ee.options.ghostClass:this.options.ghostClass,!1),se(P,this.options.chosenClass,!1),re({sortable:this,name:"unchoose",toEl:G,newIndex:null,newDraggableIndex:null,originalEvent:t}),q!==G?(ce>=0&&(re({rootEl:G,name:"add",toEl:G,fromEl:q,originalEvent:t}),re({sortable:this,name:"remove",toEl:G,originalEvent:t}),re({rootEl:G,name:"sort",toEl:G,fromEl:q,originalEvent:t}),re({sortable:this,name:"sort",toEl:G,originalEvent:t})),ee&&ee.save()):ce!==We&&ce>=0&&(re({sortable:this,name:"update",toEl:G,originalEvent:t}),re({sortable:this,name:"sort",toEl:G,originalEvent:t})),$.active&&((ce==null||ce===-1)&&(ce=We,Pe=at),re({sortable:this,name:"end",toEl:G,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){ae("nulling",this),q=P=G=F=Me=K=Ct=Be=Ae=me=ut=ce=Pe=We=at=Qe=it=ee=_t=$.dragged=$.ghost=$.clone=$.active=null,Bt.forEach(function(t){t.checked=!0}),Bt.length=rn=ln=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":P&&(this._onDragOver(t),ca(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],n,e=this.el.children,r=0,l=e.length,u=this.options;r<l;r++)n=e[r],pe(n,u.draggable,this.el,!1)&&t.push(n.getAttribute(u.dataIdAttr)||ha(n));return t},sort:function(t,n){var e={},r=this.el;this.toArray().forEach(function(l,u){var a=r.children[u];pe(a,this.options.draggable,r,!1)&&(e[l]=a)},this),n&&this.captureAnimationState(),t.forEach(function(l){e[l]&&(r.removeChild(e[l]),r.appendChild(e[l]))}),n&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,n){return pe(t,n||this.options.draggable,this.el,!1)},option:function(t,n){var e=this.options;if(n===void 0)return e[t];var r=lt.modifyOption(this,t,n);typeof r<"u"?e[t]=r:e[t]=n,t==="group"&&Mo(e)},destroy:function(){ae("destroy",this);var t=this.el;t[le]=null,U(t,"mousedown",this._onTapStart),U(t,"touchstart",this._onTapStart),U(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(U(t,"dragover",this),U(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Et.splice(Et.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!Be){if(ae("hideClone",this),$.eventCanceled)return;R(K,"display","none"),this.options.removeCloneOnHide&&K.parentNode&&K.parentNode.removeChild(K),Be=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(Be){if(ae("showClone",this),$.eventCanceled)return;P.parentNode==q&&!this.options.group.revertClone?q.insertBefore(K,P):Me?q.insertBefore(K,Me):q.appendChild(K),this.options.group.revertClone&&this.animate(P,K),R(K,"display",""),Be=!1}}};function ca(o){o.dataTransfer&&(o.dataTransfer.dropEffect="move"),o.cancelable&&o.preventDefault()}function Tt(o,t,n,e,r,l,u,a){var i,s=o[le],c=s.options.onMove,d;return window.CustomEvent&&!_e&&!nt?i=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(i=document.createEvent("Event"),i.initEvent("move",!0,!0)),i.to=t,i.from=o,i.dragged=n,i.draggedRect=e,i.related=r||t,i.relatedRect=l||W(t),i.willInsertAfter=a,i.originalEvent=u,o.dispatchEvent(i),c&&(d=c.call(s,i,u)),d}function sn(o){o.draggable=!1}function da(){un=!1}function fa(o,t,n){var e=W(Ye(n.el,0,n.options,!0)),r=Po(n.el,n.options,F),l=10;return t?o.clientX<r.left-l||o.clientY<e.top&&o.clientX<e.right:o.clientY<r.top-l||o.clientY<e.bottom&&o.clientX<e.left}function pa(o,t,n){var e=W(tn(n.el,n.options.draggable)),r=Po(n.el,n.options,F),l=10;return t?o.clientX>r.right+l||o.clientY>e.bottom&&o.clientX>e.left:o.clientY>r.bottom+l||o.clientX>e.right&&o.clientY>e.top}function ma(o,t,n,e,r,l,u,a){var i=e?o.clientY:o.clientX,s=e?n.height:n.width,c=e?n.top:n.left,d=e?n.bottom:n.right,m=!1;if(!u){if(a&&xt<s*r){if(!st&&(it===1?i>c+s*l/2:i<d-s*l/2)&&(st=!0),st)m=!0;else if(it===1?i<c+xt:i>d-xt)return-it}else if(i>c+s*(1-r)/2&&i<d-s*(1-r)/2)return Va(t)}return m=m||u,m&&(i<c+s*l/2||i>d-s*l/2)?i>c+s/2?1:-1:0}function Va(o){return fe(P)<fe(o)?1:-1}function ha(o){for(var t=o.tagName+o.className+o.src+o.href+o.textContent,n=t.length,e=0;n--;)e+=t.charCodeAt(n);return e.toString(36)}function ga(o){Bt.length=0;for(var t=o.getElementsByTagName("input"),n=t.length;n--;){var e=t[n];e.checked&&Bt.push(e)}}function Dt(o){return setTimeout(o,0)}function cn(o){return clearTimeout(o)}Pt&&L(document,"touchmove",function(o){($.active||Je)&&o.cancelable&&o.preventDefault()}),$.utils={on:L,off:U,css:R,find:_o,is:function(t,n){return!!pe(t,n,t,!1)},extend:Zl,throttle:So,closest:pe,toggleClass:se,clone:Bo,index:fe,nextTick:Dt,cancelNextTick:cn,detectDirection:Io,getChild:Ye,expando:le},$.get=function(o){return o[le]},$.mount=function(){for(var o=arguments.length,t=new Array(o),n=0;n<o;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach(function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&($.utils=he(he({},$.utils),e.utils)),lt.mount(e)})},$.create=function(o,t){return new $(o,t)},$.version=Jl;var J=[],ct,dn,fn=!1,pn,mn,zt,dt;function va(){function o(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return o.prototype={dragStarted:function(n){var e=n.originalEvent;this.sortable.nativeDraggable?L(document,"dragover",this._handleAutoScroll):this.options.supportPointer?L(document,"pointermove",this._handleFallbackAutoScroll):e.touches?L(document,"touchmove",this._handleFallbackAutoScroll):L(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var e=n.originalEvent;!this.options.dragOverBubble&&!e.rootEl&&this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?U(document,"dragover",this._handleAutoScroll):(U(document,"pointermove",this._handleFallbackAutoScroll),U(document,"touchmove",this._handleFallbackAutoScroll),U(document,"mousemove",this._handleFallbackAutoScroll)),$o(),It(),ea()},nulling:function(){zt=dn=ct=fn=dt=pn=mn=null,J.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,e){var r=this,l=(n.touches?n.touches[0]:n).clientX,u=(n.touches?n.touches[0]:n).clientY,a=document.elementFromPoint(l,u);if(zt=n,e||this.options.forceAutoScrollFallback||nt||_e||ot){Vn(n,this.options,a,e);var i=xe(a,!0);fn&&(!dt||l!==pn||u!==mn)&&(dt&&$o(),dt=setInterval(function(){var s=xe(document.elementFromPoint(l,u),!0);s!==i&&(i=s,It()),Vn(n,r.options,s,e)},10),pn=l,mn=u)}else{if(!this.options.bubbleScroll||xe(a,!0)===ge()){It();return}Vn(n,this.options,xe(a,!1),!1)}}},be(o,{pluginName:"scroll",initializeByDefault:!0})}function It(){J.forEach(function(o){clearInterval(o.pid)}),J=[]}function $o(){clearInterval(dt)}var Vn=So(function(o,t,n,e){if(t.scroll){var r=(o.touches?o.touches[0]:o).clientX,l=(o.touches?o.touches[0]:o).clientY,u=t.scrollSensitivity,a=t.scrollSpeed,i=ge(),s=!1,c;dn!==n&&(dn=n,It(),ct=t.scroll,c=t.scrollFn,ct===!0&&(ct=xe(n,!0)));var d=0,m=ct;do{var p=m,f=W(p),V=f.top,h=f.bottom,v=f.left,y=f.right,b=f.width,w=f.height,_=void 0,C=void 0,g=p.scrollWidth,x=p.scrollHeight,S=R(p),I=p.scrollLeft,z=p.scrollTop;p===i?(_=b<g&&(S.overflowX==="auto"||S.overflowX==="scroll"||S.overflowX==="visible"),C=w<x&&(S.overflowY==="auto"||S.overflowY==="scroll"||S.overflowY==="visible")):(_=b<g&&(S.overflowX==="auto"||S.overflowX==="scroll"),C=w<x&&(S.overflowY==="auto"||S.overflowY==="scroll"));var N=_&&(Math.abs(y-r)<=u&&I+b<g)-(Math.abs(v-r)<=u&&!!I),T=C&&(Math.abs(h-l)<=u&&z+w<x)-(Math.abs(V-l)<=u&&!!z);if(!J[d])for(var A=0;A<=d;A++)J[A]||(J[A]={});(J[d].vx!=N||J[d].vy!=T||J[d].el!==p)&&(J[d].el=p,J[d].vx=N,J[d].vy=T,clearInterval(J[d].pid),(N!=0||T!=0)&&(s=!0,J[d].pid=setInterval(function(){e&&this.layer===0&&$.active._onTouchMove(zt);var Y=J[this.layer].vy?J[this.layer].vy*a:0,M=J[this.layer].vx?J[this.layer].vx*a:0;typeof c=="function"&&c.call($.dragged.parentNode[le],M,Y,o,zt,J[this.layer].el)!=="continue"||xo(J[this.layer].el,M,Y)}.bind({layer:d}),24))),d++}while(t.bubbleScroll&&m!==i&&(m=xe(m,!1)));fn=s}},30),Fo=function(t){var n=t.originalEvent,e=t.putSortable,r=t.dragEl,l=t.activeSortable,u=t.dispatchSortableEvent,a=t.hideGhostForTarget,i=t.unhideGhostForTarget;if(n){var s=e||l;a();var c=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,d=document.elementFromPoint(c.clientX,c.clientY);i(),s&&!s.el.contains(d)&&(u("spill"),this.onSpill({dragEl:r,putSortable:e}))}};function hn(){}hn.prototype={startIndex:null,dragStart:function(t){var n=t.oldDraggableIndex;this.startIndex=n},onSpill:function(t){var n=t.dragEl,e=t.putSortable;this.sortable.captureAnimationState(),e&&e.captureAnimationState();var r=Ye(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(n,r):this.sortable.el.appendChild(n),this.sortable.animateAll(),e&&e.animateAll()},drop:Fo},be(hn,{pluginName:"revertOnSpill"});function gn(){}gn.prototype={onSpill:function(t){var n=t.dragEl,e=t.putSortable,r=e||this.sortable;r.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),r.animateAll()},drop:Fo},be(gn,{pluginName:"removeOnSpill"}),$.mount(new va),$.mount(gn,hn);function Oo(o,t={}){return new $(o,{forceFallback:!0,delay:150,touchStartThreshold:30,...t})}function ya(o,t){return n=>{const{getRowNode:e}=o,{newIndex:r=0,oldIndex:l=0}=n,u=e(n.item),{items:a=[],item:i}=u||{};i&&(a.splice(l,1),a.splice(r,0,i),o.reloadData(a)),t("rowSort",{info:u,newIndex:r,oldIndex:l,e:n})}}function wa(o){const{getColumnNode:t}=o;return n=>{const{dragged:e,related:r}=n,l=t(e),u=t(r);return(l==null?void 0:l.parent)===(u==null?void 0:u.parent)}}function ba(o,t,n){const{getTableColumn:e,getColumnNode:r,getColumnIndex:l,loadColumn:u}=o,a=(i,s=0)=>i.filter(c=>c.visible)[s];return i=>{const{item:s,newIndex:c=-1,oldIndex:d=-1}=i,{collectColumn:m,fullColumn:p}=e(),f=r(s);if(!f)return;const V=f.parent,h=V?V.children:m,y=p.filter(g=>g.level===f.item.level).findIndex(g=>{var x;return g.id===((x=h[0])==null?void 0:x.id)}),b=l(a(h,d-y)),w=l(a(h,c-y)),_=h.splice(b,1)[0];h.splice(w,0,_),u(m);const C={info:_,newIndex:w,oldIndex:b,e:i};n&&n(C),t("columnSort",C)}}function Ca(o){const t={};return o.forEach((n,e)=>{t[n]=e}),t}function $e(o){return o.field||o.type||""}function _a(o,t){const{resize:n={},visible:e={},sort:r=[],fixed:l={}}=t,u=Ca(r),a=i=>{i.sort((s,c)=>u[$e(s)]-u[$e(c)]||0);for(let s of i){const c=$e(s),d=n[c],m=e[c],p=l[c];d&&(s.width=d),m!==void 0&&(s.visible=m),p!==void 0&&(s.fixed=p),s.children&&a(s.children)}};return a(o),o}function ve(o,t){const{row:n,column:e}=t;let r=n[e.field];const{props:l={},events:u={}}=o,a=VtjUtils.isFunction(l)?l({row:n,column:e,cellValue:r}):l,i=Object.entries(u).reduce((s,[c,d])=>{const m=VtjUtils.camelCase(`on-${c}`);return s[m]=(...p)=>d({cellValue:r,row:n,column:e},...p),s},{});return VtjUtils.isFunction(e.formatter)&&(r=e.formatter({row:n,column:e,cellValue:r})),{props:Object.assign({},a,i),cellValue:r,row:n,column:e}}const ka={cellClassName:"x-grid__x-actions",renderDefault(o,t){const{props:n}=ve(o,t);return[Vue.h(ze,{...n})]},renderCell(o,t){const{props:n}=ve(o,t);return[Vue.h(ze,{...n})]}},Ea=Vue.defineComponent({name:"DateEdit",__name:"DateEdit",props:{params:{},renderOpts:{}},setup(o){const t=o,n=Vue.ref(),{renderProps:e,renderEvents:r,cellValue:l,onChange:u}=Fe(t.renderOpts,t.params);return Vue.onMounted(()=>{var a;(a=n.value)==null||a.focus()}),(a,i)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElDatePicker),Vue.mergeProps({ref_key:"elRef",ref:n,size:"small",clearable:"","value-format":"YYYY-MM-DD",modelValue:Vue.unref(l),"onUpdate:modelValue":i[0]||(i[0]=s=>Vue.isRef(l)?l.value=s:null),onChange:Vue.unref(u)},Vue.unref(e),Vue.toHandlers(Vue.unref(r))),null,16,["modelValue","onChange"]))}}),Sa={class:"x-grid__filter x-grid__DateFilter"},xa=Vue.defineComponent({name:"DateFilter",__name:"DateFilter",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,state:r,load:l,onChange:u,onKeyup:a}=Mt(t.renderOpts,t.params);return l(),(i,s)=>(Vue.openBlock(),Vue.createElementBlock("div",Sa,[Vue.createVNode(Vue.unref(ElementPlus.ElDatePicker),Vue.mergeProps({size:"small",placeholder:"选择日期筛选","value-format":"YYYY-MM-DD",clearable:"",modelValue:Vue.unref(r).option.value,"onUpdate:modelValue":s[0]||(s[0]=c=>Vue.unref(r).option.value=c),onChange:Vue.unref(u),onKeyup:Vue.withKeys(Vue.withModifiers(Vue.unref(a),["stop"]),["enter"])},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onChange","onKeyup"])]))}}),jo=(o,t)=>{const{row:n,column:e}=t;return[Vue.createTextVNode(n[e.field]||"")]},Ba={cellClassName:"x-grid__edit",autofocus:".el-input__inner",renderEdit(o,t){return[Vue.h(Ea,{params:t,renderOpts:o})]},renderCell:jo,renderDefault:jo,...Ie,renderFilter(o,t){return[Vue.h(xa,{params:t,renderOpts:o})]}},Pa={...qe,cellClassName:"x-grid__x-image",renderDefault(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElImage,{style:{width:"100px",height:"100px"},src:e,...n})]},renderCell(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElImage,{style:{width:"100px",height:"100px"},src:e,...n})]}},Na={...qe,cellClassName:"x-grid__x-link",renderDefault(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElLink,{type:"primary",target:"_blank",href:e,...n},()=>e)]},renderCell(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElLink,{type:"primary",target:"_blank",href:e,...n},()=>e)]}},Ta=Vue.defineComponent({name:"SelectEdit",__name:"SelectEdit",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,cellValue:r,onChange:l}=Fe(t.renderOpts,t.params);return(u,a)=>(Vue.openBlock(),Vue.createBlock(Qt,Vue.mergeProps({size:"small",clearable:"",modelValue:Vue.unref(r),"onUpdate:modelValue":a[0]||(a[0]=i=>Vue.isRef(r)?r.value=i:null),onChange:Vue.unref(l)},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onChange"]))}}),Da={class:"x-grid__filter"},za=Vue.defineComponent({name:"SelectFilter",__name:"SelectFilter",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,state:r,load:l,onChange:u,onKeyup:a}=Mt(t.renderOpts,t.params);return l(),(i,s)=>(Vue.openBlock(),Vue.createElementBlock("div",Da,[Vue.createVNode(Qt,Vue.mergeProps({size:"small",placeholder:"选择筛选项",clearable:"",modelValue:Vue.unref(r).option.value,"onUpdate:modelValue":s[0]||(s[0]=c=>Vue.unref(r).option.value=c),onChange:Vue.unref(u),onKeyup:Vue.withKeys(Vue.withModifiers(Vue.unref(a),["stop"]),["enter"])},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onChange","onKeyup"])]))}});function Uo(o,t){var c;const{props:n={}}=o,{options:e=[],multiple:r,parser:l}=n,{row:u,column:a}=t,i=(c=u[a.field])!=null?c:"";let s;if(Array.isArray(e))if(r){const d=l?l(i):i||[];s=e.filter(p=>d.includes(p.value)).map(p=>p.label).join(",")}else{const d=e.find(m=>m.value===i);s=d==null?void 0:d.label}return[Vue.createTextVNode(s||i)]}const Ia={...qe,cellClassName:"x-grid__edit",autofocus:".el-select__input",renderEdit(o,t){return[Vue.h(Ta,{params:t,renderOpts:o})]},...Ie,renderFilter(o,t){return[Vue.h(za,{params:t,renderOpts:o})]},renderDefault:Uo,renderCell:Uo},Ma={...qe,cellClassName:"x-grid__x-tag",renderDefault(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElTag,{size:"small",type:"primary",...n},()=>e)]},renderCell(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElTag,{size:"small",type:"primary",...n},()=>e)]}},Aa={...qe,cellClassName:"x-grid__x-text",renderDefault(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElText,{...n},()=>e)]},renderCell(o,t){const{props:n,cellValue:e}=ve(o,t);return[Vue.h(ElementPlus.ElText,{...n},()=>e)]}},Ra=Vue.defineComponent({name:"PickerEdit",__name:"PickerEdit",props:{params:{},renderOpts:{}},setup(o){const t=o,n=Vue.ref(),{renderProps:e,renderEvents:r,cellValue:l,onChange:u}=Fe(t.renderOpts,t.params);return Vue.onMounted(()=>{var a;(a=n.value)==null||a.focus()}),(a,i)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Rt),Vue.mergeProps({ref_key:"elRef",ref:n,size:"small",modelValue:Vue.unref(l),"onUpdate:modelValue":i[0]||(i[0]=s=>Vue.isRef(l)?l.value=s:null),onChange:Vue.unref(u),data:t.params},Vue.unref(e),Vue.toHandlers(Vue.unref(r))),null,16,["modelValue","onChange","data"]))}}),Lo=(o,t)=>{const{row:n,column:e}=t;return[Vue.createTextVNode(n[e.field]||"")]},$a={cellClassName:"x-grid__edit",autofocus:".el-select__input",renderEdit(o,t){return[Vue.h(Ra,{params:t,renderOpts:o})]},renderCell:Lo,renderDefault:Lo,...Ie,renderFilter(o,t){return[Vue.h(Zt,{params:t,renderOpts:o})]}},Fa=Vue.defineComponent({name:"NumberEdit",__name:"NumberEdit",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,cellValue:r,onChange:l}=Fe(t.renderOpts,t.params);return(u,a)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElInputNumber),Vue.mergeProps({size:"small",clearable:"",modelValue:Vue.unref(r),"onUpdate:modelValue":a[0]||(a[0]=i=>Vue.isRef(r)?r.value=i:null),onInput:Vue.unref(l)},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onInput"]))}}),Oa={class:"x-grid__filter"},ja=Vue.defineComponent({name:"NumberFilter",__name:"NumberFilter",props:{params:{},renderOpts:{}},setup(o){const t=o,{renderProps:n,renderEvents:e,state:r,load:l,onChange:u,onKeyup:a}=Mt(t.renderOpts,t.params);return l(),(i,s)=>(Vue.openBlock(),Vue.createElementBlock("div",Oa,[Vue.createVNode(Vue.unref(ElementPlus.ElInputNumber),Vue.mergeProps({size:"small",placeholder:"输入数量回车筛选",clearable:"",modelValue:Vue.unref(r).option.value,"onUpdate:modelValue":s[0]||(s[0]=c=>Vue.unref(r).option.value=c),onInput:Vue.unref(u),onKeyup:Vue.withKeys(Vue.withModifiers(Vue.unref(a),["stop"]),["enter"])},Vue.unref(n),Vue.toHandlers(Vue.unref(e))),null,16,["modelValue","onInput","onKeyup"])]))}}),Ua={cellClassName:"x-grid__edit",...{autofocus:".el-input__inner",renderDefault(o,t){var r;const{row:n,column:e}=t;return[Vue.createTextVNode((r=n[e.field])!=null?r:"")]},renderEdit(o,t){return[Vue.h(Fa,{params:t,renderOpts:o})]},renderCell(o,t){var r;const{row:n,column:e}=t;return[Vue.createTextVNode((r=n[e.field])!=null?r:"")]}},...Ie,renderFilter(o,t){return[Vue.h(ja,{params:t,renderOpts:o})]}},Ho=Vue.defineComponent({name:"XDialogGrid",__name:"DialogGrid",props:Vue.mergeModels({columns:{},model:{},formatter:{},valueFormatter:{},rules:{},plus:{type:Boolean,default:!0},minus:{type:Boolean,default:!0},gridProps:{},submitMethod:{}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:Vue.mergeModels(["submit"],["update:modelValue"]),setup(o,{expose:t,emit:n}){const e=Vue.ref(),r=Vue.ref(),l=o,u=n,a=Vue.useModel(o,"modelValue"),i=Vue.computed(()=>{var h;return(h=r.value)==null?void 0:h.$vtjEl}),s=oo(["top","extra","buttons"]),c=Vue.computed(()=>l.formatter?l.formatter(l.model):l.model),d=()=>{var h;(h=e.value)==null||h.insertActived()},m=()=>{if(!e.value)return;const h=e.value.getSelected()||[];e.value.remove(h)},p=async()=>e.value?!await e.value.validate():!1,f=async()=>{if(!e.value)return;if(await p()){const v=e.value.getRows(),y=l.valueFormatter?l.valueFormatter(v):v;l.submitMethod&&await l.submitMethod(y)&&(a.value=!1),u("submit",y)}},V=()=>{var h;return(h=e.value)==null?void 0:h.$vtjDynamicSlots()};return Vue.watch(e,async h=>{h&&(await VtjUtils.delay(),h.doLayout())}),t({$vtjEl:i,$vtjDynamicSlots:V,gridRef:e,dialogRef:r,addRow:d,removeRow:m,validate:p}),(h,v)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Xe),{ref_key:"dialogRef",ref:r,class:"x-dialog-grid",submit:"",cancel:"",primary:"",modelValue:a.value,"onUpdate:modelValue":v[0]||(v[0]=y=>a.value=y),onSubmit:f},{extra:Vue.withCtx(()=>[Vue.renderSlot(h.$slots,"extra")]),default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(At),Vue.mergeProps({ref_key:"gridRef",ref:e,editable:"",columns:l.columns,data:c.value,"edit-rules":l.rules},l.gridProps),Vue.createSlots({toolbar__buttons:Vue.withCtx(()=>[l.plus?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:0,size:"small",label:"增行",type:"primary",icon:Vue.unref(VtjIcons.Plus),onClick:d},null,8,["icon"])):Vue.createCommentVNode("",!0),l.minus?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:1,size:"small",label:"删行",type:"default",icon:Vue.unref(VtjIcons.Minus),onClick:m},null,8,["icon"])):Vue.createCommentVNode("",!0),Vue.renderSlot(h.$slots,"buttons")]),top:Vue.withCtx(()=>[Vue.renderSlot(h.$slots,"top")]),_:2},[Vue.renderList(Vue.unref(s),y=>({name:y,fn:Vue.withCtx(b=>[Vue.renderSlot(h.$slots,y,Vue.normalizeProps(Vue.guardReactiveProps(b)))])}))]),1040,["columns","data","edit-rules"])]),_:3},8,["modelValue"]))}}),vn=Vue.defineComponent({name:"XGridEditor",__name:"GridEditor",props:{title:{},columns:{},modelValue:{},formatter:{},valueFormatter:{},rules:{},plus:{type:Boolean,default:!0},minus:{type:Boolean,default:!0},gridProps:{},dialogProps:{}},emits:["update:modelValue","change"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.ref(!1),u=Vue.ref(),a=Vue.computed(()=>typeof e.modelValue=="object"?JSON.stringify(e.modelValue):e.modelValue),i=()=>{l.value=!0},s=()=>{var m;(m=u.value)==null||m.focus()},c=()=>{var m;(m=u.value)==null||m.blur()},d=async m=>(r("change",m),r("update:modelValue",m),!0);return t({open:i,dialogVisible:l,focus:s,blur:c}),(m,p)=>(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,null,[Vue.createVNode(Vue.unref(ElementPlus.ElInput),Vue.mergeProps({ref_key:"inputRef",ref:u,"suffix-icon":Vue.unref(VtjIcons.EditPen),readonly:"",onFocus:i,"model-value":a.value},m.$attrs),null,16,["suffix-icon","model-value"]),Vue.createVNode(Vue.unref(Ho),Vue.mergeProps({modelValue:l.value,"onUpdate:modelValue":p[0]||(p[0]=f=>l.value=f),title:e.title,model:e.modelValue,columns:e.columns,formatter:e.formatter,valueFormatter:e.valueFormatter,rules:e.rules,plus:e.plus,minus:e.minus,"submit-method":d,"grid-props":e.gridProps},e.dialogProps),null,16,["modelValue","title","model","columns","formatter","valueFormatter","rules","plus","minus","grid-props"])],64))}}),La=Vue.defineComponent({name:"GridEdit",__name:"GridEdit",props:{params:{},renderOpts:{}},setup(o){const t=o,n=Vue.ref(),{renderProps:e,renderEvents:r,cellValue:l,onChange:u}=Fe(t.renderOpts,t.params);return Vue.onMounted(()=>{var a;(a=n.value)==null||a.focus()}),(a,i)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(vn),Vue.mergeProps({ref_key:"elRef",ref:n,size:"small",modelValue:Vue.unref(l),"onUpdate:modelValue":i[0]||(i[0]=s=>Vue.isRef(l)?l.value=s:null),onChange:Vue.unref(u),data:t.params},Vue.unref(e),Vue.toHandlers(Vue.unref(r))),null,16,["modelValue","onChange","data"]))}}),Xo=(o,t)=>{const{row:n,column:e}=t;return[Vue.createTextVNode(n[e.field]||"")]},Ha={cellClassName:"x-grid__edit",autofocus:".el-input__inner",renderEdit(o,t){return[Vue.h(La,{params:t,renderOpts:o})]},renderCell:Xo,renderDefault:Xo,...Ie,renderFilter(o,t){return[Vue.h(Zt,{params:t,renderOpts:o})]}},Xa=o=>{const{$grid:t,$event:n,$table:e}=o;if(e.getParentElem().contains(n.target))t.clearValidate();else return!1},qa=o=>{const{$event:t,$table:n}=o;if(!n.getParentElem().contains(t.target))return!1},Ka={install(o){o.renderer.mixin({XInput:Kl,XActions:ka,XDate:Ba,XImage:Pa,XLink:Na,XSelect:Ia,XTag:Ma,XText:Aa,XPicker:$a,XNumber:Ua,XGrider:Ha}),o.interceptor.add("event.clearFilter",qa),o.interceptor.add("event.clearEdit",Xa)}};function Ya(o={}){const t=[VXETable.VxeTableFilterModule,VXETable.VxeTableEditModule,VXETable.VxeTableMenuModule,VXETable.VxeTableExportModule,VXETable.VxeTableKeyboardModule,VXETable.VxeTableValidatorModule,VXETable.VxeTableCustomModule,VXETable.VxeGrid,VXETable.VxeTooltip,VXETable.VxeToolbar,VXETable.VxeModal],n=Vue.getCurrentInstance(),e=n==null?void 0:n.appContext.app,{vxeConfig:r,vxePlugin:l}=Vt(),u=VueUse.useDark();return e&&!e.__installVxe&&(VXETable.VXETable.use(Ka),l&&VXETable.VXETable.use(l),VXETable.VXETable.setConfig({...r,...o}),t.forEach(a=>e.use(a)),e.__installVxe=!0),Vue.watch(u,a=>{VXETable.VXETable.setTheme(a?"dark":"light")},{immediate:!0}),{VxeGrid:VXETable.VxeGrid,VXETable:VXETable.VXETable}}const Ga="toolbar__buttons";function oe(o,t){var n,e;return(e=(n=o[VtjUtils.camelCase(t)])!=null?n:o[VtjUtils.kebabCase(t)])!=null?e:void 0}function Wa(o,t){const{resizable:n}=o;return{useKey:!0,resizable:n,...oe(t,"columnConfig")}}function Ja(o){return{useKey:!0,isCurrent:!0,isHover:!0,...oe(o,"rowConfig")}}function Qa(o,t){const{virtual:n}=o;return n?Object.assign({enabled:!0,gt:20,scrollToTopOnChange:!0},oe(t,"scrollY")||{}):oe(t,"scrollY")}function Za(o,t){const{pager:n}=o;return n?Object.assign({remote:n},oe(t,"fitlerConfig")||{}):oe(t,"fitlerConfig")}function eu(o,t){const{pager:n}=o;return n?Object.assign({remote:n},oe(t,"sortConfig")||{}):oe(t,"sortConfig")}function tu(o,t,n){const{editable:e}=o,r=!!e,l=e?{enabled:!!e,mode:"cell",trigger:"dblclick",showStatus:!0,...oe(t,"editConfig")||{}}:oe(t,"editConfig"),u={selected:!!e,...oe(t,"mouseConfig")||{}},a=e?{isArrow:!0,isEsc:!0,isTab:!0,isEdit:!0,isEnter:!0,isChecked:!0,isDel:!0,...oe(t,"keyboardConfig")||{}}:oe(t,"keyboardConfig");return{keepSource:r,editConfig:l,mouseConfig:u,keyboardConfig:a,onCellSelected:s=>{const{$grid:c}=s;c.clearValidate(),n("cellSelected",s)}}}function nu(o,t,n){const e=oe(t,"toolbarConfig"),r=n.find(u=>u===Ga),l={enabled:!!e||!!r,custom:!!o.customable,slots:{buttons:r}};return VtjUtils.merge(l,e||{})}function ou(o,t){const{sumFields:n=[],avgFields:e=[],sumAllFields:r}=o,l=!!n.length||!!e.length||!!r||oe(t,"showFooter"),u=(c,d)=>{let m=0;return c.forEach(p=>{m+=Number(p[d]||0)}),VtjUtils.toFixed(m,4,!0)},a=(c,d)=>{let m=u(c,d);return VtjUtils.toFixed(m/c.length,4,!0)};return{footerMethod:oe(t,"footerMethod")||(c=>{const{columns:d,data:m}=c,p=[];if(n.length){const f=d.map((V,h)=>h===0?"合计":n.includes(V.field)?u(m,V.field):null);p.push(f)}if(e.length){const f=d.map((V,h)=>h===0?"平均":e.includes(V.field)?a(m,V.field):null);p.push(f)}if(r){const f=Object.keys(r),V=d.map((h,v)=>v===0?"总计":f.includes(h.field)?r[h.field]:null);p.push(V)}return p}),showFooter:l}}function ru(o,t,n){const e=Vue.useAttrs(),r={layouts:["Toolbar","Form","Top","Table","Bottom","Pager"],loading:!1,size:"small",height:"auto",border:!0,stripe:!0,showOverflow:"tooltip",autoResize:!1};return{vxeProps:Vue.computed(()=>{const u=Wa(o,e),a=Ja(e),i=Qa(o,e),s=Za(o,e),c=eu(o,e),d=nu(o,e,t),{keepSource:m,editConfig:p,mouseConfig:f,keyboardConfig:V,onCellSelected:h}=tu(o,e,n),{footerMethod:v,showFooter:y}=ou(o,e);return{...r,...e,id:o.id,columnConfig:u,rowConfig:a,scrollY:i,filterConfig:s,sortConfig:c,keepSource:m,editConfig:p,mouseConfig:f,keyboardConfig:V,toolbarConfig:d,onCellSelected:h,footerMethod:v,showFooter:y}})}}function lu(o,t,n){const e=Vue.ref();if(!t.rowSortable)return e;const r=()=>{const l=Vue.unref(o);if(!l)return;const u=l.$el.querySelector(".vxe-table--body>tbody");if(u){const a={...t.rowSortable,onUpdate:ya(l,n)};e.value=Oo(u,a)}};return Vue.onMounted(r),Vue.onUnmounted(()=>{var l;(l=e.value)==null||l.destroy()}),Vue.onUpdated(()=>{var l;(l=e.value)==null||l.destroy(),r()}),e}function au(o,t,n,e){const r=Vue.ref([]);if(!t.columnSortable)return r;const l=async()=>{const i=Vue.unref(o);if(!i||!i.$el)return;await Vue.nextTick();const s=Array.from(i.$el.querySelectorAll(".vxe-header--row")),c={draggable:".vxe-header--column:not(.col--fixed)",filter:".vxe-header--gutter",handle:".vxe-cell",...t.columnSortable,onMove:wa(i),onUpdate:ba(i,n,e)};r.value=s.map(d=>Oo(d,c))},u=()=>{r.value.forEach(i=>{i.destroy()}),r.value=[]},a=async()=>{r.value.length>0&&u(),t.columns&&l()};return Vue.watch(()=>t.columns,a),Vue.onMounted(a),Vue.onUnmounted(u),r}function qo(o){const{columns:t=[],cellRenders:n={},filterRenders:e={},editRenders:r={},editable:l}=o,u=VtjUtils.cloneDeep(t);for(const a of u){const{field:i,children:s=[]}=a;if(i){if(l&&r[i]){const c=r[i];a.editRender=typeof c=="string"?{name:c,props:{}}:c}else if(n[i]){const c=n[i];a.cellRender=typeof c=="string"?{name:c,props:{}}:c}if(e[i]){const c=e[i];a.filterRender=typeof c=="string"?{name:c}:c,a.filters||(a.filters=[{value:""}])}}s.length&&(a.children=qo({columns:a.children,cellRenders:n,editRenders:r,filterRenders:e,editable:l}))}return u}function uu(o,t){const n=Vue.shallowRef([]),e=Vt();let r=null;const{customable:l,getCustom:u=e.getCustom,saveCustom:a=e.saveCustom}=t,i=p=>t.id||`X_Grid_${(p==null?void 0:p.id)||(p==null?void 0:p.$.uid)}`,s=p=>{if(!l||!r)return;const f=p.column,V=$e(f),h=f.renderWidth;r.resize?r.resize[V]=h:r.resize={[V]:h},a&&a(r)},c=p=>{if(!(!l||!r)&&["confirm","reset"].includes(p.type)){const{fullColumn:f,collectColumn:V}=p.$grid.getTableColumn(),h={},v={};f.forEach(y=>{const b=$e(y);h[b]=y.visible}),V.forEach(y=>{const b=$e(y);y.fixed&&(v[b]=y.fixed)}),r.visible=h,r.fixed=v,a&&a(r)}},d=p=>{const f=Vue.unref(o);if(!l||!f||!r)return;const{collectColumn:V}=f.getTableColumn(),h=v=>{const y=[];for(let b of v)y.push($e(b)),b.children&&y.push(...h(b.children));return y};r.sort=h(V),a&&a(r)},m=async p=>{const f=Vue.unref(o);if(!l||!u){n.value=p;return}const V=i(f);r=await u(V).catch(()=>null)||{id:V},r&&(n.value=_a(p,r).slice(0))};return Vue.watch(()=>[t.columns,t.editable],()=>{m(qo(t))},{immediate:!0}),{columns:n,onResize:s,onCustom:c,onSort:d}}function iu(o=[]){const t=Vue.useSlots();return Object.keys(t).filter(n=>!o.includes(n))}function Fe(o,t){const{row:n,column:e}=t,{props:r={},events:l={}}=o,{parser:u,stringify:a}=r,i=Vue.computed({get(){const d=n[e.field];return u?u(d):d},set(d){n[e.field]=a?a(d):d}});return{renderProps:VtjUtils.isFunction(r)?r({row:n,column:e,cellValue:i}):r,renderEvents:l,cellValue:i,row:n,column:e,onChange:()=>{var d;(d=t.$grid)==null||d.updateStatus(t,i.value)}}}function Mt(o,t){const{props:n={},events:e={}}=o,r=Vue.reactive({option:null});return{renderProps:n,renderEvents:e,state:r,load:()=>{if(t){const{column:i}=t,s=i.filters[0];r.option=s}},onChange:()=>{const{option:i}=r;if(t&&i){const{$panel:s}=t,c=!!i.value;s.changeOption(null,c,i)}},onKeyup:i=>{if(t){const{$panel:s}=t;s.confirmFilter(i)}}}}function su(o){const t=Vue.toRef(o,"page"),n=Vue.toRef(o,"pageSize"),e=Vue.reactive({page:o.page,pageSize:o.pageSize,total:0,filters:[],sorts:[]}),r=()=>{Object.assign(e,{page:o.page,pageSize:o.pageSize,total:0,filters:[],sorts:[]})};return Vue.watch([t,n],([l,u])=>{e.page=l,e.pageSize=u},{immediate:!0}),{state:e,resetState:r}}function cu(o,t,n){const{auto:e,pager:r}=o,{state:l,resetState:u}=su(o),a=async(f,V)=>{const h=t.value;h&&(V?await h.reloadData(f):await h.loadData(f))},i=async f=>{if(o.loader){const{list:V=[],total:h=0}=await o.loader(l)||{};await Vue.nextTick(),await a(V,f),await Vue.nextTick(),l.total=h,await Vue.nextTick(),n("loaded",V)}},s=f=>{l.page=o.page||1,i(f)},c=(f,V)=>{var h;l.page=f,l.pageSize=V,(h=t.value)==null||h.scrollTo(0,0),i()},d=f=>{l.filters=f.filterList,r&&i()},m=f=>{l.sorts=f.sortList,r&&i()},p=()=>{var f;(f=t.value)==null||f.recalculate(!0)};return Vue.watch(()=>o.pager,()=>{Vue.nextTick(p)}),Vue.watch(()=>o.loader,()=>{u(),e&&i()},{immediate:!0}),{state:l,load:i,search:s,onPagerChange:c,onFilterChange:d,onSortChange:m,doLayout:p}}const du={id:{type:String},columns:{type:Array,default(){return[]}},loader:{type:Function},rowSortable:{type:[Boolean,Object],default:!1},columnSortable:{type:[Boolean,Object],default:!1},customable:{type:Boolean},getCustom:{type:Function},saveCustom:{type:Function},resizable:{type:Boolean,default:!1},pager:{type:Boolean},page:{type:Number,default:1},pageSize:{type:Number,default:50},pageSizes:{type:Array,default:()=>[50,100,200,500]},auto:{type:Boolean,default:!0},virtual:{type:Boolean,default:!1},cellRenders:{type:Object},editRenders:{type:Object},filterRenders:{type:Object},editable:{type:Boolean,default:!1},sumFields:{type:Array},avgFields:{type:Array},sumAllFields:{type:Object}},Ko=Symbol("GridInstanceKey"),At=Vue.defineComponent({name:"XGrid",inheritAttrs:!1,__name:"Grid",props:du,emits:["rowSort","columnSort","cellSelected","editChange","loaded"],setup(o,{expose:t,emit:n}){const{VxeGrid:e}=Ya(),r=Vue.ref(),l=o,u=n,a=iu(),{vxeProps:i}=ru(l,a,u),{columns:s,onResize:c,onCustom:d,onSort:m}=uu(r,l),p=lu(r,l,u),f=au(r,l,u,m),{state:V,onPagerChange:h,onFilterChange:v,onSortChange:y,load:b,search:w,doLayout:_}=cu(l,r,u),C=Vue.getCurrentInstance();Vue.provide(Ko,C);const g=async(E={},D=-1)=>{l.editable||console.warn("XGrid在编辑模式需要开启editable");const O=r.value;if(!O)return;const{row:X}=await O.insertAt(E,D);await O.setEditRow(X)},x=async E=>{var D;return(D=r.value)==null?void 0:D.setEditRow(E)},S=()=>{const E=r.value;if(!E)return[];const{fullData:D=[]}=E.getTableData();return D},I=()=>{const E=r.value;return E?E.getColumns():[]},z=async()=>{var D;const E=S();return(D=r.value)==null?void 0:D.validate(E)},N=()=>{const E=r.value;if(!E)return null;const D=E.getColumns();return D.find(ue=>ue.type==="radio")?E.getRadioRecord(!1):D.find(ue=>ue.type==="checkbox")?E.getCheckboxRecords(!1):null},T=()=>{const E=S();u("editChange",E)},A=async E=>{const D=r.value;if(!D)return;if(!E||Array.isArray(E)&&!E.length){ElementPlus.ElNotification.info({message:"请选择需要删除的数据"});return}const O=await ElementPlus.ElMessageBox.confirm("确认删除数据？","提示",{type:"warning"});return O&&(await D.remove(E),T()),O},Y=()=>{var E;return(E=r.value)==null?void 0:E.getRecordset()},M=(E,D)=>{if(r.value){if(E)r.value.setSelectCell(E,D!=null?D:I()[0]);else{const O=S(),X=I();r.value.setSelectCell(O[0],X[0])}r.value.focus()}},k=()=>{var O;const{fullColumn:E=[]}=((O=r.value)==null?void 0:O.getTableColumn())||{};let D=[];for(const X of E){const ue=Object.values(X.slots||{}).filter(we=>typeof we=="string");D=D.concat(ue)}return D};return Vue.onActivated(()=>{var E;(E=r.value)==null||E.recalculate(!0)}),t({state:V,load:b,search:w,vxeRef:r,rowSortable:p,columnSortable:f,insertActived:g,validate:z,getSelected:N,remove:A,getRows:S,setActived:x,doLayout:_,getRecords:Y,setSelectCell:M,$vtjDynamicSlots:k}),(E,D)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(e),Vue.mergeProps({ref_key:"vxeRef",ref:r,class:"x-grid"},Vue.unref(i),{columns:Vue.unref(s),onCustom:Vue.unref(d),loading:!1,onResizableChange:Vue.unref(c),onEditClosed:T,onFilterChange:Vue.unref(v),onSortChange:Vue.unref(y)}),Vue.createSlots({empty:Vue.withCtx(()=>[Vue.renderSlot(E.$slots,"empty",{},()=>[Vue.createVNode(Vue.unref(ElementPlus.ElEmpty))])]),pager:Vue.withCtx(()=>[l.pager?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElPagination),{key:0,class:"x-grid__pager",size:"small",background:"",layout:"slot, -> ,total, sizes, prev, pager, next, jumper","page-sizes":l.pageSizes,"default-page-size":l.pageSizes[0],"current-page":Vue.unref(V).page,"onUpdate:currentPage":D[0]||(D[0]=O=>Vue.unref(V).page=O),"v-model:page-size":Vue.unref(V).pageSize,total:Vue.unref(V).total,onChange:Vue.unref(h)},{default:Vue.withCtx(()=>[Vue.renderSlot(E.$slots,"pager__left")]),_:3},8,["page-sizes","default-page-size","current-page","v-model:page-size","total","onChange"])):Vue.createCommentVNode("",!0)]),_:2},[Vue.renderList(Vue.unref(a),O=>({name:O,fn:Vue.withCtx(X=>[Vue.renderSlot(E.$slots,O,Vue.normalizeProps(Vue.guardReactiveProps(X)))])}))]),1040,["columns","onCustom","onResizableChange","onFilterChange","onSortChange"]))}}),fu={collapsible:{type:Boolean,default:!0},items:{type:Array},inlineColumns:{type:Number,default:4},disabled:{type:Boolean}};function pu(o,t){const n=Vue.ref(!!o.collapsible),e=Vue.useAttrs(),r=Vue.inject(Ko,null),l={large:45,default:37,small:29},u=Vue.computed(()=>o.items?o.collapsible&&o.items.length>(o.inlineColumns||0):o.collapsible),a=async()=>{var c;o.collapsible&&(n.value=!n.value,t("collapsed",n.value),r&&r&&(await Vue.nextTick(),(c=r.exposed)==null||c.doLayout()))},i=Vue.computed(()=>({"is-collapsed":u.value&&n.value})),s=Vue.computed(()=>{var d;const c=(d=l[e.size||"default"])!=null?d:l.default;return u.value&&n.value?{height:`${c}px`}:null});return{collapsed:n,toggleCollapsed:a,collapsedClass:i,collapsedStyle:s,showCollapsible:u}}const mu={key:0,class:"x-query-form__collapsible"},yn=Vue.defineComponent({name:"XQueryForm",__name:"QueryForm",props:fu,emits:["collapsed"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.ref(),{collapsed:u,toggleCollapsed:a,collapsedClass:i,collapsedStyle:s,showCollapsible:c}=pu(e,r),d=async()=>{var v;(v=l.value)==null||v.submit()},m=async()=>{var v;return await((v=l.value)==null?void 0:v.validate())},p=()=>{var v;(v=l.value)==null||v.clearValidate()},f=v=>{var y;(y=l.value)==null||y.reset(v)},V=v=>typeof v=="string";return t({validate:m,clearValidate:p,submit:d,reset:f,formRef:l,$vtjDynamicSlots:()=>(e.items||[]).map(y=>V(y))}),(v,y)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Ot),{ref_key:"formRef",ref:l,class:"x-query-form",inline:"","inline-columns":e.inlineColumns,"label-width":"100px","submit-text":null,"reset-text":null,disabled:v.disabled,footer:Vue.unref(c)},{action:Vue.withCtx(()=>[Vue.unref(c)?(Vue.openBlock(),Vue.createElementBlock("div",mu,[Vue.unref(u)?(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:0,icon:Vue.unref(VtjIcons.CaretBottom),label:"展开",mode:"text",type:"primary",onClick:Vue.unref(a)},null,8,["icon","onClick"])):(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),{key:1,icon:Vue.unref(VtjIcons.CaretTop),label:"收起",mode:"text",type:"primary",onClick:Vue.unref(a)},null,8,["icon","onClick"]))])):Vue.createCommentVNode("",!0)]),default:Vue.withCtx(()=>[Vue.createElementVNode("div",{class:Vue.normalizeClass(["x-query-form__inner",Vue.unref(i)]),style:Vue.normalizeStyle(Vue.unref(s))},[Vue.renderSlot(v.$slots,"default",{},()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(e.items,b=>(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,null,[V(b)?Vue.renderSlot(v.$slots,b,{key:0}):(Vue.openBlock(),Vue.createBlock(Vue.unref(Ft),Vue.mergeProps({key:1,disabled:v.disabled},{ref_for:!0},b,{key:`field_${b.name}`}),null,16,["disabled"]))],64))),256))])],6)]),_:3},8,["inline-columns","disabled","footer"]))}}),Vu=Vue.defineComponent({__name:"Dialog",props:{gridProps:{},formProps:{},columns:{},fields:{},loader:{type:Function},formModel:{},multiple:{type:Boolean},onPick:{type:Function}},setup(o,{expose:t}){const n=o,e=Vue.ref(),r=()=>{var c;(c=e.value)==null||c.search()},l=()=>{var d;const c=(d=e.value)==null?void 0:d.getSelected();if(!c||Array.isArray(c)&&!c.length){ElementPlus.ElNotification.info({message:"请选择需要返回的行数据"});return}n.onPick(c)},u=c=>{n.onPick(n.multiple?[c.row]:c.row)};Vue.onMounted(()=>{var c;(c=e.value)==null||c.vxeRef.focus()});const a=c=>{var p;if(c.$event.key!=="Enter")return;c.$event.stopPropagation(),c.$event.preventDefault();const d=c.$event.target.nodeName||"";if(!["INPUT","SELECT","RADIO","CHECKBOX","WUJIE-APP"].includes(d.toUpperCase())){if(n.multiple)l();else{const{row:f}=((p=e.value)==null?void 0:p.vxeRef.getSelectedCell())||{};f&&n.onPick(f)}return!1}},i=()=>{var c;(c=e.value)==null||c.setSelectCell()},s=()=>{r()};return t({pick:l,gridRef:e}),(c,d)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Xe),{title:"选择数据",width:"70%",height:"70%",icon:Vue.unref(VtjIcons.VtjIconDatabase),primary:"",resizable:"",maximizable:""},{default:Vue.withCtx(()=>[n.columns&&n.columns.length?(Vue.openBlock(),Vue.createBlock(Vue.unref(At),Vue.mergeProps({key:0,ref_key:"gridRef",ref:e,columns:n.columns,pager:"","auto-resize":"",resizable:"",virtual:"","checkbox-config":{highlight:!0,trigger:"row"},"radio-config":{highlight:!0,trigger:"row"},"row-config":{isHover:!0},"mouse-config":{selected:!0},editable:"","keyboard-config":{isArrow:!0,isChecked:!0,isEnter:!1},loader:n.loader,onKeydown:a,onLoaded:i,onCellDblclick:u},n.gridProps),Vue.createSlots({toolbar__buttons:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(Q),{label:"查询",type:"primary",icon:Vue.unref(VtjIcons.VtjIconNpSearch),onClick:r},null,8,["icon"]),Vue.createVNode(Vue.unref(Q),{label:"返回数据",type:"primary",icon:Vue.unref(VtjIcons.VtjIconNpReturn),onClick:l},null,8,["icon"])]),_:2},[n.fields?{name:"form",fn:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(yn),Vue.mergeProps({model:n.formModel,items:n.fields,onSubmit:s},n.formProps),null,16,["model","items"])]),key:"0"}:void 0]),1040,["columns","loader"])):Vue.createCommentVNode("",!0)]),_:1},8,["icon"]))}}),hu={columns:{type:Array},fields:{type:Array},model:{type:Object},loader:{type:Function},modelValue:{type:[String,Number,Object,Array]},multiple:{type:Boolean},raw:{type:Boolean},disabled:{type:Boolean},append:{type:Boolean},valueKey:{type:String,default:"value"},labelKey:{type:String,default:"label"},queryKey:{type:String},preload:{type:Boolean},dialogProps:{type:Object},gridProps:{type:Object},formProps:{type:Object},data:{type:Object},formatter:{type:Function},valueFormatter:{type:Function},beforeInit:{type:Function}};function gu(o,t){const{multiple:n,raw:e,valueKey:r="value",labelKey:l="label",formatter:u,valueFormatter:a}=o,i=Vue.ref(),s=Vue.ref([]),c=(m,p)=>{var v;const f=VtjUtils.toArray(m).filter(y=>!!y).map(y=>{var b;return typeof y=="object"?{label:y[l],value:(b=y[r])!=null?b:JSON.stringify(y)}:{label:y,value:y}}),V=p?[...s.value,...f]:f;s.value=VtjUtils.dedupArray(V,"value");let h;n?h=s.value.map(y=>y.value):h=(v=s.value[0])==null?void 0:v.value,i.value=h},d=m=>{const p=s.value.map(f=>{const V=Vue.toRaw(f);return{[l]:V.label,[r]:V.value}});return Array.isArray(m)?m.map(f=>p.find(V=>V[r]===f)).filter(f=>!!f):p};return Vue.watch(()=>o.modelValue,async m=>{const p=u?u(m):m;if(p){const f=d(p);c(f.length?f:p),o.multiple||(await VtjUtils.delay(0),s.value=[])}else c([])},{immediate:!0}),Vue.watch(i,(m,p)=>{if(!VtjUtils.isEqual(m,p)){const f=d(m),V=e?f:m,h=a?a(V):V;n&&Array.isArray(V)&&(s.value=s.value.filter(v=>V.includes(v.value))),t("update:modelValue",h),t("change",h,o.data)}}),{current:i,options:s,setOptions:c}}function vu(o){return Vue.computed(()=>{const{multiple:t,columns:n=[]}=o;return n.length===0?[]:[{type:t?"checkbox":"radio",width:41,fixed:"left",showOverflow:!1,resizable:!1},{type:"seq",title:"序号",width:60,fixed:"left"}].concat(n)})}function yu(o){const t=Vue.ref({});return Vue.watch(()=>o.model,n=>{t.value=n||{}},{immediate:!0}),{formModel:t}}const Rt=Vue.defineComponent({name:"XPicker",inheritAttrs:!1,__name:"Picker",props:hu,emits:["update:modelValue","change","picked"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.useAttrs(),u=Vue.ref(!1),a=Vue.ref(),i=Vue.ref(),{options:s,setOptions:c,current:d}=gu(e,r),{formModel:m}=yu(e),p=vu(e),f=Vue.computed(()=>u.value?!0:!!e.disabled),V=_=>e.loader?(_.form=m.value,e.loader(_)):{list:[],total:0},h=()=>{var _;(_=a.value)==null||_.focus()},v=()=>{var _;(_=a.value)==null||_.blur()},y=async _=>{if(e.disabled)return;const C=_.target.value;if(e.queryKey&&(m.value[e.queryKey]=C),h(),e.beforeInit&&await e.beforeInit(),e.preload){const g=await V({});g!=null&&g.list&&g.list.length===1?(v(),w(g.list[0])):u.value=!0}else v(),u.value=!0},b=async()=>{e.disabled||(v(),e.beforeInit&&await e.beforeInit(),u.value=!0)},w=async _=>{c(_,e.multiple&&e.append),u.value=!1,r("picked",_,e.data),await Vue.nextTick(),h()};return Vue.watch(()=>e.queryKey,(_,C)=>{if(_&&C){const g=m.value[C];typeof g<"u"&&(m.value[_]=g,delete m.value[C])}}),Vue.watch(u,_=>{_||(m.value={})}),Vue.onMounted(()=>{var _;(_=a.value)!=null&&_.wrapperRef&&a.value.wrapperRef.appendChild(a.value.prefixRef)}),Vue.onUnmounted(()=>{var _;(_=a.value)!=null&&_.wrapperRef&&a.value.wrapperRef.appendChild(a.value.prefixRef)}),t({focus:h,blur:v,disabled:f,options:s,setOptions:c,current:d,visible:u,dialogRef:i,formModel:m}),(_,C)=>(Vue.openBlock(),Vue.createElementBlock(Vue.Fragment,null,[Vue.createVNode(Vue.unref(ElementPlus.ElSelect),Vue.mergeProps({ref_key:"selectRef",ref:a,class:"x-picker",teleported:!1,placeholder:"输入关键字回车查询",disabled:f.value,filterable:"",clearable:"",multiple:e.multiple,"collapse-tags":"","collapse-tags-tooltip":"","reserve-keyword":!1,"suffix-icon":Vue.unref(VtjIcons.VtjIconCheck),onKeydownCapture:Vue.withKeys(Vue.withModifiers(y,["stop","prevent"]),["enter"]),modelValue:Vue.unref(d),"onUpdate:modelValue":C[0]||(C[0]=g=>Vue.isRef(d)?d.value=g:null)},Vue.unref(l)),{prefix:Vue.withCtx(()=>[Vue.createElementVNode("div",{class:"x-picker__tigger",onClick:b},[Vue.createVNode(Vue.unref(VtjIcons.MoreFilled))])]),default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(Vue.unref(s),g=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElOption),{label:g.label,value:g.value},null,8,["label","value"]))),256))]),_:1},16,["disabled","multiple","suffix-icon","onKeydownCapture","modelValue"]),u.value&&e.loader?(Vue.openBlock(),Vue.createBlock(Vu,Vue.mergeProps({key:0,ref_key:"dialogRef",ref:i,modelValue:u.value,"onUpdate:modelValue":C[1]||(C[1]=g=>u.value=g),"grid-props":e.gridProps,"form-props":e.formProps,"form-model":Vue.unref(m),columns:Vue.unref(p),fields:e.fields,loader:V,multiple:e.multiple,onPick:w},e.dialogProps),null,16,["modelValue","grid-props","form-props","form-model","columns","fields","multiple"])):Vue.createCommentVNode("",!0)],64))}}),$t={none:{component:"div",props:{class:"x-field-none"}},text:{component:ElementPlus.ElInput,props:{clearable:!0},defaultValue:""},textarea:{component:ElementPlus.ElInput,props:{type:"textarea",rows:2},defaultValue:""},select:{component:Qt,props:{clearable:!0}},checkbox:{component:Ll,props:{},defaultValue:[]},radio:{component:Hl,props:{}},number:{component:ElementPlus.ElInputNumber,props:{}},date:{component:ElementPlus.ElDatePicker,props:{}},time:{component:ElementPlus.ElTimePicker,props:{}},datetime:{component:ElementPlus.ElDatePicker,props:{type:"datetime"}},switch:{component:ElementPlus.ElSwitch,props:{}},rate:{component:ElementPlus.ElRate,props:{}},slider:{component:ElementPlus.ElSlider,props:{}},cascader:{component:ElementPlus.ElCascader,props:{}},picker:{component:Rt,props:{}},grid:{component:vn,props:{}}};function Yo(o){Object.assign($t,o)}const Go={name:{type:String},label:{type:String},editor:{type:[String,Object],default:"text"},props:{type:Object},modelValue:{type:[String,Number,Boolean,Object,Array],default(o){const t=$t[o.editor];return t==null?void 0:t.defaultValue}},size:{type:String},width:{type:[String,Number]},tooltipMessage:{type:[Boolean,Object],default:!0},tooltipPosition:{type:[String,Number],default:"outer"},placeholder:{type:String},disabled:{type:Boolean},readonly:{type:Boolean},options:{type:[Array,Function]},visible:{type:[Boolean,Object,Function],default:!0},cascader:{type:[String,Array]},error:{type:String},tip:{type:String},inline:{type:Boolean},hidden:{type:Boolean},defaultValue:{type:[String,Number,Boolean,Object,Array],default:void 0}};async function wu(o,t){const n=o.options;return n?typeof n=="function"?await n(t)||[]:n:[]}function bu(o,t,n,e,r){const l=Vue.ref([]),u=Vue.computed(()=>!e||!r?{}:VtjUtils.toArray(o.cascader).reduce((c,d)=>(c[d]=VtjUtils.get(r,d),c),{}));Vue.watch([u,()=>o.options],async([s,c])=>{var d;n.value&&(l.value=await wu(o,s),(d=e==null?void 0:e.exposed)!=null&&d.reset&&o.name)},{immediate:!0});const a=o.props||{};return{editor:Vue.computed(()=>{const{editor:s="text",placeholder:c,label:d="...",disabled:m,readonly:p}=o,f={...a,placeholder:c||(c===null?void 0:`请输入${d}`),disabled:m,readonly:p,options:l.value,onFocus:()=>t("focus"),onBlur:()=>t("blur"),onChange:(h,v)=>t("change",h,v)},V=typeof s=="string"?$t[s]:{component:s,props:{}};return VtjUtils.merge({},V,{props:f})})}}const Cu={key:1,class:"el-form-item__error"},_u={key:0,class:"x-field__tip"},Ft=Vue.defineComponent({name:"XField",__name:"Field",props:Go,emits:["update:modelValue","change","focus","blur"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.inject(wn,null),u=Vue.inject(ElementPlus.formContextKey,null),a=Vue.inject(bn,null),i=Vue.computed(()=>!(l==null?void 0:l.proxy)||!a?e.visible:typeof e.visible=="function"?e.visible(a):VtjUtils.isObject(e.visible)?Object.entries(e.visible).every(([x,S])=>VtjUtils.get(a,x)===S):e.visible),s=()=>{var x,S,I;return(l==null?void 0:l.proxy)&&a&&e.name?(S=(x=VtjUtils.get(a,e.name))!=null?x:e.modelValue)!=null?S:e.defaultValue:(I=e.modelValue)!=null?I:e.defaultValue},c=Vue.ref(s()),{editor:d}=bu(e,r,i,l,a),m=Vue.ref(),p=Vue.ref(),f=Vue.computed(()=>e.size||(u==null?void 0:u.size)||"default"),V=Vue.computed(()=>Object.assign({placement:"bottom-end"},(l==null?void 0:l.props.tooltipMessage)||{},typeof e.tooltipMessage=="boolean"?{}:e.tooltipMessage)),h=(g="")=>e.error?e.error:g.includes("is required")?`${e.label||e.name}是必填项！`:g,v=Vue.computed(()=>{var x;const g=(x=l==null?void 0:l.props.tooltipMessage)!=null?x:e.tooltipMessage;return{[`is-tooltip-${e.tooltipPosition}`]:!!g&&!!e.tooltipPosition}}),y=Vue.computed(()=>{const g=l==null?void 0:l.proxy;return{width:e.width?Ve(e.width):g&&g.inline&&g.inlineColumns?`${100/g.inlineColumns}%`:null}}),b=Vue.computed(()=>({right:typeof e.tooltipPosition=="number"?`${e.tooltipPosition}px`:void 0})),w=Vue.computed(()=>({...d.value.props,modelValue:c.value,"onUpdate:modelValue":g=>{c.value=g}}));return Vue.watch(c,(g,x)=>{i.value&&!VtjUtils.isEqual(g,x)&&(r("update:modelValue",g),l!=null&&l.proxy&&a&&e.name&&VtjUtils.set(a,e.name,g))}),Vue.watch(()=>{var x;return!(l==null?void 0:l.proxy)||!e.name||!a?e.modelValue:(x=VtjUtils.get(a,e.name))!=null?x:e.modelValue},g=>{i.value&&(c.value=g)},{immediate:!0}),Vue.watch(i,g=>{var S;(l==null?void 0:l.proxy)&&a&&e.name?g?(c.value=s(),VtjUtils.set(a,e.name,c.value)):(c.value=void 0,VtjUtils.set(a,e.name,void 0)):c.value=(S=e.modelValue)!=null?S:e.defaultValue},{immediate:!0}),t({fieldValue:c,itemRef:m,editorRef:p,focus:()=>{var g;(g=p.value)!=null&&g.focus&&p.value.focus()},blur:()=>{var g;(g=p.value)!=null&&g.foucs&&p.value.blur()}}),(g,x)=>i.value?Vue.withDirectives((Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElFormItem),Vue.mergeProps({key:0,class:["x-field",v.value],ref_key:"itemRef",ref:m,prop:e.name,label:e.label,size:f.value,style:y.value},g.$attrs),Vue.createSlots({error:Vue.withCtx(({error:S})=>[Vue.renderSlot(g.$slots,"error",{error:S},()=>{var I,z;return[((z=(I=Vue.unref(l))==null?void 0:I.props.tooltipMessage)!=null?z:e.tooltipMessage)?(Vue.openBlock(),Vue.createElementBlock("div",{key:0,class:"x-field__info",style:Vue.normalizeStyle(b.value)},[Vue.createVNode(Vue.unref(ElementPlus.ElTooltip),Vue.mergeProps({content:h(S)},V.value),{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(de),{class:"x-field__trigger",icon:Vue.unref(VtjIcons.WarningFilled),size:f.value},null,8,["icon","size"])]),_:2},1040,["content"])],4)):(Vue.openBlock(),Vue.createElementBlock("div",Cu,Vue.toDisplayString(h(S)),1))]})]),default:Vue.withCtx(()=>[Vue.createElementVNode("div",{class:Vue.normalizeClass(["x-field__editor_wrap",{"is-inline":e.inline}])},[Vue.renderSlot(g.$slots,"editor",{editor:w.value},()=>[Vue.unref(d).component?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(d).component),Vue.mergeProps({key:0,class:"x-field__editor",ref_key:"editorRef",ref:p,modelValue:c.value,"onUpdate:modelValue":x[0]||(x[0]=S=>c.value=S)},Vue.unref(d).props),Vue.createSlots({_:2},[g.$slots.option?{name:"option",fn:Vue.withCtx(({option:S})=>[Vue.renderSlot(g.$slots,"option",{option:S})]),key:"0"}:void 0]),1040,["modelValue"])):Vue.createCommentVNode("",!0)]),e.tip||g.$slots.tip?(Vue.openBlock(),Vue.createElementBlock("div",_u,[Vue.renderSlot(g.$slots,"tip",{},()=>[Vue.createTextVNode(Vue.toDisplayString(e.tip),1)])])):Vue.createCommentVNode("",!0)],2),Vue.renderSlot(g.$slots,"default")]),_:2},[g.$slots.label?{name:"label",fn:Vue.withCtx(()=>[Vue.renderSlot(g.$slots,"label")]),key:"0"}:void 0]),1040,["prop","label","size","class","style"])),[[Vue.vShow,!e.hidden]]):Vue.createCommentVNode("",!0)}}),Wo={model:{type:Object,default(){return Object.create(null)}},inline:{type:Boolean},inlineColumns:{type:Number},footer:{type:Boolean,default:!0},submitText:{type:String,default:"提交"},resetText:{type:String,default:"重置"},submitMethod:{type:Function},tooltipMessage:{type:[Object,Boolean],default:void 0},enterSubmit:{type:Boolean,default:!0},sticky:{type:Boolean,default:!1},footerAlign:{type:String,default:"left"}},wn=Symbol("formInstanceKey"),bn=Symbol("formModelKey"),Ot=Vue.defineComponent({inheritAttrs:!1,name:"XForm",__name:"Form",props:Wo,emits:["change","submit","reset"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.getCurrentInstance(),u=Vue.ref(),a=Vue.reactive(e.model||{}),i=Vue.ref(!1),s=Vue.computed(()=>({"is-sticky":!!e.sticky})),c=Vue.computed(()=>({[`is-algin-${e.footerAlign}`]:!0}));Vue.provide(wn,l),Vue.provide(bn,a),Vue.watch(()=>e.model,h=>{Object.assign(a,h)},{deep:!0}),Vue.watch(a,()=>{r("change",Vue.toRaw(a))});const d=async()=>await u.value.validate().catch(()=>!1),m=()=>{u.value.clearValidate()},p=async()=>{await d()&&(r("submit",Vue.toRaw(a)),e.submitMethod&&(i.value=!0,await e.submitMethod(Vue.toRaw(a)),i.value=!1))},f=()=>{e.enterSubmit&&p()},V=h=>{var v;(v=u.value)==null||v.resetFields(h),r("reset")};return t({formRef:u,model:a,submit:p,reset:V,validate:d,clearValidate:m}),(h,v)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElForm),Vue.mergeProps({ref_key:"formRef",ref:u,class:["x-form",s.value],inline:e.inline,model:a,onKeyup:Vue.withKeys(f,["enter"]),onSubmit:v[1]||(v[1]=Vue.withModifiers(()=>{},["stop","prevent"]))},h.$attrs),{default:Vue.withCtx(()=>[Vue.renderSlot(h.$slots,"default"),e.footer?(Vue.openBlock(),Vue.createBlock(Vue.unref(Ft),{key:0,editor:"none",class:Vue.normalizeClass(["x-form__footer",c.value]),label:" "},{editor:Vue.withCtx(()=>[Vue.renderSlot(h.$slots,"footer",{},()=>[e.submitText?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElButton),{key:0,loading:i.value,type:"primary",onClick:p},{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(e.submitText),1)]),_:1},8,["loading"])):Vue.createCommentVNode("",!0),e.resetText?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElButton),{key:1,type:"default",onClick:v[0]||(v[0]=()=>V())},{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(e.resetText),1)]),_:1})):Vue.createCommentVNode("",!0),Vue.renderSlot(h.$slots,"action")])]),_:3},8,["class"])):Vue.createCommentVNode("",!0)]),_:3},16,["class","inline","model"]))}}),Jo={modelValue:{type:Boolean,default:!0},size:{type:String},formProps:{type:Object},submit:{type:[Boolean,String],default:"确定"},cancel:{type:[Boolean,String],default:"取消"},model:{type:Object,default(){return Object.create(null)}},rules:{type:Object},submitMethod:{type:Function}},Qo=Vue.defineComponent({name:"XDialogForm",__name:"DialogForm",props:Jo,emits:["update:modelValue","submit","close"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.ref(),u=Vue.ref(),a=Vue.computed(()=>{var p;return(p=u.value)==null?void 0:p.$vtjEl}),i=()=>{var p;(p=l.value)==null||p.submit()},s=()=>{r("update:modelValue",!1),r("close")},c=async p=>{r("submit",p),e.submitMethod?await e.submitMethod(p)&&s():s()},d=Vue.computed(()=>{var p;return(p=e.formProps)!=null&&p.disabled?!1:e.submit}),m=Vue.computed(()=>{var p;return(p=e.formProps)!=null&&p.disabled?!1:e.cancel});return t({$vtjEl:a,formRef:l,dialogRef:u}),(p,f)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Xe),Vue.mergeProps({ref_key:"dialogRef",ref:u,class:"x-dialog-form","model-value":e.modelValue,submit:d.value,cancel:m.value,size:e.size,onSubmit:i,onClose:s,minimizable:!1,maximizable:!1},p.$attrs),Vue.createSlots({default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(Ot),Vue.mergeProps({ref_key:"formRef",ref:l,class:"x-dialog-form__form",footer:!1,"label-width":"80px",size:e.size,model:e.model,rules:e.rules,"submit-method":c},e.formProps),{default:Vue.withCtx(()=>[Vue.renderSlot(p.$slots,"default")]),_:3},16,["size","model","rules"])]),_:2},[p.$slots.extra?{name:"extra",fn:Vue.withCtx(()=>[Vue.renderSlot(p.$slots,"extra")]),key:"0"}:void 0,p.$slots.handle?{name:"handle",fn:Vue.withCtx(()=>[Vue.renderSlot(p.$slots,"handle")]),key:"1"}:void 0,p.$slots.footer?{name:"footer",fn:Vue.withCtx(()=>[Vue.renderSlot(p.$slots,"footer")]),key:"2"}:void 0]),1040,["model-value","submit","cancel","size"]))}}),Zo={items:{type:Array,default(){return[]}},border:{type:Boolean},fit:{type:Boolean},align:{type:String}},ku={class:"x-tabs__label-inner"},Eu={key:0,class:"x-tabs__actions"},er=Vue.defineComponent({name:"XTabs",__name:"Tabs",props:Zo,emits:["actionClick","actionCommand"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.ref(!1),u=Vue.computed(()=>(e.items||[]).filter(p=>!!p.slot).map(p=>p.slot)),a=m=>{const{label:p,name:f,value:V,disabled:h,closable:v,lazy:y}=m;return{label:p,name:V!=null?V:f,disabled:h,closable:v,lazy:y}},i=Vue.computed(()=>({"is-no-border":!e.border,"is-fit":!!e.fit,[`is-align-${e.align}`]:!!e.align})),s=m=>{r("actionClick",m)},c=m=>{r("actionCommand",m)};return Vue.onMounted(()=>{l.value=!0}),Vue.onUnmounted(()=>{l.value=!1}),t({$vtjDynamicSlots:()=>u.value}),(m,p)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElTabs),Vue.mergeProps({class:["x-tabs",i.value]},m.$attrs),{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(e.items,f=>{var V;return Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElTabPane),Vue.mergeProps({key:(V=f.value)!=null?V:f.name},{ref_for:!0},a(f)),{label:Vue.withCtx(()=>[Vue.renderSlot(m.$slots,"label",Vue.mergeProps({ref_for:!0},f),()=>[f.icon?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(De)(f.icon)),{key:0,class:"x-tabs__icon"})):Vue.createCommentVNode("",!0),Vue.createElementVNode("div",ku,[Vue.createTextVNode(Vue.toDisplayString(f.label)+" ",1),f.actions&&[f.name,f.value].includes(m.$attrs.modelValue)?(Vue.openBlock(),Vue.createElementBlock("div",Eu,[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(f.actions,h=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Q),Vue.mergeProps({mode:"icon",type:"primary",size:"small",circle:""},{ref_for:!0},h,{onClick:s,onCommand:c}),null,16))),256))])):Vue.createCommentVNode("",!0)])])]),default:Vue.withCtx(()=>[l.value?Vue.renderSlot(m.$slots,"default",Vue.mergeProps({key:0,ref_for:!0},f),()=>[f.component?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(f.component),Vue.mergeProps({key:0,ref_for:!0},f.props),null,16)):Vue.createCommentVNode("",!0)]):Vue.createCommentVNode("",!0),f.slot?Vue.renderSlot(m.$slots,f.slot,Vue.mergeProps({key:1,ref_for:!0},f)):Vue.createCommentVNode("",!0)]),_:2},1040)}),128))]),_:3},16,["class"]))}}),tr={direction:{type:String,default:"column"},imageSrc:{type:String},imageWidth:{type:[Number,String]},imageHeight:{type:[Number,String]},icon:{type:[String,Object]},title:{type:String},description:{type:String},actions:{type:Array},actionBarProps:{type:Object},split:{type:Boolean},onImageClick:{type:Function},onTitleClick:{type:Function},active:{type:Boolean},hover:{type:Boolean},padding:{type:Boolean}},Su={class:"x-data-item__title"},xu={key:1,class:"x-data-item__section x-data-item__desc"},Bu={key:2,class:"x-data-item__section x-data-item__extra"},Pu={key:3,class:"x-data-item__section x-data-item__actions"},nr=Vue.defineComponent({name:"XDataItem",__name:"DataItem",props:tr,emits:["imageClick","titleClick","actionClick","actionCommand"],setup(o,{emit:t}){const n=o,e=t,r=Vue.computed(()=>{const{imageWidth:d,imageHeight:m}=n;return{width:d?Ve(d):void 0,height:m?Ve(m):void 0}}),l=Vue.computed(()=>({[`is-image-${n.direction}`]:!!n.direction,"is-split":!!n.split,"is-active":!!n.active,"is-hover":!!n.hover})),u=Vue.computed(()=>({size:"small",mode:"text",justify:"flex-end",type:"info",items:n.actions,...n.actionBarProps})),a=()=>e("imageClick"),i=()=>e("titleClick"),s=d=>e("actionClick",d),c=(d,m)=>e("actionCommand",d,m);return(d,m)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{class:Vue.normalizeClass(["x-data-item",l.value]),direction:n.direction,padding:n.padding},{default:Vue.withCtx(()=>[n.imageSrc||d.$slots.image?(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{key:0,class:"x-data-item__img",autoPointer:!0,onClick:n.onImageClick?a:void 0},{default:Vue.withCtx(()=>[Vue.renderSlot(d.$slots,"image",{},()=>[n.imageSrc?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElImage),{key:0,style:Vue.normalizeStyle(r.value),src:n.imageSrc},null,8,["style","src"])):Vue.createCommentVNode("",!0)])]),_:3},8,["onClick"])):Vue.createCommentVNode("",!0),Vue.createVNode(Vue.unref(j),{class:"x-data-item__content",direction:"column",grow:"",shrink:""},{default:Vue.withCtx(()=>[n.title||d.$slots.title?(Vue.openBlock(),Vue.createBlock(Vue.unref(j),{key:0,class:"x-data-item__section x-data-item__wrapper",align:"center",autoPointer:!0,onClick:n.onTitleClick?i:void 0},{default:Vue.withCtx(()=>[n.icon?(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(Vue.unref(De)(n.icon)),{key:0,class:"x-data-item__icon"})):Vue.createCommentVNode("",!0),Vue.createElementVNode("span",Su,[Vue.renderSlot(d.$slots,"title",{},()=>[Vue.createTextVNode(Vue.toDisplayString(n.title),1)])])]),_:3},8,["onClick"])):Vue.createCommentVNode("",!0),n.description||d.$slots.description?(Vue.openBlock(),Vue.createElementBlock("div",xu,[Vue.renderSlot(d.$slots,"description",{},()=>[Vue.createTextVNode(Vue.toDisplayString(n.description),1)])])):Vue.createCommentVNode("",!0),d.$slots.default?(Vue.openBlock(),Vue.createElementBlock("div",Bu,[Vue.renderSlot(d.$slots,"default")])):Vue.createCommentVNode("",!0),n.actions||n.actionBarProps||d.$slots.actions?(Vue.openBlock(),Vue.createElementBlock("div",Pu,[Vue.renderSlot(d.$slots,"actions",{},()=>[Vue.createVNode(Vue.unref(ze),Vue.mergeProps(u.value,{onClick:s,onCommand:c}),null,16)])])):Vue.createCommentVNode("",!0)]),_:3})]),_:3},8,["direction","padding","class"]))}}),or={data:{type:[Object,Function],default(){return[]}},itemHeight:{type:[Number,Function]},width:{type:[String,Number],default:"100%"},height:{type:[String,Number],default:"100%"},pager:{type:[Boolean,Object]},page:{type:Number,default:1},pageSize:{type:Number,default:10},dataKey:{type:String},infiniteScroll:{type:[Boolean,Object]}},Nu={key:0,class:"x-list__loading"},Tu={key:1,class:"x-list__nomore"},Du={key:0,class:"x-list__loading"},zu={key:1,class:"x-list__nomore"},Iu={key:1,class:"x-list__pager"},rr=Vue.defineComponent({name:"XList",__name:"List",props:or,emits:["load"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.reactive({page:e.page,pageSize:e.pageSize});Vue.watchEffect(()=>{const{page:_,pageSize:C}=e;Object.assign(l,{page:_,pageSize:C})});const{data:u,loading:a}=no(e.data,{list:[],total:0},l),i=Vue.computed(()=>Math.ceil((u.value.total||0)/l.pageSize)),s=Vue.ref(!1),c=Vue.ref(),d=Vue.computed(()=>({width:Ve(e.width),height:Ve(e.height)})),m=Vue.computed(()=>({"is-virtual":!!e.itemHeight})),p=Vue.computed(()=>{const _={};for(let[C,g]of Object.entries(Ut(e.infiniteScroll)))_[`infinite-scroll-${C}`]=g;return(a.value||s.value)&&(_["infinite-scroll-disabled"]=!0),_}),f=Vue.computed(()=>Ut(e.pager,{})),{list:V,containerProps:h,wrapperProps:v,scrollTo:y}=VueUse.useVirtualList(Vue.computed(()=>u.value.list),{itemHeight:e.itemHeight||0}),b=()=>{!e.infiniteScroll||s.value||(l.page<i.value?(++l.page,r("load",l)):s.value=!0)},w=_=>e.dataKey?_[e.dataKey]:void 0;return t({list:V,scrollTo:y,wrapperRef:c,loading:a,state:l,pageCount:i,nomore:s,data:u,getKey:w}),(_,C)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:Vue.normalizeClass(["x-list",m.value]),style:Vue.normalizeStyle(d.value)},[!Vue.unref(u).total&&!Vue.unref(a)?Vue.renderSlot(_.$slots,"empty",{key:0},()=>[Vue.createVNode(Vue.unref(ElementPlus.ElEmpty))]):Vue.createCommentVNode("",!0),Vue.createElementVNode("div",Vue.mergeProps({class:"x-list__content"},Vue.unref(h)),[e.itemHeight?Vue.withDirectives((Vue.openBlock(),Vue.createElementBlock("div",Vue.mergeProps({key:0,class:"x-list__wrapper",ref_key:"wrapperRef",ref:c},{...Vue.unref(v),...p.value}),[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(Vue.unref(V),(g,x)=>Vue.renderSlot(_.$slots,"default",Vue.mergeProps({key:w(g.data),ref_for:!0},{item:g.data,index:x}),()=>[Vue.createElementVNode("div",null,Vue.toDisplayString(g),1)])),128)),Vue.unref(a)?(Vue.openBlock(),Vue.createElementBlock("div",Nu,[Vue.renderSlot(_.$slots,"loading",{},()=>[C[2]||(C[2]=Vue.createTextVNode(" 正在加载数据... ",-1))])])):Vue.createCommentVNode("",!0),s.value?(Vue.openBlock(),Vue.createElementBlock("div",Tu,[Vue.renderSlot(_.$slots,"nomore",{},()=>[C[3]||(C[3]=Vue.createTextVNode(" 没有更多数据 ",-1))])])):Vue.createCommentVNode("",!0)],16)),[[Vue.unref(ElementPlus.ElInfiniteScroll),b]]):Vue.withDirectives((Vue.openBlock(),Vue.createElementBlock("div",Vue.mergeProps({key:1,class:"x-list__wrapper"},p.value),[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(Vue.unref(u).list,(g,x)=>Vue.renderSlot(_.$slots,"default",Vue.mergeProps({key:w(g),ref_for:!0},{item:g,index:x}),()=>[Vue.createElementVNode("div",null,Vue.toDisplayString(g),1)])),128)),Vue.unref(a)?(Vue.openBlock(),Vue.createElementBlock("div",Du,[Vue.renderSlot(_.$slots,"loading",{},()=>[C[4]||(C[4]=Vue.createTextVNode(" 正在加载数据... ",-1))])])):Vue.createCommentVNode("",!0),s.value?(Vue.openBlock(),Vue.createElementBlock("div",zu,[Vue.renderSlot(_.$slots,"nomore",{},()=>[C[5]||(C[5]=Vue.createTextVNode(" 没有更多数据 ",-1))])])):Vue.createCommentVNode("",!0)],16)),[[Vue.unref(ElementPlus.ElInfiniteScroll),b]])],16),e.pager?(Vue.openBlock(),Vue.createElementBlock("div",Iu,[Vue.createVNode(Vue.unref(ElementPlus.ElPagination),Vue.mergeProps({small:"",background:"",layout:"prev, pager, next",total:Vue.unref(u).total,"default-page-size":e.pageSize,"default-current-page":e.page,"current-page":l.page,"onUpdate:currentPage":C[0]||(C[0]=g=>l.page=g),"page-size":l.pageSize,"onUpdate:pageSize":C[1]||(C[1]=g=>l.pageSize=g)},f.value),null,16,["total","default-page-size","default-current-page","current-page","page-size"])])):Vue.createCommentVNode("",!0)],6))}}),lr={name:{type:String,default:"VTJ.PRO"},tagline:{type:String,default:"基于 Vue3 + TypeScript 快速打造高生产力的低代码研发平台"},actionText:{type:String,default:"开始设计"},actionLink:{type:String,default:"/@vtj/pro/"}},Mu={class:"x-startup"},Au={class:"x-startup__wrapper"},Ru={class:"x-startup__name"},$u={class:"clip"},Fu={class:"x-startup__tagline"},Ou={class:"x-startup__actions"},ar=Vue.defineComponent({__name:"Startup",props:lr,setup(o){const t=o,n=()=>{if(typeof window<"u"){let r=(window.__VTJ_LINK__||{}).href||window.location.pathname+"__vtj__/#/";window.location.href=r}};return(e,r)=>(Vue.openBlock(),Vue.createElementBlock("div",Mu,[Vue.createElementVNode("div",Au,[Vue.createElementVNode("div",Ru,[Vue.createElementVNode("span",$u,Vue.toDisplayString(t.name),1)]),Vue.createElementVNode("div",Fu,Vue.toDisplayString(t.tagline),1),Vue.createElementVNode("div",Ou,[Vue.createVNode(Vue.unref(ElementPlus.ElButton),{type:"primary",size:"large",round:"",icon:Vue.unref(VtjIcons.EditPen),onClick:n},{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(t.actionText),1)]),_:1},8,["icon"])])])]))}}),ur={stringProp:{type:String},booleanProp:{type:Boolean},numberProp:{type:Number},selectProp:{type:String},objectProp:{type:Object},arrayProp:{type:Array},iconProp:{type:String},colorProp:{type:String},modelValue:{type:String},syncProp:{type:String}},ju={class:"x-test__slot"},Uu={class:"x-test__slot"},ir=Vue.defineComponent({name:"XTest",__name:"Test",props:ur,emits:["click","submit","change","update:modelValue","update:syncProp"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.computed(()=>Object.entries(e)),u=()=>["dSlot_1","dSlot_2"],a=Vue.computed({get(){return e.modelValue},set(f){r("update:modelValue",f)}}),i=Vue.computed({get(){return e.syncProp},set(f){r("update:syncProp",f)}}),s=Vue.ref("default inner data"),c=()=>{ElementPlus.ElMessage.info({message:"触发click事件"}),r("click",e)},d=()=>{ElementPlus.ElMessage.info({message:"触发submit事件"}),r("submit",e)},m=f=>{s.value=f,r("change",s.value)},p=()=>{m("user click change:"+Date.now())};return t({click:c,submit:d,data:s,change:m,$vtjDynamicSlots:u}),(f,V)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(Le),{class:"x-test",header:"VTJ 低代码测试组件",border:""},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElDivider),null,{default:Vue.withCtx(()=>V[2]||(V[2]=[Vue.createTextVNode("内部数据",-1)])),_:1,__:[2]}),Vue.createVNode(Vue.unref(j),null,{default:Vue.withCtx(()=>[Vue.createTextVNode(Vue.toDisplayString(s.value),1)]),_:1}),Vue.createVNode(Vue.unref(ElementPlus.ElDivider),null,{default:Vue.withCtx(()=>V[3]||(V[3]=[Vue.createTextVNode("属性",-1)])),_:1,__:[3]}),Vue.createVNode(Vue.unref(ElementPlus.ElDescriptions),{column:3,border:""},{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(l.value,([h,v])=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElDescriptionsItem),{label:h},{default:Vue.withCtx(()=>[Vue.createElementVNode("pre",null,Vue.toDisplayString(JSON.stringify(v,null,2)),1)]),_:2},1032,["label"]))),256))]),_:1}),Vue.createVNode(Vue.unref(ElementPlus.ElDivider),null,{default:Vue.withCtx(()=>V[4]||(V[4]=[Vue.createTextVNode("插槽",-1)])),_:1,__:[4]}),Vue.createVNode(Vue.unref(j),{justify:"space-around",flex:"",gap:""},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(Le),{header:"default",grow:"",card:""},{default:Vue.withCtx(()=>[Vue.createElementVNode("div",ju,[Vue.renderSlot(f.$slots,"default",{props:e,data:s.value},()=>[Vue.createElementVNode("pre",null,Vue.toDisplayString(JSON.stringify({props:e,data:s.value},null,2)),1)])])]),_:3}),Vue.createVNode(Vue.unref(Le),{header:"extra",grow:"",card:""},{default:Vue.withCtx(()=>[Vue.createElementVNode("div",Uu,[Vue.renderSlot(f.$slots,"extra",{props:e,data:s.value},()=>[Vue.createElementVNode("pre",null,Vue.toDisplayString(JSON.stringify({props:e,data:s.value},null,2)),1)])])]),_:3})]),_:3}),Vue.createVNode(Vue.unref(ElementPlus.ElDivider),null,{default:Vue.withCtx(()=>V[5]||(V[5]=[Vue.createTextVNode("动态插槽",-1)])),_:1,__:[5]}),Vue.createVNode(Vue.unref(j),null,{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(u(),h=>Vue.renderSlot(f.$slots,h)),256))]),_:3}),Vue.createVNode(Vue.unref(ElementPlus.ElDivider),null,{default:Vue.withCtx(()=>V[6]||(V[6]=[Vue.createTextVNode("双向绑定",-1)])),_:1,__:[6]}),Vue.createVNode(Vue.unref(j),null,{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElDescriptions),{column:2,border:""},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElDescriptionsItem),{label:"modelValue"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElInput),{modelValue:a.value,"onUpdate:modelValue":V[0]||(V[0]=h=>a.value=h)},null,8,["modelValue"])]),_:1}),Vue.createVNode(Vue.unref(ElementPlus.ElDescriptionsItem),{label:"syncProp"},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElInput),{modelValue:i.value,"onUpdate:modelValue":V[1]||(V[1]=h=>i.value=h)},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),Vue.createVNode(Vue.unref(ElementPlus.ElDivider),null,{default:Vue.withCtx(()=>V[7]||(V[7]=[Vue.createTextVNode("事件和方法",-1)])),_:1,__:[7]}),Vue.createVNode(Vue.unref(j),null,{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElButton),{onClick:c},{default:Vue.withCtx(()=>V[8]||(V[8]=[Vue.createTextVNode("click",-1)])),_:1,__:[8]}),Vue.createVNode(Vue.unref(ElementPlus.ElButton),{onClick:d},{default:Vue.withCtx(()=>V[9]||(V[9]=[Vue.createTextVNode("submit",-1)])),_:1,__:[9]}),Vue.createVNode(Vue.unref(ElementPlus.ElButton),{onClick:p},{default:Vue.withCtx(()=>V[10]||(V[10]=[Vue.createTextVNode("change",-1)])),_:1,__:[10]})]),_:1})]),_:3}))}}),sr={units:{type:[String,Object,Array]},unit:{type:String},appendWidth:{type:Number,default:100},withUnit:{type:Boolean,default:!0},format:{type:String},modelValue:{type:[String,Number]},selectProps:{type:Object}},Lu={key:0,class:"x-input-unit__unit"},cr=Vue.defineComponent({name:"XInputUnit",__name:"InputUnit",props:sr,emits:["update:modelValue","update:unit","change","unit-change"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.computed(()=>VtjUtils.toArray(e.units).map(V=>typeof V=="string"?{label:V,value:V}:V)),u=Vue.computed(()=>{const f=l.value.map(V=>V.value);return e.unit&&f.push(e.unit),VtjUtils.dedupArray(f)}),a=Vue.ref(),i=Vue.ref(),s=f=>e.format&&f?VtjUtils.numberFormat(Number(f)||0,e.format):f,c=f=>{if(!e.withUnit||typeof f!="string")return{value:s(f),unit:e.unit};for(const V of u.value){const h=new RegExp(`${V}$`);if(h.test(f))return{value:s(f.toString().replace(h,"")),unit:V}}return{value:s(f),unit:e.unit}},d=()=>{const f=s(i.value),V=a.value;return e.withUnit&&f&&V?`${f}${V}`:f};Vue.watch(()=>e.modelValue,f=>{const{value:V,unit:h}=c(f);a.value=h,i.value=V},{immediate:!0}),Vue.watch(i,f=>{r("change",f)});const m=async()=>{const{value:f,unit:V}=c(d());i.value=f,a.value=V,r("update:modelValue",d())},p=()=>{r("unit-change",a.value),m()};return t({parser:c,formatValue:s}),(f,V)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElInput),{modelValue:i.value,"onUpdate:modelValue":V[1]||(V[1]=h=>i.value=h),modelModifiers:{trim:!0},class:"x-input-unit",onChange:m},Vue.createSlots({_:2},[l.value.length?{name:"append",fn:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElSelect),Vue.mergeProps({class:"x-input-unit__unit",modelValue:a.value,"onUpdate:modelValue":V[0]||(V[0]=h=>a.value=h),size:f.$attrs.size,style:{width:`${e.appendWidth}px`}},f.$props.selectProps,{onChange:p}),{default:Vue.withCtx(()=>[(Vue.openBlock(!0),Vue.createElementBlock(Vue.Fragment,null,Vue.renderList(l.value,h=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElOption),{key:h.value,label:h.label,value:h.value},null,8,["label","value"]))),128))]),_:1},16,["modelValue","size","style"])]),key:"0"}:{name:"suffix",fn:Vue.withCtx(()=>[a.value?(Vue.openBlock(),Vue.createElementBlock("div",Lu,[Vue.createElementVNode("span",null,Vue.toDisplayString(a.value),1)])):Vue.createCommentVNode("",!0)]),key:"1"}]),1032,["modelValue"]))}}),Hu=["src"],dr=Vue.defineComponent({name:"XCaptcha",__name:"Captcha",props:Vue.mergeModels({src:{},maxlength:{default:4},placeholder:{default:"请输入图形验证码"},validate:{}},{modelValue:{type:String},modelModifiers:{}}),emits:["update:modelValue"],setup(o){const t=o,n=Vue.useModel(o,"modelValue"),e=Vue.ref(""),r=Vue.ref(),l=async()=>{e.value=t.src?await t.src():"",r.value=void 0};return Vue.watch(()=>t.src,l,{immediate:!0}),Vue.watch(n,async(u="")=>{u.length===t.maxlength?t.validate&&(r.value=await t.validate(u)):r.value=void 0}),(u,a)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElInput),{class:"x-captcha",placeholder:t.placeholder,maxlength:t.maxlength,modelValue:n.value,"onUpdate:modelValue":a[0]||(a[0]=i=>n.value=i),clearable:""},{prepend:Vue.withCtx(()=>[Vue.createElementVNode("img",{src:e.value,onClick:l},null,8,Hu)]),suffix:Vue.withCtx(()=>[r.value===!0?(Vue.openBlock(),Vue.createBlock(Vue.unref(de),{key:0,class:"x-captcha--success",icon:Vue.unref(VtjIcons.SuccessFilled),color:"green"},null,8,["icon"])):Vue.createCommentVNode("",!0),r.value===!1?(Vue.openBlock(),Vue.createBlock(Vue.unref(de),{key:1,class:"x-captcha--error",icon:Vue.unref(VtjIcons.CircleCloseFilled)},null,8,["icon"])):Vue.createCommentVNode("",!0)]),_:1},8,["placeholder","maxlength","modelValue"]))}});function Xu(o){return o&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o}var Ze={},Cn,fr;function qu(){return fr||(fr=1,Cn=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then}),Cn}var _n={},Ne={},pr;function Oe(){if(pr)return Ne;pr=1;let o;const t=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];return Ne.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return e*4+17},Ne.getSymbolTotalCodewords=function(e){return t[e]},Ne.getBCHDigit=function(n){let e=0;for(;n!==0;)e++,n>>>=1;return e},Ne.setToSJISFunction=function(e){if(typeof e!="function")throw new Error('"toSJISFunc" is not a valid function.');o=e},Ne.isKanjiModeEnabled=function(){return typeof o<"u"},Ne.toSJIS=function(e){return o(e)},Ne}var kn={},mr;function En(){return mr||(mr=1,function(o){o.L={bit:1},o.M={bit:0},o.Q={bit:3},o.H={bit:2};function t(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return o.L;case"m":case"medium":return o.M;case"q":case"quartile":return o.Q;case"h":case"high":return o.H;default:throw new Error("Unknown EC Level: "+n)}}o.isValid=function(e){return e&&typeof e.bit<"u"&&e.bit>=0&&e.bit<4},o.from=function(e,r){if(o.isValid(e))return e;try{return t(e)}catch(l){return r}}}(kn)),kn}var Sn,Vr;function Ku(){if(Vr)return Sn;Vr=1;function o(){this.buffer=[],this.length=0}return o.prototype={get:function(t){const n=Math.floor(t/8);return(this.buffer[n]>>>7-t%8&1)===1},put:function(t,n){for(let e=0;e<n;e++)this.putBit((t>>>n-e-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(t){const n=Math.floor(this.length/8);this.buffer.length<=n&&this.buffer.push(0),t&&(this.buffer[n]|=128>>>this.length%8),this.length++}},Sn=o,Sn}var xn,hr;function Yu(){if(hr)return xn;hr=1;function o(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}return o.prototype.set=function(t,n,e,r){const l=t*this.size+n;this.data[l]=e,r&&(this.reservedBit[l]=!0)},o.prototype.get=function(t,n){return this.data[t*this.size+n]},o.prototype.xor=function(t,n,e){this.data[t*this.size+n]^=e},o.prototype.isReserved=function(t,n){return this.reservedBit[t*this.size+n]},xn=o,xn}var Bn={},gr;function Gu(){return gr||(gr=1,function(o){const t=Oe().getSymbolSize;o.getRowColCoords=function(e){if(e===1)return[];const r=Math.floor(e/7)+2,l=t(e),u=l===145?26:Math.ceil((l-13)/(2*r-2))*2,a=[l-7];for(let i=1;i<r-1;i++)a[i]=a[i-1]-u;return a.push(6),a.reverse()},o.getPositions=function(e){const r=[],l=o.getRowColCoords(e),u=l.length;for(let a=0;a<u;a++)for(let i=0;i<u;i++)a===0&&i===0||a===0&&i===u-1||a===u-1&&i===0||r.push([l[a],l[i]]);return r}}(Bn)),Bn}var Pn={},vr;function Wu(){if(vr)return Pn;vr=1;const o=Oe().getSymbolSize,t=7;return Pn.getPositions=function(e){const r=o(e);return[[0,0],[r-t,0],[0,r-t]]},Pn}var Nn={},yr;function Ju(){return yr||(yr=1,function(o){o.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const t={N1:3,N2:3,N3:40,N4:10};o.isValid=function(r){return r!=null&&r!==""&&!isNaN(r)&&r>=0&&r<=7},o.from=function(r){return o.isValid(r)?parseInt(r,10):void 0},o.getPenaltyN1=function(r){const l=r.size;let u=0,a=0,i=0,s=null,c=null;for(let d=0;d<l;d++){a=i=0,s=c=null;for(let m=0;m<l;m++){let p=r.get(d,m);p===s?a++:(a>=5&&(u+=t.N1+(a-5)),s=p,a=1),p=r.get(m,d),p===c?i++:(i>=5&&(u+=t.N1+(i-5)),c=p,i=1)}a>=5&&(u+=t.N1+(a-5)),i>=5&&(u+=t.N1+(i-5))}return u},o.getPenaltyN2=function(r){const l=r.size;let u=0;for(let a=0;a<l-1;a++)for(let i=0;i<l-1;i++){const s=r.get(a,i)+r.get(a,i+1)+r.get(a+1,i)+r.get(a+1,i+1);(s===4||s===0)&&u++}return u*t.N2},o.getPenaltyN3=function(r){const l=r.size;let u=0,a=0,i=0;for(let s=0;s<l;s++){a=i=0;for(let c=0;c<l;c++)a=a<<1&2047|r.get(s,c),c>=10&&(a===1488||a===93)&&u++,i=i<<1&2047|r.get(c,s),c>=10&&(i===1488||i===93)&&u++}return u*t.N3},o.getPenaltyN4=function(r){let l=0;const u=r.data.length;for(let i=0;i<u;i++)l+=r.data[i];return Math.abs(Math.ceil(l*100/u/5)-10)*t.N4};function n(e,r,l){switch(e){case o.Patterns.PATTERN000:return(r+l)%2===0;case o.Patterns.PATTERN001:return r%2===0;case o.Patterns.PATTERN010:return l%3===0;case o.Patterns.PATTERN011:return(r+l)%3===0;case o.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(l/3))%2===0;case o.Patterns.PATTERN101:return r*l%2+r*l%3===0;case o.Patterns.PATTERN110:return(r*l%2+r*l%3)%2===0;case o.Patterns.PATTERN111:return(r*l%3+(r+l)%2)%2===0;default:throw new Error("bad maskPattern:"+e)}}o.applyMask=function(r,l){const u=l.size;for(let a=0;a<u;a++)for(let i=0;i<u;i++)l.isReserved(i,a)||l.xor(i,a,n(r,i,a))},o.getBestMask=function(r,l){const u=Object.keys(o.Patterns).length;let a=0,i=1/0;for(let s=0;s<u;s++){l(s),o.applyMask(s,r);const c=o.getPenaltyN1(r)+o.getPenaltyN2(r)+o.getPenaltyN3(r)+o.getPenaltyN4(r);o.applyMask(s,r),c<i&&(i=c,a=s)}return a}}(Nn)),Nn}var jt={},wr;function br(){if(wr)return jt;wr=1;const o=En(),t=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],n=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];return jt.getBlocksCount=function(r,l){switch(l){case o.L:return t[(r-1)*4+0];case o.M:return t[(r-1)*4+1];case o.Q:return t[(r-1)*4+2];case o.H:return t[(r-1)*4+3];default:return}},jt.getTotalCodewordsCount=function(r,l){switch(l){case o.L:return n[(r-1)*4+0];case o.M:return n[(r-1)*4+1];case o.Q:return n[(r-1)*4+2];case o.H:return n[(r-1)*4+3];default:return}},jt}var Tn={},ft={},Cr;function Qu(){if(Cr)return ft;Cr=1;const o=new Uint8Array(512),t=new Uint8Array(256);return function(){let e=1;for(let r=0;r<255;r++)o[r]=e,t[e]=r,e<<=1,e&256&&(e^=285);for(let r=255;r<512;r++)o[r]=o[r-255]}(),ft.log=function(e){if(e<1)throw new Error("log("+e+")");return t[e]},ft.exp=function(e){return o[e]},ft.mul=function(e,r){return e===0||r===0?0:o[t[e]+t[r]]},ft}var _r;function Zu(){return _r||(_r=1,function(o){const t=Qu();o.mul=function(e,r){const l=new Uint8Array(e.length+r.length-1);for(let u=0;u<e.length;u++)for(let a=0;a<r.length;a++)l[u+a]^=t.mul(e[u],r[a]);return l},o.mod=function(e,r){let l=new Uint8Array(e);for(;l.length-r.length>=0;){const u=l[0];for(let i=0;i<r.length;i++)l[i]^=t.mul(r[i],u);let a=0;for(;a<l.length&&l[a]===0;)a++;l=l.slice(a)}return l},o.generateECPolynomial=function(e){let r=new Uint8Array([1]);for(let l=0;l<e;l++)r=o.mul(r,new Uint8Array([1,t.exp(l)]));return r}}(Tn)),Tn}var Dn,kr;function ei(){if(kr)return Dn;kr=1;const o=Zu();function t(n){this.genPoly=void 0,this.degree=n,this.degree&&this.initialize(this.degree)}return t.prototype.initialize=function(e){this.degree=e,this.genPoly=o.generateECPolynomial(this.degree)},t.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const r=new Uint8Array(e.length+this.degree);r.set(e);const l=o.mod(r,this.genPoly),u=this.degree-l.length;if(u>0){const a=new Uint8Array(this.degree);return a.set(l,u),a}return l},Dn=t,Dn}var zn={},In={},Mn={},Er;function Sr(){return Er||(Er=1,Mn.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}),Mn}var ye={},xr;function Br(){if(xr)return ye;xr=1;const o="[0-9]+",t="[A-Z $%*+\\-./:]+";let n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";n=n.replace(/u/g,"\\u");const e="(?:(?![A-Z0-9 $%*+\\-./:]|"+n+`)(?:.|[\r
]))+`;ye.KANJI=new RegExp(n,"g"),ye.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),ye.BYTE=new RegExp(e,"g"),ye.NUMERIC=new RegExp(o,"g"),ye.ALPHANUMERIC=new RegExp(t,"g");const r=new RegExp("^"+n+"$"),l=new RegExp("^"+o+"$"),u=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");return ye.testKanji=function(i){return r.test(i)},ye.testNumeric=function(i){return l.test(i)},ye.testAlphanumeric=function(i){return u.test(i)},ye}var Pr;function je(){return Pr||(Pr=1,function(o){const t=Sr(),n=Br();o.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},o.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},o.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},o.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},o.MIXED={bit:-1},o.getCharCountIndicator=function(l,u){if(!l.ccBits)throw new Error("Invalid mode: "+l);if(!t.isValid(u))throw new Error("Invalid version: "+u);return u>=1&&u<10?l.ccBits[0]:u<27?l.ccBits[1]:l.ccBits[2]},o.getBestModeForData=function(l){return n.testNumeric(l)?o.NUMERIC:n.testAlphanumeric(l)?o.ALPHANUMERIC:n.testKanji(l)?o.KANJI:o.BYTE},o.toString=function(l){if(l&&l.id)return l.id;throw new Error("Invalid mode")},o.isValid=function(l){return l&&l.bit&&l.ccBits};function e(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"numeric":return o.NUMERIC;case"alphanumeric":return o.ALPHANUMERIC;case"kanji":return o.KANJI;case"byte":return o.BYTE;default:throw new Error("Unknown mode: "+r)}}o.from=function(l,u){if(o.isValid(l))return l;try{return e(l)}catch(a){return u}}}(In)),In}var Nr;function ti(){return Nr||(Nr=1,function(o){const t=Oe(),n=br(),e=En(),r=je(),l=Sr(),u=7973,a=t.getBCHDigit(u);function i(m,p,f){for(let V=1;V<=40;V++)if(p<=o.getCapacity(V,f,m))return V}function s(m,p){return r.getCharCountIndicator(m,p)+4}function c(m,p){let f=0;return m.forEach(function(V){const h=s(V.mode,p);f+=h+V.getBitsLength()}),f}function d(m,p){for(let f=1;f<=40;f++)if(c(m,f)<=o.getCapacity(f,p,r.MIXED))return f}o.from=function(p,f){return l.isValid(p)?parseInt(p,10):f},o.getCapacity=function(p,f,V){if(!l.isValid(p))throw new Error("Invalid QR Code version");typeof V>"u"&&(V=r.BYTE);const h=t.getSymbolTotalCodewords(p),v=n.getTotalCodewordsCount(p,f),y=(h-v)*8;if(V===r.MIXED)return y;const b=y-s(V,p);switch(V){case r.NUMERIC:return Math.floor(b/10*3);case r.ALPHANUMERIC:return Math.floor(b/11*2);case r.KANJI:return Math.floor(b/13);case r.BYTE:default:return Math.floor(b/8)}},o.getBestVersionForData=function(p,f){let V;const h=e.from(f,e.M);if(Array.isArray(p)){if(p.length>1)return d(p,h);if(p.length===0)return 1;V=p[0]}else V=p;return i(V.mode,V.getLength(),h)},o.getEncodedBits=function(p){if(!l.isValid(p)||p<7)throw new Error("Invalid QR Code version");let f=p<<12;for(;t.getBCHDigit(f)-a>=0;)f^=u<<t.getBCHDigit(f)-a;return p<<12|f}}(zn)),zn}var An={},Tr;function ni(){if(Tr)return An;Tr=1;const o=Oe(),t=1335,n=21522,e=o.getBCHDigit(t);return An.getEncodedBits=function(l,u){const a=l.bit<<3|u;let i=a<<10;for(;o.getBCHDigit(i)-e>=0;)i^=t<<o.getBCHDigit(i)-e;return(a<<10|i)^n},An}var Rn={},$n,Dr;function oi(){if(Dr)return $n;Dr=1;const o=je();function t(n){this.mode=o.NUMERIC,this.data=n.toString()}return t.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(e){let r,l,u;for(r=0;r+3<=this.data.length;r+=3)l=this.data.substr(r,3),u=parseInt(l,10),e.put(u,10);const a=this.data.length-r;a>0&&(l=this.data.substr(r),u=parseInt(l,10),e.put(u,a*3+1))},$n=t,$n}var Fn,zr;function ri(){if(zr)return Fn;zr=1;const o=je(),t=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function n(e){this.mode=o.ALPHANUMERIC,this.data=e}return n.getBitsLength=function(r){return 11*Math.floor(r/2)+6*(r%2)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(r){let l;for(l=0;l+2<=this.data.length;l+=2){let u=t.indexOf(this.data[l])*45;u+=t.indexOf(this.data[l+1]),r.put(u,11)}this.data.length%2&&r.put(t.indexOf(this.data[l]),6)},Fn=n,Fn}var On,Ir;function li(){if(Ir)return On;Ir=1;const o=je();function t(n){this.mode=o.BYTE,typeof n=="string"?this.data=new TextEncoder().encode(n):this.data=new Uint8Array(n)}return t.getBitsLength=function(e){return e*8},t.prototype.getLength=function(){return this.data.length},t.prototype.getBitsLength=function(){return t.getBitsLength(this.data.length)},t.prototype.write=function(n){for(let e=0,r=this.data.length;e<r;e++)n.put(this.data[e],8)},On=t,On}var jn,Mr;function ai(){if(Mr)return jn;Mr=1;const o=je(),t=Oe();function n(e){this.mode=o.KANJI,this.data=e}return n.getBitsLength=function(r){return r*13},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(e){let r;for(r=0;r<this.data.length;r++){let l=t.toSJIS(this.data[r]);if(l>=33088&&l<=40956)l-=33088;else if(l>=57408&&l<=60351)l-=49472;else throw new Error("Invalid SJIS character: "+this.data[r]+`
Make sure your charset is UTF-8`);l=(l>>>8&255)*192+(l&255),e.put(l,13)}},jn=n,jn}var Un={exports:{}},Ar;function ui(){return Ar||(Ar=1,function(o){var t={single_source_shortest_paths:function(n,e,r){var l={},u={};u[e]=0;var a=t.PriorityQueue.make();a.push(e,0);for(var i,s,c,d,m,p,f,V,h;!a.empty();){i=a.pop(),s=i.value,d=i.cost,m=n[s]||{};for(c in m)m.hasOwnProperty(c)&&(p=m[c],f=d+p,V=u[c],h=typeof u[c]>"u",(h||V>f)&&(u[c]=f,a.push(c,f),l[c]=s))}if(typeof r<"u"&&typeof u[r]>"u"){var v=["Could not find a path from ",e," to ",r,"."].join("");throw new Error(v)}return l},extract_shortest_path_from_predecessor_list:function(n,e){for(var r=[],l=e;l;)r.push(l),n[l],l=n[l];return r.reverse(),r},find_path:function(n,e,r){var l=t.single_source_shortest_paths(n,e,r);return t.extract_shortest_path_from_predecessor_list(l,r)},PriorityQueue:{make:function(n){var e=t.PriorityQueue,r={},l;n=n||{};for(l in e)e.hasOwnProperty(l)&&(r[l]=e[l]);return r.queue=[],r.sorter=n.sorter||e.default_sorter,r},default_sorter:function(n,e){return n.cost-e.cost},push:function(n,e){var r={value:n,cost:e};this.queue.push(r),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};o.exports=t}(Un)),Un.exports}var Rr;function ii(){return Rr||(Rr=1,function(o){const t=je(),n=oi(),e=ri(),r=li(),l=ai(),u=Br(),a=Oe(),i=ui();function s(v){return unescape(encodeURIComponent(v)).length}function c(v,y,b){const w=[];let _;for(;(_=v.exec(b))!==null;)w.push({data:_[0],index:_.index,mode:y,length:_[0].length});return w}function d(v){const y=c(u.NUMERIC,t.NUMERIC,v),b=c(u.ALPHANUMERIC,t.ALPHANUMERIC,v);let w,_;return a.isKanjiModeEnabled()?(w=c(u.BYTE,t.BYTE,v),_=c(u.KANJI,t.KANJI,v)):(w=c(u.BYTE_KANJI,t.BYTE,v),_=[]),y.concat(b,w,_).sort(function(g,x){return g.index-x.index}).map(function(g){return{data:g.data,mode:g.mode,length:g.length}})}function m(v,y){switch(y){case t.NUMERIC:return n.getBitsLength(v);case t.ALPHANUMERIC:return e.getBitsLength(v);case t.KANJI:return l.getBitsLength(v);case t.BYTE:return r.getBitsLength(v)}}function p(v){return v.reduce(function(y,b){const w=y.length-1>=0?y[y.length-1]:null;return w&&w.mode===b.mode?(y[y.length-1].data+=b.data,y):(y.push(b),y)},[])}function f(v){const y=[];for(let b=0;b<v.length;b++){const w=v[b];switch(w.mode){case t.NUMERIC:y.push([w,{data:w.data,mode:t.ALPHANUMERIC,length:w.length},{data:w.data,mode:t.BYTE,length:w.length}]);break;case t.ALPHANUMERIC:y.push([w,{data:w.data,mode:t.BYTE,length:w.length}]);break;case t.KANJI:y.push([w,{data:w.data,mode:t.BYTE,length:s(w.data)}]);break;case t.BYTE:y.push([{data:w.data,mode:t.BYTE,length:s(w.data)}])}}return y}function V(v,y){const b={},w={start:{}};let _=["start"];for(let C=0;C<v.length;C++){const g=v[C],x=[];for(let S=0;S<g.length;S++){const I=g[S],z=""+C+S;x.push(z),b[z]={node:I,lastCount:0},w[z]={};for(let N=0;N<_.length;N++){const T=_[N];b[T]&&b[T].node.mode===I.mode?(w[T][z]=m(b[T].lastCount+I.length,I.mode)-m(b[T].lastCount,I.mode),b[T].lastCount+=I.length):(b[T]&&(b[T].lastCount=I.length),w[T][z]=m(I.length,I.mode)+4+t.getCharCountIndicator(I.mode,y))}}_=x}for(let C=0;C<_.length;C++)w[_[C]].end=0;return{map:w,table:b}}function h(v,y){let b;const w=t.getBestModeForData(v);if(b=t.from(y,w),b!==t.BYTE&&b.bit<w.bit)throw new Error('"'+v+'" cannot be encoded with mode '+t.toString(b)+`.
 Suggested mode is: `+t.toString(w));switch(b===t.KANJI&&!a.isKanjiModeEnabled()&&(b=t.BYTE),b){case t.NUMERIC:return new n(v);case t.ALPHANUMERIC:return new e(v);case t.KANJI:return new l(v);case t.BYTE:return new r(v)}}o.fromArray=function(y){return y.reduce(function(b,w){return typeof w=="string"?b.push(h(w,null)):w.data&&b.push(h(w.data,w.mode)),b},[])},o.fromString=function(y,b){const w=d(y,a.isKanjiModeEnabled()),_=f(w),C=V(_,b),g=i.find_path(C.map,"start","end"),x=[];for(let S=1;S<g.length-1;S++)x.push(C.table[g[S]].node);return o.fromArray(p(x))},o.rawSplit=function(y){return o.fromArray(d(y,a.isKanjiModeEnabled()))}}(Rn)),Rn}var $r;function si(){if($r)return _n;$r=1;const o=Oe(),t=En(),n=Ku(),e=Yu(),r=Gu(),l=Wu(),u=Ju(),a=br(),i=ei(),s=ti(),c=ni(),d=je(),m=ii();function p(C,g){const x=C.size,S=l.getPositions(g);for(let I=0;I<S.length;I++){const z=S[I][0],N=S[I][1];for(let T=-1;T<=7;T++)if(!(z+T<=-1||x<=z+T))for(let A=-1;A<=7;A++)N+A<=-1||x<=N+A||(T>=0&&T<=6&&(A===0||A===6)||A>=0&&A<=6&&(T===0||T===6)||T>=2&&T<=4&&A>=2&&A<=4?C.set(z+T,N+A,!0,!0):C.set(z+T,N+A,!1,!0))}}function f(C){const g=C.size;for(let x=8;x<g-8;x++){const S=x%2===0;C.set(x,6,S,!0),C.set(6,x,S,!0)}}function V(C,g){const x=r.getPositions(g);for(let S=0;S<x.length;S++){const I=x[S][0],z=x[S][1];for(let N=-2;N<=2;N++)for(let T=-2;T<=2;T++)N===-2||N===2||T===-2||T===2||N===0&&T===0?C.set(I+N,z+T,!0,!0):C.set(I+N,z+T,!1,!0)}}function h(C,g){const x=C.size,S=s.getEncodedBits(g);let I,z,N;for(let T=0;T<18;T++)I=Math.floor(T/3),z=T%3+x-8-3,N=(S>>T&1)===1,C.set(I,z,N,!0),C.set(z,I,N,!0)}function v(C,g,x){const S=C.size,I=c.getEncodedBits(g,x);let z,N;for(z=0;z<15;z++)N=(I>>z&1)===1,z<6?C.set(z,8,N,!0):z<8?C.set(z+1,8,N,!0):C.set(S-15+z,8,N,!0),z<8?C.set(8,S-z-1,N,!0):z<9?C.set(8,15-z-1+1,N,!0):C.set(8,15-z-1,N,!0);C.set(S-8,8,1,!0)}function y(C,g){const x=C.size;let S=-1,I=x-1,z=7,N=0;for(let T=x-1;T>0;T-=2)for(T===6&&T--;;){for(let A=0;A<2;A++)if(!C.isReserved(I,T-A)){let Y=!1;N<g.length&&(Y=(g[N]>>>z&1)===1),C.set(I,T-A,Y),z--,z===-1&&(N++,z=7)}if(I+=S,I<0||x<=I){I-=S,S=-S;break}}}function b(C,g,x){const S=new n;x.forEach(function(A){S.put(A.mode.bit,4),S.put(A.getLength(),d.getCharCountIndicator(A.mode,C)),A.write(S)});const I=o.getSymbolTotalCodewords(C),z=a.getTotalCodewordsCount(C,g),N=(I-z)*8;for(S.getLengthInBits()+4<=N&&S.put(0,4);S.getLengthInBits()%8!==0;)S.putBit(0);const T=(N-S.getLengthInBits())/8;for(let A=0;A<T;A++)S.put(A%2?17:236,8);return w(S,C,g)}function w(C,g,x){const S=o.getSymbolTotalCodewords(g),I=a.getTotalCodewordsCount(g,x),z=S-I,N=a.getBlocksCount(g,x),T=S%N,A=N-T,Y=Math.floor(S/N),M=Math.floor(z/N),k=M+1,E=Y-M,D=new i(E);let O=0;const X=new Array(N),ue=new Array(N);let we=0;const Yn=new Uint8Array(C.buffer);for(let et=0;et<N;et++){const Gn=et<A?M:k;X[et]=Yn.slice(O,O+Gn),ue[et]=D.encode(X[et]),O+=Gn,we=Math.max(we,Gn)}const H=new Uint8Array(S);let te=0,Z,ke;for(Z=0;Z<we;Z++)for(ke=0;ke<N;ke++)Z<X[ke].length&&(H[te++]=X[ke][Z]);for(Z=0;Z<E;Z++)for(ke=0;ke<N;ke++)H[te++]=ue[ke][Z];return H}function _(C,g,x,S){let I;if(Array.isArray(C))I=m.fromArray(C);else if(typeof C=="string"){let Y=g;if(!Y){const M=m.rawSplit(C);Y=s.getBestVersionForData(M,x)}I=m.fromString(C,Y||40)}else throw new Error("Invalid data");const z=s.getBestVersionForData(I,x);if(!z)throw new Error("The amount of data is too big to be stored in a QR Code");if(!g)g=z;else if(g<z)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+z+`.
`);const N=b(g,x,I),T=o.getSymbolSize(g),A=new e(T);return p(A,g),f(A),V(A,g),v(A,x,0),g>=7&&h(A,g),y(A,N),isNaN(S)&&(S=u.getBestMask(A,v.bind(null,A,x))),u.applyMask(S,A),v(A,x,S),{modules:A,version:g,errorCorrectionLevel:x,maskPattern:S,segments:I}}return _n.create=function(g,x){if(typeof g>"u"||g==="")throw new Error("No input text");let S=t.M,I,z;return typeof x<"u"&&(S=t.from(x.errorCorrectionLevel,t.M),I=s.from(x.version),z=u.from(x.maskPattern),x.toSJISFunc&&o.setToSJISFunction(x.toSJISFunc)),_(g,I,S,z)},_n}var Ln={},Hn={},Fr;function Or(){return Fr||(Fr=1,function(o){function t(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let e=n.slice().replace("#","").split("");if(e.length<3||e.length===5||e.length>8)throw new Error("Invalid hex color: "+n);(e.length===3||e.length===4)&&(e=Array.prototype.concat.apply([],e.map(function(l){return[l,l]}))),e.length===6&&e.push("F","F");const r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:r&255,hex:"#"+e.slice(0,6).join("")}}o.getOptions=function(e){e||(e={}),e.color||(e.color={});const r=typeof e.margin>"u"||e.margin===null||e.margin<0?4:e.margin,l=e.width&&e.width>=21?e.width:void 0,u=e.scale||4;return{width:l,scale:l?4:u,margin:r,color:{dark:t(e.color.dark||"#000000ff"),light:t(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},o.getScale=function(e,r){return r.width&&r.width>=e+r.margin*2?r.width/(e+r.margin*2):r.scale},o.getImageWidth=function(e,r){const l=o.getScale(e,r);return Math.floor((e+r.margin*2)*l)},o.qrToImageData=function(e,r,l){const u=r.modules.size,a=r.modules.data,i=o.getScale(u,l),s=Math.floor((u+l.margin*2)*i),c=l.margin*i,d=[l.color.light,l.color.dark];for(let m=0;m<s;m++)for(let p=0;p<s;p++){let f=(m*s+p)*4,V=l.color.light;if(m>=c&&p>=c&&m<s-c&&p<s-c){const h=Math.floor((m-c)/i),v=Math.floor((p-c)/i);V=d[a[h*u+v]?1:0]}e[f++]=V.r,e[f++]=V.g,e[f++]=V.b,e[f]=V.a}}}(Hn)),Hn}var jr;function ci(){return jr||(jr=1,function(o){const t=Or();function n(r,l,u){r.clearRect(0,0,l.width,l.height),l.style||(l.style={}),l.height=u,l.width=u,l.style.height=u+"px",l.style.width=u+"px"}function e(){try{return document.createElement("canvas")}catch(r){throw new Error("You need to specify a canvas element")}}o.render=function(l,u,a){let i=a,s=u;typeof i>"u"&&(!u||!u.getContext)&&(i=u,u=void 0),u||(s=e()),i=t.getOptions(i);const c=t.getImageWidth(l.modules.size,i),d=s.getContext("2d"),m=d.createImageData(c,c);return t.qrToImageData(m.data,l,i),n(d,s,c),d.putImageData(m,0,0),s},o.renderToDataURL=function(l,u,a){let i=a;typeof i>"u"&&(!u||!u.getContext)&&(i=u,u=void 0),i||(i={});const s=o.render(l,u,i),c=i.type||"image/png",d=i.rendererOpts||{};return s.toDataURL(c,d.quality)}}(Ln)),Ln}var Xn={},Ur;function di(){if(Ur)return Xn;Ur=1;const o=Or();function t(r,l){const u=r.a/255,a=l+'="'+r.hex+'"';return u<1?a+" "+l+'-opacity="'+u.toFixed(2).slice(1)+'"':a}function n(r,l,u){let a=r+l;return typeof u<"u"&&(a+=" "+u),a}function e(r,l,u){let a="",i=0,s=!1,c=0;for(let d=0;d<r.length;d++){const m=Math.floor(d%l),p=Math.floor(d/l);!m&&!s&&(s=!0),r[d]?(c++,d>0&&m>0&&r[d-1]||(a+=s?n("M",m+u,.5+p+u):n("m",i,0),i=0,s=!1),m+1<l&&r[d+1]||(a+=n("h",c),c=0)):i++}return a}return Xn.render=function(l,u,a){const i=o.getOptions(u),s=l.modules.size,c=l.modules.data,d=s+i.margin*2,m=i.color.light.a?"<path "+t(i.color.light,"fill")+' d="M0 0h'+d+"v"+d+'H0z"/>':"",p="<path "+t(i.color.dark,"stroke")+' d="'+e(c,s,i.margin)+'"/>',f='viewBox="0 0 '+d+" "+d+'"',h='<svg xmlns="http://www.w3.org/2000/svg" '+(i.width?'width="'+i.width+'" height="'+i.width+'" ':"")+f+' shape-rendering="crispEdges">'+m+p+`</svg>
`;return typeof a=="function"&&a(null,h),h},Xn}var Lr;function fi(){if(Lr)return Ze;Lr=1;const o=qu(),t=si(),n=ci(),e=di();function r(l,u,a,i,s){const c=[].slice.call(arguments,1),d=c.length,m=typeof c[d-1]=="function";if(!m&&!o())throw new Error("Callback required as last argument");if(m){if(d<2)throw new Error("Too few arguments provided");d===2?(s=a,a=u,u=i=void 0):d===3&&(u.getContext&&typeof s>"u"?(s=i,i=void 0):(s=i,i=a,a=u,u=void 0))}else{if(d<1)throw new Error("Too few arguments provided");return d===1?(a=u,u=i=void 0):d===2&&!u.getContext&&(i=a,a=u,u=void 0),new Promise(function(p,f){try{const V=t.create(a,i);p(l(V,u,i))}catch(V){f(V)}})}try{const p=t.create(a,i);s(null,l(p,u,i))}catch(p){s(p)}}return Ze.create=t.create,Ze.toCanvas=r.bind(null,n.render),Ze.toDataURL=r.bind(null,n.renderToDataURL),Ze.toString=r.bind(null,function(l,u,a){return e.render(l,a)}),Ze}var pi=fi();const mi=Xu(pi),Vi=["src"],hi={key:1,class:"x-qr-code__expired"},gi={class:"x-qr-code__refresh"},vi={class:"x-qr-code__tip"},Hr=Vue.defineComponent({__name:"QrCode",props:{size:{default:200},content:{},expired:{},tip:{default:"二维码已失效，请刷新重试"},options:{}},emits:["draw","expired"],setup(o,{expose:t,emit:n}){const e=o,r=n,l=Vue.ref(""),u=Vue.ref(!1);let a;const i=Vue.computed(()=>{const m=e.size+"px";return{width:m,height:m}}),s=async m=>{const p=typeof m=="function"?await m():m;if(!p){console.warn("QrCode content is empty"),l.value="";return}mi.toDataURL(p,{margin:0,scale:10,...e.options},(f,V)=>{if(f){console.warn("QrCode",f);return}l.value=V,e.expired&&(u.value=!1,c()),r("draw",V)})},c=()=>{clearTimeout(a),a=setTimeout(()=>{u.value=!0,r("expired")},e.expired)},d=()=>{e.content&&s(e.content)};return Vue.watch(()=>e.content,s,{immediate:!0}),Vue.onUnmounted(()=>{a&&clearTimeout(a)}),t({refresh:d}),(m,p)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:"x-qr-code",style:Vue.normalizeStyle(i.value)},[l.value?(Vue.openBlock(),Vue.createElementBlock("img",{key:0,class:"x-qr-code__code",src:l.value},null,8,Vi)):Vue.createCommentVNode("",!0),u.value?(Vue.openBlock(),Vue.createElementBlock("div",hi,[Vue.createElementVNode("div",gi,[Vue.createVNode(Vue.unref(de),{icon:Vue.unref(VtjIcons.Refresh),onClick:d,size:32},null,8,["icon"]),p[0]||(p[0]=Vue.createTextVNode(" 刷新 ",-1))]),Vue.createElementVNode("div",vi,[Vue.renderSlot(m.$slots,"tip",{},()=>[Vue.createTextVNode(Vue.toDisplayString(e.tip),1)])])])):Vue.createCommentVNode("",!0)],4))}}),yi={key:0},Xr=Vue.defineComponent({name:"XVerify",__name:"Verify",props:Vue.mergeModels({api:{},seconds:{default:60},maxlength:{default:6},placeholder:{default:"请输入验证码"}},{modelValue:{type:String},modelModifiers:{}}),emits:["update:modelValue"],setup(o,{expose:t}){const n=o,e=Vue.useModel(o,"modelValue"),r=Vue.ref(!1),l=Vue.ref(0);let u=null;const a=async()=>{n.api&&await n.api().catch(()=>!1)&&(r.value=!0,l.value=n.seconds,i())},i=()=>{--l.value,l.value<=0?r.value=!1:u=setTimeout(i,1e3)};return Vue.onUnmounted(()=>{u&&clearTimeout(u)}),t({send:a}),(s,c)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElInput),{modelValue:e.value,"onUpdate:modelValue":c[0]||(c[0]=d=>e.value=d),placeholder:n.placeholder,maxlength:n.maxlength,class:"x-verify"},{suffix:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElDivider),{direction:"vertical"}),r.value?(Vue.openBlock(),Vue.createElementBlock("span",yi,[c[1]||(c[1]=Vue.createTextVNode(" 重新获取 ",-1)),Vue.createElementVNode("strong",null,Vue.toDisplayString(l.value),1),c[2]||(c[2]=Vue.createTextVNode(" 秒 ",-1))])):(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElLink),{key:1,type:"primary",underline:!1,onClick:Vue.withModifiers(a,["stop"])},{default:Vue.withCtx(()=>c[3]||(c[3]=[Vue.createTextVNode(" 获取验证码 ",-1)])),_:1,__:[3]}))]),_:1},8,["modelValue","placeholder","maxlength"]))}}),wi={modelValue:{type:Array,default:()=>[]},selectValue:{type:[Object,Array]},uploader:{type:Function},multiple:{type:Boolean},limit:{type:Number},accept:{type:String,default:"image/*,audio/*,video/*,.zip,.svg,.pdf,.json,.docx,.xlsx,.pptx,.doc,.xls,.ppt"},disabled:{type:Boolean},size:{type:String,default:"default"},thumbnail:{type:Function},addable:{type:Boolean,default:!0},removable:{type:Boolean,default:!0},downloadable:{type:Boolean,default:!0},previewable:{type:Boolean,default:!0},selectable:{type:Boolean,default:!1},clickable:{type:Boolean,default:!1},listType:{type:String,default:"card"},beforeUpload:{type:Function},limitSize:{type:String,default:"10M"},formatter:{type:Function},valueFormatter:{type:Function},previewer:{type:Function},downloader:{type:Function},autoUpload:{type:Boolean,default:!0}};function qn(o){if(o.type)return o.type;const t=o.name||o.url.split("?")[0],n=t.substring(t.lastIndexOf(".")+1).toLowerCase(),e=["jpg","png","gif","jpeg","bpm","webp","svg"],r=["js","mjs","cjs","ts","jsx","tsx"],l=["css","scss","sass","less"],u=["doc","docx"],a=["xls","xlsx"],i=["ppt","pptx"],s=["zip","rar"],c=["mp4","wmv"],d=["mp3","wma"],m=["pdf","json"];return e.includes(n)?"img":r.includes(n)?"js":l.includes(n)?"css":u.includes(n)?"word":a.includes(n)?"excel":i.includes(n)?"ppt":s.includes(n)?"zip":c.includes(n)?"video":d.includes(n)?"audio":m.includes(n)?n:"unknow"}function Kn(o){return qn(o)==="img"}function pt(o){const{url:t,type:n}=o||{};return{url:t,...o,...o.response,type:n||qn(o.response||o)}}const bi={css:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229470455'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='22625'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M743.417191%200l280.583066%20283.157223v655.380264a85.204583%2085.204583%200%200%201-85.461998%2085.461998H213.398352A85.204583%2085.204583%200%200%201%20128.708601%20938.537487v-106.570082H42.731771A42.730999%2042.730999%200%200%201%200.000772%20789.236406V426.537744a41.701337%2041.701337%200%200%201%2012.613367-30.117632%2042.473583%2042.473583%200%200%201%2030.117632-12.355951H128.708601V85.204583A85.461998%2085.461998%200%200%201%20213.398352%200z%20m-25.741566%2064.096499H213.398352a21.108084%2021.108084%200%200%200-15.187524%206.177975%2021.622915%2021.622915%200%200%200-6.177975%2014.930109v298.859578h639.935324A42.730999%2042.730999%200%200%201%20875.214007%20426.537744v362.698662a42.730999%2042.730999%200%200%201-42.730999%2042.730999H192.032853v106.570082a21.108084%2021.108084%200%200%200%2021.365499%2021.365499h725.139907a21.108084%2021.108084%200%200%200%2021.3655-21.365499V308.898789l-10.039211-10.296626H823.730876a97.81795%2097.81795%200%200%201-97.81795-98.075366v-128.707829zM191.260606%20467.209418a120.470528%20120.470528%200%200%200-100.392107%2043.245831%20154.449394%20154.449394%200%200%200-30.117632%2099.362443%20154.449394%20154.449394%200%200%200%2029.08797%2099.105029%20121.50019%20121.50019%200%200%200%20102.966263%2042.988414%20116.866708%20116.866708%200%200%200%2077.224697-25.741565%20120.985359%20120.985359%200%200%200%2042.988415-75.165372h-43.760662a85.461998%2085.461998%200%200%201-27.543475%2046.592234%2078.769191%2078.769191%200%200%201-48.136728%2014.415276%2079.541438%2079.541438%200%200%201-66.41324-28.058306%20121.757606%20121.757606%200%200%201-19.56359-74.650541%20118.153787%20118.153787%200%200%201%2020.078421-73.620878%2074.393125%2074.393125%200%200%201%2062.809421-29.087969%2081.343348%2081.343348%200%200%201%2047.107065%2012.355951%2064.096499%2064.096499%200%200%201%2025.741566%2039.642012h43.760662a100.392106%20100.392106%200%200%200-36.038193-66.155824%20122.529853%20122.529853%200%200%200-79.798853-25.741566z%20m258.702735%200a123.559516%20123.559516%200%200%200-72.848631%2019.821006%2064.868746%2064.868746%200%200%200-30.889879%2057.146276%2060.750095%2060.750095%200%200%200%2031.662126%2055.344366%20439.151112%20439.151112%200%200%200%2062.809421%2023.167409%20446.616166%20446.616166%200%200%201%2053.542456%2018.019096%2039.12718%2039.12718%200%200%201%2025.741566%2034.493698%2031.147295%2031.147295%200%200%201-17.76168%2027.28606%2098.590197%2098.590197%200%200%201-47.621897%2010.039211%2085.719414%2085.719414%200%200%201-49.938637-12.098536%2063.324252%2063.324252%200%200%201-22.137747-44.532909h-44.790324a93.441884%2093.441884%200%200%200%2037.840101%2077.224697%20136.94513%20136.94513%200%200%200%2079.026607%2019.56359%20136.687714%20136.687714%200%200%200%2081.343348-21.108084%2068.472565%2068.472565%200%200%200%2028.830554-58.175938%2065.383577%2065.383577%200%200%200-35.780777-59.205601%20418.815275%20418.815275%200%200%200-71.818968-25.741566%20398.479438%20398.479438%200%200%201-46.84965-16.732018A31.40471%2031.40471%200%200%201%20391.272572%20540.572881a29.087969%2029.087969%200%200%201%2016.732017-28.315723%2082.887842%2082.887842%200%200%201%2039.642011-7.465054%2077.224697%2077.224697%200%200%201%2045.305156%2011.068873A61.007511%2061.007511%200%200%201%20514.832087%20553.443663h43.760662a88.036155%2088.036155%200%200%200-32.949204-66.413239%20125.361425%20125.361425%200%200%200-75.680204-19.821006z%20m265.138128%200a123.559516%20123.559516%200%200%200-72.848632%2019.821006%2064.868746%2064.868746%200%200%200-30.889878%2057.146276A60.750095%2060.750095%200%200%200%20643.539916%20599.521066a439.151112%20439.151112%200%200%200%2062.80942%2023.167409%20446.616166%20446.616166%200%200%201%2053.542457%2018.019096%2039.12718%2039.12718%200%200%201%2025.741566%2034.493698%2031.147295%2031.147295%200%200%201-17.761681%2027.28606%2098.590197%2098.590197%200%200%201-47.621896%2010.039211%2085.719414%2085.719414%200%200%201-50.9683-12.098536%2063.324252%2063.324252%200%200%201-22.137747-44.532909h-44.018077a92.412221%2092.412221%200%200%200%2037.582686%2077.224697%20136.94513%20136.94513%200%200%200%2079.026606%2019.56359%20136.687714%20136.687714%200%200%200%2081.343348-21.108084%2068.472565%2068.472565%200%200%200%2028.830554-58.175938%2065.383577%2065.383577%200%200%200-35.780777-59.205601%20418.815275%20418.815275%200%200%200-71.818968-25.741566%20398.479438%20398.479438%200%200%201-46.84965-16.732018A31.40471%2031.40471%200%200%201%20656.410699%20540.572881a29.087969%2029.087969%200%200%201%2016.732017-28.315723%2082.887842%2082.887842%200%200%201%2039.642012-7.465054%2077.224697%2077.224697%200%200%201%2045.305155%2011.068873%2061.007511%2061.007511%200%200%201%2021.108084%2036.553024H823.730876a87.263908%2087.263908%200%200%200-32.949204-66.41324%20125.361425%20125.361425%200%200%200-75.680203-19.821005z'%20fill='%23007BEA'%20p-id='22626'%3e%3c/path%3e%3c/svg%3e",excel:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229259681'%20class='icon'%20viewBox='0%200%201082%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='13779'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='270.5'%20height='256'%3e%3cpath%20d='M1014.941308%20865.332934c-4.506568%2023.309835-32.633769%2023.869271-51.405956%2024.646266-116.673496%200.372957-233.47131-0.186479-350.238046%200v100.884967h-69.929506c-171.591468-31.017621-343.555892-59.735338-515.333838-89.634086V116.869663c172.834659-29.898749%20345.669317-59.362381%20518.379657-89.758406h66.883687v89.634087c112.726363%200%20225.452727%200.248638%20338.148011-0.310798%2019.020826%200.808074%2039.968598-0.559436%2056.53412%2010.753604%2011.592758%2016.627683%2010.225248%2037.668694%2011.002243%2056.65844-0.559436%20194.590505-0.310798%20389.056691-0.186479%20583.460717-0.590516%2032.57161%203.045818%2065.889134-3.853893%2098.025627z%20m-576.840724-547.283852c-25.547579%201.212111-51.126239%202.79718-76.580579%204.568727-18.989746%2046.029155-41.242869%2090.908358-56.067923%20138.522582-13.861582-44.910283-32.229732-88.204417-48.981734-132.026908-24.770585%201.336431-49.54117%202.79718-74.342835%204.25793%2026.138095%2057.404354%2053.954499%20113.969555%2079.3156%20171.684707-29.867669%2056.036844-57.808392%20112.819603-86.743667%20169.198325%2024.677346%201.025633%2049.323611%202.020186%2074.032036%202.393143%2017.528996-44.630565%2039.409162-87.551741%2054.669334-133.02146%2013.737263%2048.826335%2037.078178%2094.016336%2056.192243%20140.791405%2027.163728%201.864787%2054.172057%203.512015%2081.335785%205.128164-31.07978-62.781156-62.718997-125.096116-93.705538-187.877273a21887.624539%2021887.624539%200%200%200%2090.877278-183.619342z%20m546.786577-167.675416H613.297306v67.225565h90.131363v78.445366h-90.131363v44.848123h90.131363v78.445366h-90.131363v44.817043h90.131363v78.414286h-90.131363v44.879203h90.131363v78.445366h-90.131363v44.785963h90.131363v78.445366h-90.131363v67.225565h371.589855V150.373666z%20m-78.787244%20145.670931h-157.667726V217.599231h157.667726v78.445366z%20m0%20123.293489h-157.667726v-78.445366h157.667726v78.445366z%20m0%20123.293489h-157.667726V464.155129h157.667726v78.476446z%20m0%20123.262409h-157.667726v-78.445366h157.667726v78.445366z%20m0%20123.293489h-157.667726v-78.507526h157.667726v78.507526z'%20fill='%23207245'%20p-id='13780'%3e%3c/path%3e%3c/svg%3e",js:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229428492'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='20525'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M896%20448H128v-384h541.866667v221.866667H896v162.133333z%20m-243.2%20251.733333c-4.266667-8.533333-12.8-12.8-21.333333-17.066666-12.8-4.266667-29.866667-8.533333-42.666667-12.8-17.066667-4.266667-34.133333-8.533333-46.933333-17.066667-12.8-4.266667-21.333333-12.8-25.6-21.333333-4.266667-8.533333-8.533333-21.333333-8.533334-29.866667%200-12.8%204.266667-25.6%2012.8-34.133333%208.533333-12.8%2017.066667-21.333333%2029.866667-25.6%2012.8-4.266667%2029.866667-8.533333%2046.933333-8.533334%2017.066667%200%2034.133333%204.266667%2051.2%208.533334%2012.8%204.266667%2025.6%2012.8%2034.133334%2025.6%208.533333%2012.8%2012.8%2025.6%2012.8%2038.4l-29.866667%204.266666c0-12.8-8.533333-25.6-17.066667-34.133333-12.8-8.533333-29.866667-12.8-42.666666-12.8-17.066667%200-29.866667%204.266667-42.666667%2012.8-8.533333%204.266667-12.8%2017.066667-12.8%2025.6%200%208.533333%204.266667%2017.066667%208.533333%2021.333333%2017.066667%208.533333%2034.133333%2017.066667%2051.2%2017.066667%2017.066667%204.266667%2038.4%208.533333%2055.466667%2017.066667%2012.8%204.266667%2025.6%2012.8%2034.133333%2025.6%208.533333%208.533333%208.533333%2021.333333%208.533334%2034.133333%200%2012.8-4.266667%2025.6-12.8%2038.4-8.533333%2012.8-21.333333%2021.333333-34.133334%2025.6-17.066667%208.533333-29.866667%208.533333-46.933333%208.533333-21.333333%200-38.4-4.266667-55.466667-8.533333-12.8-4.266667-25.6-17.066667-34.133333-29.866667-8.533333-12.8-12.8-29.866667-12.8-46.933333l29.866667-4.266667c0%2012.8%204.266667%2021.333333%208.533333%2029.866667%204.266667%208.533333%2017.066667%2017.066667%2025.6%2021.333333%2012.8%204.266667%2025.6%208.533333%2038.4%208.533334%2012.8%200%2021.333333%200%2034.133333-4.266667%208.533333-4.266667%2017.066667-8.533333%2021.333334-17.066667%204.266667-4.266667%208.533333-12.8%208.533333-21.333333-17.066667-4.266667-21.333333-12.8-25.6-17.066667z%20m-341.333333%2017.066667l29.866666-4.266667c0%2012.8%200%2029.866667%208.533334%2038.4%208.533333%208.533333%2017.066667%2012.8%2025.6%208.533334%208.533333%200%2017.066667%200%2021.333333-4.266667%204.266667-4.266667%208.533333-8.533333%2012.8-17.066667%204.266667-8.533333%204.266667-21.333333%204.266667-29.866666v-174.933334h34.133333v170.666667c0%2017.066667%200%2034.133333-8.533333%2046.933333-4.266667%2012.8-12.8%2021.333333-25.6%2025.6-12.8%204.266667-25.6%208.533333-38.4%208.533334-17.066667%200-38.4-4.266667-51.2-17.066667-8.533333-8.533333-17.066667-29.866667-12.8-51.2zM896%20960H128V896h768v64z%20m64-512V290.133333L669.866667%200H93.866667c-4.266667%200-12.8%204.266667-21.333334%208.533333-4.266667%208.533333-8.533333%2012.8-8.533333%2021.333334v418.133333H0V896h64v98.133333c0%208.533333%204.266667%2017.066667%208.533333%2021.333334%208.533333%204.266667%2012.8%208.533333%2021.333334%208.533333h832c8.533333%200%2017.066667-4.266667%2021.333333-8.533333%204.266667-4.266667%208.533333-12.8%208.533333-21.333334V896H1024V448h-64z'%20fill='%23129C00'%20p-id='20526'%3e%3c/path%3e%3c/svg%3e",json:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229451017'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='21591'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M1008%20464h-72a8%208%200%200%201-8-8v-141.44a64%2064%200%200%200-18.688-45.376L658.688%2018.688A63.808%2063.808%200%200%200%20613.504%200H144a48%2048%200%200%200-48%2048v408a8%208%200%200%201-8%208H16A16%2016%200%200%200%200%20480v400c0%208.768%207.232%2016%2016%2016H96c0%2035.328%2014.272%2067.328%2037.504%2090.496%2023.168%2023.232%2055.168%2037.504%2090.496%2037.504h656a48%2048%200%200%200%2048-48v-64a16%2016%200%200%201%2016-16h64a16%2016%200%200%200%2016-16V480a16%2016%200%200%200-16-16zM704%20154.496L773.504%20224h-65.536A4.032%204.032%200%200%201%20704%20219.968V154.56zM160%2072c0-4.416%203.584-8%208-8H576a64%2064%200%200%201%2064%2064v144c0%208.832%207.232%2016%2016%2016h144a64%2064%200%200%201%2064%2064v104a8%208%200%200%201-8%208H168a8%208%200%200%201-8-8v-384z%20m492.288%20603.52c0%2032.512-9.472%2058.88-28.416%2079.104-18.88%2020.288-43.648%2030.4-74.24%2030.4-29.952%200-54.208-9.792-72.96-29.44-18.496-19.584-27.904-44.864-27.904-75.776%200-32.704%209.536-59.328%2028.608-79.808%2019.136-20.48%2044.352-30.784%2075.648-30.784%2029.76%200%2053.76%209.856%2072%2029.76%2018.176%2019.84%2027.264%2045.44%2027.264%2076.544z%20m-365.888%2053.248c16.896%2013.952%2035.84%2020.928%2057.28%2020.928%2012.16%200%2021.248-2.112%2027.328-6.272%206.08-4.224%209.216-9.536%209.216-16.128a22.528%2022.528%200%200%200-7.296-16c-4.928-4.992-17.6-11.904-38.4-20.48-32.64-13.824-48.896-33.92-48.896-60.416%200-19.392%207.36-34.56%2022.144-45.184%2014.848-10.688%2034.304-16.128%2058.752-16.128%2020.352%200%2037.44%202.688%2051.264%207.936v41.792a85.504%2085.504%200%200%200-49.216-14.336%2050.176%2050.176%200%200%200-26.24%206.016c-6.656%204.032-9.856%209.408-9.856%2016.128%200%205.376%202.24%2010.368%206.72%2014.848%204.48%204.48%2015.616%2010.752%2033.28%2018.432%2020.736%208.896%2035.008%2018.304%2042.88%2028.16a55.168%2055.168%200%200%201%2011.648%2035.328c0%2020.032-7.104%2035.328-21.312%2045.824-14.208%2010.496-34.368%2015.808-60.48%2015.808-23.936%200-43.52-3.904-58.688-11.648l-0.128-44.608c0.128%200%200%200%200%200z%20m-139.584%208.96c7.68%205.76%2016.512%208.768%2026.368%208.768%2021.44%200%2032.128-16.192%2032.128-48.64V572.736h44.48v126.848h-0.064c0%2027.52-6.4%2048.64-19.136%2063.488-12.672%2014.592-30.912%2022.016-54.592%2022.016a77.44%2077.44%200%200%201-29.184-5.44v-41.856zM864%20952a8%208%200%200%201-8%208H224a64%2064%200%200%201-64-64h696c4.416%200%208%203.584%208%208v48z%20m9.216-170.624h-45.504L739.328%20646.4a162.048%20162.048%200%200%201-10.048-17.024h-0.64c0.768%207.232%201.152%2018.304%201.152%2033.28v118.72h-42.24V572.8h48.576l85.12%20131.2c5.632%208.64%209.024%2014.144%2010.368%2016.576h0.64a199.168%20199.168%200%200%201-1.28-28.8v-119.04h42.24v208.704z%20m-267.392-103.168c0%2020.864-4.8%2037.504-14.528%2049.792-9.728%2012.288-23.296%2018.56-40.704%2018.56a47.744%2047.744%200%200%201-40.32-19.328c-9.792-12.928-14.72-29.504-14.72-49.856%200-20.608%205.056-37.248%2015.04-50.176a49.536%2049.536%200%200%201%2041.216-19.392c17.216%200%2030.464%206.272%2039.872%2018.88%209.408%2012.608%2014.08%2029.824%2014.08%2051.52z'%20fill='%23A0D911'%20p-id='21592'%3e%3c/path%3e%3c/svg%3e",pdf:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229295544'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='14834'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M535.198324%200h69.53853v95.276816c124.175945%200.677323%20248.351891-1.354647%20373.205159%200.677324a40.6394%2040.6394%200%200%201%2045.154889%2045.154889c1.919083%20234.579649%200%20469.272186%201.128872%20703.851835-1.128872%2024.044979%202.257744%2050.686363-11.288722%2072.022049-17.045971%2012.417595-38.946092%2011.288722-58.814243%2011.288722H604.962628v95.276816h-72.925146C354.917429%20990.924044%20177.458715%20960.557381%200%20928.723184V95.502591C178.361812%2063.555507%20356.836512%2032.51152%20535.198324%200z'%20fill='%23A33639'%20p-id='14835'%3e%3c/path%3e%3cpath%20d='M604.736854%20130.949179h383.816558v762.101642h-383.816558v-95.276816h302.42487v-47.638408H604.736854v-59.491567h302.42487V643.457171H604.736854v-60.056002h302.42487v-47.638409H604.736854v-59.491566h302.42487V428.971447H604.736854v-59.830228h302.42487v-47.638408H604.736854V261.898357h302.42487v597.17341H604.736854z'%20fill='%23FFFFFF'%20p-id='14836'%3e%3c/path%3e%3cpath%20d='M645.489141%20529.66685h302.424871v47.638409H645.489141zM645.489141%20611.510087h302.424871v47.638408H645.489141zM645.489141%20693.353324h302.424871v47.638408H645.489141zM596.383199%20775.19656h351.530813v47.638409H596.383199z'%20fill='%23A33639'%20p-id='14837'%3e%3c/path%3e%3cpath%20d='M180.619557%20317.325984c59.265792%202.822181%20130.949179-24.044979%20180.619557%2021.335685%2046.961085%2058.475582%2034.54349%20165.831331-35.107927%20200.713483-24.722302%2012.982031-53.169882%2011.288722-80.037041%2010.272737v130.949179L180.619557%20675.065594c-0.903098-119.208908-1.128872-238.530702%200-357.73961z'%20fill='%23FFFFFF'%20p-id='14838'%3e%3c/path%3e%3cpath%20d='M245.642597%20377.720648c21.448572-1.015985%2048.089957-5.079925%2062.539522%2015.578437a82.407673%2082.407673%200%200%201%201.467534%2073.263808c-12.417595%2022.577445-40.526513%2020.771249-62.313747%2023.367655-2.257744-37.365671-2.03197-74.731342-1.693309-112.2099zM885.713152%20379.865505a83.988094%2083.988094%200%200%201-48.315732-19.642376%20460.57987%20460.57987%200%200%200-77.666409%2024.835189c-20.3197%2036.011024-39.171866%2054.411641-55.540514%2054.411641a17.384632%2017.384632%200%200%201-9.821188-2.596406%2019.416602%2019.416602%200%200%201-11.288723-17.723294c0-5.870136%201.354647-22.577445%2063.329733-49.105942a467.917539%20467.917539%200%200%200%2034.656377-81.278801c-7.902106-15.691324-24.835189-54.298754-13.094918-73.941131a20.658362%2020.658362%200%200%201%2020.206813-10.498511%2021.900121%2021.900121%200%200%201%2017.045971%208.466541c8.466542%2011.288722%207.789218%2036.688347-3.27373%2073.376695A196.988204%20196.988204%200%200%200%20842.70312%20338.661669a221.14607%20221.14607%200%200%201%2041.090949-4.515489c30.705325%200.677323%2035.333701%2015.014001%2034.656377%2023.59343%200%2022.577445-21.56146%2022.577445-32.624407%2022.577444z%20m-185.022159%2042.106935l2.257745-0.564436a45.154889%2045.154889%200%200%200%2023.932091-16.820197%2056.443612%2056.443612%200%200%200-26.189836%2017.384633z%20m93.357734-200.261934h-2.144858a4.402602%204.402602%200%200%200-2.82218%200.677323%2052.492559%2052.492559%200%200%200%204.176827%2033.866167%2053.282769%2053.282769%200%200%200%200.790211-34.54349zM790.210561%20317.551758v1.24176l-0.677323-0.677324c-5.418587%2014.336677-11.288722%2028.44758-18.061956%2042.332709l1.128872-0.677323V361.239114A346.338%20346.338%200%200%201%20812.788006%20348.595745l-0.677324-0.564436h1.806196a196.310881%20196.310881%200%200%201-23.706317-30.479551z%20m94.599493%2034.769265a66.151913%2066.151913%200%200%200-18.174843%201.693308%2050.686363%2050.686363%200%200%200%2020.997024%206.660347%2026.076949%2026.076949%200%200%200%2013.885128-1.580422c-0.451549-2.596406-3.273729-6.773233-17.158858-6.773233z'%20fill='%23A33639'%20p-id='14839'%3e%3c/path%3e%3c/svg%3e",ppt:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229322104'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='16865'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M538.731891%200h65.98683v107.168391c124.387582%200.722484%20248.895579-1.324553%20373.28316%200a40.699906%2040.699906%200%200%201%2045.034808%2046.118533c2.047037%20222.404516%200%20444.929445%201.204139%20667.454374-1.204139%2024.082785%202.287865%2050.694262-11.198495%2072.248354-16.978363%2012.041392-39.014111%2010.957667-59.002822%2012.041392-116.319849-0.60207-232.639699%200-349.200376%200V1023.518344h-72.248354C355.100659%20990.886171%20177.490122%20960.662277%200%20928.752587V95.488241C179.537159%2063.698965%20359.074318%2031.30762%20538.731891%200z'%20fill='%23D24625'%20p-id='16866'%3e%3c/path%3e%3cpath%20d='M604.718721%20142.931326H988.598307v726.216369H604.718721v-95.247413h279.239887v-47.563499H604.718721v-60.206962h279.239887v-46.96143H604.839135v-69.960489c46.118532%2014.570085%2098.619003%2014.208843%20139.800564-14.088429%2044.553151-27.093133%2067.793039-78.630292%2071.646284-130.047036H663.119473c0-51.777987%200.60207-103.555974-0.963311-155.213547-19.145814%203.732832-38.171214%207.826905-57.196614%2012.041392z'%20fill='%23FFFFFF'%20p-id='16867'%3e%3c/path%3e%3cpath%20d='M686.35936%20224.69238a165.689558%20165.689558%200%200%201%20153.16651%20156.5381c-51.055503%200.60207-102.111007%200-153.286924%200%200.120414-52.380056%200.120414-104.278457%200.120414-156.5381z'%20fill='%23D24625'%20p-id='16868'%3e%3c/path%3e%3cpath%20d='M186.64158%20314.521167c63.21731%203.130762%20139.680151-25.527752%20192.662277%2022.878645%2050.092192%2062.374412%2036.84666%20176.888053-37.44873%20214.095955-26.370649%2013.847601-56.714958%2012.041392-85.373471%2010.957667v139.68015l-69.238006-5.900282c-1.806209-127.157103-2.047037-254.434619-0.60207-381.712135z'%20fill='%23FFFFFF'%20p-id='16869'%3e%3c/path%3e%3cpath%20d='M255.759172%20378.942615c22.878645-0.963311%2051.296331-5.298213%2066.709313%2016.737536a87.902164%2087.902164%200%200%201%201.565381%2078.148635c-13.245532%2024.082785-43.228598%2022.035748-66.468485%2024.925682-2.408278-39.857008-2.167451-79.714017-1.806209-119.811853z'%20fill='%23D24625'%20p-id='16870'%3e%3c/path%3e%3c/svg%3e",unknow:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229667944'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='29126'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M604.672%20142.848h384v726.528h-384V142.848z'%20fill='%23FFFFFF'%20p-id='29127'%3e%3c/path%3e%3cpath%20d='M1022.976%20154.112c2.56-22.528-12.8-42.496-35.328-45.568-3.072-0.512-6.656-0.512-9.728%200-123.904-2.048-248.832%200-373.248%200V0h-65.536C358.912%2031.232%20179.712%2063.488%200%2095.744v833.024c177.664%2031.744%20355.328%2061.952%20532.48%2094.72h72.192v-118.784h349.184c19.968-1.024%2041.984%200.512%2058.88-12.288%2013.312-21.504%2010.24-48.128%2011.264-72.192-1.536-221.184%201.024-443.904-1.024-666.112z%20m-34.304%20715.264h-384V142.848h384v726.528z'%20fill='%238199AF'%20p-id='29128'%3e%3c/path%3e%3cpath%20d='M252.928%20590.848c1.024-19.968%205.632-37.376%2013.824-52.736%208.192-15.36%2023.04-30.72%2044.544-46.592%2016.896-13.312%2028.672-23.04%2034.816-29.696%206.144-6.656%2011.264-13.824%2014.848-21.504%203.584-7.68%205.12-15.872%205.12-24.576%200-18.944-5.632-33.28-16.896-43.008-11.264-9.728-27.136-14.336-46.592-14.336s-35.328%205.632-47.616%2016.896c-12.288%2011.264-19.456%2028.16-20.992%2050.176l-73.728-2.56c4.608-37.888%2018.944-67.072%2044.032-87.04%2025.088-19.968%2057.856-30.208%2098.304-30.208%2043.008%200%2076.288%209.216%20100.352%2027.648%2024.064%2018.432%2035.84%2044.544%2035.84%2078.848%200%2018.944-4.608%2036.352-13.824%2052.224-9.216%2016.384-25.088%2033.28-48.64%2050.688l-20.48%2015.36c-24.576%2018.944-37.376%2038.912-37.888%2060.928H252.928z%20m-5.12%20114.688V640H327.68v66.048H247.808z'%20fill='%23FCFCFC'%20p-id='29129'%3e%3c/path%3e%3c/svg%3e",word:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229195392'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='8195'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M535.119473%200h69.599248v95.247413C729.226717%2096.331138%20853.614299%2093.92286%20977.881468%2096.331138a40.459078%2040.459078%200%200%201%2044.914393%2045.516463c2.047037%20234.566322%200%20469.614299%201.204139%20703.819379-1.204139%2024.082785%202.287865%2050.694262-11.318909%2072.248354-16.978363%2012.041392-38.893697%2010.837253-58.761994%2012.041392h-349.200376V1023.518344h-72.248354C354.980245%20990.886171%20177.490122%20960.541863%200%20928.752587V95.488241C178.33302%2063.578551%20356.786453%2032.511759%20535.119473%200z'%20fill='%232A5699'%20p-id='8196'%3e%3c/path%3e%3cpath%20d='M604.718721%20131.010348H988.598307v761.979304H604.718721v-95.247413h302.479774v-48.165569H604.718721v-59.002822h302.479774v-48.16557H604.718721v-59.002822h302.479774v-48.165569H604.718721v-60.206961h302.479774V428.673565H604.718721v-60.206961h302.479774v-46.96143H604.718721v-59.604892h302.479774V214.336783H604.718721zM240.827846%20341.373471c22.156162-1.324553%2044.19191-2.287865%2066.348071-3.492003%2015.533396%2080.4365%2031.30762%20160.632173%2048.165569%20240.827845%2013.125118-82.724365%2027.695202-165.087488%2041.783632-247.571025%2023.239887-0.842897%2046.479774-2.167451%2069.719661-3.612418-26.370649%20115.356538-49.369708%20231.796802-78.148636%20346.430856-19.386642%2010.355597-48.165569%200-71.52587%201.204139C301.034807%20596.169332%20283.093133%20517.779868%20269.245532%20438.667921c-13.606773%2076.944497-31.30762%20153.16651-46.841016%20229.508937-22.39699-1.204139-44.793979-2.528692-67.311383-4.094073-19.266228-104.760113-42.024459-208.918156-60.206962-313.919097%2019.868297-0.963311%2039.857008-1.806209%2060.206962-2.528693%2012.041392%2075.860771%2025.648166%20151.360301%2036.124177%20227.341487%2016.135466-77.907808%2032.873001-155.695202%2049.610536-233.603011z'%20fill='%23FFFFFF'%20p-id='8197'%3e%3c/path%3e%3c/svg%3e",zip:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713229493469'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='23652'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M192.065027%20384.129533h640.128032a42.536025%2042.536025%200%200%201%2042.536025%2042.536025v362.730519a42.796982%2042.796982%200%200%201-42.536025%2042.796982H192.065027V939.446472a20.354662%2020.354662%200%200%200%206.262973%2015.135518%2021.659448%2021.659448%200%200%200%2015.135518%206.262973H939.446472a21.398491%2021.398491%200%200%200%2021.398491-21.398491V308.973857l-10.177331-10.177332h-127.347118a98.11991%2098.11991%200%200%201-97.858953-98.11991V72.546626l-8.611588-8.611588H213.463518a21.659448%2021.659448%200%200%200-15.135518%206.262973%2020.876577%2020.876577%200%200%200-6.262973%2015.135518zM128.130511%20832.193059H42.797504A42.796982%2042.796982%200%200%201%200.000522%20789.396077v-362.730519a41.753153%2041.753153%200%200%201%2012.525946-30.010079%2042.536025%2042.536025%200%200%201%2030.271036-12.525946h85.333007V85.333529A85.07205%2085.07205%200%200%201%20213.463518%200.000522h530.004091l280.528999%20282.355699V939.446472A84.550136%2084.550136%200%200%201%20939.446472%201023.996608H213.463518A84.550136%2084.550136%200%200%201%20128.130511%20939.446472z%20m32.619651-359.599033v38.36071h146.918909l-158.401026%20200.415136v35.229223h225.988943v-38.36071H208.766289l156.574325-199.893221v-35.751138z%20m251.040835%200v274.005069h44.88464v-274.005069z%20m98.641824%200v274.005069h45.145598v-104.382883h67.848874c66.805045%200%20100.207568-28.444336%20100.207568-84.811093s-33.402523-84.028221-99.424697-84.028221z%20m45.145598%2038.36071h65.239302a69.675575%2069.675575%200%200%201%2043.318896%2010.699245%2041.492196%2041.492196%200%200%201%2014.352647%2034.968266%2042.536025%2042.536025%200%200%201-13.830732%2035.751138%2073.589933%2073.589933%200%200%201-43.579854%2010.699245h-65.239302z'%20fill='%23007BEA'%20p-id='23653'%3e%3c/path%3e%3c/svg%3e",audio:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713254049146'%20class='icon'%20viewBox='0%200%201718%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='3440'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='429.5'%20height='256'%3e%3cpath%20d='M1137.358398%20222.43912v-44.412335h-44.412336V0H536.219192c-37.995823%200-66.807224%2028.937216-66.807225%2066.807224v314.534956h22.143261V66.807224c0-24.65954%2020.004423-44.663964%2044.663964-44.663963h534.206167v177.775156h44.663964v44.663964h178.026785v712.610395c0%2024.65954-20.004423%2044.663964-44.663964%2044.663963H536.219192c-24.65954%200-44.663964-20.004423-44.663964-44.663963V470.670107h-22.143261v486.522669c0%2037.995823%2028.937216%2066.807224%2066.807225%2066.807224h712.232952c37.995823%200%2066.807224-28.937216%2066.807224-66.807224v-734.753656H1137.358398z'%20fill='%239FA0A6'%20p-id='3441'%3e%3c/path%3e%3cpath%20d='M558.362452%20890.385551h667.946431v44.663964H558.362452v-44.663964z%20m0-111.21956h667.946431v44.663964H558.362452v-44.663964z%20m0-111.471188h667.946431v44.663963H558.362452v-44.663963z%20m0%200'%20fill='%239FA0A6'%20p-id='3442'%3e%3c/path%3e%3cpath%20d='M803.196461%20111.21956h-356.053569c-24.282099%200-44.412336%2020.004423-44.663963%2044.412336v356.431011c0%2024.65954%2020.004423%2044.663964%2044.663963%2044.663964h356.053569c24.65954%200%2044.663964-20.004423%2044.663964-44.663964v-356.053569c0.125814-24.785354-20.004423-44.789778-44.663964-44.789778zM656.497358%20429.403121c-24.911168%203.019536-44.663964-9.813491-44.663963-30.95024%200-20.381865%2020.381865-40.13466%2044.663963-43.154196%2016.73326-2.138838%2026.420936%203.648605%2026.420937%203.648606V280.690994c0-9.813491-10.694188-6.039071-10.694189-6.039071L580.254085%20304.595651s-10.694188%203.648605-10.694188%2012.833026v97.505836c0%2020.381865-18.243027%2040.13466-43.154196%2043.154196-24.911168%203.648605-44.663964-9.184421-44.663964-29.440472%200-20.381865%2019.752795-40.13466%2044.663964-43.783266%2016.73326-2.138838%2024.407913%203.019536%2024.407912%203.019536v-113.987468c0-12.833026%209.813491-26.420936%2022.772331-30.069542l104.299791-33.089077c12.833026-3.648605%2022.772331%203.648605%2022.772331%2017.362329v158.651432h0.62907l0.125814-0.251628c-0.251628%2020.004423-20.507679%2039.883032-44.915592%2042.902568z'%20fill='%23A40FA8'%20p-id='3443'%3e%3c/path%3e%3c/svg%3e",video:"data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1713253996768'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='2358'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='256'%20height='256'%3e%3cpath%20d='M1000.207102%20340.239099c0-59.250945-14.250227-116.376855-57.125911-161.252571-40.875652-42.875684-79.626269-87.751399-124.501985-122.501953C785.828684%2029.984153%20740.952969%205.483762%20702.202351%203.358729%20555.200007-2.766369%20408.197664%201.358697%20261.320322%201.358697c-40.875652%202.000032-71.50114%2026.500422-73.501171%2065.376042-2.000032%2051.000813%200%20104.12666%200%20155.127473h59.250944c0-42.875684%202.000032-83.751335%200-124.501985%200-24.500391%208.12513-34.750554%2034.750554-34.750554h410.256541c6.125098%200%2012.250195%202.000032%2022.500358%206.125098v224.503579h230.628677c0%20218.378481%200%20432.756899-4.125066%20645.010283%200%2028.625456-20.375325%2026.500422-38.750617%2026.500422H281.695647c-26.500422%200-40.875652-6.125098-38.750618-34.750554%202.000032-10.250163-2.000032-18.375293-2.000032-26.500422h-55.125878c-4.125066%2089.876433%2018.375293%20120.376919%2089.751431%20120.376919H912.455703c61.250976%200%2087.751399-26.500422%2087.751399-87.751399%200-197.878155%202.000032-397.881343%200-595.8845z%20m-226.628613-108.251726V101.360291l130.627082%20130.627082H773.578489z'%20fill='%23B85884'%20p-id='2359'%3e%3c/path%3e%3cpath%20d='M628.701179%20687.244631h234.753743v61.250977H628.701179zM634.826277%20411.615237h228.628645v65.376042H634.826277zM630.701211%20550.492451h234.753743v61.250977H630.701211zM22.441514%20311.613643v536.883559h538.883591V311.613643H22.441514z%20m94.251503%20499.382961H59.942112V754.245699h56.750905v56.750905z%20m0-135.127154H59.942112V619.243547h56.750905v56.625903z%20m0-135.002152H59.942112v-56.750905h56.750905v56.750905z%20m0-135.002153H59.942112v-56.750904h56.750905v56.750904z%20m299.629776%20201.128207l-184.877947%20106.751702c-20.750331%2012.000191-46.750745-3.000048-46.750745-27.000431V473.366222c0-24.000383%2026.000415-39.000622%2046.750745-27.000431l184.877947%20106.751702c20.750331%2012.000191%2020.750331%2041.875668%200%2053.875859z%20m107.501714%20204.003252h-56.750904V754.245699h56.750904v56.750905z%20m0-135.127154h-56.750904V619.243547h56.750904v56.625903z%20m0-135.002152h-56.750904v-56.750905h56.750904v56.750905z%20m0-135.002153h-56.750904v-56.750904h56.750904v56.750904z'%20fill='%23B85884'%20p-id='2360'%3e%3c/path%3e%3c/svg%3e"},Ci={class:"x-attachment__upload"},_i=["onClick"],ki={class:"x-attachment__wrapper"},Ei=["src"],Si=["title"],xi={key:0,class:"x-attachment__item-name"},Bi={class:"el-upload-list__item-url"},Pi={key:0,class:"el-upload-list__item-actions"},Ni=["onClick"],Ti=["onClick"],Di=["onClick"],qr=Vue.defineComponent({name:"XAttachment",__name:"Attachment",props:wi,emits:["click","preview","remove","download","select","change","update:modelValue","update:selectValue"],setup(o,{expose:t,emit:n}){const e=Vt(),r=o,l=n,u={},a=Vue.ref(),i=Vue.ref(Symbol()),s=Vue.ref(!1),c=Vue.ref(0),d=Vue.ref([]),m=Vue.computed(()=>({[`x-attachment--${r.listType}`]:!!r.listType,"is-disabled":!!r.disabled,"is-pointer":!!r.clickable||!!r.selectable,"is-not-add":!r.addable||r.limit&&r.limit<=p.value.length,[`is-${r.size}`]:!!r.size})),p=Vue.ref([]),f=Vue.computed(()=>p.value.filter(k=>Kn(k)).map(k=>k.url)),V=Vue.computed(()=>r.removable||r.previewable||r.downloadable),h=k=>{const E=qn(k);return E==="img"?r.thumbnail&&!k.url.startsWith("blob:")?r.thumbnail(k):k.url:bi[E]},v=Vue.computed(()=>({limit:r.limit,disabled:r.disabled,multiple:r.multiple,accept:r.accept,listType:{card:"picture-card",list:"picture"}[r.listType],beforeUpload:r.beforeUpload,autoUpload:r.autoUpload}));Vue.watch(()=>r.modelValue,async k=>{const E=VtjUtils.cloneDeep(k||[]);p.value=r.formatter?await r.formatter(E):E},{immediate:!0}),Vue.watch(()=>r.selectValue,k=>{d.value=[].concat(VtjUtils.cloneDeep(k||[]))},{immediate:!0});const y=(k,E)=>{ElementPlus.ElMessage.warning({message:`您选择的文件数量超过了限制，只允许上传${r.limit}个文件。`})},b=async(k=[])=>r.valueFormatter?await r.valueFormatter(k):k,w=k=>{if(!r.limitSize)return!0;const E=r.limitSize.toUpperCase(),D=Number.parseFloat(E)||0;return D?E.endsWith("M")?k.size<D*1024**2:k.size<D*1024:!0},_=k=>{const E=p.value.findIndex(D=>D.uid===k.uid);E>-1&&p.value.splice(E,1)},C=async k=>{const E=r.uploader||e.uploader;if(E){const D=k.file;if(!w(D)){_(D),ElementPlus.ElMessage.error({message:`上传文件体积不可超过${r.limitSize}`});return}u[D.uid]=!0;const X=await E(D).catch(()=>null);if(u[D.uid]=!1,!X){_(D),ElementPlus.ElMessage.error({message:`文件${D.name}上传失败。`});return}return typeof X=="string"?{url:X}:X}},g=k=>{const E=d.value.findIndex(D=>D.url===k.url);E>-1?r.multiple?d.value.splice(E,1):d.value=[]:r.multiple?d.value.push(pt(k)):d.value=[pt(k)],l("select",d.value),l("update:selectValue",r.multiple?d.value:d.value[0])},x=k=>{const E=d.value.findIndex(D=>D.url===k.url);E>-1&&d.value.splice(E,1)},S=k=>!!d.value.find(E=>E.url===k.url),I=k=>{r.clickable&&l("click",pt(k)),r.selectable&&g(k)},z=async(k,E)=>{if(E.every(O=>O.status==="success")&&E.length===p.value.length){const O=p.value.map(X=>pt(X));l("change",O),l("update:modelValue",await b(O)),k!=null&&k.response&&(i.value=Symbol())}},N=async k=>{if(!await ElementPlus.ElMessageBox.confirm("确定删除文件?","提示",{type:"warning"}).catch(()=>!1))return;const D=p.value.filter(O=>O.uid!==k.uid||O.url!==k.url).map(O=>pt(O));p.value=D,l("remove",k),l("change",D),l("update:modelValue",await b(D)),x(k),k!=null&&k.raw||(i.value=Symbol())},T=k=>{r.downloader?r.downloader(k):VtjUtils.downloadRemoteFile(k.url,k.name).catch(()=>{VtjUtils.downloadUrl(k.url,k.name)}),l("download",k)},A=k=>{Kn(k)?(c.value=f.value.findIndex(E=>E===k.url),s.value=!0):r.previewer?r.previewer(k):window.open(k.url),l("preview",k)},Y=()=>{s.value=!1};return t({elUploadRef:a,remove:N,download:T,selections:d,fileList:p,upload:()=>{var k;(k=a.value)==null||k.submit()}}),(k,E)=>(Vue.openBlock(),Vue.createElementBlock("div",{class:Vue.normalizeClass(["x-attachment",m.value])},[Vue.createVNode(Vue.unref(ElementPlus.ElUpload),Vue.mergeProps({ref_key:"elUploadRef",ref:a,"file-list":p.value,"onUpdate:fileList":E[0]||(E[0]=D=>p.value=D),"on-exceed":y,"on-change":z,"http-request":C},v.value),{file:Vue.withCtx(({file:D})=>[Vue.withDirectives((Vue.openBlock(),Vue.createElementBlock("div",{class:Vue.normalizeClass(["x-attachment__item",{"is-selected":S(D)}]),onClick:O=>I(D)},[Vue.createElementVNode("div",ki,[Vue.createElementVNode("img",{class:Vue.normalizeClass(["el-upload-list__item-thumbnail",{"is-icon":!Vue.unref(Kn)(D)}]),src:h(D)},null,10,Ei),r.listType!=="card"||D.name?(Vue.openBlock(),Vue.createElementBlock("div",{key:0,class:"el-upload-list__item-name",title:D.url},[D.name?(Vue.openBlock(),Vue.createElementBlock("span",xi,Vue.toDisplayString(D.name),1)):Vue.createCommentVNode("",!0),Vue.createElementVNode("span",Bi,Vue.toDisplayString(D.url),1)],8,Si)):Vue.createCommentVNode("",!0)]),V.value?(Vue.openBlock(),Vue.createElementBlock("div",Pi,[r.previewable?(Vue.openBlock(),Vue.createElementBlock("span",{key:0,onClick:Vue.withModifiers(O=>A(D),["stop"]),class:"el-upload-list__item-preview"},[Vue.createVNode(Vue.unref(ElementPlus.ElIcon),null,{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(VtjIcons.ZoomIn))]),_:1})],8,Ni)):Vue.createCommentVNode("",!0),r.downloadable?(Vue.openBlock(),Vue.createElementBlock("span",{key:1,onClick:Vue.withModifiers(O=>T(D),["stop"]),class:"el-upload-list__item-delete"},[Vue.createVNode(Vue.unref(ElementPlus.ElIcon),null,{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(VtjIcons.Download))]),_:1})],8,Ti)):Vue.createCommentVNode("",!0),r.removable&&!r.disabled?(Vue.openBlock(),Vue.createElementBlock("span",{key:2,onClick:Vue.withModifiers(O=>N(D),["stop"]),class:"el-upload-list__item-delete"},[Vue.createVNode(Vue.unref(ElementPlus.ElIcon),null,{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(VtjIcons.Delete))]),_:1})],8,Di)):Vue.createCommentVNode("",!0)])):Vue.createCommentVNode("",!0)],10,_i)),[[Vue.unref(ElementPlus.vLoading),u[D.uid]]])]),tip:Vue.withCtx(()=>[Vue.renderSlot(k.$slots,"tip")]),default:Vue.withCtx(()=>[Vue.createElementVNode("div",Ci,[Vue.renderSlot(k.$slots,"upload",{},()=>[Vue.createVNode(Vue.unref(ElementPlus.ElIcon),null,{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(VtjIcons.Plus))]),_:1})])])]),_:3},16,["file-list"]),r.previewable&&s.value?(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElImageViewer),{key:0,"hide-on-click-modal":"",teleported:"","url-list":f.value,"initial-index":c.value,onClose:Y},null,8,["url-list","initial-index"])):Vue.createCommentVNode("",!0)],2))}}),zi={uploader:{type:Function},multiple:{type:Boolean},accept:{type:String},parser:{type:[String,Function]}},Kr=Vue.defineComponent({name:"XImportButton",inheritAttrs:!1,__name:"ImportButton",props:zi,emits:["success","fail"],setup(o,{emit:t}){const n=o,e=t,r=async u=>{const{parser:a}=n;return a?typeof a=="function"?await a(u):new Promise((i,s)=>{const c=new FileReader;a==="text"||a==="json"?c.readAsText(u):a==="base64"&&c.readAsDataURL(u),c.onload=()=>{if(a==="json"&&typeof c.result=="string"){let d;try{d=JSON.parse(c.result)}catch(m){s(m)}d&&i(d)}else i(c.result)},c.onerror=d=>{s(d)}}):u},l=async u=>{const{uploader:a}=n,i=await r(u).catch(s=>{ElementPlus.ElMessage.error({message:"导入失败:"+s.message}),e("fail",s)});return i&&a&&await a(i).catch(c=>(ElementPlus.ElMessage.error({message:"导入失败"+(c==null?void 0:c.message)}),e("fail",c),!1))&&(ElementPlus.ElMessage.success({message:"导入成功"}),e("success",i)),!1};return(u,a)=>(Vue.openBlock(),Vue.createBlock(Vue.unref(ElementPlus.ElUpload),{class:"x-import-button",multiple:n.multiple,accept:n.accept,"before-upload":l},{default:Vue.withCtx(()=>[Vue.createVNode(Vue.unref(ElementPlus.ElButton),Vue.normalizeProps(Vue.guardReactiveProps(u.$attrs)),{default:Vue.withCtx(()=>[Vue.renderSlot(u.$slots,"default",{},()=>[a[0]||(a[0]=Vue.createTextVNode("导入",-1))])]),_:3},16)]),_:3},8,["multiple","accept"]))}}),Ii=[de,Se,Q,ze,j,Xt,Le,Xe,ho,Ft,Ot,Qo,er,nr,rr,ar,ir,cr,dr,Hr,Xr,qr,yn,At,Rt,Kr],{install:Mi}=Jn(Ii);B.ADAPTER_KEY=mt,B.AdapterPlugin=Gr,B.Draggable=qt,B.INSTALLED_KEY=Lt,B.MASK_KEY=Jt,B.Resizable=Yt,B.TAB_CREATORS_KEY=Wt,B.TAB_ITEM_WIDTH=fo,B.VTJ_UI_VERSION=ie,B.XAction=Q,B.XActionBar=ze,B.XAttachment=qr,B.XCaptcha=dr,B.XContainer=j,B.XDataItem=nr,B.XDialog=Xe,B.XDialogForm=Qo,B.XDialogGrid=Ho,B.XField=Ft,B.XForm=Ot,B.XGrid=At,B.XGridEditor=vn,B.XHeader=Xt,B.XIcon=de,B.XImportButton=Kr,B.XInputUnit=cr,B.XList=rr,B.XMask=ho,B.XMenu=Se,B.XPanel=Le,B.XPicker=Rt,B.XQrCode=Hr,B.XQueryForm=yn,B.XStartup=ar,B.XTabs=er,B.XTest=ir,B.XVerify=Xr,B.actionBarProps=ro,B.actionProps=Ht,B.autoUpdate=el,B.builtinFieldEditors=$t,B.containerProps=lo,B.createDialog=gt,B.dataItemProps=tr,B.defineTab=Ul,B.dialogFormProps=Jo,B.dialogProps=co,B.fieldProps=Go,B.formInstanceKey=wn,B.formModelKey=bn,B.formProps=Wo,B.getSizeValue=Ve,B.headerProps=ao,B.iconProps=Wn,B.iconSizeMap=Te,B.inputUnitProps=sr,B.install=Mi,B.listProps=or,B.makeInstaller=Jn,B.maskProps=po,B.panelProps=uo,B.parseSize=tt,B.registerFieldEditors=Yo,B.sharedFilterOptions=Ie,B.startupProps=lr,B.tabsProps=Zo,B.testProps=ur,B.toObjectProps=Ut,B.useAdapter=Vt,B.useDefer=il,B.useDefineSlots=oo,B.useDisabled=ht,B.useEditRender=Fe,B.useIcon=De,B.useIconProps=to,B.useLoader=no,B.vDraggable=so,B.vResizable=Gt,Object.defineProperty(B,Symbol.toStringTag,{value:"Module"})});
