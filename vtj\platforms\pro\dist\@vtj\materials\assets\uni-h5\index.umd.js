(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.UniH5Material=e())})(this,function(){"use strict";var L=Object.defineProperty,G=Object.defineProperties;var R=Object.getOwnPropertyDescriptors;var r=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,q=Object.prototype.propertyIsEnumerable;var s=(t,e,a)=>e in t?L(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,n=(t,e)=>{for(var a in e||(e={}))U.call(e,a)&&s(t,a,e[a]);if(r)for(var a of r(e))q.call(e,a)&&s(t,a,e[a]);return t},o=(t,e)=>G(t,R(e));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const t="0.12.70";function e(H,O){return H.map(D=>o(n({},D),{package:O}))}const a={name:"View",label:"视图",categoryId:"container",props:[{name:"hover-class",defaultValue:"none",title:'指定按下去的样式类。当 hover-class="none" 时，没有点击态效果',setters:"InputSetter"},{name:"hover-stop-propagation",defaultValue:!1,title:"指定是否阻止本节点的祖先节点出现点击态，App、H5、支付宝小程序、百度小程序不支持（支付宝小程序、百度小程序文档中都有此属性，实测未支持）",setters:"BooleanSetter"},{name:"hover-start-time",defaultValue:50,title:"按住后多久出现点击态，单位毫秒",setters:"NumberSetter"},{name:"hover-stay-time",defaultValue:400,title:"手指松开后点击态保留时间，单位毫秒",setters:"NumberSetter"}],snippet:{children:"视图内容"}},i={name:"ScrollView",label:"滚动视图",categoryId:"container",props:[{name:"scroll-x",title:"允许横向滚动",defaultValue:!1,setters:"BooleanSetter"},{name:"scroll-y",title:"允许纵向滚动",defaultValue:!1,setters:"BooleanSetter"},{name:"upper-threshold",title:"距顶部/左边多远时（单位px），触发 scrolltoupper 事件",defaultValue:50,setters:["NumberSetter","StringSetter"]},{name:"lower-threshold",title:"距底部/右边多远时（单位px），触发 scrolltolower 事件",defaultValue:50,setters:["NumberSetter","StringSetter"]},{name:"scroll-top",title:"设置竖向滚动条位置",setters:["NumberSetter","StringSetter"]},{name:"scroll-left",title:"设置横向滚动条位置",setters:["NumberSetter","StringSetter"]},{name:"scroll-into-view",title:"值应为某子元素id（id不能以数字开头）。设置哪个方向可滚动，则在哪个方向滚动到该元素",setters:"StringSetter"},{name:"scroll-with-animation",title:"在设置滚动条位置时使用动画过渡",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-back-to-top",title:"iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只支持竖向",defaultValue:!1,setters:"BooleanSetter"},{name:"show-scrollbar",title:"控制是否出现滚动条",defaultValue:!1,setters:"BooleanSetter"},{name:"refresher-enabled",title:"开启自定义下拉刷新",defaultValue:!1,setters:"BooleanSetter"},{name:"refresher-threshold",title:"设置自定义下拉刷新阈值",defaultValue:45,setters:"NumberSetter"},{name:"refresher-default-style",title:"设置自定义下拉刷新默认样式",defaultValue:"black",setters:"SelectSetter",options:["black","white","none","none"]},{name:"refresher-background",title:"设置自定义下拉刷新区域背景颜色",defaultValue:"#FFF",setters:"ColorSetter"},{name:"refresher-triggered",title:"设置当前下拉刷新状态",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-flex",title:"启用 flexbox 布局",defaultValue:!1,setters:"BooleanSetter"},{name:"scroll-anchoring",title:"开启 scroll anchoring 特性，即控制滚动位置不随内容变化而抖动",defaultValue:!1,setters:"BooleanSetter"}],events:["scrolltoupper","scrolltolower","scroll","refresherpulling","refresherrefresh","refresherrestore","refresherabort"],snippet:{props:{"scroll-y":!0,"scroll-top":0,style:{height:"300px"}},children:[{name:"View",props:{id:"demo1",class:"scroll-view-item uni-bg-red",style:{"min-height":"300px",background:"red"}},children:"A"},{name:"View",props:{id:"demo2",class:"scroll-view-item uni-bg-green",style:{"min-height":"300px",background:"green"}},children:"B"},{name:"View",props:{id:"demo3",class:"scroll-view-item uni-bg-blue",style:{"min-height":"300px",background:"blue"}},children:"C"}]}},u=[{name:"Swiper",label:"滑块视图容器",categoryId:"container",props:[{name:"indicator-dots",title:"是否显示面板指示点",defaultValue:!1,setters:"BooleanSetter"},{name:"indicator-color",title:"指示点颜色",defaultValue:"rgba(0, 0, 0, .3)",setters:"ColorSetter"},{name:"indicator-active-color",title:"当前选中的指示点颜色",defaultValue:"#000000",setters:"ColorSetter"},{name:"active-class",title:"swiper-item 可见时的 class",setters:"StringSetter"},{name:"changing-class",title:"acceleration 设置为 true 时且处于滑动过程中，中间若干屏处于可见时的class",setters:"StringSetter"},{name:"autoplay",title:"是否自动切换",defaultValue:!1,setters:"BooleanSetter"},{name:"current",title:"当前所在滑块的 index",defaultValue:0,setters:"NumberSetter"},{name:"current-item-id",title:"当前所在滑块的 item-id ，不能与 current 被同时指定",setters:"StringSetter"},{name:"interval",title:"自动切换时间间隔",defaultValue:5e3,setters:"NumberSetter"},{name:"duration",title:"滑动动画时长",defaultValue:500,setters:"NumberSetter"},{name:"circular",title:"是否采用衔接滑动，即播放到末尾后重新回到开头",defaultValue:!1,setters:"BooleanSetter"},{name:"vertical",title:"滑动方向是否为纵向",defaultValue:!1,setters:"BooleanSetter"},{name:"previous-margin",title:"前边距，可用于露出前一项的一小部分，接受 px 和 rpx 值",defaultValue:"0px",setters:"StringSetter"},{name:"next-margin",title:"后边距，可用于露出后一项的一小部分，接受 px 和 rpx 值",defaultValue:"0px",setters:"StringSetter"},{name:"acceleration",title:"当开启时，会根据滑动速度，连续滑动多屏",defaultValue:!1,setters:"BooleanSetter"},{name:"disable-programmatic-animation",title:"是否禁用代码变动触发 swiper 切换时使用动画",defaultValue:!1,setters:"BooleanSetter"},{name:"display-multiple-items",title:"同时显示的滑块数量",defaultValue:1,setters:"NumberSetter"},{name:"skip-hidden-item-layout",title:"是否跳过未显示的滑块布局，设为 true 可优化复杂情况下的滑动性能，但会丢失隐藏状态滑块的布局信息",defaultValue:!1,setters:"BooleanSetter"},{name:"disable-touch",title:"是否禁止用户 touch 操作",defaultValue:!1,setters:"BooleanSetter"},{name:"touchable",title:"是否监听用户的触摸事件，只在初始化时有效，不能动态变更",defaultValue:!0,setters:"BooleanSetter"},{name:"easing-function",title:"指定 swiper 切换缓动动画类型",defaultValue:"default",setters:"SelectSetter",options:["default","linear","easeInCubic","easeOutCubic","easeInOutCubic"]}],events:["change","transition","animationfinish"],snippet:{props:{autoplay:!0,indicatorDots:!0,circular:!0,style:{color:"#fff",height:"200px"}},children:[{name:"SwiperItem",props:{style:{background:"#f76260",textAlign:"center",color:"#fff",height:"200px",lineHeight:"200px"}},children:"A"},{name:"SwiperItem",props:{style:{background:"#09bb07",textAlign:"center",color:"#fff",height:"200px",lineHeight:"200px"}},children:"B"},{name:"SwiperItem",props:{style:{background:"#007aff",textAlign:"center",color:"#fff",height:"200px",lineHeight:"200px"}},children:"C"}]}},{name:"SwiperItem",label:"滑块视图容器",parentIncludes:["Swiper"],categoryId:"container",props:[{name:"item-id",title:"该 swiper-item 的标识符",setters:"InputSetter"}],snippet:{children:[{name:"View",props:{style:{height:"300px",background:"green"}},children:"A"}]}}],d={name:"MatchMedia",label:"适配大屏小屏",categoryId:"container",props:[{name:"min-width",title:"页面最小宽度（ px 为单位）",setters:"NumberSetter"},{name:"max-width",title:"页面最大宽度（ px 为单位）",setters:"NumberSetter"},{name:"width",title:"页面宽度（ px 为单位）",setters:"NumberSetter"},{name:"min-height",title:"页面最小高度（ px 为单位）",setters:"NumberSetter"},{name:"max-height",title:"页面最大高度（ px 为单位）",setters:"NumberSetter"},{name:"height",title:"页面高度（ px 为单位）",setters:"NumberSetter"},{name:"orientation",title:"屏幕方向",setters:"SelectSetter",options:["landscape","portrait"]}],snippet:{props:{"min-width":"375","max-width":"800"},children:[{name:"View",children:"当页面最小宽度 375px， 页面宽度最大 800px 时显示"}]}},m={name:"MovableArea",label:"可拖动区域",categoryId:"container",props:[{name:"scale-area",title:"当里面的 movable-view 设置为支持双指缩放时，设置此值可将缩放手势生效区域修改为整个 movable-area",defaultValue:!1,setters:"BooleanSetter"},{name:"width",title:"宽度 *",defaultValue:"10px",setters:"StringSetter"},{name:"height",title:"高度 *",defaultValue:"10px",setters:"StringSetter"}],snippet:{}},c={name:"MovableView",label:"可移动的视图容器",categoryId:"container",parentIncludes:["MovableArea"],props:[{name:"direction",title:"movable-view的移动方向",defaultValue:"none",setters:"SelectSetter",options:["all","vertical","horizontal","none"]},{name:"inertia",title:"movable-view是否带有惯性",defaultValue:!1,setters:"BooleanSetter"},{name:"out-of-bounds",title:"超过可移动区域后，movable-view是否还可以移动",defaultValue:!1,setters:"BooleanSetter"},{name:"x",title:"定义x轴方向的偏移",setters:["NumberSetter","StringSetter"]},{name:"y",title:"定义y轴方向的偏移",setters:["NumberSetter","StringSetter"]},{name:"damping",title:"阻尼系数",defaultValue:20,setters:"NumberSetter"},{name:"friction",title:"摩擦系数",defaultValue:2,setters:"NumberSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"scale",title:"是否支持双指缩放",defaultValue:!1,setters:"BooleanSetter"},{name:"scale-min",title:"定义缩放倍数最小值",defaultValue:.1/.5,setters:"NumberSetter"},{name:"scale-max",title:"定义缩放倍数最大值",defaultValue:10,setters:"NumberSetter"},{name:"scale-value",title:"定义缩放倍数，取值范围为 0.1/0.5 - 10",defaultValue:1,setters:"NumberSetter"},{name:"animation",title:"是否使用动画",defaultValue:!0,setters:"BooleanSetter"}],events:["change","scale"],snippet:{props:{x:0,y:0,direction:"all"}}},p={name:"CoverView",label:"文本视图",categoryId:"container",props:[{name:"scroll-top",title:"设置顶部滚动偏移量，仅在设置了 overflow-y: scroll 成为滚动元素后生效",setters:["NumberSetter","StringSetter"]}],events:["click"],snippet:{}},f={name:"CoverImage",label:"图片视图",categoryId:"container",props:[{name:"src",title:"图标路径。支持本地路径、网络路径。不支持 base64 格式。",setters:"StringSetter"}],events:["load","error","click"],snippet:{}},S={name:"Icon",label:"图标",categoryId:"basic",props:[{name:"type",title:"icon的类型",setters:"SelectSetter",options:["success","success_no_circle","info","warn","waiting","cancel","download","search","clear"]},{name:"size",title:"icon的大小， 单位px",defaultValue:23,setters:"NumberSetter"},{name:"color",title:"icon的颜色，同css的color",setters:"ColorSetter"}],snippet:{props:{type:"success",size:26}}},h={name:"Text",label:"文本",categoryId:"basic",props:[{name:"selectable",title:"文本是否可选",defaultValue:!1,setters:"BooleanSetter"},{name:"user-select",title:"文本是否可选",defaultValue:!1,setters:"BooleanSetter"},{name:"space",title:"显示连续空格",setters:["SelectSetter","StringSetter"],options:["ensp","emsp","nbsp"]},{name:"decode",title:"是否解码",defaultValue:!1,setters:"BooleanSetter"}],snippet:{children:"VTJ"}},g={name:"RichText",label:"文本",categoryId:"basic",props:[{name:"nodes",title:"节点列表 / HTML String",defaultValue:[],setters:"ArraySetter"},{name:"space",title:"显示连续空格",setters:"StringSetter"},{name:"selectable",title:"富文本是否可以长按选中，可用于复制，粘贴等场景",defaultValue:!0,setters:"BooleanSetter"},{name:"image-menu-prevent",title:"阻止长按图片时弹起默认菜单",defaultValue:!1,setters:"BooleanSetter"},{name:"preview",title:"富文本中的图片是否可点击预览",setters:"BooleanSetter"}],events:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],snippet:{props:{nodes:[{name:"div",attrs:{class:"div-class",style:"line-height: 60px; color: red; text-align:center;"},children:[{type:"text",text:"Hello&nbsp;uni-app!"}]}]}}},b={name:"Progress",label:"进度条",categoryId:"basic",props:[{name:"percent",title:"百分比0~100",setters:"NumberSetter"},{name:"show-info",title:"在进度条右侧显示百分比",defaultValue:!1,setters:"BooleanSetter"},{name:"border-radius",title:"圆角大小",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"font-size",title:"右侧百分比字体大小",defaultValue:16,setters:["NumberSetter","StringSetter"]},{name:"stroke-width",title:"进度条线的宽度，单位px",defaultValue:6,setters:"NumberSetter"},{name:"activeColor",title:"已选择的进度条的颜色",defaultValue:"#09BB07",setters:"ColorSetter"},{name:"backgroundColor",title:"未选择的进度条的颜色",defaultValue:"#EBEBEB",setters:"ColorSetter"},{name:"active",title:"进度条从左往右的动画",defaultValue:!1,setters:"BooleanSetter"},{name:"active-mode",title:"backwards: 动画从头播；forwards：动画从上次结束点接着播",defaultValue:"backwards",setters:"SelectSetter",options:["backwards","forwards"]},{name:"duration",title:"进度增加1%所需毫秒数",defaultValue:30,setters:"NumberSetter"}],events:["activeend"],snippet:{props:{percent:10,"show-info":!0,"stroke-width":"3"}}},V={name:"Button",label:"按钮",categoryId:"form",props:[{name:"size",title:"按钮的大小",defaultValue:"default",setters:"SelectSetter",options:["default","mini"]},{name:"type",title:"按钮的样式类型",defaultValue:"default",setters:"SelectSetter",options:["primary","default","warn"]},{name:"plain",title:"按钮是否镂空，背景色透明",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",title:"名称前是否带 loading 图标",defaultValue:!1,setters:"BooleanSetter"},{name:"form-type",title:"用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件",setters:"SelectSetter",options:["submit","reset"]},{name:"open-type",title:"开放能力",setters:"SelectSetter",options:["feedback","share","getUserInfo","contact","getPhoneNumber","launchApp","openSetting","chooseAvatar","agreePrivacyAuthorization","uploadDouyinVideo","im","getAuthorize","lifestyle","contactShare","openGroupProfile","openGuildProfile","openPublicProfile","shareMessageToFriend","addFriend","addColorSign","addGroupApp","addToFavorites","chooseAddress","chooseInvoiceTitle","login","subscribe","favorite","watchLater","openProfile"]},{name:"hover-class",title:"指定按钮按下去的样式类",defaultValue:"button-hover",setters:"StringSetter"},{name:"hover-start-time",title:"按住后多久出现点击态，单位毫秒",defaultValue:20,setters:"NumberSetter"},{name:"hover-stay-time",title:"手指松开后点击态保留时间，单位毫秒",defaultValue:70,setters:"NumberSetter"},{name:"app-parameter",title:"打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效",setters:"StringSetter"},{name:"hover-stop-propagation",title:"指定是否阻止本节点的祖先节点出现点击态",defaultValue:!1,setters:"BooleanSetter"},{name:"lang",title:"指定返回用户信息的语言",defaultValue:"en",setters:"SelectSetter",options:["en","zh_CN","zh_TW"]},{name:"session-from",title:'会话来源，open-type="contact"时有效',setters:"StringSetter"},{name:"send-message-title",title:'会话内消息卡片标题，open-type="contact"时有效',setters:"StringSetter"},{name:"send-message-path",title:'会话内消息卡片点击跳转小程序路径，open-type="contact"时有效',setters:"StringSetter"},{name:"send-message-img",title:'会话内消息卡片图片，open-type="contact"时有效',setters:"StringSetter"},{name:"show-message-card",title:"是否显示会话内消息卡片",defaultValue:!1,setters:"BooleanSetter"},{name:"group-id",title:"打开群资料卡时，传递的群号",setters:"StringSetter"},{name:"guild-id",title:"打开频道页面时，传递的频道号",setters:"StringSetter"},{name:"public-id",title:"打开公众号资料卡时，传递的号码",setters:"StringSetter"},{name:"data-im-id",title:"客服的抖音号",setters:"StringSetter"},{name:"data-im-type",title:"IM卡片类型",setters:"StringSetter"},{name:"data-goods-id",title:"商品的id",setters:"StringSetter"},{name:"data-order-id",title:"订单的id，仅支持交易2.0订单",setters:"StringSetter"},{name:"data-biz-line",title:"商品类型，“1”代表生活服务，“2”代表泛知识。",setters:"StringSetter"}],events:["getphonenumber","getuserinfo","error","opensetting","launchapp","contact","chooseavatar","agreeprivacyauthorization","addgroupapp","chooseaddress","chooseinvoicetitle","subscribe","login","im"],snippet:{children:"Button"}},v=[{name:"CheckboxGroup",label:"多选框组",categoryId:"form",events:["change"],snippet:{children:[{name:"Checkbox",props:{checked:!0},children:"选中"},{name:"Checkbox",props:{checked:!1},children:"未选中"}]}},{name:"Checkbox",label:"多选项",categoryId:"form",props:[{name:"value",title:"<checkbox> 标识，选中时触发 <checkbox-group> 的 change 事件，并携带 <checkbox> 的 value。",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"checked",title:"当前是否选中，可用来设置默认选中",defaultValue:!1,setters:"BooleanSetter"},{name:"color",title:"checkbox的颜色，同css的color",setters:"ColorSetter"},{name:"backgroundColor",title:"checkbox默认的背景颜色",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"borderColor",title:"checkbox默认的边框颜色",defaultValue:"#d1d1d1",setters:"ColorSetter"},{name:"activeBackgroundColor",title:"checkbox选中时的背景颜色，优先级大于color属性",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"activeBorderColor",title:"checkbox选中时的边框颜色",defaultValue:"#d1d1d1",setters:"ColorSetter"},{name:"iconColor",title:"checkbox的图标颜色",defaultValue:"#007aff",setters:"ColorSetter"}],snippet:{children:"选项"}}],y={name:"Editor",label:"富文本编辑器",categoryId:"form",props:[{name:"read-only",title:"设置编辑器为只读",defaultValue:!1,setters:"BooleanSetter"},{name:"placeholder",title:"提示信息",setters:"BooleanSetter"},{name:"show-img-size",title:"点击图片时显示图片大小控件",defaultValue:!1,setters:"BooleanSetter"},{name:"show-img-toolbar",title:"点击图片时显示工具栏控件",defaultValue:!1,setters:"BooleanSetter"},{name:"show-img-resize",title:"点击图片时显示修改尺寸控件",defaultValue:!1,setters:"BooleanSetter"}],events:["ready","focus","blur","input","statuschange"],snippet:{props:{placeholder:"开始输入...","show-img-size":!0,"show-img-toolbar":!0,"show-img-resize":!0}}},B={name:"Form",label:"表单",categoryId:"form",props:[{name:"report-submit",title:"是否返回 formId 用于发送模板消息",setters:"BooleanSetter"},{name:"report-submit-timeout",title:"等待一段时间（毫秒数）以确认 formId 是否生效",setters:"NumberSetter"}],events:["submit","reset"],snippet:{children:[{name:"Label",children:[{name:"View",children:"用户名"},{name:"Input",props:{style:{height:"40px",backgroundColor:"#f0f0f0"},placeholder:"请输入用户名"}}]},{name:"Label",children:[{name:"View",children:"密码"},{name:"Input",props:{style:{height:"40px",backgroundColor:"#f0f0f0"},placeholder:"请输入密码"}}]},{name:"Button",children:"提交"}]}},w={name:"Input",label:"单行输入框",categoryId:"form",props:[{name:"modelValue",title:"输入框的初始内容",setters:"StringSetter"},{name:"type",title:"input 的类型 有效值",defaultValue:"text",setters:"SelectSetter",options:["text","number","idcard","digit","tel","safe-password","nickname"]},{name:"text-content-type",title:"文本区域的语义，根据类型自动填充 有效值",setters:"StringSetter"},{name:"password",title:"是否是密码类型",defaultValue:!1,setters:"BooleanSetter"},{name:"placeholder",title:"输入框为空时占位符",setters:"StringSetter"},{name:"placeholder-style",title:"指定 placeholder 的样式",setters:"StringSetter"},{name:"placeholder-class",title:"指定 placeholder 的样式类",defaultValue:"input-placeholder",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"maxlength",title:"最大输入长度，设置为 -1 的时候不限制最大长度",defaultValue:140,setters:"NumberSetter"},{name:"cursor-spacing",title:"指定光标与键盘的距离",defaultValue:0,setters:"NumberSetter"},{name:"focus",title:"获取焦点",defaultValue:!1,setters:"BooleanSetter"},{name:"confirm-type",title:'设置键盘右下角按钮的文字，仅在 type="text" 时生效。有效值',defaultValue:"done",setters:"SelectSetter",options:["send","search","next","go","done"]},{name:"confirm-hold",title:"点击键盘右下角按钮时是否保持键盘不收起",defaultValue:!1,setters:"BooleanSetter"},{name:"cursor",title:"指定focus时的光标位置",setters:"NumberSetter"},{name:"cursor-color",title:"光标颜色",setters:"StringSetter"},{name:"selection-start",title:"光标起始位置，自动聚集时有效，需与selection-end搭配使用",defaultValue:-1,setters:"NumberSetter"},{name:"selection-end",title:"光标结束位置，自动聚集时有效，需与selection-start搭配使用",defaultValue:-1,setters:"NumberSetter"},{name:"adjust-position",title:"键盘弹起时，是否自动上推页面",defaultValue:!0,setters:"BooleanSetter"},{name:"auto-blur",title:"键盘收起时，是否自动失去焦点",defaultValue:!1,setters:"BooleanSetter"},{name:"ignoreCompositionEvent",title:"是否忽略组件内对文本合成系统事件的处理",defaultValue:!0,setters:"BooleanSetter"},{name:"always-embed",title:"强制 input 处于同层状态，默认 focus 时 input 会切到非同层状态 (仅在 iOS 下生效)",defaultValue:!1,setters:"BooleanSetter"},{name:"hold-keyboard",title:"focus时，点击页面的时候不收起键盘",defaultValue:!1,setters:"BooleanSetter"},{name:"safe-password-cert-path",title:"安全键盘加密公钥的路径，只支持包内路径",setters:"StringSetter"},{name:"safe-password-length",title:"安全键盘输入密码长度",setters:"NumberSetter"},{name:"safe-password-time-stamp",title:"安全键盘加密时间戳",setters:"NumberSetter"},{name:"safe-password-nonce",title:"安全键盘加密盐值",setters:"StringSetter"},{name:"safe-password-salt",title:"安全键盘计算 hash 盐值，若指定custom-hash 则无效",setters:"StringSetter"},{name:"safe-password-custom-hash",title:"安全键盘计算 hash 的算法表达式",setters:"StringSetter"},{name:"random-number",title:"当 type 为 number, digit, idcard 数字键盘是否随机排列",defaultValue:!1,setters:"BooleanSetter"},{name:"controlled",title:"是否为受控组件。为 true 时，value 内容会完全受 setData 控制",defaultValue:!1,setters:"BooleanSetter"},{name:"always-system",title:"是否强制使用系统键盘和 Web-view 创建的 input 元素。为 true 时，confirm-type、confirm-hold 可能失效",defaultValue:!1,setters:"BooleanSetter"},{name:"inputmode",title:"是一个枚举属性，它提供了用户在编辑元素或其内容时可能输入的数据类型的提示",defaultValue:"text",setters:"SelectSetter",options:["none","text","decimal","numeric","tel","search","email","url"]}],events:["input","focus","blur","confirm","update:modelValue"],snippet:{props:{placeholder:"这个是一个输入框"}}},x={name:"Label",label:"标签",categoryId:"form",props:[{name:"for",title:"绑定控件的 id",setters:"StringSetter"}],snippet:{}},k={name:"Picker",label:"选择器",categoryId:"form",props:[{name:"mode",title:"模式",defaultValue:"selector",setters:"SelectSetter",options:["selector","multiSelector","time","date","region"]},{name:"range",title:"mode为 selector 或 multiSelector 时，range 有效",defaultValue:[],setters:"ArraySetter"},{name:"range-key",title:"当 range 是一个 Array＜Object＞ 时，通过 range-key 来指定 Object 中 key 的值作为选择器显示内容",setters:"StringSetter"},{name:"value",title:"value 的值表示选择了 range 中的第几个（下标从 0 开始）",defaultValue:0,setters:"NumberSetter"},{name:"selector-type",title:"UI类型,仅大屏时该属性生效，支持 picker、select、auto，默认在 iPad 以 picker 样式展示而在 PC 以 select 样式展示",defaultValue:"auto",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"start",title:'表示有效时间范围的开始，字符串格式为"hh:mm"',setters:"StringSetter"},{name:"end",title:'表示有效时间范围的结束，字符串格式为"hh:mm"',setters:"StringSetter"},{name:"fields",title:"有效值 year、month、day，表示选择器的粒度，默认为 day，App 端未配置此项时使用系统 UI",defaultValue:"day",setters:"SelectSetter",options:["year","month","day"]},{name:"custom-item",title:"可为每一列的顶部添加一个自定义的项",setters:"StringSetter"}],events:["change","cancel","columnchange"],snippet:{props:{mode:"selector",range:["中国","美国","巴西","日本"],value:0,children:[{name:"View",children:"中国"}]}}},N=[{name:"PickerView",label:"选择器",categoryId:"form",props:[{name:"value",title:"选中的项",defaultValue:[],setters:"ArraySetter"},{name:"indicator-style",title:"设置选择器中间选中框的样式",setters:"StringSetter"},{name:"indicator-class",title:"设置选择器中间选中框的类名",setters:"StringSetter"},{name:"mask-style",title:"设置蒙层的样式",setters:"StringSetter"},{name:"mask-top-style",title:"设置蒙层上半部分的样式",setters:"StringSetter"},{name:"mask-bottom-style",title:"设置蒙层下半部分的样式",setters:"StringSetter"},{name:"mask-class",title:"设置蒙层的类名",setters:"StringSetter"},{name:"immediate-change",title:"是否在手指松开时立即触发 change 事件。若不开启则会在滚动动画结束后触发 change 事件。",setters:"BooleanSetter"}],events:["change","pickstart","pickend"],snippet:{props:{value:[9999,10,10],"indicator-style":"height: 50px;",style:{width:" 100%",height:"300px",marginTop:"20px"}},children:[{name:"PickerViewColumn",children:[{name:"View",props:{style:{"text-align":"center"}},children:{type:"JSExpression",value:"this.context.item"},directives:[{name:"vFor",value:{type:"JSExpression",value:'["2021年","2022年","2023年","2024年"]'}}]}]},{name:"PickerViewColumn",children:[{name:"View",props:{style:{"text-align":"center"}},children:{type:"JSExpression",value:"this.context.item"},directives:[{name:"vFor",value:{type:"JSExpression",value:'["3月","4月","5月","6月", "7月"]'}}]}]},{name:"PickerViewColumn",children:[{name:"View",props:{style:{"text-align":"center"}},children:{type:"JSExpression",value:"this.context.item"},directives:[{name:"vFor",value:{type:"JSExpression",value:'["3号","4号","5号","6号", "7号"]'}}]}]}]}},{name:"PickerViewColumn",label:"选择器列",parentIncludes:["PickerView"],categoryId:"form",snippet:{children:[{name:"View",children:{type:"JSExpression",value:"this.context.item"},directives:[{name:"vFor",value:{type:"JSExpression",value:'["2021年","2022年","2023年","2024年"]'}}]}]}}],I=[{name:"RadioGroup",label:"单项选择器",categoryId:"form",events:["change"],snippet:{props:{},children:[{name:"Label",props:{key:{type:"JSExpression",value:"this.context.item.value"},style:{display:"flex",flexDirection:"row"}},children:[{name:"View",children:[{name:"Radio",props:{value:{type:"JSExpression",value:"this.context.item.value"},checked:"this.context.index === 0"}}]},{name:"View",children:{type:"JSExpression",value:"this.context.item.name"}}],directives:[{name:"vFor",value:{type:"JSExpression",value:"[{value: 'USA',name: '美国',checked: 'true'},{value: 'CHN',name:'中国'},{value: 'BRA',	name: '巴西'},{value: 'JPN',name: '日本'},{value: 'ENG',name: '英国'},{value: 'FRA',name: '法国'}]"}}]}]}},{name:"Radio",label:"单选项目",categoryId:"form",props:[{name:"value",title:"<radio> 标识。当该 <radio> 选中时，<radio-group> 的 change 事件会携带 <radio> 的 value",setters:"StringSetter"},{name:"checked",title:"当前是否选中",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"color",title:"radio的颜色，同css的color",setters:"ColorSetter"},{name:"backgroundColor",title:"radio默认的背景颜色",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"borderColor",title:"radio默认的边框颜色",defaultValue:"#d1d1d1",setters:"ColorSetter"},{name:"activeBackgroundColor",title:"radio选中时的背景颜色，优先级大于color属性",defaultValue:"#007AFF",setters:"ColorSetter"},{name:"activeBorderColor",title:"radio选中时的边框颜色",setters:"ColorSetter"},{name:"iconColor",title:"radio的图标颜色",defaultValue:"#ffffff",setters:"ColorSetter"}],snippet:{props:{checked:!1},children:"选项"}}],C={name:"Slider",label:"滑动选择器",categoryId:"form",props:[{name:"min",title:"最小值",defaultValue:0,setters:"NumberSetter"},{name:"max",title:"最大值",defaultValue:"100",setters:"NumberSetter"},{name:"step",title:"步长，取值必须大于 0，并且可被(max - min)整除",defaultValue:1,setters:"NumberSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"value",title:"当前取值",defaultValue:0,setters:"NumberSetter"},{name:"activeColor",title:"滑块左侧已选择部分的线条颜色",setters:"ColorSetter"},{name:"backgroundColor",title:"滑块右侧背景条的颜色",defaultValue:"#e9e9e9",setters:"ColorSetter"},{name:"block-size",title:"滑块的大小，取值范围为 12 - 28",defaultValue:28,setters:"NumberSetter"},{name:"block-color",title:"滑块的颜色",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"show-value",title:"是否显示当前 value",defaultValue:!1,setters:"BooleanSetter"}],events:["change","changing"],snippet:{props:{value:60}}},A={name:"Switch",label:"开关选择器",categoryId:"form",props:[{name:"checked",title:"是否选中",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"type",title:"样式",defaultValue:"switch",setters:"SelectSetter",options:["switch","checkbox"]},{name:"color",title:"switch 的颜色",setters:"ColorSetter"}],events:["change"]},$={name:"Textarea",label:"多行输入框",categoryId:"form",props:[{name:"value",title:"输入框的内容",setters:"StringSetter"},{name:"placeholder",title:"输入框为空时占位符",setters:"StringSetter"},{name:"placeholder-style",title:"指定 placeholder 的样式",setters:"StringSetter"},{name:"placeholder-class",title:"指定 placeholder 的样式类",defaultValue:"textarea-placeholder",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"maxlength",title:"最大输入长度，设置为 -1 的时候不限制最大长度",defaultValue:140,setters:"NumberSetter"},{name:"focus",title:"获取焦点",defaultValue:!1,setters:"BooleanSetter"},{name:"auto-focus",title:"自动聚焦，拉起键盘",defaultValue:!1,setters:"BooleanSetter"},{name:"auto-height",title:"是否自动增高，设置auto-height时，style.height不生效",defaultValue:!1,setters:"BooleanSetter"},{name:"fixed",title:"如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true",defaultValue:!1,setters:"BooleanSetter"},{name:"cursor-spacing",title:"指定光标与键盘的距离，单位 px",defaultValue:0,setters:"NumberSetter"},{name:"cursor",title:"指定focus时的光标位置",setters:"NumberSetter"},{name:"cursor-color",title:"光标颜色",setters:"StringSetter"},{name:"confirm-type",title:"设置键盘右下角按钮的文字",defaultValue:"done",setters:"SelectSetter",options:["send","search","next","go","done"]},{name:"confirm-hold",title:"点击键盘右下角按钮时是否保持键盘不收起",defaultValue:!1,setters:"BooleanSetter"},{name:"show-confirm-bar",title:"是否显示键盘上方带有”完成“按钮那一栏",defaultValue:!0,setters:"BooleanSetter"},{name:"selection-start",title:"光标起始位置，自动聚焦时有效，需与selection-end搭配使用",defaultValue:-1,setters:"NumberSetter"},{name:"selection-end",title:"光标结束位置，自动聚焦时有效，需与selection-end搭配使用",defaultValue:-1,setters:"NumberSetter"},{name:"adjust-position",title:"键盘弹起时，是否自动上推页面",defaultValue:!0,setters:"BooleanSetter"},{name:"disable-default-padding",title:"是否去掉 iOS 下的默认内边距",defaultValue:!1,setters:"BooleanSetter"},{name:"hold-keyboard",title:"focus时，点击页面的时候不收起键盘",defaultValue:!1,setters:"BooleanSetter"},{name:"auto-blur",title:"键盘收起时，是否自动失去焦点",defaultValue:!1,setters:"BooleanSetter"},{name:"ignoreCompositionEvent",title:"是否忽略组件内对文本合成系统事件的处理",defaultValue:!0,setters:"BooleanSetter"},{name:"inputmode",title:"提供了用户在编辑元素或其内容时可能输入的数据类型的提示",defaultValue:"text",setters:"SelectSetter",options:["none","text","decimal","numeric","tel","search","email","url"]}],events:["confirm","input","linechange","blur","focus"],snippet:{props:{style:{backgroundColor:"#999"},placeholder:"输入区域"}}},P={name:"Navigator",label:"页面跳转",categoryId:"nav",props:[{name:"url",title:"应用内的跳转链接，值为相对路径或绝对路径",setters:"StringSetter"},{name:"open-type",title:"跳转方式",defaultValue:"navigate",setters:"SelectSetter",options:["navigate","redirect","switchTab","reLaunch","navigateBack","exit"]},{name:"delta",title:'当 open-type 为 "navigateBack" 时有效，表示回退的层数',setters:"NumberSetter"},{name:"animation-type",title:"当 open-type 为 navigate、navigateBack 时有效，窗口的显示/关闭动画效果",defaultValue:"pop-in/out",setters:"StringSetter"},{name:"animation-duration",title:"当 open-type 为 navigate、navigateBack 时有效，窗口显示/关闭动画的持续时间",defaultValue:300,setters:"NumberSetter"},{name:"render-link",title:"是否给 navigator 组件加一层 a 标签控制 ssr 渲染",defaultValue:!0,setters:"BooleanSetter"},{name:"hover-class",title:'指定点击时的样式类，当hover-class="none"时，没有点击态效果',defaultValue:"navigator-hover",setters:"StringSetter"},{name:"hover-stop-propagation",title:"指定是否阻止本节点的祖先节点出现点击态",defaultValue:!1,setters:"BooleanSetter"},{name:"hover-start-time",title:"按住后多久出现点击态，单位毫秒",defaultValue:50,setters:"NumberSetter"},{name:"hover-stay-time",title:"手指松开后点击态保留时间，单位毫秒",defaultValue:600,setters:"NumberSetter"},{name:"target",title:"在哪个小程序目标上发生跳转，默认当前小程序，值域self/miniProgram",defaultValue:"self",setters:"StringSetter"}],snippet:{children:[{name:"Button",props:{type:"default"},children:"跳转到新页面"}]}},F={name:"Audio",label:"音频",categoryId:"media",props:[{name:"id",title:"audio 组件的唯一标识符",setters:"StringSetter"},{name:"src",title:"要播放音频的资源地址",setters:"StringSetter"},{name:"loop",title:"是否循环播放",defaultValue:!1,setters:"BooleanSetter"},{name:"controls",title:"是否显示默认控件",defaultValue:!1,setters:"BooleanSetter"},{name:"poster",title:"默认控件上的音频封面的图片资源地址",setters:"StringSetter"},{name:"name",title:"默认控件上的音频名字",defaultValue:"未知音频",setters:"StringSetter"},{name:"author",title:"默认控件上的作者名字",defaultValue:"未知作者",setters:"StringSetter"},{name:"action",setters:"ObjectSetter"}],events:["error","play","pause","timeupdate","ended"],snippet:{props:{src:"https://web-ext-storage.dcloud.net.cn/uni-app/ForElise.mp3",poster:"https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/music-a.png",name:"致爱丽丝",author:"暂无",controls:!0,action:{audioAction:{method:"pause"}}}}},z={name:"Image",label:"图片",categoryId:"media",props:[{name:"src",title:"图片资源地址",setters:"StringSetter"},{name:"mode",title:"图片裁剪、缩放的模式",defaultValue:"scaleToFill",setters:"SelectSetter",options:["scaleToFill","aspectFit","aspectFill","widthFix","heightFix","top","bottom","center","left","right","top left","top right","bottom left","bottom right"]},{name:"lazy-load",title:"图片懒加载",defaultValue:!1,setters:"BooleanSetter"},{name:"fade-show",title:"图片显示动画效果",defaultValue:!0,setters:"BooleanSetter"},{name:"webp",title:"在系统不支持webp的情况下是否单独启用webp。默认false，只支持网络资源",defaultValue:!1,setters:"BooleanSetter"},{name:"show-menu-by-longpress",title:"开启长按图片显示识别小程序码菜单",defaultValue:!1,setters:"BooleanSetter"},{name:"draggable",title:"是否能拖动图片",defaultValue:!0,setters:"BooleanSetter"}],events:["error","load"],snippet:{props:{src:"https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/shuijiao.jpg",style:{width:"200px",height:"200px"}}}},E={name:"Video",label:"视频播放组件",categoryId:"media",props:[{name:"src",title:"要播放视频的资源地址",setters:"StringSetter"},{name:"autoplay",title:"是否自动播放",defaultValue:!1,setters:"BooleanSetter"},{name:"loop",title:"是否循环播放",defaultValue:!1,setters:"BooleanSetter"},{name:"muted",title:"是否静音播放",defaultValue:!1,setters:"BooleanSetter"},{name:"initial-time",title:"指定视频初始播放位置，单位为秒（s）",setters:"NumberSetter"},{name:"duration",title:"指定视频时长，单位为秒（s）",setters:"NumberSetter"},{name:"controls",title:"是否显示默认播放控件（播放/暂停按钮、播放进度、时间）",defaultValue:!0,setters:"BooleanSetter"},{name:"danmu-list",title:"弹幕列表",setters:"ExpressionSetter"},{name:"danmu-btn",title:"是否显示弹幕按钮，只在初始化时有效，不能动态变更",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-danmu",title:"是否展示弹幕，只在初始化时有效，不能动态变更",defaultValue:!1,setters:"BooleanSetter"},{name:"page-gesture",title:"在非全屏模式下，是否开启亮度与音量调节手势",defaultValue:!1,setters:"BooleanSetter"},{name:"direction",title:"设置全屏时视频的方向，不指定则根据宽高比自动判断",setters:"SelectSetter",options:[{label:"0",value:0},{label:"90",value:90},{label:"-90",value:-90}]},{name:"show-progress",title:"若不设置，宽度大于240时才会显示",defaultValue:!0,setters:"BooleanSetter"},{name:"show-fullscreen-btn",title:"是否显示全屏按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"show-play-btn",title:"是否显示视频底部控制栏的播放按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"show-center-play-btn",title:"是否显示视频中间的播放按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"show-loading",title:"是否显示loading控件",defaultValue:!0,setters:"BooleanSetter"},{name:"enable-progress-gesture",title:"是否开启控制进度的手势",defaultValue:!0,setters:"BooleanSetter"},{name:"object-fit",title:"视频的表现形式",defaultValue:"contain",setters:"SelectSetter",options:["contain","fill","cover"]},{name:"poster",title:"视频封面的图片网络资源地址，如果 controls 属性值为 false 则设置 poster 无效	",setters:"StringSetter"},{name:"show-mute-btn",title:"是否显示静音按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"title",title:"视频的标题，全屏时在顶部展示",setters:"StringSetter"},{name:"play-btn-position",title:"播放按钮的位置",defaultValue:"bottom",setters:"SelectSetter",options:["bottom","center"]},{name:"mobilenet-hint-type",title:"移动网络提醒样式：0是不提醒，1是提醒，默认值为1",defaultValue:1,setters:"SelectSetter",options:[{label:"0",value:0},{label:"1",value:1}]},{name:"enable-play-gesture",title:"是否开启播放手势，即双击切换播放/暂停",defaultValue:!1,setters:"BooleanSetter"},{name:"auto-pause-if-navigate",title:"当跳转到其它小程序页面时，是否自动暂停本页面的视频",defaultValue:!0,setters:"BooleanSetter"},{name:"auto-pause-if-open-native",title:"当跳转到其它微信原生页面时，是否自动暂停本页面的视频",defaultValue:!0,setters:"BooleanSetter"},{name:"vslide-gesture",title:"在非全屏模式下，是否开启亮度与音量调节手势（同 page-gesture）",defaultValue:!1,setters:"BooleanSetter"},{name:"vslide-gesture-in-fullscreen",title:"在全屏模式下，是否开启亮度与音量调节手势",defaultValue:!0,setters:"BooleanSetter"},{name:"ad-unit-id",title:"视频前贴广告单元ID",setters:"StringSetter"},{name:"poster-for-crawler",title:"用于给搜索等场景作为视频封面展示",setters:"StringSetter"},{name:"codec",title:"解码器选择",defaultValue:"hardware",setters:"SelectSetter",options:["hardware","software"]},{name:"http-cache",title:"是否对 http、https 视频源开启本地缓存",defaultValue:!0,setters:"BooleanSetter"},{name:"play-strategy",title:"播放策略",defaultValue:0,setters:"SelectSetter",options:[{label:"0",value:0},{label:"1",value:1},{label:"2",value:2}]},{name:"header",title:"HTTP 请求 Header",setters:"ObjectSetter"},{name:"is-live",title:"是否为直播源",defaultValue:!1,setters:"BooleanSetter"}],events:["play","pause","ended","timeupdate","fullscreenchange","waiting","error","progress","loadeddata","loadstart","seeked","seeking","loadedmetadata","fullscreenclick","controlstoggle"],snippet:{props:{style:{width:"300px",height:"225px"},src:"https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/2minute-demo.mp4","enable-danmu":!0,"danmu-btn":!0,controls:!0,"danmu-list":[{text:"第 1s 出现的弹幕",color:"#ff0000",time:1},{text:"第 3s 出现的弹幕",color:"#ff00ff",time:3}]}}},T={name:"Map",label:"地图组件",categoryId:"media",props:[{name:"longitude",title:"中心经度",setters:"NumberSetter"},{name:"latitude",title:"中心纬度",setters:"NumberSetter"},{name:"scale",title:"缩放级别，取值范围为3-20",defaultValue:16,setters:"NumberSetter"},{name:"theme",title:"主题",defaultValue:"normal",setters:"SelectSetter",options:["satellite","normal"]},{name:"min-scale",title:"最小缩放级别",defaultValue:3,setters:"NumberSetter"},{name:"max-scale",title:"最大缩放级别",defaultValue:20,setters:"NumberSetter"},{name:"layer-style",title:"个性化地图",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"markers",title:"标记点",setters:"ArraySetter"},{name:"polyline",title:"路线",setters:"ArraySetter"},{name:"circles",title:"圆",setters:"ArraySetter"},{name:"controls",title:"控件",setters:"ArraySetter"},{name:"include-points",title:"缩放视野以包含所有给定的坐标点",setters:"ArraySetter"},{name:"zIndex",title:"显示层级",defaultValue:!1,setters:"NumberSetter"},{name:"enable-3D",title:"是否显示3D楼块",defaultValue:!1,setters:"BooleanSetter"},{name:"show-compass",title:"是否显示指南针",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-zoom",title:"是否支持缩放",defaultValue:!0,setters:"BooleanSetter"},{name:"enable-scroll",title:"是否支持拖动",defaultValue:!0,setters:"BooleanSetter"},{name:"enable-rotate",title:"是否支持旋转",defaultValue:!1,setters:"BooleanSetter"},{name:"rotate",title:"旋转角度(范围0-360)地图正北和设备 y 轴角度的夹角",defaultValue:0,setters:"NumberSetter"},{name:"skew",title:"倾斜角度，范围 0 ~ 40 , 关于 z 轴的倾角",defaultValue:0,setters:"NumberSetter"},{name:"enable-overlooking",title:"是否开启俯视",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-satellite",title:"是否开启卫星图",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-traffic",title:"是否开启实时路况",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-poi",title:"是否展示 POI 点",defaultValue:!1,setters:"BooleanSetter"},{name:"enable-building",title:"是否展示建筑物",defaultValue:!1,setters:"BooleanSetter"},{name:"show-location",title:"显示带有方向的当前定位点",setters:"BooleanSetter"},{name:"polygons",title:"多边形",setters:"ArraySetter"},{name:"polygon",title:"多边形(支付宝)",setters:"ArraySetter"},{name:"enable-indoorMap",title:"是否展示室内地图",defaultValue:!1,setters:"BooleanSetter"}],events:["markertap","labeltap","callouttap","controltap","regionchange","tap","updated","anchorpointtap","poitap"],snippet:{props:{style:{width:"100%",height:"300px"},latitude:"39.909",longitude:"116.39742",markers:[{latitude:39.909,longitude:116.39742},{latitude:39.9,longitude:116.39}]}}},j={name:"Canvas",label:"画布",categoryId:"media",props:[{name:"type",title:"指定 canvas 类型，支持 2d (2.9.0) 和 webgl",defaultValue:"",setters:"SelectSetter",options:["2d","webgl"]},{name:"canvas-id",title:"canvas 组件的唯一标识符",setters:"StringSetter"},{name:"disable-scroll",title:"当在 canvas 中移动时且有绑定手势事件时，禁止屏幕滚动以及下拉刷新",defaultValue:!1,setters:"BooleanSetter"},{name:"hidpi",title:"是否启用高清处理",defaultValue:!0,setters:"BooleanSetter"}],events:["touchstart","touchmove","touchend","touchcancel","longtap","error"],snippet:{props:{style:{width:"300px",height:"200px"},"canvas-id":"firstCanvas",id:"firstCanvas"}}},J={name:"WebView",label:"浏览器组件",categoryId:"media",props:[{name:"src",title:"webview 指向网页的链接",setters:"StringSetter"},{name:"allow",title:"用于为 iframe 指定其特征策略",setters:"StringSetter"},{name:"sandbox",title:"该属性对呈现在 iframe 框架中的内容启用一些额外的限制条件",setters:"StringSetter"},{name:"fullscreen",title:"是否铺满整个页面",defaultValue:!0,setters:"BooleanSetter"},{name:"webview-styles",title:"webview 的样式",setters:"ObjectSetter"},{name:"update-title",title:"是否自动更新当前页面标题",defaultValue:!0,setters:"BooleanSetter"}],events:["message","onPostMessage","load","error"],snippet:{props:{"webview-styles":{progress:{color:"#FF3333"}},src:"https://uniapp.dcloud.io/static/web-view.html"}}},l="@dcloudio/uni-h5",M=[a,i,u,d,m,c,p,f,S,h,g,b,V,v,y,B,w,x,k,N,I,C,A,$,P,F,z,E,T,j,J,{name:"component",label:"动态组件",categoryId:"elements",doc:"https://cn.vuejs.org/api/built-in-special-elements.html#component",props:[{name:"is",label:"组件名",setters:"InputSetter"}],snippet:{children:"组件文本内容示例",props:{is:"view"}}},{name:"slot",label:"插槽",categoryId:"elements",doc:"https://cn.vuejs.org/api/built-in-special-elements.html#slot",props:[{name:"name",label:"名称",defaultValue:"default",setters:"InputSetter"}],snippet:{children:"默认插槽内容"}}].flat();return{name:l,version:t,label:"内置",library:"UniH5Material",order:4,categories:[{id:"container",category:"视图组件"},{id:"basic",category:"基础内容"},{id:"form",category:"表单"},{id:"nav",category:"路由与页面跳转"},{id:"media",category:"媒体"},{id:"elements",category:"特殊元素"}],components:e(M,l)}});
