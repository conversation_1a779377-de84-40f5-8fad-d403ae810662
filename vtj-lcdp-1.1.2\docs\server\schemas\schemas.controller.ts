import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Query,
  Delete
} from '@nestjs/common';
import type { PlatformType } from '@vtj/core';
import { SchemasService } from './schemas.service';
import { SchemaDto } from './dto/schema.dto';
import { SchemaType, Public } from '../shared';
import { ParseVueDto } from './dto/parse-vue.dto';
import { QuerySchemaDto } from './dto/query-app.dto';

@Controller('schemas')
export class SchemasController {
  constructor(private readonly service: SchemasService) {}

  @Post(':app/:type')
  save(@Body() dto: SchemaDto) {
    dto.content =
      typeof dto.content === 'string' ? JSON.parse(dto.content) : dto.content;
    return this.service.save(dto);
  }

  @Public()
  @Get('info/:app/:type')
  findOne(
    @Param('app') app: string,
    @Param('type') type: string,
    @Query('name') name: string
  ) {
    return this.service.findOne(app, type as SchemaType, name);
  }

  @Get(':app/:type')
  find(
    @Param('app') app: string,
    @Param('type') type: string,
    @Query('name') name: string
  ) {
    return this.service.find(app, type as SchemaType, name);
  }

  @Get()
  search(@Query() dto: QuerySchemaDto) {
    return this.service.search(dto);
  }

  @Get(':id')
  findOneById(@Param('id') id: string) {
    return this.service.findOneById(id);
  }

  @Delete(':app/:type')
  remove(
    @Param('app') app: string,
    @Param('type') type: string,
    @Body() names: string[]
  ) {
    return this.service.remove(app, type as SchemaType, names);
  }

  @Delete()
  removeByIds(@Body() ids: string[]) {
    return this.service.removeByIds(ids);
  }

  @Post('generator/:app/vue')
  generator(
    @Param('app') app: string,
    @Query('platform') platform: PlatformType,
    @Body() dsl: any
  ) {
    return this.service.genVueContent(app, dsl, platform || 'web');
  }

  @Post('generator/:app/project')
  projectGenerator(@Param('app') app: string, @Body() project: any) {
    return this.service.genProject(app, project);
  }

  @Post('parser')
  parseVue(@Body() dto: ParseVueDto) {
    return this.service.parseVue(dto);
  }
}
