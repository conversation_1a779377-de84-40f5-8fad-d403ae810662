import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SchemasService } from './schemas.service';
import { SchemasController } from './schemas.controller';
import { Schema } from './entities/schema.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Schema])],
  controllers: [SchemasController],
  providers: [SchemasService],
  exports: [TypeOrmModule, SchemasService]
})
export class SchemasModule {}
