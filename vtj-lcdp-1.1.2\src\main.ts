import { createApp } from 'vue';
import { Access, ContextMode } from '@vtj/pro';
import {
  STORAGE_KEY,
  ACCESS_PRIVATE_KEY,
  AUTH_PATH,
  setGlobalRequest
} from '@/shared';
import { loading, notify, alert, autoAuth } from '@/utils';
import router from './router';
import App from './App.vue';
import './style/index.scss';
const app = createApp(App);
const request = setGlobalRequest({ loading, notify });
const access = new Access({
  alert,
  storageKey: STORAGE_KEY,
  privateKey: ACCESS_PRIVATE_KEY,
  auth: AUTH_PATH
});
access.connect({ request, router, mode: ContextMode.Design });
app.use(router);
app.use(access);

(async function () {
  await autoAuth(access);
  app.mount('#app');
})();
