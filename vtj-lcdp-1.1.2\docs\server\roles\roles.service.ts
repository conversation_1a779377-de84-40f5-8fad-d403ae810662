import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In } from 'typeorm';
import { RoleDto } from './dto/role.dto';
import { QueryRoleDto } from './dto/quey-role.dto';
import { Role } from './entities/role.entity';
import { pager } from '../shared';
import { Access } from '../access/entities/access.entity';
import { AccessService } from '../access/access.service';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role) private repo: Repository<Role>,
    private access: AccessService
  ) {}

  save(dto: RoleDto) {
    const role = new Role();
    return this.repo.save(Object.assign(role, dto));
  }

  async find(dto: QueryRoleDto) {
    const { page, limit, skip, take } = pager(dto);
    const { withDeleted } = dto;
    const [list, total] = await this.repo.findAndCount({
      withDeleted,
      skip,
      take,
      where: [
        {
          name: dto.keyword ? Like(`%${dto.keyword}%`) : undefined
        },
        {
          label: dto.keyword ? Like(`%${dto.keyword}%`) : undefined
        }
      ],
      order: {
        order: 'ASC',
        createdAt: 'DESC'
      }
    });
    return {
      page,
      limit,
      total,
      list
    };
  }

  remove(id: string | string[]) {
    return this.repo.softDelete(id);
  }

  async setDefault(id: string) {
    const rows = await this.repo.findBy({ isDefault: true });
    if (rows.length) {
      for (const row of rows) {
        row.isDefault = false;
        await this.repo.save(row);
      }
    }
    const role = await this.repo.findOneBy({ id });
    if (role) {
      role.isDefault = true;
      await this.repo.save(role);
    }
    return true;
  }

  getDefaultRole() {
    return this.repo.findOneBy({
      isDefault: true
    });
  }

  getSuperRoles() {
    return this.repo.findBy({
      isSuper: true
    });
  }

  async getAccess(roleIds: string[]) {
    const roles = await this.repo.find({
      where: { id: In(roleIds) },
      relations: {
        access: true
      }
    });
    const result: Access[] = [];
    for (const role of roles) {
      result.push(...role.access);
    }
    return result;
  }

  async getPermissions(roleIds: string[]) {
    const access = await this.getAccess(roleIds);
    const accessIds = access.map((n) => n.id);
    const apis = await this.access.getApis(accessIds);
    const accessMap: Record<string, boolean> = {};
    const apisMap: Record<string, boolean> = {};
    const appsMap: Record<string, boolean> = {};
    for (const n of access) {
      accessMap[n.code] = true;
      appsMap[n.root] = true;
    }
    for (const n of apis) {
      apisMap[`${n.method.toLowerCase()},${n.path.toLowerCase()}`] = true;
    }
    return {
      permissions: accessMap,
      apis: apisMap,
      apps: appsMap
    };
  }

  async saveAccess(roleId: string, access: Access[] = []) {
    const role = await this.repo.findOneBy({ id: roleId });
    if (role) {
      role.access = access;
    }
    return await this.repo.save(role);
  }
}
