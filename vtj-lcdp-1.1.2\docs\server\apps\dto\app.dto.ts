import {
  IsNotEmpty,
  IsOptional,
  IsInt,
  IsEnum,
  IsBoolean
} from 'class-validator';
import { PlatformType, AppScopeType } from '../../shared';

export class AppDto {
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  name: string;

  @IsNotEmpty()
  label: string;

  @IsOptional()
  @IsInt()
  order?: number;

  @IsOptional()
  logo?: string;

  @IsNotEmpty()
  @IsEnum(PlatformType)
  platform: PlatformType;

  @IsOptional()
  icon?: string;

  @IsOptional()
  @IsBoolean()
  isBase?: boolean;

  @IsOptional()
  @IsBoolean()
  isAccess?: boolean;

  @IsOptional()
  notes?: string;

  @IsOptional()
  userId?: string;

  @IsOptional()
  @IsEnum(AppScopeType)
  scope?: AppScopeType;
}
