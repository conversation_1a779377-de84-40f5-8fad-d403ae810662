import { MenuProps, MenuDataItem } from './types';
import { DefineComponent, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
declare const _default: DefineComponent<MenuProps, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    select: (item: MenuDataItem) => any;
}, string, PublicProps, Readonly<MenuProps> & Readonly<{
    onSelect?: ((item: MenuDataItem) => any) | undefined;
}>, {
    data: MenuDataItem[];
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>;
export default _default;
