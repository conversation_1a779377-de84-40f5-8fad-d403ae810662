{"name": "@vtj", "entryPoints": ["docs/refs/base.ts", "docs/refs/node.ts", "docs/refs/cli.ts", "docs/refs/utils.ts", "docs/refs/icons.ts", "docs/refs/ui.ts", "docs/refs/core.ts", "docs/refs/designer.ts", "docs/refs/renderer.ts"], "readme": "typedoc.md", "entryPointStrategy": "resolve", "out": "docs/src/typedoc", "excludeExternals": true, "externalPattern": ["**/node_modules/**"], "exclude": ["README.md"]}