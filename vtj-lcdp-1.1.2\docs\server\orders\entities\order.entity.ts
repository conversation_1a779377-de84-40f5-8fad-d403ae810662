import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../shared';
import { OrderStatus } from '../interfaces/types';
@Entity({
  name: 'orders',
  comment: '订阅订单',
  orderBy: {
    createdAt: 'DESC'
  }
})
@Index(['userId', 'status', 'start', 'end'])
export class Order extends BaseEntity {
  @Column({ name: 'user_id', comment: '用户ID' })
  userId: string;

  @Column({ name: 'user_name', comment: '用户名' })
  userName: string;

  @Column({
    type: 'enum',
    comment: '状态',
    enum: OrderStatus,
    default: OrderStatus.Pending
  })
  status: OrderStatus;

  @Column({ comment: '开始时间' })
  start: Date;

  @Column({ comment: '结束时间' })
  end: Date;

  @Column({ comment: 'AI Token', default: 0 })
  tokens: number;
}
