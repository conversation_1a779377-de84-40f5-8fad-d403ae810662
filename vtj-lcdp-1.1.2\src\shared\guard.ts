import { getLowCodeApp } from '@/apis';
import type { Access } from '@vtj/renderer';

/**
 * 低代码应用权限守卫
 * @param appId
 * @param access
 * @returns
 */
export async function appGuard(appId: string, access: Access) {
  const app = await getLowCodeApp(appId).catch(() => null);
  const info = access.getData();
  if (app) {
    if (app.scope === 'private' && (!info || app.userId !== info?.id)) {
      await access.showTip('这是私密应用，您无权限访问');
      history.back();
      return false;
    }
    if (app.scope === 'protected' && !info) {
      await access.showTip('您未登录或登录已过期，重新登录！');
      access.toLogin();
      return false;
    }
    return true;
  }
  return true;
}
