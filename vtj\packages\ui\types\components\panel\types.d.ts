import { PropType } from 'vue';
import { ComponentPropsType, BaseSize } from '../shared';
import { HeaderProps, ContainerProps } from '../';
export type PanelBadgeType = 'success' | 'primary' | 'warning' | 'danger';
export interface PanelBadge {
    type: PanelBadgeType;
    text: String;
}
export declare const panelProps: {
    /**
     * 右上角标记
     */
    badge: {
        type: PropType<PanelBadge>;
    };
    /**
     * 宽高自适应
     */
    fit: {
        type: BooleanConstructor;
        default: boolean;
    };
    /**
     * 指定高度，fit 为true 失效
     */
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    /**
     * 指定高度，fit 为true失效
     */
    height: {
        type: (StringConstructor | NumberConstructor)[];
    };
    /**
     * 显示边框
     */
    border: {
        type: BooleanConstructor;
        default: boolean;
    };
    /**
     * 圆角
     */
    radius: {
        type: BooleanConstructor;
        default: boolean;
    };
    /**
     * 卡片模式
     */
    card: {
        type: BooleanConstructor;
    };
    /**
     * 尺寸
     */
    size: {
        type: PropType<BaseSize>;
    };
    /**
     * 阴影设置
     */
    shadow: {
        type: PropType<"none" | "always" | "hover">;
    };
    /**
     * 头部设置
     */
    header: {
        type: PropType<string | HeaderProps | null>;
    };
    body: {
        type: PropType<ContainerProps>;
    };
    footer: {
        type: PropType<ContainerProps>;
    };
};
export type PanelProps = ComponentPropsType<typeof panelProps>;
