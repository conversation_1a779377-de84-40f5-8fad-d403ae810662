import { VerifyProps } from './types';
import { ModelRef, DefineComponent, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
type __VLS_Props = VerifyProps;
declare const modelValue: ModelRef<string | undefined, string, string | undefined, string | undefined>;
type __VLS_PublicProps = {
    modelValue?: typeof modelValue['value'];
} & __VLS_Props;
declare const _default: DefineComponent<__VLS_PublicProps, {
    send: () => Promise<void>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    "update:modelValue": (value: string | undefined) => any;
}, string, PublicProps, Readonly<__VLS_PublicProps> & Readonly<{
    "onUpdate:modelValue"?: ((value: string | undefined) => any) | undefined;
}>, {
    placeholder: string;
    maxlength: number;
    seconds: number;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>;
export default _default;
