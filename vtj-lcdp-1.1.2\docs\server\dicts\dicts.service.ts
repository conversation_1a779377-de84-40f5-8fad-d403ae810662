import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, Like, In } from 'typeorm';
import { DictDto } from './dto/dict.dto';
import { QueryDictDto } from './dto/query-dict.dto';
import { Dict } from './entities/dict.entity';
import { pager } from '../shared';
import { CacheService } from '../cache/cache.service';

@Injectable()
export class DictsService {
  private cachePrefix: string = 'DICT_GROUP_';

  constructor(
    @InjectRepository(Dict) private repo: Repository<Dict>,
    private cache: CacheService
  ) {}

  async save(dto: DictDto) {
    const dict = new Dict();
    const result = await this.repo.save(Object.assign(dict, dto));
    if (result.group) {
      await this.cache.removeByKey(`${this.cachePrefix}${result.group}`);
    }
    return result;
  }

  findOne(id: string) {
    return this.repo.findOneBy({ id });
  }

  async findForRoot(dto: QueryDictDto) {
    const { page, limit, skip, take } = pager(dto);
    const { withDeleted } = dto;
    const [list, total] = await this.repo.findAndCount({
      withDeleted,
      skip,
      take,
      where: [
        {
          name: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          group: IsNull()
        },
        {
          label: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          group: IsNull()
        }
      ],
      order: {
        order: 'ASC',
        createdAt: 'ASC'
      }
    });

    return {
      page,
      limit,
      total,
      list
    };
  }

  async findByGroup(group: string) {
    const cacheKey = `${this.cachePrefix}${group}`;
    const cache = await this.cache.get(cacheKey);
    if (cache) {
      return cache.value as Dict[];
    }

    const result = await this.repo.find({
      where: {
        group
      },
      order: {
        order: 'ASC',
        createdAt: 'ASC'
      }
    });
    if (result) {
      await this.cache.set(cacheKey, result);
    }
    return result;
  }

  async findByGroups(groups: string[]) {
    const results: Record<string, Dict[]> = {};
    for (const group of groups) {
      const items = await this.findByGroup(group);
      if (items) {
        results[group] = items;
      }
    }
    return results;
  }

  async removeGroups(groups: string[]) {
    // 删除项
    await this.repo.delete({
      group: In(groups)
    });
    // 删除组
    const result = await this.repo.delete({
      name: In(groups)
    });
    const cacheKeys = groups.map((n) => `${this.cachePrefix}${n}`);
    await this.cache.removeByKey(cacheKeys);
    return result;
  }

  async updateGroupItems(
    group: string,
    records: DictDto[] = [],
    removed: string[] = []
  ) {
    for (const item of records) {
      item.group = group;
      await this.save(item);
    }
    if (removed.length) {
      await this.repo.delete({
        id: In(removed)
      });
    }
    const cacheKey = `${this.cachePrefix}${group}`;
    await this.cache.removeByKey(cacheKey);
    return true;
  }
}
