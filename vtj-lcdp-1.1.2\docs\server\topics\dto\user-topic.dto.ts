import { ProjectSchema, BlockSchema } from '@vtj/core';
import { IsNotEmpty, IsJSON, IsEnum, IsOptional } from 'class-validator';
import { TopicType, TopicDataType } from '../types';

export class UserTopicDto {
  @IsNotEmpty()
  model: string;

  @IsJSON()
  project: ProjectSchema;

  @IsJSON()
  dsl: BlockSchema;

  @IsNotEmpty()
  source: string;

  @IsNotEmpty()
  prompt: string;

  userId: string;
  userName: string;

  @IsOptional()
  @IsEnum(TopicType)
  type?: TopicType;

  @IsOptional()
  @IsEnum(TopicDataType)
  dataType?: TopicDataType;
}
