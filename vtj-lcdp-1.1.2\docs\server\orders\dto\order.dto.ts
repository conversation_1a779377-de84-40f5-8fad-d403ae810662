import { <PERSON><PERSON><PERSON>al, <PERSON>N<PERSON>ber, IsNotEmpty, IsEnum } from 'class-validator';
import { OrderStatus } from '../interfaces/types';
export class OrderDto {
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  userId: string;

  @IsNotEmpty()
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @IsNotEmpty()
  start: Date;

  @IsOptional()
  end?: Date;

  @IsNumber()
  month?: number;

  @IsNumber()
  tokens?: number;
}
