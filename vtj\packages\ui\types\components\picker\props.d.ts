import { PropType } from 'vue';
import { PickerC<PERSON>umns, PickerFields, PickerDialogProps, PickerGridProps, PickerLoader } from './types';
export declare const pickerProps: {
    /**
     * 表格列配置
     */
    columns: {
        type: PropType<PickerColumns>;
    };
    /**
     * 查询条件表单字段
     */
    fields: {
        type: PropType<PickerFields>;
    };
    /**
     * 查询表单字段值
     */
    model: {
        type: PropType<Record<string, any>>;
    };
    /**
     * 表格数据加载器
     */
    loader: {
        type: PropType<PickerLoader>;
    };
    /**
     * 值
     */
    modelValue: {
        type: (ObjectConstructor | StringConstructor | NumberConstructor | ArrayConstructor)[];
    };
    /**
     * 多选模式
     */
    multiple: {
        type: BooleanConstructor;
    };
    /**
     * 值为对象模式
     */
    raw: {
        type: BooleanConstructor;
    };
    /**
     * 禁用
     */
    disabled: {
        type: BooleanConstructor;
    };
    /**
     * 多选追加模式
     */
    append: {
        type: BooleanConstructor;
    };
    /**
     * 值映射字段名称
     */
    valueKey: {
        type: StringConstructor;
        default: string;
    };
    /**
     * 输入框显示映射字段名称
     */
    labelKey: {
        type: StringConstructor;
        default: string;
    };
    /**
     * 查询参数名称
     */
    queryKey: {
        type: StringConstructor;
    };
    /**
     * 回车时自动检测取回有且计有唯一数据
     */
    preload: {
        type: BooleanConstructor;
    };
    /**
     *  弹窗组件配置参数
     */
    dialogProps: {
        type: PropType<PickerDialogProps>;
    };
    /**
     * 表格组件配置参数
     */
    gridProps: {
        type: PropType<PickerGridProps>;
    };
    /**
     * 查询表单参数
     */
    formProps: {
        type: PropType<Record<string, any>>;
    };
    /**
     * 附加数据，在事件中回调
     */
    data: {
        type: PropType<any>;
    };
    /**
     * 接受数据后格式化
     */
    formatter: {
        type: FunctionConstructor;
    };
    /**
     * 输出数据格式化
     */
    valueFormatter: {
        type: FunctionConstructor;
    };
    /**
     * 弹窗在打开之前回调
     */
    beforeInit: {
        type: FunctionConstructor;
    };
};
