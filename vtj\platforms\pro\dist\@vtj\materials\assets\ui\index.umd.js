(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.VtjUIMaterial=e())})(this,function(){"use strict";var G=Object.defineProperty,K=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var b=Object.getOwnPropertySymbols;var U=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var g=(t,e,a)=>e in t?G(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,V=(t,e)=>{for(var a in e||(e={}))U.call(e,a)&&g(t,a,e[a]);if(b)for(var a of b(e))W.call(e,a)&&g(t,a,e[a]);return t},h=(t,e)=>K(t,L(e));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const t="0.12.70";function e(l="size"){return{name:l,defaultValue:"default",setters:"SelectSetter",options:["default","large","small"]}}function a(l="type"){return{name:l,defaultValue:"default",setters:"SelectSetter",options:["default","primary","success","warning","danger","info"]}}function B(l,m){return l.map(i=>h(V({},i),{package:m}))}function r(l=[],m=[]){return l.filter(i=>!m.includes(i.name))}const y={name:"XTest",label:"测试组件",categoryId:"test",props:[{name:"booleanProp",label:"布尔值",setters:"BooleanSetter",title:"提示说明文本",defaultValue:!0},{name:"stringProp",setters:"StringSetter"},{name:"numberProp",setters:"NumberSetter"},{name:"selectProp",setters:"SelectSetter",defaultValue:"default",options:["default","primary","success","warning","danger","info"]},{name:"objectProp",setters:"ObjectSetter"},{name:"arrayProp",setters:"ArraySetter"},{name:"iconProp",setters:"IconSetter"},{name:"colorProp",setters:"ColorSetter"},{name:"modelValue",setters:"StringSetter"},{name:"syncProp",setters:"StringSetter"}],events:[{name:"click",params:["props"]},{name:"submit",params:["props"]},{name:"change",params:["data"]},{name:"update:modelValue",params:["value"]},{name:"update:syncProp",params:["value"]}],slots:[{name:"default",params:["props","data"]},{name:"extra",params:["props","data"]}],snippet:{props:{}}},I={name:"XIcon",label:"图标",categoryId:"base",props:[{name:"icon",label:"图标",setters:"IconSetter"},Object.assign(e("size"),{setters:["SelectSetter","NumberSetter"]}),{name:"color",label:"颜色",setters:"ColorSetter"},{name:"background",label:"背景色",setters:"ColorSetter"},{name:"src",label:"图片Url",setters:"InputSetter"},{name:"radius",label:"圆角值",setters:"NumberSetter"},{name:"padding",label:"内边距",setters:"NumberSetter"},{name:"hoverEffect",label:"悬停效果",setters:"BooleanSetter"}],snippet:{props:{icon:"Star"}}},u={name:"XContainer",label:"容器",categoryId:"base",props:[{name:"tag",label:"标签名",setters:"StringSetter",defaultValue:"div"},{name:"fit",label:"高度自适应",setters:"BooleanSetter"},{name:"width",label:"宽度",setters:"StringSetter"},{name:"height",label:"高度",setters:"StringSetter"},{name:"flex",label:"flex布局",setters:"BooleanSetter",defaultValue:!0},{name:"inline",label:"inline-flex",setters:"BooleanSetter"},{name:"direction",label:"排版方向",setters:"SelectSetter",options:["row","row-reverse","column","column-reverse"],defaultValue:"row"},{name:"wrap",label:"换行",setters:"SelectSetter",options:["nowrap","wrap","wrap-reverse"],defaultValue:"nowrap"},{name:"justify",label:"主轴对齐",setters:"SelectSetter",options:["flex-start","flex-end","center","space-between","space-around"],defaultValue:"flex-start"},{name:"align",label:"交叉轴对齐",setters:"SelectSetter",options:["flex-start","flex-end","center","baseline","stretch"],defaultValue:"flex-start"},{name:"alignContent",label:"多轴线对齐",title:"多根轴线的对齐方式。如果项目只有一根轴线，该属性不起作用",setters:"SelectSetter",options:["flex-start","flex-end","center","space-between","space-around","stretch"],defaultValue:"stretch"},{name:"grow",label:"自动放大",setters:"BooleanSetter",defaultValue:!1},{name:"shrink",label:"自动缩小",setters:"BooleanSetter",defaultValue:!1},{name:"alignSelf",label:"alignSelf",title:"单个项目有与其他项目不一样的对齐方式。可覆盖容器的align-items属性",setters:"SelectSetter",options:["flex-start","flex-end","center","baseline","stretch"],defaultValue:"auto"},{name:"overflow",label:"overflow",setters:"SelectSetter",options:["auto","hidden","visible"]},{name:"padding",label:"内边距",setters:"BooleanSetter",defaultValue:!1},{name:"gap",label:"子组件间隔",setters:"BooleanSetter"},{name:"autoPointer",label:"autoPointer",setters:"BooleanSetter"}],snippet:{props:{padding:!0}}},v={name:"XAction",label:"操作按钮",categoryId:"base",props:[{name:"name",label:"名称标识",setters:"StringSetter"},{name:"label",label:"标题文本",setters:"StringSetter"},{name:"value",label:"动作值",setters:"StringSetter",title:"用来传输数据"},{name:"icon",label:"图标配置",setters:"IconSetter"},{name:"mode",label:"模式",setters:"SelectSetter",options:["button","text","icon"],defaultValue:"button"},e(),a(),{name:"menus",label:"菜单配置",setters:"JsonSetter"},{name:"tooltip",label:"Tooltip配置",setters:["StringSetter","JsonSetter"]},{name:"badge",label:"Badge配置",setters:["StringSetter","NumberSetter","JsonSetter"]},{name:"dropdown",label:"Dropdown配置",setters:"JsonSetter"},{name:"button",label:"Button配置",setters:"JsonSetter",title:"mode为button时有效"},{name:"disabled",label:"禁用",setters:"BooleanSetter"},{name:"background",label:"背景",setters:"SelectSetter",options:["always","hover","none"]},{name:"circle",label:"圆形",setters:"BooleanSetter"}],events:["click","command"],slots:[{name:"item",params:["item","index"]},{name:"default"}],snippet:{props:{label:"操作按钮"}}},x={name:"XActionBar",label:"操作按钮集",categoryId:"base",props:[{name:"items",title:"动作项",setters:"ArraySetter"},{name:"mode",label:"模式",setters:"SelectSetter",options:["button","text","icon"],defaultValue:"button"},e(),a(),{name:"tooltip",label:"Tooltip配置",setters:["StringSetter","JsonSetter"]},{name:"badge",label:"Badge配置",setters:["StringSetter","NumberSetter","JsonSetter"]},{name:"dropdown",label:"Dropdown配置",setters:"JsonSetter"},{name:"button",label:"Button配置",setters:"JsonSetter",title:"mode为button时有效"},{name:"disabled",label:"禁用",setters:"BooleanSetter"},{name:"background",label:"背景",setters:"SelectSetter",options:["always","hover","none"]},{name:"circle",label:"圆形",setters:"BooleanSetter"}],events:["click","command"],snippet:{props:{items:[{label:"操作一",value:1},{label:"操作二",value:2},{label:"操作三",value:3}]}}},O={name:"XAttachment",label:"附件",categoryId:"data",props:[{name:"modelValue",label:"modelValue",title:"列表显示的文件",setters:"ArraySetter"},{name:"selectValue",label:"selectValue",title:"选中值，开启 selectable 有效",setters:"ArraySetter"},{name:"uploader",label:"uploader",title:"文件上传方法",setters:"FunctionsSetter"},{name:"multiple",label:"multiple",title:"支持多文件上传, 同时在selectable时控制多选",setters:"BooleanSetter"},{name:"limit",label:"limit",title:"允许上传文件的最大数量",setters:"NumberSetter"},{name:"accept",label:"accept",title:" 接受上传的文件类型, 如：image/png, image/jpeg, .pptx",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"禁止更改文件，不能上传和删除",setters:"BooleanSetter"},e(),{name:"thumbnail",label:"thumbnail",title:"缩略图生成方法",setters:"FunctionSetter"},{name:"addable",label:"addable",title:"可增加",setters:"BooleanSetter",defaultValue:!0},{name:"removable",label:"removable",title:"可删除",setters:"BooleanSetter",defaultValue:!0},{name:"downloadable",label:"downloadable",title:"可下载",setters:"BooleanSetter",defaultValue:!0},{name:"previewable",label:"previewable",title:"可预览",setters:"BooleanSetter",defaultValue:!0},{name:"selectable",label:"selectable",title:"可选择",setters:"BooleanSetter",defaultValue:!1},{name:"clickable",label:"clickable",title:"可点击",setters:"BooleanSetter",defaultValue:!1},{name:"listType",label:"listType",title:"列表类型",setters:"SelectSetter",options:["card","list"],defaultValue:"card"},{name:"beforeUpload",title:"上传前守卫",setters:"FunctionSetter"},{name:"limitSize",title:"允许上传的文件大写最大值， 支持 K / M",setters:"StringSetter",defaultValue:"10M"},{name:"formatter",setters:"FunctionSetter"},{name:"valueFormatter",setters:"FunctionSetter"},{name:"previewer",setters:"FunctionSetter"},{name:"downloader",setters:"FunctionSetter"},{name:"autoUpload",setters:"BooleanSetter"}],events:["click","preview","remove","download","select","change","update:modelValue","update:selectValue"],slots:["tip"],snippet:{props:{}}},w={name:"XQrCode",label:"二维码",categoryId:"base",props:[{name:"size",title:"二维码尺寸",setters:"NumberSetter"},{name:"content",setters:["StringSetter","FunctionSetter"]},{name:"expired",title:"设置过期时间，单位毫秒",setters:"NumberSetter"},{name:"tip",title:"过期时提示文本",setters:"StringSetter"},{name:"options",title:"qrcode工具配置参数：https://www.npmjs.com/package/qrcode",setters:"ObjectSetter"}],events:[{name:"refresh"},{name:"draw"}],slots:["tip"],snippet:{props:{content:"二维码内容"}}},d={name:"XPanel",label:"面板",categoryId:"layout",props:[{name:"fit",label:"fit",title:"宽高自适应",setters:"BooleanSetter"},{name:"width",label:"width",setters:["StringSetter","NumberSetter"]},{name:"height",label:"height",setters:["StringSetter","NumberSetter"]},{name:"border",title:"显示边框",setters:"BooleanSetter"},{name:"radius",title:"圆角",setters:"BooleanSetter"},{name:"card",title:"卡片模式",setters:"BooleanSetter"},Object.assign(e("size"),{setters:["SelectSetter"]}),{name:"shadow",title:"阴影设置",setters:"SelectSetter",options:["none","always","hover"]},{name:"header",title:"头部设置",setters:["StringSetter","ObjectSetter"]},{name:"body",title:"body",setters:"ObjectSetter"},{name:"footer",title:"footer",setters:"ObjectSetter"},{name:"badge",title:"标记设置",setters:"ObjectSetter"}],slots:["default","actions","title","footer"],snippet:{props:{header:"标题"},children:"内容文本"}},j={name:"XDataItem",label:"数据项",categoryId:"data",props:[{name:"direction",title:"排版方向",setters:"SelectSetter",options:["row","column"],defaultValue:"column"},{name:"imageSrc",title:"图片URL",setters:"StringSetter"},{name:"imageWidth",title:"图片宽度",setters:["StringSetter","NumberSetter"]},{name:"imageHeight",title:"图片高度",setters:["StringSetter","NumberSetter"]},{name:"icon",title:"图片高度",setters:["IconSetter"]},{name:"title",setters:["StringSetter"]},{name:"description",setters:["StringSetter"]},{name:"actions",title:"动作按钮 ActionBarItems",setters:["ArraySetter"]},{name:"actionBarProps",title:"动作条组件配置 ActionBarProps",setters:["ObjectSetter"]},{name:"split",setters:["BooleanSetter"]},{name:"active",setters:["BooleanSetter"]},{name:"hover",setters:["BooleanSetter"]},{name:"padding",setters:["BooleanSetter"]},...r(u.props,["direction"])],events:[{name:"imageClick"},{name:"titleClick"},{name:"actionClick",params:["action"]},{name:"actionCommand",params:["action","menu"]}],slots:[{name:"image"},{name:"title"},{name:"description"},{name:"actions"},{name:"default"}],snippet:{props:{title:"标题文本",description:"描述文本示例内容"}}},n={name:"XDialog",label:"弹窗",categoryId:"layout",props:[{name:"modelValue",title:"控制是否显示弹窗",setters:"BooleanSetter",defaultValue:!0},{name:"title",setters:"StringSetter"},{name:"subtitle",setters:"StringSetter"},{name:"icon",setters:"IconSetter"},e(),{name:"width",setters:["StringSetter","NumberSetter"]},{name:"height",setters:["StringSetter","NumberSetter"]},{name:"left",setters:["StringSetter","NumberSetter"]},{name:"top",setters:["StringSetter","NumberSetter"]},{name:"modal",setters:"BooleanSetter",defaultValue:!0},{name:"draggable",setters:"BooleanSetter",defaultValue:!0},{name:"resizable",setters:"BooleanSetter"},{name:"closable",setters:"BooleanSetter",defaultValue:!0},{name:"maximizable",setters:"BooleanSetter"},{name:"minimizable",setters:"BooleanSetter"},{name:"mode",setters:"SelectSetter",options:["normal","maximized","minimized"],defaultValue:"normal"},{name:"src",title:"加载页码url",setters:"StringSetter"},{name:"beforeClose",title:"关闭弹窗回调函数，return true 阻止关闭",setters:"FunctionSetter"},{name:"submit",setters:["Boolean","StringSetter"]},{name:"cancel",setters:["Boolean","StringSetter"]},{name:"bodyPadding",setters:"BooleanSetter"},{name:"primary",setters:"BooleanSetter"},...r(d.props,["size"])],events:[{name:"update:modelValue",params:["modelValue"]},{name:"open",params:["instance"]},{name:"close"},{name:"destroy"},{name:"maximized"},{name:"minimized"},{name:"normal"},{name:"modeChange",params:["mode"]},{name:"dragStart",params:["position"]},{name:"dragging",params:["position"]},{name:"dragEnd",params:["position"]},{name:"resizeStart",params:["dir","mie"]},{name:"resizeEnd",params:["dir","mie"]},{name:"resizing",params:["dir","mie"]},{name:"submit"},{name:"cancel"}],slots:[{name:"title"},{name:"actions"},{name:"default"},{name:"footer"},{name:"extra"},{name:"handle"}],snippet:{props:{title:"弹窗标题"}}},k={name:"XDialogForm",label:"弹窗表单",categoryId:"form",props:[{name:"modelValue",setters:"BooleanSetter",defaultValue:!0},e(),{name:"submit",setters:["StringSetter","BooleanSetter"],defaultValue:"确定"},{name:"cancel",setters:["StringSetter","BooleanSetter"],defaultValue:"取消"},{name:"model",setters:"ObjectSetter"},{name:"rules",setters:"ObjectSetter"},{name:"formProps",title:"表单XForm组件参数选项",setters:"ObjectSetter"},{name:"submitMethod",title:"表单提交处理方法, return true 关闭弹窗",setters:"FunctionSetter"},...r(n.props,["modelValue","size","submit","cancel"])],events:[{name:"update:modelValue",params:["modelValue"]},{name:"submit",params:["model"]},{name:"close"}],slots:["default","extra","handle","footer"],snippet:{props:{title:"弹窗表单"}}},p=[{name:"ElForm",label:"表单",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/form.html",package:"element-plus",props:[{name:"model",title:"表单数据对象",defaultValue:"",setters:"ExpressionSetter"},{name:"rules",defaultValue:"",setters:"ExpressionSetter"},{name:"inline",defaultValue:!1,setters:"BooleanSetter"},{name:"labelPosition",defaultValue:"right",options:["left","right","top"],setters:"SelectSetter"},{name:"labelWidth",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"labelSuffix",defaultValue:"",setters:"InputSetter"},{name:"hideRequiredAsterisk",defaultValue:!1,title:"是否显示必填字段的标签旁边的红色星号",setters:"BooleanSetter"},{name:"requireAsteriskPosition",defaultValue:"left",title:"星号的位置",options:["left","right"],setters:"SelectSetter"},{name:"showMessage",defaultValue:!0,title:"是否显示校验错误信息",setters:"BooleanSetter"},{name:"inlineMessage",defaultValue:!1,title:"是否以行内形式展示校验信息",setters:"BooleanSetter"},{name:"statusIcon",defaultValue:!1,title:"是否在输入框中显示校验结果反馈图标",setters:"BooleanSetter"},{name:"validateOnRuleChange",defaultValue:!0,title:"是否在 rules 属性改变后立即触发一次验证",setters:"BooleanSetter"},{name:"size",defaultValue:"",options:["large","default","small"],setters:"SelectSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollToError",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollIntoViewOptions",defaultValue:"",setters:["ExpressionSetter","BooleanSetter"]}],events:[{name:"validate"}],slots:["default"],snippet:{name:"ElForm",props:{labelWidth:"80px"},children:[{name:"ElFormItem",props:{label:"表单项"},children:[{name:"ElInput"}]},{name:"ElFormItem",props:{label:" "},children:[{name:"ElButton",props:{type:"primary"},children:"确定"}]}]}},{name:"ElFormItem",label:"表单项",categoryId:"form",package:"element-plus",props:[{name:"prop",defaultValue:"",setters:["InputSetter","ArraySetter"]},{name:"label",defaultValue:"",setters:"InputSetter"},{name:"labelPosition",defaultValue:"",setters:"SelectSetter",options:["left","right","top"]},{name:"labelWidth",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"required",defaultValue:!1,setters:"BooleanSetter"},{name:"rules",defaultValue:"",setters:"JSONSetter"},{name:"error",defaultValue:"",setters:"InputSetter"},{name:"showMessage",defaultValue:!0,title:"是否显示校验错误信息",label:"错误信息",setters:"BooleanSetter"},{name:"inlineMessage",defaultValue:!1,title:"是否在行内显示校验信息",label:"校验信息",setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"for",defaultValue:"",setters:"StringSetter"},{name:"validateStatus",title:"formitem 校验的状态",options:["","error","validating","success"],setters:"SelectSetter"}],slots:["default","label","error"],snippet:{props:{label:"表单项"},children:[{name:"ElInput"}]}}],C={name:"XField",label:"字段",categoryId:"form",props:[{name:"name",title:"字段名称",setters:"StringSetter"},{name:"label",title:"字段标题文本",setters:"StringSetter"},{name:"editor",title:"编辑器组件",setters:"SelectSetter",options:["none","text","textarea","select","checkbox","radio","number","date","time","datetime","switch","slider","rate","cascader","picker"]},{name:"props",title:"编辑器组件参数",setters:"ObjectSetter"},{name:"modelValue",setters:"StringSetter"},e(),{name:"width",setters:["StringSetter","NumberSetter"]},{name:"tooltipMessage",title:"是否在tooltip显示校验信息",setters:["BooleanSetter","ObjectSetter"]},{name:"tooltipPosition",setters:["SelectSetter","NumberSetter"],options:["inner","outer"],defaultValue:"outer"},{name:"placeholder",setters:"StringSetter"},{name:"disabled",setters:"BooleanSetter"},{name:"readonly",setters:"BooleanSetter"},{name:"options",setters:["ArraySetter","FunctionSetter"]},{name:"visible",setters:"BooleanSetter"},{name:"cascader",title:"级联字段，根据字段值变化刷新options",setters:["StringSetter","ArraySetter"]},{name:"error",setters:"StringSetter"},{name:"tip",setters:"StringSetter"},{name:"inline",title:"表单项内容采用inline布局",setters:"BooleanSetter"},{name:"hidden",title:"隐藏域",setters:"BooleanSetter"},{name:"defaultValue",title:"默认值",setters:"StringSetter"},...r(p[1].props,["prop","size","label"])],events:["update:modelValue","change","focus","blur"],slots:[{name:"label"},{name:"error",params:["error"]},{name:"editor",params:["editor"]},{name:"tip"},{name:"default"}],snippet:{props:{label:"字段名称"}}},o={name:"XForm",label:"表单",categoryId:"form",props:[{name:"model",setters:"ObjectSetter"},{name:"inline",setters:"BooleanSetter"},{name:"inlineColumns",title:"inline模式显示列数",setters:"NumberSetter"},{name:"footer",setters:"BooleanSetter"},{name:"submitText",setters:"StringSetter",defaultValue:"提交"},{name:"resetText",setters:"StringSetter",defaultValue:"重置"},{name:"submitMethod",title:"表单提交处理方法",setters:"FunctionSetter"},{name:"tooltipMessage",title:"是否在tooltip显示校验信息",setters:["BooleanSetter","ObjectSetter"]},{name:"enterSubmit",title:"回车键触发提交",setters:"BooleanSetter"},{name:"sticky",title:"开启底部sticky定位",setters:"BooleanSetter"},{name:"footerAlign",title:"底部对齐方式",setters:"SelectSetter",defaultValue:"left",options:["left","center","right"]},...r(p[0].props,["model","inline"])],events:[{name:"change",params:["model"]},{name:"submit",params:["model"]},{name:"reset"}],slots:["default","footer","action"],snippet:{props:{labelWidth:"100px"},children:[{name:"XField",props:{label:"字段名称"}}]}},N={name:"XGrid",label:"表格",categoryId:"data",doc:"https://vxetable.cn/#/grid/api",props:[{name:"columns",setters:"ArraySetter"},{name:"loader",title:"数据加载函数: (state) => {list, total}",setters:"FunctionSetter"},{name:"rowSortable",title:"行拖拽排序",setters:"BooleanSetter"},{name:"columnSortable",title:"列拖拽排序",setters:"BooleanSetter"},{name:"customable",title:"开启用户自定义",setters:"BooleanSetter"},{name:"resizable",setters:"BooleanSetter"},{name:"pager",title:"开启分页功能",setters:"BooleanSetter"},{name:"page",setters:"NumberSetter"},{name:"pageSize",setters:"NumberSetter"},{name:"pageSizes",title:"每页显示个数选择器的选项设置",setters:"ArraySetter"},{name:"auto",title:"初始执行加载函数",setters:"BooleanSetter",defaultValue:!0},{name:"virtual",title:"开启虚拟滚动",setters:"BooleanSetter"},{name:"editable",title:"开启编辑模式",setters:"BooleanSetter"},{name:"cellRenders",title:"单元格渲染器",setters:"ObjectSetter"},{name:"editRenders",title:"单元格渲染器",setters:"ObjectSetter"},{name:"filterRenders",title:"过滤器渲染器",setters:"ObjectSetter"},{name:"id",setters:"StringSetter"},{name:"data",setters:"ArraySetter"},{name:"height",setters:["StringSetter","NumberSetter"]},{name:"minHeight",setters:["StringSetter","NumberSetter"]},{name:"maxHeight",setters:["StringSetter","NumberSetter"]},{name:"autoResize",setters:"BooleanSetter"},{name:"syncResize",setters:["BooleanSetter","StringSetter"]},{name:"stripe",setters:"BooleanSetter"},{name:"border",setters:"BooleanSetter"},{name:"round",setters:"BooleanSetter"},{name:"size",setters:"SelectSetter",options:["small","mini"]},{name:"loading",setters:"BooleanSetter"},{name:"align",setters:"SelectSetter",options:["left","center","right"]},{name:"HeaderAlign",setters:"SelectSetter",options:["left","center","right"]},{name:"FooterAlign",setters:"SelectSetter",options:["left","center","right"]},{name:"showHeader",setters:"BooleanSetter"},{name:"showFooter",setters:"BooleanSetter"},{name:"footerData",setters:"ArraySetter"},{name:"mergeCells",setters:"FunctionSetter"},{name:"mergeFooterItems",setters:"FunctionSetter"},{name:"showOverflow",setters:"BooleanSetter"},{name:"showHeaderOverflow",setters:"BooleanSetter"},{name:"showFooterOverflow",setters:"BooleanSetter"},{name:"keepSource",setters:"BooleanSetter"},{name:"columnConfig",setters:"ObjectSetter"},{name:"rowConfig",setters:"ObjectSetter"},{name:"resizeConfig",setters:"ObjectSetter"},{name:"resizableConfig",setters:"ObjectSetter"},{name:"seqConfig",setters:"ObjectSetter"},{name:"sortConfig",setters:"ObjectSetter"},{name:"filterConfig",setters:"ObjectSetter"},{name:"exportConfig",setters:"ObjectSetter"},{name:"importConfig",setters:"ObjectSetter"},{name:"printConfig",setters:"ObjectSetter"},{name:"radioConfig",setters:"ObjectSetter"},{name:"checkboxConfig",setters:"ObjectSetter"},{name:"tooltipConfig",setters:"ObjectSetter"},{name:"expandConfig",setters:"ObjectSetter"},{name:"treeConfig",setters:"ObjectSetter"},{name:"menuConfig",setters:"ObjectSetter"},{name:"mouseConfig",setters:"ObjectSetter"},{name:"keyboardConfig",setters:"ObjectSetter"},{name:"editConfig",setters:"ObjectSetter"},{name:"validConfig",setters:"ObjectSetter"},{name:"editRules",setters:"ObjectSetter"},{name:"emptyText",setters:"StringSetter"},{name:"emptyRender",setters:"ObjectSetter"},{name:"loadingConfig",setters:"ObjectSetter"},{name:"customConfig",setters:"ObjectSetter"},{name:"scrollX",setters:"ObjectSetter"},{name:"scrollY",setters:"ObjectSetter"},{name:"params",setters:"ObjectSetter"},{name:"toolbarConfig",setters:"ObjectSetter"},{name:"pagerConfig",setters:"ObjectSetter"},{name:"proxyConfig",setters:"ObjectSetter"},{name:"zoomConfig",setters:"ObjectSetter"},{name:"layouts",setters:"ArraySetter"}],events:["rowSort","columnSort","editChange","keydown","current-change","radio-change","checkbox-change","checkbox-all","checkbox-range-start","checkbox-range-change","checkbox-range-end","cell-click","cell-dblclick","cell-menu","cell-mouseenter","cell-mouseleave","cell-delete-value","header-cell-click","header-cell-dblclick","header-cell-menu","footer-cell-click","footer-cell-dblclick","footer-cell-menu","clear-merge","sort-change","clear-sort","filter-change","filter-visible","clear-filter","resizable-change","toggle-row-expand","toggle-tree-expand","menu-click","cell-selected","edit-closed","edit-activated","edit-disabled","valid-error","scroll","custom","page-change","proxy-query","proxy-delete","proxy-save","toolbar-button-click","toolbar-tool-click","zoom"],slots:["empty","form","top","bottom","toolbar__buttons","pager__left"],snippet:{props:{height:"auto",columns:[{type:"seq",title:"#"},{field:"title",title:"标题"}]}}},F={name:"XHeader",label:"标题头",categoryId:"layout",props:[e(),{name:"content",setters:"StringSetter"},{name:"subtitle",setters:"StringSetter"},{name:"icon",setters:"IconSetter"},{name:"border",setters:"BooleanSetter"},{name:"more",setters:"BooleanSetter"}],events:["click","clickIcon"],slots:["default","subtitle","actions"],snippet:{props:{content:"标题内容文本",subtitle:"这里是子标题内容示例"}}},E={name:"XList",label:"列表",categoryId:"data",props:[{name:"data",setters:["ObjectSetter","FunctionSetter"]},{name:"itemHeight",title:"设置 itemHeight 即自动开启虚拟滚动",setters:["StringSetter","NumberSetter"]},{name:"width",setters:["StringSetter","NumberSetter"]},{name:"height",setters:["StringSetter","NumberSetter"]},{name:"pager",setters:["BooleanSetter","ObjectSetter"]},{name:"page",setters:"NumberSetter",defaultValue:1},{name:"pageSize",setters:"NumberSetter",defaultValue:10},{name:"dataKey",title:"数据主键属性名称",setters:"StringSetter"},{name:"infiniteScroll",setters:["BooleanSetter","ObjectSetter"]}],events:[{name:"load",params:["state"]}],slots:[{name:"empty"},{name:"default",params:["item","index"]},{name:"loading"},{name:"nomore"}],snippet:{props:{data:{list:["列表项内容一","列表项内容二"],total:2}}}},z={name:"XQueryForm",label:"查询表单",categoryId:"form",props:[{name:"collapsible",setters:"BooleanSetter"},{name:"items",setters:"ArraySetter"},...r(o.props,[])],events:[...o.events||[],"collapsed"],slots:[{name:"default"}],snippet:{children:[{name:"XField",props:{label:"条件名称"}}]}},S=[{name:"ElTabs",label:"标签页",doc:"https://element-plus.org/zh-CN/component/tabs.html",categoryId:"nav",package:"element-plus",props:[{name:"modelValue",setters:["InputSetter","NumberSetter"]},{name:"type",setters:[{name:"SelectSetter",props:{closable:!0}}],defaultValue:"",options:["","card","border-card"]},{name:"closable",defaultValue:!1,setters:"BooleanSetter"},{name:"addable",defaultValue:!1,setters:"BooleanSetter"},{name:"editable",defaultValue:!1,setters:"BooleanSetter"},{name:"tabPosition",defaultValue:"top",setters:"SelectSetter",options:["top","right","bottom","left"]},{name:"stretch",defaultValue:!1,setters:"BooleanSetter"},{name:"beforeLeave",defaultValue:"",setters:"FunctionSetter"}],events:[{name:"tab-click"},{name:"tab-change"},{name:"tab-remove"},{name:"tab-add"},{name:"edit"},{name:"update:modelValue"}],slots:["default","addIcon","add-icon"],snippet:{props:{modelValue:"1"},children:[{name:"ElTabPane",children:"面板一内容",props:{label:"面板一",name:"1"}},{name:"ElTabPane",children:"面板二内容",props:{label:"面板二",name:"2"}},{name:"ElTabPane",children:"面板三内容",props:{label:"面板三",name:"3"}}]}},{name:"ElTabPane",label:"标签页面板",categoryId:"nav",package:"element-plus",props:[{name:"label",defaultValue:"",setters:"InputSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"name",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"closable",defaultValue:!1,setters:"BooleanSetter"},{name:"lazy",defaultValue:!1,setters:"BooleanSetter"}],slots:[{name:"default"},{name:"label"}],snippet:{props:{label:"面板标题"},children:"面板内容"}}],T={name:"XTabs",label:"选项卡",categoryId:"layout",props:[{name:"items",setters:"ArraySetter"},{name:"border",setters:"BooleanSetter"},{name:"fit",setters:"BooleanSetter"},{name:"align",setters:"SelectSetter",options:["left","center","right"]},...r(S[0].props,[])],events:[...S[0].events||[],"actionClick","actionCommand"],slots:["label","default"],snippet:{props:{modelValue:"1",items:[{label:"Tab 1",value:"1"},{label:"Tab 2",value:"2"},{label:"Tab 3",value:"3"}]}}},c=[{name:"ElSelect",label:"选择器",doc:"https://element-plus.org/zh-CN/component/select.html",categoryId:"form",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["NumberSetter","InputSetter","BooleanSetter"]},{name:"multiple",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"valueKey",defaultValue:"value",setters:"InputSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTags",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTagsTooltip",title:"当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签。 要使用此属性，collapse-tags属性必须设定为 true",defaultValue:!1,setters:"BooleanSetter"},{name:"multipleLimit",defaultValue:0,setters:"NumberSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"light",options:["dark","light"],setters:"SelectSetter"},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"placeholder",defaultValue:"Select",setters:"InputSetter"},{name:"filterable",defaultValue:!1,setters:"BooleanSetter"},{name:"allowCreate",defaultValue:!1,setters:"BooleanSetter"},{name:"filterMethod",defaultValue:"",setters:"FunctionSetter"},{name:"remote",defaultValue:!1,setters:"BooleanSetter"},{name:"remoteMethod",defaultValue:"",setters:"FunctionSetter"},{name:"remoteShowSuffix",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"loadingText",defaultValue:"Loading",setters:"InputSetter"},{name:"noMatchText",defaultValue:"No matching data",setters:"InputSetter"},{name:"noDataText",defaultValue:"No data",setters:"InputSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"reserveKeyword",defaultValue:!0,setters:"BooleanSetter"},{name:"defaultFirstOption",defaultValue:!1,setters:"BooleanSetter"},{name:"teleported",defaultValue:!0,setters:"BooleanSetter"},{name:"appendTo",title:"下拉框挂载到哪个 DOM 元素",defaultValue:"",setters:"StringSetter"},{name:"persistent",defaultValue:!0,setters:"BooleanSetter"},{name:"automaticDropdown",defaultValue:!1,setters:"BooleanSetter"},{name:"clearIcon",defaultValue:"CircleClose",setters:"InputSetter"},{name:"fitInputWidth",defaultValue:!1,setters:"BooleanSetter"},{name:"suffixIcon",defaultValue:"ArrowUp",setters:"InputSetter"},{name:"tagType",defaultValue:"info",options:["success","info","warning","danger"],setters:"SelectSetter"},{name:"tagEffect",defaultValue:"light",options:["","light","dark","plain"],setters:"SelectSetter"},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"},{name:"offset",defaultValue:12,setters:"NumberSetter"},{name:"showArrow",defaultValue:!0,setters:"BooleanSetter"},{name:"placement",label:"placement",title:"下拉框出现的位置",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],defaultValue:"bottom-start"},{name:"fallbackPlacements",label:"fallbackPlacements",title:"dropdown 可用的 positions",setters:"ArraySetter",defaultValue:["bottom-start","top-start","right","left"]},{name:"maxCollapseTags",label:"maxCollapseTags",title:"需要显示的 Tag 的最大数量 只有当 collapse-tags 设置为 true 时才会生效。",setters:"NumberSetter",defaultValue:1},{name:"popperOptions",label:"popperOptions",title:"popper.js 参数",setters:"ObjectSetter",defaultValue:{}},{name:"ariaLabel",label:"ariaLabel",title:"等价于原生 input aria-label 属性",setters:"StringSetter"},{name:"emptyValues",title:"组件的空值配置",setters:"ArraySetter"},{name:"valueOnClear",title:"清空选项的值 ",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]},{name:"suffixTransition",title:"下拉菜单显示/消失时后缀图标的动画",defaultValue:!0,setters:"BooleanSetter"},{name:"tabindex",title:"input 的 tabindex",setters:["StringSetter","NumberSetter"]}],events:["change","visible-change","remove-tag","clear","blur","focus","update:modelValue"],slots:[{name:"default"},{name:"header"},{name:"footer"},{name:"prefix"},{name:"empty"},{name:"tag"},{name:"loading"},{name:"label"}],snippet:{children:[{name:"ElOption",props:{label:{type:"JSExpression",value:"`选项${this.context.item}`"}},directives:[{name:"vFor",value:{type:"JSExpression",value:"6"}}]}]}},{name:"ElOptionGroup",label:"选择器选项组",categoryId:"form",package:"element-plus",parentIncludes:["ElSelect"],props:[{name:"label",defaultValue:"",setters:"InputSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{props:{label:"分组"}}},{name:"ElOption",label:"选择器选项",categoryId:"form",package:"element-plus",parentIncludes:["ElSelect","ElOptionGroup"],props:[{name:"value",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter","JSONSetter"]},{name:"label",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{props:{label:"选项"}}}],P={name:"XPicker",label:"数据选择器",categoryId:"form",childIncludes:!1,props:[{name:"columns",title:"表格列配置",setters:"ArraySetter"},{name:"fields",title:"查询条件表单字段配置",setters:"ArraySetter"},{name:"loader",title:"表格数据加载函数",setters:"FunctionSetter"},{name:"modelValue",setters:"StringSetter"},{name:"multiple",title:"多选模式",setters:"BooleanSetter"},{name:"raw",title:"值为对象模式",setters:"BooleanSetter"},{name:"disabled",setters:"BooleanSetter"},{name:"append",title:"多选可追加",setters:"BooleanSetter"},{name:"valueKey",title:"值映射字段名称",setters:"StringSetter",defaultValue:"value"},{name:"labelKey",title:"输入框显示映射字段名称",setters:"StringSetter",defaultValue:"label"},{name:"queryKey",title:"查询参数名称",setters:"StringSetter"},{name:"preload",title:"单选模式，回车时自动检测取回有且计有唯一数据",setters:"BooleanSetter"},{name:"defaultQuery",title:"初始默认查询参数 () => Record<string,any>",setters:"FunctionSetter"},{name:"dialogProps",title:"弹窗组件配置参数",setters:"ObjectSetter"},{name:"gridProps",title:"表格组件配置参数",setters:"ObjectSetter"},{name:"formProps",title:"查询表单配置参数",setters:"ObjectSetter"},{name:"formatter",title:"接受数据转换函数",setters:"FunctionSetter"},{name:"valueFormatter",title:"发送数据转换函数",setters:"FunctionSetter"},...r(c[0].props,["modelValue","disabled","valueKey","teleported","multiple"])],events:["picked",...c[0].events||[]],slots:[],snippet:{props:{}}},A={name:"ElButton",label:"按钮",categoryId:"base",doc:"https://element-plus.org/zh-CN/component/button.html",props:[e("size"),a("type"),{name:"plain",defaultValue:!1,setters:"BooleanSetter"},{name:"text",defaultValue:!1,setters:"BooleanSetter"},{name:"bg",defaultValue:!1,setters:"BooleanSetter"},{name:"link",defaultValue:!1,setters:"BooleanSetter"},{name:"round",defaultValue:!1,setters:"BooleanSetter"},{name:"circle",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"loadingIcon",defaultValue:void 0,setters:"IconSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"icon",defaultValue:void 0,setters:"IconSetter"},{name:"autofocus",defaultValue:!1,setters:"BooleanSetter"},{name:"nativeType",defaultValue:"button",setters:"SelectSetter",options:["button ","submit","reset"]},{name:"autoInsertSpace",setters:"BooleanSetter"},{name:"color",setters:"StringSetter"},{name:"dark",defaultValue:!1,setters:"BooleanSetter"},{name:"tag",setters:"StringSetter",defaultValue:"button"}],events:["click"],slots:["default","loading","icon","tag"],snippet:{name:"ElButton",children:"按钮",props:{type:"primary"}}},M={name:"ElButtonGroup",childIncludes:["ElButton"],label:"按钮组",categoryId:"base",props:[e("size"),a("type")],slots:["default"],snippet:{name:"ElButtonGroup",children:[{name:"ElButton",children:"Button1"},{name:"ElButton",children:"Button2"},{name:"ElButton",children:"Button3"}]}},X={name:"XImportButton",label:"导入按钮",categoryId:"base",props:[{name:"uploader",title:"文件上传函数",setters:"FunctionSetter"},{name:"multiple",setters:"BooleanSetter"},{name:"accept",title:"接受上传的文件类型",setters:"StringSetter"},{name:"parser",setters:["SelectSetter","FunctionSetter"],options:["text","json","base64"]},...[A,M][0].props||[]],events:["success","fail"],slots:["default"],snippet:{props:{}}},$={name:"XDialogGrid",label:"弹窗表格",categoryId:"form",props:[{name:"modelValue",setters:"BooleanSetter",defaultValue:!0},{name:"columns",title:"表格列配置",setters:"ArraySetter"},{name:"model",setters:"ObjectSetter"},{name:"rules",setters:"ObjectSetter"},{name:"formatter",title:" model数据转换为表格数据",setters:"FunctionSetter"},{name:"valueFormatter",title:" 表格数据转换为model数据",setters:"FunctionSetter"},{name:"puls",title:"显示增行按钮",setters:"BooleanSetter",defaultValue:!0},{name:"minus",title:"显示删行按钮",setters:"BooleanSetter",defaultValue:!0},{name:"submitMethod",title:"表单提交处理方法, return true 关闭弹窗",setters:"FunctionSetter"},{name:"gridProps",title:"表格其他配置",setters:"ObjectSetter"},...r(n.props,["modelValue","size","submit","cancel"])],events:[{name:"update:modelValue",params:["modelValue"]},{name:"submit",params:["model"]},{name:"close"}],slots:["buttons","extra","top"],snippet:{props:{title:"弹窗表格"}}},s={props:[{name:"type",defaultValue:"text",options:["text","textarea"],setters:["SelectSetter","InputSetter"]},{name:"modelValue",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"maxlength",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"minlength",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"showWordLimit",defaultValue:!1,title:'是否显示输入字数统计，只在 type = "text" 或 type = "textarea" 时有效',label:"字数统计",setters:"BooleanSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"formatter",defaultValue:"",setters:"FunctionSetter"},{name:"parser",defaultValue:"",setters:"FunctionSetter"},{name:"showPassword",defaultValue:!1,title:"是否显示切换密码图标",label:"密码图标",setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},e("size"),{name:"prefix-icon",defaultValue:"",setters:"InputSetter"},{name:"suffix-icon",defaultValue:"",setters:"InputSetter"},{name:"rows",defaultValue:2,setters:"NumberSetter"},{name:"autosize",defaultValue:!1,setters:["BooleanSetter","JSONStter"]},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"max",defaultValue:"",setters:"InputSetter"},{name:"min",defaultValue:"",setters:"InputSetter"},{name:"step",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"resize",defaultValue:"",options:["none","both","horizontal","vertical"],setters:"InputSetter"},{name:"autofocus",defaultValue:!1,setters:"BooleanSetter"},{name:"form",defaultValue:"",setters:"InputSetter"},{name:"aria-label",defaultValue:"",setters:"InputSetter"},{name:"tabindex",defaultValue:"",setters:"InputSetter"},{name:"validateEvent",defaultValue:!0,title:"输入时是否触发表单的校验",label:"表单校验",setters:"BooleanSetter"},{name:"inputStyle",defaultValue:{},setters:["JSONSetter"]}],events:[{name:"blur"},{name:"focus"},{name:"change"},{name:"input"},{name:"clear"},{name:"update:modelValue"}]},J={name:"XCaptcha",label:"图形验证码",categoryId:"form",props:[{name:"src",setters:"FunctionSetter",title:"() => MaybePromise<string>"},{name:"maxlength",title:"验证码长度",setters:"NumberSetter",defaultValue:4},{name:"placeholder",setters:"StringSetter",defaultValue:"请输入图形验证码"},{name:"validate",setters:"FunctionSetter",title:"校验函数：(value: string) => MaybePromise<boolean>"},...r(s.props,["maxlength","placeholder"])],events:[...s.events||[]],snippet:{props:{src:{type:"JSFunction",value:"() => 'https://dummyimage.com/300x120'"}}}},q={name:"XVerify",label:"短信验证码",categoryId:"form",props:[{name:"api",setters:"FunctionSetter",title:"() => Promise<boolean>"},{name:"maxlength",title:"验证码长度",setters:"NumberSetter",defaultValue:6},{name:"placeholder",setters:"StringSetter",defaultValue:"请输入图形验证码"},{name:"seconds",setters:"NumberSetter",title:"可重发秒数",defaultValue:60},...r(s.props,["maxlength","placeholder"])],events:[...s.events||[]],snippet:{props:{src:{type:"JSFunction",value:"async () => true"}}}},f=[{name:"ElMenu",label:"导航菜单",doc:"https://element-plus.org/zh-CN/component/menu.html",categoryId:"nav",package:"element-plus",props:[{name:"mode",defaultValue:"vertical",setters:"SelectSetter",options:["horizontal","vertical"]},{name:"collapse",defaultValue:!1,setters:"BooleanSetter"},{name:"ellipsis",defaultValue:!0,setters:"BooleanSetter"},{name:"ellipsisIcon",label:"ellipsisIcon",title:"自定义省略图标 (仅在水平模式下可用)",setters:"StringSetter"},{name:"popperOffset",label:"popperOffset",title:"弹出层的偏移量(对所有子菜单有效)",setters:"NumberSetter",defaultValue:6},{name:"defaultActive",defaultValue:"",setters:"InputSetter"},{name:"defaultOpeneds",defaultValue:[],setters:"ArraySetter"},{name:"uniqueOpened",defaultValue:!1,setters:"BooleanSetter"},{name:"menuTrigger",defaultValue:"hover",setters:"SelectSetter",options:["hover","click"]},{name:"router",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTransition",defaultValue:!0,setters:"BooleanSetter"},{name:"popperEffect",label:"popperEffect",title:"Tooltip 主题，内置了 dark / light 两种主题",setters:"SelectSetter",options:["dark","light"],defaultValue:"dark"},{name:"closeOnClickOutside",label:"closeOnClickOutside",title:"可选，单击外部时是否折叠菜单",setters:"BooleanSetter",defaultValue:!1},{name:"popperClass",label:"popperClass",title:"为 popper 添加类名",setters:"StringSetter"},{name:"showTimeout",label:"showTimeout",title:"菜单出现前的延迟",setters:"NumberSetter",defaultValue:300},{name:"hideTimeout",label:"hideTimeout",title:"菜单消失前的延迟",setters:"NumberSetter",defaultValue:300},{name:"backgroundColor",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"textColor",defaultValue:"#303133",setters:"ColorSetter"},{name:"activeTextColor",defaultValue:"#409EFF",setters:"ColorSetter"}],events:[{name:"select"},{name:"open"},{name:"close"}],slots:["default","ellipsis-icon"],snippet:{props:{mode:"horizontal"},children:[{name:"ElMenuItem",children:"菜单项一",props:{index:"1"}},{name:"ElSubMenu",props:{index:"2"},children:[{name:"component",slot:"title",props:{is:"div"},children:"子菜单"},{name:"ElMenuItem",children:"子菜单项一",props:{index:"2-1"}},{name:"ElMenuItem",children:"子菜单项二",props:{index:"2-2"}}]},{name:"ElMenuItem",children:"菜单项三",props:{index:"3"}}]}},{name:"ElSubMenu",label:"导航子菜单",categoryId:"nav",package:"element-plus",props:[{name:"index",label:"index *",defaultValue:"",setters:"InputSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"showTimeout",setters:"NumberSetter"},{name:"hideTimeout",setters:"NumberSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"teleported",defaultValue:void 0,setters:"BooleanSetter"},{name:"popperOffset",defaultValue:6,setters:"NumberSetter"},{name:"expandCloseIcon",defaultValue:"",setters:["InputSetter"]},{name:"expandOpenIcon",defaultValue:"",setters:["InputSetter"]},{name:"collapseCloseIcon",defaultValue:"",setters:["InputSetter"]},{name:"collapseOpenIcon",defaultValue:"",setters:["InputSetter"]}],slots:[{name:"default"},{name:"title"}],snippet:{children:[{name:"component",slot:"title",props:{is:"div"},children:"子菜单"},{name:"ElMenuItem",children:"子菜单项一"}]}},{name:"ElMenuItem",label:"导航菜单项",categoryId:"nav",package:"element-plus",props:[{name:"index",defaultValue:null,setters:"InputSetter"},{name:"route",defaultValue:"",setters:["StringSetter","JSONSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"click"}],slots:[{name:"default"},{name:"title"}],snippet:{children:"菜单项"}},{name:"ElMenuItemGroup",label:"导航菜单组",categoryId:"nav",package:"element-plus",props:[{name:"title",defaultValue:"",setters:"InputSetter"}],slots:[{name:"default"},{name:"title"}],snippet:{props:{title:"分组一"},children:[{name:"ElMenuItem",children:"子菜单项一"},{name:"ElMenuItem",children:"子菜单项一"}]}}],R={name:"XMenu",label:"菜单",categoryId:"data",props:[{name:"data",title:"菜单项数据",setters:"ArraySetter"},...f[0].props||[],{name:"subMenu",title:"ElSubMenu组件参数配置",setters:"ObjectSetter"},{name:"defaultIcon",title:"默认Icon",setters:"ExpressionSetter"}],events:[...f[0].events||[]],slots:[],snippet:{props:{data:[{id:"1",title:"菜单一",icon:"Setting",children:[{id:"1-1",title:"子菜单一"},{id:"1-2",title:"子菜单一"}]},{id:"2",title:"菜单二",icon:"Edit",children:[{id:"2-1",title:"子菜单一"},{id:"2-2",title:"子菜单一"}]}]}}},D="@vtj/ui",H=[y,I,u,v,x,w,d,O,j,n,k,C,o,N,F,E,z,T,P,X,$,J,q,R].flat();return{name:"@vtj/ui",version:t,label:"UI",library:"VtjUIMaterial",order:1,categories:[{id:"base",category:"基础元件"},{id:"layout",category:"布局排版"},{id:"form",category:"表单"},{id:"data",category:"数据展示"},{id:"test",category:"测试套件"}],components:B(H,D)}});
