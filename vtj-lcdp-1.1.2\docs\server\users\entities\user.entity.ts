import { <PERSON>ti<PERSON>, Column, ManyTo<PERSON>any, JoinTable } from 'typeorm';
import { BaseEntity, OAuthType, defaultUserPassword } from '../../shared';
import { Role } from '../../roles/entities/role.entity';

@Entity({
  name: 'users',
  comment: '用户表'
})
export class User extends BaseEntity {
  @Column({ comment: '用户名', unique: true })
  name: string;

  @Column('varchar', {
    comment: 'MD5密码',
    length: 32,
    select: false,
    default: defaultUserPassword
  })
  password: string;

  @Column({ comment: '头像', nullable: true })
  avatar: string;

  @Column({ comment: '邮箱', nullable: true })
  email: string;

  @Column({ comment: '电话号码', nullable: true })
  phone: string;

  @Column('enum', {
    name: 'oauth_type',
    enum: OAuthType,
    comment: '第三方登录类型',
    nullable: true
  })
  oauthType: OAuthType;

  @Column({ comment: '第三方平台用户标识', name: 'oauth_id', nullable: true })
  oauthId: string;

  @Column({
    comment: '冻结',
    default: false
  })
  freeze: boolean;

  @ManyToMany(() => Role, { cascade: true })
  @JoinTable({ name: 'user_r_role' })
  roles: Role[];

  @Column({ comment: '最后登录时间', name: 'last_login_at', nullable: true })
  lastLoginAt: Date;
}
