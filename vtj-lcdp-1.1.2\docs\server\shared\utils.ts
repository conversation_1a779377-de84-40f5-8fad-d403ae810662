import { MD5, groupBy } from '@vtj/node';
import { SALT, USER_INIT_PASSWORD } from './constants';
import { QueryDto } from './dto';
import type { Request } from 'express';

/**
 * MD5 加密
 * @param content
 * @returns
 */
export function passwordHash(content: string) {
  return MD5(content + SALT);
}

/**
 * 分页查询条件转换
 * @param dto
 * @param defaultPage
 * @param defaultLimit
 * @returns
 */
export function pager(
  dto?: QueryDto,
  defaultPage: number = 1,
  defaultLimit: number = 10
) {
  const { page = defaultPage, limit = defaultLimit } = dto || {};
  const skip = (page - 1) * limit;
  return {
    page,
    skip,
    limit,
    take: limit
  };
}

export const defaultUserPassword = passwordHash(USER_INIT_PASSWORD);

/**
 * 列表转树结构
 * @param array
 * @param id
 * @param parentId
 * @param children
 * @param root
 * @returns
 */
export function arrayToTree<T extends Record<string, any>>(
  array: T[],
  id: keyof T = 'id',
  parentId: keyof T = 'parentId',
  children: string = 'children',
  root: string | number = 'null'
) {
  const groupByParentId = groupBy(array, parentId);
  for (const items of Object.values(groupByParentId)) {
    items.forEach((item: any) => {
      item[children] = groupByParentId[item[id]] || [];
    });
  }
  return groupByParentId[root] || [];
}

export function getIP(req: Request) {
  const ip = req.ip;
  const xForwardedFor = req.headers['x-forwarded-for'];
  const realIpHeader = req.headers['x-real-ip'];

  let clientIp: string;
  if (Array.isArray(xForwardedFor)) {
    clientIp = xForwardedFor[0].split(',')[0].trim();
  } else if (xForwardedFor) {
    clientIp = xForwardedFor.split(',')[0].trim();
  } else {
    clientIp = realIpHeader?.toString() || ip;
  }

  return clientIp;
}
