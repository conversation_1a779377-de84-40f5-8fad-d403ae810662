import { ActionMenuItem } from '../';
import { MaskTab } from './types';
import { CreateComponentPublicInstanceWithMixins, ComponentOptionsMixin, PublicProps, GlobalComponents, GlobalDirectives, ComponentInternalInstance, VNodeProps, AllowedComponentProps, ComponentCustomProps, Slot, ComponentPublicInstance, ComponentOptionsBase, ExtractPropTypes, PropType, ComputedRef, ComponentProvideOptions, DebuggerEvent, nextTick, WatchOptions, WatchStopHandle, ShallowUnwrapRef, ComponentCustomProperties, DefineComponent } from 'vue';
import { Props } from './components/Tabs';
import { MenuDataItem } from '../menu';
import { ContainerWrap, ContainerDirection, ContainerJustifyContent, ContainerAlignItems, ContainerAlignContent } from '../container';
import { OnCleanup } from '@vue/reactivity';
import { ActionBarItems } from '../action-bar';
import { IconParam } from '../icon';
import { ActionMode } from '../action';
import { ElTooltipProps, BadgeProps, TooltipTriggerType, PopperEffect, ButtonProps } from 'element-plus';
import { BaseSize, BaseType } from '../shared';
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        user?(_: {}): any;
        default?(_: {}): any;
        default?(_: {}): any;
    };
    refs: {
        tabRef: CreateComponentPublicInstanceWithMixins<Readonly< Props> & Readonly<{
            onDialog?: ((tab: MaskTab) => any) | undefined;
            onClick?: ((tab: MaskTab) => any) | undefined;
            onRemove?: ((tab: MaskTab) => any) | undefined;
            onRefresh?: ((tab: MaskTab) => any) | undefined;
            onToggleFavorite?: ((item: MenuDataItem) => any) | undefined;
        }>, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            dialog: (tab: MaskTab) => any;
            click: (tab: MaskTab) => any;
            remove: (tab: MaskTab) => any;
            refresh: (tab: MaskTab) => any;
            toggleFavorite: (item: MenuDataItem) => any;
        }, PublicProps, {}, false, {}, {}, GlobalComponents, GlobalDirectives, string, {
            tabsRef: ({
                $: ComponentInternalInstance;
                $data: {};
                $props: Partial<{
                    padding: boolean;
                    tag: string;
                    wrap: ContainerWrap;
                    fit: boolean;
                    flex: boolean;
                    inline: boolean;
                    direction: ContainerDirection;
                    justify: ContainerJustifyContent;
                    align: ContainerAlignItems;
                    alignContent: ContainerAlignContent;
                    grow: boolean;
                    shrink: boolean;
                    alignSelf: "auto" | ContainerAlignItems;
                    gap: boolean;
                    autoPointer: boolean;
                }> & Omit<{
                    readonly padding: boolean;
                    readonly tag: string;
                    readonly wrap: ContainerWrap;
                    readonly fit: boolean;
                    readonly flex: boolean;
                    readonly inline: boolean;
                    readonly direction: ContainerDirection;
                    readonly justify: ContainerJustifyContent;
                    readonly align: ContainerAlignItems;
                    readonly alignContent: ContainerAlignContent;
                    readonly grow: boolean;
                    readonly shrink: boolean;
                    readonly alignSelf: "auto" | ContainerAlignItems;
                    readonly gap: boolean;
                    readonly autoPointer: boolean;
                    readonly width?: string | number | undefined;
                    readonly height?: string | number | undefined;
                    readonly overflow?: "hidden" | "auto" | "visible" | undefined;
                } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer">;
                $attrs: {
                    [x: string]: unknown;
                };
                $refs: {
                    [x: string]: unknown;
                } & {
                    elRef: unknown;
                };
                $slots: Readonly<{
                    [name: string]: Slot<any> | undefined;
                }>;
                $root: ComponentPublicInstance | null;
                $parent: ComponentPublicInstance | null;
                $host: Element | null;
                $emit: (event: string, ...args: any[]) => void;
                $el: any;
                $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                    tag: {
                        type: StringConstructor;
                        default: string;
                    };
                    fit: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    width: {
                        type: (StringConstructor | NumberConstructor)[];
                    };
                    height: {
                        type: (StringConstructor | NumberConstructor)[];
                    };
                    flex: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    inline: {
                        type: BooleanConstructor;
                    };
                    direction: {
                        type: PropType<ContainerDirection>;
                        default: string;
                    };
                    wrap: {
                        type: PropType<ContainerWrap>;
                        default: string;
                    };
                    justify: {
                        type: PropType<ContainerJustifyContent>;
                        default: string;
                    };
                    align: {
                        type: PropType<ContainerAlignItems>;
                        default: string;
                    };
                    alignContent: {
                        type: PropType<ContainerAlignContent>;
                        default: string;
                    };
                    grow: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    shrink: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    alignSelf: {
                        type: PropType<"auto" | ContainerAlignItems>;
                        default: string;
                    };
                    overflow: {
                        type: PropType<"auto" | "hidden" | "visible">;
                    };
                    padding: {
                        type: BooleanConstructor;
                        default: boolean;
                    };
                    gap: {
                        type: BooleanConstructor;
                    };
                    autoPointer: {
                        type: BooleanConstructor;
                    };
                }>> & Readonly<{}>, {
                    $vtjEl: ComputedRef<any>;
                }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, {
                    padding: boolean;
                    tag: string;
                    wrap: ContainerWrap;
                    fit: boolean;
                    flex: boolean;
                    inline: boolean;
                    direction: ContainerDirection;
                    justify: ContainerJustifyContent;
                    align: ContainerAlignItems;
                    alignContent: ContainerAlignContent;
                    grow: boolean;
                    shrink: boolean;
                    alignSelf: "auto" | ContainerAlignItems;
                    gap: boolean;
                    autoPointer: boolean;
                }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                    beforeCreate?: (() => void) | (() => void)[];
                    created?: (() => void) | (() => void)[];
                    beforeMount?: (() => void) | (() => void)[];
                    mounted?: (() => void) | (() => void)[];
                    beforeUpdate?: (() => void) | (() => void)[];
                    updated?: (() => void) | (() => void)[];
                    activated?: (() => void) | (() => void)[];
                    deactivated?: (() => void) | (() => void)[];
                    beforeDestroy?: (() => void) | (() => void)[];
                    beforeUnmount?: (() => void) | (() => void)[];
                    destroyed?: (() => void) | (() => void)[];
                    unmounted?: (() => void) | (() => void)[];
                    renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
                };
                $forceUpdate: () => void;
                $nextTick: nextTick;
                $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
            } & Readonly<{
                padding: boolean;
                tag: string;
                wrap: ContainerWrap;
                fit: boolean;
                flex: boolean;
                inline: boolean;
                direction: ContainerDirection;
                justify: ContainerJustifyContent;
                align: ContainerAlignItems;
                alignContent: ContainerAlignContent;
                grow: boolean;
                shrink: boolean;
                alignSelf: "auto" | ContainerAlignItems;
                gap: boolean;
                autoPointer: boolean;
            }> & Omit<Readonly< ExtractPropTypes<{
                tag: {
                    type: StringConstructor;
                    default: string;
                };
                fit: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                width: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                height: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                flex: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                inline: {
                    type: BooleanConstructor;
                };
                direction: {
                    type: PropType<ContainerDirection>;
                    default: string;
                };
                wrap: {
                    type: PropType<ContainerWrap>;
                    default: string;
                };
                justify: {
                    type: PropType<ContainerJustifyContent>;
                    default: string;
                };
                align: {
                    type: PropType<ContainerAlignItems>;
                    default: string;
                };
                alignContent: {
                    type: PropType<ContainerAlignContent>;
                    default: string;
                };
                grow: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                shrink: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                alignSelf: {
                    type: PropType<"auto" | ContainerAlignItems>;
                    default: string;
                };
                overflow: {
                    type: PropType<"auto" | "hidden" | "visible">;
                };
                padding: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                gap: {
                    type: BooleanConstructor;
                };
                autoPointer: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{}>, ("padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer") | "$vtjEl"> & ShallowUnwrapRef<{
                $vtjEl: ComputedRef<any>;
            }> & {} & ComponentCustomProperties & {} & {
                $slots: {
                    default?(_: {}): any;
                };
            }) | null;
        }, any, ComponentProvideOptions, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly< Props> & Readonly<{
            onDialog?: ((tab: MaskTab) => any) | undefined;
            onClick?: ((tab: MaskTab) => any) | undefined;
            onRemove?: ((tab: MaskTab) => any) | undefined;
            onRefresh?: ((tab: MaskTab) => any) | undefined;
            onToggleFavorite?: ((item: MenuDataItem) => any) | undefined;
        }>, {}, {}, {}, {}, {}> | null;
    };
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: DefineComponent<ExtractPropTypes<{
    logo: {
        type: StringConstructor;
        default: string;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    menus: {
        type: PropType< MenuDataItem[] | (() => Promise< MenuDataItem[]> | MenuDataItem[])>;
        default(): never[];
    };
    favorites: {
        type: PropType< MenuDataItem[] | (() => Promise< MenuDataItem[]> | MenuDataItem[])>;
        default(): never[];
    };
    menuAdapter: {
        type: PropType<(menu: MenuDataItem) => MenuDataItem>;
    };
    home: {
        type: PropType<string | MaskTab>;
        default: string;
    };
    tabs: {
        type: NumberConstructor;
        default: number;
    };
    actions: {
        type: PropType<ActionBarItems>;
    };
    avatar: {
        type: StringConstructor;
    };
    theme: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    addFavorite: {
        type: PropType<(menu: MenuDataItem) => void>;
    };
    removeFavorite: {
        type: PropType<(menu: MenuDataItem) => void>;
    };
    userCardWidth: {
        type: NumberConstructor;
        default: number;
    };
    pure: {
        type: BooleanConstructor;
    };
}>, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    select: (menu: MenuDataItem) => any;
    actionClick: (action: Readonly<Partial< ExtractPropTypes<{
        name: {
            type: StringConstructor;
        };
        label: {
            type: StringConstructor;
        };
        value: {
            type: PropType<unknown>;
        };
        icon: {
            type: PropType<IconParam>;
        };
        mode: {
            type: PropType<ActionMode>;
            default: string;
        };
        menus: {
            type: PropType<ActionMenuItem[]>;
        };
        tooltip: {
            type: PropType<string | Partial< ElTooltipProps>>;
        };
        badge: {
            type: PropType<string | number | Partial< BadgeProps>>;
        };
        dropdown: {
            type: PropType<Partial< ExtractPropTypes<{
                readonly trigger: {
                    readonly type: PropType< TooltipTriggerType | TooltipTriggerType[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "hover";
                };
                readonly triggerKeys: {
                    readonly type: PropType<string[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => string[];
                };
                readonly effect: {
                    readonly default: "light";
                    readonly type: PropType<PopperEffect>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    readonly __epPropKey: true;
                };
                readonly type: {
                    readonly type: PropType<"" | "primary" | "success" | "warning" | "info" | "danger" | "default" | "text">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly placement: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "bottom";
                };
                readonly popperOptions: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => {};
                };
                readonly id: StringConstructor;
                readonly size: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly splitButton: BooleanConstructor;
                readonly hideOnClick: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly loop: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly showTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly hideTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly tabindex: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 0;
                };
                readonly maxHeight: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly popperClass: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly disabled: BooleanConstructor;
                readonly role: {
                    readonly type: PropType<"dialog" | "menu" | "grid" | "listbox" | "tooltip" | "tree" | "group" | "navigation">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "menu";
                };
                readonly buttonProps: {
                    readonly type: PropType<Partial< ButtonProps>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly teleported: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly persistent: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
            }>>>;
        };
        button: {
            type: PropType<Partial< ButtonProps>>;
        };
        disabled: {
            type: PropType<boolean | (() => boolean)>;
        };
        size: {
            type: PropType<BaseSize>;
            default: string;
        };
        type: {
            type: PropType<BaseType>;
        };
        background: {
            type: PropType<"always" | "hover" | "none">;
            default: string;
        };
        circle: {
            type: BooleanConstructor;
        };
        draggable: {
            type: BooleanConstructor;
        };
    }>>>) => any;
    actionCommand: (action: Readonly<Partial< ExtractPropTypes<{
        name: {
            type: StringConstructor;
        };
        label: {
            type: StringConstructor;
        };
        value: {
            type: PropType<unknown>;
        };
        icon: {
            type: PropType<IconParam>;
        };
        mode: {
            type: PropType<ActionMode>;
            default: string;
        };
        menus: {
            type: PropType<ActionMenuItem[]>;
        };
        tooltip: {
            type: PropType<string | Partial< ElTooltipProps>>;
        };
        badge: {
            type: PropType<string | number | Partial< BadgeProps>>;
        };
        dropdown: {
            type: PropType<Partial< ExtractPropTypes<{
                readonly trigger: {
                    readonly type: PropType< TooltipTriggerType | TooltipTriggerType[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "hover";
                };
                readonly triggerKeys: {
                    readonly type: PropType<string[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => string[];
                };
                readonly effect: {
                    readonly default: "light";
                    readonly type: PropType<PopperEffect>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    readonly __epPropKey: true;
                };
                readonly type: {
                    readonly type: PropType<"" | "primary" | "success" | "warning" | "info" | "danger" | "default" | "text">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly placement: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "bottom";
                };
                readonly popperOptions: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => {};
                };
                readonly id: StringConstructor;
                readonly size: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly splitButton: BooleanConstructor;
                readonly hideOnClick: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly loop: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly showTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly hideTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly tabindex: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 0;
                };
                readonly maxHeight: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly popperClass: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly disabled: BooleanConstructor;
                readonly role: {
                    readonly type: PropType<"dialog" | "menu" | "grid" | "listbox" | "tooltip" | "tree" | "group" | "navigation">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "menu";
                };
                readonly buttonProps: {
                    readonly type: PropType<Partial< ButtonProps>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly teleported: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly persistent: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
            }>>>;
        };
        button: {
            type: PropType<Partial< ButtonProps>>;
        };
        disabled: {
            type: PropType<boolean | (() => boolean)>;
        };
        size: {
            type: PropType<BaseSize>;
            default: string;
        };
        type: {
            type: PropType<BaseType>;
        };
        background: {
            type: PropType<"always" | "hover" | "none">;
            default: string;
        };
        circle: {
            type: BooleanConstructor;
        };
        draggable: {
            type: BooleanConstructor;
        };
    }>>>, item: ActionMenuItem) => any;
}, string, PublicProps, Readonly< ExtractPropTypes<{
    logo: {
        type: StringConstructor;
        default: string;
    };
    title: {
        type: StringConstructor;
        default: string;
    };
    menus: {
        type: PropType< MenuDataItem[] | (() => Promise< MenuDataItem[]> | MenuDataItem[])>;
        default(): never[];
    };
    favorites: {
        type: PropType< MenuDataItem[] | (() => Promise< MenuDataItem[]> | MenuDataItem[])>;
        default(): never[];
    };
    menuAdapter: {
        type: PropType<(menu: MenuDataItem) => MenuDataItem>;
    };
    home: {
        type: PropType<string | MaskTab>;
        default: string;
    };
    tabs: {
        type: NumberConstructor;
        default: number;
    };
    actions: {
        type: PropType<ActionBarItems>;
    };
    avatar: {
        type: StringConstructor;
    };
    theme: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    addFavorite: {
        type: PropType<(menu: MenuDataItem) => void>;
    };
    removeFavorite: {
        type: PropType<(menu: MenuDataItem) => void>;
    };
    userCardWidth: {
        type: NumberConstructor;
        default: number;
    };
    pure: {
        type: BooleanConstructor;
    };
}>> & Readonly<{
    onSelect?: ((menu: MenuDataItem) => any) | undefined;
    onActionClick?: ((action: Readonly<Partial< ExtractPropTypes<{
        name: {
            type: StringConstructor;
        };
        label: {
            type: StringConstructor;
        };
        value: {
            type: PropType<unknown>;
        };
        icon: {
            type: PropType<IconParam>;
        };
        mode: {
            type: PropType<ActionMode>;
            default: string;
        };
        menus: {
            type: PropType<ActionMenuItem[]>;
        };
        tooltip: {
            type: PropType<string | Partial< ElTooltipProps>>;
        };
        badge: {
            type: PropType<string | number | Partial< BadgeProps>>;
        };
        dropdown: {
            type: PropType<Partial< ExtractPropTypes<{
                readonly trigger: {
                    readonly type: PropType< TooltipTriggerType | TooltipTriggerType[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "hover";
                };
                readonly triggerKeys: {
                    readonly type: PropType<string[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => string[];
                };
                readonly effect: {
                    readonly default: "light";
                    readonly type: PropType<PopperEffect>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    readonly __epPropKey: true;
                };
                readonly type: {
                    readonly type: PropType<"" | "primary" | "success" | "warning" | "info" | "danger" | "default" | "text">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly placement: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "bottom";
                };
                readonly popperOptions: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => {};
                };
                readonly id: StringConstructor;
                readonly size: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly splitButton: BooleanConstructor;
                readonly hideOnClick: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly loop: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly showTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly hideTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly tabindex: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 0;
                };
                readonly maxHeight: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly popperClass: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly disabled: BooleanConstructor;
                readonly role: {
                    readonly type: PropType<"dialog" | "menu" | "grid" | "listbox" | "tooltip" | "tree" | "group" | "navigation">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "menu";
                };
                readonly buttonProps: {
                    readonly type: PropType<Partial< ButtonProps>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly teleported: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly persistent: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
            }>>>;
        };
        button: {
            type: PropType<Partial< ButtonProps>>;
        };
        disabled: {
            type: PropType<boolean | (() => boolean)>;
        };
        size: {
            type: PropType<BaseSize>;
            default: string;
        };
        type: {
            type: PropType<BaseType>;
        };
        background: {
            type: PropType<"always" | "hover" | "none">;
            default: string;
        };
        circle: {
            type: BooleanConstructor;
        };
        draggable: {
            type: BooleanConstructor;
        };
    }>>>) => any) | undefined;
    onActionCommand?: ((action: Readonly<Partial< ExtractPropTypes<{
        name: {
            type: StringConstructor;
        };
        label: {
            type: StringConstructor;
        };
        value: {
            type: PropType<unknown>;
        };
        icon: {
            type: PropType<IconParam>;
        };
        mode: {
            type: PropType<ActionMode>;
            default: string;
        };
        menus: {
            type: PropType<ActionMenuItem[]>;
        };
        tooltip: {
            type: PropType<string | Partial< ElTooltipProps>>;
        };
        badge: {
            type: PropType<string | number | Partial< BadgeProps>>;
        };
        dropdown: {
            type: PropType<Partial< ExtractPropTypes<{
                readonly trigger: {
                    readonly type: PropType< TooltipTriggerType | TooltipTriggerType[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "hover";
                };
                readonly triggerKeys: {
                    readonly type: PropType<string[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => string[];
                };
                readonly effect: {
                    readonly default: "light";
                    readonly type: PropType<PopperEffect>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    readonly __epPropKey: true;
                };
                readonly type: {
                    readonly type: PropType<"" | "primary" | "success" | "warning" | "info" | "danger" | "default" | "text">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly placement: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "bottom";
                };
                readonly popperOptions: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => {};
                };
                readonly id: StringConstructor;
                readonly size: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly splitButton: BooleanConstructor;
                readonly hideOnClick: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly loop: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly showTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly hideTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly tabindex: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 0;
                };
                readonly maxHeight: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly popperClass: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly disabled: BooleanConstructor;
                readonly role: {
                    readonly type: PropType<"dialog" | "menu" | "grid" | "listbox" | "tooltip" | "tree" | "group" | "navigation">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "menu";
                };
                readonly buttonProps: {
                    readonly type: PropType<Partial< ButtonProps>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly teleported: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly persistent: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
            }>>>;
        };
        button: {
            type: PropType<Partial< ButtonProps>>;
        };
        disabled: {
            type: PropType<boolean | (() => boolean)>;
        };
        size: {
            type: PropType<BaseSize>;
            default: string;
        };
        type: {
            type: PropType<BaseType>;
        };
        background: {
            type: PropType<"always" | "hover" | "none">;
            default: string;
        };
        circle: {
            type: BooleanConstructor;
        };
        draggable: {
            type: BooleanConstructor;
        };
    }>>>, item: ActionMenuItem) => any) | undefined;
}>, {
    disabled: boolean;
    title: string;
    pure: boolean;
    menus: MenuDataItem[] | (() => Promise< MenuDataItem[]> | MenuDataItem[]);
    logo: string;
    favorites: MenuDataItem[] | (() => Promise< MenuDataItem[]> | MenuDataItem[]);
    home: string | MaskTab;
    tabs: number;
    theme: boolean;
    userCardWidth: number;
}, {}, {}, {}, string, ComponentProvideOptions, true, {
    tabRef: CreateComponentPublicInstanceWithMixins<Readonly< Props> & Readonly<{
        onDialog?: ((tab: MaskTab) => any) | undefined;
        onClick?: ((tab: MaskTab) => any) | undefined;
        onRemove?: ((tab: MaskTab) => any) | undefined;
        onRefresh?: ((tab: MaskTab) => any) | undefined;
        onToggleFavorite?: ((item: MenuDataItem) => any) | undefined;
    }>, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
        dialog: (tab: MaskTab) => any;
        click: (tab: MaskTab) => any;
        remove: (tab: MaskTab) => any;
        refresh: (tab: MaskTab) => any;
        toggleFavorite: (item: MenuDataItem) => any;
    }, PublicProps, {}, false, {}, {}, GlobalComponents, GlobalDirectives, string, {
        tabsRef: ({
            $: ComponentInternalInstance;
            $data: {};
            $props: Partial<{
                padding: boolean;
                tag: string;
                wrap: ContainerWrap;
                fit: boolean;
                flex: boolean;
                inline: boolean;
                direction: ContainerDirection;
                justify: ContainerJustifyContent;
                align: ContainerAlignItems;
                alignContent: ContainerAlignContent;
                grow: boolean;
                shrink: boolean;
                alignSelf: "auto" | ContainerAlignItems;
                gap: boolean;
                autoPointer: boolean;
            }> & Omit<{
                readonly padding: boolean;
                readonly tag: string;
                readonly wrap: ContainerWrap;
                readonly fit: boolean;
                readonly flex: boolean;
                readonly inline: boolean;
                readonly direction: ContainerDirection;
                readonly justify: ContainerJustifyContent;
                readonly align: ContainerAlignItems;
                readonly alignContent: ContainerAlignContent;
                readonly grow: boolean;
                readonly shrink: boolean;
                readonly alignSelf: "auto" | ContainerAlignItems;
                readonly gap: boolean;
                readonly autoPointer: boolean;
                readonly width?: string | number | undefined;
                readonly height?: string | number | undefined;
                readonly overflow?: "hidden" | "auto" | "visible" | undefined;
            } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer">;
            $attrs: {
                [x: string]: unknown;
            };
            $refs: {
                [x: string]: unknown;
            } & {
                elRef: unknown;
            };
            $slots: Readonly<{
                [name: string]: Slot<any> | undefined;
            }>;
            $root: ComponentPublicInstance | null;
            $parent: ComponentPublicInstance | null;
            $host: Element | null;
            $emit: (event: string, ...args: any[]) => void;
            $el: any;
            $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                tag: {
                    type: StringConstructor;
                    default: string;
                };
                fit: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                width: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                height: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                flex: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                inline: {
                    type: BooleanConstructor;
                };
                direction: {
                    type: PropType<ContainerDirection>;
                    default: string;
                };
                wrap: {
                    type: PropType<ContainerWrap>;
                    default: string;
                };
                justify: {
                    type: PropType<ContainerJustifyContent>;
                    default: string;
                };
                align: {
                    type: PropType<ContainerAlignItems>;
                    default: string;
                };
                alignContent: {
                    type: PropType<ContainerAlignContent>;
                    default: string;
                };
                grow: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                shrink: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                alignSelf: {
                    type: PropType<"auto" | ContainerAlignItems>;
                    default: string;
                };
                overflow: {
                    type: PropType<"auto" | "hidden" | "visible">;
                };
                padding: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                gap: {
                    type: BooleanConstructor;
                };
                autoPointer: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{}>, {
                $vtjEl: ComputedRef<any>;
            }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, {
                padding: boolean;
                tag: string;
                wrap: ContainerWrap;
                fit: boolean;
                flex: boolean;
                inline: boolean;
                direction: ContainerDirection;
                justify: ContainerJustifyContent;
                align: ContainerAlignItems;
                alignContent: ContainerAlignContent;
                grow: boolean;
                shrink: boolean;
                alignSelf: "auto" | ContainerAlignItems;
                gap: boolean;
                autoPointer: boolean;
            }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                beforeCreate?: (() => void) | (() => void)[];
                created?: (() => void) | (() => void)[];
                beforeMount?: (() => void) | (() => void)[];
                mounted?: (() => void) | (() => void)[];
                beforeUpdate?: (() => void) | (() => void)[];
                updated?: (() => void) | (() => void)[];
                activated?: (() => void) | (() => void)[];
                deactivated?: (() => void) | (() => void)[];
                beforeDestroy?: (() => void) | (() => void)[];
                beforeUnmount?: (() => void) | (() => void)[];
                destroyed?: (() => void) | (() => void)[];
                unmounted?: (() => void) | (() => void)[];
                renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
            };
            $forceUpdate: () => void;
            $nextTick: nextTick;
            $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
        } & Readonly<{
            padding: boolean;
            tag: string;
            wrap: ContainerWrap;
            fit: boolean;
            flex: boolean;
            inline: boolean;
            direction: ContainerDirection;
            justify: ContainerJustifyContent;
            align: ContainerAlignItems;
            alignContent: ContainerAlignContent;
            grow: boolean;
            shrink: boolean;
            alignSelf: "auto" | ContainerAlignItems;
            gap: boolean;
            autoPointer: boolean;
        }> & Omit<Readonly< ExtractPropTypes<{
            tag: {
                type: StringConstructor;
                default: string;
            };
            fit: {
                type: BooleanConstructor;
                default: boolean;
            };
            width: {
                type: (StringConstructor | NumberConstructor)[];
            };
            height: {
                type: (StringConstructor | NumberConstructor)[];
            };
            flex: {
                type: BooleanConstructor;
                default: boolean;
            };
            inline: {
                type: BooleanConstructor;
            };
            direction: {
                type: PropType<ContainerDirection>;
                default: string;
            };
            wrap: {
                type: PropType<ContainerWrap>;
                default: string;
            };
            justify: {
                type: PropType<ContainerJustifyContent>;
                default: string;
            };
            align: {
                type: PropType<ContainerAlignItems>;
                default: string;
            };
            alignContent: {
                type: PropType<ContainerAlignContent>;
                default: string;
            };
            grow: {
                type: BooleanConstructor;
                default: boolean;
            };
            shrink: {
                type: BooleanConstructor;
                default: boolean;
            };
            alignSelf: {
                type: PropType<"auto" | ContainerAlignItems>;
                default: string;
            };
            overflow: {
                type: PropType<"auto" | "hidden" | "visible">;
            };
            padding: {
                type: BooleanConstructor;
                default: boolean;
            };
            gap: {
                type: BooleanConstructor;
            };
            autoPointer: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, ("padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer") | "$vtjEl"> & ShallowUnwrapRef<{
            $vtjEl: ComputedRef<any>;
        }> & {} & ComponentCustomProperties & {} & {
            $slots: {
                default?(_: {}): any;
            };
        }) | null;
    }, any, ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly< Props> & Readonly<{
        onDialog?: ((tab: MaskTab) => any) | undefined;
        onClick?: ((tab: MaskTab) => any) | undefined;
        onRemove?: ((tab: MaskTab) => any) | undefined;
        onRefresh?: ((tab: MaskTab) => any) | undefined;
        onToggleFavorite?: ((item: MenuDataItem) => any) | undefined;
    }>, {}, {}, {}, {}, {}> | null;
}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
