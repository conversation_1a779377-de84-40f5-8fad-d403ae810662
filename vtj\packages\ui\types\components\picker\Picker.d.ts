import { nextTick, DefineComponent, ExtractPropTypes, PropType, ComputedRef, Ref, ComponentOptionsMixin, PublicProps, ComponentProvideOptions, CreateComponentPublicInstanceWithMixins, Component, ShallowRef, WritableComputedRef, VNodeProps, AllowedComponentProps, ComponentCustomProps, GlobalComponents, GlobalDirectives, ComponentInternalInstance, Slot, ComponentPublicInstance, ComponentOptionsBase, VNode, DebuggerEvent, WatchOptions, WatchStopHandle, ShallowUnwrapRef, ComponentCustomProperties } from 'vue';
import { PickerLoader, PickerColumns, PickerFields, PickerDialogProps, PickerGridProps, PickerOption } from './types';
import { PopperEffect, SelectOptionProxy, OptionBasic, TooltipInstance, ScrollbarInstance, MessageBoxData } from 'element-plus';
import { DebouncedFunc } from 'lodash';
import { Props } from './Dialog';
import { GridColumns, GridCustomInfo, GridLoader, GridCellRenders, GridEditRenders, GridFilterRenders, GridSortableEvent, GridSortableOptions } from '..';
import { Options } from 'sortablejs';
import { VxeTableProps, VxeGridEventProps, VxeGridSlots, VxeTableDataRow, SlotVNodeType, VxeGridInstance, VxeTableConstructor, VxeGridConstructor } from 'vxe-table';
import { OnCleanup } from '@vue/reactivity';
declare const _default: DefineComponent<ExtractPropTypes<{
    columns: {
        type: PropType<PickerColumns>;
    };
    fields: {
        type: PropType<PickerFields>;
    };
    model: {
        type: PropType<Record<string, any>>;
    };
    loader: {
        type: PropType<PickerLoader>;
    };
    modelValue: {
        type: (ObjectConstructor | StringConstructor | NumberConstructor | ArrayConstructor)[];
    };
    multiple: {
        type: BooleanConstructor;
    };
    raw: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    append: {
        type: BooleanConstructor;
    };
    valueKey: {
        type: StringConstructor;
        default: string;
    };
    labelKey: {
        type: StringConstructor;
        default: string;
    };
    queryKey: {
        type: StringConstructor;
    };
    preload: {
        type: BooleanConstructor;
    };
    dialogProps: {
        type: PropType<PickerDialogProps>;
    };
    gridProps: {
        type: PropType<PickerGridProps>;
    };
    formProps: {
        type: PropType<Record<string, any>>;
    };
    data: {
        type: PropType<any>;
    };
    formatter: {
        type: FunctionConstructor;
    };
    valueFormatter: {
        type: FunctionConstructor;
    };
    beforeInit: {
        type: FunctionConstructor;
    };
}>, {
    focus: () => void;
    blur: () => void;
    disabled: ComputedRef<boolean>;
    options: Ref<{
        label: string;
        value: any;
    }[], PickerOption[] | {
        label: string;
        value: any;
    }[]>;
    setOptions: (rows: any, append?: boolean) => void;
    current: Ref<any, any>;
    visible: Ref<boolean, boolean>;
    dialogRef: Ref<any, any>;
    formModel: Ref<Record<string, any>, Record<string, any>>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    change: (value: any, data: any) => any;
    "update:modelValue": (value: any) => any;
    picked: (value: any, data: any) => any;
}, string, PublicProps, Readonly< ExtractPropTypes<{
    columns: {
        type: PropType<PickerColumns>;
    };
    fields: {
        type: PropType<PickerFields>;
    };
    model: {
        type: PropType<Record<string, any>>;
    };
    loader: {
        type: PropType<PickerLoader>;
    };
    modelValue: {
        type: (ObjectConstructor | StringConstructor | NumberConstructor | ArrayConstructor)[];
    };
    multiple: {
        type: BooleanConstructor;
    };
    raw: {
        type: BooleanConstructor;
    };
    disabled: {
        type: BooleanConstructor;
    };
    append: {
        type: BooleanConstructor;
    };
    valueKey: {
        type: StringConstructor;
        default: string;
    };
    labelKey: {
        type: StringConstructor;
        default: string;
    };
    queryKey: {
        type: StringConstructor;
    };
    preload: {
        type: BooleanConstructor;
    };
    dialogProps: {
        type: PropType<PickerDialogProps>;
    };
    gridProps: {
        type: PropType<PickerGridProps>;
    };
    formProps: {
        type: PropType<Record<string, any>>;
    };
    data: {
        type: PropType<any>;
    };
    formatter: {
        type: FunctionConstructor;
    };
    valueFormatter: {
        type: FunctionConstructor;
    };
    beforeInit: {
        type: FunctionConstructor;
    };
}>> & Readonly<{
    onChange?: ((value: any, data: any) => any) | undefined;
    "onUpdate:modelValue"?: ((value: any) => any) | undefined;
    onPicked?: ((value: any, data: any) => any) | undefined;
}>, {
    raw: boolean;
    disabled: boolean;
    append: boolean;
    multiple: boolean;
    valueKey: string;
    labelKey: string;
    preload: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, true, {
    selectRef: CreateComponentPublicInstanceWithMixins<Readonly< ExtractPropTypes<{
        ariaLabel: StringConstructor;
        emptyValues: ArrayConstructor;
        valueOnClear: {
            readonly type: PropType<string | number | boolean | Function>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: undefined;
        };
        name: StringConstructor;
        id: StringConstructor;
        modelValue: {
            readonly type: PropType<(string | number | boolean | Record<string, any>) | (string | number | boolean | Record<string, any>)[]>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: undefined;
        };
        autocomplete: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        automaticDropdown: BooleanConstructor;
        size: {
            readonly type: PropType<"" | "large" | "default" | "small">;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        effect: {
            readonly type: PropType<PopperEffect>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        disabled: BooleanConstructor;
        clearable: BooleanConstructor;
        filterable: BooleanConstructor;
        allowCreate: BooleanConstructor;
        loading: BooleanConstructor;
        popperClass: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        popperOptions: {
            readonly type: PropType<any>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: () => Partial<Options>;
        };
        remote: BooleanConstructor;
        loadingText: StringConstructor;
        noMatchText: StringConstructor;
        noDataText: StringConstructor;
        remoteMethod: FunctionConstructor;
        filterMethod: FunctionConstructor;
        multiple: BooleanConstructor;
        multipleLimit: {
            readonly type: PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        placeholder: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        defaultFirstOption: BooleanConstructor;
        reserveKeyword: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        valueKey: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        collapseTags: BooleanConstructor;
        collapseTagsTooltip: BooleanConstructor;
        maxCollapseTags: {
            readonly type: PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        teleported: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: true;
        };
        persistent: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        clearIcon: {
            readonly type: PropType<string | Component>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        fitInputWidth: BooleanConstructor;
        suffixIcon: {
            readonly type: PropType<string | Component>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        tagType: {
            default: string;
            type: PropType<"primary" | "success" | "warning" | "info" | "danger">;
            required: false;
            validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        tagEffect: {
            default: string;
            type: PropType<"dark" | "light" | "plain">;
            required: false;
            validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        validateEvent: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        remoteShowSuffix: BooleanConstructor;
        showArrow: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        offset: {
            readonly type: PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        placement: {
            readonly type: PropType<any>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        fallbackPlacements: {
            readonly type: PropType<Placement[]>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string[];
        };
        tabindex: {
            readonly type: PropType<string | number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        appendTo: {
            readonly type: PropType<string | HTMLElement>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>> & {
        "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
        onChange?: ((...args: any[]) => any) | undefined;
        onFocus?: ((...args: any[]) => any) | undefined;
        onBlur?: ((...args: any[]) => any) | undefined;
        onClear?: ((...args: any[]) => any) | undefined;
        "onVisible-change"?: ((...args: any[]) => any) | undefined;
        "onRemove-tag"?: ((...args: any[]) => any) | undefined;
        "onPopup-scroll"?: ((...args: any[]) => any) | undefined;
    }, {
        modelValue: ComputedRef<string | number | boolean | any[] | Record<string, any> | undefined>;
        selectedLabel: ComputedRef<string | string[]>;
        calculatorRef: ShallowRef<HTMLElement | undefined>;
        inputStyle: ComputedRef<{
            minWidth: string;
        }>;
        inputId: Ref<string | undefined>;
        contentId: Ref<string>;
        nsSelect: {
            namespace: ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string) => string;
            m: (modifier?: string) => string;
            be: (blockSuffix?: string, element?: string) => string;
            em: (element?: string, modifier?: string) => string;
            bm: (blockSuffix?: string, modifier?: string) => string;
            bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        nsInput: {
            namespace: ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string) => string;
            m: (modifier?: string) => string;
            be: (blockSuffix?: string, element?: string) => string;
            em: (element?: string, modifier?: string) => string;
            bm: (blockSuffix?: string, modifier?: string) => string;
            bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        states: {
            inputValue: string;
            options: Map<string | number | boolean | Record<string, any>, SelectOptionProxy> & Omit<Map<string | number | boolean | Record<string, any>, SelectOptionProxy>, keyof Map<any, any>>;
            cachedOptions: Map<string | number | boolean | Record<string, any>, SelectOptionProxy> & Omit<Map<string | number | boolean | Record<string, any>, SelectOptionProxy>, keyof Map<any, any>>;
            optionValues: (string | number | boolean | Record<string, any>)[];
            selected: {
                value: string | number | boolean | Record<string, any>;
                currentLabel: SelectOptionProxy["currentLabel"];
                isDisabled?: SelectOptionProxy["isDisabled"] | undefined;
            }[];
            hoveringIndex: number;
            inputHovering: boolean;
            selectionWidth: number;
            collapseItemWidth: number;
            previousQuery: string | null;
            selectedLabel: string;
            menuVisibleOnFocus: boolean;
            isBeforeHide: boolean;
        };
        isFocused: Ref<boolean>;
        expanded: Ref<boolean>;
        optionsArray: ComputedRef< SelectOptionProxy[]>;
        hoverOption: Ref<any>;
        selectSize: ComputedRef<"" | "small" | "default" | "large">;
        filteredOptionsCount: ComputedRef<number>;
        updateTooltip: () => void;
        updateTagTooltip: () => void;
        debouncedOnInputChange: DebouncedFunc<() => void>;
        onInput: (event: Event) => void;
        deletePrevTag: (e: KeyboardEvent) => void;
        deleteTag: (event: MouseEvent, tag: SelectOptionProxy | OptionBasic) => void;
        deleteSelected: (event: Event) => void;
        handleOptionSelect: (option: SelectOptionProxy) => void;
        scrollToOption: (option: SelectOptionProxy | SelectOptionProxy[] | OptionBasic[]) => void;
        hasModelValue: ComputedRef<boolean>;
        shouldShowPlaceholder: ComputedRef<boolean>;
        currentPlaceholder: ComputedRef<string>;
        mouseEnterEventName: ComputedRef<"mouseenter" | null>;
        needStatusIcon: ComputedRef<boolean>;
        showClose: ComputedRef<boolean>;
        iconComponent: ComputedRef<(string | Component) | undefined>;
        iconReverse: ComputedRef<string>;
        validateState: ComputedRef<"" | "error" | "success" | "validating">;
        validateIcon: ComputedRef<"" | Component>;
        showNewOption: ComputedRef<boolean>;
        updateOptions: () => void;
        collapseTagSize: ComputedRef<"default" | "small">;
        setSelected: () => void;
        selectDisabled: ComputedRef<boolean>;
        emptyText: ComputedRef<string | null>;
        handleCompositionStart: (event: CompositionEvent) => void;
        handleCompositionUpdate: (event: CompositionEvent) => void;
        handleCompositionEnd: (event: CompositionEvent) => void;
        onOptionCreate: (vm: SelectOptionProxy) => void;
        onOptionDestroy: (key: string | number | boolean | Record<string, any>, vm: SelectOptionProxy) => void;
        handleMenuEnter: () => void;
        focus: () => void;
        blur: () => void;
        handleClearClick: (event: Event) => void;
        handleClickOutside: (event: Event) => void;
        handleEsc: () => void;
        toggleMenu: () => void;
        selectOption: () => void;
        getValueKey: (item: SelectOptionProxy | OptionBasic) => any;
        navigateOptions: (direction: "prev" | "next") => void;
        dropdownMenuVisible: WritableComputedRef<boolean>;
        showTagList: ComputedRef<{
            value: string | number | boolean | Record<string, any>;
            currentLabel: SelectOptionProxy["currentLabel"];
            isDisabled?: SelectOptionProxy["isDisabled"] | undefined;
        }[]>;
        collapseTagList: ComputedRef<{
            value: string | number | boolean | Record<string, any>;
            currentLabel: SelectOptionProxy["currentLabel"];
            isDisabled?: SelectOptionProxy["isDisabled"] | undefined;
        }[]>;
        popupScroll: (data: {
            scrollTop: number;
            scrollLeft: number;
        }) => void;
        tagStyle: ComputedRef<{
            maxWidth: string;
        }>;
        collapseTagStyle: ComputedRef<{
            maxWidth: string;
        }>;
        popperRef: ComputedRef<HTMLElement | undefined>;
        inputRef: Ref<HTMLInputElement | undefined>;
        tooltipRef: Ref< TooltipInstance | undefined>;
        tagTooltipRef: Ref< TooltipInstance | undefined>;
        prefixRef: Ref<HTMLElement | undefined>;
        suffixRef: Ref<HTMLElement | undefined>;
        selectRef: Ref<HTMLElement | undefined>;
        wrapperRef: ShallowRef<HTMLElement | undefined>;
        selectionRef: Ref<HTMLElement | undefined>;
        scrollbarRef: Ref< ScrollbarInstance | undefined>;
        menuRef: Ref<HTMLElement | undefined>;
        tagMenuRef: Ref<HTMLElement | undefined>;
        collapseItemRef: Ref<HTMLElement | undefined>;
    }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, ("clear" | "focus" | "blur" | "change" | "visible-change" | "update:modelValue" | "remove-tag" | "popup-scroll")[], VNodeProps & AllowedComponentProps & ComponentCustomProps, {
        disabled: boolean;
        offset: number;
        multiple: boolean;
        loading: boolean;
        modelValue: (string | number | boolean | Record<string, any>) | (string | number | boolean | Record<string, any>)[];
        placement: any;
        effect: PopperEffect;
        tabindex: string | number;
        valueOnClear: string | number | boolean | Function;
        autocomplete: string;
        validateEvent: boolean;
        clearable: boolean;
        fallbackPlacements: Placement[];
        popperOptions: Partial<Options>;
        popperClass: string;
        teleported: boolean;
        persistent: boolean;
        showArrow: boolean;
        valueKey: string;
        fitInputWidth: boolean;
        filterable: boolean;
        collapseTags: boolean;
        maxCollapseTags: number;
        collapseTagsTooltip: boolean;
        tagType: "primary" | "success" | "warning" | "info" | "danger";
        tagEffect: "dark" | "light" | "plain";
        automaticDropdown: boolean;
        allowCreate: boolean;
        remote: boolean;
        multipleLimit: number;
        defaultFirstOption: boolean;
        reserveKeyword: boolean;
        remoteShowSuffix: boolean;
    }, true, {}, {}, GlobalComponents, GlobalDirectives, string, {}, any, ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly< ExtractPropTypes<{
        ariaLabel: StringConstructor;
        emptyValues: ArrayConstructor;
        valueOnClear: {
            readonly type: PropType<string | number | boolean | Function>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: undefined;
        };
        name: StringConstructor;
        id: StringConstructor;
        modelValue: {
            readonly type: PropType<(string | number | boolean | Record<string, any>) | (string | number | boolean | Record<string, any>)[]>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: undefined;
        };
        autocomplete: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        automaticDropdown: BooleanConstructor;
        size: {
            readonly type: PropType<"" | "large" | "default" | "small">;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        effect: {
            readonly type: PropType<PopperEffect>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        disabled: BooleanConstructor;
        clearable: BooleanConstructor;
        filterable: BooleanConstructor;
        allowCreate: BooleanConstructor;
        loading: BooleanConstructor;
        popperClass: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        popperOptions: {
            readonly type: PropType<any>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: () => Partial<Options>;
        };
        remote: BooleanConstructor;
        loadingText: StringConstructor;
        noMatchText: StringConstructor;
        noDataText: StringConstructor;
        remoteMethod: FunctionConstructor;
        filterMethod: FunctionConstructor;
        multiple: BooleanConstructor;
        multipleLimit: {
            readonly type: PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        placeholder: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        defaultFirstOption: BooleanConstructor;
        reserveKeyword: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        valueKey: {
            readonly type: PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        collapseTags: BooleanConstructor;
        collapseTagsTooltip: BooleanConstructor;
        maxCollapseTags: {
            readonly type: PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        teleported: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: true;
        };
        persistent: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        clearIcon: {
            readonly type: PropType<string | Component>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        fitInputWidth: BooleanConstructor;
        suffixIcon: {
            readonly type: PropType<string | Component>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        tagType: {
            default: string;
            type: PropType<"primary" | "success" | "warning" | "info" | "danger">;
            required: false;
            validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        tagEffect: {
            default: string;
            type: PropType<"dark" | "light" | "plain">;
            required: false;
            validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        validateEvent: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        remoteShowSuffix: BooleanConstructor;
        showArrow: {
            readonly type: PropType<boolean>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: boolean;
        };
        offset: {
            readonly type: PropType<number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        placement: {
            readonly type: PropType<any>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string;
        };
        fallbackPlacements: {
            readonly type: PropType<Placement[]>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: string[];
        };
        tabindex: {
            readonly type: PropType<string | number>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        } & {
            readonly default: number;
        };
        appendTo: {
            readonly type: PropType<string | HTMLElement>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>> & {
        "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
        onChange?: ((...args: any[]) => any) | undefined;
        onFocus?: ((...args: any[]) => any) | undefined;
        onBlur?: ((...args: any[]) => any) | undefined;
        onClear?: ((...args: any[]) => any) | undefined;
        "onVisible-change"?: ((...args: any[]) => any) | undefined;
        "onRemove-tag"?: ((...args: any[]) => any) | undefined;
        "onPopup-scroll"?: ((...args: any[]) => any) | undefined;
    }, {
        modelValue: ComputedRef<string | number | boolean | any[] | Record<string, any> | undefined>;
        selectedLabel: ComputedRef<string | string[]>;
        calculatorRef: ShallowRef<HTMLElement | undefined>;
        inputStyle: ComputedRef<{
            minWidth: string;
        }>;
        inputId: Ref<string | undefined>;
        contentId: Ref<string>;
        nsSelect: {
            namespace: ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string) => string;
            m: (modifier?: string) => string;
            be: (blockSuffix?: string, element?: string) => string;
            em: (element?: string, modifier?: string) => string;
            bm: (blockSuffix?: string, modifier?: string) => string;
            bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        nsInput: {
            namespace: ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string) => string;
            m: (modifier?: string) => string;
            be: (blockSuffix?: string, element?: string) => string;
            em: (element?: string, modifier?: string) => string;
            bm: (blockSuffix?: string, modifier?: string) => string;
            bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        states: {
            inputValue: string;
            options: Map<string | number | boolean | Record<string, any>, SelectOptionProxy> & Omit<Map<string | number | boolean | Record<string, any>, SelectOptionProxy>, keyof Map<any, any>>;
            cachedOptions: Map<string | number | boolean | Record<string, any>, SelectOptionProxy> & Omit<Map<string | number | boolean | Record<string, any>, SelectOptionProxy>, keyof Map<any, any>>;
            optionValues: (string | number | boolean | Record<string, any>)[];
            selected: {
                value: string | number | boolean | Record<string, any>;
                currentLabel: SelectOptionProxy["currentLabel"];
                isDisabled?: SelectOptionProxy["isDisabled"] | undefined;
            }[];
            hoveringIndex: number;
            inputHovering: boolean;
            selectionWidth: number;
            collapseItemWidth: number;
            previousQuery: string | null;
            selectedLabel: string;
            menuVisibleOnFocus: boolean;
            isBeforeHide: boolean;
        };
        isFocused: Ref<boolean>;
        expanded: Ref<boolean>;
        optionsArray: ComputedRef< SelectOptionProxy[]>;
        hoverOption: Ref<any>;
        selectSize: ComputedRef<"" | "small" | "default" | "large">;
        filteredOptionsCount: ComputedRef<number>;
        updateTooltip: () => void;
        updateTagTooltip: () => void;
        debouncedOnInputChange: DebouncedFunc<() => void>;
        onInput: (event: Event) => void;
        deletePrevTag: (e: KeyboardEvent) => void;
        deleteTag: (event: MouseEvent, tag: SelectOptionProxy | OptionBasic) => void;
        deleteSelected: (event: Event) => void;
        handleOptionSelect: (option: SelectOptionProxy) => void;
        scrollToOption: (option: SelectOptionProxy | SelectOptionProxy[] | OptionBasic[]) => void;
        hasModelValue: ComputedRef<boolean>;
        shouldShowPlaceholder: ComputedRef<boolean>;
        currentPlaceholder: ComputedRef<string>;
        mouseEnterEventName: ComputedRef<"mouseenter" | null>;
        needStatusIcon: ComputedRef<boolean>;
        showClose: ComputedRef<boolean>;
        iconComponent: ComputedRef<(string | Component) | undefined>;
        iconReverse: ComputedRef<string>;
        validateState: ComputedRef<"" | "error" | "success" | "validating">;
        validateIcon: ComputedRef<"" | Component>;
        showNewOption: ComputedRef<boolean>;
        updateOptions: () => void;
        collapseTagSize: ComputedRef<"default" | "small">;
        setSelected: () => void;
        selectDisabled: ComputedRef<boolean>;
        emptyText: ComputedRef<string | null>;
        handleCompositionStart: (event: CompositionEvent) => void;
        handleCompositionUpdate: (event: CompositionEvent) => void;
        handleCompositionEnd: (event: CompositionEvent) => void;
        onOptionCreate: (vm: SelectOptionProxy) => void;
        onOptionDestroy: (key: string | number | boolean | Record<string, any>, vm: SelectOptionProxy) => void;
        handleMenuEnter: () => void;
        focus: () => void;
        blur: () => void;
        handleClearClick: (event: Event) => void;
        handleClickOutside: (event: Event) => void;
        handleEsc: () => void;
        toggleMenu: () => void;
        selectOption: () => void;
        getValueKey: (item: SelectOptionProxy | OptionBasic) => any;
        navigateOptions: (direction: "prev" | "next") => void;
        dropdownMenuVisible: WritableComputedRef<boolean>;
        showTagList: ComputedRef<{
            value: string | number | boolean | Record<string, any>;
            currentLabel: SelectOptionProxy["currentLabel"];
            isDisabled?: SelectOptionProxy["isDisabled"] | undefined;
        }[]>;
        collapseTagList: ComputedRef<{
            value: string | number | boolean | Record<string, any>;
            currentLabel: SelectOptionProxy["currentLabel"];
            isDisabled?: SelectOptionProxy["isDisabled"] | undefined;
        }[]>;
        popupScroll: (data: {
            scrollTop: number;
            scrollLeft: number;
        }) => void;
        tagStyle: ComputedRef<{
            maxWidth: string;
        }>;
        collapseTagStyle: ComputedRef<{
            maxWidth: string;
        }>;
        popperRef: ComputedRef<HTMLElement | undefined>;
        inputRef: Ref<HTMLInputElement | undefined>;
        tooltipRef: Ref< TooltipInstance | undefined>;
        tagTooltipRef: Ref< TooltipInstance | undefined>;
        prefixRef: Ref<HTMLElement | undefined>;
        suffixRef: Ref<HTMLElement | undefined>;
        selectRef: Ref<HTMLElement | undefined>;
        wrapperRef: ShallowRef<HTMLElement | undefined>;
        selectionRef: Ref<HTMLElement | undefined>;
        scrollbarRef: Ref< ScrollbarInstance | undefined>;
        menuRef: Ref<HTMLElement | undefined>;
        tagMenuRef: Ref<HTMLElement | undefined>;
        collapseItemRef: Ref<HTMLElement | undefined>;
    }, {}, {}, {}, {
        disabled: boolean;
        offset: number;
        multiple: boolean;
        loading: boolean;
        modelValue: (string | number | boolean | Record<string, any>) | (string | number | boolean | Record<string, any>)[];
        placement: any;
        effect: PopperEffect;
        tabindex: string | number;
        valueOnClear: string | number | boolean | Function;
        autocomplete: string;
        validateEvent: boolean;
        clearable: boolean;
        fallbackPlacements: Placement[];
        popperOptions: Partial<Options>;
        popperClass: string;
        teleported: boolean;
        persistent: boolean;
        showArrow: boolean;
        valueKey: string;
        fitInputWidth: boolean;
        filterable: boolean;
        collapseTags: boolean;
        maxCollapseTags: number;
        collapseTagsTooltip: boolean;
        tagType: "primary" | "success" | "warning" | "info" | "danger";
        tagEffect: "dark" | "light" | "plain";
        automaticDropdown: boolean;
        allowCreate: boolean;
        remote: boolean;
        multipleLimit: number;
        defaultFirstOption: boolean;
        reserveKeyword: boolean;
        remoteShowSuffix: boolean;
    }> | null;
    dialogRef: CreateComponentPublicInstanceWithMixins<Readonly< Props> & Readonly<{}>, {
        pick: () => void;
        gridRef: Ref<any, any>;
    }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, PublicProps, {}, false, {}, {}, GlobalComponents, GlobalDirectives, string, {
        gridRef: ({
            $: ComponentInternalInstance;
            $data: {};
            $props: Partial<{
                auto: boolean;
                resizable: boolean;
                editable: boolean;
                page: number;
                pageSize: number;
                columns: GridColumns;
                rowSortable: boolean | Options;
                columnSortable: boolean | Options;
                customable: boolean;
                pager: boolean;
                pageSizes: number[];
                virtual: boolean;
            }> & Omit<{
                readonly auto: boolean;
                readonly resizable: boolean;
                readonly editable: boolean;
                readonly page: number;
                readonly pageSize: number;
                readonly columns: GridColumns;
                readonly rowSortable: boolean | Options;
                readonly columnSortable: boolean | Options;
                readonly customable: boolean;
                readonly pager: boolean;
                readonly pageSizes: number[];
                readonly virtual: boolean;
                readonly getCustom?: ((id: string) => Promise< GridCustomInfo>) | undefined;
                readonly saveCustom?: ((info: GridCustomInfo) => Promise<any>) | undefined;
                readonly id?: string | undefined;
                readonly loader?: GridLoader | undefined;
                readonly cellRenders?: GridCellRenders | undefined;
                readonly editRenders?: GridEditRenders | undefined;
                readonly filterRenders?: GridFilterRenders | undefined;
                readonly sumFields?: string[] | undefined;
                readonly avgFields?: string[] | undefined;
                readonly sumAllFields?: Record<string, number> | undefined;
                readonly onRowSort?: ((e: GridSortableEvent) => any) | undefined;
                readonly onColumnSort?: ((e: GridSortableEvent) => any) | undefined;
                readonly onCellSelected?: ((params: any) => any) | undefined;
                readonly onEditChange?: ((data: any[]) => any) | undefined;
                readonly onLoaded?: ((rows: any[]) => any) | undefined;
            } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "auto" | "resizable" | "editable" | "page" | "pageSize" | "columns" | "rowSortable" | "columnSortable" | "customable" | "pager" | "pageSizes" | "virtual">;
            $attrs: {
                [x: string]: unknown;
            };
            $refs: {
                [x: string]: unknown;
            } & {
                vxeRef: {
                    $props: VxeTableProps<any> & {
                        layouts?: import("vxe-table").VxeGridPropTypes.Layouts;
                        columns?: import("vxe-table").VxeGridPropTypes.Columns<any> | undefined;
                        pagerConfig?: import("vxe-table").VxeGridPropTypes.PagerConfig;
                        proxyConfig?: import("vxe-table").VxeGridPropTypes.ProxyConfig<any> | undefined;
                        toolbarConfig?: import("vxe-table").VxeGridPropTypes.ToolbarConfig;
                        formConfig?: import("vxe-table").VxeGridPropTypes.FormConfig;
                        zoomConfig?: import("vxe-table").VxeGridPropTypes.ZoomConfig;
                    } & VxeGridEventProps<any>;
                    $slots: VxeGridSlots<any>;
                } | null;
            };
            $slots: Readonly<{
                [name: string]: Slot<any> | undefined;
            }>;
            $root: ComponentPublicInstance | null;
            $parent: ComponentPublicInstance | null;
            $host: Element | null;
            $emit: ((event: "rowSort", e: GridSortableEvent) => void) & ((event: "columnSort", e: GridSortableEvent) => void) & ((event: "cellSelected", params: any) => void) & ((event: "editChange", data: any[]) => void) & ((event: "loaded", rows: any[]) => void);
            $el: any;
            $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                id: {
                    type: StringConstructor;
                };
                columns: {
                    type: PropType<GridColumns>;
                    default(): GridColumns;
                };
                loader: {
                    type: PropType<GridLoader>;
                };
                rowSortable: {
                    type: PropType<boolean | GridSortableOptions>;
                    default: boolean;
                };
                columnSortable: {
                    type: PropType<boolean | GridSortableOptions>;
                    default: boolean;
                };
                customable: {
                    type: BooleanConstructor;
                };
                getCustom: {
                    type: PropType<(id: string) => Promise< GridCustomInfo>>;
                };
                saveCustom: {
                    type: PropType<(info: GridCustomInfo) => Promise<any>>;
                };
                resizable: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                pager: {
                    type: BooleanConstructor;
                };
                page: {
                    type: NumberConstructor;
                    default: number;
                };
                pageSize: {
                    type: NumberConstructor;
                    default: number;
                };
                pageSizes: {
                    type: PropType<number[]>;
                    default: () => number[];
                };
                auto: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                virtual: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                cellRenders: {
                    type: PropType<GridCellRenders>;
                };
                editRenders: {
                    type: PropType<GridEditRenders>;
                };
                filterRenders: {
                    type: PropType<GridFilterRenders>;
                };
                editable: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                sumFields: {
                    type: PropType<string[]>;
                };
                avgFields: {
                    type: PropType<string[]>;
                };
                sumAllFields: {
                    type: PropType<Record<string, number>>;
                };
            }>> & Readonly<{
                onRowSort?: ((e: GridSortableEvent) => any) | undefined;
                onColumnSort?: ((e: GridSortableEvent) => any) | undefined;
                onCellSelected?: ((params: any) => any) | undefined;
                onEditChange?: ((data: any[]) => any) | undefined;
                onLoaded?: ((rows: any[]) => any) | undefined;
            }>, {
                state: {
                    [x: string]: any;
                    page?: number | undefined;
                    pageSize?: number | undefined;
                    total?: number | undefined;
                    filters?: {
                        column: {
                            property: import("vxe-table").VxeColumnPropTypes.Field;
                            type: import("vxe-table").VxeColumnPropTypes.Type;
                            field: import("vxe-table").VxeColumnPropTypes.Field;
                            title: import("vxe-table").VxeColumnPropTypes.Title;
                            width: import("vxe-table").VxeColumnPropTypes.Width;
                            minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                            maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                            resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                            fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                            align: import("vxe-table").VxeColumnPropTypes.Align;
                            headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                            footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                            showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                            showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                            showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                            className: import("vxe-table").VxeColumnPropTypes.ClassName;
                            headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                            footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                            formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                            sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                            sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                            sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                            filters: {
                                label?: string | number | undefined;
                                value?: any;
                                data?: any;
                                resetValue?: any;
                                checked?: boolean | undefined;
                            }[];
                            filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                            filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                            filterRender: {
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                content?: string | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                events?: {
                                    [key: string]: (...args: any[]) => any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                            visible: import("vxe-table").VxeColumnPropTypes.Visible;
                            exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                            footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                            titleHelp: {
                                useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                                content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                                enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                                theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                                icon?: string | undefined;
                                message?: string | undefined;
                            };
                            titlePrefix: {
                                useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                                content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                                enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                                theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                                icon?: string | undefined;
                                message?: string | undefined;
                            };
                            titleSuffix: {
                                useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                                content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                                enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                                theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                                icon?: string | undefined;
                            };
                            cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                            cellRender: {
                                events?: {
                                    [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                                } | undefined;
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                content?: string | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            editRender: {
                                events?: {
                                    [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                                } | undefined;
                                enabled?: boolean | undefined;
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                autofocus?: string | undefined;
                                autoselect?: boolean | undefined;
                                defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                                    column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                                }) => any) | null | undefined;
                                immediate?: boolean | undefined;
                                content?: string | undefined;
                                placeholder?: string | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            contentRender: {
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                events?: {
                                    [key: string]: (...args: any[]) => any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            params: import("vxe-table").VxeColumnPropTypes.Params;
                            slots: {
                                title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            };
                            id: string;
                            parentId: string;
                            level: number;
                            rowSpan: number;
                            colSpan: number;
                            halfVisible: boolean;
                            defaultVisible: any;
                            defaultFixed: any;
                            checked: boolean;
                            halfChecked: boolean;
                            disabled: boolean;
                            order: import("vxe-table").VxeTablePropTypes.SortOrder;
                            sortTime: number;
                            sortNumber: number;
                            renderSortNumber: number;
                            renderWidth: number;
                            renderHeight: number;
                            resizeWidth: number;
                            model: {
                                update: boolean;
                                value: any;
                            };
                            children: /*elided*/ any[];
                            renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                            renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                            renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                            renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                            getTitle: () => string;
                            getKey: () => string;
                        };
                        field: import("vxe-table").VxeColumnPropTypes.Field;
                        property: import("vxe-table").VxeColumnPropTypes.Field;
                        values: any[];
                        datas: any[];
                    }[] | undefined;
                    sorts?: {
                        column: {
                            property: import("vxe-table").VxeColumnPropTypes.Field;
                            type: import("vxe-table").VxeColumnPropTypes.Type;
                            field: import("vxe-table").VxeColumnPropTypes.Field;
                            title: import("vxe-table").VxeColumnPropTypes.Title;
                            width: import("vxe-table").VxeColumnPropTypes.Width;
                            minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                            maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                            resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                            fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                            align: import("vxe-table").VxeColumnPropTypes.Align;
                            headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                            footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                            showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                            showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                            showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                            className: import("vxe-table").VxeColumnPropTypes.ClassName;
                            headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                            footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                            formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                            sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                            sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                            sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                            filters: {
                                label?: string | number | undefined;
                                value?: any;
                                data?: any;
                                resetValue?: any;
                                checked?: boolean | undefined;
                            }[];
                            filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                            filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                            filterRender: {
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                content?: string | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                events?: {
                                    [key: string]: (...args: any[]) => any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                            visible: import("vxe-table").VxeColumnPropTypes.Visible;
                            exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                            footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                            titleHelp: {
                                useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                                content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                                enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                                theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                                icon?: string | undefined;
                                message?: string | undefined;
                            };
                            titlePrefix: {
                                useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                                content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                                enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                                theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                                icon?: string | undefined;
                                message?: string | undefined;
                            };
                            titleSuffix: {
                                useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                                content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                                enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                                theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                                icon?: string | undefined;
                            };
                            cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                            cellRender: {
                                events?: {
                                    [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                                } | undefined;
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                content?: string | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            editRender: {
                                events?: {
                                    [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                                } | undefined;
                                enabled?: boolean | undefined;
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                autofocus?: string | undefined;
                                autoselect?: boolean | undefined;
                                defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                                    column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                                }) => any) | null | undefined;
                                immediate?: boolean | undefined;
                                content?: string | undefined;
                                placeholder?: string | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            contentRender: {
                                options?: any[] | undefined;
                                optionProps?: {
                                    value?: string | undefined;
                                    label?: string | undefined;
                                    disabled?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                optionGroups?: any[] | undefined;
                                optionGroupProps?: {
                                    options?: string | undefined;
                                    label?: string | undefined;
                                    key?: string | undefined;
                                } | undefined;
                                name?: string | undefined;
                                props?: {
                                    [key: string]: any;
                                } | undefined;
                                attrs?: {
                                    [key: string]: any;
                                } | undefined;
                                events?: {
                                    [key: string]: (...args: any[]) => any;
                                } | undefined;
                                children?: any[] | undefined;
                                cellType?: "string" | "number" | undefined;
                            };
                            params: import("vxe-table").VxeColumnPropTypes.Params;
                            slots: {
                                title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                                icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            };
                            id: string;
                            parentId: string;
                            level: number;
                            rowSpan: number;
                            colSpan: number;
                            halfVisible: boolean;
                            defaultVisible: any;
                            defaultFixed: any;
                            checked: boolean;
                            halfChecked: boolean;
                            disabled: boolean;
                            order: import("vxe-table").VxeTablePropTypes.SortOrder;
                            sortTime: number;
                            sortNumber: number;
                            renderSortNumber: number;
                            renderWidth: number;
                            renderHeight: number;
                            resizeWidth: number;
                            model: {
                                update: boolean;
                                value: any;
                            };
                            children: /*elided*/ any[];
                            renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                            renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                            renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                            renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                            getTitle: () => string;
                            getKey: () => string;
                        };
                        field: import("vxe-table").VxeColumnPropTypes.Field;
                        property: import("vxe-table").VxeColumnPropTypes.Field;
                        order: import("vxe-table").VxeTablePropTypes.SortOrder;
                        sortTime: number;
                    }[] | undefined;
                    form?: Record<string, any> | undefined;
                };
                load: (reset?: boolean) => Promise<void>;
                search: (reset?: boolean) => void;
                vxeRef: Ref< VxeGridInstance | undefined, VxeGridInstance | undefined>;
                rowSortable: Ref<any, any>;
                columnSortable: Ref<any[], any[]>;
                insertActived: (record?: any, row?: any) => Promise<void>;
                validate: () => Promise<import("vxe-table").VxeTableDefines.ValidatorErrorMapParams<any> | undefined>;
                getSelected: () => any;
                remove: (rows: any) => Promise< MessageBoxData | undefined>;
                getRows: () => any[];
                setActived: (row: any) => Promise<void | undefined>;
                doLayout: () => void;
                getRecords: () => {
                    insertRecords: any[];
                    removeRecords: any[];
                    updateRecords: any[];
                    pendingRecords: any[];
                } | undefined;
                setSelectCell: (row?: any, column?: any) => void;
                $vtjDynamicSlots: () => string[];
            }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
                rowSort: (e: GridSortableEvent) => any;
                columnSort: (e: GridSortableEvent) => any;
                cellSelected: (params: any) => any;
                editChange: (data: any[]) => any;
                loaded: (rows: any[]) => any;
            }, string, {
                auto: boolean;
                resizable: boolean;
                editable: boolean;
                page: number;
                pageSize: number;
                columns: GridColumns;
                rowSortable: boolean | Options;
                columnSortable: boolean | Options;
                customable: boolean;
                pager: boolean;
                pageSizes: number[];
                virtual: boolean;
            }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                beforeCreate?: (() => void) | (() => void)[];
                created?: (() => void) | (() => void)[];
                beforeMount?: (() => void) | (() => void)[];
                mounted?: (() => void) | (() => void)[];
                beforeUpdate?: (() => void) | (() => void)[];
                updated?: (() => void) | (() => void)[];
                activated?: (() => void) | (() => void)[];
                deactivated?: (() => void) | (() => void)[];
                beforeDestroy?: (() => void) | (() => void)[];
                beforeUnmount?: (() => void) | (() => void)[];
                destroyed?: (() => void) | (() => void)[];
                unmounted?: (() => void) | (() => void)[];
                renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
            };
            $forceUpdate: () => void;
            $nextTick: typeof nextTick;
            $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
        } & Readonly<{
            auto: boolean;
            resizable: boolean;
            editable: boolean;
            page: number;
            pageSize: number;
            columns: GridColumns;
            rowSortable: boolean | Options;
            columnSortable: boolean | Options;
            customable: boolean;
            pager: boolean;
            pageSizes: number[];
            virtual: boolean;
        }> & Omit<Readonly< ExtractPropTypes<{
            id: {
                type: StringConstructor;
            };
            columns: {
                type: PropType<GridColumns>;
                default(): GridColumns;
            };
            loader: {
                type: PropType<GridLoader>;
            };
            rowSortable: {
                type: PropType<boolean | GridSortableOptions>;
                default: boolean;
            };
            columnSortable: {
                type: PropType<boolean | GridSortableOptions>;
                default: boolean;
            };
            customable: {
                type: BooleanConstructor;
            };
            getCustom: {
                type: PropType<(id: string) => Promise< GridCustomInfo>>;
            };
            saveCustom: {
                type: PropType<(info: GridCustomInfo) => Promise<any>>;
            };
            resizable: {
                type: BooleanConstructor;
                default: boolean;
            };
            pager: {
                type: BooleanConstructor;
            };
            page: {
                type: NumberConstructor;
                default: number;
            };
            pageSize: {
                type: NumberConstructor;
                default: number;
            };
            pageSizes: {
                type: PropType<number[]>;
                default: () => number[];
            };
            auto: {
                type: BooleanConstructor;
                default: boolean;
            };
            virtual: {
                type: BooleanConstructor;
                default: boolean;
            };
            cellRenders: {
                type: PropType<GridCellRenders>;
            };
            editRenders: {
                type: PropType<GridEditRenders>;
            };
            filterRenders: {
                type: PropType<GridFilterRenders>;
            };
            editable: {
                type: BooleanConstructor;
                default: boolean;
            };
            sumFields: {
                type: PropType<string[]>;
            };
            avgFields: {
                type: PropType<string[]>;
            };
            sumAllFields: {
                type: PropType<Record<string, number>>;
            };
        }>> & Readonly<{
            onRowSort?: ((e: GridSortableEvent) => any) | undefined;
            onColumnSort?: ((e: GridSortableEvent) => any) | undefined;
            onCellSelected?: ((params: any) => any) | undefined;
            onEditChange?: ((data: any[]) => any) | undefined;
            onLoaded?: ((rows: any[]) => any) | undefined;
        }>, "search" | "load" | "state" | "remove" | "setSelectCell" | "validate" | "vxeRef" | ("auto" | "resizable" | "editable" | "page" | "pageSize" | "columns" | "rowSortable" | "columnSortable" | "customable" | "pager" | "pageSizes" | "virtual") | "doLayout" | "insertActived" | "getSelected" | "getRows" | "setActived" | "getRecords" | "$vtjDynamicSlots"> & ShallowUnwrapRef<{
            state: {
                [x: string]: any;
                page?: number | undefined;
                pageSize?: number | undefined;
                total?: number | undefined;
                filters?: {
                    column: {
                        property: import("vxe-table").VxeColumnPropTypes.Field;
                        type: import("vxe-table").VxeColumnPropTypes.Type;
                        field: import("vxe-table").VxeColumnPropTypes.Field;
                        title: import("vxe-table").VxeColumnPropTypes.Title;
                        width: import("vxe-table").VxeColumnPropTypes.Width;
                        minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                        maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                        resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                        fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                        align: import("vxe-table").VxeColumnPropTypes.Align;
                        headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                        footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                        showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                        showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                        showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                        className: import("vxe-table").VxeColumnPropTypes.ClassName;
                        headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                        footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                        formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                        sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                        sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                        sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                        filters: {
                            label?: string | number | undefined;
                            value?: any;
                            data?: any;
                            resetValue?: any;
                            checked?: boolean | undefined;
                        }[];
                        filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                        filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                        filterRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                        visible: import("vxe-table").VxeColumnPropTypes.Visible;
                        exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                        footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                        titleHelp: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titlePrefix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titleSuffix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                        };
                        cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                        cellRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                            } | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        editRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                            } | undefined;
                            enabled?: boolean | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            autofocus?: string | undefined;
                            autoselect?: boolean | undefined;
                            defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                                column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                            }) => any) | null | undefined;
                            immediate?: boolean | undefined;
                            content?: string | undefined;
                            placeholder?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        contentRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        params: import("vxe-table").VxeColumnPropTypes.Params;
                        slots: {
                            title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        };
                        id: string;
                        parentId: string;
                        level: number;
                        rowSpan: number;
                        colSpan: number;
                        halfVisible: boolean;
                        defaultVisible: any;
                        defaultFixed: any;
                        checked: boolean;
                        halfChecked: boolean;
                        disabled: boolean;
                        order: import("vxe-table").VxeTablePropTypes.SortOrder;
                        sortTime: number;
                        sortNumber: number;
                        renderSortNumber: number;
                        renderWidth: number;
                        renderHeight: number;
                        resizeWidth: number;
                        model: {
                            update: boolean;
                            value: any;
                        };
                        children: /*elided*/ any[];
                        renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                        renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                        renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                        renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                        getTitle: () => string;
                        getKey: () => string;
                    };
                    field: import("vxe-table").VxeColumnPropTypes.Field;
                    property: import("vxe-table").VxeColumnPropTypes.Field;
                    values: any[];
                    datas: any[];
                }[] | undefined;
                sorts?: {
                    column: {
                        property: import("vxe-table").VxeColumnPropTypes.Field;
                        type: import("vxe-table").VxeColumnPropTypes.Type;
                        field: import("vxe-table").VxeColumnPropTypes.Field;
                        title: import("vxe-table").VxeColumnPropTypes.Title;
                        width: import("vxe-table").VxeColumnPropTypes.Width;
                        minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                        maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                        resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                        fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                        align: import("vxe-table").VxeColumnPropTypes.Align;
                        headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                        footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                        showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                        showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                        showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                        className: import("vxe-table").VxeColumnPropTypes.ClassName;
                        headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                        footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                        formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                        sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                        sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                        sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                        filters: {
                            label?: string | number | undefined;
                            value?: any;
                            data?: any;
                            resetValue?: any;
                            checked?: boolean | undefined;
                        }[];
                        filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                        filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                        filterRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                        visible: import("vxe-table").VxeColumnPropTypes.Visible;
                        exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                        footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                        titleHelp: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titlePrefix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titleSuffix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                        };
                        cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                        cellRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                            } | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        editRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                            } | undefined;
                            enabled?: boolean | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            autofocus?: string | undefined;
                            autoselect?: boolean | undefined;
                            defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                                column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                            }) => any) | null | undefined;
                            immediate?: boolean | undefined;
                            content?: string | undefined;
                            placeholder?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        contentRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        params: import("vxe-table").VxeColumnPropTypes.Params;
                        slots: {
                            title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        };
                        id: string;
                        parentId: string;
                        level: number;
                        rowSpan: number;
                        colSpan: number;
                        halfVisible: boolean;
                        defaultVisible: any;
                        defaultFixed: any;
                        checked: boolean;
                        halfChecked: boolean;
                        disabled: boolean;
                        order: import("vxe-table").VxeTablePropTypes.SortOrder;
                        sortTime: number;
                        sortNumber: number;
                        renderSortNumber: number;
                        renderWidth: number;
                        renderHeight: number;
                        resizeWidth: number;
                        model: {
                            update: boolean;
                            value: any;
                        };
                        children: /*elided*/ any[];
                        renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                        renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                        renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                        renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                        getTitle: () => string;
                        getKey: () => string;
                    };
                    field: import("vxe-table").VxeColumnPropTypes.Field;
                    property: import("vxe-table").VxeColumnPropTypes.Field;
                    order: import("vxe-table").VxeTablePropTypes.SortOrder;
                    sortTime: number;
                }[] | undefined;
                form?: Record<string, any> | undefined;
            };
            load: (reset?: boolean) => Promise<void>;
            search: (reset?: boolean) => void;
            vxeRef: Ref< VxeGridInstance | undefined, VxeGridInstance | undefined>;
            rowSortable: Ref<any, any>;
            columnSortable: Ref<any[], any[]>;
            insertActived: (record?: any, row?: any) => Promise<void>;
            validate: () => Promise<import("vxe-table").VxeTableDefines.ValidatorErrorMapParams<any> | undefined>;
            getSelected: () => any;
            remove: (rows: any) => Promise< MessageBoxData | undefined>;
            getRows: () => any[];
            setActived: (row: any) => Promise<void | undefined>;
            doLayout: () => void;
            getRecords: () => {
                insertRecords: any[];
                removeRecords: any[];
                updateRecords: any[];
                pendingRecords: any[];
            } | undefined;
            setSelectCell: (row?: any, column?: any) => void;
            $vtjDynamicSlots: () => string[];
        }> & {} & ComponentCustomProperties & {} & {
            $slots: Partial<Record<string, (_: {
                [key: string]: any;
                $table: VxeTableConstructor<any>;
                $grid: VxeGridConstructor<any> | null | undefined;
                row: any;
                rowIndex: number;
                $rowIndex: number;
                _rowIndex: number;
                column: import("vxe-table").VxeTableDefines.ColumnInfo<any>;
                columnIndex: number;
                $columnIndex: number;
                _columnIndex: number;
                checked?: boolean;
                indeterminate?: boolean;
                items: any[];
            }) => any>> & {
                empty?(_: {}): any;
                pager__left?(_: {}): any;
            };
        }) | null;
    }, any, ComponentProvideOptions, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly< Props> & Readonly<{}>, {
        pick: () => void;
        gridRef: Ref<any, any>;
    }, {}, {}, {}, {}> | null;
}, any>;
export default _default;
