{"version": 3, "sources": ["../../@vtj/core/dist/index.mjs"], "sourcesContent": ["import { uid as m, timestamp as G, isString as A, uuid as k, cloneDeep as C, upperFirstCamelCase as R, delay as P, merge as I, mitt as F } from \"@vtj/base\";\n/**!\n * Copyright (c) 2025, VTJ.PRO All rights reserved.\n * @name @vtj/core \n * @<NAME_EMAIL> \n * @version 0.12.47\n * @license <a href=\"https://vtj.pro/license.html\">MIT License</a>\n */\nconst Y = \"0.12.47\", W = \"BuiltIn\", H = \"VueMaterial\", J = \"VueRouterMaterial\", q = [H, J], z = {\n  vue: \"Vue\",\n  \"vue-router\": \"VueRouter\"\n}, Q = {\n  [H]: [\n    \"Transition\",\n    \"TransitionGroup\",\n    \"KeepAlive\",\n    \"Teleport\",\n    \"Suspense\"\n  ],\n  [J]: [\"RouterView\", \"RouterLink\"]\n}, X = [\n  \"slot\",\n  \"template\",\n  \"component\",\n  \"img\",\n  \"div\",\n  \"p\",\n  \"h1\",\n  \"h2\",\n  \"h3\",\n  \"span\",\n  \"a\"\n];\nclass Z {\n}\nclass ee {\n  listeners = [];\n  isReady = !1;\n  triggerReady() {\n    this.isReady = !0;\n    for (const e of this.listeners)\n      e();\n    this.listeners = [];\n  }\n  ready(e) {\n    this.isReady ? e() : this.listeners.push(e);\n  }\n  resetReady() {\n    this.isReady = !1;\n  }\n}\nclass x {\n  constructor(e, t, i) {\n    this.name = e, this.value = t, this.defaultValue = i, this.setValue(t);\n  }\n  /**\n   * 标识是否设置了值， 设置的值与默认值一致，表示未设置，在转换成dsl时会排查该属性\n   */\n  isUnset = !1;\n  setValue(e) {\n    this.value = e, this.isUnset = this.value === this.defaultValue;\n  }\n  getValue() {\n    return this.value ?? this.defaultValue;\n  }\n  static toDsl(e = {}) {\n    return Object.entries(e).reduce((t, [i, s]) => (s.isUnset || (t[i] = s.getValue()), t), {});\n  }\n  static parse(e = {}) {\n    return Object.entries(e).reduce((t, [i, s]) => (t[i] = new x(i, s), t), {});\n  }\n}\nclass T {\n  constructor(e) {\n    this.schema = e;\n    const { name: t, handler: i } = this.schema;\n    this.name = t, this.handler = i, this.update(e);\n  }\n  name;\n  handler;\n  modifiers = {};\n  update(e) {\n    Object.assign(this.schema, e);\n    const { handler: t, modifiers: i = {} } = this.schema;\n    this.handler = t, this.modifiers = i;\n  }\n  static toDsl(e) {\n    return Object.entries(e).reduce((t, [i, s]) => {\n      const { handler: a, modifiers: o } = s;\n      return t[i] = { name: i, handler: a, modifiers: o }, t;\n    }, {});\n  }\n  static parse(e = {}) {\n    return Object.entries(e).reduce(\n      (t, [i, s]) => (t[i] = new T(s), t),\n      {}\n    );\n  }\n}\nclass v {\n  constructor(e) {\n    this.schema = e, this.id = e.id || m(), this.update(e);\n  }\n  /**\n   * 标识\n   */\n  id;\n  /**\n   * 指令名称\n   */\n  name = \"\";\n  /**\n   * 参数\n   */\n  arg;\n  /**\n   * 修饰符\n   */\n  modifiers;\n  /**\n   * 指令值\n   */\n  value;\n  /**\n   *  v-for 迭代器\n   */\n  iterator;\n  update(e) {\n    Object.assign(this.schema, e);\n    const { name: t, arg: i, modifiers: s, value: a, iterator: o } = this.schema;\n    this.name = t, this.arg = i, this.modifiers = s, this.value = a, this.iterator = o;\n  }\n  static parse(e = []) {\n    return e.map((t) => new v(t));\n  }\n  static toDsl(e = []) {\n    return e.map((t) => {\n      const { name: i, arg: s, modifiers: a, value: o, iterator: d, id: c } = t;\n      return {\n        id: c,\n        name: i,\n        arg: s,\n        modifiers: a,\n        value: o,\n        iterator: d\n      };\n    });\n  }\n}\nconst h = \"EVENT_NODE_CHANGE\";\nclass _ {\n  constructor(e, t = null) {\n    this.parent = t;\n    const { id: i = m(), name: s, from: a = \"\" } = e;\n    this.id = i, this.name = s, this.from = a, this.update(e, !0), _.nodes[this.id] = this;\n  }\n  /**\n   * 标记\n   */\n  __VTJ_NODE__ = !0;\n  /**\n   * 锁定\n   */\n  locked = !1;\n  /**\n   * 记录所有节点的实例\n   */\n  static nodes = {};\n  /**\n   * 节点唯一标识\n   */\n  id;\n  /**\n   * 名称，即组件的名称或html的标签名\n   */\n  name;\n  /**\n   * 组件来源\n   */\n  from;\n  /**\n   * 是否不可见\n   */\n  invisible = !1;\n  /**\n   * 子节点\n   */\n  children = \"\";\n  /**\n   * 放置在父组件的插槽\n   */\n  slot;\n  /**\n   * 节点属性\n   */\n  props = {};\n  /**\n   * 节点事件\n   */\n  events = {};\n  /**\n   * 指令\n   */\n  directives = [];\n  /**\n   * 销毁标识\n   */\n  disposed = !1;\n  /**\n   * 更新节点属性\n   * @param schema\n   * @param silent 是否静默，静默更新即不触发事件\n   */\n  update(e, t = !1) {\n    const {\n      invisible: i = !1,\n      locked: s = !1,\n      children: a = [],\n      slot: o,\n      props: d = {},\n      events: c = {},\n      directives: p = []\n    } = e;\n    this.invisible = i, this.locked = s, this.setChildren(a, !0), this.setSlot(o, !0), this.props = x.parse(d), this.events = T.parse(c), this.directives = v.parse(p), t || n.emit(h, this);\n  }\n  /**\n   * 设置子节点\n   * @param children\n   * @param silent\n   */\n  setChildren(e = \"\", t = !1) {\n    Array.isArray(e) ? this.children = e.map((i) => new _(i, this)) : this.children = e, t || n.emit(h, this);\n  }\n  /**\n   * 设置节点放置的插槽\n   * @param slot\n   * @param silent\n   */\n  setSlot(e, t = !1) {\n    this.slot = typeof e == \"string\" ? { name: e, params: [] } : e, t || n.emit(h, this);\n  }\n  /**\n   * 新增或更新属性\n   * @param name\n   * @param value\n   * @param defaultValue\n   * @param silent\n   */\n  setProp(e, t, i, s = !1) {\n    const a = this.props[e];\n    a ? a.setValue(t) : this.props[e] = new x(e, t, i), s || n.emit(h, this);\n  }\n  /**\n   * 删除属性\n   * @param name\n   * @param silent\n   */\n  removeProp(e, t = !1) {\n    delete this.props[e], t || n.emit(h, this);\n  }\n  /**\n   * 获取属性值\n   * @param name\n   * @returns\n   */\n  getPropValue(e) {\n    const t = this.props[e];\n    if (t)\n      return t.getValue();\n  }\n  /**\n   * 新增或更新事件\n   * @param scheam\n   * @param silent\n   */\n  setEvent(e, t = !1) {\n    const i = this.events[e.name];\n    i ? i.update(e) : this.events[e.name] = new T(e), t || n.emit(h, this);\n  }\n  /**\n   * 删除事件\n   * @param name\n   * @param silent\n   */\n  removeEvent(e, t = !1) {\n    delete this.events[e], t || n.emit(h, this);\n  }\n  /**\n   * 新增或更新指令\n   * @param scheam\n   * @param silent\n   */\n  setDirective(e, t = !1) {\n    const i = e instanceof v ? e : new v(e), s = this.directives.findIndex((a) => a.id === e.id);\n    s >= 0 ? this.directives.splice(s, 1, i) : this.directives.push(i), t || n.emit(h, this);\n  }\n  /**\n   * 删除指令\n   * @param dirctive\n   * @param silent\n   */\n  removeDirective(e, t = !1) {\n    const i = this.directives.findIndex(\n      (s) => s === e || s.id === e.id\n    );\n    i >= 0 && this.directives.splice(i, 1), t || n.emit(h, this);\n  }\n  /**\n   * 删除子节点\n   * @param node\n   * @param silent\n   * @returns\n   */\n  removeChild(e, t = !1) {\n    const { children: i, disposed: s } = this;\n    if (s || !Array.isArray(i)) return;\n    const a = i.findIndex((o) => o === e);\n    e.parent = null, i.splice(a, 1), t || n.emit(h, this);\n  }\n  /**\n   * 追加子节点\n   * @param node\n   * @param silent\n   * @returns\n   */\n  appendChild(e, t = !1) {\n    const { children: i, disposed: s } = this;\n    s || (e.parent = this, Array.isArray(i) ? i.push(e) : this.children = [e], t || n.emit(h, this));\n  }\n  /**\n   * 在当前节点的后面插入节点\n   * @param node\n   * @param silent\n   * @returns\n   */\n  insertAfter(e, t = !1) {\n    if (!this.parent) return;\n    const i = this.parent.children;\n    if (Array.isArray(i)) {\n      e.parent = this.parent;\n      const s = i.indexOf(this);\n      i.splice(s + 1, 0, e), t || n.emit(h, this.parent);\n    }\n  }\n  /**\n   * 在当前节点的前面插入节点\n   * @param node\n   * @param silent\n   * @returns\n   */\n  insertBefore(e, t = !1) {\n    if (!this.parent) return;\n    const i = this.parent.children;\n    if (Array.isArray(i)) {\n      e.parent = this.parent;\n      const s = i.indexOf(this);\n      i.splice(s, 0, e), t || n.emit(h, this.parent);\n    }\n  }\n  movePrev(e = !1) {\n    const t = this.parent;\n    if (!t) return;\n    const i = t.children;\n    if (Array.isArray(i)) {\n      const s = i.indexOf(this);\n      s > 0 && (i.splice(s, 1), i.splice(s - 1, 0, this), e || n.emit(h, t));\n    }\n  }\n  moveNext(e = !1) {\n    const t = this.parent;\n    if (!t) return;\n    const i = t.children;\n    if (Array.isArray(i)) {\n      const s = i.indexOf(this);\n      s > -1 && s < i.length - 1 && (i.splice(s, 1), i.splice(s + 1, 0, this), e || n.emit(h, t));\n    }\n  }\n  /**\n   * 获取DSL\n   * @returns\n   */\n  toDsl() {\n    const {\n      id: e,\n      name: t,\n      from: i,\n      invisible: s,\n      locked: a,\n      slot: o,\n      children: d,\n      props: c,\n      directives: p,\n      events: O\n    } = this, E = Array.isArray(d) ? d.map((U) => U.toDsl()) : d;\n    return {\n      id: e,\n      name: t,\n      from: i,\n      invisible: s,\n      locked: a,\n      slot: o,\n      children: E,\n      props: x.toDsl(c),\n      directives: v.toDsl(p),\n      events: T.toDsl(O)\n    };\n  }\n  /**\n   * 销毁\n   * @param silent\n   * @returns\n   */\n  dispose(e = !1) {\n    const { children: t, disposed: i } = this;\n    i || (Array.isArray(t) && t.forEach((s) => s.dispose(!0)), this.parent ? this.parent.removeChild(this, e) : e || n.emit(h, this), this.parent = null, this.disposed = !0, delete _.nodes[this.id]);\n  }\n  lock(e = !1) {\n    if (this.locked = !0, Array.isArray(this.children))\n      for (const t of this.children)\n        t.lock(!0);\n    e || n.emit(h, this);\n  }\n  unlock(e = !1) {\n    if (this.locked = !1, Array.isArray(this.children))\n      for (const t of this.children)\n        t.unlock(!0);\n    e || n.emit(h, this);\n  }\n  setVisible(e, t = !1) {\n    if (this.invisible = !e, Array.isArray(this.children))\n      for (const i of this.children)\n        i.setVisible(e, !0);\n    t || n.emit(h, this);\n  }\n  isChild(e) {\n    let t = !1;\n    if (Array.isArray(this.children)) {\n      for (const i of this.children)\n        if (e === i || e.id === i.id) {\n          t = !0;\n          break;\n        } else if (t = i.isChild(e), t)\n          break;\n    }\n    return t;\n  }\n}\nconst r = \"EVENT_BLOCK_CHANGE\";\nclass u {\n  __VTJ_BLOCK__ = !0;\n  id;\n  name = \"\";\n  inject = [];\n  state = {};\n  lifeCycles = {};\n  methods = {};\n  computed = {};\n  watch = [];\n  css = \"\";\n  props = [];\n  emits = [];\n  slots = [];\n  dataSources = {};\n  nodes = [];\n  locked = !1;\n  disposed = !1;\n  static normalAttrs = [\n    \"name\",\n    \"locked\",\n    \"inject\",\n    \"state\",\n    \"lifeCycles\",\n    \"methods\",\n    \"computed\",\n    \"watch\",\n    \"css\",\n    \"props\",\n    \"emits\",\n    \"slots\",\n    \"dataSources\",\n    \"__TEMPLATE_ID__\"\n  ];\n  constructor(e) {\n    const { id: t } = e;\n    this.id = t || m(), this.update(e, !0);\n  }\n  update(e, t = !1) {\n    for (const s of u.normalAttrs) {\n      const a = e[s];\n      a !== void 0 && (this[s] = a);\n    }\n    const { nodes: i = [] } = e;\n    i.length && (this.nodes = i.map((s) => new _(s))), t || n.emit(r, this);\n  }\n  /**\n   * 获取DSL\n   * @returns\n   */\n  toDsl(e) {\n    const { __VTJ_BLOCK__: t, id: i, nodes: s } = this;\n    return {\n      ...u.normalAttrs.reduce(\n        (o, d) => (o[d] = this[d], o),\n        {}\n      ),\n      __VTJ_BLOCK__: t,\n      __VERSION__: e || G().toString(),\n      id: i,\n      nodes: s.map((o) => o.toDsl())\n    };\n  }\n  /**\n   * 销毁\n   */\n  dispose() {\n    this.nodes.map((e) => e.dispose(!0)), this.nodes = [], this.disposed = !0;\n  }\n  /**\n   * 设置通用函数属性\n   * @param type\n   * @param name\n   * @param value\n   * @param silent\n   */\n  setFunction(e, t, i, s = !1) {\n    this[e][t] = i, s || n.emit(r, this);\n  }\n  /**\n   * 删除通用函数属性\n   * @param type\n   * @param name\n   * @param silent\n   */\n  removeFunction(e, t, i = !1) {\n    delete this[e][t], i || n.emit(r, this);\n  }\n  /**\n   * 设置状态\n   * @param name\n   * @param value\n   * @param silent\n   */\n  setState(e, t, i = !1) {\n    this.state[e] = t, i || n.emit(r, this);\n  }\n  /**\n   * 删除状态\n   * @param name\n   * @param silent\n   */\n  removeState(e, t = !1) {\n    delete this.state[e], t || n.emit(r, this);\n  }\n  /**\n   * 更新CSS\n   * @param content\n   * @param silent\n   */\n  setCss(e, t = !1) {\n    this.css = e, t || n.emit(r, this);\n  }\n  /**\n   * 新增或更新 watch\n   * @param watch\n   * @param silent\n   */\n  setWatch(e, t = !1) {\n    e.id = e.id || m();\n    const i = this.watch.findIndex(\n      (s) => s.id && s.id === e.id || s === e\n    );\n    i > -1 ? this.watch.splice(i, 1, e) : this.watch.push(e), t || n.emit(r, this);\n  }\n  /**\n   * 删除 watch\n   * @param watch\n   * @param silent\n   */\n  removeWatch(e, t = !1) {\n    const i = this.watch.findIndex(\n      (s) => s.id && s.id === e.id || s === e\n    );\n    i > -1 && (this.watch.splice(i, 1), t || n.emit(r, this));\n  }\n  /**\n   * 定义属性参数\n   * @param prop\n   * @param silent\n   */\n  setProp(e, t = !1) {\n    const i = this.props.findIndex(\n      (s) => typeof s == \"string\" ? s === e.name : s.name === e.name\n    );\n    i > -1 ? this.props.splice(i, 1, e) : this.props.push(e), t || n.emit(r, this);\n  }\n  /**\n   * 删除属性\n   * @param prop\n   * @param silent\n   */\n  removeProp(e, t = !1) {\n    const i = this.props.findIndex(\n      (s) => typeof s == \"string\" ? s === e.name : s.name === e.name\n    );\n    i > -1 && (this.props.splice(i, 1), t || n.emit(r, this));\n  }\n  /**\n   * 设置事件\n   * @param emit\n   * @param silent\n   */\n  setEmit(e, t = !1) {\n    const i = A(e) ? { name: e, params: [] } : e, s = this.emits.findIndex((a) => a === i.name || a === i);\n    s > -1 ? this.emits.splice(s, 1, i) : this.emits.push(i), t || n.emit(r, this);\n  }\n  /**\n   * 删除事件\n   * @param emit\n   * @param silent\n   */\n  removeEmit(e, t = !1) {\n    const i = this.emits.findIndex(\n      (s) => A(s) ? s === e : s.name === e\n    );\n    i > -1 && (this.emits.splice(i, 1), t || n.emit(r, this));\n  }\n  /**\n   * 设置插槽\n   * @param slot\n   * @param silent\n   */\n  setSlot(e, t = !1) {\n    const i = A(e) ? { name: e, params: [] } : e, s = this.slots.findIndex((a) => a === i.name || a === i);\n    s > -1 ? this.slots.splice(s, 1, i) : this.slots.push(i), t || n.emit(r, this);\n  }\n  /**\n   * 删除插槽\n   * @param slot\n   * @param silent\n   */\n  removeSlot(e, t = !1) {\n    const i = this.slots.findIndex(\n      (s) => A(s) ? s === e : s.name === e\n    );\n    i > -1 && (this.slots.splice(i, 1), t || n.emit(r, this));\n  }\n  /**\n   * 设置注入\n   * @param inject\n   * @param silent\n   */\n  setInject(e, t = !1) {\n    const i = this.inject.findIndex((s) => s.name === e.name);\n    i > -1 ? this.inject.splice(i, 1, e) : this.inject.push(e), t || n.emit(r, this);\n  }\n  /**\n   * 删除注入\n   * @param inject\n   * @param silent\n   */\n  removeInject(e, t = !1) {\n    const i = this.inject.findIndex((s) => s.name === e.name);\n    i > -1 && (this.inject.splice(i, 1), t || n.emit(r, this));\n  }\n  /**\n   * 设置数据源\n   * @param source\n   * @param silent\n   */\n  setDataSource(e, t = !1) {\n    this.dataSources[e.name] = e, t || n.emit(r, this);\n  }\n  /**\n   * 删除数据源\n   * @param name\n   * @param silent\n   */\n  removeDataSource(e, t = !1) {\n    delete this.dataSources[e], t || n.emit(r, this);\n  }\n  insertAfter(e, t, i = !1) {\n    e.parent = null;\n    const s = this.nodes.indexOf(t);\n    this.nodes.splice(s + 1, 0, e), i || n.emit(r, this);\n  }\n  insertBefore(e, t, i = !1) {\n    e.parent = null;\n    const s = this.nodes.indexOf(t);\n    this.nodes.splice(s, 0, e), i || n.emit(r, this);\n  }\n  appendNode(e, t = !1) {\n    e.parent = null, this.nodes.push(e), t || n.emit(r, this);\n  }\n  /**\n   * 添加节点\n   * @param node\n   * @param target\n   * @param position\n   * @param silent\n   */\n  addNode(e, t, i = \"inner\", s = !1) {\n    t ? [\"left\", \"top\"].includes(i) ? t.parent ? t.insertAfter(e, s) : this.insertBefore(e, t, s) : [\"right\", \"bottom\"].includes(i) ? t.parent ? t.insertAfter(e, s) : this.insertAfter(e, t, s) : t.appendChild(e, s) : this.appendNode(e, s);\n  }\n  __removeNode(e, t = !1) {\n    const i = this.nodes.findIndex((s) => s.id === e.id);\n    i > -1 && (this.nodes.splice(i, 1), t || n.emit(r, this));\n  }\n  /**\n   * 删除节点\n   * @param node\n   * @param silent\n   */\n  removeNode(e, t = !1) {\n    e.parent ? e.dispose(t) : (e.dispose(!0), this.__removeNode(e, t));\n  }\n  /**\n   * 移动节点\n   * @param node\n   * @param target\n   * @param position\n   * @param silent\n   */\n  move(e, t, i = \"inner\", s = !1) {\n    e.parent ? e.parent.removeChild(e, !0) : this.__removeNode(e, !0), this.addNode(e, t, i, s);\n  }\n  /**\n   * 向前交换节点\n   * @param node\n   * @param silent\n   */\n  movePrev(e, t = !1) {\n    if (e.parent)\n      e.movePrev(t);\n    else {\n      const i = this.nodes, s = i.indexOf(e);\n      s > 0 && (i.splice(s, 1), i.splice(s - 1, 0, e), t || n.emit(r, this));\n    }\n  }\n  /**\n   * 向后交换节点\n   * @param node\n   * @param silent\n   */\n  moveNext(e, t = !1) {\n    if (e.parent)\n      e.moveNext(t);\n    else {\n      const i = this.nodes, s = i.indexOf(e);\n      s > -1 && s < i.length - 1 && (i.splice(s, 1), i.splice(s + 1, 0, e), t || n.emit(r, this));\n    }\n  }\n  /**\n   * 克隆节点\n   * @param target\n   * @param silent\n   * @returns\n   */\n  cloneNode(e, t = !1) {\n    const i = L(e.toDsl()), s = new _(i);\n    return this.addNode(s, e, \"bottom\", t), s;\n  }\n  lock(e = !1) {\n    this.locked = !0;\n    for (const t of this.nodes)\n      t.lock(!0);\n    e || n.emit(r, this);\n  }\n  unlock(e = !1) {\n    this.locked = !1;\n    for (const t of this.nodes)\n      t.unlock(!0);\n    e || n.emit(r, this);\n  }\n  isChild(e) {\n    let t = !1;\n    for (const i of this.nodes)\n      if (e === i || e.id === i.id) {\n        t = !0;\n        break;\n      } else if (t = i.isChild(e), t)\n        break;\n    return t;\n  }\n}\nconst l = \"EVENT_PROJECT_CHANGE\", D = \"EVENT_PROJECT_ACTIVED\", B = \"EVENT_PROJECT_DEPS_CHANGE\", g = \"EVENT_PROJECT_PAGES_CHANGE\", y = \"EVENT_PROJECT_BLOCKS_CHANGE\", b = \"EVENT_PROJECT_APIS_CHANGE\", w = \"EVENT_PROJECT_META_CHANGE\", j = \"EVENT_PROJECT_PUBLISH\", $ = \"EVENT_PROJECT_FILE_PUBLISH\", M = \"EVENT_PROJECT_GEN_SOURCE\";\nclass S {\n  id = \"\";\n  locked = \"\";\n  platform = \"web\";\n  name = \"\";\n  description = \"\";\n  homepage = \"\";\n  dependencies = [];\n  pages = [];\n  blocks = [];\n  apis = [];\n  meta = [];\n  currentFile = null;\n  config = {};\n  uniConfig = {};\n  __BASE_PATH__ = \"/\";\n  __UID__ = k(!0);\n  static attrs = [\n    \"locked\",\n    \"platform\",\n    \"name\",\n    \"homepage\",\n    \"description\",\n    \"dependencies\",\n    \"pages\",\n    \"blocks\",\n    \"apis\",\n    \"meta\",\n    \"config\",\n    \"uniConfig\",\n    \"__BASE_PATH__\",\n    \"__UID__\"\n  ];\n  constructor(e) {\n    const { id: t, __UID__: i } = e;\n    this.id = t || m(), this.__UID__ = i || k(!0), this.update(e, !0);\n  }\n  update(e, t = !1) {\n    for (const i of S.attrs) {\n      const s = e[i];\n      s && (this[i] = s);\n    }\n    t || n.emit(l, {\n      model: this,\n      type: \"update\",\n      data: e\n    });\n  }\n  isPageFile(e) {\n    return e.type === \"page\";\n  }\n  cleanPagesDsl(e) {\n    for (const t of e)\n      delete t.dsl, t.children && t.children.length && this.cleanPagesDsl(t.children);\n  }\n  toDsl(e) {\n    const { id: t } = this, i = S.attrs.reduce(\n      (s, a) => (s[a] = this[a], s),\n      {}\n    );\n    return i.pages && (i.pages = i.pages.map((s) => C({\n      ...s,\n      dsl: void 0\n    })), this.cleanPagesDsl(i.pages)), i.blocks && (i.blocks = i.blocks.map((s) => C({\n      ...s,\n      dsl: void 0\n    }))), {\n      __VTJ_PROJECT__: !0,\n      id: t,\n      ...i\n    };\n  }\n  /**\n   * 打开文件\n   * @param file\n   * @param silent\n   */\n  active(e, t = !1) {\n    this.currentFile = e, t || n.emit(D, {\n      model: this,\n      type: \"update\",\n      data: e\n    });\n  }\n  /**\n   * 关闭文件\n   * @param silent\n   */\n  deactivate(e = !1) {\n    this.currentFile = null, e || n.emit(D, {\n      model: this,\n      type: \"update\",\n      data: null\n    });\n  }\n  /**\n   * 新增或更新依赖\n   * @param item\n   * @param silent\n   */\n  setDeps(e, t = !1) {\n    const i = this.dependencies, s = i.findIndex((o) => o.package === e.package);\n    let a;\n    if (s > -1 ? (a = \"update\", i.splice(s, 1, {\n      ...i[s],\n      ...e\n    })) : (a = \"create\", i.push(e)), !t) {\n      const o = {\n        model: this,\n        type: a,\n        data: e\n      };\n      n.emit(B, o), n.emit(l, o);\n    }\n  }\n  /**\n   * 删除依赖\n   * @param item\n   * @param silent\n   */\n  removeDeps(e, t = !1) {\n    const i = this.dependencies, s = i.findIndex((a) => a.package === e.package);\n    if (s > -1 && i.splice(s, 1), !t) {\n      const a = {\n        model: this,\n        type: \"delete\",\n        data: e\n      };\n      n.emit(B, a), n.emit(l, a);\n    }\n  }\n  /**\n   * 根据页面id查找页面或目录\n   * @param id\n   * @returns\n   */\n  getPage(e) {\n    const t = (i, s = []) => {\n      for (const a of s) {\n        if (a.id === i)\n          return a;\n        if (a.children && a.children.length) {\n          const o = t(i, a.children);\n          if (o)\n            return o;\n        }\n      }\n    };\n    return t(e, this.pages);\n  }\n  /**\n   * 查找全部页面，不含目录\n   * @returns\n   */\n  getPages() {\n    const e = (t = []) => {\n      let i = [];\n      for (const s of t)\n        s.dir ? s.children && s.children.length && (i = i.concat(e(s.children))) : i.push(s);\n      return i;\n    };\n    return e(this.pages);\n  }\n  /**\n   * 新建页面\n   * @param page\n   * @param parentId\n   * @param silent\n   */\n  async createPage(e, t, i = !1) {\n    if (e.id = e.raw ? e.name : e.id || m(), e.type = \"page\", e.dir ? e.children = [] : e.dsl = e.dsl || new u({\n      id: e.id,\n      name: R(e.name)\n    }).toDsl(), t) {\n      const s = this.getPage(t);\n      s ? s.children ? s.children.push(e) : s.children = [e] : console.warn(`not found PageFile for id: ${t} `);\n    } else\n      this.pages.push(e);\n    if (!i) {\n      const s = {\n        model: this,\n        type: \"create\",\n        data: e\n      };\n      n.emit(g, s), n.emit(l, s);\n    }\n    !this.currentFile && !e.dir && (await P(1e3), this.active(e, i));\n  }\n  /**\n   * 更新页面\n   * @param page\n   * @param silent\n   */\n  updatePage(e, t = !1) {\n    const i = this.getPage(e.id);\n    if (delete e.dsl, i ? Object.assign(i, e) : console.warn(`not found PageFile for id: ${e.id} `), !t) {\n      const s = {\n        model: this,\n        type: \"update\",\n        data: e\n      };\n      n.emit(g, s), n.emit(l, s);\n    }\n  }\n  /**\n   * 复制页面\n   * @param page\n   * @param parentId\n   * @param silent\n   */\n  clonePage(e, t, i = !1) {\n    const s = m(), a = `${e.name}Copy`, o = `${e.title}_副本`, d = new u({\n      id: s,\n      name: a\n    }).toDsl(), c = I({}, e, { id: s, name: a, title: o, dsl: d }), p = t ? this.getPage(t)?.children || [] : this.pages, O = p.findIndex((E) => E.id === e.id);\n    if (p.splice(O + 1, 0, c), !i) {\n      const E = {\n        model: this,\n        type: \"clone\",\n        data: {\n          source: e,\n          target: c\n        }\n      };\n      n.emit(g, E), n.emit(l, E);\n    }\n  }\n  async saveToBlock(e, t = !1) {\n    this.active(e, t), await P(1e3);\n    const i = m(), s = e.name, a = e.title, o = new u({\n      ...e.dsl,\n      id: i,\n      name: s\n    }).toDsl(), d = I({}, e, {\n      id: i,\n      name: s,\n      title: a,\n      dsl: o,\n      type: \"block\",\n      fromType: \"Schema\"\n    });\n    if (this.blocks.push(d), !t) {\n      const c = {\n        model: this,\n        type: \"create\",\n        data: d\n      };\n      n.emit(y, c), n.emit(l, c);\n    }\n  }\n  /**\n   * 删除页面或目录\n   * @param id\n   * @param silent\n   */\n  removePage(e, t = !1) {\n    const i = this.getPage(e), s = (a, o) => {\n      const d = o.findIndex((c) => c.id === a);\n      if (d >= 0) {\n        o.splice(d, 1);\n        return;\n      }\n      for (const c of o)\n        c.children && c.children.length && s(a, c.children);\n    };\n    if (s(e, this.pages), e === this.homepage && (this.homepage = \"\"), this.currentFile?.id === e && this.deactivate(t), !t) {\n      const a = {\n        model: this,\n        type: \"delete\",\n        data: i\n      };\n      n.emit(g, a), n.emit(l, a);\n    }\n  }\n  /**\n   * 获取区块文件\n   * @param id\n   * @returns\n   */\n  getBlock(e) {\n    return this.blocks.find((t) => t.id === e);\n  }\n  /**\n   * 创建区块\n   * @param block\n   * @param silent\n   */\n  async createBlock(e, t = !1) {\n    const i = e.id || m(), s = R(e.name);\n    e.id = i, e.type = \"block\", e.dsl = new u({ id: i, name: s }).toDsl(), this.blocks.push(e);\n    const a = e.fromType || \"Schema\";\n    if (!t) {\n      const o = {\n        model: this,\n        type: \"create\",\n        data: e\n      };\n      n.emit(y, o), n.emit(l, o);\n    }\n    !this.currentFile && a === \"Schema\" && (await P(1e3), this.active(e, t));\n  }\n  /**\n   *\n   * @param block 更新区块\n   * @param silent\n   */\n  updateBlock(e, t = !1) {\n    const i = this.getBlock(e.id);\n    if (i ? (Object.assign(i, e), i.dsl && (i.dsl.name = e.name)) : console.warn(`not found PageFile for id: ${e.id} `), !t) {\n      const s = {\n        model: this,\n        type: \"update\",\n        data: e\n      };\n      n.emit(y, s), n.emit(l, s);\n    }\n  }\n  cloneBlock(e, t = !1) {\n    const i = m(), s = `${e.name}Copy`, a = `${e.title}_副本`, o = new u({\n      id: i,\n      name: s\n    }).toDsl(), d = I({}, e, { id: i, name: s, title: a, dsl: o }), c = this.blocks.findIndex((p) => p.id === e.id);\n    if (this.blocks.splice(c + 1, 0, d), !t) {\n      const p = {\n        model: this,\n        type: \"clone\",\n        data: {\n          source: e,\n          target: d\n        }\n      };\n      n.emit(y, p), n.emit(l, p);\n    }\n  }\n  /**\n   * 删除区块\n   * @param id\n   * @param silent\n   */\n  removeBlock(e, t = !1) {\n    const i = this.getBlock(e), s = this.blocks, a = s.findIndex((o) => o.id === e);\n    if (a > -1 ? (s.splice(a, 1), this.currentFile?.id === e && this.deactivate(t)) : console.warn(`not found PageFile for id: ${e} `), !t) {\n      const o = {\n        model: this,\n        type: \"delete\",\n        data: i\n      };\n      n.emit(y, o), n.emit(l, o);\n    }\n  }\n  /**\n   * 检查是否存在名称的区块\n   * @param name\n   * @param excludes\n   * @returns\n   */\n  existBlockName(e, t = []) {\n    return this.blocks.some((i) => i.name === e && !t.includes(i.id));\n  }\n  /**\n   * 检测是否存在名称的页面\n   * @param name\n   * @param excludes\n   * @returns\n   */\n  existPageName(e, t = []) {\n    return this.getPages().some((s) => s.name === e && !t.includes(s.id));\n  }\n  /**\n   * 新增或更新api\n   * @param item\n   * @param silent\n   */\n  setApi(e, t = !1) {\n    const i = this.apis.find(\n      (a) => a.name === e.name || a.id === e.id\n    );\n    let s;\n    if (i ? (s = \"update\", Object.assign(i, e)) : (s = \"create\", e.id = m(), this.apis.push(e)), !t) {\n      const a = {\n        model: this,\n        type: s,\n        data: e\n      };\n      n.emit(b, a), n.emit(l, a);\n    }\n  }\n  /**\n   * 删除api\n   * @param name\n   * @param silent\n   */\n  removeApi(e, t = !1) {\n    const i = this.apis.findIndex((s) => s.name === e || s.id === e);\n    if (i > -1 ? this.apis.splice(i, 1) : console.warn(`not found Api for name: ${e} `), !t) {\n      const s = {\n        model: this,\n        type: \"delete\",\n        data: e\n      };\n      n.emit(b, s), n.emit(l, s);\n    }\n  }\n  existApiName(e, t = []) {\n    return this.apis.some((i) => i.name === e && !t.includes(i.id));\n  }\n  setMeta(e, t = !1) {\n    const i = this.meta.find(\n      (a) => a.code === e.code || a.id === e.id\n    );\n    let s;\n    if (i ? (s = \"update\", Object.assign(i, e)) : (s = \"create\", e.id = m(), this.meta.push(e)), !t) {\n      const a = {\n        model: this,\n        type: s,\n        data: e\n      };\n      n.emit(w, a), n.emit(l, a);\n    }\n  }\n  removeMeta(e, t = !1) {\n    const i = this.meta.findIndex((s) => s.code === e || s.id === e);\n    if (i > -1 ? this.meta.splice(i, 1) : console.warn(`not found meta for name: ${name} `), !t) {\n      const s = {\n        model: this,\n        type: \"delete\",\n        data: e\n      };\n      n.emit(w, s), n.emit(l, s);\n    }\n  }\n  existMetaCode(e, t = []) {\n    return this.meta.some((i) => i.code === e && !t.includes(i.id));\n  }\n  setHomepage(e, t = !1) {\n    if (this.homepage = e, !t) {\n      const i = {\n        model: this,\n        type: \"update\",\n        data: e\n      };\n      n.emit(l, i);\n    }\n  }\n  setConfig(e, t = !1) {\n    if (this.config = Object.assign(this.config, e), !t) {\n      const i = {\n        model: this,\n        type: \"update\",\n        data: e\n      };\n      n.emit(l, i);\n    }\n  }\n  setUniConfig(e, t, i = !1) {\n    if (this.uniConfig = Object.assign(this.uniConfig, { [e]: t }), !i) {\n      const s = {\n        model: this,\n        type: \"update\",\n        data: this.uniConfig\n      };\n      n.emit(l, s);\n    }\n  }\n  publish(e) {\n    const t = {\n      model: this,\n      type: \"publish\",\n      data: e || this\n    };\n    e ? n.emit($, t) : n.emit(j, t);\n  }\n  genSource() {\n    const e = {\n      model: this,\n      type: \"gen\",\n      data: null\n    };\n    n.emit(M, e);\n  }\n  lock(e) {\n    this.locked = e;\n    const t = {\n      model: this,\n      type: \"update\",\n      data: e\n    };\n    n.emit(l, t);\n  }\n  unlock(e) {\n    if (e !== this.locked)\n      return;\n    this.locked = \"\";\n    const t = {\n      model: this,\n      type: \"update\",\n      data: null\n    };\n    n.emit(l, t);\n  }\n}\nconst N = \"EVENT_HISTORY_CHANGE\", V = \"EVENT_HISTORY_LOAD\";\nclass te {\n  options = { max: 50 };\n  index = -1;\n  id;\n  items;\n  constructor(e, t = {}) {\n    Object.assign(this.options, t);\n    const { id: i, items: s = [] } = e;\n    this.id = i, this.items = s;\n  }\n  toDsl() {\n    const { id: e, items: t } = this;\n    return {\n      id: e,\n      items: t.map((i) => ({ id: i.id, label: i.label }))\n    };\n  }\n  /**\n   * 获取历史项\n   * @param id\n   * @returns\n   */\n  get(e) {\n    return this.items.find((t) => t.id === e);\n  }\n  /**\n   * 增加历史记录\n   * @param dsl\n   * @param silent\n   */\n  add(e, t = !1) {\n    const { max: i } = this.options, s = {\n      id: m(),\n      label: (/* @__PURE__ */ new Date()).toLocaleString(),\n      dsl: C(e)\n    };\n    if (this.items.unshift(s), this.items.length > i) {\n      const a = this.items.splice(i);\n      t || n.emit(N, {\n        model: this,\n        type: \"delete\",\n        data: a.map((o) => o.id)\n      });\n    }\n    this.index = -1, t || n.emit(N, {\n      model: this,\n      type: \"create\",\n      data: s\n    });\n  }\n  /**\n   * 删除历史记录\n   * @param id\n   * @param silent\n   */\n  remove(e, t = !1) {\n    const i = this.items.findIndex((s) => s.id === e);\n    i > -1 ? (this.items.splice(i, 1), i === this.index ? this.index = -1 : this.index >= this.items.length && (this.index = this.items.length - 1)) : console.warn(`not found HistoryItem for id: ${e} `), t || n.emit(N, {\n      model: this,\n      type: \"delete\",\n      data: [e]\n    });\n  }\n  forward(e = !1) {\n    const { index: t, items: i } = this;\n    if (t < 0) return;\n    --this.index;\n    const s = i[this.index];\n    s && !e && n.emit(V, {\n      model: this,\n      type: \"load\",\n      data: s\n    });\n  }\n  backward(e = !1) {\n    const { index: t, items: i } = this;\n    if (t >= i.length - 1) return;\n    t < 0 && (this.index = 0), ++this.index;\n    const s = i[this.index];\n    s && !e && n.emit(V, {\n      model: this,\n      type: \"load\",\n      data: s\n    });\n  }\n  load(e, t = !1) {\n    const i = this.items.findIndex((s) => s.id === e);\n    i >= 0 && (this.index = i, t || n.emit(V, {\n      model: this,\n      type: \"load\",\n      data: this.items[i]\n    }));\n  }\n  clear(e = !1) {\n    this.index = -1;\n    const t = this.items.map((i) => i.id);\n    this.items = [], e || n.emit(N, {\n      model: this,\n      type: \"clear\",\n      data: t\n    });\n  }\n}\nconst n = F();\nfunction ie(f) {\n  return f instanceof u;\n}\nfunction se(f) {\n  return f instanceof _;\n}\nfunction ne(f) {\n  return !!f.__VTJ_BLOCK__;\n}\nfunction L(f) {\n  const e = C(f);\n  return delete e.id, Array.isArray(e.children) && (e.children = e.children.map((t) => L(t))), e;\n}\nfunction ae(f) {\n  const e = f.fromType || \"Schema\";\n  return e === \"Schema\" ? {\n    type: \"Schema\",\n    id: f.id\n  } : e === \"UrlSchema\" ? {\n    type: \"UrlSchema\",\n    url: (f.urls || \"\").split(\",\")[0] || \"\"\n  } : e === \"Plugin\" ? {\n    type: \"Plugin\",\n    urls: (f.urls || \"\").split(\",\"),\n    library: f.library\n  } : \"\";\n}\nexport {\n  Q as BUILT_IN_COMPONENTS,\n  z as BUILT_IN_LIBRARAY_MAP,\n  q as BUILT_IN_MATERIALS,\n  W as BUILT_IN_NAME,\n  X as BUILT_IN_TAGS,\n  H as BUILT_IN_VUE,\n  J as BUILT_IN_VUE_ROUTER,\n  ee as Base,\n  u as BlockModel,\n  v as DirectiveModel,\n  r as EVENT_BLOCK_CHANGE,\n  N as EVENT_HISTORY_CHANGE,\n  V as EVENT_HISTORY_LOAD,\n  h as EVENT_NODE_CHANGE,\n  D as EVENT_PROJECT_ACTIVED,\n  b as EVENT_PROJECT_APIS_CHANGE,\n  y as EVENT_PROJECT_BLOCKS_CHANGE,\n  l as EVENT_PROJECT_CHANGE,\n  B as EVENT_PROJECT_DEPS_CHANGE,\n  $ as EVENT_PROJECT_FILE_PUBLISH,\n  M as EVENT_PROJECT_GEN_SOURCE,\n  w as EVENT_PROJECT_META_CHANGE,\n  g as EVENT_PROJECT_PAGES_CHANGE,\n  j as EVENT_PROJECT_PUBLISH,\n  T as EventModel,\n  te as HistoryModel,\n  _ as NodeModel,\n  S as ProjectModel,\n  x as PropModel,\n  Z as Service,\n  Y as VTJ_CORE_VERSION,\n  L as cloneDsl,\n  ae as createNodeFrom,\n  n as emitter,\n  ie as isBlock,\n  ne as isBlockSchema,\n  se as isNode\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,IAAM,IAAI;AAAV,IAAqB,IAAI;AAAzB,IAAoC,IAAI;AAAxC,IAAuD,IAAI;AAA3D,IAAgF,IAAI,CAAC,GAAG,CAAC;AAAzF,IAA4F,IAAI;AAAA,EAC9F,KAAK;AAAA,EACL,cAAc;AAChB;AAHA,IAGG,IAAI;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,CAAC,CAAC,GAAG,CAAC,cAAc,YAAY;AAClC;AAZA,IAYG,IAAI;AAAA,EACL;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,IAAN,MAAQ;AACR;AACA,IAAM,KAAN,MAAS;AAAA,EAAT;AACE,qCAAY,CAAC;AACb,mCAAU;AAAA;AAAA,EACV,eAAe;AACb,SAAK,UAAU;AACf,eAAW,KAAK,KAAK;AACnB,QAAE;AACJ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,MAAM,GAAG;AACP,SAAK,UAAU,EAAE,IAAI,KAAK,UAAU,KAAK,CAAC;AAAA,EAC5C;AAAA,EACA,aAAa;AACX,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,IAAN,MAAM,GAAE;AAAA,EACN,YAAY,GAAG,GAAG,GAAG;AAMrB;AAAA;AAAA;AAAA,mCAAU;AALR,SAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK,eAAe,GAAG,KAAK,SAAS,CAAC;AAAA,EACvE;AAAA,EAKA,SAAS,GAAG;AACV,SAAK,QAAQ,GAAG,KAAK,UAAU,KAAK,UAAU,KAAK;AAAA,EACrD;AAAA,EACA,WAAW;AACT,WAAO,KAAK,SAAS,KAAK;AAAA,EAC5B;AAAA,EACA,OAAO,MAAM,IAAI,CAAC,GAAG;AACnB,WAAO,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC,IAAI,EAAE,SAAS,IAAI,IAAI,CAAC,CAAC;AAAA,EAC5F;AAAA,EACA,OAAO,MAAM,IAAI,CAAC,GAAG;AACnB,WAAO,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,IAAI,GAAE,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,EAC5E;AACF;AACA,IAAM,IAAN,MAAM,GAAE;AAAA,EACN,YAAY,GAAG;AAKf;AACA;AACA,qCAAY,CAAC;AANX,SAAK,SAAS;AACd,UAAM,EAAE,MAAM,GAAG,SAAS,EAAE,IAAI,KAAK;AACrC,SAAK,OAAO,GAAG,KAAK,UAAU,GAAG,KAAK,OAAO,CAAC;AAAA,EAChD;AAAA,EAIA,OAAO,GAAG;AACR,WAAO,OAAO,KAAK,QAAQ,CAAC;AAC5B,UAAM,EAAE,SAAS,GAAG,WAAW,IAAI,CAAC,EAAE,IAAI,KAAK;AAC/C,SAAK,UAAU,GAAG,KAAK,YAAY;AAAA,EACrC;AAAA,EACA,OAAO,MAAM,GAAG;AACd,WAAO,OAAO,QAAQ,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM;AAC7C,YAAM,EAAE,SAAS,GAAG,WAAW,EAAE,IAAI;AACrC,aAAO,EAAE,CAAC,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,WAAW,EAAE,GAAG;AAAA,IACvD,GAAG,CAAC,CAAC;AAAA,EACP;AAAA,EACA,OAAO,MAAM,IAAI,CAAC,GAAG;AACnB,WAAO,OAAO,QAAQ,CAAC,EAAE;AAAA,MACvB,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,IAAI,GAAE,CAAC,GAAG;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,IAAM,IAAN,MAAM,GAAE;AAAA,EACN,YAAY,GAAG;AAMf;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA,gCAAO;AAIP;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAzBE,SAAK,SAAS,GAAG,KAAK,KAAK,EAAE,MAAM,GAAE,GAAG,KAAK,OAAO,CAAC;AAAA,EACvD;AAAA,EAyBA,OAAO,GAAG;AACR,WAAO,OAAO,KAAK,QAAQ,CAAC;AAC5B,UAAM,EAAE,MAAM,GAAG,KAAK,GAAG,WAAW,GAAG,OAAO,GAAG,UAAU,EAAE,IAAI,KAAK;AACtE,SAAK,OAAO,GAAG,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,KAAK,QAAQ,GAAG,KAAK,WAAW;AAAA,EACnF;AAAA,EACA,OAAO,MAAM,IAAI,CAAC,GAAG;AACnB,WAAO,EAAE,IAAI,CAAC,MAAM,IAAI,GAAE,CAAC,CAAC;AAAA,EAC9B;AAAA,EACA,OAAO,MAAM,IAAI,CAAC,GAAG;AACnB,WAAO,EAAE,IAAI,CAAC,MAAM;AAClB,YAAM,EAAE,MAAM,GAAG,KAAK,GAAG,WAAW,GAAG,OAAO,GAAG,UAAU,GAAG,IAAI,EAAE,IAAI;AACxE,aAAO;AAAA,QACL,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,KAAK;AAAA,QACL,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,IAAM,IAAI;AACV,IAAM,KAAN,MAAM,GAAE;AAAA,EACN,YAAY,GAAG,IAAI,MAAM;AAQzB;AAAA;AAAA;AAAA,wCAAe;AAIf;AAAA;AAAA;AAAA,kCAAS;AAQT;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA,qCAAY;AAIZ;AAAA;AAAA;AAAA,oCAAW;AAIX;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAAA,iCAAQ,CAAC;AAIT;AAAA;AAAA;AAAA,kCAAS,CAAC;AAIV;AAAA;AAAA;AAAA,sCAAa,CAAC;AAId;AAAA;AAAA;AAAA,oCAAW;AAvDT,SAAK,SAAS;AACd,UAAM,EAAE,IAAI,IAAI,GAAE,GAAG,MAAM,GAAG,MAAM,IAAI,GAAG,IAAI;AAC/C,SAAK,KAAK,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,IAAE,GAAG,GAAE,MAAM,KAAK,EAAE,IAAI;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0DA,OAAO,GAAG,IAAI,OAAI;AAChB,UAAM;AAAA,MACJ,WAAW,IAAI;AAAA,MACf,QAAQ,IAAI;AAAA,MACZ,UAAU,IAAI,CAAC;AAAA,MACf,MAAM;AAAA,MACN,OAAO,IAAI,CAAC;AAAA,MACZ,QAAQ,IAAI,CAAC;AAAA,MACb,YAAY,IAAI,CAAC;AAAA,IACnB,IAAI;AACJ,SAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,YAAY,GAAG,IAAE,GAAG,KAAK,QAAQ,GAAG,IAAE,GAAG,KAAK,QAAQ,EAAE,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,MAAM,CAAC,GAAG,KAAK,aAAa,EAAE,MAAM,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,IAAI,IAAI,IAAI,OAAI;AAC1B,UAAM,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE,IAAI,CAAC,MAAM,IAAI,GAAE,GAAG,IAAI,CAAC,IAAI,KAAK,WAAW,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC1G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG,IAAI,OAAI;AACjB,SAAK,OAAO,OAAO,KAAK,WAAW,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,GAAG,GAAG,GAAG,IAAI,OAAI;AACvB,UAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAI,EAAE,SAAS,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,WAAO,KAAK,MAAM,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,GAAG;AACd,UAAM,IAAI,KAAK,MAAM,CAAC;AACtB,QAAI;AACF,aAAO,EAAE,SAAS;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG,IAAI,OAAI;AAClB,UAAM,IAAI,KAAK,OAAO,EAAE,IAAI;AAC5B,QAAI,EAAE,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG,IAAI,OAAI;AACrB,WAAO,KAAK,OAAO,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,GAAG,IAAI,OAAI;AACtB,UAAM,IAAI,aAAa,IAAI,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,KAAK,WAAW,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;AAC3F,SAAK,IAAI,KAAK,WAAW,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,WAAW,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,GAAG,IAAI,OAAI;AACzB,UAAM,IAAI,KAAK,WAAW;AAAA,MACxB,CAAC,MAAM,MAAM,KAAK,EAAE,OAAO,EAAE;AAAA,IAC/B;AACA,SAAK,KAAK,KAAK,WAAW,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,GAAG,IAAI,OAAI;AACrB,UAAM,EAAE,UAAU,GAAG,UAAU,EAAE,IAAI;AACrC,QAAI,KAAK,CAAC,MAAM,QAAQ,CAAC,EAAG;AAC5B,UAAM,IAAI,EAAE,UAAU,CAAC,MAAM,MAAM,CAAC;AACpC,MAAE,SAAS,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,GAAG,IAAI,OAAI;AACrB,UAAM,EAAE,UAAU,GAAG,UAAU,EAAE,IAAI;AACrC,UAAM,EAAE,SAAS,MAAM,MAAM,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,GAAG,IAAI,OAAI;AACrB,QAAI,CAAC,KAAK,OAAQ;AAClB,UAAM,IAAI,KAAK,OAAO;AACtB,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,QAAE,SAAS,KAAK;AAChB,YAAM,IAAI,EAAE,QAAQ,IAAI;AACxB,QAAE,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,KAAK,MAAM;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,GAAG,IAAI,OAAI;AACtB,QAAI,CAAC,KAAK,OAAQ;AAClB,UAAM,IAAI,KAAK,OAAO;AACtB,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,QAAE,SAAS,KAAK;AAChB,YAAM,IAAI,EAAE,QAAQ,IAAI;AACxB,QAAE,OAAO,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,KAAK,MAAM;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,SAAS,IAAI,OAAI;AACf,UAAM,IAAI,KAAK;AACf,QAAI,CAAC,EAAG;AACR,UAAM,IAAI,EAAE;AACZ,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,YAAM,IAAI,EAAE,QAAQ,IAAI;AACxB,UAAI,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,IACtE;AAAA,EACF;AAAA,EACA,SAAS,IAAI,OAAI;AACf,UAAM,IAAI,KAAK;AACf,QAAI,CAAC,EAAG;AACR,UAAM,IAAI,EAAE;AACZ,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,YAAM,IAAI,EAAE,QAAQ,IAAI;AACxB,UAAI,MAAM,IAAI,EAAE,SAAS,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,UAAM;AAAA,MACJ,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,IAAI,MAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI;AAC3D,WAAO;AAAA,MACL,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,MAAM;AAAA,MACN,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,UAAU;AAAA,MACV,OAAO,EAAE,MAAM,CAAC;AAAA,MAChB,YAAY,EAAE,MAAM,CAAC;AAAA,MACrB,QAAQ,EAAE,MAAM,CAAC;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,IAAI,OAAI;AACd,UAAM,EAAE,UAAU,GAAG,UAAU,EAAE,IAAI;AACrC,UAAM,MAAM,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,IAAE,CAAC,GAAG,KAAK,SAAS,KAAK,OAAO,YAAY,MAAM,CAAC,IAAI,KAAK,EAAE,KAAK,GAAG,IAAI,GAAG,KAAK,SAAS,MAAM,KAAK,WAAW,MAAI,OAAO,GAAE,MAAM,KAAK,EAAE;AAAA,EAClM;AAAA,EACA,KAAK,IAAI,OAAI;AACX,QAAI,KAAK,SAAS,MAAI,MAAM,QAAQ,KAAK,QAAQ;AAC/C,iBAAW,KAAK,KAAK;AACnB,UAAE,KAAK,IAAE;AACb,SAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA,EACA,OAAO,IAAI,OAAI;AACb,QAAI,KAAK,SAAS,OAAI,MAAM,QAAQ,KAAK,QAAQ;AAC/C,iBAAW,KAAK,KAAK;AACnB,UAAE,OAAO,IAAE;AACf,SAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA,EACA,WAAW,GAAG,IAAI,OAAI;AACpB,QAAI,KAAK,YAAY,CAAC,GAAG,MAAM,QAAQ,KAAK,QAAQ;AAClD,iBAAW,KAAK,KAAK;AACnB,UAAE,WAAW,GAAG,IAAE;AACtB,SAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA,EACA,QAAQ,GAAG;AACT,QAAI,IAAI;AACR,QAAI,MAAM,QAAQ,KAAK,QAAQ,GAAG;AAChC,iBAAW,KAAK,KAAK;AACnB,YAAI,MAAM,KAAK,EAAE,OAAO,EAAE,IAAI;AAC5B,cAAI;AACJ;AAAA,QACF,WAAW,IAAI,EAAE,QAAQ,CAAC,GAAG;AAC3B;AAAA,IACN;AACA,WAAO;AAAA,EACT;AACF;AAAA;AAAA;AAAA;AAvRE,cAjBI,IAiBG,SAAQ,CAAC;AAjBlB,IAAM,IAAN;AAySA,IAAM,IAAI;AACV,IAAM,KAAN,MAAM,GAAE;AAAA,EAkCN,YAAY,GAAG;AAjCf,yCAAgB;AAChB;AACA,gCAAO;AACP,kCAAS,CAAC;AACV,iCAAQ,CAAC;AACT,sCAAa,CAAC;AACd,mCAAU,CAAC;AACX,oCAAW,CAAC;AACZ,iCAAQ,CAAC;AACT,+BAAM;AACN,iCAAQ,CAAC;AACT,iCAAQ,CAAC;AACT,iCAAQ,CAAC;AACT,uCAAc,CAAC;AACf,iCAAQ,CAAC;AACT,kCAAS;AACT,oCAAW;AAkBT,UAAM,EAAE,IAAI,EAAE,IAAI;AAClB,SAAK,KAAK,KAAK,GAAE,GAAG,KAAK,OAAO,GAAG,IAAE;AAAA,EACvC;AAAA,EACA,OAAO,GAAG,IAAI,OAAI;AAChB,eAAW,KAAK,GAAE,aAAa;AAC7B,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,WAAW,KAAK,CAAC,IAAI;AAAA,IAC7B;AACA,UAAM,EAAE,OAAO,IAAI,CAAC,EAAE,IAAI;AAC1B,MAAE,WAAW,KAAK,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,GAAG;AACP,UAAM,EAAE,eAAe,GAAG,IAAI,GAAG,OAAO,EAAE,IAAI;AAC9C,WAAO;AAAA,MACL,GAAG,GAAE,YAAY;AAAA,QACf,CAAC,GAAG,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,GAAG;AAAA,QAC3B,CAAC;AAAA,MACH;AAAA,MACA,eAAe;AAAA,MACf,aAAa,KAAK,GAAE,EAAE,SAAS;AAAA,MAC/B,IAAI;AAAA,MACJ,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACR,SAAK,MAAM,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,KAAK,WAAW;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,GAAG,GAAG,GAAG,IAAI,OAAI;AAC3B,SAAK,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,GAAG,GAAG,IAAI,OAAI;AAC3B,WAAO,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,GAAG,GAAG,IAAI,OAAI;AACrB,SAAK,MAAM,CAAC,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG,IAAI,OAAI;AACrB,WAAO,KAAK,MAAM,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG,IAAI,OAAI;AAChB,SAAK,MAAM,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG,IAAI,OAAI;AAClB,MAAE,KAAK,EAAE,MAAM,GAAE;AACjB,UAAM,IAAI,KAAK,MAAM;AAAA,MACnB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,MAAM;AAAA,IACxC;AACA,QAAI,KAAK,KAAK,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG,IAAI,OAAI;AACrB,UAAM,IAAI,KAAK,MAAM;AAAA,MACnB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,MAAM;AAAA,IACxC;AACA,QAAI,OAAO,KAAK,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG,IAAI,OAAI;AACjB,UAAM,IAAI,KAAK,MAAM;AAAA,MACnB,CAAC,MAAM,OAAO,KAAK,WAAW,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE;AAAA,IAC5D;AACA,QAAI,KAAK,KAAK,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,KAAK,MAAM;AAAA,MACnB,CAAC,MAAM,OAAO,KAAK,WAAW,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE;AAAA,IAC5D;AACA,QAAI,OAAO,KAAK,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG,IAAI,OAAI;AACjB,UAAM,IAAI,GAAE,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,MAAM,UAAU,CAAC,MAAM,MAAM,EAAE,QAAQ,MAAM,CAAC;AACrG,QAAI,KAAK,KAAK,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,KAAK,MAAM;AAAA,MACnB,CAAC,MAAM,GAAE,CAAC,IAAI,MAAM,IAAI,EAAE,SAAS;AAAA,IACrC;AACA,QAAI,OAAO,KAAK,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG,IAAI,OAAI;AACjB,UAAM,IAAI,GAAE,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,MAAM,UAAU,CAAC,MAAM,MAAM,EAAE,QAAQ,MAAM,CAAC;AACrG,QAAI,KAAK,KAAK,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,KAAK,MAAM;AAAA,MACnB,CAAC,MAAM,GAAE,CAAC,IAAI,MAAM,IAAI,EAAE,SAAS;AAAA,IACrC;AACA,QAAI,OAAO,KAAK,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG,IAAI,OAAI;AACnB,UAAM,IAAI,KAAK,OAAO,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI;AACxD,QAAI,KAAK,KAAK,OAAO,OAAO,GAAG,GAAG,CAAC,IAAI,KAAK,OAAO,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,GAAG,IAAI,OAAI;AACtB,UAAM,IAAI,KAAK,OAAO,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI;AACxD,QAAI,OAAO,KAAK,OAAO,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,GAAG,IAAI,OAAI;AACvB,SAAK,YAAY,EAAE,IAAI,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,GAAG,IAAI,OAAI;AAC1B,WAAO,KAAK,YAAY,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACjD;AAAA,EACA,YAAY,GAAG,GAAG,IAAI,OAAI;AACxB,MAAE,SAAS;AACX,UAAM,IAAI,KAAK,MAAM,QAAQ,CAAC;AAC9B,SAAK,MAAM,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrD;AAAA,EACA,aAAa,GAAG,GAAG,IAAI,OAAI;AACzB,MAAE,SAAS;AACX,UAAM,IAAI,KAAK,MAAM,QAAQ,CAAC;AAC9B,SAAK,MAAM,OAAO,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACjD;AAAA,EACA,WAAW,GAAG,IAAI,OAAI;AACpB,MAAE,SAAS,MAAM,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,GAAG,GAAG,IAAI,SAAS,IAAI,OAAI;AACjC,QAAI,CAAC,QAAQ,KAAK,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,YAAY,GAAG,CAAC,IAAI,KAAK,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,YAAY,GAAG,CAAC,IAAI,KAAK,YAAY,GAAG,GAAG,CAAC,IAAI,EAAE,YAAY,GAAG,CAAC,IAAI,KAAK,WAAW,GAAG,CAAC;AAAA,EAC3O;AAAA,EACA,aAAa,GAAG,IAAI,OAAI;AACtB,UAAM,IAAI,KAAK,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;AACnD,QAAI,OAAO,KAAK,MAAM,OAAO,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,MAAE,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,QAAQ,IAAE,GAAG,KAAK,aAAa,GAAG,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,GAAG,GAAG,IAAI,SAAS,IAAI,OAAI;AAC9B,MAAE,SAAS,EAAE,OAAO,YAAY,GAAG,IAAE,IAAI,KAAK,aAAa,GAAG,IAAE,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG,IAAI,OAAI;AAClB,QAAI,EAAE;AACJ,QAAE,SAAS,CAAC;AAAA,SACT;AACH,YAAM,IAAI,KAAK,OAAO,IAAI,EAAE,QAAQ,CAAC;AACrC,UAAI,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG,IAAI,OAAI;AAClB,QAAI,EAAE;AACJ,QAAE,SAAS,CAAC;AAAA,SACT;AACH,YAAM,IAAI,KAAK,OAAO,IAAI,EAAE,QAAQ,CAAC;AACrC,UAAI,MAAM,IAAI,EAAE,SAAS,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI;AAAA,IAC3F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,GAAG,IAAI,OAAI;AACnB,UAAM,IAAI,EAAE,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;AACnC,WAAO,KAAK,QAAQ,GAAG,GAAG,UAAU,CAAC,GAAG;AAAA,EAC1C;AAAA,EACA,KAAK,IAAI,OAAI;AACX,SAAK,SAAS;AACd,eAAW,KAAK,KAAK;AACnB,QAAE,KAAK,IAAE;AACX,SAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA,EACA,OAAO,IAAI,OAAI;AACb,SAAK,SAAS;AACd,eAAW,KAAK,KAAK;AACnB,QAAE,OAAO,IAAE;AACb,SAAK,EAAE,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA,EACA,QAAQ,GAAG;AACT,QAAI,IAAI;AACR,eAAW,KAAK,KAAK;AACnB,UAAI,MAAM,KAAK,EAAE,OAAO,EAAE,IAAI;AAC5B,YAAI;AACJ;AAAA,MACF,WAAW,IAAI,EAAE,QAAQ,CAAC,GAAG;AAC3B;AACJ,WAAO;AAAA,EACT;AACF;AA7TE,cAlBI,IAkBG,eAAc;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAjCF,IAAM,IAAN;AAgVA,IAAM,IAAI;AAAV,IAAkC,IAAI;AAAtC,IAA+D,IAAI;AAAnE,IAAgG,IAAI;AAApG,IAAkI,IAAI;AAAtI,IAAqK,IAAI;AAAzK,IAAsM,IAAI;AAA1M,IAAuO,IAAI;AAA3O,IAAoQ,IAAI;AAAxQ,IAAsS,IAAI;AAC1S,IAAM,KAAN,MAAM,GAAE;AAAA,EAiCN,YAAY,GAAG;AAhCf,8BAAK;AACL,kCAAS;AACT,oCAAW;AACX,gCAAO;AACP,uCAAc;AACd,oCAAW;AACX,wCAAe,CAAC;AAChB,iCAAQ,CAAC;AACT,kCAAS,CAAC;AACV,gCAAO,CAAC;AACR,gCAAO,CAAC;AACR,uCAAc;AACd,kCAAS,CAAC;AACV,qCAAY,CAAC;AACb,yCAAgB;AAChB,mCAAU,GAAE,IAAE;AAkBZ,UAAM,EAAE,IAAI,GAAG,SAAS,EAAE,IAAI;AAC9B,SAAK,KAAK,KAAK,GAAE,GAAG,KAAK,UAAU,KAAK,GAAE,IAAE,GAAG,KAAK,OAAO,GAAG,IAAE;AAAA,EAClE;AAAA,EACA,OAAO,GAAG,IAAI,OAAI;AAChB,eAAW,KAAK,GAAE,OAAO;AACvB,YAAM,IAAI,EAAE,CAAC;AACb,YAAM,KAAK,CAAC,IAAI;AAAA,IAClB;AACA,SAAK,EAAE,KAAK,GAAG;AAAA,MACb,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,WAAW,GAAG;AACZ,WAAO,EAAE,SAAS;AAAA,EACpB;AAAA,EACA,cAAc,GAAG;AACf,eAAW,KAAK;AACd,aAAO,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,UAAU,KAAK,cAAc,EAAE,QAAQ;AAAA,EAClF;AAAA,EACA,MAAM,GAAG;AACP,UAAM,EAAE,IAAI,EAAE,IAAI,MAAM,IAAI,GAAE,MAAM;AAAA,MAClC,CAAC,GAAG,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,GAAG;AAAA,MAC3B,CAAC;AAAA,IACH;AACA,WAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,GAAE;AAAA,MAChD,GAAG;AAAA,MACH,KAAK;AAAA,IACP,CAAC,CAAC,GAAG,KAAK,cAAc,EAAE,KAAK,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,IAAI,CAAC,MAAM,GAAE;AAAA,MAC/E,GAAG;AAAA,MACH,KAAK;AAAA,IACP,CAAC,CAAC,IAAI;AAAA,MACJ,iBAAiB;AAAA,MACjB,IAAI;AAAA,MACJ,GAAG;AAAA,IACL;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG,IAAI,OAAI;AAChB,SAAK,cAAc,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,MACnC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,IAAI,OAAI;AACjB,SAAK,cAAc,MAAM,KAAK,EAAE,KAAK,GAAG;AAAA,MACtC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG,IAAI,OAAI;AACjB,UAAM,IAAI,KAAK,cAAc,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO;AAC3E,QAAI;AACJ,QAAI,IAAI,MAAM,IAAI,UAAU,EAAE,OAAO,GAAG,GAAG;AAAA,MACzC,GAAG,EAAE,CAAC;AAAA,MACN,GAAG;AAAA,IACL,CAAC,MAAM,IAAI,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG;AACnC,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,KAAK,cAAc,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO;AAC3E,QAAI,IAAI,MAAM,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG;AAChC,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,GAAG;AACT,UAAM,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM;AACvB,iBAAW,KAAK,GAAG;AACjB,YAAI,EAAE,OAAO;AACX,iBAAO;AACT,YAAI,EAAE,YAAY,EAAE,SAAS,QAAQ;AACnC,gBAAM,IAAI,EAAE,GAAG,EAAE,QAAQ;AACzB,cAAI;AACF,mBAAO;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACT,UAAM,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,UAAI,IAAI,CAAC;AACT,iBAAW,KAAK;AACd,UAAE,MAAM,EAAE,YAAY,EAAE,SAAS,WAAW,IAAI,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;AACrF,aAAO;AAAA,IACT;AACA,WAAO,EAAE,KAAK,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,GAAG,GAAG,IAAI,OAAI;AAC7B,QAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAE,GAAG,EAAE,OAAO,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,IAAI,EAAE;AAAA,MACzG,IAAI,EAAE;AAAA,MACN,MAAM,GAAE,EAAE,IAAI;AAAA,IAChB,CAAC,EAAE,MAAM,GAAG,GAAG;AACb,YAAM,IAAI,KAAK,QAAQ,CAAC;AACxB,UAAI,EAAE,WAAW,EAAE,SAAS,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,IAAI,QAAQ,KAAK,8BAA8B,CAAC,GAAG;AAAA,IAC1G;AACE,WAAK,MAAM,KAAK,CAAC;AACnB,QAAI,CAAC,GAAG;AACN,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AACA,KAAC,KAAK,eAAe,CAAC,EAAE,QAAQ,MAAM,GAAE,GAAG,GAAG,KAAK,OAAO,GAAG,CAAC;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,KAAK,QAAQ,EAAE,EAAE;AAC3B,QAAI,OAAO,EAAE,KAAK,IAAI,OAAO,OAAO,GAAG,CAAC,IAAI,QAAQ,KAAK,8BAA8B,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG;AACnG,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,GAAG,GAAG,IAAI,OAAI;AAn+B1B;AAo+BI,UAAM,IAAI,GAAE,GAAG,IAAI,GAAG,EAAE,IAAI,QAAQ,IAAI,GAAG,EAAE,KAAK,OAAO,IAAI,IAAI,EAAE;AAAA,MACjE,IAAI;AAAA,MACJ,MAAM;AAAA,IACR,CAAC,EAAE,MAAM,GAAG,IAAI,GAAE,CAAC,GAAG,GAAG,EAAE,IAAI,GAAG,MAAM,GAAG,OAAO,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,MAAI,UAAK,QAAQ,CAAC,MAAd,mBAAiB,aAAY,CAAC,IAAI,KAAK,OAAO,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;AAC1J,QAAI,EAAE,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;AAC7B,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,MACF;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,MAAM,YAAY,GAAG,IAAI,OAAI;AAC3B,SAAK,OAAO,GAAG,CAAC,GAAG,MAAM,GAAE,GAAG;AAC9B,UAAM,IAAI,GAAE,GAAG,IAAI,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,IAAI,EAAE;AAAA,MAChD,GAAG,EAAE;AAAA,MACL,IAAI;AAAA,MACJ,MAAM;AAAA,IACR,CAAC,EAAE,MAAM,GAAG,IAAI,GAAE,CAAC,GAAG,GAAG;AAAA,MACvB,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,KAAK;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,KAAK,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG;AAC3B,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,GAAG,IAAI,OAAI;AAhhCxB;AAihCI,UAAM,IAAI,KAAK,QAAQ,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM;AACvC,YAAM,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC;AACvC,UAAI,KAAK,GAAG;AACV,UAAE,OAAO,GAAG,CAAC;AACb;AAAA,MACF;AACA,iBAAW,KAAK;AACd,UAAE,YAAY,EAAE,SAAS,UAAU,EAAE,GAAG,EAAE,QAAQ;AAAA,IACtD;AACA,QAAI,EAAE,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK,aAAa,KAAK,WAAW,OAAK,UAAK,gBAAL,mBAAkB,QAAO,KAAK,KAAK,WAAW,CAAC,GAAG,CAAC,GAAG;AACvH,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,GAAG;AACV,WAAO,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY,GAAG,IAAI,OAAI;AAC3B,UAAM,IAAI,EAAE,MAAM,GAAE,GAAG,IAAI,GAAE,EAAE,IAAI;AACnC,MAAE,KAAK,GAAG,EAAE,OAAO,SAAS,EAAE,MAAM,IAAI,EAAE,EAAE,IAAI,GAAG,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC;AACzF,UAAM,IAAI,EAAE,YAAY;AACxB,QAAI,CAAC,GAAG;AACN,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AACA,KAAC,KAAK,eAAe,MAAM,aAAa,MAAM,GAAE,GAAG,GAAG,KAAK,OAAO,GAAG,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG,IAAI,OAAI;AACrB,UAAM,IAAI,KAAK,SAAS,EAAE,EAAE;AAC5B,QAAI,KAAK,OAAO,OAAO,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,OAAO,EAAE,SAAS,QAAQ,KAAK,8BAA8B,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG;AACvH,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,GAAE,GAAG,IAAI,GAAG,EAAE,IAAI,QAAQ,IAAI,GAAG,EAAE,KAAK,OAAO,IAAI,IAAI,EAAE;AAAA,MACjE,IAAI;AAAA,MACJ,MAAM;AAAA,IACR,CAAC,EAAE,MAAM,GAAG,IAAI,GAAE,CAAC,GAAG,GAAG,EAAE,IAAI,GAAG,MAAM,GAAG,OAAO,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,KAAK,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;AAC9G,QAAI,KAAK,OAAO,OAAO,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;AACvC,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,QAAQ;AAAA,UACR,QAAQ;AAAA,QACV;AAAA,MACF;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG,IAAI,OAAI;AApmCzB;AAqmCI,UAAM,IAAI,KAAK,SAAS,CAAC,GAAG,IAAI,KAAK,QAAQ,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC;AAC9E,QAAI,IAAI,MAAM,EAAE,OAAO,GAAG,CAAC,KAAG,UAAK,gBAAL,mBAAkB,QAAO,KAAK,KAAK,WAAW,CAAC,KAAK,QAAQ,KAAK,8BAA8B,CAAC,GAAG,GAAG,CAAC,GAAG;AACtI,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,GAAG,IAAI,CAAC,GAAG;AACxB,WAAO,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,GAAG,IAAI,CAAC,GAAG;AACvB,WAAO,KAAK,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG,IAAI,OAAI;AAChB,UAAM,IAAI,KAAK,KAAK;AAAA,MAClB,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE;AAAA,IACzC;AACA,QAAI;AACJ,QAAI,KAAK,IAAI,UAAU,OAAO,OAAO,GAAG,CAAC,MAAM,IAAI,UAAU,EAAE,KAAK,GAAE,GAAG,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG;AAC/F,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG,IAAI,OAAI;AACnB,UAAM,IAAI,KAAK,KAAK,UAAU,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,OAAO,CAAC;AAC/D,QAAI,IAAI,KAAK,KAAK,KAAK,OAAO,GAAG,CAAC,IAAI,QAAQ,KAAK,2BAA2B,CAAC,GAAG,GAAG,CAAC,GAAG;AACvF,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,aAAa,GAAG,IAAI,CAAC,GAAG;AACtB,WAAO,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;AAAA,EAChE;AAAA,EACA,QAAQ,GAAG,IAAI,OAAI;AACjB,UAAM,IAAI,KAAK,KAAK;AAAA,MAClB,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE;AAAA,IACzC;AACA,QAAI;AACJ,QAAI,KAAK,IAAI,UAAU,OAAO,OAAO,GAAG,CAAC,MAAM,IAAI,UAAU,EAAE,KAAK,GAAE,GAAG,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG;AAC/F,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,WAAW,GAAG,IAAI,OAAI;AACpB,UAAM,IAAI,KAAK,KAAK,UAAU,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,OAAO,CAAC;AAC/D,QAAI,IAAI,KAAK,KAAK,KAAK,OAAO,GAAG,CAAC,IAAI,QAAQ,KAAK,4BAA4B,IAAI,GAAG,GAAG,CAAC,GAAG;AAC3F,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,cAAc,GAAG,IAAI,CAAC,GAAG;AACvB,WAAO,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;AAAA,EAChE;AAAA,EACA,YAAY,GAAG,IAAI,OAAI;AACrB,QAAI,KAAK,WAAW,GAAG,CAAC,GAAG;AACzB,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,UAAU,GAAG,IAAI,OAAI;AACnB,QAAI,KAAK,SAAS,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG;AACnD,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AACA,QAAE,KAAK,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,aAAa,GAAG,GAAG,IAAI,OAAI;AACzB,QAAI,KAAK,YAAY,OAAO,OAAO,KAAK,WAAW,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG;AAClE,YAAM,IAAI;AAAA,QACR,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb;AACA,QAAE,KAAK,GAAG,CAAC;AAAA,IACb;AAAA,EACF;AAAA,EACA,QAAQ,GAAG;AACT,UAAM,IAAI;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,IACb;AACA,QAAI,EAAE,KAAK,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC;AAAA,EAChC;AAAA,EACA,YAAY;AACV,UAAM,IAAI;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,MAAE,KAAK,GAAG,CAAC;AAAA,EACb;AAAA,EACA,KAAK,GAAG;AACN,SAAK,SAAS;AACd,UAAM,IAAI;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,MAAE,KAAK,GAAG,CAAC;AAAA,EACb;AAAA,EACA,OAAO,GAAG;AACR,QAAI,MAAM,KAAK;AACb;AACF,SAAK,SAAS;AACd,UAAM,IAAI;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR;AACA,MAAE,KAAK,GAAG,CAAC;AAAA,EACb;AACF;AAneE,cAjBI,IAiBG,SAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAhCF,IAAM,IAAN;AAqfA,IAAM,IAAI;AAAV,IAAkC,IAAI;AACtC,IAAM,KAAN,MAAS;AAAA,EAKP,YAAY,GAAG,IAAI,CAAC,GAAG;AAJvB,mCAAU,EAAE,KAAK,GAAG;AACpB,iCAAQ;AACR;AACA;AAEE,WAAO,OAAO,KAAK,SAAS,CAAC;AAC7B,UAAM,EAAE,IAAI,GAAG,OAAO,IAAI,CAAC,EAAE,IAAI;AACjC,SAAK,KAAK,GAAG,KAAK,QAAQ;AAAA,EAC5B;AAAA,EACA,QAAQ;AACN,UAAM,EAAE,IAAI,GAAG,OAAO,EAAE,IAAI;AAC5B,WAAO;AAAA,MACL,IAAI;AAAA,MACJ,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,OAAO,EAAE,MAAM,EAAE;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,GAAG;AACL,WAAO,KAAK,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,GAAG,IAAI,OAAI;AACb,UAAM,EAAE,KAAK,EAAE,IAAI,KAAK,SAAS,IAAI;AAAA,MACnC,IAAI,GAAE;AAAA,MACN,QAAwB,oBAAI,KAAK,GAAG,eAAe;AAAA,MACnD,KAAK,GAAE,CAAC;AAAA,IACV;AACA,QAAI,KAAK,MAAM,QAAQ,CAAC,GAAG,KAAK,MAAM,SAAS,GAAG;AAChD,YAAM,IAAI,KAAK,MAAM,OAAO,CAAC;AAC7B,WAAK,EAAE,KAAK,GAAG;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE;AAAA,MACzB,CAAC;AAAA,IACH;AACA,SAAK,QAAQ,IAAI,KAAK,EAAE,KAAK,GAAG;AAAA,MAC9B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,GAAG,IAAI,OAAI;AAChB,UAAM,IAAI,KAAK,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC;AAChD,QAAI,MAAM,KAAK,MAAM,OAAO,GAAG,CAAC,GAAG,MAAM,KAAK,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,KAAK,MAAM,WAAW,KAAK,QAAQ,KAAK,MAAM,SAAS,MAAM,QAAQ,KAAK,iCAAiC,CAAC,GAAG,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,MACrN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,CAAC;AAAA,IACV,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,IAAI,OAAI;AACd,UAAM,EAAE,OAAO,GAAG,OAAO,EAAE,IAAI;AAC/B,QAAI,IAAI,EAAG;AACX,MAAE,KAAK;AACP,UAAM,IAAI,EAAE,KAAK,KAAK;AACtB,SAAK,CAAC,KAAK,EAAE,KAAK,GAAG;AAAA,MACnB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,SAAS,IAAI,OAAI;AACf,UAAM,EAAE,OAAO,GAAG,OAAO,EAAE,IAAI;AAC/B,QAAI,KAAK,EAAE,SAAS,EAAG;AACvB,QAAI,MAAM,KAAK,QAAQ,IAAI,EAAE,KAAK;AAClC,UAAM,IAAI,EAAE,KAAK,KAAK;AACtB,SAAK,CAAC,KAAK,EAAE,KAAK,GAAG;AAAA,MACnB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,KAAK,GAAG,IAAI,OAAI;AACd,UAAM,IAAI,KAAK,MAAM,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC;AAChD,SAAK,MAAM,KAAK,QAAQ,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,MACxC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM,KAAK,MAAM,CAAC;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,MAAM,IAAI,OAAI;AACZ,SAAK,QAAQ;AACb,UAAM,IAAI,KAAK,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE;AACpC,SAAK,QAAQ,CAAC,GAAG,KAAK,EAAE,KAAK,GAAG;AAAA,MAC9B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AACA,IAAM,IAAI,GAAE;AACZ,SAAS,GAAG,GAAG;AACb,SAAO,aAAa;AACtB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,aAAa;AACtB;AACA,SAAS,GAAG,GAAG;AACb,SAAO,CAAC,CAAC,EAAE;AACb;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,GAAE,CAAC;AACb,SAAO,OAAO,EAAE,IAAI,MAAM,QAAQ,EAAE,QAAQ,MAAM,EAAE,WAAW,EAAE,SAAS,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI;AAC/F;AACA,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,EAAE,YAAY;AACxB,SAAO,MAAM,WAAW;AAAA,IACtB,MAAM;AAAA,IACN,IAAI,EAAE;AAAA,EACR,IAAI,MAAM,cAAc;AAAA,IACtB,MAAM;AAAA,IACN,MAAM,EAAE,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK;AAAA,EACvC,IAAI,MAAM,WAAW;AAAA,IACnB,MAAM;AAAA,IACN,OAAO,EAAE,QAAQ,IAAI,MAAM,GAAG;AAAA,IAC9B,SAAS,EAAE;AAAA,EACb,IAAI;AACN;", "names": []}