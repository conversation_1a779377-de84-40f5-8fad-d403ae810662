import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThan } from 'typeorm';
import { ReportDto } from './dto/report.dto';
import { Report } from './entities/report.entity';
import { QueryReportDto } from './dto/query-report.dto';
import { pager, MIN_DATE, MAX_DATE } from '../shared';
import { pick, dayjs } from '@vtj/node';

@Injectable()
export class ReportService {
  constructor(@InjectRepository(Report) private repo: Repository<Report>) {}

  save(dto: ReportDto) {
    const model = new Report();
    Object.assign(model, dto);
    return this.repo.save(model);
  }

  async find(dto: QueryReportDto) {
    const { page, limit, skip, take } = pager(dto);
    const {
      type,
      userId,
      userName,
      ip,
      host,
      engineVersion,
      startTime,
      endTime,
      projectUid
    } = pick(dto, (_k, v) => !!v);
    const createdAt = Between(
      new Date(startTime || MIN_DATE),
      new Date(endTime || MAX_DATE)
    );
    const [list, total] = await this.repo.findAndCount({
      skip,
      take,
      where: {
        type,
        userId,
        userName,
        ip,
        host,
        engineVersion,
        createdAt,
        projectUid
      },
      order: {
        createdAt: 'DESC'
      }
    });

    return {
      page,
      limit,
      total,
      list
    };
  }

  remove(id: string | string[]) {
    return this.repo.delete(id);
  }

  async count(
    start: string,
    end: string,
    field: string,
    types: string[],
    isOfficial?: boolean
  ) {
    const builder = this.repo
      .createQueryBuilder('report')
      .select(`COUNT(DISTINCT report.${field})`, 'count')
      .where('report.created_at > :start and report.created_at < :end', {
        start,
        end
      })
      .andWhere('report.type IN (:...types)', { types });
    if (isOfficial) {
      builder.andWhere('report.host LIKE :host', { host: '%vtj.pro%' });
    }

    const res = await builder.getRawOne();
    return Number(res?.count || 0) as number;
  }

  async onlineTimeAvg(start: string, end: string) {
    // 1. 构建子查询：按 IP 分组，计算每个 IP 的持续时间（小时）
    const subQuery = this.repo
      .createQueryBuilder('report')
      .select('report.ip', 'ip')
      .addSelect(
        'TIMESTAMPDIFF(HOUR, MIN(report.created_at), MAX(report.created_at))',
        'duration_hours'
      )
      .where('report.created_at > :start AND report.created_at < :end', {
        start,
        end
      })
      .groupBy('report.ip');

    // 2. 外层查询：计算子查询结果的平均值
    const mainQuery = this.repo
      .createQueryBuilder()
      .select('AVG(sub.duration_hours)', 'total_hours')
      .from(`(${subQuery.getQuery()})`, 'sub') // 注入子查询
      .setParameters(subQuery.getParameters());

    // 3. 执行并返回结果
    const res = await mainQuery.getRawOne();
    return Number.parseFloat(res?.total_hours || 0) as number;
  }

  async online() {
    const start = dayjs(new Date())
      .subtract(5, 'minute')
      .format('YYYY-MM-DD HH:mm:ss');

    const result = await this.repo
      .createQueryBuilder('report')
      .select('COUNT(DISTINCT report.session_id)', 'total')
      .where('report.created_at > :start', { start })
      .getRawOne();
    return parseInt(result.total, 10);
  }

  async removeBeforeDate(date: Date) {
    this.repo.delete({
      createdAt: LessThan(date)
    });
  }
}
