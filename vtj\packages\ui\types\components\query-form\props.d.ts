import { PropType } from 'vue';
import { QueryFormItems } from './types';
export declare const queryFormProps: {
    collapsible: {
        type: BooleanConstructor;
        default: boolean;
    };
    items: {
        type: PropType<QueryFormItems>;
    };
    inlineColumns: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
    };
};
