(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t(require("vue"),require("xe-utils")):"function"===typeof define&&define.amd?define([,"xe-utils"],t):"object"===typeof exports?exports["VXETable"]=t(require("vue"),require("xe-utils")):e["VXETable"]=t(e["Vue"],e["XEUtils"])})("undefined"!==typeof self?self:this,(function(e,t){return function(){"use strict";var o={9274:function(t){t.exports=e},8871:function(e){e.exports=t},9306:function(e,t,o){var n=o(4901),l=o(6823),r=TypeError;e.exports=function(e){if(n(e))return e;throw new r(l(e)+" is not a function")}},8551:function(e,t,o){var n=o(34),l=String,r=TypeError;e.exports=function(e){if(n(e))return e;throw new r(l(e)+" is not an object")}},9617:function(e,t,o){var n=o(5397),l=o(5610),r=o(6198),a=function(e){return function(t,o,a){var i=n(t),s=r(i);if(0===s)return!e&&-1;var c,u=l(a,s);if(e&&o!==o){while(s>u)if(c=i[u++],c!==c)return!0}else for(;s>u;u++)if((e||u in i)&&i[u]===o)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}},4527:function(e,t,o){var n=o(3724),l=o(4376),r=TypeError,a=Object.getOwnPropertyDescriptor,i=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=i?function(e,t){if(l(e)&&!a(e,"length").writable)throw new r("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},4576:function(e,t,o){var n=o(9504),l=n({}.toString),r=n("".slice);e.exports=function(e){return r(l(e),8,-1)}},6955:function(e,t,o){var n=o(2140),l=o(4901),r=o(4576),a=o(8227),i=a("toStringTag"),s=Object,c="Arguments"===r(function(){return arguments}()),u=function(e,t){try{return e[t]}catch(o){}};e.exports=n?r:function(e){var t,o,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(o=u(t=s(e),i))?o:c?r(t):"Object"===(n=r(t))&&l(t.callee)?"Arguments":n}},7740:function(e,t,o){var n=o(9297),l=o(5031),r=o(7347),a=o(4913);e.exports=function(e,t,o){for(var i=l(t),s=a.f,c=r.f,u=0;u<i.length;u++){var d=i[u];n(e,d)||o&&n(o,d)||s(e,d,c(t,d))}}},6699:function(e,t,o){var n=o(3724),l=o(4913),r=o(6980);e.exports=n?function(e,t,o){return l.f(e,t,r(1,o))}:function(e,t,o){return e[t]=o,e}},6980:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},2106:function(e,t,o){var n=o(283),l=o(4913);e.exports=function(e,t,o){return o.get&&n(o.get,t,{getter:!0}),o.set&&n(o.set,t,{setter:!0}),l.f(e,t,o)}},6840:function(e,t,o){var n=o(4901),l=o(4913),r=o(283),a=o(9433);e.exports=function(e,t,o,i){i||(i={});var s=i.enumerable,c=void 0!==i.name?i.name:t;if(n(o)&&r(o,c,i),i.global)s?e[t]=o:a(t,o);else{try{i.unsafe?e[t]&&(s=!0):delete e[t]}catch(u){}s?e[t]=o:l.f(e,t,{value:o,enumerable:!1,configurable:!i.nonConfigurable,writable:!i.nonWritable})}return e}},9433:function(e,t,o){var n=o(4475),l=Object.defineProperty;e.exports=function(e,t){try{l(n,e,{value:t,configurable:!0,writable:!0})}catch(o){n[e]=t}return t}},3724:function(e,t,o){var n=o(9039);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},4055:function(e,t,o){var n=o(4475),l=o(34),r=n.document,a=l(r)&&l(r.createElement);e.exports=function(e){return a?r.createElement(e):{}}},6837:function(e){var t=TypeError,o=9007199254740991;e.exports=function(e){if(e>o)throw t("Maximum allowed index exceeded");return e}},9392:function(e){e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},7388:function(e,t,o){var n,l,r=o(4475),a=o(9392),i=r.process,s=r.Deno,c=i&&i.versions||s&&s.version,u=c&&c.v8;u&&(n=u.split("."),l=n[0]>0&&n[0]<4?1:+(n[0]+n[1])),!l&&a&&(n=a.match(/Edge\/(\d+)/),(!n||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/),n&&(l=+n[1]))),e.exports=l},8727:function(e){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6518:function(e,t,o){var n=o(4475),l=o(7347).f,r=o(6699),a=o(6840),i=o(9433),s=o(7740),c=o(2796);e.exports=function(e,t){var o,u,d,p,m,f,h=e.target,g=e.global,v=e.stat;if(u=g?n:v?n[h]||i(h,{}):n[h]&&n[h].prototype,u)for(d in t){if(m=t[d],e.dontCallGetSet?(f=l(u,d),p=f&&f.value):p=u[d],o=c(g?d:h+(v?".":"#")+d,e.forced),!o&&void 0!==p){if(typeof m==typeof p)continue;s(m,p)}(e.sham||p&&p.sham)&&r(m,"sham",!0),a(u,d,m,e)}}},9039:function(e){e.exports=function(e){try{return!!e()}catch(t){return!0}}},616:function(e,t,o){var n=o(9039);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},9565:function(e,t,o){var n=o(616),l=Function.prototype.call;e.exports=n?l.bind(l):function(){return l.apply(l,arguments)}},350:function(e,t,o){var n=o(3724),l=o(9297),r=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,i=l(r,"name"),s=i&&"something"===function(){}.name,c=i&&(!n||n&&a(r,"name").configurable);e.exports={EXISTS:i,PROPER:s,CONFIGURABLE:c}},9504:function(e,t,o){var n=o(616),l=Function.prototype,r=l.call,a=n&&l.bind.bind(r,r);e.exports=n?a:function(e){return function(){return r.apply(e,arguments)}}},7751:function(e,t,o){var n=o(4475),l=o(4901),r=function(e){return l(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?r(n[e]):n[e]&&n[e][t]}},5966:function(e,t,o){var n=o(9306),l=o(4117);e.exports=function(e,t){var o=e[t];return l(o)?void 0:n(o)}},4475:function(e,t,o){var n=function(e){return e&&e.Math===Math&&e};e.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof o.g&&o.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},9297:function(e,t,o){var n=o(9504),l=o(8981),r=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return r(l(e),t)}},421:function(e){e.exports={}},5917:function(e,t,o){var n=o(3724),l=o(9039),r=o(4055);e.exports=!n&&!l((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},7055:function(e,t,o){var n=o(9504),l=o(9039),r=o(4576),a=Object,i=n("".split);e.exports=l((function(){return!a("z").propertyIsEnumerable(0)}))?function(e){return"String"===r(e)?i(e,""):a(e)}:a},3706:function(e,t,o){var n=o(9504),l=o(4901),r=o(7629),a=n(Function.toString);l(r.inspectSource)||(r.inspectSource=function(e){return a(e)}),e.exports=r.inspectSource},1181:function(e,t,o){var n,l,r,a=o(8622),i=o(4475),s=o(34),c=o(6699),u=o(9297),d=o(7629),p=o(6119),m=o(421),f="Object already initialized",h=i.TypeError,g=i.WeakMap,v=function(e){return r(e)?l(e):n(e,{})},x=function(e){return function(t){var o;if(!s(t)||(o=l(t)).type!==e)throw new h("Incompatible receiver, "+e+" required");return o}};if(a||d.state){var b=d.state||(d.state=new g);b.get=b.get,b.has=b.has,b.set=b.set,n=function(e,t){if(b.has(e))throw new h(f);return t.facade=e,b.set(e,t),t},l=function(e){return b.get(e)||{}},r=function(e){return b.has(e)}}else{var w=p("state");m[w]=!0,n=function(e,t){if(u(e,w))throw new h(f);return t.facade=e,c(e,w,t),t},l=function(e){return u(e,w)?e[w]:{}},r=function(e){return u(e,w)}}e.exports={set:n,get:l,has:r,enforce:v,getterFor:x}},4376:function(e,t,o){var n=o(4576);e.exports=Array.isArray||function(e){return"Array"===n(e)}},4901:function(e){var t="object"==typeof document&&document.all;e.exports="undefined"==typeof t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},2796:function(e,t,o){var n=o(9039),l=o(4901),r=/#|\.prototype\./,a=function(e,t){var o=s[i(e)];return o===u||o!==c&&(l(t)?n(t):!!t)},i=a.normalize=function(e){return String(e).replace(r,".").toLowerCase()},s=a.data={},c=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},4117:function(e){e.exports=function(e){return null===e||void 0===e}},34:function(e,t,o){var n=o(4901);e.exports=function(e){return"object"==typeof e?null!==e:n(e)}},6395:function(e){e.exports=!1},757:function(e,t,o){var n=o(7751),l=o(4901),r=o(1625),a=o(7040),i=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return l(t)&&r(t.prototype,i(e))}},6198:function(e,t,o){var n=o(8014);e.exports=function(e){return n(e.length)}},283:function(e,t,o){var n=o(9504),l=o(9039),r=o(4901),a=o(9297),i=o(3724),s=o(350).CONFIGURABLE,c=o(3706),u=o(1181),d=u.enforce,p=u.get,m=String,f=Object.defineProperty,h=n("".slice),g=n("".replace),v=n([].join),x=i&&!l((function(){return 8!==f((function(){}),"length",{value:8}).length})),b=String(String).split("String"),w=e.exports=function(e,t,o){"Symbol("===h(m(t),0,7)&&(t="["+g(m(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),o&&o.getter&&(t="get "+t),o&&o.setter&&(t="set "+t),(!a(e,"name")||s&&e.name!==t)&&(i?f(e,"name",{value:t,configurable:!0}):e.name=t),x&&o&&a(o,"arity")&&e.length!==o.arity&&f(e,"length",{value:o.arity});try{o&&a(o,"constructor")&&o.constructor?i&&f(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(l){}var n=d(e);return a(n,"source")||(n.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=w((function(){return r(this)&&p(this).source||c(this)}),"toString")},741:function(e){var t=Math.ceil,o=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?o:t)(n)}},4913:function(e,t,o){var n=o(3724),l=o(5917),r=o(8686),a=o(8551),i=o(6969),s=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",m="writable";t.f=n?r?function(e,t,o){if(a(e),t=i(t),a(o),"function"===typeof e&&"prototype"===t&&"value"in o&&m in o&&!o[m]){var n=u(e,t);n&&n[m]&&(e[t]=o.value,o={configurable:p in o?o[p]:n[p],enumerable:d in o?o[d]:n[d],writable:!1})}return c(e,t,o)}:c:function(e,t,o){if(a(e),t=i(t),a(o),l)try{return c(e,t,o)}catch(n){}if("get"in o||"set"in o)throw new s("Accessors not supported");return"value"in o&&(e[t]=o.value),e}},7347:function(e,t,o){var n=o(3724),l=o(9565),r=o(8773),a=o(6980),i=o(5397),s=o(6969),c=o(9297),u=o(5917),d=Object.getOwnPropertyDescriptor;t.f=n?d:function(e,t){if(e=i(e),t=s(t),u)try{return d(e,t)}catch(o){}if(c(e,t))return a(!l(r.f,e,t),e[t])}},8480:function(e,t,o){var n=o(1828),l=o(8727),r=l.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,r)}},3717:function(e,t){t.f=Object.getOwnPropertySymbols},1625:function(e,t,o){var n=o(9504);e.exports=n({}.isPrototypeOf)},1828:function(e,t,o){var n=o(9504),l=o(9297),r=o(5397),a=o(9617).indexOf,i=o(421),s=n([].push);e.exports=function(e,t){var o,n=r(e),c=0,u=[];for(o in n)!l(i,o)&&l(n,o)&&s(u,o);while(t.length>c)l(n,o=t[c++])&&(~a(u,o)||s(u,o));return u}},8773:function(e,t){var o={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,l=n&&!o.call({1:2},1);t.f=l?function(e){var t=n(this,e);return!!t&&t.enumerable}:o},4270:function(e,t,o){var n=o(9565),l=o(4901),r=o(34),a=TypeError;e.exports=function(e,t){var o,i;if("string"===t&&l(o=e.toString)&&!r(i=n(o,e)))return i;if(l(o=e.valueOf)&&!r(i=n(o,e)))return i;if("string"!==t&&l(o=e.toString)&&!r(i=n(o,e)))return i;throw new a("Can't convert object to primitive value")}},5031:function(e,t,o){var n=o(7751),l=o(9504),r=o(8480),a=o(3717),i=o(8551),s=l([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=r.f(i(e)),o=a.f;return o?s(t,o(e)):t}},7750:function(e,t,o){var n=o(4117),l=TypeError;e.exports=function(e){if(n(e))throw new l("Can't call method on "+e);return e}},6119:function(e,t,o){var n=o(5745),l=o(3392),r=n("keys");e.exports=function(e){return r[e]||(r[e]=l(e))}},7629:function(e,t,o){var n=o(6395),l=o(4475),r=o(9433),a="__core-js_shared__",i=e.exports=l[a]||r(a,{});(i.versions||(i.versions=[])).push({version:"3.37.1",mode:n?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"})},5745:function(e,t,o){var n=o(7629);e.exports=function(e,t){return n[e]||(n[e]=t||{})}},4495:function(e,t,o){var n=o(7388),l=o(9039),r=o(4475),a=r.String;e.exports=!!Object.getOwnPropertySymbols&&!l((function(){var e=Symbol("symbol detection");return!a(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},5610:function(e,t,o){var n=o(1291),l=Math.max,r=Math.min;e.exports=function(e,t){var o=n(e);return o<0?l(o+t,0):r(o,t)}},5397:function(e,t,o){var n=o(7055),l=o(7750);e.exports=function(e){return n(l(e))}},1291:function(e,t,o){var n=o(741);e.exports=function(e){var t=+e;return t!==t||0===t?0:n(t)}},8014:function(e,t,o){var n=o(1291),l=Math.min;e.exports=function(e){var t=n(e);return t>0?l(t,9007199254740991):0}},8981:function(e,t,o){var n=o(7750),l=Object;e.exports=function(e){return l(n(e))}},2777:function(e,t,o){var n=o(9565),l=o(34),r=o(757),a=o(5966),i=o(4270),s=o(8227),c=TypeError,u=s("toPrimitive");e.exports=function(e,t){if(!l(e)||r(e))return e;var o,s=a(e,u);if(s){if(void 0===t&&(t="default"),o=n(s,e,t),!l(o)||r(o))return o;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),i(e,t)}},6969:function(e,t,o){var n=o(2777),l=o(757);e.exports=function(e){var t=n(e,"string");return l(t)?t:t+""}},2140:function(e,t,o){var n=o(8227),l=n("toStringTag"),r={};r[l]="z",e.exports="[object z]"===String(r)},655:function(e,t,o){var n=o(6955),l=String;e.exports=function(e){if("Symbol"===n(e))throw new TypeError("Cannot convert a Symbol value to a string");return l(e)}},6823:function(e){var t=String;e.exports=function(e){try{return t(e)}catch(o){return"Object"}}},3392:function(e,t,o){var n=o(9504),l=0,r=Math.random(),a=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++l+r,36)}},7040:function(e,t,o){var n=o(4495);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8686:function(e,t,o){var n=o(3724),l=o(9039);e.exports=n&&l((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2812:function(e){var t=TypeError;e.exports=function(e,o){if(e<o)throw new t("Not enough arguments");return e}},8622:function(e,t,o){var n=o(4475),l=o(4901),r=n.WeakMap;e.exports=l(r)&&/native code/.test(String(r))},8227:function(e,t,o){var n=o(4475),l=o(5745),r=o(9297),a=o(3392),i=o(4495),s=o(7040),c=n.Symbol,u=l("wks"),d=s?c["for"]||c:c&&c.withoutSetter||a;e.exports=function(e){return r(u,e)||(u[e]=i&&r(c,e)?c[e]:d("Symbol."+e)),u[e]}},4114:function(e,t,o){var n=o(6518),l=o(8981),r=o(6198),a=o(4527),i=o(6837),s=o(9039),c=s((function(){return 4294967297!==[].push.call({length:4294967296},1)})),u=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},d=c||!u();n({target:"Array",proto:!0,arity:1,forced:d},{push:function(e){var t=l(this),o=r(t),n=arguments.length;i(o+n);for(var s=0;s<n;s++)t[o]=arguments[s],o++;return a(t,o),o}})},4603:function(e,t,o){var n=o(6840),l=o(9504),r=o(655),a=o(2812),i=URLSearchParams,s=i.prototype,c=l(s.append),u=l(s["delete"]),d=l(s.forEach),p=l([].push),m=new i("a=1&a=2&b=3");m["delete"]("a",1),m["delete"]("b",void 0),m+""!=="a=2"&&n(s,"delete",(function(e){var t=arguments.length,o=t<2?void 0:arguments[1];if(t&&void 0===o)return u(this,e);var n=[];d(this,(function(e,t){p(n,{key:t,value:e})})),a(t,1);var l,i=r(e),s=r(o),m=0,f=0,h=!1,g=n.length;while(m<g)l=n[m++],h||l.key===i?(h=!0,u(this,l.key)):f++;while(f<g)l=n[f++],l.key===i&&l.value===s||c(this,l.key,l.value)}),{enumerable:!0,unsafe:!0})},7566:function(e,t,o){var n=o(6840),l=o(9504),r=o(655),a=o(2812),i=URLSearchParams,s=i.prototype,c=l(s.getAll),u=l(s.has),d=new i("a=1");!d.has("a",2)&&d.has("a",void 0)||n(s,"has",(function(e){var t=arguments.length,o=t<2?void 0:arguments[1];if(t&&void 0===o)return u(this,e);var n=c(this,e);a(t,1);var l=r(o),i=0;while(i<n.length)if(n[i++]===l)return!0;return!1}),{enumerable:!0,unsafe:!0})},8721:function(e,t,o){var n=o(3724),l=o(9504),r=o(2106),a=URLSearchParams.prototype,i=l(a.forEach);n&&!("size"in a)&&r(a,"size",{get:function(){var e=0;return i(this,(function(){e++})),e},configurable:!0,enumerable:!0})}},n={};function l(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}};return o[e].call(r.exports,r,r.exports,l),r.exports}!function(){l.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return l.d(t,{a:t}),t}}(),function(){l.d=function(e,t){for(var o in t)l.o(t,o)&&!l.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}}(),function(){l.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){l.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){l.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){l.p=""}();var r={};return function(){l.r(r),l.d(r,{Button:function(){return Br},ButtonGroup:function(){return _a},Checkbox:function(){return ya},CheckboxGroup:function(){return Sa},Colgroup:function(){return lr},Column:function(){return tr},Custom:function(){return jl},Drawer:function(){return ai},Edit:function(){return dn},Export:function(){return Tl},Filter:function(){return Uo},Form:function(){return ma},FormGather:function(){return mi},FormItem:function(){return ui},Grid:function(){return wa},Icon:function(){return Wl},Input:function(){return Fa},Keyboard:function(){return Ol},List:function(){return Ii},Menu:function(){return rn},Modal:function(){return Za},Optgroup:function(){return yi},Option:function(){return Si},Pager:function(){return Lr},Pulldown:function(){return Ni},Radio:function(){return Ra},RadioButton:function(){return Ia},RadioGroup:function(){return Ma},Select:function(){return Dr},Switch:function(){return Oi},Table:function(){return Mr},Textarea:function(){return Aa},Toolbar:function(){return zr},Tooltip:function(){return ta},VXETable:function(){return No},Validator:function(){return Nl},VxeButton:function(){return Vr},VxeButtonGroup:function(){return Ba},VxeCheckbox:function(){return Ca},VxeCheckboxGroup:function(){return Ea},VxeColgroup:function(){return nr},VxeColumn:function(){return er},VxeDrawer:function(){return ri},VxeForm:function(){return pa},VxeFormGather:function(){return pi},VxeFormItem:function(){return ci},VxeGrid:function(){return ba},VxeIcon:function(){return zl},VxeInput:function(){return Da},VxeList:function(){return $i},VxeModal:function(){return Ka},VxeOptgroup:function(){return Ci},VxeOption:function(){return Ei},VxePager:function(){return Pr},VxePulldown:function(){return Fi},VxeRadio:function(){return ka},VxeRadioButton:function(){return $a},VxeRadioGroup:function(){return Oa},VxeSelect:function(){return Ir},VxeSwitch:function(){return Ri},VxeTable:function(){return Or},VxeTableCustomModule:function(){return _l},VxeTableEditModule:function(){return un},VxeTableExportModule:function(){return yl},VxeTableFilterModule:function(){return qo},VxeTableKeyboardModule:function(){return Rl},VxeTableMenuModule:function(){return ln},VxeTableValidatorModule:function(){return Fl},VxeTextarea:function(){return La},VxeToolbar:function(){return Hr},VxeTooltip:function(){return ea},VxeUI:function(){return Po},_t:function(){return wo},commands:function(){return ao},component:function(){return Io},config:function(){return ko},default:function(){return Bi},drawer:function(){return li},formats:function(){return X},getComponent:function(){return $o},globalConfs:function(){return To},globalStore:function(){return Oo},hooks:function(){return mo},install:function(){return Ai},interceptor:function(){return j},menus:function(){return so},modal:function(){return Ya},print:function(){return Cl},readFile:function(){return Wn},renderer:function(){return lo},saveFile:function(){return Yn},setConfig:function(){return Co},setIcon:function(){return Ro},setup:function(){return So},t:function(){return bo},tableVersion:function(){return Fo},use:function(){return xo},v:function(){return Eo},validators:function(){return po},version:function(){return Do}});var e={};if(l.r(e),l.d(e,{Button:function(){return Br},ButtonGroup:function(){return _a},Checkbox:function(){return ya},CheckboxGroup:function(){return Sa},Colgroup:function(){return lr},Column:function(){return tr},Custom:function(){return jl},Drawer:function(){return ai},Edit:function(){return dn},Export:function(){return Tl},Filter:function(){return Uo},Form:function(){return ma},FormGather:function(){return mi},FormItem:function(){return ui},Grid:function(){return wa},Icon:function(){return Wl},Input:function(){return Fa},Keyboard:function(){return Ol},List:function(){return Ii},Menu:function(){return rn},Modal:function(){return Za},Optgroup:function(){return yi},Option:function(){return Si},Pager:function(){return Lr},Pulldown:function(){return Ni},Radio:function(){return Ra},RadioButton:function(){return Ia},RadioGroup:function(){return Ma},Select:function(){return Dr},Switch:function(){return Oi},Table:function(){return Mr},Textarea:function(){return Aa},Toolbar:function(){return zr},Tooltip:function(){return ta},VXETable:function(){return No},Validator:function(){return Nl},VxeButton:function(){return Vr},VxeButtonGroup:function(){return Ba},VxeCheckbox:function(){return Ca},VxeCheckboxGroup:function(){return Ea},VxeColgroup:function(){return nr},VxeColumn:function(){return er},VxeDrawer:function(){return ri},VxeForm:function(){return pa},VxeFormGather:function(){return pi},VxeFormItem:function(){return ci},VxeGrid:function(){return ba},VxeIcon:function(){return zl},VxeInput:function(){return Da},VxeList:function(){return $i},VxeModal:function(){return Ka},VxeOptgroup:function(){return Ci},VxeOption:function(){return Ei},VxePager:function(){return Pr},VxePulldown:function(){return Fi},VxeRadio:function(){return ka},VxeRadioButton:function(){return $a},VxeRadioGroup:function(){return Oa},VxeSelect:function(){return Ir},VxeSwitch:function(){return Ri},VxeTable:function(){return Or},VxeTableCustomModule:function(){return _l},VxeTableEditModule:function(){return un},VxeTableExportModule:function(){return yl},VxeTableFilterModule:function(){return qo},VxeTableKeyboardModule:function(){return Rl},VxeTableMenuModule:function(){return ln},VxeTableValidatorModule:function(){return Fl},VxeTextarea:function(){return La},VxeToolbar:function(){return Hr},VxeTooltip:function(){return ea},VxeUI:function(){return Po},_t:function(){return wo},commands:function(){return ao},component:function(){return Io},config:function(){return ko},drawer:function(){return li},formats:function(){return X},getComponent:function(){return $o},globalConfs:function(){return To},globalStore:function(){return Oo},hooks:function(){return mo},install:function(){return Ai},interceptor:function(){return j},menus:function(){return so},modal:function(){return Ya},print:function(){return Cl},readFile:function(){return Wn},renderer:function(){return lo},saveFile:function(){return Yn},setConfig:function(){return Co},setIcon:function(){return Ro},setup:function(){return So},t:function(){return bo},tableVersion:function(){return Fo},use:function(){return xo},v:function(){return Eo},validators:function(){return po},version:function(){return Do}}),"undefined"!==typeof window){var t=window.document.currentScript,o=t&&t.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);o&&(l.p=o[1])}var n=l(8871),a=l.n(n);l(4114);const i="vxe-icon-",s={size:null,zIndex:999,version:0,emptyCell:"　",table:{fit:!0,showHeader:!0,animat:!0,delayHover:250,autoResize:!0,minHeight:144,resizeConfig:{refreshDelay:250},radioConfig:{strict:!0},checkboxConfig:{strict:!0},tooltipConfig:{enterable:!0},validConfig:{showMessage:!0,autoClear:!0,autoPos:!0,message:"inline",msgMode:"single"},columnConfig:{maxFixedSize:4},customConfig:{allowFixed:!0,showFooter:!0},sortConfig:{showIcon:!0,iconLayout:"vertical"},filterConfig:{showIcon:!0},treeConfig:{rowField:"id",parentField:"parentId",childrenField:"children",hasChildField:"hasChild",mapChildrenField:"_X_ROW_CHILD",indent:20,showIcon:!0},expandConfig:{showIcon:!0},editConfig:{showIcon:!0,showAsterisk:!0},importConfig:{_typeMaps:{},modes:["insert","covering"]},exportConfig:{_typeMaps:{csv:1,html:1,xml:1,txt:1},modes:["current","selected"]},printConfig:{modes:["current","selected"]},mouseConfig:{extension:!0},keyboardConfig:{isEsc:!0},areaConfig:{autoClear:!0,selectCellByHeader:!0},clipConfig:{isCopy:!0,isCut:!0,isPaste:!0},fnrConfig:{isFind:!0,isReplace:!0},scrollX:{gt:60},scrollY:{gt:100}},export:{types:{}},grid:{formConfig:{enabled:!0},pagerConfig:{enabled:!0},toolbarConfig:{enabled:!0},proxyConfig:{enabled:!0,autoLoad:!0,message:!0,props:{list:null,result:"result",total:"page.total",message:"message"}}},toolbar:{},icon:{LOADING:i+"spinner roll vxe-loading--default-icon",TABLE_SORT_ASC:i+"caret-up",TABLE_SORT_DESC:i+"caret-down",TABLE_FILTER_NONE:i+"funnel",TABLE_FILTER_MATCH:i+"funnel",TABLE_EDIT:i+"edit",TABLE_TITLE_PREFIX:i+"question-circle-fill",TABLE_TITLE_SUFFIX:i+"question-circle-fill",TABLE_TREE_LOADED:i+"spinner roll",TABLE_TREE_OPEN:i+"caret-right rotate90",TABLE_TREE_CLOSE:i+"caret-right",TABLE_EXPAND_LOADED:i+"spinner roll",TABLE_EXPAND_OPEN:i+"arrow-right rotate90",TABLE_EXPAND_CLOSE:i+"arrow-right",TABLE_CHECKBOX_CHECKED:i+"checkbox-checked",TABLE_CHECKBOX_UNCHECKED:i+"checkbox-unchecked",TABLE_CHECKBOX_INDETERMINATE:i+"checkbox-indeterminate",TABLE_RADIO_CHECKED:i+"radio-checked",TABLE_RADIO_UNCHECKED:i+"radio-unchecked",BUTTON_DROPDOWN:i+"arrow-down",BUTTON_LOADING:i+"spinner roll",SELECT_LOADED:i+"spinner roll",SELECT_OPEN:i+"caret-down rotate180",SELECT_CLOSE:i+"caret-down",PAGER_HOME:i+"home-page",PAGER_END:i+"end-page",PAGER_JUMP_PREV:i+"arrow-double-left",PAGER_JUMP_NEXT:i+"arrow-double-right",PAGER_PREV_PAGE:i+"arrow-left",PAGER_NEXT_PAGE:i+"arrow-right",PAGER_JUMP_MORE:i+"ellipsis-h",INPUT_CLEAR:i+"error-circle-fill",INPUT_PWD:i+"eye-fill",INPUT_SHOW_PWD:i+"eye-fill-close",INPUT_PREV_NUM:i+"caret-up",INPUT_NEXT_NUM:i+"caret-down",INPUT_DATE:i+"calendar",INPUT_SEARCH:i+"search",MODAL_ZOOM_IN:i+"square",MODAL_ZOOM_OUT:i+"maximize",MODAL_CLOSE:i+"close",MODAL_INFO:i+"info-circle-fill",MODAL_SUCCESS:i+"success-circle-fill",MODAL_WARNING:i+"warnion-circle-fill",MODAL_ERROR:i+"error-circle-fill",MODAL_QUESTION:i+"question-circle-fill",MODAL_LOADING:i+"spinner roll",TOOLBAR_TOOLS_REFRESH:i+"repeat",TOOLBAR_TOOLS_REFRESH_LOADING:i+"repeat roll",TOOLBAR_TOOLS_IMPORT:i+"upload",TOOLBAR_TOOLS_EXPORT:i+"download",TOOLBAR_TOOLS_PRINT:i+"print",TOOLBAR_TOOLS_FULLSCREEN:i+"fullscreen",TOOLBAR_TOOLS_MINIMIZE:i+"minimize",TOOLBAR_TOOLS_CUSTOM:i+"custom-column",TOOLBAR_TOOLS_FIXED_LEFT:i+"fixed-left",TOOLBAR_TOOLS_FIXED_LEFT_ACTIVED:i+"fixed-left-fill",TOOLBAR_TOOLS_FIXED_RIGHT:i+"fixed-right",TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVED:i+"fixed-right-fill",FORM_PREFIX:i+"question-circle-fill",FORM_SUFFIX:i+"question-circle-fill",FORM_FOLDING:i+"arrow-up rotate180",FORM_UNFOLDING:i+"arrow-up"},tooltip:{trigger:"hover",theme:"dark",enterDelay:500,leaveDelay:300},pager:{},form:{validConfig:{showMessage:!0,autoPos:!0},tooltipConfig:{enterable:!0},titleAsterisk:!0},input:{startDate:new Date(1900,0,1),endDate:new Date(2100,0,1),startDay:1,selectDay:1,digits:2,controls:!0},textarea:{},select:{multiCharOverflow:8},button:{},buttonGroup:{},radio:{strict:!0},radioButton:{strict:!0},radioGroup:{strict:!0},checkbox:{},checkboxGroup:{},switch:{},modal:{top:15,showHeader:!0,minWidth:340,minHeight:140,lockView:!0,mask:!0,duration:3e3,marginSize:0,dblclickZoom:!0,showTitleOverflow:!0,animat:!0,showClose:!0,draggable:!0,showConfirmButton:null,storageKey:"VXE_MODAL_POSITION"},drawer:{showHeader:!0,lockView:!0,mask:!0,showTitleOverflow:!0,showClose:!0},list:{scrollY:{enabled:!0,gt:100}},i18n:e=>e};var c=s,u=null,d=null,p=null,m="z-index-manage",f=null,h="z-index-style",g="m",v="s",x={m:1e3,s:1e3};function b(){return u||"undefined"!==typeof document&&(u=document),u}function w(){return u&&!d&&(d=u.body||u.getElementsByTagName("body")[0]),d}function C(){var e=0,t=b();if(t){var o=w();if(o)for(var n=o.getElementsByTagName("*"),l=0;l<n.length;l++){var r=n[l];if(r&&r.style&&1===r.nodeType){var a=r.style.zIndex;a&&/^\d+$/.test(a)&&(e=Math.max(e,Number(a)))}}}return e}function y(){if(!f){var e=b();e&&(f=e.getElementById(h),f||(f=e.createElement("style"),f.id=h,e.getElementsByTagName("head")[0].appendChild(f)))}return f}function T(){var e=y();if(e){var t="--dom-",o="-z-index";e.innerHTML=":root{"+t+"main"+o+":"+O()+";"+t+"sub"+o+":"+D()+"}"}}function E(){if(!p){var e=b();if(e&&(p=e.getElementById(m),!p)){var t=w();t&&(p=e.createElement("div"),p.id=m,p.style.display="none",t.appendChild(p),k(x.m),$(x.s))}}return p}function S(e){return function(t){if(t){t=Number(t),x[e]=t;var o=E();o&&(o.dataset?o.dataset[e]=t+"":o.setAttribute("data-"+e,t+""))}return T(),x[e]}}var k=S(g);function R(e,t){return function(o){var n,l=E();if(l){var r=l.dataset?l.dataset[e]:l.getAttribute("data-"+e);r&&(n=Number(r))}return n||(n=x[e]),o?Number(o)<n?t():o:n}}var O=R(g,M);function M(){return k(O()+1)}var $=S(v),I=R(v,F);function D(){return O()+I()}function F(){return $(I()+1),D()}var N={setCurrent:k,getCurrent:O,getNext:M,setSubCurrent:$,getSubCurrent:D,getSubNext:F,getMax:C};T();var P=N;function L(e,t){return`[vxe-table v4.6.25] ${c.i18n(e,t)}`}function A(e){return function(t,o){const n=L(t,o);return console[e](n),n}}const V=A("warn"),B=A("error"),_={},j={mixin(e){return a().each(e,((e,t)=>j.add(t,e))),j},get(e){return _[e]||[]},add(e,t){{const t=["created","mounted","activated","beforeUnmount","unmounted","event.clearEdit","event.clearActived","event.clearFilter","event.clearAreas","event.showMenu","event.keydown","event.export","event.import"];-1===t.indexOf(e)&&V("vxe.error.errProp",[`Interceptor.${e}`,t.join("|")])}if(t){let o=_[e];o||(o=_[e]=[]),o.indexOf(t)>-1&&V("vxe.error.coverProp",["Interceptor",e]),o.push(t)}return j},delete(e,t){const o=_[e];o&&(t?a().remove(o,(e=>e===t)):delete _[e])}};var H=l(9274);function z(e){return z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},z(e)}function W(e,t){if("object"!=z(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=z(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function q(e){var t=W(e,"string");return"symbol"==z(t)?t:t+""}function U(e,t,o){return(t=q(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}class G{constructor(){U(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];a().isFunction(t)&&(V("vxe.error.delProp",["formats -> callback","cellFormatMethod"]),t={cellFormatMethod:t});{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&V("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}const X=new G;function Y(e){return e&&!1!==e.enabled}function K(e){return null===e||void 0===e||""===e}function Z(e){const t=e.name,o=a().lastIndexOf(t,"."),n=t.substring(o+1,t.length).toLowerCase(),l=t.substring(0,o);return{filename:l,type:n}}function J(){return P.getNext()}function Q(){return P.getCurrent()}function ee(e){return e&&e.children&&e.children.length>0}function te(e){return e?a().toValueString(c.translate?c.translate(""+e):e):""}function oe(e,t){return""+(K(e)?t?c.emptyCell:"":e)}function ne(e){return""===e||a().eqNull(e)}Object.assign(X,{_name:"Formats"});class le{constructor(e,t,{renderHeader:o,renderCell:n,renderFooter:l,renderData:r}={}){const i=e.xegrid,s=t.formatter,c=!a().isBoolean(t.visible)||t.visible;{const o=["seq","checkbox","radio","expand","html"];if(t.type&&-1===o.indexOf(t.type)&&V("vxe.error.errProp",[`type=${t.type}`,o.join(", ")]),(a().isBoolean(t.cellRender)||t.cellRender&&!a().isObject(t.cellRender))&&V("vxe.error.errProp",[`column.cell-render=${t.cellRender}`,"column.cell-render={}"]),(a().isBoolean(t.editRender)||t.editRender&&!a().isObject(t.editRender))&&V("vxe.error.errProp",[`column.edit-render=${t.editRender}`,"column.edit-render={}"]),t.cellRender&&t.editRender&&V("vxe.error.errConflicts",["column.cell-render","column.edit-render"]),"expand"===t.type){const{props:t}=e,{treeConfig:o}=t,{computeTreeOpts:n}=e.getComputeMaps(),l=n.value;o&&(l.showLine||l.line)&&B("vxe.error.errConflicts",["tree-config.showLine","column.type=expand"])}if(s)if(a().isString(s)){const e=X.get(s)||a()[s];e&&a().isFunction(e.tableCellFormatMethod||e.cellFormatMethod)||B("vxe.error.notFormats",[s])}else if(a().isArray(s)){const e=X.get(s[0])||a()[s[0]];e&&a().isFunction(e.tableCellFormatMethod||e.cellFormatMethod)||B("vxe.error.notFormats",[s[0]])}}if(Object.assign(this,{type:t.type,property:t.field,field:t.field,title:t.title,width:t.width,minWidth:t.minWidth,maxWidth:t.maxWidth,resizable:t.resizable,fixed:t.fixed,align:t.align,headerAlign:t.headerAlign,footerAlign:t.footerAlign,showOverflow:t.showOverflow,showHeaderOverflow:t.showHeaderOverflow,showFooterOverflow:t.showFooterOverflow,className:t.className,headerClassName:t.headerClassName,footerClassName:t.footerClassName,formatter:s,sortable:t.sortable,sortBy:t.sortBy,sortType:t.sortType,filters:je(t.filters),filterMultiple:!a().isBoolean(t.filterMultiple)||t.filterMultiple,filterMethod:t.filterMethod,filterResetMethod:t.filterResetMethod,filterRecoverMethod:t.filterRecoverMethod,filterRender:t.filterRender,treeNode:t.treeNode,cellType:t.cellType,cellRender:t.cellRender,editRender:t.editRender,contentRender:t.contentRender,headerExportMethod:t.headerExportMethod,exportMethod:t.exportMethod,footerExportMethod:t.footerExportMethod,titleHelp:t.titleHelp,titlePrefix:t.titlePrefix,titleSuffix:t.titleSuffix,params:t.params,id:t.colId||a().uniqueId("col_"),parentId:null,visible:c,halfVisible:!1,defaultVisible:c,defaultFixed:t.fixed,checked:!1,halfChecked:!1,disabled:!1,level:1,rowSpan:1,colSpan:1,order:null,sortTime:0,sortNumber:0,renderSortNumber:0,renderWidth:0,renderHeight:0,resizeWidth:0,renderLeft:0,renderArgs:[],model:{},renderHeader:o||t.renderHeader,renderCell:n||t.renderCell,renderFooter:l||t.renderFooter,renderData:r,slots:t.slots}),i){const{computeProxyOpts:e}=i.getComputeMaps(),t=e.value;t.beforeColumn&&t.beforeColumn({$grid:i,column:this})}}getTitle(){return te(this.title||("seq"===this.type?c.i18n("vxe.table.seqTitle"):""))}getKey(){return this.field||(this.type?`type=${this.type}`:null)}update(e,t){"filters"!==e&&("field"===e&&(this.property=t),this[e]=t)}}const re={},ae=a().browse();function ie(e,t){return e?a().isFunction(e)?e(t):e:""}function se(e){return re[e]||(re[e]=new RegExp(`(?:^|\\s)${e}(?!\\S)`,"g")),re[e]}function ce(e,t,o){if(e){const n=e.parentNode;if(o.top+=e.offsetTop,o.left+=e.offsetLeft,n&&n!==document.documentElement&&n!==document.body&&(o.top-=n.scrollTop,o.left-=n.scrollLeft),(!t||e!==t&&e.offsetParent!==t)&&e.offsetParent)return ce(e.offsetParent,t,o)}return o}function ue(e){return e&&/^\d+(px)?$/.test(e)}function de(e){return e&&/^\d+%$/.test(e)}function pe(e,t){return e&&e.className&&e.className.match&&e.className.match(se(t))}function me(e,t){e&&pe(e,t)&&(e.className=e.className.replace(se(t),""))}function fe(e,t){e&&!pe(e,t)&&(me(e,t),e.className=`${e.className} ${t}`)}function he(){const e=document.documentElement,t=document.body;return{scrollTop:e.scrollTop||t.scrollTop,scrollLeft:e.scrollLeft||t.scrollLeft,visibleHeight:e.clientHeight||t.clientHeight,visibleWidth:e.clientWidth||t.clientWidth}}function ge(e){return e?e.offsetHeight:0}function ve(e){if(e){const t=getComputedStyle(e),o=a().toNumber(t.paddingTop),n=a().toNumber(t.paddingBottom);return o+n}return 0}function xe(e,t){e&&(e.scrollTop=t)}function be(e,t){e&&(e.scrollLeft=t)}function we(e,t){const o="html"===t.type?e.innerText:e.textContent;e.getAttribute("title")!==o&&e.setAttribute("title",o)}function Ce(e,t,o,n){let l,r=e.target.shadowRoot&&e.composed&&e.composedPath()[0]||e.target;while(r&&r.nodeType&&r!==document){if(o&&pe(r,o)&&(!n||n(r)))l=r;else if(r===t)return{flag:!o||!!l,container:t,targetElem:l};r=r.parentNode}return{flag:!1}}function ye(e,t){return ce(e,t,{left:0,top:0})}function Te(e){const t=e.getBoundingClientRect(),o=t.top,n=t.left,{scrollTop:l,scrollLeft:r,visibleHeight:a,visibleWidth:i}=he();return{boundingTop:o,top:l+o,boundingLeft:n,left:r+n,visibleHeight:a,visibleWidth:i}}const Ee="scrollIntoViewIfNeeded",Se="scrollIntoView";function ke(e){e&&(e[Ee]?e[Ee]():e[Se]&&e[Se]())}function Re(e,t){e&&e.dispatchEvent(new Event(t))}function Oe(e){return e&&1===e.nodeType}const Me=(e,t)=>{const o=[];return e.forEach((e=>{e.parentId=t?t.id:null,e.visible&&(e.children&&e.children.length&&e.children.some((e=>e.visible))?(o.push(e),o.push(...Me(e.children,e))):o.push(e))})),o},$e=e=>{let t=1;const o=(e,n)=>{if(n&&(e.level=n.level+1,t<e.level&&(t=e.level)),e.children&&e.children.length&&e.children.some((e=>e.visible))){let t=0;e.children.forEach((n=>{n.visible&&(o(n,e),t+=n.colSpan)})),e.colSpan=t}else e.colSpan=1};e.forEach((e=>{e.level=1,o(e)}));const n=[];for(let r=0;r<t;r++)n.push([]);const l=Me(e);return l.forEach((e=>{e.children&&e.children.length&&e.children.some((e=>e.visible))?e.rowSpan=1:e.rowSpan=t-e.level+1,n[e.level-1].push(e)})),n};function Ie(e,t,o){const{internalData:n}=e;return e.clearScroll().then((()=>{if(t||o)return n.lastScrollLeft=0,n.lastScrollTop=0,e.scrollTo(t,o)}))}function De(e){e&&e._onscroll&&(e.onscroll=null)}function Fe(e){e&&e._onscroll&&(e.onscroll=e._onscroll)}function Ne(){return a().uniqueId("row_")}function Pe(e){const{props:t}=e,{computeRowOpts:o}=e.getComputeMaps(),{rowId:n}=t,l=o.value;return n||l.keyField||"_X_ROW_KEY"}function Le(e,t){const o=a().get(t,Pe(e));return a().eqNull(o)?"":encodeURIComponent(o)}const Ae=(e,t)=>t?a().isString(t)?e.getColumnByField(t):t:null;function Ve(e){if(e){const t=getComputedStyle(e),o=a().toNumber(t.paddingLeft),n=a().toNumber(t.paddingRight);return o+n}return 0}function Be(e){if(e){const t=getComputedStyle(e),o=a().toNumber(t.marginLeft),n=a().toNumber(t.marginRight);return e.offsetWidth+o+n}return 0}function _e(e,t){return e.querySelector(".vxe-cell"+t)}function je(e){return e&&a().isArray(e)?e.map((({label:e,value:t,data:o,resetValue:n,checked:l})=>({label:e,value:t,data:o,resetValue:n,checked:!!l,_checked:!!l}))):e}function He(e){return e.map(((e,t)=>t%2===0?Number(e)+1:".")).join("")}function ze(e,t){return a().get(e,t.field)}function We(e,t,o){return a().set(e,t.field,o)}function qe(e){const{$table:t,column:o,cell:n}=e,{props:l}=t,{computeResizableOpts:r}=t.getComputeMaps(),i=r.value,{minWidth:s}=i;if(s){const t=a().isFunction(s)?s(e):s;if("auto"!==t)return Math.max(1,a().toNumber(t))}const{showHeaderOverflow:c}=l,{showHeaderOverflow:u,minWidth:d}=o,p=a().isUndefined(u)||a().isNull(u)?c:u,m="ellipsis"===p,f="title"===p,h=!0===p||"tooltip"===p,g=f||h||m,v=a().floor(1.6*(a().toNumber(getComputedStyle(n).fontSize)||14)),x=Ve(n)+Ve(_e(n,""));let b=v+x;if(g){const e=Ve(_e(n,"--title>.vxe-cell--checkbox")),t=Be(_e(n,">.vxe-cell--required-icon")),o=Be(_e(n,">.vxe-cell--edit-icon")),l=Be(_e(n,">.vxe-cell-title-prefix-icon")),r=Be(_e(n,">.vxe-cell-title-suffix-icon")),a=Be(_e(n,">.vxe-cell--sort")),i=Be(_e(n,">.vxe-cell--filter"));b+=e+t+o+l+r+i+a}if(d){const{refTableBody:e}=t.getRefMaps(),o=e.value,n=o?o.$el:null;if(n){if(de(d)){const e=n.clientWidth-1,t=e/100;return Math.max(b,Math.floor(a().toInteger(d)*t))}if(ue(d))return Math.max(b,a().toInteger(d))}}return b}function Ue(e){return e&&(e.constructor===le||e instanceof le)}function Ge(e,t,o){return Ue(t)?t:(0,H.reactive)(new le(e,t,o))}function Xe(e,t,o){Object.keys(t).forEach((n=>{(0,H.watch)((()=>t[n]),(t=>{o.update(n,t),e&&("filters"===n?(e.setFilter(o,t),e.handleUpdateDataQueue()):["visible","fixed","width","minWidth","maxWidth"].includes(n)&&e.handleRefreshColumnQueue())}))}))}function Ye(e,t,o,n){const{reactData:l}=e,{staticColumns:r}=l,i=t.parentNode,s=n?n.column:null,c=s?s.children:r;i&&c&&(c.splice(a().arrayIndexOf(i.children,t),0,o),l.staticColumns=r.slice(0))}function Ke(e,t){const{reactData:o}=e,{staticColumns:n}=o,l=a().findTree(n,(e=>e.id===t.id),{children:"children"});l&&l.items.splice(l.index,1),o.staticColumns=n.slice(0)}function Ze(e,t){const{internalData:o}=e,{fullColumnIdData:n}=o;if(!t)return null;let l=t.parentId;while(n[l]){const e=n[l].column;if(l=e.parentId,!l)return e}return t}function Je(e,t,o){for(let n=0;n<e.length;n++){const{row:l,col:r,rowspan:a,colspan:i}=e[n];if(r>-1&&l>-1&&a&&i){if(l===t&&r===o)return{rowspan:a,colspan:i};if(t>=l&&t<l+a&&o>=r&&o<r+i)return{rowspan:0,colspan:0}}}}function Qe(e){const{props:t,internalData:o}=e;return o.initStatus=!1,e.clearSort(),e.clearCurrentRow(),e.clearCurrentColumn(),e.clearRadioRow(),e.clearRadioReserve(),e.clearCheckboxRow(),e.clearCheckboxReserve(),e.clearRowExpand(),e.clearTreeExpand(),e.clearTreeExpandReserve(),e.clearPendingRow(),e.clearFilter&&e.clearFilter(),e.clearSelected&&(t.keyboardConfig||t.mouseConfig)&&e.clearSelected(),e.clearCellAreas&&t.mouseConfig&&(e.clearCellAreas(),e.clearCopyCellArea()),e.clearScroll()}function et(e){return e.clearFilter&&e.clearFilter(),Qe(e)}function tt(e,t){const{reactData:o,internalData:n}=e,{refTableBody:l}=e.getRefMaps(),{scrollYLoad:r}=o,{afterFullData:a,scrollYStore:i}=n,s=l.value,c=s?s.$el:null;if(c){const o=c.querySelector(`[rowid="${Le(e,t)}"]`);if(o){const t=c.clientHeight,n=c.scrollTop,l=o.offsetParent,r=o.offsetTop+(l?l.offsetTop:0),a=o.clientHeight;if(r<n||r>n+t)return e.scrollTo(null,r);if(r+a>=t+n)return e.scrollTo(null,n+a)}else if(r)return e.scrollTo(null,(a.indexOf(t)-1)*i.rowHeight)}return Promise.resolve()}function ot(e,t){const{reactData:o,internalData:n}=e,{refTableBody:l}=e.getRefMaps(),{scrollXLoad:r}=o,{visibleColumn:a}=n,i=l.value,s=i?i.$el:null;if(t&&t.fixed)return Promise.resolve();if(s){const o=s.querySelector(`.${t.id}`);if(o){const t=s.clientWidth,n=s.scrollLeft,l=o.offsetParent,r=o.offsetLeft+(l?l.offsetLeft:0),a=o.clientWidth;if(r<n||r>n+t)return e.scrollTo(r);if(r+a>=t+n)return e.scrollTo(n+a)}else if(r){let o=0;for(let e=0;e<a.length;e++){if(a[e]===t)break;o+=a[e].renderWidth}return e.scrollTo(o)}}return Promise.resolve()}function nt(e){return"on"+e.substring(0,1).toLocaleUpperCase()+e.substring(1)}function lt(e){return a().isArray(e)?e:[e]}const rt="modelValue",at={transfer:!0};function it(e){switch(e.name){case"input":case"textarea":return"input"}return"update:modelValue"}function st(e){switch(e.name){case"input":case"textarea":case"VxeInput":case"VxeTextarea":case"$input":case"$textarea":return"input"}return"change"}function ct(e,t){return e&&t.valueFormat?a().toStringDate(e,t.valueFormat):e}function ut(e,t,o){const{dateConfig:n={}}=t;return a().toDateString(ct(e,t),n.labelFormat||o)}function dt(e,t){return ut(e,t,c.i18n(`vxe.input.date.labelFormat.${t.type}`))}function pt(e){return`vxe-${e.replace("$","")}`}function mt({name:e}){return(0,H.resolveComponent)(e)}function ft({name:e}){return(0,H.resolveComponent)(pt(e))}function ht(e,t,o){const{$panel:n}=e;n.changeOption({},t,o)}function gt(e){let{name:t,attrs:o}=e;return"input"===t&&(o=Object.assign({type:"text"},o)),o}function vt(e){const{name:t,immediate:o,props:n}=e;if(!o){if("VxeInput"===t||"$input"===t){const{type:e}=n||{};return!(!e||"text"===e||"number"===e||"integer"===e||"float"===e)}return"input"!==t&&"textarea"!==t&&"$textarea"!==t}return o}function xt(e,t,o,n){return a().assign({immediate:vt(e)},at,n,e.props,{[rt]:o})}function bt(e,t,o,n){return a().assign({},at,n,e.props,{[rt]:o})}function wt(e,t,o,n){return a().assign({},at,n,e.props,{[rt]:o})}function Ct(e,t){return"cell"===t.$type||vt(e)}function yt(e,t,o){const{placeholder:n}=e;return[(0,H.h)("span",{class:"vxe-cell--label"},n&&K(o)?[(0,H.h)("span",{class:"vxe-cell--placeholder"},oe(te(n),1))]:oe(o,1))]}function Tt(e,t,o,n){const{events:l}=e,r=it(e),i=st(e),s=i===r,c={};return l&&a().objectEach(l,((e,o)=>{c[nt(o)]=function(...o){e(t,...o)}})),o&&(c[nt(r)]=function(e){o(e),s&&n&&n(e),l&&l[r]&&l[r](t,e)}),!s&&n&&(c[nt(i)]=function(...e){n(...e),l&&l[i]&&l[i](t,...e)}),c}function Et(e,t,o,n){const{events:l}=e,r=it(e),i=st(e),s={};return a().objectEach(l,((e,o)=>{s[nt(o)]=function(...o){a().isFunction(e)||B("vxe.error.errFunc",[e]),e(t,...o)}})),o&&(s[nt(r)]=function(e){o(e),l&&l[r]&&l[r](t,e)}),n&&(s[nt(i)]=function(...e){n(...e),l&&l[i]&&l[i](t,...e)}),s}function St(e,t){const{$table:o,row:n,column:l}=t,{name:r}=e,{model:a}=l,i=Ct(e,t);return Et(e,t,(e=>{i?We(n,l,e):(a.update=!0,a.value=e)}),(e=>{if(!i&&["VxeInput","VxeTextarea","$input","$textarea"].includes(r)){const n=e.value;a.update=!0,a.value=n,o.updateStatus(t,n)}else o.updateStatus(t)}))}function kt(e,t,o){return Et(e,t,(e=>{o.data=e}),(()=>{ht(t,!a().eqNull(o.data),o)}))}function Rt(e,t){const{$form:o,data:n,property:l}=t;return Et(e,t,(e=>{a().set(n,l,e)}),(()=>{o.updateStatus(t)}))}function Ot(e,t){const{$table:o,row:n,column:l}=t,{model:r}=l;return Tt(e,t,(o=>{const a=o.target.value;Ct(e,t)?We(n,l,a):(r.update=!0,r.value=a)}),(e=>{const n=e.target.value;o.updateStatus(t,n)}))}function Mt(e,t,o){return Tt(e,t,(e=>{o.data=e.target.value}),(()=>{ht(t,!a().eqNull(o.data),o)}))}function $t(e,t){const{$form:o,data:n,property:l}=t;return Tt(e,t,(e=>{const t=e.target.value;a().set(n,l,t)}),(()=>{o.updateStatus(t)}))}function It(e,t){const{row:o,column:n}=t,{name:l}=e,r=Ct(e,t)?ze(o,n):n.model.value;return[(0,H.h)(l,{class:`vxe-default-${l}`,...gt(e),value:r,...Ot(e,t)})]}function Dt(e,t){return[(0,H.h)(mt(e),{...xt(e,t,null),...Et(e,t)})]}function Ft(e,t){const{row:o,column:n}=t,l=ze(o,n);return[(0,H.h)(mt(e),{...xt(e,t,l),...St(e,t)})]}function Nt(e,t){const{row:o,column:n}=t,l=ze(o,n);return[(0,H.h)(ft(e),{...xt(e,t,l),...St(e,t)})]}function Pt(e,t){return[(0,H.h)((0,H.resolveComponent)("vxe-button"),{...xt(e,t,null),...Et(e,t)})]}function Lt(e,t){return e.children.map((e=>Pt(e,t)[0]))}function At(e,t,o){const{optionGroups:n,optionGroupProps:l={}}=e,r=l.options||"options",a=l.label||"label";return n.map(((n,l)=>(0,H.h)("optgroup",{key:l,label:n[a]},o(n[r],e,t))))}function Vt(e,t,o){const{optionProps:n={}}=t,{row:l,column:r}=o,a=n.label||"label",i=n.value||"value",s=n.disabled||"disabled",c=Ct(t,o)?ze(l,r):r.model.value;return e.map(((e,t)=>(0,H.h)("option",{key:t,value:e[i],disabled:e[s],selected:e[i]==c},e[a])))}function Bt(e,t){const{column:o}=t,{name:n}=e,l=gt(e);return o.filters.map(((o,r)=>(0,H.h)(n,{key:r,class:`vxe-default-${n}`,...l,value:o.data,...Mt(e,t,o)})))}function _t(e,t){const{column:o}=t;return o.filters.map(((o,n)=>{const l=o.data;return(0,H.h)(mt(e),{key:n,...bt(e,e,l),...kt(e,t,o)})}))}function jt(e,t){const{column:o}=t;return o.filters.map(((o,n)=>{const l=o.data;return(0,H.h)(ft(e),{key:n,...bt(e,e,l),...kt(e,t,o)})}))}function Ht({option:e,row:t,column:o}){const{data:n}=e,l=a().get(t,o.property);return l==n}function zt(e,t){return[(0,H.h)("select",{class:"vxe-default-select",...gt(e),...Ot(e,t)},e.optionGroups?At(e,t,Vt):Vt(e.options,e,t))]}function Wt(e,t){const{row:o,column:n}=t,{options:l,optionProps:r,optionGroups:a,optionGroupProps:i}=e,s=ze(o,n);return[(0,H.h)(mt(e),{...xt(e,t,s,{options:l,optionProps:r,optionGroups:a,optionGroupProps:i}),...St(e,t)})]}function qt(e,t){const{row:o,column:n}=t,{options:l,optionProps:r,optionGroups:a,optionGroupProps:i}=e,s=ze(o,n);return[(0,H.h)(ft(e),{...xt(e,t,s,{options:l,optionProps:r,optionGroups:a,optionGroupProps:i}),...St(e,t)})]}function Ut(e,{row:t,column:o}){const{props:n={},options:l,optionGroups:r,optionProps:i={},optionGroupProps:s={}}=e,c=a().get(t,o.property);let u;const d=i.label||"label",p=i.value||"value";return K(c)?"":a().map(n.multiple?c:[c],r?e=>{const t=s.options||"options";for(let o=0;o<r.length;o++)if(u=a().find(r[o][t],(t=>t[p]==e)),u)break;return u?u[d]:e}:e=>(u=a().find(l,(t=>t[p]==e)),u?u[d]:e)).join(", ")}function Gt(e,t){const{data:o,property:n}=t,{name:l}=e,r=gt(e),i=a().get(o,n);return[(0,H.h)(l,{class:`vxe-default-${l}`,...r,value:!r||"input"!==l||"submit"!==r.type&&"reset"!==r.type?i:null,...$t(e,t)})]}function Xt(e,t){const{data:o,property:n}=t,l=a().get(o,n);return[(0,H.h)(mt(e),{...wt(e,t,l),...Rt(e,t)})]}function Yt(e,t){const{data:o,property:n}=t,l=a().get(o,n);return[(0,H.h)(ft(e),{...wt(e,t,l),...Rt(e,t)})]}function Kt(e,t){return[(0,H.h)((0,H.resolveComponent)("vxe-button"),{...wt(e,t,null),...Et(e,t)})]}function Zt(e,t){return e.children.map((e=>Kt(e,t)[0]))}function Jt(e,t,o){const{data:n,property:l}=o,{optionProps:r={}}=t,i=r.label||"label",s=r.value||"value",c=r.disabled||"disabled",u=a().get(n,l);return e.map(((e,t)=>(0,H.h)("option",{key:t,value:e[s],disabled:e[c],selected:e[s]==u},e[i])))}function Qt(e){const{row:t,column:o,options:n}=e;return n.original?ze(t,o):Ut(o.editRender||o.cellRender,e)}function eo(e,t){const{data:o,property:n}=t,l=a().get(o,n);return[(0,H.h)(mt(e),{...wt(e,t,l),...Rt(e,t)})]}function to(e,t){const{options:o,optionProps:n}=e,{data:l,property:r}=t,i=a().get(l,r);return[(0,H.h)(mt(e),{options:o,optionProps:n,...wt(e,t,i),...Rt(e,t)})]}function oo(e,t){const{name:o,options:n,optionProps:l={}}=e,{data:r,property:i}=t,s=l.label||"label",c=l.value||"value",u=l.disabled||"disabled",d=a().get(r,i),p=pt(o);return n?[(0,H.h)((0,H.resolveComponent)(`${p}-group`),{...wt(e,t,d),...Rt(e,t)},{default:()=>n.map(((e,t)=>(0,H.h)((0,H.resolveComponent)(p),{key:t,label:e[c],content:e[s],disabled:e[u]})))})]:[(0,H.h)((0,H.resolveComponent)(p),{...wt(e,t,d),...Rt(e,t)})]}const no={input:{autofocus:"input",renderEdit:It,renderDefault:It,renderFilter:Bt,defaultFilterMethod:Ht,renderItemContent:Gt},textarea:{autofocus:"textarea",renderEdit:It,renderItemContent:Gt},select:{renderEdit:zt,renderDefault:zt,renderCell(e,t){return yt(e,t,Ut(e,t))},renderFilter(e,t){const{column:o}=t;return o.filters.map(((o,n)=>(0,H.h)("select",{key:n,class:"vxe-default-select",...gt(e),...Mt(e,t,o)},e.optionGroups?At(e,t,Vt):Vt(e.options,e,t))))},defaultFilterMethod:Ht,renderItemContent(e,t){return[(0,H.h)("select",{class:"vxe-default-select",...gt(e),...$t(e,t)},e.optionGroups?At(e,t,Jt):Jt(e.options,e,t))]},exportMethod:Qt},VxeInput:{autofocus:".vxe-input--inner",renderEdit:Ft,renderCell(e,t){const{props:o={}}=e,{row:n,column:l}=t,r=o.digits||c.input.digits;let i=a().get(n,l.property);if(i)switch(o.type){case"date":case"week":case"month":case"year":i=dt(i,o);break;case"float":i=a().toFixed(a().floor(i,r),r);break}return yt(e,t,i)},renderDefault:Ft,renderFilter:_t,defaultFilterMethod:Ht,renderItemContent:Xt},VxeTextarea:{autofocus:".vxe-textarea--inner",renderItemContent:Xt},VxeButton:{renderDefault:Dt,renderItemContent:eo},VxeButtonGroup:{renderDefault:Dt,renderItemContent(e,t){const{options:o}=e,{data:n,property:l}=t,r=a().get(n,l);return[(0,H.h)(mt(e),{options:o,...wt(e,t,r),...Rt(e,t)})]}},VxeSelect:{autofocus:".vxe-input--inner",renderEdit:Wt,renderDefault:Wt,renderCell(e,t){return yt(e,t,Ut(e,t))},renderFilter(e,t){const{column:o}=t,{options:n,optionProps:l,optionGroups:r,optionGroupProps:a}=e;return o.filters.map(((o,i)=>{const s=o.data;return(0,H.h)(mt(e),{key:i,...bt(e,t,s,{options:n,optionProps:l,optionGroups:r,optionGroupProps:a}),...kt(e,t,o)})}))},defaultFilterMethod:Ht,renderItemContent(e,t){const{data:o,property:n}=t,{options:l,optionProps:r,optionGroups:i,optionGroupProps:s}=e,c=a().get(o,n);return[(0,H.h)(mt(e),{...wt(e,t,c,{options:l,optionProps:r,optionGroups:i,optionGroupProps:s}),...Rt(e,t)})]},exportMethod:Qt},VxeRadio:{autofocus:".vxe-radio--input",renderItemContent:eo},VxeRadioGroup:{autofocus:".vxe-radio--input",renderItemContent:to},VxeCheckbox:{autofocus:".vxe-checkbox--input",renderItemContent:eo},VxeCheckboxGroup:{autofocus:".vxe-checkbox--input",renderItemContent:to},VxeSwitch:{autofocus:".vxe-switch--button",renderEdit:Ft,renderDefault:Ft,renderItemContent:Xt},$input:{autofocus:".vxe-input--inner",renderEdit:Nt,renderCell(e,t){const{props:o={}}=e,{row:n,column:l}=t,r=o.digits||c.input.digits;let i=a().get(n,l.property);if(i)switch(o.type){case"date":case"week":case"month":case"year":i=dt(i,o);break;case"float":i=a().toFixed(a().floor(i,r),r);break}return yt(e,t,i)},renderDefault:Nt,renderFilter:jt,defaultFilterMethod:Ht,renderItemContent:Yt},$textarea:{autofocus:".vxe-textarea--inner",renderItemContent:Yt},$button:{renderDefault:Pt,renderItemContent:Kt},$buttons:{renderDefault:Lt,renderItemContent:Zt},$select:{autofocus:".vxe-input--inner",renderEdit:qt,renderDefault:qt,renderCell(e,t){return yt(e,t,Ut(e,t))},renderFilter(e,t){const{column:o}=t,{options:n,optionProps:l,optionGroups:r,optionGroupProps:a}=e;return o.filters.map(((o,i)=>{const s=o.data;return(0,H.h)(ft(e),{key:i,...bt(e,t,s,{options:n,optionProps:l,optionGroups:r,optionGroupProps:a}),...kt(e,t,o)})}))},defaultFilterMethod:Ht,renderItemContent(e,t){const{data:o,property:n}=t,{options:l,optionProps:r,optionGroups:i,optionGroupProps:s}=e,c=a().get(o,n);return[(0,H.h)(ft(e),{...wt(e,t,c,{options:l,optionProps:r,optionGroups:i,optionGroupProps:s}),...Rt(e,t)})]},exportMethod:Qt},$radio:{autofocus:".vxe-radio--input",renderItemContent:oo},$checkbox:{autofocus:".vxe-checkbox--input",renderItemContent:oo},$switch:{autofocus:".vxe-switch--button",renderEdit:Nt,renderDefault:Nt,renderItemContent:Yt}},lo={mixin(e){return a().each(e,((e,t)=>lo.add(t,e))),lo},get(e){return no[e]||null},add(e,t){if(e&&t){const o=no[e];o?(a().each(t,((t,n)=>{a().eqNull(o[n])||o[n]===t||V("vxe.error.coverProp",[`Renderer.${e}`,n])})),Object.assign(o,t)):no[e]=t}return lo},delete(e){return delete no[e],lo}};class ro{constructor(){U(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];a().isFunction(t)&&(V("vxe.error.delProp",["commands -> callback","commandMethod"]),t={commandMethod:t});{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&V("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}const ao=new ro;Object.assign(ao,{_name:"Commands"});class io{constructor(){U(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];a().isFunction(t)&&(V("vxe.error.delProp",["menus -> callback","menuMethod"]),t={menuMethod:t});{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&V("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}const so=new io;Object.assign(so,{_name:"Menus"});class co{constructor(){U(this,"store",{})}mixin(e){return a().each(e,((e,t)=>{this.add(t,e)})),this}has(e){return!!this.get(e)}get(e){return this.store[e]}add(e,t){const o=this.store[e];{const n=a().keys(o);a().each(t,((t,o)=>{n.includes(o)&&V("vxe.error.coverProp",[e,o])}))}return this.store[e]=o?a().merge(o,t):t,this}delete(e){delete this.store[e]}forEach(e){a().objectEach(this.store,e)}}var uo=co;const po=new uo;Object.assign(po,{_name:"Validators"});const mo=new uo;function fo(e){let t=e||c.theme;if(t&&"default"!==t||(t="light"),c.theme=t,"undefined"!==typeof document){const e=document.documentElement;e&&e.setAttribute("data-vxe-ui-theme",t)}}function ho(){return c.theme}function go(e,t){const o=[];return a().objectEach(e,((e,n)=>{0!==e&&e!==t||o.push(n)})),o}const vo=[];function xo(e,t){return e&&e.install&&-1===vo.indexOf(e)&&(e.install(No,t),vo.push(e)),No}function bo(e,t){return c.i18n(e,t)}function wo(e,t){return e?a().toValueString(c.translate?c.translate(e,t):e):""}const Co=e=>(e&&(e.theme&&fo(e.theme),e.zIndex&&P.setCurrent(e.zIndex),a().merge(c,e)),No);class yo{get zIndex(){return Q()}get nextZIndex(){return J()}get exportTypes(){return go(c.export.types,1)}get importTypes(){return go(c.export.types,2)}}const To=new yo,Eo="v4",So=e=>(V("vxe.error.delFunc",["setup","setConfig"]),Co(e),c),ko=e=>(V("vxe.error.delFunc",["setup","setConfig"]),Co(e),c);function Ro(e){return e&&Object.assign(c.icon,e),No}const Oo={},Mo={};function $o(e){return Mo[e]||null}function Io(e){e&&e.name&&(Mo[e.name]=e)}const Do="4.6.25",Fo=Do,No={v:Eo,version:Do,tableVersion:Fo,setConfig:Co,setIcon:Ro,globalStore:Oo,interceptor:j,renderer:lo,commands:ao,formats:X,validators:po,menus:so,hooks:mo,use:xo,t:bo,_t:wo,setTheme:fo,getTheme:ho,getComponent:$o,config:ko,setup:So,globalConfs:To},Po=No;fo("light");var Lo=(0,H.defineComponent)({name:"VxeTableFilterPanel",props:{filterStore:Object},setup(e){const t=(0,H.inject)("$xetable",{}),{reactData:o,internalData:n,getComputeMaps:l}=t,{computeFilterOpts:r}=l(),a=(0,H.computed)((()=>{const{filterStore:t}=e;return t&&t.options.some((e=>e.checked))})),i=(t,o)=>{const{filterStore:n}=e;n.options.forEach((e=>{e._checked=o,e.checked=o})),n.isAllSelected=o,n.isIndeterminate=!1},s=o=>{const{filterStore:n}=e;n.options.forEach((e=>{e.checked=e._checked})),t.confirmFilterEvent(o)},u=(o,n,l)=>{const{filterStore:r}=e;r.options.forEach((e=>{e._checked=!1})),l._checked=n,t.checkFilterOptions(),s(o)},d=o=>{const{filterStore:n}=e;t.handleClearFilter(n.column),t.confirmFilterEvent(o)},p=(e,o,n)=>{n._checked=o,t.checkFilterOptions()},m=(t,o,n)=>{const{filterStore:l}=e;l.multiple?p(t,o,n):u(t,o,n)},f=(t,o)=>{const{filterStore:n}=e;n.multiple?i(t,o):d(t)},h={changeRadioOption:u,changeMultipleOption:p,changeAllOption:f,changeOption:m,confirmFilter:s,resetFilter:d},g=(o,l)=>{const{filterStore:r}=e,{column:a,multiple:i,maxHeight:s}=r,{slots:u}=a,d=u?u.filter:null,p=Object.assign({},n._currFilterParams,{$panel:h,$table:t});if(d)return[(0,H.h)("div",{class:"vxe-table--filter-template"},t.callSlot(d,p))];if(l&&l.renderFilter)return[(0,H.h)("div",{class:"vxe-table--filter-template"},lt(l.renderFilter(o,p)))];const g=i?r.isAllSelected:!r.options.some((e=>e._checked)),v=i&&r.isIndeterminate;return[(0,H.h)("ul",{class:"vxe-table--filter-header"},[(0,H.h)("li",{class:["vxe-table--filter-option",{"is--checked":g,"is--indeterminate":v}],title:c.i18n(i?"vxe.table.allTitle":"vxe.table.allFilter"),onClick:e=>{f(e,!r.isAllSelected)}},(i?[(0,H.h)("span",{class:["vxe-checkbox--icon",v?c.icon.TABLE_CHECKBOX_INDETERMINATE:g?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,H.h)("span",{class:"vxe-checkbox--label"},c.i18n("vxe.table.allFilter"))]))]),(0,H.h)("ul",{class:"vxe-table--filter-body",style:s?{maxHeight:`${s}px`}:{}},r.options.map((e=>{const t=e._checked,o=!1;return(0,H.h)("li",{class:["vxe-table--filter-option",{"is--checked":e._checked}],title:e.label,onClick:t=>{m(t,!e._checked,e)}},(i?[(0,H.h)("span",{class:["vxe-checkbox--icon",o?c.icon.TABLE_CHECKBOX_INDETERMINATE:t?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})]:[]).concat([(0,H.h)("span",{class:"vxe-checkbox--label"},oe(e.label,1))]))})))]},v=()=>{const{filterStore:t}=e,{column:o,multiple:n}=t,l=r.value,i=a.value,u=o.filterRender,p=u?No.renderer.get(u.name):null,m=!i&&!t.isAllSelected&&!t.isIndeterminate;return!n||p&&!1===p.showFilterFooter?[]:[(0,H.h)("div",{class:"vxe-table--filter-footer"},[(0,H.h)("button",{class:{"is--disabled":m},disabled:m,onClick:s},l.confirmButtonText||c.i18n("vxe.table.confirmFilter")),(0,H.h)("button",{onClick:d},l.resetButtonText||c.i18n("vxe.table.resetFilter"))])]},x=()=>{const{filterStore:l}=e,{initStore:r}=o,{column:a}=l,i=a?a.filterRender:null,s=i?No.renderer.get(i.name):null,c=s?s.filterClassName:"",u=Object.assign({},n._currFilterParams,{$panel:h,$table:t});return(0,H.h)("div",{class:["vxe-table--filter-wrapper","filter--prevent-default",ie(c,u),{"is--animat":t.props.animat,"is--multiple":l.multiple,"is--active":l.visible}],style:l.style},r.filter&&l.visible?g(i,s).concat(v()):[])};return x}});const Ao=["setFilter","clearFilter","getCheckedFilters"],Vo={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refTableBody:l,refTableFilter:r}=e.getRefMaps(),{computeFilterOpts:i,computeMouseOpts:s}=e.getComputeMaps(),c={checkFilterOptions(){const{filterStore:e}=o;e.isAllSelected=e.options.every((e=>e._checked)),e.isIndeterminate=!e.isAllSelected&&e.options.some((e=>e._checked))},triggerFilterEvent(t,a,i){const{initStore:s,filterStore:c}=o;if(c.column===a&&c.visible)c.visible=!1;else{const{target:o,pageX:u}=t,{visibleWidth:d}=he(),{filters:p,filterMultiple:m,filterRender:f}=a,h=f?No.renderer.get(f.name):null,g=a.filterRecoverMethod||(h?h.filterRecoverMethod:null);n._currFilterParams=i,Object.assign(c,{multiple:m,options:p,column:a,style:null}),c.options.forEach((t=>{const{_checked:o,checked:n}=t;t._checked=n,n||o===n||g&&g({option:t,column:a,$table:e})})),this.checkFilterOptions(),c.visible=!0,s.filter=!0,(0,H.nextTick)((()=>{const e=l.value,t=e.$el,n=r.value,i=n?n.$el:null;let s=0,p=0,m=null,f=null;i&&(s=i.offsetWidth,p=i.offsetHeight,m=i.querySelector(".vxe-table--filter-header"),f=i.querySelector(".vxe-table--filter-footer"));const h=s/2,g=10,v=t.clientWidth-s-g;let x,b;const w={top:`${o.offsetTop+o.offsetParent.offsetTop+o.offsetHeight+8}px`};let C=null;if(p>=t.clientHeight&&(C=Math.max(60,t.clientHeight-(f?f.offsetHeight:0)-(m?m.offsetHeight:0))),"left"===a.fixed?x=o.offsetLeft+o.offsetParent.offsetLeft-h:"right"===a.fixed?b=o.offsetParent.offsetWidth-o.offsetLeft+(o.offsetParent.offsetParent.offsetWidth-o.offsetParent.offsetLeft)-a.renderWidth-h:x=o.offsetLeft+o.offsetParent.offsetLeft-h-t.scrollLeft,x){const e=u+s-h+g-d;e>0&&(x-=e),w.left=`${Math.min(v,Math.max(g,x))}px`}else if(b){const e=u+s-h+g-d;e>0&&(b+=e),w.right=`${Math.max(g,b)}px`}c.style=w,c.maxHeight=C}))}e.dispatchEvent("filter-visible",{column:a,field:a.field,property:a.field,filterList:e.getCheckedFilters(),visible:c.visible},t)},handleClearFilter(t){if(t){const{filters:o,filterRender:n}=t;if(o){const l=n?No.renderer.get(n.name):null,r=t.filterResetMethod||(l?l.filterResetMethod:null);o.forEach((e=>{e._checked=!1,e.checked=!1,r||(e.data=a().clone(e.resetValue,!0))})),r&&r({options:o,column:t,$table:e})}}},confirmFilterEvent(n){const{mouseConfig:l}=t,{filterStore:r,scrollXLoad:a,scrollYLoad:c}=o,u=i.value,d=s.value,{column:p}=r,{field:m}=p,f=[],h=[];p.filters.forEach((e=>{e.checked&&(f.push(e.value),h.push(e.data))}));const g=e.getCheckedFilters(),v={$table:e,$event:n,column:p,field:m,property:m,values:f,datas:h,filters:g,filterList:g};u.remote||(e.handleTableData(!0),e.checkSelectionStatus()),l&&d.area&&e.handleFilterEvent&&e.handleFilterEvent(n,v),e.dispatchEvent("filter-change",v,n),e.closeFilter(),e.updateFooter().then((()=>{const{scrollXLoad:t,scrollYLoad:n}=o;if(a||t||c||n)return(a||t)&&e.updateScrollXSpace(),(c||n)&&e.updateScrollYSpace(),e.refreshScroll()})).then((()=>(e.updateCellAreas(),e.recalculate(!0)))).then((()=>{setTimeout((()=>e.recalculate()),50)}))}},u={openFilter(t){const o=Ae(e,t);if(o&&o.filters){const{elemStore:t}=n,{fixed:l}=o;return e.scrollToColumn(o).then((()=>{const e=t[`${l||"main"}-header-wrapper`]||t["main-header-wrapper"],n=e?e.value:null;if(n){const e=n.querySelector(`.vxe-header--column.${o.id} .vxe-filter--btn`);Re(e,"click")}}))}return(0,H.nextTick)()},setFilter(t,o){const n=Ae(e,t);return n&&n.filters&&(n.filters=je(o||[])),(0,H.nextTick)()},clearFilter(t){const{filterStore:l}=o,{tableFullColumn:r}=n,a=i.value;let s;return t?(s=Ae(e,t),s&&c.handleClearFilter(s)):r.forEach(c.handleClearFilter),t&&s===l.column||Object.assign(l,{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1}),a.remote?(0,H.nextTick)():e.updateData()},getCheckedFilters(){const{tableFullColumn:e}=n,t=[];return e.forEach((e=>{const{field:o,filters:n}=e,l=[],r=[];n&&n.length&&(n.forEach((e=>{e.checked&&(l.push(e.value),r.push(e.data))})),l.length&&t.push({column:e,field:o,property:o,values:l,datas:r}))})),t}};return{...u,...c}},setupGrid(e){return e.extendTableMethods(Ao)}};var Bo=Vo;let _o;const jo=(0,H.reactive)({modals:[],drawers:[]}),Ho=(0,H.defineComponent)({setup(){return()=>{const{modals:e,drawers:t}=jo;return[e.length?(0,H.h)("div",{class:"vxe-dynamics--modal"},e.map((e=>(0,H.h)((0,H.resolveComponent)("vxe-modal"),e)))):(0,H.createCommentVNode)(),t.length?(0,H.h)("div",{class:"vxe-dynamics--drawer"},t.map((e=>(0,H.h)((0,H.resolveComponent)("vxe-drawer"),e)))):(0,H.createCommentVNode)()]}}}),zo=(0,H.createApp)(Ho);function Wo(){_o||(_o=document.createElement("div"),_o.className="vxe-dynamics",document.body.appendChild(_o),zo.mount(_o))}const qo={Panel:Lo,install(e){No.hooks.add("$tableFilter",Bo),e.component(Lo.name,Lo)}},Uo=qo;zo.component(Lo.name,Lo);var Go=(0,H.defineComponent)({name:"VxeTableMenuPanel",setup(e,t){const o=a().uniqueId(),n=(0,H.inject)("$xetable",{}),{reactData:l}=n,r=(0,H.ref)(),i={refElem:r},s={xID:o,props:e,context:t,getRefMaps:()=>i},c=()=>{const{ctxMenuStore:e}=l,{computeMenuOpts:t}=n.getComputeMaps(),o=t.value;return(0,H.h)(H.Teleport,{to:"body",disabled:!1},[(0,H.h)("div",{ref:r,class:["vxe-table--context-menu-wrapper",o.className,{"is--visible":e.visible}],style:e.style},e.list.map(((t,o)=>t.every((e=>!1===e.visible))?(0,H.createCommentVNode)():(0,H.h)("ul",{class:"vxe-context-menu--option-wrapper",key:o},t.map(((t,l)=>{const r=t.children&&t.children.some((e=>!1!==e.visible));return!1===t.visible?null:(0,H.h)("li",{class:[t.className,{"link--disabled":t.disabled,"link--active":t===e.selected}],key:`${o}_${l}`},[(0,H.h)("a",{class:"vxe-context-menu--link",onClick(e){n.ctxMenuLinkEvent(e,t)},onMouseover(e){n.ctxMenuMouseoverEvent(e,t)},onMouseout(e){n.ctxMenuMouseoutEvent(e,t)}},[(0,H.h)("i",{class:["vxe-context-menu--link-prefix",t.prefixIcon]}),(0,H.h)("span",{class:"vxe-context-menu--link-content"},te(t.name)),(0,H.h)("i",{class:["vxe-context-menu--link-suffix",r?t.suffixIcon||"suffix--haschild":t.suffixIcon]})]),r?(0,H.h)("ul",{class:["vxe-table--context-menu-clild-wrapper",{"is--show":t===e.selected&&e.showChild}]},t.children.map(((r,a)=>!1===r.visible?null:(0,H.h)("li",{class:[r.className,{"link--disabled":r.disabled,"link--active":r===e.selectChild}],key:`${o}_${l}_${a}`},[(0,H.h)("a",{class:"vxe-context-menu--link",onClick(e){n.ctxMenuLinkEvent(e,r)},onMouseover(e){n.ctxMenuMouseoverEvent(e,t,r)},onMouseout(e){n.ctxMenuMouseoutEvent(e,t)}},[(0,H.h)("i",{class:["vxe-context-menu--link-prefix",r.prefixIcon]}),(0,H.h)("span",{class:"vxe-context-menu--link-content"},te(r.name))])])))):null])}))))))])};return s.renderVN=c,s},render(){return this.renderVN()}});const Xo={F2:"F2",ESCAPE:"Escape",ENTER:"Enter",TAB:"Tab",DELETE:"Delete",BACKSPACE:"Backspace",SPACEBAR:" ",CONTEXT_MENU:"ContextMenu",ARROW_UP:"ArrowUp",ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown"},Yo={" ":"Spacebar",Apps:Xo.CONTEXT_MENU,Del:Xo.DELETE,Up:Xo.ARROW_UP,Down:Xo.ARROW_DOWN,Left:Xo.ARROW_LEFT,Right:Xo.ARROW_RIGHT},Ko=ae.firefox?"DOMMouseScroll":"mousewheel",Zo=[],Jo=(e,t)=>{const{key:o}=e;return t=t.toLowerCase(),!!o&&(t===o.toLowerCase()||!(!Yo[o]||Yo[o].toLowerCase()!==t))};function Qo(e){const t=e.type===Ko;Zo.forEach((({type:o,cb:n})=>{e.cancelBubble||(o===e.type||t&&"mousewheel"===o)&&n(e)}))}const en={on(e,t,o){Zo.push({comp:e,type:t,cb:o})},off(e,t){a().remove(Zo,(o=>o.comp===e&&o.type===t))},trigger:Qo,eqKeypad(e,t){const{key:o}=e;return t.toLowerCase()===o.toLowerCase()}};ae.isDoc&&(ae.msie||(window.addEventListener("copy",Qo,!1),window.addEventListener("cut",Qo,!1),window.addEventListener("paste",Qo,!1)),document.addEventListener("keydown",Qo,!1),document.addEventListener("contextmenu",Qo,!1),window.addEventListener("mousedown",Qo,!1),window.addEventListener("blur",Qo,!1),window.addEventListener("resize",Qo,!1),window.addEventListener(Ko,a().throttle(Qo,100,{leading:!0,trailing:!1}),{passive:!0,capture:!1}));const tn=["closeMenu"],on={setupTable(e){const{xID:t,props:o,reactData:n,internalData:l}=e,{refElem:r,refTableFilter:i,refTableMenu:s}=e.getRefMaps(),{computeMouseOpts:c,computeIsMenu:u,computeMenuOpts:d}=e.getComputeMaps();let p={},m={};const f=(t,o,r)=>{const{ctxMenuStore:a}=n,i=u.value,c=d.value,m=c[o],f=c.visibleMethod;if(m){const{options:o,disabled:n}=m;n?t.preventDefault():i&&o&&o.length&&(r.options=o,e.preventEvent(t,"event.showMenu",r,(()=>{if(!f||f(r)){t.preventDefault(),e.updateZindex();const{scrollTop:n,scrollLeft:i,visibleHeight:c,visibleWidth:u}=he();let d=t.clientY+n,p=t.clientX+i;const m=()=>{l._currMenuParams=r,Object.assign(a,{visible:!0,list:o,selected:null,selectChild:null,showChild:!1,style:{zIndex:l.tZindex,top:`${d}px`,left:`${p}px`}}),(0,H.nextTick)((()=>{const e=s.value,t=e.getRefMaps().refElem.value,o=t.clientHeight,l=t.clientWidth,{boundingTop:r,boundingLeft:m}=Te(t),f=r+o-c,h=m+l-u;f>-10&&(a.style.top=`${Math.max(n+2,d-o-2)}px`),h>-10&&(a.style.left=`${Math.max(i+2,p-l-2)}px`)}))},{keyboard:f,row:h,column:g}=r;f&&h&&g?e.scrollToRow(h,g).then((()=>{const t=e.getCell(h,g);if(t){const{boundingTop:e,boundingLeft:o}=Te(t);d=e+n+Math.floor(t.offsetHeight/2),p=o+i+Math.floor(t.offsetWidth/2)}m()})):m()}else p.closeMenu()})))}e.closeFilter()};return p={closeMenu(){return Object.assign(n.ctxMenuStore,{visible:!1,selected:null,selectChild:null,showChild:!1}),(0,H.nextTick)()}},m={moveCtxMenu(e,t,o,n,l,r){let i;const s=a().findIndexOf(r,(e=>t[o]===e));if(n)l&&ee(t.selected)?t.showChild=!0:(t.showChild=!1,t.selectChild=null);else if(Jo(e,Xo.ARROW_UP)){for(let e=s-1;e>=0;e--)if(!1!==r[e].visible){i=r[e];break}t[o]=i||r[r.length-1]}else if(Jo(e,Xo.ARROW_DOWN)){for(let e=s+1;e<r.length;e++)if(!1!==r[e].visible){i=r[e];break}t[o]=i||r[0]}else t[o]&&(Jo(e,Xo.ENTER)||Jo(e,Xo.SPACEBAR))&&m.ctxMenuLinkEvent(e,t[o])},handleOpenMenuEvent:f,handleGlobalContextmenuEvent(a){const{mouseConfig:u,menuConfig:m}=o,{editStore:h,ctxMenuStore:g}=n,{visibleColumn:v}=l,x=i.value,b=s.value,w=c.value,C=d.value,y=r.value,{selected:T}=h,E=["header","body","footer"];if(Y(m)){if(g.visible&&b&&Ce(a,b.getRefMaps().refElem.value).flag)return void a.preventDefault();if(l._keyCtx){const t="body",o={type:t,$table:e,keyboard:!0,columns:v.slice(0),$event:a};if(u&&w.area){const n=e.getActiveCellArea();if(n&&n.row&&n.column)return o.row=n.row,o.column=n.column,void f(a,t,o)}else if(u&&w.selected&&T.row&&T.column)return o.row=T.row,o.column=T.column,void f(a,t,o)}for(let o=0;o<E.length;o++){const n=E[o],l=Ce(a,y,`vxe-${n}--column`,(e=>e.parentNode.parentNode.parentNode.getAttribute("xid")===t)),r={type:n,$table:e,columns:v.slice(0),$event:a};if(l.flag){const t=l.targetElem,o=e.getColumnNode(t),i=o?o.item:null;let s=`${n}-`;if(i&&Object.assign(r,{column:i,columnIndex:e.getColumnIndex(i),cell:t}),"body"===n){const o=e.getRowNode(t.parentNode),n=o?o.item:null;s="",n&&(r.row=n,r.rowIndex=e.getRowIndex(n))}const c=`${s}cell-menu`;return f(a,n,r),void e.dispatchEvent(c,r,a)}if(Ce(a,y,`vxe-table--${n}-wrapper`,(e=>e.getAttribute("xid")===t)).flag)return void("cell"===C.trigger?a.preventDefault():f(a,n,r))}}x&&!Ce(a,x.$el).flag&&e.closeFilter(),p.closeMenu()},ctxMenuMouseoverEvent(e,t,o){const l=e.currentTarget,{ctxMenuStore:r}=n;e.preventDefault(),e.stopPropagation(),r.selected=t,r.selectChild=o,o||(r.showChild=ee(t),r.showChild&&(0,H.nextTick)((()=>{const e=l.nextElementSibling;if(e){const{boundingTop:t,boundingLeft:o,visibleHeight:n,visibleWidth:r}=Te(l),a=t+l.offsetHeight,i=o+l.offsetWidth;let s="",c="";i+e.offsetWidth>r-10&&(s="auto",c=`${l.offsetWidth}px`);let u="",d="";a+e.offsetHeight>n-10&&(u="auto",d="0"),e.style.left=s,e.style.right=c,e.style.top=u,e.style.bottom=d}})))},ctxMenuMouseoutEvent(e,t){const{ctxMenuStore:o}=n;t.children||(o.selected=null),o.selectChild=null},ctxMenuLinkEvent(t,o){if(!o.disabled&&(o.code||!o.children||!o.children.length)){const n=No.menus.get(o.code),r=Object.assign({},l._currMenuParams,{menu:o,$table:e,$grid:e.xegrid,$event:t}),a=n?n.tableMenuMethod||n.menuMethod:null;a&&a(r,t),e.dispatchEvent("menu-click",r,t),p.closeMenu()}}},{...p,...m}},setupGrid(e){return e.extendTableMethods(tn)}};var nn=on;const ln={Panel:Go,install(e){No.hooks.add("$tableMenu",nn),e.component(Go.name,Go)}},rn=ln;zo.component(Go.name,Go);const an=["insert","insertAt","insertNextAt","remove","removeCheckboxRow","removeRadioRow","removeCurrentRow","getRecordset","getInsertRecords","getRemoveRecords","getUpdateRecords","getEditRecord","getActiveRecord","getSelectedCell","clearEdit","clearActived","clearSelected","isEditByRow","isActiveByRow","setEditRow","setActiveRow","setEditCell","setActiveCell","setSelectCell"],sn={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refElem:l}=e.getRefMaps(),{computeMouseOpts:r,computeEditOpts:i,computeCheckboxOpts:s,computeTreeOpts:u}=e.getComputeMaps();let d={},p={};const m=(e,t)=>{const{model:o,editRender:n}=t;n&&(o.value=ze(e,t),o.update=!1)},f=(e,t)=>{const{model:o,editRender:n}=t;n&&o.update&&(We(e,t,o.value),o.update=!1,o.value=null)},h=()=>{const e=l.value;if(e){const t=e.querySelector(".col--selected");t&&me(t,"col--selected")}};function g(){const{editStore:e,tableColumn:t}=o,n=i.value,{actived:l}=e,{row:r,column:a}=l;(r||a)&&("row"===n.mode?t.forEach((e=>f(r,e))):f(r,a))}function v(t,o){const{tableFullTreeData:l,afterFullData:r,fullDataRowIdData:i,fullAllDataRowIdData:s}=n,c=u.value,{rowField:d,parentField:p,mapChildrenField:m}=c,f=c.children||c.childrenField,h=o?"push":"unshift";t.forEach((t=>{const o=t[p],n=Le(e,t),c=o?a().findTree(l,(e=>o===e[d]),{children:m}):null;if(c){const{item:o}=c,l=s[Le(e,o)],r=l?l.level:0;let u=o[f],d=o[m];a().isArray(u)||(u=o[f]=[]),a().isArray(d)||(d=o[f]=[]),u[h](t),d[h](t);const p={row:t,rowid:n,seq:-1,index:-1,_index:-1,$index:-1,items:u,parent:o,level:r+1};i[n]=p,s[n]=p}else{o&&V("vxe.error.unableInsert"),r[h](t),l[h](t);const e={row:t,rowid:n,seq:-1,index:-1,_index:-1,$index:-1,items:l,parent:null,level:0};i[n]=e,s[n]=e}}))}const x=(l,r,i)=>{const{treeConfig:s}=t,{mergeList:c,editStore:d}=o,{tableFullTreeData:p,afterFullData:m,tableFullData:f,fullDataRowIdData:h,fullAllDataRowIdData:g}=n,x=u.value,{transform:b,rowField:w,mapChildrenField:C}=x,y=x.children||x.childrenField;a().isArray(l)||(l=[l]);const T=(0,H.reactive)(e.defineField(l.map((e=>Object.assign(s&&b?{[C]:[],[y]:[]}:{},e)))));if(a().eqNull(r))s&&b?v(T,!1):(m.unshift(...T),f.unshift(...T),c.forEach((e=>{const{row:t}=e;t>0&&(e.row=t+T.length)})));else if(-1===r)s&&b?v(T,!0):(m.push(...T),f.push(...T),c.forEach((e=>{const{row:t,rowspan:o}=e;t+o>m.length&&(e.rowspan=o+T.length)})));else if(s&&b){const t=a().findTree(p,(e=>r[w]===e[w]),{children:C});if(t){const{parent:o}=t,n=o?o[C]:p,l=g[Le(e,o)],s=l?l.level:0;if(T.forEach(((l,r)=>{const a=Le(e,l);l[x.parentField]&&o&&l[x.parentField]!==o[w]&&B("vxe.error.errProp",[`${x.parentField}=${l[x.parentField]}`,`${x.parentField}=${o[w]}`]),o&&(l[x.parentField]=o[w]);let c=t.index+r;i&&(c+=1),n.splice(c,0,l);const u={row:l,rowid:a,seq:-1,index:-1,_index:-1,$index:-1,items:n,parent:o,level:s+1};h[a]=u,g[a]=u})),o){const e=a().findTree(p,(e=>r[w]===e[w]),{children:y});if(e){const t=e.items;let o=e.index;i&&(o+=1),t.splice(o,0,...T)}}}else V("vxe.error.unableInsert"),v(T,!0)}else{if(s)throw new Error(L("vxe.error.noTree",["insert"]));let t=-1;if(a().isNumber(r)?r<m.length&&(t=r):t=e.findRowIndexOf(m,r),i&&(t=Math.min(m.length,t+1)),-1===t)throw new Error(B("vxe.error.unableInsert"));m.splice(t,0,...T),f.splice(e.findRowIndexOf(f,r),0,...T),c.forEach((e=>{const{row:o,rowspan:n}=e;o>t?e.row=o+T.length:o+n>t&&(e.rowspan=n+T.length)}))}const{insertMaps:E}=d;return T.forEach((t=>{const o=Le(e,t);E[o]=t})),e.cacheRowMap(),e.updateScrollYStatus(),e.handleTableData(s&&b),s&&b||e.updateAfterDataIndex(),e.updateFooter(),e.checkSelectionStatus(),o.scrollYLoad&&e.updateScrollYSpace(),(0,H.nextTick)().then((()=>(e.updateCellAreas(),e.recalculate()))).then((()=>({row:T.length?T[T.length-1]:null,rows:T})))};return d={insert(e){return x(e,null)},insertAt(e,t){return x(e,t)},insertNextAt(e,t){return x(e,t,!0)},remove(l){const{treeConfig:r}=t,{mergeList:i,editStore:c,selectCheckboxMaps:p}=o,{tableFullTreeData:m,afterFullData:f,tableFullData:h}=n,g=s.value,v=u.value,{transform:x,mapChildrenField:b}=v,w=v.children||v.childrenField,{actived:C,removeMaps:y,insertMaps:T}=c,{checkField:E}=g;let S=[];if(l?a().isArray(l)||(l=[l]):l=h,l.forEach((t=>{if(!e.isInsertByRow(t)){const o=Le(e,t);y[o]=t}})),!E){const t={...p};l.forEach((o=>{const n=Le(e,o);t[n]&&delete t[n]})),o.selectCheckboxMaps=t}return h===l?(l=S=h.slice(0),n.tableFullData=[],n.afterFullData=[],e.clearMergeCells()):r&&x?l.forEach((t=>{const o=Le(e,t),n=a().findTree(m,(t=>o===Le(e,t)),{children:b});if(n){const e=n.items.splice(n.index,1);S.push(e[0])}const l=a().findTree(m,(t=>o===Le(e,t)),{children:w});l&&l.items.splice(l.index,1);const r=e.findRowIndexOf(f,t);r>-1&&f.splice(r,1)})):l.forEach((t=>{const o=e.findRowIndexOf(h,t);if(o>-1){const e=h.splice(o,1);S.push(e[0])}const n=e.findRowIndexOf(f,t);n>-1&&(i.forEach((e=>{const{row:t,rowspan:o}=e;t>n?e.row=t-1:t+o>n&&(e.rowspan=o-1)})),f.splice(n,1))})),C.row&&e.findRowIndexOf(l,C.row)>-1&&d.clearEdit(),l.forEach((t=>{const o=Le(e,t);T[o]&&delete T[o]})),e.updateFooter(),e.cacheRowMap(),e.handleTableData(r&&x),r&&x||e.updateAfterDataIndex(),e.checkSelectionStatus(),o.scrollYLoad&&e.updateScrollYSpace(),(0,H.nextTick)().then((()=>(e.updateCellAreas(),e.recalculate()))).then((()=>({row:S.length?S[S.length-1]:null,rows:S})))},removeCheckboxRow(){return d.remove(e.getCheckboxRecords()).then((t=>(e.clearCheckboxRow(),t)))},removeRadioRow(){const t=e.getRadioRecord();return d.remove(t||[]).then((t=>(e.clearRadioRow(),t)))},removeCurrentRow(){const t=e.getCurrentRecord();return d.remove(t||[]).then((t=>(e.clearCurrentRow(),t)))},getRecordset(){return{insertRecords:d.getInsertRecords(),removeRecords:d.getRemoveRecords(),updateRecords:d.getUpdateRecords(),pendingRecords:e.getPendingRecords()}},getInsertRecords(){const{editStore:e}=o,{fullAllDataRowIdData:t}=n,{insertMaps:l}=e,r=[];return a().each(l,((e,o)=>{t[o]&&r.push(e)})),r},getRemoveRecords(){const{editStore:e}=o,{removeMaps:t}=e,n=[];return a().each(t,(e=>{n.push(e)})),n},getUpdateRecords(){const{keepSource:o,treeConfig:l}=t,{tableFullData:r}=n,i=u.value;return o?(g(),l?a().filterTree(r,(t=>e.isUpdateByRow(t)),i):r.filter((t=>e.isUpdateByRow(t)))):[]},getActiveRecord(){return V("vxe.error.delFunc",["getActiveRecord","getEditRecord"]),this.getEditRecord()},getEditRecord(){const{editStore:t}=o,{afterFullData:r}=n,a=l.value,{args:i,row:s}=t.actived;return i&&e.findRowIndexOf(r,s)>-1&&a.querySelectorAll(".vxe-body--column.col--active").length?Object.assign({},i):null},getSelectedCell(){const{editStore:e}=o,{args:t,column:n}=e.selected;return t&&n?Object.assign({},t):null},clearActived(e){return V("vxe.error.delFunc",["clearActived","clearEdit"]),this.clearEdit(e)},clearEdit(t){const{editStore:n}=o,{actived:l,focused:r}=n,{row:a,column:i}=l;return(a||i)&&(g(),l.args=null,l.row=null,l.column=null,e.updateFooter(),e.dispatchEvent("edit-closed",{row:a,rowIndex:e.getRowIndex(a),$rowIndex:e.getVMRowIndex(a),column:i,columnIndex:e.getColumnIndex(i),$columnIndex:e.getVMColumnIndex(i)},t||null)),"obsolete"===c.cellVaildMode&&e.clearValidate?e.clearValidate():(r.row=null,r.column=null,(0,H.nextTick)())},clearSelected(){const{editStore:e}=o,{selected:t}=e;return t.row=null,t.column=null,h(),(0,H.nextTick)()},isActiveByRow(e){return V("vxe.error.delFunc",["isActiveByRow","isEditByRow"]),this.isEditByRow(e)},isEditByRow(e){const{editStore:t}=o;return t.actived.row===e},setActiveRow(e){return V("vxe.error.delFunc",["setActiveRow","setEditRow"]),d.setEditRow(e)},setEditRow(t,o){const{visibleColumn:l}=n;let r=a().find(l,(e=>Y(e.editRender)));return o&&(r=a().isString(o)?e.getColumnByField(o):o),e.setEditCell(t,r)},setActiveCell(e,t){return V("vxe.error.delFunc",["setActiveCell","setEditCell"]),d.setEditCell(e,t)},setEditCell(o,l){const{editConfig:r}=t,i=a().isString(l)?e.getColumnByField(l):l;return o&&i&&Y(r)&&Y(i.editRender)?e.scrollToRow(o,i).then((()=>{const t=e.getCell(o,i);return t&&(p.handleActived({row:o,rowIndex:e.getRowIndex(o),column:i,columnIndex:e.getColumnIndex(i),cell:t,$table:e}),n._lastCallTime=Date.now()),(0,H.nextTick)()})):(0,H.nextTick)()},setSelectCell(t,n){const{tableData:l}=o,r=i.value,s=a().isString(n)?e.getColumnByField(n):n;if(t&&s&&"manual"!==r.trigger){const o=e.findRowIndexOf(l,t);if(o>-1&&s){const n=e.getCell(t,s),l={row:t,rowIndex:o,column:s,columnIndex:e.getColumnIndex(s),cell:n};e.handleSelected(l,{})}}return(0,H.nextTick)()}},p={handleActived(n,l){const{editConfig:r,mouseConfig:a}=t,{editStore:s,tableColumn:c}=o,u=i.value,{mode:f}=u,{actived:h,focused:g}=s,{row:v,column:x}=n,{editRender:b}=x,w=n.cell||e.getCell(v,x),C=u.beforeEditMethod||u.activeMethod;if(n.cell=w,w&&Y(r)&&Y(b)&&!e.hasPendingByRow(v)){if(h.row!==v||"cell"===f&&h.column!==x){let t="edit-disabled";if(!C||C({...n,$table:e,$grid:e.xegrid})){a&&(d.clearSelected(),e.clearCellAreas&&(e.clearCellAreas(),e.clearCopyCellArea())),e.closeTooltip(),h.column&&d.clearEdit(l),t="edit-activated",x.renderHeight=w.offsetHeight,h.args=n,h.row=v,h.column=x,"row"===f?c.forEach((e=>m(v,e))):m(v,x);const o=u.afterEditMethod;(0,H.nextTick)((()=>{p.handleFocus(n,l),o&&o({...n,$table:e,$grid:e.xegrid})}))}e.dispatchEvent(t,{row:v,rowIndex:e.getRowIndex(v),$rowIndex:e.getVMRowIndex(v),column:x,columnIndex:e.getColumnIndex(x),$columnIndex:e.getVMColumnIndex(x)},l),"edit-activated"===t&&e.dispatchEvent("edit-actived",{row:v,rowIndex:e.getRowIndex(v),$rowIndex:e.getVMRowIndex(v),column:x,columnIndex:e.getColumnIndex(x),$columnIndex:e.getVMColumnIndex(x)},l)}else{const{column:t}=h;if(a&&(d.clearSelected(),e.clearCellAreas&&(e.clearCellAreas(),e.clearCopyCellArea())),t!==x){const{model:o}=t;o.update&&We(v,t,o.value),e.clearValidate&&e.clearValidate(v,x)}x.renderHeight=w.offsetHeight,h.args=n,h.column=x,setTimeout((()=>{p.handleFocus(n,l)}))}g.column=null,g.row=null,e.focus()}return(0,H.nextTick)()},handleFocus(t){const{row:o,column:n,cell:l}=t,{editRender:r}=n;if(Y(r)){const i=lo.get(r.name);let s,{autofocus:c,autoselect:u}=r;if(!c&&i&&(c=i.autofocus),!u&&i&&(u=i.autoselect),a().isFunction(c)?s=c.call(this,t):c&&(s=!0===c?l.querySelector("input,textarea"):l.querySelector(c),s&&s.focus()),s){if(u)s.select();else if(ae.msie){const e=s.createTextRange();e.collapse(!1),e.select()}}else e.scrollToRow(o,n)}},handleSelected(n,l){const{mouseConfig:a}=t,{editStore:s}=o,c=r.value,u=i.value,{actived:m,selected:f}=s,{row:h,column:g}=n,v=a&&c.selected,x=()=>(!v||f.row===h&&f.column===g||(m.row!==h||"cell"===u.mode&&m.column!==g)&&(d.clearEdit(l),d.clearSelected(),e.clearCellAreas&&(e.clearCellAreas(),e.clearCopyCellArea()),f.args=n,f.row=h,f.column=g,v&&p.addCellSelectedClass(),e.focus(),l&&e.dispatchEvent("cell-selected",n,l)),(0,H.nextTick)());return x()},addCellSelectedClass(){const{editStore:t}=o,{selected:n}=t,{row:l,column:r}=n;if(h(),l&&r){const t=e.getCell(l,r);t&&fe(t,"col--selected")}}},{...d,...p}},setupGrid(e){return e.extendTableMethods(an)}};var cn=sn;const un={install(){No.hooks.add("$tableEdit",cn)}},dn=un;function pn(e){const t=(0,H.inject)("xesize",null),o=(0,H.computed)((()=>e.size||(t?t.value:null)));return(0,H.provide)("xesize",o),o}var mn=(0,H.defineComponent)({name:"VxeButton",props:{type:String,mode:String,className:[String,Function],popupClassName:[String,Function],size:{type:String,default:()=>c.button.size||c.size},name:[String,Number],content:String,placement:String,status:String,title:String,icon:String,round:Boolean,circle:Boolean,disabled:Boolean,loading:Boolean,destroyOnClose:Boolean,transfer:{type:Boolean,default:()=>c.button.transfer}},emits:["click","mouseenter","mouseleave","dropdown-click"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.reactive)({inited:!1,showPanel:!1,animatVisible:!1,panelIndex:0,panelStyle:{},panelPlacement:""}),s={showTime:null},u=(0,H.ref)(),d=(0,H.ref)(),p=(0,H.ref)(),m={refElem:u},f={xID:l,props:e,context:t,reactData:i,internalData:s,getRefMaps:()=>m},h=(0,H.inject)("$xebuttongroup",null);let g={};const v=(0,H.computed)((()=>{const{type:t}=e;return!!t&&["submit","reset","button"].indexOf(t)>-1})),x=(0,H.computed)((()=>{const{type:t,mode:o}=e;return"text"===o||"text"===t||h&&"text"===h.props.mode?"text":"button"})),b=(0,H.computed)((()=>{const{status:t}=e;return t||(h?h.props.status:"")})),w=(0,H.computed)((()=>{const{round:t}=e;return t||!!h&&h.props.round})),C=(0,H.computed)((()=>{const{circle:t}=e;return t||!!h&&h.props.circle})),y=()=>{i.panelIndex<Q()&&(i.panelIndex=J())},T=()=>(0,H.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n}=i,l=d.value,r=p.value;if(r&&l){const e=l.offsetHeight,a=l.offsetWidth,s=r.offsetHeight,c=r.offsetWidth,u=5,d={zIndex:n},{top:p,left:m,boundingTop:f,visibleHeight:h,visibleWidth:g}=Te(l);let v="bottom";if(t){let t=m+a-c,n=p+e;"top"===o?(v="top",n=p-s):o||(f+e+s+u>h&&(v="top",n=p-s),n<u&&(v="bottom",n=p+e)),t+c+u>g&&(t-=t+c+u-g),t<u&&(t=u),Object.assign(d,{left:`${t}px`,right:"auto",top:`${n}px`,minWidth:`${a}px`})}else"top"===o?(v="top",d.bottom=`${e}px`):o||f+e+s>h&&f-e-s>u&&(v="top",d.bottom=`${e}px`);return i.panelStyle=d,i.panelPlacement=v,(0,H.nextTick)()}})),E=t=>{h?h.handleClick({name:e.name},t):g.dispatchEvent("click",{$event:t},t)},S=e=>{const t=0===e.button;t&&e.stopPropagation()},k=e=>{const t=e.currentTarget,o=p.value,{flag:n,targetElem:l}=Ce(e,t,"vxe-button");n&&(o&&(o.dataset.active="N"),i.showPanel=!1,setTimeout((()=>{o&&"Y"===o.dataset.active||(i.animatVisible=!1)}),350),g.dispatchEvent("dropdown-click",{name:l.getAttribute("name"),$event:e},e))},R=()=>{const e=p.value;e&&(e.dataset.active="Y",i.animatVisible=!0,setTimeout((()=>{"Y"===e.dataset.active&&(i.showPanel=!0,y(),T(),setTimeout((()=>{i.showPanel&&T()}),50))}),20))},O=e=>{const t=p.value;t&&(t.dataset.active="Y",i.inited||(i.inited=!0),s.showTime=setTimeout((()=>{"Y"===t.dataset.active?R():i.animatVisible=!1}),250)),$(e)},M=e=>{D(),I(e)},$=e=>{n("mouseenter",{$event:e})},I=e=>{n("mouseleave",{$event:e})},D=()=>{const e=p.value;clearTimeout(s.showTime),e?(e.dataset.active="N",setTimeout((()=>{"Y"!==e.dataset.active&&(i.showPanel=!1,setTimeout((()=>{"Y"!==e.dataset.active&&(i.animatVisible=!1)}),350))}),100)):(i.animatVisible=!1,i.showPanel=!1)},F=()=>{D()},N=()=>{const{content:t,icon:n,loading:l}=e,r=[];return l?r.push((0,H.h)("i",{class:["vxe-button--loading-icon",c.icon.BUTTON_LOADING]})):o.icon?r.push((0,H.h)("span",{class:"vxe-button--custom-icon"},o.icon({}))):n&&r.push((0,H.h)("i",{class:["vxe-button--icon",n]})),o.default?r.push((0,H.h)("span",{class:"vxe-button--content"},o.default({}))):t&&r.push((0,H.h)("span",{class:"vxe-button--content"},te(t))),r};g={dispatchEvent(e,t,o){n(e,Object.assign({$button:f,$event:o},t))},focus(){const e=d.value;return e.focus(),(0,H.nextTick)()},blur(){const e=d.value;return e.blur(),(0,H.nextTick)()}},Object.assign(f,g),(0,H.onMounted)((()=>{en.on(f,"mousewheel",(e=>{const t=p.value;i.showPanel&&!Ce(e,t).flag&&D()}))})),(0,H.onUnmounted)((()=>{en.off(f,"mousewheel")}));const P=()=>{const{className:t,popupClassName:n,transfer:l,title:s,type:m,destroyOnClose:h,name:g,disabled:y,loading:T}=e,{inited:D,showPanel:P}=i,L=v.value,A=x.value,V=b.value,B=w.value,_=C.value,j=r.value;return o.dropdowns?(0,H.h)("div",{ref:u,class:["vxe-button--dropdown",t?a().isFunction(t)?t({$button:f}):t:"",{[`size--${j}`]:j,"is--active":P}]},[(0,H.h)("button",{ref:d,class:["vxe-button",`type--${A}`,{[`size--${j}`]:j,[`theme--${V}`]:V,"is--round":B,"is--circle":_,"is--disabled":y||T,"is--loading":T}],title:s,name:g,type:L?m:"button",disabled:y||T,onMouseenter:O,onMouseleave:M,onClick:E},N().concat([(0,H.h)("i",{class:`vxe-button--dropdown-arrow ${c.icon.BUTTON_DROPDOWN}`})])),(0,H.h)(H.Teleport,{to:"body",disabled:!l||!D},[(0,H.h)("div",{ref:p,class:["vxe-button--dropdown-panel",n?a().isFunction(n)?n({$button:f}):n:"",{[`size--${j}`]:j,"animat--leave":i.animatVisible,"animat--enter":P}],placement:i.panelPlacement,style:i.panelStyle},D?[(0,H.h)("div",{class:"vxe-button--dropdown-wrapper",onMousedown:S,onClick:k,onMouseenter:R,onMouseleave:F},h&&!P?[]:o.dropdowns({}))]:[])])]):(0,H.h)("button",{ref:d,class:["vxe-button",`type--${A}`,t?a().isFunction(t)?t({$button:f}):t:"",{[`size--${j}`]:j,[`theme--${V}`]:V,"is--round":B,"is--circle":_,"is--disabled":y||T,"is--loading":T}],title:s,name:g,type:L?m:"button",disabled:y||T,onClick:E,onMouseenter:$,onMouseleave:I},N())};return f.renderVN=P,f},render(){return this.renderVN()}}),fn=(0,H.defineComponent)({name:"VxeLoading",props:{modelValue:Boolean,icon:String,text:String},setup(e,{slots:t}){const o=(0,H.computed)((()=>e.icon||c.icon.LOADING)),n=(0,H.computed)((()=>{const t=c.loadingText;return e.text||(null===t?t:c.i18n("vxe.loading.text"))}));return()=>{const l=o.value,r=n.value;return(0,H.h)("div",{class:["vxe-loading",{"is--visible":e.modelValue}]},t.default?[(0,H.h)("div",{class:"vxe-loading--wrapper"},t.default({}))]:[(0,H.h)("div",{class:"vxe-loading--chunk"},[l?(0,H.h)("i",{class:l}):(0,H.h)("div",{class:"vxe-loading--spinner"}),r?(0,H.h)("div",{class:"vxe-loading--text"},`${r}`):null])])}}});const hn=Object.assign(fn,{install(e){e.component(fn.name,fn)}});var gn=hn;const vn=[],xn=[];var bn=(0,H.defineComponent)({name:"VxeModal",props:{modelValue:Boolean,id:String,type:{type:String,default:"modal"},loading:{type:Boolean,default:null},status:String,iconStatus:String,className:String,top:{type:[Number,String],default:()=>c.modal.top},position:[String,Object],title:String,duration:{type:[Number,String],default:()=>c.modal.duration},message:[Number,String],content:[Number,String],showCancelButton:{type:Boolean,default:null},cancelButtonText:{type:String,default:()=>c.modal.cancelButtonText},showConfirmButton:{type:Boolean,default:()=>c.modal.showConfirmButton},confirmButtonText:{type:String,default:()=>c.modal.confirmButtonText},lockView:{type:Boolean,default:()=>c.modal.lockView},lockScroll:Boolean,mask:{type:Boolean,default:()=>c.modal.mask},maskClosable:{type:Boolean,default:()=>c.modal.maskClosable},escClosable:{type:Boolean,default:()=>c.modal.escClosable},resize:Boolean,showHeader:{type:Boolean,default:()=>c.modal.showHeader},showFooter:{type:Boolean,default:()=>c.modal.showFooter},showZoom:Boolean,showClose:{type:Boolean,default:()=>c.modal.showClose},dblclickZoom:{type:Boolean,default:()=>c.modal.dblclickZoom},width:[Number,String],height:[Number,String],minWidth:{type:[Number,String],default:()=>c.modal.minWidth},minHeight:{type:[Number,String],default:()=>c.modal.minHeight},zIndex:Number,marginSize:{type:[Number,String],default:()=>c.modal.marginSize},fullscreen:Boolean,draggable:{type:Boolean,default:()=>c.modal.draggable},remember:{type:Boolean,default:()=>c.modal.remember},destroyOnClose:{type:Boolean,default:()=>c.modal.destroyOnClose},showTitleOverflow:{type:Boolean,default:()=>c.modal.showTitleOverflow},transfer:{type:Boolean,default:()=>c.modal.transfer},storage:{type:Boolean,default:()=>c.modal.storage},storageKey:{type:String,default:()=>c.modal.storageKey},animat:{type:Boolean,default:()=>c.modal.animat},size:{type:String,default:()=>c.modal.size||c.size},beforeHideMethod:{type:Function,default:()=>c.modal.beforeHideMethod},slots:Object},emits:["update:modelValue","show","hide","before-hide","close","confirm","cancel","zoom","resize","move"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.reactive)({inited:!1,visible:!1,contentVisible:!1,modalTop:0,modalZindex:0,zoomLocat:null,firstOpen:!0}),s=(0,H.ref)(),u=(0,H.ref)(),d=(0,H.ref)(),p=(0,H.ref)(),m={refElem:s},f={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>m};let h={};const g=(0,H.computed)((()=>"message"===e.type)),v=()=>{const e=u.value;return e},x=()=>{const{width:t,height:o}=e,n=v();return n.style.width=`${t?isNaN(t)?t:`${t}px`:""}`,n.style.height=`${o?isNaN(o)?o:`${o}px`:""}`,(0,H.nextTick)()},b=()=>{const{zIndex:t}=e,{modalZindex:o}=i;t?i.modalZindex=t:o<Q()&&(i.modalZindex=J())},w=()=>(0,H.nextTick)().then((()=>{const{position:t}=e,o=a().toNumber(e.marginSize),n=v(),l=document.documentElement.clientWidth||document.body.clientWidth,r=document.documentElement.clientHeight||document.body.clientHeight,i="center"===t,{top:s,left:c}=a().isString(t)?{top:t,left:t}:Object.assign({},t),u=i||"center"===s,d=i||"center"===c;let p="",m="";m=c&&!d?isNaN(c)?c:`${c}px`:`${Math.max(o,l/2-n.offsetWidth/2)}px`,p=s&&!u?isNaN(s)?s:`${s}px`:`${Math.max(o,r/2-n.offsetHeight/2)}px`,n.style.top=p,n.style.left=m})),C=()=>{(0,H.nextTick)((()=>{let e=0;xn.forEach((t=>{const o=t.getBox();e+=a().toNumber(t.props.top),t.reactData.modalTop=e,e+=o.clientHeight}))}))},y=()=>{xn.indexOf(f)>-1&&a().remove(xn,(e=>e===f)),C()},T=t=>{const{remember:o,beforeHideMethod:l}=e,{visible:r}=i,s=g.value,c={type:t};return r&&Promise.resolve(l?l(c):null).then((e=>{a().isError(e)||(s&&y(),i.contentVisible=!1,o||(i.zoomLocat=null),a().remove(vn,(e=>e===f)),h.dispatchEvent("before-hide",c),setTimeout((()=>{i.visible=!1,n("update:modelValue",!1),h.dispatchEvent("hide",c)}),200))})).catch((e=>e)),(0,H.nextTick)()},E=e=>{const t="close";h.dispatchEvent(t,{type:t},e),T(t)},S=e=>{const t="confirm";h.dispatchEvent(t,{type:t},e),T(t)},k=e=>{const t="cancel";h.dispatchEvent(t,{type:t},e),T(t)},R=e=>{const t=c.version,o=a().toStringJSON(localStorage.getItem(e)||"");return o&&o._v===t?o:{_v:t}},O=()=>{const{id:t,remember:o,storage:n,storageKey:l}=e;return!!(t&&o&&n&&R(l)[t])},M=()=>{const{id:t,remember:o,storage:n,storageKey:l}=e;if(t&&o&&n){const e=R(l)[t];if(e){const t=v(),[o,n,l,r,a,s,c,u]=e.split(",");o&&(t.style.left=`${o}px`),n&&(t.style.top=`${n}px`),l&&(t.style.width=`${l}px`),r&&(t.style.height=`${r}px`),a&&s&&(i.zoomLocat={left:a,top:s,width:c,height:u})}}},$=()=>{-1===xn.indexOf(f)&&xn.push(f),C()},I=()=>{const{id:t,remember:o,storage:n,storageKey:l}=e,{zoomLocat:r}=i;if(t&&o&&n){const e=v(),o=R(l);o[t]=[e.style.left,e.style.top,e.style.width,e.style.height].concat(r?[r.left,r.top,r.width,r.height]:[]).map((e=>e?a().toNumber(e):"")).join(","),localStorage.setItem(l,a().toJSONString(o))}},D=()=>(0,H.nextTick)().then((()=>{if(!i.zoomLocat){const t=Math.max(0,a().toNumber(e.marginSize)),o=v(),{visibleHeight:n,visibleWidth:l}=he();i.zoomLocat={top:o.offsetTop,left:o.offsetLeft,width:o.offsetWidth+(o.style.width?0:1),height:o.offsetHeight+(o.style.height?0:1)},Object.assign(o.style,{top:`${t}px`,left:`${t}px`,width:l-2*t+"px",height:n-2*t+"px"}),I()}})),F=()=>{const{duration:t,remember:o,showFooter:l}=e,{inited:r,visible:s}=i,c=g.value;return r||(i.inited=!0),s||(o||x(),i.visible=!0,i.contentVisible=!1,b(),vn.push(f),setTimeout((()=>{i.contentVisible=!0,(0,H.nextTick)((()=>{if(l){const e=d.value,t=p.value,o=e||t;o&&o.focus()}const e="",t={type:e};n("update:modelValue",!0),h.dispatchEvent("show",t)}))}),10),c?($(),-1!==t&&setTimeout((()=>T("close")),a().toNumber(t))):(0,H.nextTick)((()=>{const{fullscreen:t}=e,{firstOpen:n}=i;o&&!n||w().then((()=>{setTimeout((()=>w()),20)})),n?(i.firstOpen=!1,O()?M():t&&(0,H.nextTick)((()=>D()))):t&&(0,H.nextTick)((()=>D()))}))),(0,H.nextTick)()},N=t=>{const o=s.value;if(e.maskClosable&&t.target===o){const e="mask";T(e)}},P=e=>{const t=Jo(e,Xo.ESCAPE);if(t){const e=a().max(vn,(e=>e.reactData.modalZindex));e&&setTimeout((()=>{e===f&&e.props.escClosable&&T("exit")}),10)}},L=()=>!!i.zoomLocat,A=()=>(0,H.nextTick)().then((()=>{const{zoomLocat:e}=i;if(e){const t=v();i.zoomLocat=null,Object.assign(t.style,{top:`${e.top}px`,left:`${e.left}px`,width:`${e.width}px`,height:`${e.height}px`}),I()}})),_=()=>i.zoomLocat?A().then((()=>L())):D().then((()=>L())),j=e=>{const{zoomLocat:t}=i,o={type:t?"revert":"max"};return _().then((()=>{h.dispatchEvent("zoom",o,e)}))},z=()=>{const e=g.value;if(!e){const e=v();if(e)return{top:e.offsetTop,left:e.offsetLeft}}return null},W=(e,t)=>{const o=g.value;if(!o){const o=v();a().isNumber(e)&&(o.style.top=`${e}px`),a().isNumber(t)&&(o.style.left=`${t}px`)}return(0,H.nextTick)()},q=()=>{const{modalZindex:e}=i;vn.some((t=>t.reactData.visible&&t.reactData.modalZindex>e))&&b()},U=t=>{const{remember:o,storage:l}=e,{zoomLocat:r}=i,s=a().toNumber(e.marginSize),c=v();if(!r&&0===t.button&&!Ce(t,c,"trigger--btn").flag){t.preventDefault();const e=document.onmousemove,r=document.onmouseup,a=t.clientX-c.offsetLeft,i=t.clientY-c.offsetTop,{visibleHeight:u,visibleWidth:d}=he();document.onmousemove=e=>{e.preventDefault();const t=c.offsetWidth,o=c.offsetHeight,l=s,r=d-t-s-1,p=s,m=u-o-s-1;let f=e.clientX-a,h=e.clientY-i;f>r&&(f=r),f<l&&(f=l),h>m&&(h=m),h<p&&(h=p),c.style.left=`${f}px`,c.style.top=`${h}px`,c.className=c.className.replace(/\s?is--drag/,"")+" is--drag",n("move",{type:"move",$event:e})},document.onmouseup=()=>{document.onmousemove=e,document.onmouseup=r,o&&l&&(0,H.nextTick)((()=>{I()})),setTimeout((()=>{c.className=c.className.replace(/\s?is--drag/,"")}),50)}}},G=t=>{t.preventDefault();const{remember:o,storage:n}=e,{visibleHeight:l,visibleWidth:r}=he(),s=a().toNumber(e.marginSize),c=t.target,u=c.getAttribute("type"),d=a().toNumber(e.minWidth),p=a().toNumber(e.minHeight),m=r,f=l,g=v(),x=document.onmousemove,b=document.onmouseup,w=g.clientWidth,C=g.clientHeight,y=t.clientX,T=t.clientY,E=g.offsetTop,S=g.offsetLeft,k={type:"resize"};document.onmousemove=e=>{let t,a,i,c;switch(e.preventDefault(),u){case"wl":t=y-e.clientX,i=t+w,S-t>s&&i>d&&(g.style.width=`${i<m?i:m}px`,g.style.left=S-t+"px");break;case"swst":t=y-e.clientX,a=T-e.clientY,i=t+w,c=a+C,S-t>s&&i>d&&(g.style.width=`${i<m?i:m}px`,g.style.left=S-t+"px"),E-a>s&&c>p&&(g.style.height=`${c<f?c:f}px`,g.style.top=E-a+"px");break;case"swlb":t=y-e.clientX,a=e.clientY-T,i=t+w,c=a+C,S-t>s&&i>d&&(g.style.width=`${i<m?i:m}px`,g.style.left=S-t+"px"),E+c+s<l&&c>p&&(g.style.height=`${c<f?c:f}px`);break;case"st":a=T-e.clientY,c=C+a,E-a>s&&c>p&&(g.style.height=`${c<f?c:f}px`,g.style.top=E-a+"px");break;case"wr":t=e.clientX-y,i=t+w,S+i+s<r&&i>d&&(g.style.width=`${i<m?i:m}px`);break;case"sest":t=e.clientX-y,a=T-e.clientY,i=t+w,c=a+C,S+i+s<r&&i>d&&(g.style.width=`${i<m?i:m}px`),E-a>s&&c>p&&(g.style.height=`${c<f?c:f}px`,g.style.top=E-a+"px");break;case"selb":t=e.clientX-y,a=e.clientY-T,i=t+w,c=a+C,S+i+s<r&&i>d&&(g.style.width=`${i<m?i:m}px`),E+c+s<l&&c>p&&(g.style.height=`${c<f?c:f}px`);break;case"sb":a=e.clientY-T,c=a+C,E+c+s<l&&c>p&&(g.style.height=`${c<f?c:f}px`);break}g.className=g.className.replace(/\s?is--drag/,"")+" is--drag",o&&n&&I(),h.dispatchEvent("resize",k,e)},document.onmouseup=()=>{i.zoomLocat=null,document.onmousemove=x,document.onmouseup=b,setTimeout((()=>{g.className=g.className.replace(/\s?is--drag/,"")}),50)}};h={dispatchEvent(e,t,o){n(e,Object.assign({$modal:f,$event:o},t))},open:F,close(){return T("close")},getBox:v,getPosition:z,setPosition:W,isMaximized:L,zoom:_,maximize:D,revert:A},Object.assign(f,h);const X=()=>{const{slots:t={},showClose:n,showZoom:l,title:r}=e,{zoomLocat:a}=i,s=o.title||t.title,u=o.corner||t.corner,d=[(0,H.h)("div",{class:"vxe-modal--header-title"},s?lt(s({$modal:f})):r?te(r):c.i18n("vxe.alert.title"))],p=[];return u&&p.push((0,H.h)("span",{class:"vxe-modal--corner-wrapper"},lt(u({$modal:f})))),l&&p.push((0,H.h)("i",{class:["vxe-modal--zoom-btn","trigger--btn",a?c.icon.MODAL_ZOOM_OUT:c.icon.MODAL_ZOOM_IN],title:c.i18n("vxe.modal.zoom"+(a?"Out":"In")),onClick:j})),n&&p.push((0,H.h)("i",{class:["vxe-modal--close-btn","trigger--btn",c.icon.MODAL_CLOSE],title:c.i18n("vxe.modal.close"),onClick:E})),d.push((0,H.h)("div",{class:"vxe-modal--header-right"},p)),d},Y=()=>{const{slots:t={},showZoom:n,draggable:l}=e,r=g.value,a=o.header||t.header,s=[];if(e.showHeader){const t={};l&&(t.onMousedown=U),n&&e.dblclickZoom&&"modal"===e.type&&(t.onDblclick=j),s.push((0,H.h)("div",{class:["vxe-modal--header",{"is--draggable":l,"is--ellipsis":!r&&e.showTitleOverflow}],...t},a?!i.inited||e.destroyOnClose&&!i.visible?[]:lt(a({$modal:f})):X()))}return s},K=()=>{const{slots:t={},status:n,message:l}=e,r=e.content||l,a=g.value,s=o.default||t.default,u=[];return n&&u.push((0,H.h)("div",{class:"vxe-modal--status-wrapper"},[(0,H.h)("i",{class:["vxe-modal--status-icon",e.iconStatus||c.icon[`MODAL_${n}`.toLocaleUpperCase()]]})])),u.push((0,H.h)("div",{class:"vxe-modal--content"},s?!i.inited||e.destroyOnClose&&!i.visible?[]:lt(s({$modal:f})):te(r))),a||u.push((0,H.h)(gn,{class:"vxe-modal--loading",modelValue:e.loading})),[(0,H.h)("div",{class:"vxe-modal--body"},u)]},Z=()=>{const{showCancelButton:t,showConfirmButton:o,type:n}=e,l=[];return(a().isBoolean(t)?t:"confirm"===n)&&l.push((0,H.h)(mn,{key:1,ref:p,content:e.cancelButtonText||c.i18n("vxe.button.cancel"),onClick:k})),(a().isBoolean(o)?o:"confirm"===n||"alert"===n)&&l.push((0,H.h)(mn,{key:2,ref:d,status:"primary",content:e.confirmButtonText||c.i18n("vxe.button.confirm"),onClick:S})),l},ee=()=>{const{slots:t={}}=e,n=g.value,l=o.footer||t.footer,r=[];return e.showFooter&&r.push((0,H.h)("div",{class:"vxe-modal--footer"},l?!i.inited||e.destroyOnClose&&!i.visible?[]:lt(l({$modal:f})):Z())),!n&&e.resize&&r.push((0,H.h)("span",{class:"vxe-modal--resize"},["wl","wr","swst","sest","st","swlb","selb","sb"].map((e=>(0,H.h)("span",{class:`${e}-resize`,type:e,onMousedown:G}))))),r},oe=()=>{const{className:t,type:o,animat:n,loading:l,status:a,lockScroll:c,lockView:d,mask:p,resize:m}=e,{inited:f,zoomLocat:h,modalTop:g,contentVisible:v,visible:x}=i,b=r.value;return(0,H.h)(H.Teleport,{to:"body",disabled:!e.transfer||!f},[(0,H.h)("div",{ref:s,class:["vxe-modal--wrapper",`type--${o}`,t||"",{[`size--${b}`]:b,[`status--${a}`]:a,"is--animat":n,"lock--scroll":c,"lock--view":d,"is--resize":m,"is--mask":p,"is--maximize":h,"is--visible":v,"is--active":x,"is--loading":l}],style:{zIndex:i.modalZindex,top:g?`${g}px`:null},onClick:N},[(0,H.h)("div",{ref:u,class:"vxe-modal--box",onMousedown:q},Y().concat(K(),ee()))])])};return f.renderVN=oe,(0,H.watch)((()=>e.width),x),(0,H.watch)((()=>e.height),x),(0,H.watch)((()=>e.modelValue),(e=>{e?F():T("model")})),(0,H.onMounted)((()=>{"modal"===e.type&&e.showFooter&&!(e.showConfirmButton||e.showCancelButton||o.footer)&&V("vxe.modal.footPropErr"),(0,H.nextTick)((()=>{e.storage&&!e.id&&B("vxe.error.reqProp",["modal.id"]),e.modelValue&&F(),x()})),e.escClosable&&en.on(f,"keydown",P)})),(0,H.onUnmounted)((()=>{en.off(f,"keydown"),y()})),f},render(){return this.renderVN()}});function wn(e){if(e){const t=new Date;let o=0,n=0,l=0;if(a().isDate(e))o=e.getHours(),n=e.getMinutes(),l=e.getSeconds();else{e=a().toValueString(e);const t=e.match(/^(\d{1,2})(:(\d{1,2}))?(:(\d{1,2}))?/);t&&(o=a().toNumber(t[1]),n=a().toNumber(t[3]),l=a().toNumber(t[5]))}return t.setHours(o),t.setMinutes(n),t.setSeconds(l),t}return new Date("")}function Cn(e){const t=e.getMonth();return t<3?1:t<6?2:t<9?3:4}function yn(e){return a().isString(e)?e.replace(/,/g,""):e}function Tn(e,t){return/^-/.test(""+e)?a().toFixed(a().ceil(e,t),t):a().toFixed(a().floor(e,t),t)}const En=12,Sn=20,kn=8;var Rn=(0,H.defineComponent)({name:"VxeInput",props:{modelValue:[String,Number,Date],immediate:{type:Boolean,default:!0},name:String,type:{type:String,default:"text"},clearable:{type:Boolean,default:()=>c.input.clearable},readonly:Boolean,disabled:Boolean,placeholder:{type:String,default:()=>a().eqNull(c.input.placeholder)?c.i18n("vxe.base.pleaseInput"):c.input.placeholder},maxlength:[String,Number],autocomplete:{type:String,default:"off"},align:String,form:String,className:String,size:{type:String,default:()=>c.input.size||c.size},multiple:Boolean,showWordCount:Boolean,countMethod:Function,min:{type:[String,Number],default:null},max:{type:[String,Number],default:null},step:[String,Number],exponential:{type:Boolean,default:()=>c.input.exponential},controls:{type:Boolean,default:()=>c.input.controls},digits:{type:[String,Number],default:()=>c.input.digits},startDate:{type:[String,Number,Date],default:()=>c.input.startDate},endDate:{type:[String,Number,Date],default:()=>c.input.endDate},minDate:[String,Number,Date],maxDate:[String,Number,Date],startWeek:Number,startDay:{type:[String,Number],default:()=>c.input.startDay},labelFormat:{type:String,default:()=>c.input.labelFormat},valueFormat:{type:String,default:()=>c.input.valueFormat},editable:{type:Boolean,default:!0},festivalMethod:{type:Function,default:()=>c.input.festivalMethod},disabledMethod:{type:Function,default:()=>c.input.disabledMethod},selectDay:{type:[String,Number],default:()=>c.input.selectDay},prefixIcon:String,suffixIcon:String,placement:String,transfer:{type:Boolean,default:()=>c.input.transfer}},emits:["update:modelValue","input","change","keydown","keyup","wheel","click","focus","blur","clear","search-click","toggle-visible","prev-number","next-number","prefix-click","suffix-click","date-prev","date-today","date-next"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=pn(e),u=(0,H.reactive)({inited:!1,panelIndex:0,showPwd:!1,visiblePanel:!1,animatVisible:!1,panelStyle:null,panelPlacement:"",isActivated:!1,inputValue:e.modelValue,datetimePanelValue:null,datePanelValue:null,datePanelLabel:"",datePanelType:"day",selectMonth:null,currentDate:null}),d=(0,H.ref)(),p=(0,H.ref)(),m=(0,H.ref)(),f=(0,H.ref)(),h={refElem:d,refInput:p},g={xID:i,props:e,context:t,reactData:u,getRefMaps:()=>h};let v={};const x=(t,o)=>{const{type:n}=e;return"time"===n?wn(t):a().toStringDate(t,o)},b=(0,H.computed)((()=>{const{type:t}=e;return"time"===t||"datetime"===t})),w=(0,H.computed)((()=>["number","integer","float"].indexOf(e.type)>-1)),C=(0,H.computed)((()=>a().getSize(u.inputValue))),y=(0,H.computed)((()=>{const t=C.value;return e.maxlength&&t>a().toNumber(e.maxlength)})),T=(0,H.computed)((()=>{const t=b.value;return t||["date","week","month","quarter","year"].indexOf(e.type)>-1})),E=(0,H.computed)((()=>"password"===e.type)),S=(0,H.computed)((()=>"search"===e.type)),k=(0,H.computed)((()=>a().toInteger(e.digits)||1)),R=(0,H.computed)((()=>{const{type:t}=e,o=k.value,n=e.step;return"integer"===t?a().toInteger(n)||1:"float"===t?a().toNumber(n)||1/Math.pow(10,o):a().toNumber(n)||1})),O=(0,H.computed)((()=>{const{type:t}=e,o=w.value,n=T.value,l=E.value;return e.clearable&&(l||o||n||"text"===t||"search"===t)})),M=(0,H.computed)((()=>e.startDate?a().toStringDate(e.startDate):null)),$=(0,H.computed)((()=>e.endDate?a().toStringDate(e.endDate):null)),I=(0,H.computed)((()=>["date","week","month","quarter","year"].includes(e.type))),D=(0,H.computed)((()=>{const{modelValue:t,multiple:o}=e,n=T.value,l=P.value;return o&&t&&n?a().toValueString(t).split(",").map((e=>{const t=x(e,l);return a().isValidDate(t)?t:null})):[]})),F=(0,H.computed)((()=>{const e=D.value,t=P.value;return e.map((e=>a().toDateString(e,t)))})),N=(0,H.computed)((()=>{const e=D.value,t=j.value;return e.map((e=>a().toDateString(e,t))).join(", ")})),P=(0,H.computed)((()=>{const{type:t}=e;return"time"===t?"HH:mm:ss":e.valueFormat||("datetime"===t?"yyyy-MM-dd HH:mm:ss":"yyyy-MM-dd")})),L=(0,H.computed)((()=>{const{modelValue:t}=e,o=T.value,n=P.value;let l=null;if(t&&o){const e=x(t,n);a().isValidDate(e)&&(l=e)}return l})),A=(0,H.computed)((()=>{const e=M.value,{selectMonth:t}=u;return!(!t||!e)&&t<=e})),V=(0,H.computed)((()=>{const e=$.value,{selectMonth:t}=u;return!(!t||!e)&&t>=e})),B=(0,H.computed)((()=>{const{datetimePanelValue:e}=u;return e?a().toDateString(e,"HH:mm:ss"):""})),_=(0,H.computed)((()=>{const e=L.value,t=b.value;return e&&t?1e3*(3600*e.getHours()+60*e.getMinutes()+e.getSeconds()):0})),j=(0,H.computed)((()=>{const t=T.value;return t?e.labelFormat||c.i18n(`vxe.input.date.labelFormat.${e.type}`):null})),z=(0,H.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=t.getFullYear(),l=e.getFullYear(),r=new Date(l-l%En,0,1);for(let e=-4;e<En+4;e++){const t=a().getWhatYear(r,e,"first"),l=t.getFullYear();o.push({date:t,isCurrent:!0,isPrev:e<0,isNow:n===l,isNext:e>=En,year:l})}}return o})),W=(0,H.computed)((()=>{const e=T.value;if(e){const{datePanelType:e,selectMonth:t}=u,o=z.value;let n,l="";return t&&(l=t.getFullYear(),n=t.getMonth()+1),"quarter"===e?c.i18n("vxe.input.date.quarterLabel",[l]):"month"===e?c.i18n("vxe.input.date.monthLabel",[l]):"year"===e?o.length?`${o[0].year} - ${o[o.length-1].year}`:"":c.i18n("vxe.input.date.dayLabel",[l,n?c.i18n(`vxe.input.date.m${n}`):"-"])}return""})),q=(0,H.computed)((()=>{const{startDay:t,startWeek:o}=e;return a().toNumber(a().isNumber(t)||a().isString(t)?t:o)})),U=(0,H.computed)((()=>{const e=[],t=T.value;if(t){let t=q.value;e.push(t);for(let o=0;o<6;o++)t>=6?t=0:t++,e.push(t)}return e})),G=(0,H.computed)((()=>{const e=T.value;if(e){const e=U.value;return e.map((e=>({value:e,label:c.i18n(`vxe.input.date.weeks.w${e}`)})))}return[]})),X=(0,H.computed)((()=>{const e=T.value;if(e){const e=G.value;return[{label:c.i18n("vxe.input.date.weeks.w")}].concat(e)}return[]})),Y=(0,H.computed)((()=>{const e=z.value;return a().chunk(e,4)})),K=(0,H.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=t.getFullYear(),l=Cn(t),r=a().getWhatYear(e,0,"first"),i=r.getFullYear();for(let e=-2;e<kn-2;e++){const t=a().getWhatQuarter(r,e),s=t.getFullYear(),c=Cn(t),u=s<i;o.push({date:t,isPrev:u,isCurrent:s===i,isNow:s===n&&c===l,isNext:!u&&s>i,quarter:c})}}return o})),Z=(0,H.computed)((()=>{const e=K.value;return a().chunk(e,2)})),ee=(0,H.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=t.getFullYear(),l=t.getMonth(),r=a().getWhatYear(e,0,"first").getFullYear();for(let t=-4;t<Sn-4;t++){const i=a().getWhatYear(e,0,t),s=i.getFullYear(),c=i.getMonth(),u=s<r;o.push({date:i,isPrev:u,isCurrent:s===r,isNow:s===n&&c===l,isNext:!u&&s>r,month:c})}}return o})),oe=(0,H.computed)((()=>{const e=ee.value;return a().chunk(e,4)})),ne=(0,H.computed)((()=>{const{selectMonth:e,currentDate:t}=u,o=[];if(e&&t){const n=_.value,l=U.value,r=t.getFullYear(),i=t.getMonth(),s=t.getDate(),c=e.getFullYear(),u=e.getMonth(),d=e.getDay(),p=-l.indexOf(d),m=new Date(a().getWhatDay(e,p).getTime()+n);for(let t=0;t<42;t++){const n=a().getWhatDay(m,t),l=n.getFullYear(),d=n.getMonth(),p=n.getDate(),f=n<e;o.push({date:n,isPrev:f,isCurrent:l===c&&d===u,isNow:l===r&&d===i&&p===s,isNext:!f&&u!==d,label:p})}}return o})),le=(0,H.computed)((()=>{const e=ne.value;return a().chunk(e,7)})),re=(0,H.computed)((()=>{const e=le.value,t=q.value;return e.map((e=>{const o=e[0],n={date:o.date,isWeekNumber:!0,isPrev:!1,isCurrent:!1,isNow:!1,isNext:!1,label:a().getYearWeek(o.date,t)};return[n].concat(e)}))})),ae=(0,H.computed)((()=>{const e=[],t=b.value;if(t)for(let o=0;o<24;o++)e.push({value:o,label:(""+o).padStart(2,"0")});return e})),ie=(0,H.computed)((()=>{const e=[],t=b.value;if(t)for(let o=0;o<60;o++)e.push({value:o,label:(""+o).padStart(2,"0")});return e})),se=(0,H.computed)((()=>{const e=ie.value;return e})),ce=(0,H.computed)((()=>{const{type:t,readonly:o,editable:n,multiple:l}=e;return o||l||!n||"week"===t||"quarter"===t})),ue=(0,H.computed)((()=>{const{type:t}=e,{showPwd:o}=u,n=w.value,l=T.value,r=E.value;return l||n||r&&o||"number"===t?"text":t})),de=(0,H.computed)((()=>{const{placeholder:t}=e;return t?te(t):""})),me=(0,H.computed)((()=>{const{maxlength:t}=e,o=w.value;return o&&!a().toNumber(t)?16:t})),fe=(0,H.computed)((()=>{const{type:t,immediate:o}=e;return o||!("text"===t||"number"===t||"integer"===t||"float"===t)})),he=(0,H.computed)((()=>{const{type:t}=e,{inputValue:o}=u,n=w.value;return n?"integer"===t?a().toInteger(yn(o)):a().toNumber(yn(o)):0})),ge=(0,H.computed)((()=>{const{min:t}=e,{inputValue:o}=u,n=w.value,l=he.value;return!(!o&&0!==o||!n||null===t)&&l<=a().toNumber(t)})),ve=(0,H.computed)((()=>{const{max:t}=e,{inputValue:o}=u,n=w.value,l=he.value;return!(!o&&0!==o||!n||null===t)&&l>=a().toNumber(t)})),xe=t=>{const{type:o,exponential:n}=e,l=me.value,r=k.value,i="float"===o?Tn(t,r):a().toValueString(t);return!n||t!==i&&a().toValueString(t).toLowerCase()!==a().toNumber(i).toExponential()?i.slice(0,l):t},be=e=>{const{inputValue:t}=u;v.dispatchEvent(e.type,{value:t},e)},we=(t,o)=>{u.inputValue=t,n("update:modelValue",t),v.dispatchEvent("input",{value:t},o),a().toValueString(e.modelValue)!==t&&(v.dispatchEvent("change",{value:t},o),l&&r&&l.triggerItemEvent(o,r.itemConfig.field,t))},ye=(e,t)=>{const o=T.value,n=fe.value;u.inputValue=e,o||(n?we(e,t):v.dispatchEvent("input",{value:e},t))},Ee=e=>{const t=e.target,o=t.value;ye(o,e)},Se=e=>{const t=fe.value;t||be(e)},ke=e=>{u.isActivated=!0;const t=T.value;t&&Ot(e),be(e)},Re=t=>{const{disabled:o}=e;if(!o){const{inputValue:e}=u;v.dispatchEvent("prefix-click",{value:e},t)}};let Oe;const Me=()=>new Promise((e=>{u.visiblePanel=!1,Oe=window.setTimeout((()=>{u.animatVisible=!1,e()}),350)})),$e=(t,o)=>{const{type:n}=e,l=w.value,r=T.value;r&&Me(),(l||["text","search","password"].indexOf(n)>-1)&&focus(),v.dispatchEvent("clear",{value:o},t)},Ie=t=>{const{disabled:o}=e;if(!o)if(pe(t.currentTarget,"is--clear"))we("",t),$e(t,"");else{const{inputValue:e}=u;v.dispatchEvent("suffix-click",{value:e},t)}},De=t=>{const{type:o}=e,{valueFormat:n}=e,l=j.value,r=q.value;let i=null,s="";if(t&&(i=x(t,n)),a().isValidDate(i)){if(s=a().toDateString(i,l,{firstDay:r}),l&&"week"===o){const e=a().getWhatWeek(i,0,r,r);if(e.getFullYear()<i.getFullYear()){const e=l.indexOf("yyyy");if(e>-1){const t=Number(s.substring(e,e+4));t&&!isNaN(t)&&(s=s.replace(`${t}`,""+(t-1)))}}}}else i=null;u.datePanelValue=i,u.datePanelLabel=s},Fe=()=>{const t=T.value,{inputValue:o}=u;t&&(De(o),u.inputValue=e.multiple?N.value:u.datePanelLabel)},Ne=()=>{const{type:t}=e,{inputValue:o}=u,n=T.value,l=k.value;if(n)Fe();else if("float"===t&&o){const e=Tn(o,l);o!==e&&we(e,{type:"init"})}},Pe=t=>null===e.max||a().toNumber(t)<=a().toNumber(e.max),Le=t=>null===e.min||a().toNumber(t)>=a().toNumber(e.min),Ae=()=>{u.inputValue=e.multiple?N.value:u.datePanelLabel},Ve=e=>{const t=a().getWhatMonth(e,0,"first");a().isEqual(t,u.selectMonth)||(u.selectMonth=t)},Be=t=>{const{modelValue:o,multiple:n}=e,{datetimePanelValue:l}=u,r=b.value,i=P.value,s=q.value;if("week"===e.type){const o=a().toNumber(e.selectDay);t=a().getWhatWeek(t,0,o,s)}else r&&(t.setHours(l.getHours()),t.setMinutes(l.getMinutes()),t.setSeconds(l.getSeconds()));const c=a().toDateString(t,i,{firstDay:s});if(Ve(t),n){const e=F.value;if(r){const e=[...D.value],o=[],n=a().findIndexOf(e,(e=>a().isDateSame(t,e,"yyyyMMdd")));-1===n?e.push(t):e.splice(n,1),e.forEach((e=>{e&&(e.setHours(l.getHours()),e.setMinutes(l.getMinutes()),e.setSeconds(l.getSeconds()),o.push(e))})),we(o.map((e=>a().toDateString(e,i))).join(","),{type:"update"})}else e.some((e=>a().isEqual(e,c)))?we(e.filter((e=>!a().isEqual(e,c))).join(","),{type:"update"}):we(e.concat([c]).join(","),{type:"update"})}else a().isEqual(o,c)||we(c,{type:"update"})},_e=()=>{const{type:t,min:o,max:n,exponential:l}=e,{inputValue:r,datetimePanelValue:i}=u,s=w.value,c=T.value,d=j.value,p=ce.value;if(!p)if(s){if(r){let e="integer"===t?a().toInteger(yn(r)):a().toNumber(yn(r));if(Le(e)?Pe(e)||(e=n):e=o,l){const t=a().toValueString(r).toLowerCase();t===a().toNumber(e).toExponential()&&(e=t)}we(xe(e),{type:"check"})}}else if(c)if(r){let e=x(r,d);if(a().isValidDate(e))if("time"===t)e=a().toDateString(e,d),r!==e&&we(e,{type:"check"}),u.inputValue=e;else{let o=!1;const n=q.value;if("datetime"===t){const t=L.value;r===a().toDateString(t,d)&&r===a().toDateString(e,d)||(o=!0,i.setHours(e.getHours()),i.setMinutes(e.getMinutes()),i.setSeconds(e.getSeconds()))}else o=!0;u.inputValue=a().toDateString(e,d,{firstDay:n}),o&&Be(e)}else Ae()}else we("",{type:"check"})},je=e=>{const{inputValue:t}=u,o=fe.value;o||we(t,e),_e(),u.visiblePanel||(u.isActivated=!1),v.dispatchEvent("blur",{value:t},e)},He=t=>{const{readonly:o,disabled:n}=e,{showPwd:l}=u;n||o||(u.showPwd=!l),v.dispatchEvent("toggle-visible",{visible:u.showPwd},t)},ze=e=>{v.dispatchEvent("search-click",{},e)},We=(t,o)=>{const{min:n,max:l,type:r}=e,{inputValue:i}=u,s=R.value,c="integer"===r?a().toInteger(yn(i)):a().toNumber(yn(i)),d=t?a().add(c,s):a().subtract(c,s);let p;p=Le(d)?Pe(d)?d:l:n,ye(xe(p),o)};let qe;const Ue=t=>{const{readonly:o,disabled:n}=e,l=ge.value;clearTimeout(qe),n||o||l||We(!1,t),v.dispatchEvent("next-number",{value:u.inputValue},t)},Ge=e=>{qe=window.setTimeout((()=>{Ue(e),Ge(e)}),60)},Xe=t=>{const{readonly:o,disabled:n}=e,l=ve.value;clearTimeout(qe),n||o||l||We(!0,t),v.dispatchEvent("prev-number",{value:u.inputValue},t)},Ye=e=>{const t=Jo(e,Xo.ARROW_UP),o=Jo(e,Xo.ARROW_DOWN);(t||o)&&(e.preventDefault(),t?Xe(e):Ue(e))},Ke=t=>{const{exponential:o,controls:n}=e,l=w.value;if(l){const e=t.ctrlKey,l=t.shiftKey,r=t.altKey,a=t.keyCode;e||l||r||!(Jo(t,Xo.SPACEBAR)||(!o||69!==a)&&a>=65&&a<=90||a>=186&&a<=188||a>=191)||t.preventDefault(),n&&Ye(t)}be(t)},Ze=e=>{be(e)},Je=()=>{clearTimeout(qe)},Qe=e=>{qe=window.setTimeout((()=>{Xe(e),Qe(e)}),60)},et=e=>{if(Je(),0===e.button){const t=pe(e.currentTarget,"is--prev");t?Xe(e):Ue(e),qe=window.setTimeout((()=>{t?Qe(e):Ge(e)}),500)}},tt=t=>{const o=w.value;if(o&&e.controls&&u.isActivated){const e=t.deltaY;e>0?Ue(t):e<0&&Xe(t),t.preventDefault()}be(t)},ot=(e,t)=>{u.selectMonth=a().getWhatMonth(e,t,"first")},nt=()=>{const e=a().getWhatDay(Date.now(),0,"first");u.currentDate=e,ot(e,0)},lt=()=>{let{datePanelType:e}=u;e="month"===e||"quarter"===e?"year":"month",u.datePanelType=e},rt=t=>{const{type:o}=e,{datePanelType:n,selectMonth:l,inputValue:r}=u,i=r,s=A.value;s||(u.selectMonth="year"===o?a().getWhatYear(l,-En,"first"):"month"===o||"quarter"===o?"year"===n?a().getWhatYear(l,-En,"first"):a().getWhatYear(l,-1,"first"):"year"===n?a().getWhatYear(l,-En,"first"):"month"===n?a().getWhatYear(l,-1,"first"):a().getWhatMonth(l,-1,"first"),v.dispatchEvent("date-prev",{value:i,type:o},t))},at=t=>{nt(),e.multiple||(Be(u.currentDate),Me()),v.dispatchEvent("date-today",{type:e.type},t)},it=t=>{const{type:o}=e,{datePanelType:n,selectMonth:l,inputValue:r}=u,i=r,s=V.value;s||(u.selectMonth="year"===o?a().getWhatYear(l,En,"first"):"month"===o||"quarter"===o?"year"===n?a().getWhatYear(l,En,"first"):a().getWhatYear(l,1,"first"):"year"===n?a().getWhatYear(l,En,"first"):"month"===n?a().getWhatYear(l,1,"first"):a().getWhatMonth(l,1,"first"),v.dispatchEvent("date-next",{value:i,type:o},t))},st=t=>{const{disabledMethod:o}=e,{datePanelType:n}=u;return o&&o({type:n,viewType:n,date:t.date,$input:g})},ct=t=>{const{type:o,multiple:n}=e,{datePanelType:l}=u;"month"===o?"year"===l?(u.datePanelType="month",Ve(t)):(Be(t),n||Me()):"year"===o?(Be(t),n||Me()):"quarter"===o?"year"===l?(u.datePanelType="quarter",Ve(t)):(Be(t),n||Me()):"month"===l?(u.datePanelType="week"===o?o:"day",Ve(t)):"year"===l?(u.datePanelType="month",Ve(t)):(Be(t),"datetime"===o||n||Me())},ut=e=>{st(e)||ct(e.date)},dt=e=>{if(!st({date:e})){const t=ne.value;t.some((t=>a().isDateSame(t.date,e,"yyyyMMdd")))||Ve(e),De(e)}},pt=e=>{if(!st({date:e})){const t=z.value;t.some((t=>a().isDateSame(t.date,e,"yyyy")))||Ve(e),De(e)}},mt=e=>{if(!st({date:e})){const t=K.value;t.some((t=>a().isDateSame(t.date,e,"yyyyq")))||Ve(e),De(e)}},ft=e=>{if(!st({date:e})){const t=ee.value;t.some((t=>a().isDateSame(t.date,e,"yyyyMM")))||Ve(e),De(e)}},ht=e=>{if(!st(e)){const{datePanelType:t}=u;"month"===t?ft(e.date):"quarter"===t?mt(e.date):"year"===t?pt(e.date):dt(e.date)}},gt=e=>{if(e){const t=e.offsetHeight,o=e.parentNode;o.scrollTop=e.offsetTop-4*t}},vt=e=>{u.datetimePanelValue=new Date(u.datetimePanelValue.getTime()),gt(e.currentTarget)},xt=(e,t)=>{u.datetimePanelValue.setHours(t.value),vt(e)},bt=()=>{const{multiple:t}=e,{datetimePanelValue:o}=u,n=L.value,l=b.value;if(l){const e=P.value;if(t){const t=F.value;if(l){const t=[...D.value],n=[];t.forEach((e=>{e&&(e.setHours(o.getHours()),e.setMinutes(o.getMinutes()),e.setSeconds(o.getSeconds()),n.push(e))})),we(n.map((t=>a().toDateString(t,e))).join(","),{type:"update"})}else we(t.join(","),{type:"update"})}else Be(n||u.currentDate)}Me()},wt=(e,t)=>{u.datetimePanelValue.setMinutes(t.value),vt(e)},Ct=(e,t)=>{u.datetimePanelValue.setSeconds(t.value),vt(e)},yt=e=>{const{isActivated:t,datePanelValue:o,datePanelType:n}=u;if(t){e.preventDefault();const t=Jo(e,Xo.ARROW_LEFT),l=Jo(e,Xo.ARROW_UP),r=Jo(e,Xo.ARROW_RIGHT),i=Jo(e,Xo.ARROW_DOWN);if("year"===n){let e=a().getWhatYear(o||Date.now(),0,"first");t?e=a().getWhatYear(e,-1):l?e=a().getWhatYear(e,-4):r?e=a().getWhatYear(e,1):i&&(e=a().getWhatYear(e,4)),pt(e)}else if("quarter"===n){let e=a().getWhatQuarter(o||Date.now(),0,"first");t?e=a().getWhatQuarter(e,-1):l?e=a().getWhatQuarter(e,-2):r?e=a().getWhatQuarter(e,1):i&&(e=a().getWhatQuarter(e,2)),mt(e)}else if("month"===n){let e=a().getWhatMonth(o||Date.now(),0,"first");t?e=a().getWhatMonth(e,-1):l?e=a().getWhatMonth(e,-4):r?e=a().getWhatMonth(e,1):i&&(e=a().getWhatMonth(e,4)),ft(e)}else{let e=o||a().getWhatDay(Date.now(),0,"first");const n=q.value;t?e=a().getWhatDay(e,-1):l?e=a().getWhatWeek(e,-1,n):r?e=a().getWhatDay(e,1):i&&(e=a().getWhatWeek(e,1,n)),dt(e)}}},Tt=e=>{const{isActivated:t}=u;if(t){const t=Jo(e,Xo.PAGE_UP);e.preventDefault(),t?rt(e):it(e)}},Et=()=>{const{type:t}=e,o=b.value,n=L.value;["year","quarter","month","week"].indexOf(t)>-1?u.datePanelType=t:u.datePanelType="day",u.currentDate=a().getWhatDay(Date.now(),0,"first"),n?(ot(n,0),De(n)):nt(),o&&(u.datetimePanelValue=u.datePanelValue||a().getWhatDay(Date.now(),0,"first"),(0,H.nextTick)((()=>{const e=f.value;a().arrayEach(e.querySelectorAll("li.is--selected"),gt)})))},St=()=>{u.panelIndex<Q()&&(u.panelIndex=J())},kt=()=>(0,H.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n}=u,l=p.value,r=m.value;if(l&&r){const e=l.offsetHeight,a=l.offsetWidth,i=r.offsetHeight,s=r.offsetWidth,c=5,d={zIndex:n},{boundingTop:p,boundingLeft:m,visibleHeight:f,visibleWidth:h}=Te(l);let g="bottom";if(console.log(Te(l)),t){let t=m,n=p+e;"top"===o?(g="top",n=p-i):o||(n+i+c>f&&(g="top",n=p-i),n<c&&(g="bottom",n=p+e)),t+s+c>h&&(t-=t+s+c-h),t<c&&(t=c),Object.assign(d,{left:`${t}px`,top:`${n}px`,minWidth:`${a}px`})}else"top"===o?(g="top",d.bottom=`${e}px`):o||p+e+i>f&&p-e-i>c&&(g="top",d.bottom=`${e}px`);return u.panelStyle=d,u.panelPlacement=g,(0,H.nextTick)()}})),Rt=()=>{const{disabled:t}=e,{visiblePanel:o}=u,n=T.value;return t||o?(0,H.nextTick)():(u.inited||(u.inited=!0),clearTimeout(Oe),u.isActivated=!0,u.animatVisible=!0,n&&Et(),setTimeout((()=>{u.visiblePanel=!0}),10),St(),kt())},Ot=t=>{const{readonly:o}=e;o||(t.preventDefault(),Rt())},Mt=e=>{be(e)},$t=t=>{const{disabled:o}=e,{visiblePanel:n,isActivated:l}=u,r=T.value,a=d.value,i=m.value;!o&&l&&(u.isActivated=Ce(t,a).flag||Ce(t,i).flag,u.isActivated||(r?n&&(Me(),_e()):_e()))},It=t=>{const{clearable:o,disabled:n}=e,{visiblePanel:l}=u,r=T.value;if(!n){const e=Jo(t,Xo.TAB),n=Jo(t,Xo.DELETE),a=Jo(t,Xo.ESCAPE),i=Jo(t,Xo.ENTER),s=Jo(t,Xo.ARROW_LEFT),c=Jo(t,Xo.ARROW_UP),d=Jo(t,Xo.ARROW_RIGHT),p=Jo(t,Xo.ARROW_DOWN),m=Jo(t,Xo.PAGE_UP),f=Jo(t,Xo.PAGE_DOWN),h=s||c||d||p;let g=u.isActivated;e?(g&&_e(),g=!1,u.isActivated=g):h?r&&g&&(l?yt(t):(c||p)&&Ot(t)):i?r&&(l?u.datePanelValue?ct(u.datePanelValue):Me():g&&Ot(t)):(m||f)&&r&&g&&Tt(t),e||a?l&&Me():n&&o&&g&&$e(t,null)}},Dt=t=>{const{disabled:o}=e,{visiblePanel:n}=u;if(!o&&n){const e=m.value;Ce(t,e).flag?kt():(Me(),_e())}},Ft=()=>{const{isActivated:e,visiblePanel:t}=u;t?(Me(),_e()):e&&_e()},Nt=(t,o)=>{const{festivalMethod:n}=e;if(n){const{datePanelType:e}=u,l=n({type:e,viewType:e,date:t.date,$input:g}),r=l?a().isString(l)?{label:l}:l:{},i=r.extra?a().isString(r.extra)?{label:r.extra}:r.extra:null,s=[(0,H.h)("span",{class:["vxe-input--date-label",{"is-notice":r.notice}]},i&&i.label?[(0,H.h)("span",o),(0,H.h)("span",{class:["vxe-input--date-label--extra",i.important?"is-important":"",i.className],style:i.style},a().toValueString(i.label))]:o)],c=r.label;if(c){const e=a().toValueString(c).split(",");s.push((0,H.h)("span",{class:["vxe-input--date-festival",r.important?"is-important":"",r.className],style:r.style},[e.length>1?(0,H.h)("span",{class:["vxe-input--date-festival--overlap",`overlap--${e.length}`]},e.map((e=>(0,H.h)("span",e.substring(0,3))))):(0,H.h)("span",{class:"vxe-input--date-festival--label"},e[0].substring(0,3))]))}return s}return o},Pt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=L.value,r=G.value,i=le.value,s=D.value,c="yyyyMMdd";return[(0,H.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("thead",[(0,H.h)("tr",r.map((e=>(0,H.h)("th",e.label))))]),(0,H.h)("tbody",i.map((e=>(0,H.h)("tr",e.map((e=>(0,H.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?s.some((t=>a().isDateSame(t,e.date,c))):a().isDateSame(l,e.date,c),"is--hover":a().isDateSame(n,e.date,c)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,e.label))))))))])]},Lt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=L.value,r=X.value,i=re.value,s=D.value,c="yyyyMMdd";return[(0,H.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("thead",[(0,H.h)("tr",r.map((e=>(0,H.h)("th",e.label))))]),(0,H.h)("tbody",i.map((e=>{const o=t?e.some((e=>s.some((t=>a().isDateSame(t,e.date,c))))):e.some((e=>a().isDateSame(l,e.date,c))),r=e.some((e=>a().isDateSame(n,e.date,c)));return(0,H.h)("tr",e.map((e=>(0,H.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":o,"is--hover":r},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,e.label)))))})))])]},At=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=L.value,r=oe.value,i=D.value,s="yyyyMM";return[(0,H.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("tbody",r.map((e=>(0,H.h)("tr",e.map((e=>(0,H.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?i.some((t=>a().isDateSame(t,e.date,s))):a().isDateSame(l,e.date,s),"is--hover":a().isDateSame(n,e.date,s)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,c.i18n(`vxe.input.date.months.m${e.month}`)))))))))])]},Vt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=L.value,r=Z.value,i=D.value,s="yyyyq";return[(0,H.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("tbody",r.map((e=>(0,H.h)("tr",e.map((e=>(0,H.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?i.some((t=>a().isDateSame(t,e.date,s))):a().isDateSame(l,e.date,s),"is--hover":a().isDateSame(n,e.date,s)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,c.i18n(`vxe.input.date.quarters.q${e.quarter}`)))))))))])]},Bt=()=>{const{multiple:t}=e,{datePanelType:o,datePanelValue:n}=u,l=L.value,r=Y.value,i=D.value,s="yyyy";return[(0,H.h)("table",{class:`vxe-input--date-${o}-view`,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("tbody",r.map((e=>(0,H.h)("tr",e.map((e=>(0,H.h)("td",{class:{"is--prev":e.isPrev,"is--current":e.isCurrent,"is--now":e.isNow,"is--next":e.isNext,"is--disabled":st(e),"is--selected":t?i.some((t=>a().isDateSame(t,e.date,s))):a().isDateSame(l,e.date,s),"is--hover":a().isDateSame(n,e.date,s)},onClick:()=>ut(e),onMouseenter:()=>ht(e)},Nt(e,e.year))))))))])]},_t=()=>{const{datePanelType:e}=u;switch(e){case"week":return Lt();case"month":return At();case"quarter":return Vt();case"year":return Bt()}return Pt()},jt=()=>{const{multiple:t}=e,{datePanelType:o}=u,n=A.value,l=V.value,r=W.value;return[(0,H.h)("div",{class:"vxe-input--date-picker-header"},[(0,H.h)("div",{class:"vxe-input--date-picker-type-wrapper"},["year"===o?(0,H.h)("span",{class:"vxe-input--date-picker-label"},r):(0,H.h)("span",{class:"vxe-input--date-picker-btn",onClick:lt},r)]),(0,H.h)("div",{class:"vxe-input--date-picker-btn-wrapper"},[(0,H.h)("span",{class:["vxe-input--date-picker-btn vxe-input--date-picker-prev-btn",{"is--disabled":n}],onClick:rt},[(0,H.h)("i",{class:"vxe-icon-caret-left"})]),(0,H.h)("span",{class:"vxe-input--date-picker-btn vxe-input--date-picker-current-btn",onClick:at},[(0,H.h)("i",{class:"vxe-icon-dot"})]),(0,H.h)("span",{class:["vxe-input--date-picker-btn vxe-input--date-picker-next-btn",{"is--disabled":l}],onClick:it},[(0,H.h)("i",{class:"vxe-icon-caret-right"})]),t&&I.value?(0,H.h)("span",{class:"vxe-input--date-picker-btn vxe-input--date-picker-confirm-btn"},[(0,H.h)("button",{class:"vxe-input--date-picker-confirm",type:"button",onClick:bt},c.i18n("vxe.button.confirm"))]):null])]),(0,H.h)("div",{class:"vxe-input--date-picker-body"},_t())]},Ht=()=>{const{datetimePanelValue:e}=u,t=B.value,o=ae.value,n=ie.value,l=se.value;return[(0,H.h)("div",{class:"vxe-input--time-picker-header"},[(0,H.h)("span",{class:"vxe-input--time-picker-title"},t),(0,H.h)("button",{class:"vxe-input--time-picker-confirm",type:"button",onClick:bt},c.i18n("vxe.button.confirm"))]),(0,H.h)("div",{ref:f,class:"vxe-input--time-picker-body"},[(0,H.h)("ul",{class:"vxe-input--time-picker-hour-list"},o.map(((t,o)=>(0,H.h)("li",{key:o,class:{"is--selected":e&&e.getHours()===t.value},onClick:e=>xt(e,t)},t.label)))),(0,H.h)("ul",{class:"vxe-input--time-picker-minute-list"},n.map(((t,o)=>(0,H.h)("li",{key:o,class:{"is--selected":e&&e.getMinutes()===t.value},onClick:e=>wt(e,t)},t.label)))),(0,H.h)("ul",{class:"vxe-input--time-picker-second-list"},l.map(((t,o)=>(0,H.h)("li",{key:o,class:{"is--selected":e&&e.getSeconds()===t.value},onClick:e=>Ct(e,t)},t.label))))])]},zt=()=>{const{type:t,transfer:o}=e,{inited:n,animatVisible:l,visiblePanel:r,panelPlacement:a,panelStyle:i}=u,c=s.value,d=T.value,p=[];return d?("datetime"===t?p.push((0,H.h)("div",{class:"vxe-input--panel-layout-wrapper"},[(0,H.h)("div",{class:"vxe-input--panel-left-wrapper"},jt()),(0,H.h)("div",{class:"vxe-input--panel-right-wrapper"},Ht())])):"time"===t?p.push((0,H.h)("div",{class:"vxe-input--panel-wrapper"},Ht())):p.push((0,H.h)("div",{class:"vxe-input--panel-wrapper"},jt())),(0,H.h)(H.Teleport,{to:"body",disabled:!o||!n},[(0,H.h)("div",{ref:m,class:["vxe-table--ignore-clear vxe-input--panel",`type--${t}`,{[`size--${c}`]:c,"is--transfer":o,"animat--leave":l,"animat--enter":r}],placement:a,style:i},p)])):null},Wt=()=>{const e=ve.value,t=ge.value;return(0,H.h)("span",{class:"vxe-input--number-suffix"},[(0,H.h)("span",{class:["vxe-input--number-prev is--prev",{"is--disabled":e}],onMousedown:et,onMouseup:Je,onMouseleave:Je},[(0,H.h)("i",{class:["vxe-input--number-prev-icon",c.icon.INPUT_PREV_NUM]})]),(0,H.h)("span",{class:["vxe-input--number-next is--next",{"is--disabled":t}],onMousedown:et,onMouseup:Je,onMouseleave:Je},[(0,H.h)("i",{class:["vxe-input--number-next-icon",c.icon.INPUT_NEXT_NUM]})])])},qt=()=>(0,H.h)("span",{class:"vxe-input--date-picker-suffix",onClick:Ot},[(0,H.h)("i",{class:["vxe-input--date-picker-icon",c.icon.INPUT_DATE]})]),Ut=()=>(0,H.h)("span",{class:"vxe-input--search-suffix",onClick:ze},[(0,H.h)("i",{class:["vxe-input--search-icon",c.icon.INPUT_SEARCH]})]),Gt=()=>{const{showPwd:e}=u;return(0,H.h)("span",{class:"vxe-input--password-suffix",onClick:He},[(0,H.h)("i",{class:["vxe-input--password-icon",e?c.icon.INPUT_SHOW_PWD:c.icon.INPUT_PWD]})])},Xt=()=>{const{prefixIcon:t}=e,n=o.prefix,l=[];return n?l.push((0,H.h)("span",{class:"vxe-input--prefix-icon"},n({}))):t&&l.push((0,H.h)("i",{class:["vxe-input--prefix-icon",t]})),l.length?(0,H.h)("span",{class:"vxe-input--prefix",onClick:Re},l):null},Yt=()=>{const{disabled:t,suffixIcon:n}=e,{inputValue:l}=u,r=o.suffix,i=O.value,s=[];return r?s.push((0,H.h)("span",{class:"vxe-input--suffix-icon"},r({}))):n&&s.push((0,H.h)("i",{class:["vxe-input--suffix-icon",n]})),i&&s.push((0,H.h)("i",{class:["vxe-input--clear-icon",c.icon.INPUT_CLEAR]})),s.length?(0,H.h)("span",{class:["vxe-input--suffix",{"is--clear":i&&!t&&!(""===l||a().eqNull(l))}],onClick:Ie},s):null},Kt=()=>{const{controls:t}=e,o=w.value,n=T.value,l=E.value,r=S.value;let a;return l?a=Gt():o?t&&(a=Wt()):n?a=qt():r&&(a=Ut()),a?(0,H.h)("span",{class:"vxe-input--extra-suffix"},[a]):null};v={dispatchEvent(e,t,o){n(e,Object.assign({$input:g,$event:o},t))},focus(){const e=p.value;return u.isActivated=!0,e.focus(),(0,H.nextTick)()},blur(){const e=p.value;return e.blur(),u.isActivated=!1,(0,H.nextTick)()},select(){const e=p.value;return e.select(),u.isActivated=!1,(0,H.nextTick)()},showPanel:Rt,hidePanel:Me,updatePlacement:kt},Object.assign(g,v),(0,H.watch)((()=>e.modelValue),(e=>{u.inputValue=e,Fe()})),(0,H.watch)((()=>e.type),(()=>{Object.assign(u,{inputValue:e.modelValue,datetimePanelValue:null,datePanelValue:null,datePanelLabel:"",datePanelType:"day",selectMonth:null,currentDate:null}),Ne()})),(0,H.watch)(j,(()=>{const t=T.value;t&&(De(u.datePanelValue),u.inputValue=e.multiple?N.value:u.datePanelLabel)})),(0,H.nextTick)((()=>{en.on(g,"mousewheel",Dt),en.on(g,"mousedown",$t),en.on(g,"keydown",It),en.on(g,"blur",Ft)})),(0,H.onUnmounted)((()=>{Je(),en.off(g,"mousewheel"),en.off(g,"mousedown"),en.off(g,"keydown"),en.off(g,"blur")})),Ne();const Zt=()=>{const{className:t,controls:o,type:n,align:l,showWordCount:r,countMethod:a,name:i,disabled:c,readonly:m,autocomplete:f}=e,{inputValue:h,visiblePanel:g,isActivated:v}=u,x=s.value,b=y.value,w=C.value,E=T.value,S=ce.value,k=me.value,R=ue.value,O=de.value,M=[],$=Xt(),I=Yt();$&&M.push($),M.push((0,H.h)("input",{ref:p,class:"vxe-input--inner",value:h,name:i,type:R,placeholder:O,maxlength:k,readonly:S,disabled:c,autocomplete:f,onKeydown:Ke,onKeyup:Ze,onWheel:tt,onClick:Mt,onInput:Ee,onChange:Se,onFocus:ke,onBlur:je})),I&&M.push(I),M.push(Kt()),E&&M.push(zt());let D=!1;return r&&["text","search"].includes(n)&&(D=!0,M.push((0,H.h)("span",{class:["vxe-input--count",{"is--error":b}]},a?`${a({value:h})}`:`${w}${k?`/${k}`:""}`))),(0,H.h)("div",{ref:d,class:["vxe-input",`type--${n}`,t,{[`size--${x}`]:x,[`is--${l}`]:l,"is--controls":o,"is--prefix":!!$,"is--suffix":!!I,"is--readonly":m,"is--visivle":g,"is--count":D,"is--disabled":c,"is--active":v}]},M)};return g.renderVN=Zt,g},render(){return this.renderVN()}}),On=(0,H.defineComponent)({name:"VxeCheckbox",props:{modelValue:[String,Number,Boolean],label:{type:[String,Number],default:null},indeterminate:Boolean,title:[String,Number],checkedValue:{type:[String,Number,Boolean],default:!0},uncheckedValue:{type:[String,Number,Boolean],default:!1},content:[String,Number],disabled:Boolean,size:{type:String,default:()=>c.checkbox.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s={xID:i,props:e,context:t};let c={};const u=pn(e),d=(0,H.inject)("$xecheckboxgroup",null),p=(0,H.computed)((()=>d?a().includes(d.props.modelValue,e.label):e.modelValue===e.checkedValue)),m=(0,H.computed)((()=>{if(e.disabled)return!0;if(d){const{props:e}=d,{computeIsMaximize:t}=d.getComputeMaps(),o=t.value,n=p.value;return e.disabled||o&&!n}return!1})),f=t=>{const{checkedValue:o,uncheckedValue:a}=e,i=m.value;if(!i){const i=t.target.checked,s=i?o:a,u={checked:i,value:s,label:e.label};d?d.handleChecked(u,t):(n("update:modelValue",s),c.dispatchEvent("change",u,t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,s))}};c={dispatchEvent(e,t,o){n(e,Object.assign({$checkbox:s,$event:o},t))}},Object.assign(s,c);const h=()=>{const t=u.value,n=m.value,l=p.value,r=e.indeterminate;return(0,H.h)("label",{class:["vxe-checkbox",{[`size--${t}`]:t,"is--indeterminate":r,"is--disabled":n,"is--checked":l}],title:e.title},[(0,H.h)("input",{class:"vxe-checkbox--input",type:"checkbox",disabled:n,checked:l,onChange:f}),(0,H.h)("span",{class:["vxe-checkbox--icon",r?"vxe-icon-checkbox-indeterminate":l?"vxe-icon-checkbox-checked":"vxe-icon-checkbox-unchecked"]}),(0,H.h)("span",{class:"vxe-checkbox--label"},o.default?o.default({}):te(e.content))])};return s.renderVN=h,s},render(){return this.renderVN()}});function Mn(e){return!1!==e.visible}function $n(){return a().uniqueId("opt_")}var In=(0,H.defineComponent)({name:"VxeSelect",props:{modelValue:null,clearable:Boolean,placeholder:{type:String,default:()=>a().eqNull(c.select.placeholder)?c.i18n("vxe.base.pleaseSelect"):c.select.placeholder},loading:Boolean,disabled:Boolean,multiple:Boolean,multiCharOverflow:{type:[Number,String],default:()=>c.select.multiCharOverflow},prefixIcon:String,placement:String,options:Array,optionProps:Object,optionGroups:Array,optionGroupProps:Object,optionConfig:Object,className:[String,Function],popupClassName:[String,Function],max:{type:[String,Number],default:null},size:{type:String,default:()=>c.select.size||c.size},filterable:Boolean,filterMethod:Function,remote:Boolean,remoteMethod:Function,emptyText:String,optionId:{type:String,default:()=>c.select.optionId},optionKey:Boolean,transfer:{type:Boolean,default:()=>c.select.transfer}},emits:["update:modelValue","change","clear","blur","focus"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=pn(e),u=(0,H.reactive)({inited:!1,staticOptions:[],fullGroupList:[],fullOptionList:[],visibleGroupList:[],visibleOptionList:[],remoteValueList:[],panelIndex:0,panelStyle:{},panelPlacement:null,currentOption:null,currentValue:null,visiblePanel:!1,animatVisible:!1,isActivated:!1,searchValue:"",searchLoading:!1}),d=(0,H.ref)(),p=(0,H.ref)(),m=(0,H.ref)(),f=(0,H.ref)(),h=(0,H.ref)(),g={refElem:d},v={xID:i,props:e,context:t,reactData:u,getRefMaps:()=>g};let x={};const b=(0,H.computed)((()=>e.optionProps||{})),w=(0,H.computed)((()=>e.optionGroupProps||{})),C=(0,H.computed)((()=>{const e=b.value;return e.label||"label"})),y=(0,H.computed)((()=>{const e=b.value;return e.value||"value"})),T=(0,H.computed)((()=>{const e=w.value;return e.label||"label"})),E=(0,H.computed)((()=>{const e=w.value;return e.options||"options"})),S=(0,H.computed)((()=>{const{modelValue:t,multiple:o,max:n}=e;return!(!o||!n)&&(t?t.length:0)>=a().toNumber(n)})),k=(0,H.computed)((()=>Object.assign({},c.select.optionConfig,e.optionConfig))),R=(0,H.computed)((()=>u.fullGroupList.some((e=>e.options&&e.options.length)))),O=(0,H.computed)((()=>a().toNumber(e.multiCharOverflow))),M=(e,t)=>e&&(a().isString(e)&&(e=o[e]||null),a().isFunction(e))?lt(e(t)):[],$=e=>{const{fullOptionList:t,fullGroupList:o}=u,n=R.value,l=y.value;if(n)for(let r=0;r<o.length;r++){const t=o[r];if(t.options)for(let o=0;o<t.options.length;o++){const n=t.options[o];if(e===n[l])return n}}return t.find((t=>e===t[l]))},I=e=>{const{remoteValueList:t}=u,o=C.value,n=t.find((t=>e===t.key)),l=n?n.result:null;return a().toValueString(l?l[o]:e)},D=e=>{const t=C.value,o=$(e);return a().toValueString(o?o[t]:e)},F=(0,H.computed)((()=>{const{modelValue:t,multiple:o,remote:n}=e,l=O.value;if(t&&o){const e=a().isArray(t)?t:[t];return n?e.map((e=>I(e))).join(", "):e.map((e=>{const t=D(e);return l>0&&t.length>l?`${t.substring(0,l)}...`:t})).join(", ")}return n?I(t):D(t)})),N=()=>{const t=k.value;return t.keyField||e.optionId||"_X_OPTION_KEY"},P=e=>{const t=e[N()];return t?encodeURIComponent(t):""},L=()=>{const{filterable:t,filterMethod:o}=e,{fullOptionList:n,fullGroupList:l,searchValue:r}=u,a=R.value,i=T.value,s=C.value;return a?u.visibleGroupList=t&&o?l.filter((e=>Mn(e)&&o({group:e,option:null,searchValue:r}))):t?l.filter((e=>Mn(e)&&(!r||`${e[i]}`.indexOf(r)>-1))):l.filter(Mn):u.visibleOptionList=t&&o?n.filter((e=>Mn(e)&&o({group:null,option:e,searchValue:r}))):t?n.filter((e=>Mn(e)&&(!r||`${e[s]}`.indexOf(r)>-1))):n.filter(Mn),(0,H.nextTick)()},A=()=>{const{fullOptionList:e,fullGroupList:t}=u,o=E.value,n=N(),l=e=>{P(e)||(e[n]=$n())};t.length?t.forEach((e=>{l(e),e[o]&&e[o].forEach(l)})):e.length&&e.forEach(l),L()},V=e=>{const t=y.value;e&&(u.currentOption=e,u.currentValue=e[t])},B=(e,t)=>(0,H.nextTick)().then((()=>{if(e){const o=f.value,n=h.value,l=n.querySelector(`[optid='${P(e)}']`);if(o&&l){const e=o.offsetHeight,n=5;t?l.offsetTop+l.offsetHeight-o.scrollTop>e&&(o.scrollTop=l.offsetTop+l.offsetHeight-e):(l.offsetTop+n<o.scrollTop||l.offsetTop+n>o.scrollTop+o.clientHeight)&&(o.scrollTop=l.offsetTop-n)}}})),_=()=>{u.panelIndex<Q()&&(u.panelIndex=J())},j=()=>(0,H.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n}=u,l=d.value,r=h.value;if(r&&l){const e=l.offsetHeight,a=l.offsetWidth,i=r.offsetHeight,s=r.offsetWidth,c=5,d={zIndex:n},{boundingTop:p,boundingLeft:m,visibleHeight:f,visibleWidth:h}=Te(l);let g="bottom";if(t){let t=m,n=p+e;"top"===o?(g="top",n=p-i):o||(n+i+c>f&&(g="top",n=p-i),n<c&&(g="bottom",n=p+e)),t+s+c>h&&(t-=t+s+c-h),t<c&&(t=c),Object.assign(d,{left:`${t}px`,top:`${n}px`,minWidth:`${a}px`})}else"top"===o?(g="top",d.bottom=`${e}px`):o||p+e+i>f&&p-e-i>c&&(g="top",d.bottom=`${e}px`);return u.panelStyle=d,u.panelPlacement=g,(0,H.nextTick)()}}));let z;const W=()=>{const{loading:t,disabled:o,filterable:n}=e;t||o||(clearTimeout(z),u.inited||(u.inited=!0),u.isActivated=!0,u.animatVisible=!0,n&&L(),setTimeout((()=>{const{modelValue:t,multiple:o}=e,n=$(o&&t?t[0]:t);u.visiblePanel=!0,n&&(V(n),B(n)),re()}),10),_(),j())},q=()=>{u.searchValue="",u.searchLoading=!1,u.visiblePanel=!1,z=window.setTimeout((()=>{u.animatVisible=!1}),350)},U=(t,o)=>{o!==e.modelValue&&(n("update:modelValue",o),x.dispatchEvent("change",{value:o},t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,o))},G=(e,t)=>{u.remoteValueList=[],U(e,t),x.dispatchEvent("clear",{value:t},e)},X=(e,t)=>{G(t,null),q()},Y=(t,o,n)=>{const{modelValue:l,multiple:r}=e,{remoteValueList:a}=u;if(r){let e;e=l?-1===l.indexOf(o)?l.concat([o]):l.filter((e=>e!==o)):[o];const r=a.find((e=>e.key===o));r?r.result=n:a.push({key:o,result:n}),U(t,e)}else u.remoteValueList=[{key:o,result:n}],U(t,o),q()},K=t=>{const{disabled:o}=e,{visiblePanel:n}=u;if(!o&&n){const e=h.value;Ce(t,e).flag?j():q()}},Z=t=>{const{disabled:o}=e,{visiblePanel:n}=u;if(!o){const e=d.value,o=h.value;u.isActivated=Ce(t,e).flag||Ce(t,o).flag,n&&!u.isActivated&&q()}},ee=(e,t)=>{const{visibleOptionList:o,visibleGroupList:n}=u,l=R.value,r=y.value,a=E.value;let i,s,c,d;if(l)for(let u=0;u<n.length;u++){const o=n[u],l=o[a],p=o.disabled;if(l)for(let n=0;n<l.length;n++){const o=l[n],a=Mn(o),u=p||o.disabled;if(i||u||(i=o),d&&a&&!u&&(c=o,!t))return{offsetOption:c};if(e===o[r]){if(d=o,t)return{offsetOption:s}}else a&&!u&&(s=o)}}else for(let u=0;u<o.length;u++){const n=o[u],l=n.disabled;if(i||l||(i=n),d&&!l&&(c=n,!t))return{offsetOption:c};if(e===n[r]){if(d=n,t)return{offsetOption:s}}else l||(s=n)}return{firstOption:i}},ne=t=>{const{clearable:o,disabled:n}=e,{visiblePanel:l,currentValue:r,currentOption:a}=u;if(!n){const e=Jo(t,Xo.TAB),n=Jo(t,Xo.ENTER),i=Jo(t,Xo.ESCAPE),s=Jo(t,Xo.ARROW_UP),c=Jo(t,Xo.ARROW_DOWN),d=Jo(t,Xo.DELETE),p=Jo(t,Xo.SPACEBAR);if(e&&(u.isActivated=!1),l)if(i||e)q();else if(n)t.preventDefault(),t.stopPropagation(),Y(t,r,a);else if(s||c){t.preventDefault();let{firstOption:e,offsetOption:o}=ee(r,s);o||$(r)||(o=e),V(o),B(o,c)}else p&&t.preventDefault();else(s||c||n||p)&&u.isActivated&&(t.preventDefault(),W());u.isActivated&&d&&o&&G(t,null)}},le=()=>{q()},re=()=>{e.filterable&&(0,H.nextTick)((()=>{const e=m.value;e&&e.focus()}))},ae=t=>{e.disabled||(u.isActivated=!0),x.dispatchEvent("focus",{},t)},ie=e=>{u.isActivated=!1,x.dispatchEvent("blur",{},e)},se=e=>{u.searchValue=e},ce=()=>{u.isActivated=!0},ue=e=>{const{$event:t}=e,o=Jo(t,Xo.ENTER);o&&(t.preventDefault(),t.stopPropagation())},de=a().debounce((function(){const{remote:t,remoteMethod:o}=e,{searchValue:n}=u;t&&o?(u.searchLoading=!0,Promise.resolve(o({searchValue:n})).then((()=>(0,H.nextTick)())).catch((()=>(0,H.nextTick)())).finally((()=>{u.searchLoading=!1,L()}))):L()}),350,{trailing:!0}),pe=e=>{const{$event:t}=e;t.preventDefault(),u.visiblePanel?q():W()},me=(e,t,o)=>{if(t.disabled)return!0;if(o&&o.disabled)return!0;const n=S.value;return!(!n||e)},fe=(t,n)=>{const{optionKey:l,modelValue:r,multiple:i}=e,{currentValue:s}=u,c=k.value,d=C.value,p=y.value,m=R.value,{useKey:f}=c,h=o.option;return t.map(((e,t)=>{const{slots:o,className:c}=e,u=e[p],g=i?r&&r.indexOf(u)>-1:r===u,x=!m||Mn(e),b=me(g,e,n),w=P(e),C=o?o.default:null,y={option:e,group:null,$select:v};return x?(0,H.h)("div",{key:f||l?w:t,class:["vxe-select-option",c?a().isFunction(c)?c(y):c:"",{"is--disabled":b,"is--selected":g,"is--hover":s===u}],optid:w,onMousedown:e=>{const t=0===e.button;t&&e.stopPropagation()},onClick:t=>{b||Y(t,u,e)},onMouseenter:()=>{b||V(e)}},h?M(h,y):C?M(C,y):oe(te(e[d]))):null}))},he=()=>{const{optionKey:t}=e,{visibleGroupList:n}=u,l=k.value,r=T.value,i=E.value,{useKey:s}=l,c=o.option;return n.map(((e,o)=>{const{slots:n,className:l}=e,u=P(e),d=e.disabled,p=n?n.default:null,m={option:e,group:e,$select:v};return(0,H.h)("div",{key:s||t?u:o,class:["vxe-optgroup",l?a().isFunction(l)?l(m):l:"",{"is--disabled":d}],optid:u},[(0,H.h)("div",{class:"vxe-optgroup--title"},c?M(c,m):p?M(p,m):te(e[r])),(0,H.h)("div",{class:"vxe-optgroup--wrapper"},fe(e[i]||[],e))])}))},ge=()=>{const{visibleGroupList:t,visibleOptionList:o,searchLoading:n}=u,l=R.value;if(n)return[(0,H.h)("div",{class:"vxe-select--search-loading"},[(0,H.h)("i",{class:["vxe-select--search-icon",c.icon.SELECT_LOADED]}),(0,H.h)("span",{class:"vxe-select--search-text"},c.i18n("vxe.select.loadingText"))])];if(l){if(t.length)return he()}else if(o.length)return fe(o);return[(0,H.h)("div",{class:"vxe-select--empty-placeholder"},e.emptyText||c.i18n("vxe.select.emptyText"))]};x={dispatchEvent(e,t,o){n(e,Object.assign({$select:v,$event:o},t))},isPanelVisible(){return u.visiblePanel},togglePanel(){return u.visiblePanel?q():W(),(0,H.nextTick)()},hidePanel(){return u.visiblePanel&&q(),(0,H.nextTick)()},showPanel(){return u.visiblePanel||W(),(0,H.nextTick)()},refreshOption:L,focus(){const e=p.value;return u.isActivated=!0,e.blur(),(0,H.nextTick)()},blur(){const e=p.value;return e.blur(),u.isActivated=!1,(0,H.nextTick)()}},Object.assign(v,x),(0,H.watch)((()=>u.staticOptions),(e=>{e.some((e=>e.options&&e.options.length))?(u.fullOptionList=[],u.fullGroupList=e):(u.fullGroupList=[],u.fullOptionList=e||[]),A()})),(0,H.watch)((()=>e.options),(e=>{u.fullGroupList=[],u.fullOptionList=e||[],A()})),(0,H.watch)((()=>e.optionGroups),(e=>{u.fullOptionList=[],u.fullGroupList=e||[],A()})),(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{options:t,optionGroups:o}=e;o?u.fullGroupList=o:t&&(u.fullOptionList=t),A()})),en.on(v,"mousewheel",K),en.on(v,"mousedown",Z),en.on(v,"keydown",ne),en.on(v,"blur",le)})),(0,H.onUnmounted)((()=>{en.off(v,"mousewheel"),en.off(v,"mousedown"),en.off(v,"keydown"),en.off(v,"blur")}));const ve=()=>{const{className:t,popupClassName:n,transfer:l,disabled:r,loading:i,filterable:g}=e,{inited:x,isActivated:b,visiblePanel:w}=u,C=s.value,y=F.value,T=o.default,E=o.header,S=o.footer,k=o.prefix;return(0,H.h)("div",{ref:d,class:["vxe-select",t?a().isFunction(t)?t({$select:v}):t:"",{[`size--${C}`]:C,"is--visivle":w,"is--disabled":r,"is--filter":g,"is--loading":i,"is--active":b}]},[(0,H.h)("div",{class:"vxe-select-slots",ref:"hideOption"},T?T({}):[]),(0,H.h)(Rn,{ref:p,clearable:e.clearable,placeholder:e.placeholder,readonly:!0,disabled:r,type:"text",prefixIcon:e.prefixIcon,suffixIcon:i?c.icon.SELECT_LOADED:w?c.icon.SELECT_OPEN:c.icon.SELECT_CLOSE,modelValue:y,onClear:X,onClick:pe,onFocus:ae,onBlur:ie,onSuffixClick:pe},k?{prefix:()=>k({})}:{}),(0,H.h)(H.Teleport,{to:"body",disabled:!l||!x},[(0,H.h)("div",{ref:h,class:["vxe-table--ignore-clear vxe-select--panel",n?a().isFunction(n)?n({$select:v}):n:"",{[`size--${C}`]:C,"is--transfer":l,"animat--leave":!i&&u.animatVisible,"animat--enter":!i&&w}],placement:u.panelPlacement,style:u.panelStyle},x?[g?(0,H.h)("div",{class:"vxe-select--panel-search"},[(0,H.h)(Rn,{ref:m,class:"vxe-select-search--input",modelValue:u.searchValue,clearable:!0,placeholder:c.i18n("vxe.select.search"),prefixIcon:c.icon.INPUT_SEARCH,"onUpdate:modelValue":se,onFocus:ce,onKeydown:ue,onChange:de,onSearch:de})]):(0,H.createCommentVNode)(),(0,H.h)("div",{class:"vxe-select--panel-wrapper"},[E?(0,H.h)("div",{class:"vxe-select--panel-header"},E({})):(0,H.createCommentVNode)(),(0,H.h)("div",{class:"vxe-select--panel-body"},[(0,H.h)("div",{ref:f,class:"vxe-select-option--wrapper"},ge())]),S?(0,H.h)("div",{class:"vxe-select--panel-footer"},S({})):(0,H.createCommentVNode)()])]:[])])])};return v.renderVN=ve,(0,H.provide)("$xeselect",v),v},render(){return this.renderVN()}}),Dn=(0,H.defineComponent)({name:"VxeTableExportPanel",props:{defaultOptions:Object,storeData:Object},setup(e){const t=(0,H.inject)("$xetable",{}),{computeExportOpts:o,computePrintOpts:n}=t.getComputeMaps(),l=(0,H.reactive)({isAll:!1,isIndeterminate:!1,loading:!1}),r=(0,H.ref)(),i=(0,H.ref)(),s=(0,H.ref)(),u=(0,H.computed)((()=>{const{storeData:t}=e;return t.columns.every((e=>e.checked))})),d=(0,H.computed)((()=>{const{defaultOptions:t}=e;return["html","xml","xlsx","pdf"].indexOf(t.type)>-1})),p=(0,H.computed)((()=>{const{storeData:t,defaultOptions:o}=e;return!o.original&&"current"===o.mode&&(t.isPrint||["html","xlsx"].indexOf(o.type)>-1)})),m=(0,H.computed)((()=>{const{defaultOptions:t}=e;return!t.original&&["xlsx"].indexOf(t.type)>-1})),f=t=>{const{storeData:o}=e,n=a().findTree(o.columns,(e=>e===t));if(n&&n.parent){const{parent:e}=n;e.children&&e.children.length&&(e.checked=e.children.every((e=>e.checked)),e.halfChecked=!e.checked&&e.children.some((e=>e.checked||e.halfChecked)),f(e))}},h=()=>{const{storeData:t}=e,o=t.columns;l.isAll=o.every((e=>e.disabled||e.checked)),l.isIndeterminate=!l.isAll&&o.some((e=>!e.disabled&&(e.checked||e.halfChecked)))},g=e=>{const t=!e.checked;a().eachTree([e],(e=>{e.checked=t,e.halfChecked=!1})),f(e),h()},v=()=>{const{storeData:t}=e,o=!l.isAll;a().eachTree(t.columns,(e=>{e.disabled||(e.checked=o,e.halfChecked=!1)})),l.isAll=o,h()},x=()=>{(0,H.nextTick)((()=>{const e=i.value,t=s.value,o=r.value,n=e||t||o;n&&n.focus()})),h()},b=()=>{const{storeData:t,defaultOptions:o}=e,{hasMerge:n,columns:l}=t,r=u.value,i=p.value,s=a().searchTree(l,(e=>e.checked),{children:"children",mapChildren:"childNodes",original:!0});return Object.assign({},o,{columns:s,isMerge:!!(n&&i&&r)&&o.isMerge})},w=()=>{const{storeData:o}=e,l=n.value;o.visible=!1,t.print(Object.assign({},l,b()))},C=()=>{const{storeData:n}=e,r=o.value;l.loading=!0,t.exportData(Object.assign({},r,b())).then((()=>{l.loading=!1,n.visible=!1})).catch((()=>{l.loading=!1}))},y=()=>{const{storeData:t}=e;t.visible=!1},T=()=>{const{storeData:t}=e;t.isPrint?w():C()},E=()=>{const{defaultOptions:t,storeData:o}=e,{isAll:n,isIndeterminate:f}=l,{hasTree:h,hasMerge:b,isPrint:w,hasColgroup:C}=o,{isHeader:E}=t,S=[],k=u.value,R=d.value,O=p.value,M=m.value;return a().eachTree(o.columns,(e=>{const t=oe(e.getTitle(),1),o=e.children&&e.children.length,n=e.checked,l=e.halfChecked;S.push((0,H.h)("li",{class:["vxe-export--panel-column-option",`level--${e.level}`,{"is--group":o,"is--checked":n,"is--indeterminate":l,"is--disabled":e.disabled}],title:t,onClick:()=>{e.disabled||g(e)}},[(0,H.h)("span",{class:["vxe-checkbox--icon",l?c.icon.TABLE_CHECKBOX_INDETERMINATE:n?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,H.h)("span",{class:"vxe-checkbox--label"},t)]))})),(0,H.h)(bn,{modelValue:o.visible,title:c.i18n(w?"vxe.export.printTitle":"vxe.export.expTitle"),className:"vxe-table-export-popup-wrapper",width:660,mask:!0,lockView:!0,showFooter:!1,escClosable:!0,maskClosable:!0,loading:l.loading,"onUpdate:modelValue"(e){o.visible=e},onShow:x},{default:()=>(0,H.h)("div",{class:"vxe-export--panel"},[(0,H.h)("table",{cellspacing:0,cellpadding:0,border:0},[(0,H.h)("tbody",[[w?(0,H.createCommentVNode)():(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.export.expName")),(0,H.h)("td",[(0,H.h)(Rn,{ref:i,modelValue:t.filename,type:"text",clearable:!0,placeholder:c.i18n("vxe.export.expNamePlaceholder"),"onUpdate:modelValue"(e){t.filename=e}})])]),w?(0,H.createCommentVNode)():(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.export.expType")),(0,H.h)("td",[(0,H.h)(In,{modelValue:t.type,options:o.typeList.map((e=>({value:e.value,label:c.i18n(e.label)}))),"onUpdate:modelValue"(e){t.type=e}})])]),w||R?(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.export.expSheetName")),(0,H.h)("td",[(0,H.h)(Rn,{ref:s,modelValue:t.sheetName,type:"text",clearable:!0,placeholder:c.i18n("vxe.export.expSheetNamePlaceholder"),"onUpdate:modelValue"(e){t.sheetName=e}})])]):(0,H.createCommentVNode)(),(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.export.expMode")),(0,H.h)("td",[(0,H.h)(In,{modelValue:t.mode,options:o.modeList.map((e=>({value:e.value,label:c.i18n(e.label)}))),"onUpdate:modelValue"(e){t.mode=e}})])]),(0,H.h)("tr",[(0,H.h)("td",[c.i18n("vxe.export.expColumn")]),(0,H.h)("td",[(0,H.h)("div",{class:"vxe-export--panel-column"},[(0,H.h)("ul",{class:"vxe-export--panel-column-header"},[(0,H.h)("li",{class:["vxe-export--panel-column-option",{"is--checked":n,"is--indeterminate":f}],title:c.i18n("vxe.table.allTitle"),onClick:v},[(0,H.h)("span",{class:["vxe-checkbox--icon",f?c.icon.TABLE_CHECKBOX_INDETERMINATE:n?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,H.h)("span",{class:"vxe-checkbox--label"},c.i18n("vxe.export.expCurrentColumn"))])]),(0,H.h)("ul",{class:"vxe-export--panel-column-body"},S)])])]),(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.export.expOpts")),(0,H.h)("td",[(0,H.h)("div",{class:"vxe-export--panel-option-row"},[(0,H.h)(On,{modelValue:t.isHeader,title:c.i18n("vxe.export.expHeaderTitle"),content:c.i18n("vxe.export.expOptHeader"),"onUpdate:modelValue"(e){t.isHeader=e}}),(0,H.h)(On,{modelValue:t.isFooter,disabled:!o.hasFooter,title:c.i18n("vxe.export.expFooterTitle"),content:c.i18n("vxe.export.expOptFooter"),"onUpdate:modelValue"(e){t.isFooter=e}}),(0,H.h)(On,{modelValue:t.original,title:c.i18n("vxe.export.expOriginalTitle"),content:c.i18n("vxe.export.expOptOriginal"),"onUpdate:modelValue"(e){t.original=e}})]),(0,H.h)("div",{class:"vxe-export--panel-option-row"},[(0,H.h)(On,{modelValue:!!(E&&C&&O)&&t.isColgroup,title:c.i18n("vxe.export.expColgroupTitle"),disabled:!E||!C||!O,content:c.i18n("vxe.export.expOptColgroup"),"onUpdate:modelValue"(e){t.isColgroup=e}}),(0,H.h)(On,{modelValue:!!(b&&O&&k)&&t.isMerge,title:c.i18n("vxe.export.expMergeTitle"),disabled:!b||!O||!k,content:c.i18n("vxe.export.expOptMerge"),"onUpdate:modelValue"(e){t.isMerge=e}}),w?(0,H.createCommentVNode)():(0,H.h)(On,{modelValue:!!M&&t.useStyle,disabled:!M,title:c.i18n("vxe.export.expUseStyleTitle"),content:c.i18n("vxe.export.expOptUseStyle"),"onUpdate:modelValue"(e){t.useStyle=e}}),(0,H.h)(On,{modelValue:!!h&&t.isAllExpand,disabled:!h,title:c.i18n("vxe.export.expAllExpandTitle"),content:c.i18n("vxe.export.expOptAllExpand"),"onUpdate:modelValue"(e){t.isAllExpand=e}})])])])]])]),(0,H.h)("div",{class:"vxe-export--panel-btns"},[(0,H.h)(mn,{content:c.i18n("vxe.export.expCancel"),onClick:y}),(0,H.h)(mn,{ref:r,status:"primary",content:c.i18n(w?"vxe.export.expPrint":"vxe.export.expConfirm"),onClick:T})])])})};return E}}),Fn=(0,H.defineComponent)({name:"VxeRadio",props:{modelValue:[String,Number,Boolean],label:{type:[String,Number,Boolean],default:null},title:[String,Number],content:[String,Number],disabled:Boolean,name:String,strict:{type:Boolean,default:()=>c.radio.strict},size:{type:String,default:()=>c.radio.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s={xID:i,props:e,context:t},c=pn(e),u=(0,H.inject)("$xeradiogroup",null);let d={};const p=(0,H.computed)((()=>e.disabled||u&&u.props.disabled)),m=(0,H.computed)((()=>u?u.name:e.name)),f=(0,H.computed)((()=>u?u.props.strict:e.strict)),h=(0,H.computed)((()=>{const{modelValue:t,label:o}=e;return u?u.props.modelValue===o:t===o})),g=(e,t)=>{u?u.handleChecked({label:e},t):(n("update:modelValue",e),d.dispatchEvent("change",{label:e},t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,e))},v=t=>{const o=p.value;o||g(e.label,t)},x=t=>{const o=p.value,n=f.value;o||n||e.label===(u?u.props.modelValue:e.modelValue)&&g(null,t)};d={dispatchEvent(e,t,o){n(e,Object.assign({$radio:s,$event:o},t))}},Object.assign(s,d);const b=()=>{const t=c.value,n=p.value,l=m.value,r=h.value;return(0,H.h)("label",{class:["vxe-radio",{[`size--${t}`]:t,"is--checked":r,"is--disabled":n}],title:e.title},[(0,H.h)("input",{class:"vxe-radio--input",type:"radio",name:l,checked:r,disabled:n,onChange:v,onClick:x}),(0,H.h)("span",{class:["vxe-radio--icon",r?"vxe-icon-radio-checked":"vxe-icon-radio-unchecked"]}),(0,H.h)("span",{class:"vxe-radio--label"},o.default?o.default({}):te(e.content))])};return s.renderVN=b,s},render(){return this.renderVN()}}),Nn=(0,H.defineComponent)({name:"VxeRadioButton",props:{modelValue:[String,Number,Boolean],label:{type:[String,Number,Boolean],default:null},title:[String,Number],content:[String,Number],disabled:Boolean,strict:{type:Boolean,default:()=>c.radioButton.strict},size:{type:String,default:()=>c.radioButton.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=pn(e),c={xID:i,props:e,context:t};let u={};const d=(0,H.inject)("$xeradiogroup",null),p=(0,H.computed)((()=>e.disabled||d&&d.props.disabled)),m=(0,H.computed)((()=>d?d.name:null)),f=(0,H.computed)((()=>d?d.props.strict:e.strict)),h=(0,H.computed)((()=>{const{modelValue:t,label:o}=e;return d?d.props.modelValue===o:t===o}));u={dispatchEvent(e,t,o){n(e,Object.assign({$radioButton:c,$event:o},t))}},Object.assign(c,u);const g=(e,t)=>{d?d.handleChecked({label:e},t):(n("update:modelValue",e),u.dispatchEvent("change",{label:e},t),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,e))},v=t=>{const o=p.value;o||g(e.label,t)},x=t=>{const o=p.value,n=f.value;o||n||e.label===(d?d.props.modelValue:e.modelValue)&&g(null,t)},b=()=>{const t=s.value,n=p.value,l=m.value,r=h.value;return(0,H.h)("label",{class:["vxe-radio","vxe-radio-button",{[`size--${t}`]:t,"is--disabled":n}],title:e.title},[(0,H.h)("input",{class:"vxe-radio--input",type:"radio",name:l,checked:r,disabled:n,onChange:v,onClick:x}),(0,H.h)("span",{class:"vxe-radio--label"},o.default?o.default({}):te(e.content))])};return Object.assign(c,{renderVN:b,dispatchEvent:dispatchEvent}),b}}),Pn=(0,H.defineComponent)({name:"VxeRadioGroup",props:{modelValue:[String,Number,Boolean],disabled:Boolean,type:String,options:Array,optionProps:Object,strict:{type:Boolean,default:()=>c.radioGroup.strict},size:{type:String,default:()=>c.radioGroup.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s={xID:i,props:e,context:t,name:a().uniqueId("xegroup_")},c=(0,H.computed)((()=>e.optionProps||{})),u=(0,H.computed)((()=>{const e=c.value;return e.label||"label"})),d=(0,H.computed)((()=>{const e=c.value;return e.value||"value"})),p=(0,H.computed)((()=>{const e=c.value;return e.disabled||"disabled"}));let m={};pn(e);const f={handleChecked(e,t){n("update:modelValue",e.label),m.dispatchEvent("change",e),l&&r&&l.triggerItemEvent(t,r.itemConfig.field,e.label)}};m={dispatchEvent(e,t,o){n(e,Object.assign({$radioGroup:s,$event:o},t))}};const h=()=>{const{options:t,type:n}=e,l=o.default,r=d.value,a=u.value,i=p.value,s="button"===n?Nn:Fn;return(0,H.h)("div",{class:"vxe-radio-group"},l?l({}):t?t.map((e=>(0,H.h)(s,{label:e[r],content:e[a],disabled:e[i]}))):[])};return Object.assign(s,f,{renderVN:h,dispatchEvent:dispatchEvent}),(0,H.provide)("$xeradiogroup",s),h}}),Ln=(0,H.defineComponent)({name:"VxeTableImportPanel",props:{defaultOptions:Object,storeData:Object},setup(e){const t=(0,H.inject)("$xetable",{}),{computeImportOpts:o}=t.getComputeMaps(),n=(0,H.reactive)({loading:!1}),l=(0,H.ref)(),r=(0,H.computed)((()=>{const{storeData:t}=e;return`${t.filename}.${t.type}`})),i=(0,H.computed)((()=>{const{storeData:t}=e;return t.file&&t.type})),s=(0,H.computed)((()=>{const{storeData:t}=e,{type:o,typeList:n}=t;if(o){const e=a().find(n,(e=>o===e.value));return e?c.i18n(e.label):"*.*"}return`*.${n.map((e=>e.value)).join(", *.")}`})),u=()=>{const{storeData:t}=e;Object.assign(t,{filename:"",sheetName:"",type:""})},d=()=>{const{storeData:o,defaultOptions:n}=e;t.readFile(n).then((e=>{const{file:t}=e;Object.assign(o,Z(t),{file:t})})).catch((e=>e))},p=()=>{(0,H.nextTick)((()=>{const e=l.value;e&&e.focus()}))},m=()=>{const{storeData:t}=e;t.visible=!1},f=()=>{const{storeData:l,defaultOptions:r}=e,a=o.value;n.loading=!0,t.importByFile(l.file,Object.assign({},a,r)).then((()=>{n.loading=!1,l.visible=!1})).catch((()=>{n.loading=!1}))},h=()=>{const{defaultOptions:t,storeData:o}=e,a=r.value,h=i.value,g=s.value;return(0,H.h)(bn,{modelValue:o.visible,title:c.i18n("vxe.import.impTitle"),className:"vxe-table-import-popup-wrapper",width:440,mask:!0,lockView:!0,showFooter:!1,escClosable:!0,maskClosable:!0,loading:n.loading,"onUpdate:modelValue"(e){o.visible=e},onShow:p},{default:()=>(0,H.h)("div",{class:"vxe-export--panel"},[(0,H.h)("table",{cellspacing:0,cellpadding:0,border:0},[(0,H.h)("tbody",[(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.import.impFile")),(0,H.h)("td",[h?(0,H.h)("div",{class:"vxe-import-selected--file",title:a},[(0,H.h)("span",a),(0,H.h)("i",{class:c.icon.INPUT_CLEAR,onClick:u})]):(0,H.h)("button",{ref:l,class:"vxe-import-select--file",onClick:d},c.i18n("vxe.import.impSelect"))])]),(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.import.impType")),(0,H.h)("td",g)]),(0,H.h)("tr",[(0,H.h)("td",c.i18n("vxe.import.impOpts")),(0,H.h)("td",[(0,H.h)(Pn,{modelValue:t.mode,"onUpdate:modelValue"(e){t.mode=e}},{default:()=>o.modeList.map((e=>(0,H.h)(Fn,{label:e.value,content:c.i18n(e.label)})))})])])])]),(0,H.h)("div",{class:"vxe-export--panel-btns"},[(0,H.h)(mn,{content:c.i18n("vxe.import.impCancel"),onClick:m}),(0,H.h)(mn,{status:"primary",disabled:!h,content:c.i18n("vxe.import.impConfirm"),onClick:f})])])})};return h}});l(4603),l(7566),l(8721);let An,Vn,Bn;const _n='body{margin:0;padding: 0 1px;color:#333333;font-size:14px;font-family:"Microsoft YaHei",微软雅黑,"MicrosoftJhengHei",华文细黑,STHeiti,MingLiu}body *{-webkit-box-sizing:border-box;box-sizing:border-box}.vxe-table{border-collapse:collapse;text-align:left;border-spacing:0}.vxe-table:not(.is--print){table-layout:fixed}.vxe-table,.vxe-table th,.vxe-table td,.vxe-table td{border-color:#D0D0D0;border-style:solid;border-width:0}.vxe-table.is--print{width:100%}.border--default,.border--full,.border--outer{border-top-width:1px}.border--default,.border--full,.border--outer{border-left-width:1px}.border--outer,.border--default th,.border--default td,.border--full th,.border--full td,.border--outer th,.border--inner th,.border--inner td{border-bottom-width:1px}.border--default,.border--outer,.border--full th,.border--full td{border-right-width:1px}.border--default th,.border--full th,.border--outer th{background-color:#f8f8f9}.vxe-table td>div,.vxe-table th>div{padding:.5em .4em}.col--center{text-align:center}.col--right{text-align:right}.vxe-table:not(.is--print) .col--ellipsis>div{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-break:break-all}.vxe-table--tree-node{text-align:left}.vxe-table--tree-node-wrapper{position:relative}.vxe-table--tree-icon-wrapper{position:absolute;top:50%;width:1em;height:1em;text-align:center;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:pointer}.vxe-table--tree-unfold-icon,.vxe-table--tree-fold-icon{position:absolute;width:0;height:0;border-style:solid;border-width:.5em;border-right-color:transparent;border-bottom-color:transparent}.vxe-table--tree-unfold-icon{left:.3em;top:0;border-left-color:#939599;border-top-color:transparent}.vxe-table--tree-fold-icon{left:0;top:.3em;border-left-color:transparent;border-top-color:#939599}.vxe-table--tree-cell{display:block;padding-left:1.5em}.vxe-table input[type="checkbox"]{margin:0}.vxe-table input[type="checkbox"],.vxe-table input[type="radio"],.vxe-table input[type="checkbox"]+span,.vxe-table input[type="radio"]+span{vertical-align:middle;padding-left:0.4em}';function jn(){const e=document.createElement("iframe");return e.className="vxe-table--print-frame",e}function Hn(e,t){return new Blob([e],{type:`text/${t.type};charset=utf-8;`})}function zn(e,t){const{style:o}=e;return["<!DOCTYPE html><html>","<head>",'<meta charset="utf-8"><meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,minimal-ui">',`<title>${e.sheetName}</title>`,'<style media="print">.vxe-page-break-before{page-break-before:always;}.vxe-page-break-after{page-break-after:always;}</style>',`<style>${_n}</style>`,o?`<style>${o}</style>`:"","</head>",`<body>${t}</body>`,"</html>"].join("")}const Wn=e=>{const t=Object.assign({},e);return An||(An=document.createElement("form"),Vn=document.createElement("input"),An.className="vxe-table--file-form",Vn.name="file",Vn.type="file",An.appendChild(Vn),document.body.appendChild(An)),new Promise(((e,o)=>{const n=t.types||[],l=!n.length||n.some((e=>"*"===e));Vn.multiple=!!t.multiple,Vn.accept=l?"":`.${n.join(", .")}`,Vn.onchange=r=>{const{files:i}=r.target,s=i[0];let u="";if(!l)for(let e=0;e<i.length;e++){const{type:t}=Z(i[e]);if(!a().includes(n,t)){u=t;break}}if(u){!1!==t.message&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:c.i18n("vxe.error.notType",[u]),status:"error"}));const e={status:!1,files:i,file:s};o(e)}else e({status:!0,files:i,file:s})},An.reset(),Vn.click()}))};function qn(){if(Bn){if(Bn.parentNode){try{Bn.contentDocument.write("")}catch(e){}Bn.parentNode.removeChild(Bn)}Bn=null}}function Un(){Bn.parentNode||document.body.appendChild(Bn)}function Gn(){requestAnimationFrame(qn)}function Xn(e,t,o=""){const{beforePrintMethod:n}=t;n&&(o=n({content:o,options:t,$table:e})||""),o=zn(t,o);const l=Hn(o,t);ae.msie?(qn(),Bn=jn(),Un(),Bn.contentDocument.write(o),Bn.contentDocument.execCommand("print")):(Bn||(Bn=jn(),Bn.onload=e=>{e.target.src&&(e.target.contentWindow.onafterprint=Gn,e.target.contentWindow.print())}),Un(),Bn.src=URL.createObjectURL(l))}const Yn=e=>{const{filename:t,type:o,content:n}=e,l=`${t}.${o}`;if(window.Blob){const t=n instanceof Blob?n:Hn(a().toValueString(n),e);if(navigator.msSaveBlob)navigator.msSaveBlob(t,l);else{const e=URL.createObjectURL(t),o=document.createElement("a");o.target="_blank",o.download=l,o.href=e,document.body.appendChild(o),o.click(),requestAnimationFrame((()=>{o.parentNode&&o.parentNode.removeChild(o),URL.revokeObjectURL(e)}))}return Promise.resolve()}return Promise.reject(new Error(L("vxe.error.notExp")))};let Kn;const Zn="\ufeff",Jn="\r\n";function Qn(e){return e.property||["seq","checkbox","radio"].indexOf(e.type)>-1}const el=e=>{const t=[];return e.forEach((e=>{e.childNodes&&e.childNodes.length?(t.push(e),t.push(...el(e.childNodes))):t.push(e)})),t},tl=e=>{let t=1;const o=(e,n)=>{if(n&&(e._level=n._level+1,t<e._level&&(t=e._level)),e.childNodes&&e.childNodes.length){let t=0;e.childNodes.forEach((n=>{o(n,e),t+=n._colSpan})),e._colSpan=t}else e._colSpan=1};e.forEach((e=>{e._level=1,o(e)}));const n=[];for(let r=0;r<t;r++)n.push([]);const l=el(e);return l.forEach((e=>{e.childNodes&&e.childNodes.length?e._rowSpan=1:e._rowSpan=t-e._level+1,n[e._level-1].push(e)})),n};function ol(e){return!0===e?"full":e||"default"}function nl(e){return"TRUE"===e||"true"===e||!0===e}function ll(e,t){const{footerFilterMethod:o}=e;return o?t.filter(((e,t)=>o({items:e,$rowIndex:t}))):t}function rl(e,t){if(t){if("seq"===e.type)return`\t${t}`;switch(e.cellType){case"string":if(!isNaN(t))return`\t${t}`;break;case"number":break;default:if(t.length>=12&&!isNaN(t))return`\t${t}`;break}}return t}function al(e){return/[",\s\n]/.test(e)?`"${e.replace(/"/g,'""')}"`:e}function il(e,t){return e.getElementsByTagName(t)}function sl(e){return`#${e}@${a().uniqueId()}`}function cl(e,t){return e.replace(/#\d+@\d+/g,(e=>a().hasOwnProp(t,e)?t[e]:e))}function ul(e,t){const o=cl(e,t);return o.replace(/^"+$/g,(e=>'"'.repeat(Math.ceil(e.length/2))))}function dl(e,t,o){const n=t.split(Jn),l=[];let r=[];if(n.length){const e={},t=Date.now();n.forEach((n=>{if(n){const a={};n=n.replace(/("")|(\n)/g,((o,n)=>{const l=sl(t);return e[l]=n?'"':"\n",l})).replace(/"(.*?)"/g,((o,n)=>{const l=sl(t);return e[l]=cl(n,e),l}));const i=n.split(o);r.length?(i.forEach(((t,o)=>{o<r.length&&(a[r[o]]=ul(t.trim(),e))})),l.push(a)):r=i.map((t=>ul(t.trim(),e)))}}))}return{fields:r,rows:l}}function pl(e,t){return dl(e,t,",")}function ml(e,t){return dl(e,t,"\t")}function fl(e,t){const o=new DOMParser,n=o.parseFromString(t,"text/html"),l=il(n,"body"),r=[],i=[];if(l.length){const e=il(l[0],"table");if(e.length){const t=il(e[0],"thead");if(t.length){a().arrayEach(il(t[0],"tr"),(e=>{a().arrayEach(il(e,"th"),(e=>{i.push(e.textContent)}))}));const o=il(e[0],"tbody");o.length&&a().arrayEach(il(o[0],"tr"),(e=>{const t={};a().arrayEach(il(e,"td"),((e,o)=>{i[o]&&(t[i[o]]=e.textContent||"")})),r.push(t)}))}}}return{fields:i,rows:r}}function hl(e,t){const o=new DOMParser,n=o.parseFromString(t,"application/xml"),l=il(n,"Worksheet"),r=[],i=[];if(l.length){const e=il(l[0],"Table");if(e.length){const t=il(e[0],"Row");t.length&&(a().arrayEach(il(t[0],"Cell"),(e=>{i.push(e.textContent)})),a().arrayEach(t,((e,t)=>{if(t){const t={},o=il(e,"Cell");a().arrayEach(o,((e,o)=>{i[o]&&(t[i[o]]=e.textContent)})),r.push(t)}})))}}return{fields:i,rows:r}}function gl(e){a().eachTree(e,(e=>{delete e._level,delete e._colSpan,delete e._rowSpan,delete e._children,delete e.childNodes}),{children:"children"})}function vl(e,t){const o=[];return e.forEach((e=>{const t=e.property;t&&o.push(t)})),t.some((e=>o.indexOf(e)>-1))}const xl=["exportData","importByFile","importData","saveFile","readFile","print","openImport","openExport","openPrint"],bl={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{computeTreeOpts:l,computePrintOpts:r,computeExportOpts:i,computeImportOpts:s,computeCustomOpts:u,computeSeqOpts:d,computeRadioOpts:p,computeCheckboxOpts:m,computeColumnOpts:f}=e.getComputeMaps(),h=(0,H.inject)("$xegrid",null),g=e=>{const t=l.value,o=t.children||t.childrenField;return e[o]&&e[o].length},v=(t,o,n,l)=>{const r=d.value,a=r.seqMethod||n.seqMethod;return a?a({row:t,rowIndex:e.getRowIndex(t),$rowIndex:o,column:n,columnIndex:e.getColumnIndex(n),$columnIndex:l}):e.getRowSeq(t)};function x(t,o){const n=f.value,l=o.headerExportMethod||n.headerExportMethod;return l?l({column:o,options:t,$table:e}):(t.original?o.property:o.getTitle())||""}const b=e=>a().isBoolean(e)?e?"TRUE":"FALSE":e,w=(o,n,r)=>{const{isAllExpand:i,mode:s}=o,{treeConfig:c}=t,u=p.value,d=m.value,h=l.value,x=f.value;if(Kn||(Kn=document.createElement("div")),c){const t=h.children||h.childrenField,l=[],c=new Map;return a().eachTree(r,((t,r,p,m,f,h)=>{const w=t._row||t,C=f&&f._row?f._row:f;if(i||!C||c.has(C)&&e.isTreeExpandByRow(C)){const t=g(w),i={_row:w,_level:h.length-1,_hasChild:t,_expand:t&&e.isTreeExpandByRow(w)};n.forEach(((t,n)=>{let l="";const c=t.editRender||t.cellRender;let p=t.exportMethod;if(!p&&c&&c.name){const e=No.renderer.get(c.name);e&&(p=e.exportMethod)}if(p||(p=x.exportMethod),p)l=p({$table:e,row:w,column:t,options:o});else switch(t.type){case"seq":l="all"===s?m.map(((e,t)=>t%2===0?Number(e)+1:".")).join(""):v(w,r,t,n);break;case"checkbox":l=b(e.isCheckedByCheckboxRow(w)),i._checkboxLabel=d.labelField?a().get(w,d.labelField):"",i._checkboxDisabled=d.checkMethod&&!d.checkMethod({row:w});break;case"radio":l=b(e.isCheckedByRadioRow(w)),i._radioLabel=u.labelField?a().get(w,u.labelField):"",i._radioDisabled=u.checkMethod&&!u.checkMethod({row:w});break;default:if(o.original)l=ze(w,t);else if(l=e.getCellLabel(w,t),"html"===t.type)Kn.innerHTML=l,l=Kn.innerText.trim();else{const o=e.getCell(w,t);o&&(l=o.innerText.trim())}}i[t.id]=a().toValueString(l)})),c.set(w,1),l.push(Object.assign(i,w))}}),{children:t}),l}return r.map(((t,l)=>{const r={_row:t};return n.forEach(((n,i)=>{let c="";const p=n.editRender||n.cellRender;let m=n.exportMethod;if(!m&&p&&p.name){const e=No.renderer.get(p.name);e&&(m=e.exportMethod)}if(m)c=m({$table:e,row:t,column:n,options:o});else switch(n.type){case"seq":c="all"===s?l+1:v(t,l,n,i);break;case"checkbox":c=b(e.isCheckedByCheckboxRow(t)),r._checkboxLabel=d.labelField?a().get(t,d.labelField):"",r._checkboxDisabled=d.checkMethod&&!d.checkMethod({row:t});break;case"radio":c=b(e.isCheckedByRadioRow(t)),r._radioLabel=u.labelField?a().get(t,u.labelField):"",r._radioDisabled=u.checkMethod&&!u.checkMethod({row:t});break;default:if(o.original)c=ze(t,n);else if(c=e.getCellLabel(t,n),"html"===n.type)Kn.innerHTML=c,c=Kn.innerText.trim();else{const o=e.getCell(t,n);o&&(c=o.innerText.trim())}}r[n.id]=a().toValueString(c)})),r}))},C=e=>{const{columns:t,dataFilterMethod:o}=e;let n=e.data;return o&&(n=n.filter(((e,t)=>o({row:e,$rowIndex:t})))),w(e,t,n)},y=(t,o,n)=>{const l=f.value,r=n.editRender||n.cellRender;let i=n.footerExportMethod;if(!i&&r&&r.name){const e=No.renderer.get(r.name);e&&(i=e.footerExportMethod)}i||(i=l.footerExportMethod);const s=e.getVTColumnIndex(n),c=i?i({$table:e,items:o,itemIndex:s,row:o,_columnIndex:s,column:n,options:t}):a().toValueString(o[s]);return c},T=(e,t,n)=>{let l=Zn;if(e.isHeader&&(l+=t.map((t=>al(x(e,t)))).join(",")+Jn),n.forEach((e=>{l+=t.map((t=>al(rl(t,e[t.id])))).join(",")+Jn})),e.isFooter){const{footerTableData:n}=o,r=ll(e,n);r.forEach((o=>{l+=t.map((t=>al(y(e,o,t)))).join(",")+Jn}))}return l},E=(e,t,n)=>{let l="";if(e.isHeader&&(l+=t.map((t=>al(x(e,t)))).join("\t")+Jn),n.forEach((e=>{l+=t.map((t=>al(e[t.id]))).join("\t")+Jn})),e.isFooter){const{footerTableData:n}=o,r=ll(e,n);r.forEach((o=>{l+=t.map((t=>al(y(e,o,t)))).join(",")+Jn}))}return l},S=(e,t,n)=>{const l=e[t],r=a().isUndefined(l)||a().isNull(l)?n:l,i="ellipsis"===r,s="title"===r,c=!0===r||"tooltip"===r;let u=s||c||i;const{scrollXLoad:d,scrollYLoad:p}=o;return!d&&!p||u||(u=!0),u},k=(n,r,i)=>{const{id:s,border:c,treeConfig:u,headerAlign:d,align:p,footerAlign:m,showOverflow:f,showHeaderOverflow:h}=t,{isAllSelected:g,isIndeterminate:v,mergeList:b}=o,w=l.value,{print:C,isHeader:T,isFooter:E,isColgroup:k,isMerge:R,colgroups:O,original:M}=n,$="check-all",I=["vxe-table",`border--${ol(c)}`,C?"is--print":"",T?"is--header":""].filter((e=>e)),D=[`<table class="${I.join(" ")}" border="0" cellspacing="0" cellpadding="0">`,`<colgroup>${r.map((e=>`<col style="width:${e.renderWidth}px">`)).join("")}</colgroup>`];if(T&&(D.push("<thead>"),k&&!M?O.forEach((e=>{D.push(`<tr>${e.map((e=>{const t=e.headerAlign||e.align||d||p,o=S(e,"showHeaderOverflow",h)?["col--ellipsis"]:[],l=x(n,e);let r=0,i=0;a().eachTree([e],(t=>{t.childNodes&&e.childNodes.length||i++,r+=t.renderWidth}),{children:"childNodes"});const s=r-i;return t&&o.push(`col--${t}`),"checkbox"===e.type?`<th class="${o.join(" ")}" colspan="${e._colSpan}" rowspan="${e._rowSpan}"><div ${C?"":`style="width: ${s}px"`}><input type="checkbox" class="${$}" ${g?"checked":""}><span>${l}</span></div></th>`:`<th class="${o.join(" ")}" colspan="${e._colSpan}" rowspan="${e._rowSpan}" title="${l}"><div ${C?"":`style="width: ${s}px"`}><span>${oe(l,!0)}</span></div></th>`})).join("")}</tr>`)})):D.push(`<tr>${r.map((e=>{const t=e.headerAlign||e.align||d||p,o=S(e,"showHeaderOverflow",h)?["col--ellipsis"]:[],l=x(n,e);return t&&o.push(`col--${t}`),"checkbox"===e.type?`<th class="${o.join(" ")}"><div ${C?"":`style="width: ${e.renderWidth}px"`}><input type="checkbox" class="${$}" ${g?"checked":""}><span>${l}</span></div></th>`:`<th class="${o.join(" ")}" title="${l}"><div ${C?"":`style="width: ${e.renderWidth}px"`}><span>${oe(l,!0)}</span></div></th>`})).join("")}</tr>`),D.push("</thead>")),i.length&&(D.push("<tbody>"),u?i.forEach((e=>{D.push("<tr>"+r.map((t=>{const o=t.align||p,n=S(t,"showOverflow",f)?["col--ellipsis"]:[],l=e[t.id];if(o&&n.push(`col--${o}`),t.treeNode){let o="";return e._hasChild&&(o=`<i class="${e._expand?"vxe-table--tree-fold-icon":"vxe-table--tree-unfold-icon"}"></i>`),n.push("vxe-table--tree-node"),"radio"===t.type?`<td class="${n.join(" ")}" title="${l}"><div ${C?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${e._level*w.indent}px"><div class="vxe-table--tree-icon-wrapper">${o}</div><div class="vxe-table--tree-cell"><input type="radio" name="radio_${s}" ${e._radioDisabled?"disabled ":""}${nl(l)?"checked":""}><span>${e._radioLabel}</span></div></div></div></td>`:"checkbox"===t.type?`<td class="${n.join(" ")}" title="${l}"><div ${C?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${e._level*w.indent}px"><div class="vxe-table--tree-icon-wrapper">${o}</div><div class="vxe-table--tree-cell"><input type="checkbox" ${e._checkboxDisabled?"disabled ":""}${nl(l)?"checked":""}><span>${e._checkboxLabel}</span></div></div></div></td>`:`<td class="${n.join(" ")}" title="${l}"><div ${C?"":`style="width: ${t.renderWidth}px"`}><div class="vxe-table--tree-node-wrapper" style="padding-left: ${e._level*w.indent}px"><div class="vxe-table--tree-icon-wrapper">${o}</div><div class="vxe-table--tree-cell">${l}</div></div></div></td>`}return"radio"===t.type?`<td class="${n.join(" ")}"><div ${C?"":`style="width: ${t.renderWidth}px"`}><input type="radio" name="radio_${s}" ${e._radioDisabled?"disabled ":""}${nl(l)?"checked":""}><span>${e._radioLabel}</span></div></td>`:"checkbox"===t.type?`<td class="${n.join(" ")}"><div ${C?"":`style="width: ${t.renderWidth}px"`}><input type="checkbox" ${e._checkboxDisabled?"disabled ":""}${nl(l)?"checked":""}><span>${e._checkboxLabel}</span></div></td>`:`<td class="${n.join(" ")}" title="${l}"><div ${C?"":`style="width: ${t.renderWidth}px"`}>${oe(l,!0)}</div></td>`})).join("")+"</tr>")})):i.forEach((t=>{D.push("<tr>"+r.map((o=>{const n=o.align||p,l=S(o,"showOverflow",f)?["col--ellipsis"]:[],r=t[o.id];let a=1,i=1;if(R&&b.length){const n=e.getVTRowIndex(t._row),l=e.getVTColumnIndex(o),r=Je(b,n,l);if(r){const{rowspan:e,colspan:t}=r;if(!e||!t)return"";e>1&&(a=e),t>1&&(i=t)}}return n&&l.push(`col--${n}`),"radio"===o.type?`<td class="${l.join(" ")}" rowspan="${a}" colspan="${i}"><div ${C?"":`style="width: ${o.renderWidth}px"`}><input type="radio" name="radio_${s}" ${t._radioDisabled?"disabled ":""}${nl(r)?"checked":""}><span>${t._radioLabel}</span></div></td>`:"checkbox"===o.type?`<td class="${l.join(" ")}" rowspan="${a}" colspan="${i}"><div ${C?"":`style="width: ${o.renderWidth}px"`}><input type="checkbox" ${t._checkboxDisabled?"disabled ":""}${nl(r)?"checked":""}><span>${t._checkboxLabel}</span></div></td>`:`<td class="${l.join(" ")}" rowspan="${a}" colspan="${i}" title="${r}"><div ${C?"":`style="width: ${o.renderWidth}px"`}>${oe(r,!0)}</div></td>`})).join("")+"</tr>")})),D.push("</tbody>")),E){const{footerTableData:e}=o,t=ll(n,e);t.length&&(D.push("<tfoot>"),t.forEach((e=>{D.push(`<tr>${r.map((t=>{const o=t.footerAlign||t.align||m||p,l=S(t,"showOverflow",f)?["col--ellipsis"]:[],r=y(n,e,t);return o&&l.push(`col--${o}`),`<td class="${l.join(" ")}" title="${r}"><div ${C?"":`style="width: ${t.renderWidth}px"`}>${oe(r,!0)}</div></td>`})).join("")}</tr>`)})),D.push("</tfoot>"))}const F=!g&&v?`<script>(function(){var a=document.querySelector(".${$}");if(a){a.indeterminate=true}})()<\/script>`:"";return D.push("</table>",F),C?D.join(""):zn(n,D.join(""))},R=(e,t,n)=>{let l=['<?xml version="1.0"?>','<?mso-application progid="Excel.Sheet"?>','<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">','<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">',"<Version>16.00</Version>","</DocumentProperties>",'<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">',"<WindowHeight>7920</WindowHeight>","<WindowWidth>21570</WindowWidth>","<WindowTopX>32767</WindowTopX>","<WindowTopY>32767</WindowTopY>","<ProtectStructure>False</ProtectStructure>","<ProtectWindows>False</ProtectWindows>","</ExcelWorkbook>",`<Worksheet ss:Name="${e.sheetName}">`,"<Table>",t.map((e=>`<Column ss:Width="${e.renderWidth}"/>`)).join("")].join("");if(e.isHeader&&(l+=`<Row>${t.map((t=>`<Cell><Data ss:Type="String">${x(e,t)}</Data></Cell>`)).join("")}</Row>`),n.forEach((e=>{l+="<Row>"+t.map((t=>`<Cell><Data ss:Type="String">${e[t.id]}</Data></Cell>`)).join("")+"</Row>"})),e.isFooter){const{footerTableData:n}=o,r=ll(e,n);r.forEach((o=>{l+=`<Row>${t.map((t=>`<Cell><Data ss:Type="String">${y(e,o,t)}</Data></Cell>`)).join("")}</Row>`}))}return`${l}</Table></Worksheet></Workbook>`},O=(e,t,o)=>{if(t.length)switch(e.type){case"csv":return T(e,t,o);case"txt":return E(e,t,o);case"html":return k(e,t,o);case"xml":return R(e,t,o)}return""},M=(e,t)=>{const{filename:o,type:n,download:l}=e;if(!l){const o=Hn(t,e);return Promise.resolve({type:n,content:t,blob:o})}Yn({filename:o,type:n,content:t}).then((()=>{!1!==e.message&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:c.i18n("vxe.table.expSuccess"),status:"success"}))}))},$=t=>{const{remote:o,columns:n,colgroups:l,exportMethod:r,afterExportMethod:a}=t;return new Promise((a=>{if(o){const o={options:t,$table:e,$grid:h};a(r?r(o):o)}else{const o=C(t);a(e.preventEvent(null,"event.export",{options:t,columns:n,colgroups:l,datas:o},(()=>M(t,O(t,n,o)))))}})).then((o=>(gl(n),t.print||a&&a({status:!0,options:t,$table:e,$grid:h}),Object.assign({status:!0},o)))).catch((()=>{gl(n),t.print||a&&a({status:!1,options:t,$table:e,$grid:h});const o={status:!1};return Promise.reject(o)}))},I=(t,o)=>{const{tableFullColumn:l,_importResolve:r,_importReject:a}=n;let i={fields:[],rows:[]};switch(o.type){case"csv":i=pl(l,t);break;case"txt":i=ml(l,t);break;case"html":i=fl(l,t);break;case"xml":i=hl(l,t);break}const{fields:s,rows:u}=i,d=vl(l,s);d?e.createData(u).then((t=>{let n;return n="insert"===o.mode?e.insert(t):e.reloadData(t),!1!==o.message&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:c.i18n("vxe.table.impSuccess",[u.length]),status:"success"})),n.then((()=>{r&&r({status:!0})}))})):!1!==o.message&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:c.i18n("vxe.error.impFields"),status:"error"}),a&&a({status:!1}))},D=(t,o)=>{const{importMethod:l,afterImportMethod:r}=o,{type:i,filename:s}=Z(t);if(!l&&!a().includes(No.globalConfs.importTypes,i)){!1!==o.message&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:c.i18n("vxe.error.notType",[i]),status:"error"}));const e={status:!1};return Promise.reject(e)}const u=new Promise(((r,a)=>{const c=e=>{r(e),n._importResolve=null,n._importReject=null},u=e=>{a(e),n._importResolve=null,n._importReject=null};if(n._importResolve=c,n._importReject=u,window.FileReader){const r=Object.assign({mode:"insert"},o,{type:i,filename:s});if(r.remote)l?Promise.resolve(l({file:t,options:r,$table:e})).then((()=>{c({status:!0})})).catch((()=>{c({status:!0})})):c({status:!0});else{const{tableFullColumn:o}=n;e.preventEvent(null,"event.import",{file:t,options:r,columns:o},(()=>{const e=new FileReader;e.onerror=()=>{B("vxe.error.notType",[i]),u({status:!1})},e.onload=e=>{I(e.target.result,r)},e.readAsText(t,r.encoding||"UTF-8")}))}}else B("vxe.error.notExp"),c({status:!0})}));return u.then((()=>{r&&r({status:!0,options:o,$table:e})})).catch((t=>(r&&r({status:!1,options:o,$table:e}),Promise.reject(t))))},F=(l,r)=>{const{treeConfig:i,showHeader:s,showFooter:c}=t,{initStore:d,mergeList:p,isGroup:m,footerTableData:f,exportStore:h,exportParams:g}=o,{collectColumn:v}=n,x=i,b=u.value,w=e.getCheckboxRecords(),C=!!f.length,y=!x&&p.length,T=Object.assign({message:!0,isHeader:s,isFooter:c},l),E=T.types||No.globalConfs.exportTypes,S=T.modes,k=b.checkMethod,R=v.slice(0),{columns:O}=T,M=E.map((e=>({value:e,label:`vxe.export.types.${e}`}))),$=S.map((e=>({value:e,label:`vxe.export.modes.${e}`})));return a().eachTree(R,((e,t,o,n,l)=>{const r=e.children&&e.children.length;(r||Qn(e))&&(e.checked=O?O.some((t=>{if(Ue(t))return e===t;if(a().isString(t))return e.field===t;{const o=t.id||t.colId,n=t.type,l=t.property||t.field;if(o)return e.id===o;if(l&&n)return e.property===l&&e.type===n;if(l)return e.property===l;if(n)return e.type===n}return!1})):e.visible,e.halfChecked=!1,e.disabled=l&&l.disabled||!!k&&!k({column:e}))})),Object.assign(h,{columns:R,typeList:M,modeList:$,hasFooter:C,hasMerge:y,hasTree:x,isPrint:r,hasColgroup:m,visible:!0}),Object.assign(g,{mode:w.length?"selected":"current"},T),-1===S.indexOf(g.mode)&&(g.mode=S[0]),-1===E.indexOf(g.type)&&(g.type=E[0]),d.export=!0,(0,H.nextTick)()},N={exportData(r){const{treeConfig:s}=t,{isGroup:u,tableGroupColumn:d}=o,{tableFullColumn:p,afterFullData:m}=n,f=i.value,g=l.value,v=Object.assign({isHeader:!0,isFooter:!0,isColgroup:!0,download:!0,type:"csv",mode:"current"},f,{print:!1},r),{type:x,mode:b,columns:w,original:C,beforeExportMethod:y}=v;let T=[];const E=w&&w.length?w:null;let S=v.columnFilterMethod;E||S||(S=C?({column:e})=>e.property:({column:e})=>Qn(e)),E?(v._isCustomColumn=!0,T=a().searchTree(a().mapTree(E,(t=>{let o;if(t){if(Ue(t))o=t;else if(a().isString(t))o=e.getColumnByField(t);else{const n=t.id||t.colId,l=t.type,r=t.property||t.field;n?o=e.getColumnById(n):r&&l?o=p.find((e=>e.property===r&&e.type===l)):r?o=e.getColumnByField(r):l&&(o=p.find((e=>e.type===l)))}return o||{}}}),{children:"childNodes",mapChildren:"_children"}),((e,t)=>Ue(e)&&(!S||S({column:e,$columnIndex:t}))),{children:"_children",mapChildren:"childNodes",original:!0})):T=a().searchTree(u?d:p,((e,t)=>e.visible&&(!S||S({column:e,$columnIndex:t}))),{children:"children",mapChildren:"childNodes",original:!0});const k=[];if(a().eachTree(T,(e=>{const t=e.children&&e.children.length;t||k.push(e)}),{children:"childNodes"}),v.columns=k,v.colgroups=tl(T),v.filename||(v.filename=c.i18n(v.original?"vxe.table.expOriginFilename":"vxe.table.expFilename",[a().toDateString(Date.now(),"yyyyMMddHHmmss")])),v.sheetName||(v.sheetName=document.title),!v.exportMethod&&!a().includes(No.globalConfs.exportTypes,x)){B("vxe.error.notType",[x]);const e={status:!1};return Promise.reject(e)}if(v.print||y&&y({options:v,$table:e,$grid:h}),!v.data)if(v.data=m,"selected"===b){const t=e.getCheckboxRecords();["html","pdf"].indexOf(x)>-1&&s?v.data=a().searchTree(e.getTableData().fullData,(o=>e.findRowIndexOf(t,o)>-1),Object.assign({},g,{data:"_row"})):v.data=t}else if("all"===b&&(h||V("vxe.error.errProp",["all","mode=current,selected"]),h&&!v.remote)){const{reactData:t}=h,{computeProxyOpts:o}=h.getComputeMaps(),n=o.value,{beforeQueryAll:l,afterQueryAll:r,ajax:i={},props:s={}}=n,c=i.queryAll;if(c||V("vxe.error.notFunc",["proxy-config.ajax.queryAll"]),c){const o={$table:e,$grid:h,sort:t.sortData,filters:t.filterData,form:t.formData,target:c,options:v};return Promise.resolve((l||c)(o)).catch((e=>e)).then((e=>(v.data=(s.list?a().get(e,s.list):e)||[],r&&r(o),$(v))))}}return $(v)},importByFile(t,o){const n=Object.assign({},o),{beforeImportMethod:l}=n;return l&&l({options:n,$table:e}),D(t,n)},importData(t){const o=s.value,n=Object.assign({types:No.globalConfs.importTypes},o,t),{beforeImportMethod:l,afterImportMethod:r}=n;return l&&l({options:n,$table:e}),Wn(n).catch((t=>(r&&r({status:!1,options:n,$table:e}),Promise.reject(t)))).then((e=>{const{file:t}=e;return D(t,n)}))},saveFile(e){return Yn(e)},readFile(e){return Wn(e)},print(t){const o=r.value,n=Object.assign({original:!1},o,t,{type:"html",download:!1,remote:!1,print:!0});return n.sheetName||(n.sheetName=document.title),new Promise((t=>{n.content?t(Xn(e,n,n.content)):t(N.exportData(n).then((({content:t})=>Xn(e,n,t))))}))},openImport(e){const{treeConfig:n,importConfig:l}=t,{initStore:r,importStore:a,importParams:i}=o,u=s.value,d=Object.assign({mode:"insert",message:!0,types:No.globalConfs.importTypes},e,u),{types:p}=d,m=!!n;if(m)return void(d.message&&No.modal.message({content:c.i18n("vxe.error.treeNotImp"),status:"error"}));l||B("vxe.error.reqProp",["import-config"]);const f=p.map((e=>({value:e,label:`vxe.export.types.${e}`}))),h=d.modes.map((e=>({value:e,label:`vxe.import.modes.${e}`})));Object.assign(a,{file:null,type:"",filename:"",modeList:h,typeList:f,visible:!0}),Object.assign(i,d),r.import=!0},openExport(e){const o=i.value;t.exportConfig||B("vxe.error.reqProp",["export-config"]),F(Object.assign({},o,e))},openPrint(e){const o=r.value;t.printConfig||B("vxe.error.reqProp",["print-config"]),F(Object.assign({},o,e),!0)}};return N},setupGrid(e){return e.extendTableMethods(xl)}};var wl=bl;const Cl=e=>{const t=Object.assign({},e,{type:"html"});Xn(null,t,t.content)},yl={ExportPanel:Dn,ImportPanel:Ln,install(e){No.saveFile=Yn,No.readFile=Wn,No.print=Cl,No.setConfig({export:{types:{csv:0,html:0,xml:0,txt:0}}}),No.hooks.add("$tableExport",wl),e.component(Dn.name,Dn),e.component(Ln.name,Ln)}},Tl=yl;zo.component(Dn.name,Dn),zo.component(Ln.name,Ln);function El(e,t){let o=0,n=0;const l=!ae.firefox&&pe(e,"vxe-checkbox--label");if(l){const t=getComputedStyle(e);o-=a().toNumber(t.paddingTop),n-=a().toNumber(t.paddingLeft)}while(e&&e!==t)if(o+=e.offsetTop,n+=e.offsetLeft,e=e.offsetParent,l){const t=getComputedStyle(e);o-=a().toNumber(t.paddingTop),n-=a().toNumber(t.paddingLeft)}return{offsetTop:o,offsetLeft:n}}const Sl={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refElem:l}=e.getRefMaps(),{computeEditOpts:r,computeCheckboxOpts:i,computeMouseOpts:s,computeTreeOpts:c}=e.getComputeMaps();function u(t,l,r){let a=0,i=[];const s=r>0,c=r>0?r:Math.abs(r)+l.offsetHeight,{scrollYLoad:u}=o,{afterFullData:d,scrollYStore:p}=n;if(u){const o=e.getVTRowIndex(t.row);i=s?d.slice(o,o+Math.ceil(c/p.rowHeight)):d.slice(o-Math.floor(c/p.rowHeight)+1,o+1)}else{const t=s?"next":"previous";while(l&&a<c){const o=e.getRowNode(l);o&&(i.push(o.item),a+=l.offsetHeight,l=l[`${t}ElementSibling`])}}return i}const d=(t,o)=>{const{column:r,cell:a}=o;if("checkbox"===r.type){const i=l.value,{elemStore:s}=n,c=t.clientX,d=t.clientY,p=s[`${r.fixed||"main"}-body-wrapper`]||s["main-body-wrapper"],m=p?p.value:null;if(!m)return;const f=m.querySelector(".vxe-table--checkbox-range"),h=document.onmousemove,g=document.onmouseup,v=a.parentNode,x=e.getCheckboxRecords();let b=[];const w=1,C=El(t.target,m),y=C.offsetTop+t.offsetY,T=C.offsetLeft+t.offsetX,E=m.scrollTop,S=v.offsetHeight;let k=null,R=!1,O=1;const M=(t,o)=>{e.dispatchEvent(`checkbox-range-${t}`,{records:e.getCheckboxRecords(),reserves:e.getCheckboxReserveRecords()},o)},$=t=>{const{clientX:n,clientY:l}=t,r=n-c,a=l-d+(m.scrollTop-E);let i=Math.abs(a),s=Math.abs(r),p=y,h=T;a<w?(p+=a,p<w&&(p=w,i=y)):i=Math.min(i,m.scrollHeight-y-w),r<w?(h+=r,s>T&&(h=w,s=T)):s=Math.min(s,m.clientWidth-T-w),f.style.height=`${i}px`,f.style.width=`${s}px`,f.style.left=`${h}px`,f.style.top=`${p}px`,f.style.display="block";const g=u(o,v,a<w?-i:i);i>10&&g.length!==b.length&&(b=g,t.ctrlKey?g.forEach((t=>{e.handleSelectRow({row:t},-1===x.indexOf(t))})):(e.setAllCheckboxRow(!1),e.handleCheckedCheckboxRow(g,!0,!1)),M("change",t))},I=()=>{clearTimeout(k),k=null},D=t=>{I(),k=setTimeout((()=>{if(k){const{scrollLeft:o,scrollTop:n,clientHeight:l,scrollHeight:r}=m,a=Math.ceil(50*O/S);R?n+l<r?(e.scrollTo(o,n+a),D(t),$(t)):I():n?(e.scrollTo(o,n-a),D(t),$(t)):I()}}),50)};fe(i,"drag--range"),document.onmousemove=e=>{e.preventDefault(),e.stopPropagation();const{clientY:t}=e,{boundingTop:o}=Te(m);t<o?(R=!1,O=o-t,k||D(e)):t>o+m.clientHeight?(R=!0,O=t-o-m.clientHeight,k||D(e)):k&&I(),$(e)},document.onmouseup=e=>{I(),me(i,"drag--range"),f.removeAttribute("style"),document.onmousemove=h,document.onmouseup=g,M("end",e)},M("start",t)}},p=(o,n)=>{const{editConfig:l,checkboxConfig:a,mouseConfig:c}=t,u=i.value,p=s.value,m=r.value;if(c&&p.area&&e.handleCellAreaEvent)return e.handleCellAreaEvent(o,n);a&&u.range&&d(o,n),c&&p.selected&&(l&&"cell"!==m.mode||e.handleSelected(n,o))},m={moveTabSelected(o,l,a){const{editConfig:i}=t,{afterFullData:s,visibleColumn:c}=n,u=r.value;let d,p,m;const f=Object.assign({},o),h=e.getVTRowIndex(f.row),g=e.getVTColumnIndex(f.column);a.preventDefault(),l?g<=0?h>0&&(p=h-1,d=s[p],m=c.length-1):m=g-1:g>=c.length-1?h<s.length-1&&(p=h+1,d=s[p],m=0):m=g+1;const v=c[m];v&&(d?(f.rowIndex=p,f.row=d):f.rowIndex=h,f.columnIndex=m,f.column=v,f.cell=e.getCell(f.row,f.column),i?"click"!==u.trigger&&"dblclick"!==u.trigger||("row"===u.mode?e.handleActived(f,a):e.scrollToRow(f.row,f.column).then((()=>e.handleSelected(f,a)))):e.scrollToRow(f.row,f.column).then((()=>e.handleSelected(f,a))))},moveCurrentRow(l,r,i){const{treeConfig:s}=t,{currentRow:u}=o,{afterFullData:d}=n,p=c.value,m=p.children||p.childrenField;let f;if(i.preventDefault(),u)if(s){const{index:e,items:t}=a().findTree(d,(e=>e===u),{children:m});l&&e>0?f=t[e-1]:r&&e<t.length-1&&(f=t[e+1])}else{const t=e.getVTRowIndex(u);l&&t>0?f=d[t-1]:r&&t<d.length-1&&(f=d[t+1])}else f=d[0];if(f){const t={$table:e,row:f,rowIndex:e.getRowIndex(f),$rowIndex:e.getVMRowIndex(f)};e.scrollToRow(f).then((()=>e.triggerCurrentRowEvent(i,t)))}},moveSelected(t,o,l,r,a,i){const{afterFullData:s,visibleColumn:c}=n,u=Object.assign({},t),d=e.getVTRowIndex(u.row),p=e.getVTColumnIndex(u.column);i.preventDefault(),l&&d>0?(u.rowIndex=d-1,u.row=s[u.rowIndex]):a&&d<s.length-1?(u.rowIndex=d+1,u.row=s[u.rowIndex]):o&&p?(u.columnIndex=p-1,u.column=c[u.columnIndex]):r&&p<c.length-1&&(u.columnIndex=p+1,u.column=c[u.columnIndex]),e.scrollToRow(u.row,u.column).then((()=>{u.cell=e.getCell(u.row,u.column),e.handleSelected(u,i)}))},triggerHeaderCellMousedownEvent(o,n){const{mouseConfig:l}=t,r=s.value;if(l&&r.area&&e.handleHeaderCellAreaEvent){const t=o.currentTarget,l=Ce(o,t,"vxe-cell--sort").flag,r=Ce(o,t,"vxe-cell--filter").flag;e.handleHeaderCellAreaEvent(o,Object.assign({cell:t,triggerSort:l,triggerFilter:r},n))}e.focus(),e.closeMenu&&e.closeMenu()},triggerCellMousedownEvent(t,o){const n=t.currentTarget;o.cell=n,p(t,o),e.focus(),e.closeFilter(),e.closeMenu&&e.closeMenu()}};return m}};var kl=Sl;const Rl={install(){No.hooks.add("$tableKeyboard",kl)}},Ol=Rl;class Ml{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.max,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return te(this.$options.content||this.$options.message)}get message(){return this.content}}const $l=["fullValidate","validate","clearValidate"],Il={setupTable(e){const{props:t,reactData:o,internalData:n}=e,{refValidTooltip:l}=e.getRefMaps(),{computeValidOpts:r,computeTreeOpts:i,computeEditOpts:s}=e.getComputeMaps();let u,d={},p={};const m=t=>new Promise((o=>{const n=r.value;!1===n.autoPos?(e.dispatchEvent("valid-error",t,null),o()):e.handleActived(t,{type:"valid-error",trigger:"call"}).then((()=>{o(p.showValidTooltip(t))}))})),f=e=>{const t=r.value;if("single"===t.msgMode){const t=Object.keys(e),o=e;if(t.length){const n=t[0];o[n]=e[n]}return o}return e},h=(l,s,h)=>{const g={},{editRules:v,treeConfig:x}=t,{afterFullData:b,visibleColumn:w}=n,C=i.value,y=C.children||C.childrenField,T=r.value;let E;!0===l?E=b:l&&(a().isFunction(l)?s=l:E=a().isArray(l)?l:[l]),E||(E=e.getInsertRecords?e.getInsertRecords().concat(e.getUpdateRecords()):[]);const S=[];n._lastCallTime=Date.now(),u=!1,d.clearValidate();const k={};if(v){const t=e.getColumns(),n=o=>{if(h||!u){const n=[];t.forEach((t=>{!h&&u||!a().has(v,t.property)||n.push(p.validCellRules("all",o,t).catch((({rule:n,rules:l})=>{const r={rule:n,rules:l,rowIndex:e.getRowIndex(o),row:o,columnIndex:e.getColumnIndex(t),column:t,field:t.property,$table:e};if(g[t.property]||(g[t.property]=[]),k[`${Le(e,o)}:${t.id}`]={column:t,row:o,rule:n,content:n.content},g[t.property].push(r),!h)return u=!0,Promise.reject(r)})))})),S.push(Promise.all(n))}};return x?a().eachTree(E,n,{children:y}):E.forEach(n),Promise.all(S).then((()=>{const e=Object.keys(g);return o.validErrorMaps=f(k),(0,H.nextTick)().then((()=>{if(e.length)return Promise.reject(g[e[0]][0]);s&&s()}))})).catch((t=>new Promise(((o,n)=>{const l=()=>{(0,H.nextTick)((()=>{s?(s(g),o()):"obsolete"===c.validToReject?n(g):o(g)}))},r=()=>{t.cell=e.getCell(t.row,t.column),ke(t.cell),m(t).then(l)};if(!1===T.autoPos)l();else{const o=t.row,n=t.column,l=b.indexOf(o),a=w.indexOf(n),i=l>0?b[l-1]:o,s=a>0?w[a-1]:n;e.scrollToRow(i,s).then(r)}}))))}return o.validErrorMaps={},(0,H.nextTick)().then((()=>{s&&s()}))};d={fullValidate(e,t){return a().isFunction(t)&&V("vxe.error.notValidators",["fullValidate(rows, callback)","fullValidate(rows)"]),h(e,t,!0)},validate(e,t){return a().isFunction(t)&&V("vxe.error.notValidators",["validate(rows, callback)","validate(rows)"]),h(e,t)},clearValidate(t,n){const{validErrorMaps:i}=o,s=l.value,c=r.value,u=a().isArray(t)?t:t?[t]:[],d=a().isArray(n)?n:(n?[n]:[]).map((t=>Ae(e,t)));let p={};if(s&&s.reactData.visible&&s.close(),"single"===c.msgMode)return o.validErrorMaps={},(0,H.nextTick)();if(u.length&&d.length)p=Object.assign({},i),u.forEach((t=>{d.forEach((o=>{const n=`${Le(e,t)}:${o.id}`;p[n]&&delete p[n]}))}));else if(u.length){const t=u.map((t=>`${Le(e,t)}`));a().each(i,((e,o)=>{t.indexOf(o.split(":")[0])>-1&&(p[o]=e)}))}else if(d.length){const e=d.map((e=>`${e.id}`));a().each(i,((t,o)=>{e.indexOf(o.split(":")[1])>-1&&(p[o]=t)}))}return o.validErrorMaps=p,(0,H.nextTick)()}};const g=(e,t)=>{const{type:o,min:n,max:l,pattern:r}=e,i="number"===o,s=i?a().toNumber(t):a().getSize(t);return!(!i||!isNaN(t))||(!a().eqNull(n)&&s<a().toNumber(n)||(!a().eqNull(l)&&s>a().toNumber(l)||!(!r||(a().isRegExp(r)?r:new RegExp(r)).test(t))))};return p={validCellRules(o,n,l,r){const{editRules:i}=t,{field:s}=l,c=[],d=[];if(s&&i){const t=a().get(i,s);if(t){const i=a().isUndefined(r)?a().get(n,s):r;t.forEach((r=>{const{type:s,trigger:p,required:m,validator:f}=r;if("all"===o||!p||o===p)if(f){const o={cellValue:i,rule:r,rules:t,row:n,rowIndex:e.getRowIndex(n),column:l,columnIndex:e.getColumnIndex(l),field:l.field,$table:e,$grid:e.xegrid};let s;if(a().isString(f)){const e=No.validators.get(f);e?e.cellValidatorMethod?s=e.cellValidatorMethod(o):V("vxe.error.notValidators",[f]):B("vxe.error.notValidators",[f])}else s=f(o);s&&(a().isError(s)?(u=!0,c.push(new Ml({type:"custom",trigger:p,content:s.message,rule:new Ml(r)}))):s.catch&&d.push(s.catch((e=>{u=!0,c.push(new Ml({type:"custom",trigger:p,content:e&&e.message?e.message:r.content||r.message,rule:new Ml(r)}))}))))}else{const e="array"===s,t=a().isArray(i);let o=!0;o=e||t?!t||!i.length:a().isString(i)?ne(i.trim()):ne(i),(m?o||g(r,i):!o&&g(r,i))&&(u=!0,c.push(new Ml(r)))}}))}}return Promise.all(d).then((()=>{if(c.length){const e={rules:c,rule:c[0]};return Promise.reject(e)}}))},hasCellRules(e,o,n){const{editRules:l}=t,{field:r}=n;if(r&&l){const t=a().get(l,r);return t&&!!a().find(t,(t=>"all"===e||!t.trigger||e===t.trigger))}return!1},triggerValidate(e){const{editConfig:n,editRules:l}=t,{editStore:a}=o,{actived:i}=a,c=s.value,u=r.value;if(l&&"single"===u.msgMode&&(o.validErrorMaps={}),n&&l&&i.row){const{row:t,column:o,cell:n}=i.args;if(p.hasCellRules(e,t,o))return p.validCellRules(e,t,o).then((()=>{"row"===c.mode&&d.clearValidate(t,o)})).catch((({rule:l})=>{if(!l.trigger||e===l.trigger){const e={rule:l,row:t,column:o,cell:n};return p.showValidTooltip(e),Promise.reject(e)}return Promise.resolve()}))}return Promise.resolve()},showValidTooltip(n){const{height:a}=t,{tableData:i,validStore:s,validErrorMaps:c}=o,{rule:u,row:d,column:p,cell:m}=n,f=r.value,h=l.value,g=u.content;return s.visible=!0,"single"===f.msgMode?o.validErrorMaps={[`${Le(e,d)}:${p.id}`]:{column:p,row:d,rule:u,content:g}}:o.validErrorMaps=Object.assign({},c,{[`${Le(e,d)}:${p.id}`]:{column:p,row:d,rule:u,content:g}}),e.dispatchEvent("valid-error",n,null),h&&h&&("tooltip"===f.message||"default"===f.message&&!a&&i.length<2)?h.open(m,g):(0,H.nextTick)()}},{...d,...p}},setupGrid(e){return e.extendTableMethods($l)}};var Dl=Il;const Fl={install(){No.hooks.add("$tableValidator",Dl)}},Nl=Fl;var Pl=(0,H.defineComponent)({name:"VxeTooltip",props:{modelValue:Boolean,size:{type:String,default:()=>c.tooltip.size||c.size},trigger:{type:String,default:()=>c.tooltip.trigger||"hover"},theme:{type:String,default:()=>c.tooltip.theme||"dark"},content:{type:[String,Number],default:null},useHTML:Boolean,zIndex:[String,Number],popupClassName:[String,Function],isArrow:{type:Boolean,default:!0},enterable:Boolean,enterDelay:{type:Number,default:()=>c.tooltip.enterDelay},leaveDelay:{type:Number,default:()=>c.tooltip.leaveDelay}},emits:["update:modelValue"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.reactive)({target:null,isUpdate:!1,visible:!1,tipContent:"",tipActive:!1,tipTarget:null,tipZindex:0,tipStore:{style:{},placement:"",arrowStyle:{}}}),s=(0,H.ref)(),c={refElem:s},u={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>c};let d={};const p=()=>{const{tipTarget:e,tipStore:t}=i;if(e){const{scrollTop:o,scrollLeft:n,visibleWidth:l}=he(),{top:r,left:a}=Te(e),i=s.value,c=6,u=i.offsetHeight,d=i.offsetWidth;let p=a,m=r-u-c;p=Math.max(c,a+Math.floor((e.offsetWidth-d)/2)),p+d+c>n+l&&(p=n+l-d-c),r-u<o+c&&(t.placement="bottom",m=r+e.offsetHeight+c),t.style.top=`${m}px`,t.style.left=`${p}px`,t.arrowStyle.left=a-p+e.offsetWidth/2+"px"}},m=e=>{e!==i.visible&&(i.visible=e,i.isUpdate=!0,n("update:modelValue",e))},f=()=>{i.tipZindex<Q()&&(i.tipZindex=J())},h=()=>{i.visible?d.close():d.open()},g=()=>{d.open()},v=()=>{const{trigger:t,enterable:o,leaveDelay:n}=e;i.tipActive=!1,o&&"hover"===t?setTimeout((()=>{i.tipActive||d.close()}),n):d.close()},x=()=>{i.tipActive=!0},b=()=>{const{trigger:t,enterable:o,leaveDelay:n}=e;i.tipActive=!1,o&&"hover"===t&&setTimeout((()=>{i.tipActive||d.close()}),n)},w=()=>{const{tipStore:t}=i,o=s.value;if(o){const e=o.parentNode;e||document.body.appendChild(o)}return m(!0),f(),t.placement="top",t.style={width:"auto",left:0,top:0,zIndex:e.zIndex||i.tipZindex},t.arrowStyle={left:"50%"},d.updatePlacement()},C=a().debounce((()=>{i.tipActive&&w()}),e.enterDelay,{leading:!1,trailing:!0});d={dispatchEvent(e,t,o){n(e,Object.assign({$tooltip:u,$event:o},t))},open(e,t){return d.toVisible(e||i.target,t)},close(){return i.tipTarget=null,i.tipActive=!1,Object.assign(i.tipStore,{style:{},placement:"",arrowStyle:null}),m(!1),(0,H.nextTick)()},toVisible(t,o){if(t){const{trigger:n,enterDelay:l}=e;if(i.tipActive=!0,i.tipTarget=t,o&&(i.tipContent=o),!l||"hover"!==n)return w();C()}return(0,H.nextTick)()},updatePlacement(){return(0,H.nextTick)().then((()=>{const{tipTarget:e}=i,t=s.value;if(e&&t)return p(),(0,H.nextTick)().then(p)}))},isActived(){return i.tipActive},setActived(e){i.tipActive=!!e}},Object.assign(u,d),(0,H.watch)((()=>e.content),(()=>{i.tipContent=e.content})),(0,H.watch)((()=>e.modelValue),(()=>{i.isUpdate||(e.modelValue?d.open():d.close()),i.isUpdate=!1})),(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{trigger:t,content:o,modelValue:n}=e,l=s.value;if(l){const e=l.parentNode;if(e){i.tipContent=o,i.tipZindex=J(),a().arrayEach(l.children,((t,o)=>{o>1&&(e.insertBefore(t,l),i.target||(i.target=t))})),e.removeChild(l);const{target:r}=i;r&&("hover"===t?(r.onmouseenter=g,r.onmouseleave=v):"click"===t&&(r.onclick=h)),n&&d.open()}}}))})),(0,H.onBeforeUnmount)((()=>{const{trigger:t}=e,{target:o}=i,n=s.value;if(o&&("hover"===t?(o.onmouseenter=null,o.onmouseleave=null):"click"===t&&(o.onclick=null)),n){const e=n.parentNode;e&&e.removeChild(n)}}));const y=()=>{const{useHTML:t}=e,{tipContent:n}=i,l=o.content;return l?(0,H.h)("div",{key:1,class:"vxe-table--tooltip-content"},lt(l({}))):t?(0,H.h)("div",{key:2,class:"vxe-table--tooltip-content",innerHTML:n}):(0,H.h)("div",{key:3,class:"vxe-table--tooltip-content"},oe(n))},T=()=>{const{popupClassName:t,theme:n,isArrow:l,enterable:c}=e,{tipActive:d,visible:p,tipStore:m}=i,f=o.default,h=r.value;let g;return c&&(g={onMouseenter:x,onMouseleave:b}),(0,H.h)("div",{ref:s,class:["vxe-table--tooltip-wrapper",`theme--${n}`,t?a().isFunction(t)?t({$tooltip:u}):t:"",{[`size--${h}`]:h,[`placement--${m.placement}`]:m.placement,"is--enterable":c,"is--visible":p,"is--arrow":l,"is--active":d}],style:m.style,...g},[y(),(0,H.h)("div",{class:"vxe-table--tooltip-arrow",style:m.arrowStyle}),...f?lt(f({})):[]])};return u.renderVN=T,u},render(){return this.renderVN()}}),Ll=(0,H.defineComponent)({name:"VxeTableCustomPanel",props:{customStore:{type:Object,default:()=>({})}},setup(e){const t=(0,H.inject)("$xetable",{}),{reactData:o}=t,{computeCustomOpts:n,computeColumnOpts:l,computeIsMaxFixedColumn:r}=t.getComputeMaps(),i=(0,H.ref)(),s=(0,H.ref)(),u=(0,H.ref)(),d=(0,H.ref)();let p;const m=o=>{const{customStore:n}=e;n.activeWrapper=!0,t.customOpenEvent(o)},f=o=>{const{customStore:n}=e;n.activeWrapper=!1,setTimeout((()=>{n.activeBtn||n.activeWrapper||t.customColseEvent(o)}),300)},h=e=>{R(),t.closeCustom(),t.emitCustomEvent("confirm",e)},g=e=>{t.closeCustom(),t.emitCustomEvent("cancel",e)},v=e=>{t.resetColumn(!0),t.closeCustom(),t.emitCustomEvent("reset",e)},x=e=>{No.modal?No.modal.confirm({content:c.i18n("vxe.custom.cstmConfirmRestore"),className:"vxe-table--ignore-clear",escClosable:!0}).then((t=>{"confirm"===t&&v(e)})):v(e)},b=e=>{const{customColumnList:t}=o,n=a().findTree(t,(t=>t===e));if(n&&n.parent){const{parent:e}=n;e.children&&e.children.length&&(e.visible=e.children.every((e=>e.visible)),e.halfVisible=!e.visible&&e.children.some((e=>e.visible||e.halfVisible)),b(e))}},w=e=>{const o=!e.visible,l=n.value;a().eachTree([e],(e=>{e.visible=o,e.halfVisible=!1})),b(e),l.immediate&&t.handleCustom(),t.checkCustomStatus()},C=(e,o)=>{const n=r.value;e.fixed===o?t.clearColumnFixed(e):n&&!e.fixed||t.setColumnFixed(e,o)},y=e=>{const o=r.value;o||t.setColumnFixed(e,e.fixed)},T=()=>{const{customStore:l}=e,{customColumnList:r}=o,i=n.value,{checkMethod:s}=i,c=!l.isAll;a().eachTree(r,(e=>{s&&!s({column:e})||(e.visible=c,e.halfVisible=!1)})),l.isAll=c,t.checkCustomStatus()},E=e=>{const o=e.currentTarget,n=o.parentNode,l=n.parentNode,r=l.getAttribute("colid"),a=t.getColumnById(r);l.draggable=!0,d.value=a,fe(l,"active--drag-origin")},S=e=>{const t=e.currentTarget,o=t.parentNode,n=o.parentNode,l=u.value;n.draggable=!1,d.value=null,me(n,"active--drag-origin"),l&&(l.style.display="")},k=e=>{const t=new Image;e.dataTransfer&&e.dataTransfer.setDragImage(t,0,0)},R=()=>{const{customColumnList:e}=o;e.forEach(((e,t)=>{const o=t+1;e.renderSortNumber=o}))},O=e=>{const{customColumnList:n}=o,l=e.currentTarget,r=u.value;if(p){if(p!==l){const e=p.getAttribute("drag-pos"),o=l.getAttribute("colid"),r=t.getColumnById(o);if(!r)return;const i=a().findIndexOf(n,(e=>e.id===r.id)),s=p.getAttribute("colid"),c=t.getColumnById(s);if(!c)return;n.splice(i,1);const u=a().findIndexOf(n,(e=>e.id===c.id));n.splice(u+("bottom"===e?1:0),0,r)}p.draggable=!1,p.removeAttribute("drag-pos"),me(p,"active--drag-target")}d.value=null,l.draggable=!1,l.removeAttribute("drag-pos"),r&&(r.style.display=""),me(l,"active--drag-target"),me(l,"active--drag-origin"),R()},M=e=>{const o=e.currentTarget;p!==o&&me(p,"active--drag-target");const n=o.getAttribute("colid"),l=t.getColumnById(n);if(l&&1===l.level){e.preventDefault();const t=e.clientY-o.getBoundingClientRect().y,n=t<o.clientHeight/2?"top":"bottom";fe(o,"active--drag-target"),o.setAttribute("drag-pos",n),p=o}$(e)},$=e=>{const t=u.value,o=s.value;if(o&&t){const n=o.parentNode,l=n.getBoundingClientRect();t.style.display="block",t.style.top=`${Math.min(n.clientHeight-n.scrollTop-t.clientHeight,e.clientY-l.y)}px`,t.style.left=`${Math.min(n.clientWidth-n.scrollLeft-t.clientWidth-16,e.clientX-l.x)}px`}},I=()=>{const{customStore:t}=e,{customColumnList:l}=o,s=n.value,{maxHeight:u}=t,{checkMethod:d,visibleMethod:p,trigger:g}=s,x=r.value,b=[],y={};"hover"===g&&(y.onMouseenter=m,y.onMouseleave=f),a().eachTree(l,((e,t,o,n,l)=>{const r=!p||p({column:e});if(r){const t=e.visible,o=e.halfVisible,n=e.children&&e.children.length,r=oe(e.getTitle(),1),a=!!d&&!d({column:e});b.push((0,H.h)("li",{key:e.id,class:["vxe-table-custom--option",`level--${e.level}`,{"is--group":n}]},[(0,H.h)("div",{title:r,class:["vxe-table-custom--checkbox-option",{"is--checked":t,"is--indeterminate":o,"is--disabled":a}],onClick:()=>{a||w(e)}},[(0,H.h)("span",{class:["vxe-checkbox--icon",o?c.icon.TABLE_CHECKBOX_INDETERMINATE:t?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,H.h)("span",{class:"vxe-checkbox--label"},r)]),!l&&s.allowFixed?(0,H.h)("div",{class:"vxe-table-custom--fixed-option"},[(0,H.h)("span",{class:["vxe-table-custom--fixed-left-option","left"===e.fixed?c.icon.TOOLBAR_TOOLS_FIXED_LEFT_ACTIVED:c.icon.TOOLBAR_TOOLS_FIXED_LEFT,{"is--checked":"left"===e.fixed,"is--disabled":x&&!e.fixed}],title:c.i18n("left"===e.fixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedLeft"),onClick:()=>{C(e,"left")}}),(0,H.h)("span",{class:["vxe-table-custom--fixed-right-option","right"===e.fixed?c.icon.TOOLBAR_TOOLS_FIXED_RIGHT_ACTIVED:c.icon.TOOLBAR_TOOLS_FIXED_RIGHT,{"is--checked":"right"===e.fixed,"is--disabled":x&&!e.fixed}],title:c.i18n("right"===e.fixed?"vxe.toolbar.cancelFixed":"vxe.toolbar.fixedRight"),onClick:()=>{C(e,"right")}})]):null]))}}));const E=t.isAll,S=t.isIndeterminate;return(0,H.h)("div",{ref:i,key:"simple",class:["vxe-table-custom-wrapper",{"is--active":t.visible}]},[(0,H.h)("ul",{class:"vxe-table-custom--header"},[(0,H.h)("li",{class:"vxe-table-custom--option"},[(0,H.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":E,"is--indeterminate":S}],title:c.i18n("vxe.table.allTitle"),onClick:T},[(0,H.h)("span",{class:["vxe-checkbox--icon",S?c.icon.TABLE_CHECKBOX_INDETERMINATE:E?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]}),(0,H.h)("span",{class:"vxe-checkbox--label"},c.i18n("vxe.toolbar.customAll"))])])]),(0,H.h)("ul",{class:"vxe-table-custom--body",style:u?{maxHeight:`${u}px`}:{},...y},b),s.showFooter?(0,H.h)("div",{class:"vxe-table-custom--footer"},[(0,H.h)("button",{class:"btn--reset",onClick:v},s.resetButtonText||c.i18n("vxe.toolbar.customRestore")),(0,H.h)("button",{class:"btn--confirm",onClick:h},s.confirmButtonText||c.i18n("vxe.toolbar.customConfirm"))]):null])},D=()=>{const{customStore:t}=e,{customColumnList:i}=o,p=n.value,{checkMethod:m,visibleMethod:f}=p,v=l.value,b=r.value,C=[];return a().eachTree(i,((e,t,o,n,l)=>{const r=!f||f({column:e});if(r){const t=e.visible,o=e.halfVisible,n=oe(e.getTitle(),1),r=e.children&&e.children.length,a=!!m&&!m({column:e});C.push((0,H.h)("tr",{key:e.id,colid:e.id,class:[`vxe-table-custom-popup--row level--${e.level}`,{"is--group":r}],onDragstart:k,onDragend:O,onDragover:M},[(0,H.h)("td",{class:"vxe-table-custom-popup--column-item col--sort"},[1===e.level?(0,H.h)("span",{class:"vxe-table-custom-popup--column-sort-btn",onMousedown:E,onMouseup:S},[(0,H.h)("i",{class:"vxe-icon-sort"})]):null]),(0,H.h)("td",{class:"vxe-table-custom-popup--column-item col--name"},[(0,H.h)("div",{class:"vxe-table-custom-popup--name",title:n},n)]),(0,H.h)("td",{class:"vxe-table-custom-popup--column-item col--visible"},[(0,H.h)("div",{class:["vxe-table-custom--checkbox-option",{"is--checked":t,"is--indeterminate":o,"is--disabled":a}],onClick:()=>{a||w(e)}},[(0,H.h)("span",{class:["vxe-checkbox--icon",o?c.icon.TABLE_CHECKBOX_INDETERMINATE:t?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})])]),(0,H.h)("td",{class:"vxe-table-custom-popup--column-item col--fixed"},[!l&&p.allowFixed?(0,H.h)(Pn,{modelValue:e.fixed||"",type:"button",size:"mini",options:[{label:c.i18n("vxe.custom.setting.fixedLeft"),value:"left",disabled:b},{label:c.i18n("vxe.custom.setting.fixedUnset"),value:""},{label:c.i18n("vxe.custom.setting.fixedRight"),value:"right",disabled:b}],"onUpdate:modelValue"(t){e.fixed=t},onChange(){y(e)}}):null])]))}})),(0,H.h)(bn,{key:"popup",className:"vxe-table-custom-popup-wrapper vxe-table--ignore-clear",modelValue:t.visible,title:c.i18n("vxe.custom.cstmTitle"),width:"40vw",minWidth:520,height:"50vh",minHeight:300,mask:!0,lockView:!0,showFooter:!0,resize:!0,escClosable:!0,destroyOnClose:!0,"onUpdate:modelValue"(e){t.visible=e}},{default:()=>(0,H.h)("div",{ref:s,class:"vxe-table-custom-popup--body"},[(0,H.h)("div",{class:"vxe-table-custom-popup--table-wrapper"},[(0,H.h)("table",{},[(0,H.h)("colgroup",{},[(0,H.h)("col",{style:{width:"80px"}}),(0,H.h)("col",{}),(0,H.h)("col",{style:{width:"80px"}}),(0,H.h)("col",{style:{width:"200px"}})]),(0,H.h)("thead",{},[(0,H.h)("tr",{},[(0,H.h)("th",{},[(0,H.h)("span",{class:"vxe-table-custom-popup--table-sort-help-title"},c.i18n("vxe.custom.setting.colSort")),(0,H.h)(Pl,{enterable:!0,content:c.i18n("vxe.custom.setting.sortHelpTip")},{default:()=>(0,H.h)("i",{class:"vxe-table-custom-popup--table-sort-help-icon vxe-icon-question-circle-fill"})})]),(0,H.h)("th",{},c.i18n("vxe.custom.setting.colTitle")),(0,H.h)("th",{},c.i18n("vxe.custom.setting.colVisible")),(0,H.h)("th",{},c.i18n("vxe.custom.setting.colFixed",[v.maxFixedSize||0]))])]),(0,H.h)(H.TransitionGroup,{class:"vxe-table-custom--body",tag:"tbody",name:"vxe-table-custom--list"},{default:()=>C})])]),(0,H.h)("div",{ref:u,class:"vxe-table-custom-popup--drag-hint"},c.i18n("vxe.custom.cstmDragTarget",[d.value?d.value.getTitle():""]))]),footer:()=>(0,H.h)("div",{class:"vxe-table-custom-popup--footer"},[(0,H.h)(mn,{content:p.resetButtonText||c.i18n("vxe.custom.cstmRestore"),onClick:x}),(0,H.h)(mn,{content:p.resetButtonText||c.i18n("vxe.custom.cstmCancel"),onClick:g}),(0,H.h)(mn,{status:"primary",content:p.confirmButtonText||c.i18n("vxe.custom.cstmConfirm"),onClick:h})])})},F=()=>{const e=n.value;return"popup"===e.mode?D():I()};return F}});const Al=["openCustom","closeCustom"],Vl={setupTable(e){const{reactData:t,internalData:o}=e,{computeCustomOpts:n}=e.getComputeMaps(),{refTableHeader:l,refTableBody:r,refTableCustom:a}=e.getRefMaps(),i=e.xegrid,s=()=>{const{customStore:e}=t,o=l.value,n=r.value,i=a.value,s=i?i.$el:null,c=o.$el,u=n.$el;let d=0;c&&(d+=c.clientHeight),u&&(d+=u.clientHeight),e.maxHeight=Math.max(0,s?Math.min(s.clientHeight,d-80):0)},c=()=>{const{initStore:e,customStore:n}=t;return n.visible=!0,e.custom=!0,t.customColumnList=o.collectColumn.slice(0),p(),s(),(0,H.nextTick)().then((()=>s()))},u=()=>{const{customStore:o}=t,l=n.value;return o.visible&&(o.visible=!1,l.immediate||e.handleCustom()),(0,H.nextTick)()},d={openCustom:c,closeCustom:u},p=()=>{const{customStore:e}=t,{collectColumn:l}=o,r=n.value,{checkMethod:a}=r;e.isAll=l.every((e=>!!a&&!a({column:e})||e.visible)),e.isIndeterminate=!e.isAll&&l.some((e=>(!a||a({column:e}))&&(e.visible||e.halfVisible)))},m=(t,o)=>{const n=i||e;n.dispatchEvent("custom",{type:t},o)},f={checkCustomStatus:p,emitCustomEvent:m,triggerCustomEvent(t){const{customStore:o}=e.reactData;o.visible?(u(),m("close",t)):(o.btnEl=t.target,c(),m("open",t))},customOpenEvent(o){const{customStore:n}=t;n.visible||(n.activeBtn=!0,n.btnEl=o.target,e.openCustom(),e.emitCustomEvent("open",o))},customColseEvent(o){const{customStore:n}=t;n.visible&&(n.activeBtn=!1,e.closeCustom(),e.emitCustomEvent("close",o))}};return{...d,...f}},setupGrid(e){return e.extendTableMethods(Al)}};var Bl=Vl;const _l={Panel:Ll,install(e){No.hooks.add("$tableCustom",Bl),e.component(Ll.name,Ll)}},jl=_l;zo.component(Ll.name,Ll);var Hl=(0,H.defineComponent)({name:"VxeIcon",props:{name:String,roll:Boolean,status:String},emits:["click"],setup(e,{emit:t}){const o=e=>{t("click",{$event:e})};return()=>{const{name:t,roll:n,status:l}=e;return(0,H.h)("i",{class:[`vxe-icon-${t}`,n?"roll":"",l?[`theme--${l}`]:""],onClick:o})}}});const zl=Object.assign(Hl,{install(e){e.component(Hl.name,Hl)}}),Wl=zl;zo.component(zl.name,zl);function ql(e){const{$table:t,column:o}=e,n=o.titlePrefix||o.titleHelp;return n?[(0,H.h)("i",{class:["vxe-cell-title-prefix-icon",n.icon||c.icon.TABLE_TITLE_PREFIX],onMouseenter(o){t.triggerHeaderTitleEvent(o,n,e)},onMouseleave(e){t.handleTargetLeaveEvent(e)}})]:[]}function Ul(e){const{$table:t,column:o}=e,n=o.titleSuffix;return n?[(0,H.h)("i",{class:["vxe-cell-title-suffix-icon",n.icon||c.icon.TABLE_TITLE_SUFFIX],onMouseenter(o){t.triggerHeaderTitleEvent(o,n,e)},onMouseleave(e){t.handleTargetLeaveEvent(e)}})]:[]}function Gl(e,t){const{$table:o,column:n}=e,{props:l,reactData:r}=o,{computeTooltipOpts:i}=o.getComputeMaps(),{showHeaderOverflow:s}=l,{type:c,showHeaderOverflow:u}=n,d=i.value,p=d.showAll,m=a().isUndefined(u)||a().isNull(u)?s:u,f="title"===m,h=!0===m||"tooltip"===m,g={};return(f||h||p)&&(g.onMouseenter=t=>{r._isResize||(f?we(t.currentTarget,n):(h||p)&&o.triggerHeaderTooltipEvent(t,e))}),(h||p)&&(g.onMouseleave=e=>{r._isResize||(h||p)&&o.handleTargetLeaveEvent(e)}),["html"===c&&a().isString(t)?(0,H.h)("span",{class:"vxe-cell--title",innerHTML:t,...g}):(0,H.h)("span",{class:"vxe-cell--title",...g},lt(t))]}function Xl(e){const{$table:t,column:o,_columnIndex:n,items:l,row:r}=e,{slots:i,editRender:s,cellRender:c}=o,u=s||c,d=i?i.footer:null;if(d)return t.callSlot(d,e);if(u){const t=No.renderer.get(u.name);if(t&&t.renderFooter)return lt(t.renderFooter(u,e))}return a().isArray(l)?[oe(l[n],1)]:[oe(a().get(r,o.field),1)]}function Yl(e){const{$table:t,row:o,column:n}=e;return oe(t.getCellLabel(o,n),1)}const Kl={createColumn(e,t){const{type:o,sortable:n,filters:l,editRender:r,treeNode:a}=t,{props:i}=e,{editConfig:s}=i,{computeEditOpts:c,computeCheckboxOpts:u}=e.getComputeMaps(),d=u.value,p=c.value,m={renderHeader:Kl.renderDefaultHeader,renderCell:a?Kl.renderTreeCell:Kl.renderDefaultCell,renderFooter:Kl.renderDefaultFooter};switch(o){case"seq":m.renderHeader=Kl.renderSeqHeader,m.renderCell=a?Kl.renderTreeIndexCell:Kl.renderSeqCell;break;case"radio":m.renderHeader=Kl.renderRadioHeader,m.renderCell=a?Kl.renderTreeRadioCell:Kl.renderRadioCell;break;case"checkbox":m.renderHeader=Kl.renderCheckboxHeader,m.renderCell=d.checkField?a?Kl.renderTreeSelectionCellByProp:Kl.renderCheckboxCellByProp:a?Kl.renderTreeSelectionCell:Kl.renderCheckboxCell;break;case"expand":m.renderCell=Kl.renderExpandCell,m.renderData=Kl.renderExpandData;break;case"html":m.renderCell=a?Kl.renderTreeHTMLCell:Kl.renderHTMLCell,l&&n?m.renderHeader=Kl.renderSortAndFilterHeader:n?m.renderHeader=Kl.renderSortHeader:l&&(m.renderHeader=Kl.renderFilterHeader);break;default:s&&r?(m.renderHeader=Kl.renderEditHeader,m.renderCell="cell"===p.mode?a?Kl.renderTreeCellEdit:Kl.renderCellEdit:a?Kl.renderTreeRowEdit:Kl.renderRowEdit):l&&n?m.renderHeader=Kl.renderSortAndFilterHeader:n?m.renderHeader=Kl.renderSortHeader:l&&(m.renderHeader=Kl.renderFilterHeader)}return Ge(e,t,m)},renderHeaderTitle(e){const{$table:t,column:o}=e,{slots:n,editRender:l,cellRender:r}=o,a=l||r,i=n?n.header:null;if(i)return Gl(e,t.callSlot(i,e));if(a){const t=No.renderer.get(a.name);if(t&&t.renderHeader)return Gl(e,lt(t.renderHeader(a,e)))}return Gl(e,oe(o.getTitle(),1))},renderDefaultHeader(e){return ql(e).concat(Kl.renderHeaderTitle(e)).concat(Ul(e))},renderDefaultCell(e){const{$table:t,row:o,column:n}=e,{slots:l,editRender:r,cellRender:a}=n,i=r||a,s=l?l.default:null;if(s)return t.callSlot(s,e);if(i){const t=r?"renderCell":"renderDefault",o=No.renderer.get(i.name),n=o?o[t]:null;if(n)return lt(n(i,Object.assign({$type:r?"edit":"cell"},e)))}const c=t.getCellLabel(o,n),u=r?r.placeholder:"";return[(0,H.h)("span",{class:"vxe-cell--label"},r&&ne(c)?[(0,H.h)("span",{class:"vxe-cell--placeholder"},oe(te(u),1))]:oe(c,1))]},renderTreeCell(e){return Kl.renderTreeIcon(e,Kl.renderDefaultCell(e))},renderDefaultFooter(e){return[(0,H.h)("span",{class:"vxe-cell--item"},Xl(e))]},renderTreeIcon(e,t){const{$table:o,isHidden:n}=e,{reactData:l}=o,{computeTreeOpts:r}=o.getComputeMaps(),{treeExpandedMaps:a,treeExpandLazyLoadedMaps:i}=l,s=r.value,{row:u,column:d,level:p}=e,{slots:m}=d,{indent:f,lazy:h,trigger:g,iconLoaded:v,showIcon:x,iconOpen:b,iconClose:w}=s,C=s.children||s.childrenField,y=s.hasChild||s.hasChildField,T=u[C],E=m?m.icon:null;let S=!1,k=!1,R=!1;const O={};if(E)return o.callSlot(E,e);if(!n){const e=Le(o,u);k=!!a[e],h&&(R=!!i[e],S=u[y])}return g&&"default"!==g||(O.onClick=t=>{t.stopPropagation(),o.triggerTreeExpandEvent(t,e)}),[(0,H.h)("div",{class:["vxe-cell--tree-node",{"is--active":k}],style:{paddingLeft:p*f+"px"}},[x&&(T&&T.length||S)?[(0,H.h)("div",{class:"vxe-tree--btn-wrapper",...O},[(0,H.h)("i",{class:["vxe-tree--node-btn",R?v||c.icon.TABLE_TREE_LOADED:k?b||c.icon.TABLE_TREE_OPEN:w||c.icon.TABLE_TREE_CLOSE]})])]:null,(0,H.h)("div",{class:"vxe-tree-cell"},t)])]},renderSeqHeader(e){const{$table:t,column:o}=e,{slots:n}=o,l=n?n.header:null;return Gl(e,l?t.callSlot(l,e):oe(o.getTitle(),1))},renderSeqCell(e){const{$table:t,column:o}=e,{props:n}=t,{treeConfig:l}=n,{computeSeqOpts:r}=t.getComputeMaps(),a=r.value,{slots:i}=o,s=i?i.default:null;if(s)return t.callSlot(s,e);const{seq:c}=e,u=a.seqMethod;return[oe(u?u(e):l?c:(a.startIndex||0)+c,1)]},renderTreeIndexCell(e){return Kl.renderTreeIcon(e,Kl.renderSeqCell(e))},renderRadioHeader(e){const{$table:t,column:o}=e,{slots:n}=o,l=n?n.header:null,r=n?n.title:null;return Gl(e,l?t.callSlot(l,e):[(0,H.h)("span",{class:"vxe-radio--label"},r?t.callSlot(r,e):oe(o.getTitle(),1))])},renderRadioCell(e){const{$table:t,column:o,isHidden:n}=e,{reactData:l}=t,{computeRadioOpts:r}=t.getComputeMaps(),{selectRadioRow:i}=l,s=r.value,{slots:u}=o,{labelField:d,checkMethod:p,visibleMethod:m}=s,{row:f}=e,h=u?u.default:null,g=u?u.radio:null,v=t.eqRow(f,i),x=!m||m({row:f});let b,w=!!p;n||(b={onClick(o){!w&&x&&(o.stopPropagation(),t.triggerRadioRowEvent(o,e))}},p&&(w=!p({row:f})));const C={...e,checked:v,disabled:w,visible:x};if(g)return t.callSlot(g,C);const y=[];return x&&y.push((0,H.h)("span",{class:["vxe-radio--icon",v?c.icon.TABLE_RADIO_CHECKED:c.icon.TABLE_RADIO_UNCHECKED]})),(h||d)&&y.push((0,H.h)("span",{class:"vxe-radio--label"},h?t.callSlot(h,C):a().get(f,d))),[(0,H.h)("span",{class:["vxe-cell--radio",{"is--checked":v,"is--disabled":w}],...b},y)]},renderTreeRadioCell(e){return Kl.renderTreeIcon(e,Kl.renderRadioCell(e))},renderCheckboxHeader(e){const{$table:t,column:o,isHidden:n}=e,{reactData:l}=t,{computeIsAllCheckboxDisabled:r,computeCheckboxOpts:a}=t.getComputeMaps(),{isAllSelected:i,isIndeterminate:s}=l,u=r.value,{slots:d}=o,p=d?d.header:null,m=d?d.title:null,f=a.value,h=o.getTitle();let g;n||(g={onClick(e){u||(e.stopPropagation(),t.triggerCheckAllEvent(e,!i))}});const v={...e,checked:i,disabled:u,indeterminate:s};return p?Gl(v,t.callSlot(p,v)):(f.checkStrictly?f.showHeader:!1!==f.showHeader)?Gl(v,[(0,H.h)("span",{class:["vxe-cell--checkbox",{"is--checked":i,"is--disabled":u,"is--indeterminate":s}],title:c.i18n("vxe.table.allTitle"),...g},[(0,H.h)("span",{class:["vxe-checkbox--icon",s?c.icon.TABLE_CHECKBOX_INDETERMINATE:i?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})].concat(m||h?[(0,H.h)("span",{class:"vxe-checkbox--label"},m?t.callSlot(m,v):h)]:[]))]):Gl(v,[(0,H.h)("span",{class:"vxe-checkbox--label"},m?t.callSlot(m,v):h)])},renderCheckboxCell(e){const{$table:t,row:o,column:n,isHidden:l}=e,{props:r,reactData:i}=t,{treeConfig:s}=r,{selectCheckboxMaps:u,treeIndeterminateMaps:d}=i,{computeCheckboxOpts:p}=t.getComputeMaps(),m=p.value,{labelField:f,checkMethod:h,visibleMethod:g}=m,{slots:v}=n,x=v?v.default:null,b=v?v.checkbox:null;let w=!1,C=!1;const y=!g||g({row:o});let T,E=!!h;if(!l){const n=Le(t,o);C=!!u[n],T={onClick(o){!E&&y&&(o.stopPropagation(),t.triggerCheckRowEvent(o,e,!C))}},h&&(E=!h({row:o})),s&&(w=!!d[n])}const S={...e,checked:C,disabled:E,visible:y,indeterminate:w};if(b)return t.callSlot(b,S);const k=[];return y&&k.push((0,H.h)("span",{class:["vxe-checkbox--icon",w?c.icon.TABLE_CHECKBOX_INDETERMINATE:C?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})),(x||f)&&k.push((0,H.h)("span",{class:"vxe-checkbox--label"},x?t.callSlot(x,S):a().get(o,f))),[(0,H.h)("span",{class:["vxe-cell--checkbox",{"is--checked":C,"is--disabled":E,"is--indeterminate":w,"is--hidden":!y}],...T},k)]},renderTreeSelectionCell(e){return Kl.renderTreeIcon(e,Kl.renderCheckboxCell(e))},renderCheckboxCellByProp(e){const{$table:t,row:o,column:n,isHidden:l}=e,{props:r,reactData:i}=t,{treeConfig:s}=r,{treeIndeterminateMaps:u}=i,{computeCheckboxOpts:d}=t.getComputeMaps(),p=d.value,{labelField:m,checkField:f,checkMethod:h,visibleMethod:g}=p,v=p.indeterminateField||p.halfField,{slots:x}=n,b=x?x.default:null,w=x?x.checkbox:null;let C=!1,y=!1;const T=!g||g({row:o});let E,S=!!h;if(!l){const n=Le(t,o);y=a().get(o,f),E={onClick(o){!S&&T&&(o.stopPropagation(),t.triggerCheckRowEvent(o,e,!y))}},h&&(S=!h({row:o})),s&&(C=!!u[n])}const k={...e,checked:y,disabled:S,visible:T,indeterminate:C};if(w)return t.callSlot(w,k);const R=[];return T&&(R.push((0,H.h)("span",{class:["vxe-checkbox--icon",C?c.icon.TABLE_CHECKBOX_INDETERMINATE:y?c.icon.TABLE_CHECKBOX_CHECKED:c.icon.TABLE_CHECKBOX_UNCHECKED]})),(b||m)&&R.push((0,H.h)("span",{class:"vxe-checkbox--label"},b?t.callSlot(b,k):a().get(o,m)))),[(0,H.h)("span",{class:["vxe-cell--checkbox",{"is--checked":y,"is--disabled":S,"is--indeterminate":v&&!y?o[v]:C,"is--hidden":!T}],...E},R)]},renderTreeSelectionCellByProp(e){return Kl.renderTreeIcon(e,Kl.renderCheckboxCellByProp(e))},renderExpandCell(e){const{$table:t,isHidden:o,row:n,column:l}=e,{reactData:r}=t,{rowExpandedMaps:i,rowExpandLazyLoadedMaps:s}=r,{computeExpandOpts:u}=t.getComputeMaps(),d=u.value,{lazy:p,labelField:m,iconLoaded:f,showIcon:h,iconOpen:g,iconClose:v,visibleMethod:x}=d,{slots:b}=l,w=b?b.default:null,C=b?b.icon:null;let y=!1,T=!1;if(C)return t.callSlot(C,e);if(!o){const e=Le(t,n);y=!!i[e],p&&(T=!!s[e])}return[!h||x&&!x(e)?null:(0,H.h)("span",{class:["vxe-table--expanded",{"is--active":y}],onClick(o){o.stopPropagation(),t.triggerRowExpandEvent(o,e)}},[(0,H.h)("i",{class:["vxe-table--expand-btn",T?f||c.icon.TABLE_EXPAND_LOADED:y?g||c.icon.TABLE_EXPAND_OPEN:v||c.icon.TABLE_EXPAND_CLOSE]})]),w||m?(0,H.h)("span",{class:"vxe-table--expand-label"},w?t.callSlot(w,e):a().get(n,m)):null]},renderExpandData(e){const{$table:t,column:o}=e,{slots:n,contentRender:l}=o,r=n?n.content:null;if(r)return t.callSlot(r,e);if(l){const t=No.renderer.get(l.name);if(t&&t.renderExpand)return lt(t.renderExpand(l,e))}return[]},renderHTMLCell(e){const{$table:t,column:o}=e,{slots:n}=o,l=n?n.default:null;return l?t.callSlot(l,e):[(0,H.h)("span",{class:"vxe-cell--html",innerHTML:Yl(e)})]},renderTreeHTMLCell(e){return Kl.renderTreeIcon(e,Kl.renderHTMLCell(e))},renderSortAndFilterHeader(e){return Kl.renderDefaultHeader(e).concat(Kl.renderSortIcon(e)).concat(Kl.renderFilterIcon(e))},renderSortHeader(e){return Kl.renderDefaultHeader(e).concat(Kl.renderSortIcon(e))},renderSortIcon(e){const{$table:t,column:o}=e,{computeSortOpts:n}=t.getComputeMaps(),l=n.value,{showIcon:r,iconLayout:a,iconAsc:i,iconDesc:s}=l,{order:u}=o;return r?[(0,H.h)("span",{class:["vxe-cell--sort",`vxe-cell--sort-${a}-layout`]},[(0,H.h)("i",{class:["vxe-sort--asc-btn",i||c.icon.TABLE_SORT_ASC,{"sort--active":"asc"===u}],title:c.i18n("vxe.table.sortAsc"),onClick(e){e.stopPropagation(),t.triggerSortEvent(e,o,"asc")}}),(0,H.h)("i",{class:["vxe-sort--desc-btn",s||c.icon.TABLE_SORT_DESC,{"sort--active":"desc"===u}],title:c.i18n("vxe.table.sortDesc"),onClick(e){e.stopPropagation(),t.triggerSortEvent(e,o,"desc")}})])]:[]},renderFilterHeader(e){return Kl.renderDefaultHeader(e).concat(Kl.renderFilterIcon(e))},renderFilterIcon(e){const{$table:t,column:o,hasFilter:n}=e,{reactData:l}=t,{filterStore:r}=l,{computeFilterOpts:a}=t.getComputeMaps(),i=a.value,{showIcon:s,iconNone:u,iconMatch:d}=i;return s?[(0,H.h)("span",{class:["vxe-cell--filter",{"is--active":r.visible&&r.column===o}]},[(0,H.h)("i",{class:["vxe-filter--btn",n?d||c.icon.TABLE_FILTER_MATCH:u||c.icon.TABLE_FILTER_NONE],title:c.i18n("vxe.table.filter"),onClick(o){t.triggerFilterEvent&&t.triggerFilterEvent(o,e.column,e)}})])]:[]},renderEditHeader(e){const{$table:t,column:o}=e,{props:n}=t,{computeEditOpts:l}=t.getComputeMaps(),{editConfig:r,editRules:i}=n,s=l.value,{sortable:u,filters:d,editRender:p}=o;let m=!1;if(i){const e=a().get(i,o.field);e&&(m=e.some((e=>e.required)))}return(Y(r)?[m&&s.showAsterisk?(0,H.h)("i",{class:"vxe-cell--required-icon"}):null,Y(p)&&s.showIcon?(0,H.h)("i",{class:["vxe-cell--edit-icon",s.icon||c.icon.TABLE_EDIT]}):null]:[]).concat(Kl.renderDefaultHeader(e)).concat(u?Kl.renderSortIcon(e):[]).concat(d?Kl.renderFilterIcon(e):[])},renderRowEdit(e){const{$table:t,column:o}=e,{reactData:n}=t,{editStore:l}=n,{actived:r}=l,{editRender:a}=o;return Kl.runRenderer(e,Y(a)&&r&&r.row===e.row)},renderTreeRowEdit(e){return Kl.renderTreeIcon(e,Kl.renderRowEdit(e))},renderCellEdit(e){const{$table:t,column:o}=e,{reactData:n}=t,{editStore:l}=n,{actived:r}=l,{editRender:a}=o;return Kl.runRenderer(e,Y(a)&&r&&r.row===e.row&&r.column===e.column)},renderTreeCellEdit(e){return Kl.renderTreeIcon(e,Kl.renderCellEdit(e))},runRenderer(e,t){const{$table:o,column:n}=e,{slots:l,editRender:r,formatter:a}=n,i=l?l.default:null,s=l?l.edit:null,c=No.renderer.get(r.name);return t?s?o.callSlot(s,e):c&&c.renderEdit?lt(c.renderEdit(r,Object.assign({$type:"edit"},e))):[]:i?o.callSlot(i,e):a?[(0,H.h)("span",{class:"vxe-cell--label"},Yl(e))]:Kl.renderDefaultCell(e)}};var Zl=Kl;const Jl={colId:[String,Number],type:String,field:String,title:String,width:[Number,String],minWidth:[Number,String],maxWidth:[Number,String],resizable:{type:Boolean,default:null},fixed:String,align:String,headerAlign:String,footerAlign:String,showOverflow:{type:[Boolean,String],default:null},showHeaderOverflow:{type:[Boolean,String],default:null},showFooterOverflow:{type:[Boolean,String],default:null},className:[String,Function],headerClassName:[String,Function],footerClassName:[String,Function],formatter:[Function,Array,String],sortable:Boolean,sortBy:[String,Function],sortType:String,filters:{type:Array,default:null},filterMultiple:{type:Boolean,default:!0},filterMethod:Function,filterResetMethod:Function,filterRecoverMethod:Function,filterRender:Object,treeNode:Boolean,visible:{type:Boolean,default:null},headerExportMethod:Function,exportMethod:Function,footerExportMethod:Function,titleHelp:Object,titlePrefix:Object,titleSuffix:Object,cellType:String,cellRender:Object,editRender:Object,contentRender:Object,params:Object};var Ql=(0,H.defineComponent)({name:"VxeColumn",props:Jl,setup(e,{slots:t}){const o=(0,H.ref)(),n=(0,H.inject)("$xetable",{}),l=(0,H.inject)("xecolgroup",null),r=Zl.createColumn(n,e);r.slots=t,(0,H.provide)("$xegrid",null),Xe(n,e,r),(0,H.onMounted)((()=>{Ye(n,o.value,r,l)})),(0,H.onUnmounted)((()=>{Ke(n,r)}));const a=()=>(0,H.h)("div",{ref:o});return a}});const er=Object.assign(Ql,{install(e){e.component(Ql.name,Ql),e.component("VxeTableColumn",Ql)}}),tr=er;zo.component(Ql.name,Ql),zo.component("VxeTableColumn",Ql);var or=(0,H.defineComponent)({name:"VxeColgroup",props:Jl,setup(e,{slots:t}){const o=(0,H.ref)(),n=(0,H.inject)("$xetable",{}),l=(0,H.inject)("xecolgroup",null),r=Zl.createColumn(n,e),a={};t.header&&(a.header=t.header);const i={column:r};r.slots=a,r.children=[],(0,H.provide)("xecolgroup",i),(0,H.provide)("$xegrid",null),Xe(n,e,r),(0,H.onMounted)((()=>{Ye(n,o.value,r,l)})),(0,H.onUnmounted)((()=>{Ke(n,r)}));const s=()=>(0,H.h)("div",{ref:o},t.default?t.default():[]);return s}});const nr=Object.assign(or,{install(e){e.component(or.name,or),e.component("VxeTableColgroup",or)}}),lr=nr;zo.component(or.name,or),zo.component("VxeTableColgroup",or);let rr;const ar=[],ir=500;function sr(){ar.length&&(ar.forEach((e=>{e.tarList.forEach((t=>{const{target:o,width:n,heighe:l}=t,r=o.clientWidth,a=o.clientHeight,i=r&&n!==r,s=a&&l!==a;(i||s)&&(t.width=r,t.heighe=a,setTimeout(e.callback))}))})),cr())}function cr(){clearTimeout(rr),rr=setTimeout(sr,c.resizeInterval||ir)}class ur{constructor(e){U(this,"tarList",[]),U(this,"callback",void 0),this.callback=e}observe(e){if(e){const{tarList:t}=this;t.some((t=>t.target===e))||t.push({target:e,width:e.clientWidth,heighe:e.clientHeight}),ar.length||cr(),ar.some((e=>e===this))||ar.push(this)}}unobserve(e){a().remove(ar,(t=>t.tarList.some((t=>t.target===e))))}disconnect(){a().remove(ar,(e=>e===this))}}function dr(e){return window.ResizeObserver?new window.ResizeObserver(e):new ur(e)}const pr="body",mr={mini:3,small:2,medium:1};var fr=(0,H.defineComponent)({name:"VxeTableBody",props:{tableData:Array,tableColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(e){const t=(0,H.inject)("$xetable",{}),o=(0,H.inject)("xesize",null),{xID:n,props:l,context:r,reactData:i,internalData:s}=t,{refTableHeader:u,refTableBody:d,refTableFooter:p,refTableLeftBody:m,refTableRightBody:f,refValidTooltip:h}=t.getRefMaps(),{computeEditOpts:g,computeMouseOpts:v,computeSYOpts:x,computeEmptyOpts:b,computeKeyboardOpts:w,computeTooltipOpts:C,computeRadioOpts:y,computeExpandOpts:T,computeTreeOpts:E,computeCheckboxOpts:S,computeValidOpts:k,computeRowOpts:R,computeColumnOpts:O}=t.getComputeMaps(),M=(0,H.ref)(),$=(0,H.ref)(),I=(0,H.ref)(),D=(0,H.ref)(),F=(0,H.ref)(),N=(0,H.ref)(),P=(0,H.ref)(),L=()=>{if(o){const e=o.value;if(e)return mr[e]||0}return 0},A=()=>{const{delayHover:e}=l,{lastScrollTime:t,_isResize:o}=i;return!!(o||t&&Date.now()<t+e)},V=(e,o)=>{let n=1;if(!e)return n;const l=E.value,r=l.children||l.childrenField,a=e[r];if(a&&t.isTreeExpandByRow(e))for(let t=0;t<a.length;t++)n+=V(a[t],o);return n},B=(e,t,o)=>{let n=1;return o&&(n=V(t[o-1],e)),i.rowHeight*n-(o?1:12-L())},_=e=>{const{row:o,column:n}=e,{afterFullData:r}=s,{treeConfig:a}=l,i=E.value,{slots:c,treeNode:u}=n,{fullAllDataRowIdData:d}=s,p=Le(t,o),m=d[p];let f=0,h=0,g=[];if(m&&(f=m.level,h=m._index,g=m.items),c&&c.line)return t.callSlot(c.line,e);const v=t.eqRow(r[0],o);return a&&u&&(i.showLine||i.line)?[(0,H.h)("div",{class:"vxe-tree--line-wrapper"},[(0,H.h)("div",{class:"vxe-tree--line",style:{height:`${v?1:B(e,g,h)}px`,left:f*i.indent+(f?2-L():0)+16+"px"}})])]:[]},j=(e,o,n,r,c,u,d,p,m,f,h,v)=>{const{columnKey:b,height:w,showOverflow:y,cellClassName:T,cellStyle:E,align:M,spanMethod:$,mouseConfig:I,editConfig:D,editRules:F,tooltipConfig:N}=l,{tableData:P,overflowX:L,scrollYLoad:V,currentColumn:B,mergeList:j,editStore:z,isAllOverflow:W,validErrorMaps:q}=i,{afterFullData:U}=s,G=k.value,X=S.value,K=g.value,Z=C.value,J=R.value,Q=x.value,ee=O.value,{type:te,cellRender:oe,editRender:ne,align:le,showOverflow:re,className:ae,treeNode:se,slots:ce}=m,{actived:ue}=z,{rHeight:de}=Q,{height:pe}=J,me=ne||oe,fe=me?No.renderer.get(me.name):null,he=fe?fe.cellClassName:"",ge=fe?fe.cellStyle:"",ve=Z.showAll,xe=t.getColumnIndex(m),be=t.getVTColumnIndex(m),Ce=Y(ne);let ye=n?m.fixed!==n:m.fixed&&L;const Te=a().isUndefined(re)||a().isNull(re)?y:re;let Ee="ellipsis"===Te;const Se="title"===Te,ke=!0===Te||"tooltip"===Te;let Re,Oe=Se||ke||Ee;const Me={},$e=le||M,Ie=q[`${o}:${m.id}`],De=F&&G.showMessage&&("default"===G.message?w||P.length>1:"inline"===G.message),Fe={colid:m.id},Ne={$table:t,$grid:t.xegrid,seq:e,rowid:o,row:c,rowIndex:u,$rowIndex:d,_rowIndex:p,column:m,columnIndex:xe,$columnIndex:f,_columnIndex:be,fixed:n,type:pr,isHidden:ye,level:r,visibleData:U,data:P,items:v};if(V&&!Oe&&(Ee=Oe=!0),(Se||ke||ve||N)&&(Me.onMouseenter=e=>{A()||(Se?we(e.currentTarget,m):(ke||ve)&&t.triggerBodyTooltipEvent(e,Ne),t.dispatchEvent("cell-mouseenter",Object.assign({cell:e.currentTarget},Ne),e))}),(ke||ve||N)&&(Me.onMouseleave=e=>{A()||((ke||ve)&&t.handleTargetLeaveEvent(e),t.dispatchEvent("cell-mouseleave",Object.assign({cell:e.currentTarget},Ne),e))}),(X.range||I)&&(Me.onMousedown=e=>{t.triggerCellMousedownEvent(e,Ne)}),Me.onClick=e=>{t.triggerCellClickEvent(e,Ne)},Me.onDblclick=e=>{t.triggerCellDblclickEvent(e,Ne)},j.length){const e=Je(j,p,be);if(e){const{rowspan:t,colspan:o}=e;if(!t||!o)return null;t>1&&(Fe.rowspan=t),o>1&&(Fe.colspan=o)}}else if($){const{rowspan:e=1,colspan:t=1}=$(Ne)||{};if(!e||!t)return null;e>1&&(Fe.rowspan=e),t>1&&(Fe.colspan=t)}ye&&j&&(Fe.colspan>1||Fe.rowspan>1)&&(ye=!1),!ye&&D&&(ne||oe)&&(K.showStatus||K.showUpdateStatus)&&(Re=t.isUpdateByRow(c,m.field));const Pe=[];if(ye&&(y?W:y))Pe.push((0,H.h)("div",{class:["vxe-cell",{"c--title":Se,"c--tooltip":ke,"c--ellipsis":Ee}],style:{maxHeight:Oe&&(de||pe)?`${de||pe}px`:""}}));else if(Pe.push(..._(Ne),(0,H.h)("div",{class:["vxe-cell",{"c--title":Se,"c--tooltip":ke,"c--ellipsis":Ee}],style:{maxHeight:Oe&&(de||pe)?`${de||pe}px`:""},title:Se?t.getCellLabel(c,m):null},m.renderCell(Ne))),De&&Ie){const e=Ie.rule,o=ce?ce.valid:null,n={...Ne,...Ie};Pe.push((0,H.h)("div",{class:["vxe-cell--valid-error-hint",ie(G.className,n)],style:e&&e.maxWidth?{width:`${e.maxWidth}px`}:null},o?t.callSlot(o,n):[(0,H.h)("span",{class:"vxe-cell--valid-error-msg"},Ie.content)]))}return(0,H.h)("td",{class:["vxe-body--column",m.id,{[`col--${$e}`]:$e,[`col--${te}`]:te,"col--last":f===h.length-1,"col--tree-node":se,"col--edit":Ce,"col--ellipsis":Oe,"fixed--hidden":ye,"col--dirty":Re,"col--active":D&&Ce&&ue.row===c&&(ue.column===m||"row"===K.mode),"col--valid-error":!!Ie,"col--current":B===m},ie(he,Ne),ie(ae,Ne),ie(T,Ne)],key:b||ee.useKey?m.id:f,...Fe,style:Object.assign({height:Oe&&(de||pe)?`${de||pe}px`:""},a().isFunction(ge)?ge(Ne):ge,a().isFunction(E)?E(Ne):E),...Me},Pe)},z=(e,o,n)=>{const{stripe:r,rowKey:c,highlightHoverRow:u,rowClassName:d,rowStyle:p,showOverflow:m,editConfig:f,treeConfig:h}=l,{hasFixedColumn:v,treeExpandedMaps:x,scrollYLoad:b,rowExpandedMaps:w,expandColumn:C,selectRadioRow:k,pendingRowMaps:O,pendingRowList:M}=i,{fullAllDataRowIdData:$}=s,I=S.value,D=y.value,F=E.value,N=g.value,P=R.value,{transform:L}=F,V=F.children||F.childrenField,B=[];return o.forEach(((l,i)=>{const s={};let g=i;g=t.getRowIndex(l),(P.isHover||u)&&(s.onMouseenter=e=>{A()||t.triggerHoverEvent(e,{row:l,rowIndex:g})},s.onMouseleave=()=>{A()||t.clearHoverRow()});const y=Le(t,l),E=$[y];let S=0,R=-1,_=0;E&&(S=E.level,R=E.seq,_=E._index);const W={$table:t,seq:R,rowid:y,fixed:e,type:pr,level:S,row:l,rowIndex:g,$rowIndex:i,_rowIndex:_},q=C&&!!w[y];let U=!1,G=[],X=!1;if(f&&(X=t.isInsertByRow(l)),!h||b||L||(G=l[V],U=G&&G.length&&!!x[y]),B.push((0,H.h)("tr",{class:["vxe-body--row",h?`row--level-${S}`:"",{"row--stripe":r&&(t.getVTRowIndex(l)+1)%2===0,"is--new":X,"is--expand-row":q,"is--expand-tree":U,"row--new":X&&(N.showStatus||N.showInsertStatus),"row--radio":D.highlight&&t.eqRow(k,l),"row--checked":I.highlight&&t.isCheckedByCheckboxRow(l),"row--pending":M.length&&!!O[y]},ie(d,W)],rowid:y,style:p?a().isFunction(p)?p(W):p:null,key:c||P.useKey||h?y:i,...s},n.map(((t,r)=>j(R,y,e,S,l,g,i,_,t,r,n,o))))),q){const o=T.value,{height:r}=o,c={};r&&(c.height=`${r}px`),h&&(c.paddingLeft=S*F.indent+30+"px");const{showOverflow:u}=C,d=a().isUndefined(u)||a().isNull(u)?m:u,f={$table:t,seq:R,column:C,fixed:e,type:pr,level:S,row:l,rowIndex:g,$rowIndex:i,_rowIndex:_};B.push((0,H.h)("tr",{class:"vxe-body--expanded-row",key:`expand_${y}`,style:p?a().isFunction(p)?p(f):p:null,...s},[(0,H.h)("td",{class:{"vxe-body--expanded-column":1,"fixed--hidden":e&&!v,"col--ellipsis":d},colspan:n.length},[(0,H.h)("div",{class:{"vxe-body--expanded-cell":1,"is--ellipsis":r},style:c},[C.renderData(f)])])]))}U&&B.push(...z(e,G,n))})),B};let W;const q=(e,t,o,n)=>{(o||n)&&(o&&(De(o),o.scrollTop=t),n&&(De(n),n.scrollTop=t),clearTimeout(W),W=setTimeout((()=>{Fe(o),Fe(n),i.lastScrollTime=Date.now()}),300))},U=null,G=o=>{const{fixedType:n}=e,{highlightHoverRow:r}=l,{scrollXLoad:a,scrollYLoad:c}=i,{elemStore:g,lastScrollTop:v,lastScrollLeft:x}=s,b=R.value,w=u.value,C=d.value,y=p.value,T=m.value,E=f.value,S=h.value,k=M.value,O=w?w.$el:null,$=y?y.$el:null,I=C.$el,D=T?T.$el:null,F=E?E.$el:null,N=g["main-body-ySpace"],P=N?N.value:null,L=g["main-body-xSpace"],A=L?L.value:null,V=c&&P?P.clientHeight:I.clientHeight,B=a&&A?A.clientWidth:I.clientWidth;let _=k.scrollTop;const j=I.scrollLeft,H=j!==x,z=_!==v;s.lastScrollTop=_,s.lastScrollLeft=j,i.lastScrollTime=Date.now(),(b.isHover||r)&&t.clearHoverRow(),D&&"left"===n?(_=D.scrollTop,q(n,_,I,F)):F&&"right"===n?(_=F.scrollTop,q(n,_,I,D)):(H&&(O&&(O.scrollLeft=I.scrollLeft),$&&($.scrollLeft=I.scrollLeft)),(D||F)&&(t.checkScrolling(),z&&q(n,_,D,F))),a&&H&&t.triggerScrollXEvent(o),c&&z&&t.triggerScrollYEvent(o),null!==U&&clearTimeout(U),H&&S&&S.reactData.visible&&S.updatePlacement(),t.dispatchEvent("scroll",{type:pr,fixed:n,scrollTop:_,scrollLeft:j,scrollHeight:I.scrollHeight,scrollWidth:I.scrollWidth,bodyHeight:V,bodyWidth:B,isX:H,isY:z},o)};let X,K=0,Z=0,J=0,Q=!1;const ee=(o,n,l,r,a)=>{const{elemStore:c}=s,{scrollXLoad:u,scrollYLoad:p}=i,h=d.value,g=m.value,v=f.value,x=g?g.$el:null,b=v?v.$el:null,w=h.$el,C=c["main-body-ySpace"],y=C?C.value:null,T=c["main-body-xSpace"],E=T?T.value:null,S=p&&y?y.clientHeight:w.clientHeight,k=u&&E?E.clientWidth:w.clientWidth,R=Q===n?Math.max(0,K-J):0;Q=n,K=Math.abs(n?l-R:l+R),Z=0,J=0,clearTimeout(X);const O=()=>{if(J<K){const{fixedType:l}=e;Z=Math.max(5,Math.floor(1.5*Z)),J+=Z,J>K&&(Z-=J-K);const{scrollTop:i,clientHeight:s,scrollHeight:c}=w,u=i+Z*(n?-1:1);w.scrollTop=u,x&&(x.scrollTop=u),b&&(b.scrollTop=u),(n?u<c-s:u>=0)&&(X=setTimeout(O,10)),t.dispatchEvent("scroll",{type:pr,fixed:l,scrollTop:w.scrollTop,scrollLeft:w.scrollLeft,scrollHeight:w.scrollHeight,scrollWidth:w.scrollWidth,bodyHeight:S,bodyWidth:k,isX:r,isY:a},o)}};O()},te=e=>{const{deltaY:o,deltaX:n}=e,{highlightHoverRow:r}=l,{scrollYLoad:a}=i,{lastScrollTop:c,lastScrollLeft:u}=s,p=R.value,m=d.value,f=M.value,h=m.$el,g=o,v=n,x=g<0;if(x?f.scrollTop<=0:f.scrollTop>=f.scrollHeight-f.clientHeight)return;const b=f.scrollTop+g,w=h.scrollLeft+v,C=w!==u,y=b!==c;y&&(e.preventDefault(),s.lastScrollTop=b,s.lastScrollLeft=w,i.lastScrollTime=Date.now(),(p.isHover||r)&&t.clearHoverRow(),ee(e,x,g,C,y),a&&t.triggerScrollYEvent(e))};(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{fixedType:t}=e,{elemStore:o}=s,n=`${t||"main"}-body-`,l=M.value;o[`${n}wrapper`]=M,o[`${n}table`]=$,o[`${n}colgroup`]=I,o[`${n}list`]=D,o[`${n}xSpace`]=F,o[`${n}ySpace`]=N,o[`${n}emptyBlock`]=P,l&&(l.onscroll=G,l._onscroll=G)}))})),(0,H.onBeforeUnmount)((()=>{const e=M.value;clearTimeout(X),e&&(e._onscroll=null,e.onscroll=null)})),(0,H.onUnmounted)((()=>{const{fixedType:t}=e,{elemStore:o}=s,n=`${t||"main"}-body-`;o[`${n}wrapper`]=null,o[`${n}table`]=null,o[`${n}colgroup`]=null,o[`${n}list`]=null,o[`${n}xSpace`]=null,o[`${n}ySpace`]=null,o[`${n}emptyBlock`]=null}));const oe=()=>{let{fixedColumn:o,fixedType:a,tableColumn:u}=e;const{keyboardConfig:d,showOverflow:p,spanMethod:m,mouseConfig:f}=l,{tableData:h,mergeList:g,scrollYLoad:C,isAllOverflow:y}=i,{visibleColumn:T}=s,{slots:E}=r,S=x.value,k=b.value,R=w.value,O=v.value;let L;a&&(u=i.expandColumn||!C&&!(p?y:p)||g.length||m||d&&R.isMerge?T:o);const A=E?E.empty:null;if(A)L=t.callSlot(A,{$table:t,$grid:t.xegrid});else{const e=k.name?No.renderer.get(k.name):null,o=e?e.renderTableEmptyView||e.renderEmpty:null;L=o?lt(o(k,{$table:t})):l.emptyText||c.i18n("vxe.table.emptyText")}return(0,H.h)("div",{ref:M,class:["vxe-table--body-wrapper",a?`fixed-${a}--wrapper`:"body--wrapper"],xid:n,..."wheel"===S.mode?{onWheel:te}:{}},[a?(0,H.createCommentVNode)():(0,H.h)("div",{ref:F,class:"vxe-body--x-space"}),(0,H.h)("div",{ref:N,class:"vxe-body--y-space"}),(0,H.h)("table",{ref:$,class:"vxe-table--body",xid:n,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("colgroup",{ref:I},u.map(((e,t)=>(0,H.h)("col",{name:e.id,key:t})))),(0,H.h)("tbody",{ref:D},z(a,h,u))]),(0,H.h)("div",{class:"vxe-table--checkbox-range"}),f&&O.area?(0,H.h)("div",{class:"vxe-table--cell-area"},[(0,H.h)("span",{class:"vxe-table--cell-main-area"},O.extension?[(0,H.h)("span",{class:"vxe-table--cell-main-area-btn",onMousedown(e){t.triggerCellExtendMousedownEvent(e,{$table:t,fixed:a,type:pr})}})]:[]),(0,H.h)("span",{class:"vxe-table--cell-copy-area"}),(0,H.h)("span",{class:"vxe-table--cell-extend-area"}),(0,H.h)("span",{class:"vxe-table--cell-multi-area"}),(0,H.h)("span",{class:"vxe-table--cell-active-area"})]):null,a?null:(0,H.h)("div",{class:"vxe-table--empty-block",ref:P},[(0,H.h)("div",{class:"vxe-table--empty-content"},L)])])};return oe}});const hr="header";var gr=(0,H.defineComponent)({name:"VxeTableHeader",props:{tableData:Array,tableColumn:Array,tableGroupColumn:Array,fixedColumn:Array,fixedType:{type:String,default:null}},setup(e){const t=(0,H.inject)("$xetable",{}),{xID:o,props:n,reactData:l,internalData:r}=t,{refElem:i,refTableBody:s,refLeftContainer:c,refRightContainer:u,refCellResizeBar:d}=t.getRefMaps(),{computeColumnOpts:p}=t.getComputeMaps(),m=(0,H.ref)([]),f=(0,H.ref)(),h=(0,H.ref)(),g=(0,H.ref)(),v=(0,H.ref)(),x=(0,H.ref)(),b=(0,H.ref)(),w=()=>{const{isGroup:t}=l;m.value=t?$e(e.tableGroupColumn):[]},C=(o,n)=>{const{column:a}=n,{fixedType:p}=e,m=s.value,h=c.value,g=u.value,v=d.value,{clientX:x}=o,b=f.value,w=o.target,C=n.cell=w.parentNode;let y=0;const T=m.$el,E=ye(w,b),S=w.clientWidth,k=Math.floor(S/2),R=qe(n)-k;let O=E.left-C.clientWidth+S+R,M=E.left+k;const $=document.onmousemove,I=document.onmouseup,D="left"===p,F="right"===p,N=i.value;let P=0;if(D||F){const e=D?"nextElementSibling":"previousElementSibling";let t=C[e];while(t){if(pe(t,"fixed--hidden"))break;pe(t,"col--group")||(P+=t.offsetWidth),t=t[e]}F&&g&&(M=g.offsetLeft+P)}const L=function(e){e.stopPropagation(),e.preventDefault();const t=e.clientX-x;let o=M+t;const n=p?0:T.scrollLeft;D?o=Math.min(o,(g?g.offsetLeft:T.clientWidth)-P-R):F?(O=(h?h.clientWidth:0)+P+R,o=Math.min(o,M+C.clientWidth-R)):O=Math.max(T.scrollLeft,O),y=Math.max(o,O),v.style.left=y-n+"px"};l._isResize=!0,fe(N,"drag--resize"),v.style.display="block",document.onmousemove=L,document.onmouseup=function(e){document.onmousemove=$,document.onmouseup=I;const o=a.renderWidth+(F?M-y:y-M);a.resizeWidth=o,v.style.display="none",l._isResize=!1,r._lastResizeTime=Date.now(),t.analyColumnWidth(),t.recalculate(!0).then((()=>{t.saveCustomResizable(),t.updateCellAreas(),t.dispatchEvent("resizable-change",{...n,resizeWidth:o},e)})),me(N,"drag--resize")},L(o),t.closeMenu&&t.closeMenu()};(0,H.watch)((()=>e.tableColumn),w),(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{fixedType:o}=e,{internalData:n}=t,{elemStore:l}=n,r=`${o||"main"}-header-`;l[`${r}wrapper`]=f,l[`${r}table`]=h,l[`${r}colgroup`]=g,l[`${r}list`]=v,l[`${r}xSpace`]=x,l[`${r}repair`]=b,w()}))})),(0,H.onUnmounted)((()=>{const{fixedType:o}=e,{internalData:n}=t,{elemStore:l}=n,r=`${o||"main"}-header-`;l[`${r}wrapper`]=null,l[`${r}table`]=null,l[`${r}colgroup`]=null,l[`${r}list`]=null,l[`${r}xSpace`]=null,l[`${r}repair`]=null}));const y=()=>{const{fixedType:i,fixedColumn:s,tableColumn:c}=e,{resizable:u,border:d,columnKey:w,headerRowClassName:y,headerCellClassName:T,headerRowStyle:E,headerCellStyle:S,showHeaderOverflow:k,headerAlign:R,align:O,mouseConfig:M}=n,{isGroup:$,currentColumn:I,scrollXLoad:D,overflowX:F,scrollbarWidth:N}=l,{visibleColumn:P}=r,L=p.value;let A=m.value,V=c;return $?V=P:(i&&(D||k)&&(V=s),A=[V]),(0,H.h)("div",{ref:f,class:["vxe-table--header-wrapper",i?`fixed-${i}--wrapper`:"body--wrapper"],xid:o},[i?(0,H.createCommentVNode)():(0,H.h)("div",{ref:x,class:"vxe-body--x-space"}),(0,H.h)("table",{ref:h,class:"vxe-table--header",xid:o,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("colgroup",{ref:g},V.map(((e,t)=>(0,H.h)("col",{name:e.id,key:t}))).concat(N?[(0,H.h)("col",{name:"col_gutter"})]:[])),(0,H.h)("thead",{ref:v},A.map(((e,o)=>(0,H.h)("tr",{class:["vxe-header--row",y?a().isFunction(y)?y({$table:t,$rowIndex:o,fixed:i,type:hr}):y:""],style:E?a().isFunction(E)?E({$table:t,$rowIndex:o,fixed:i,type:hr}):E:null},e.map(((n,l)=>{const{type:r,showHeaderOverflow:s,headerAlign:c,align:p,headerClassName:m}=n,f=n.children&&n.children.length,h=i?n.fixed!==i&&!f:!!n.fixed&&F,g=a().isUndefined(s)||a().isNull(s)?k:s,v=c||p||R||O;let x="ellipsis"===g;const b="title"===g,y=!0===g||"tooltip"===g;let E=b||y||x;const $=n.filters&&n.filters.some((e=>e.checked)),N=t.getColumnIndex(n),P=t.getVTColumnIndex(n),A={$table:t,$grid:t.xegrid,$rowIndex:o,column:n,columnIndex:N,$columnIndex:l,_columnIndex:P,fixed:i,type:hr,isHidden:h,hasFilter:$},V={onClick:e=>t.triggerHeaderCellClickEvent(e,A),onDblclick:e=>t.triggerHeaderCellDblclickEvent(e,A)};return D&&!E&&(x=E=!0),M&&(V.onMousedown=e=>t.triggerHeaderCellMousedownEvent(e,A)),(0,H.h)("th",{class:["vxe-header--column",n.id,{[`col--${v}`]:v,[`col--${r}`]:r,"col--last":l===e.length-1,"col--fixed":n.fixed,"col--group":f,"col--ellipsis":E,"fixed--hidden":h,"is--sortable":n.sortable,"col--filter":!!n.filters,"is--filter-active":$,"col--current":I===n},m?a().isFunction(m)?m(A):m:"",T?a().isFunction(T)?T(A):T:""],colid:n.id,colspan:n.colSpan>1?n.colSpan:null,rowspan:n.rowSpan>1?n.rowSpan:null,style:S?a().isFunction(S)?S(A):S:null,...V,key:w||L.useKey||f?n.id:l},[(0,H.h)("div",{class:["vxe-cell",{"c--title":b,"c--tooltip":y,"c--ellipsis":x}]},n.renderHeader(A)),h||f||!(a().isBoolean(n.resizable)?n.resizable:L.resizable||u)?null:(0,H.h)("div",{class:["vxe-resizable",{"is--line":!d||"none"===d}],onMousedown:e=>C(e,A)})])})).concat(N?[(0,H.h)("th",{class:"vxe-header--gutter col--gutter"})]:[])))))]),(0,H.h)("div",{ref:b,class:"vxe-table--header-border-line"})])};return y}});const vr="footer";function xr(e,t,o){for(let n=0;n<e.length;n++){const{row:l,col:r,rowspan:a,colspan:i}=e[n];if(r>-1&&l>-1&&a&&i){if(l===t&&r===o)return{rowspan:a,colspan:i};if(t>=l&&t<l+a&&o>=r&&o<r+i)return{rowspan:0,colspan:0}}}}var br=(0,H.defineComponent)({name:"VxeTableFooter",props:{footerTableData:{type:Array,default:()=>[]},tableColumn:{type:Array,default:()=>[]},fixedColumn:{type:Array,default:()=>[]},fixedType:{type:String,default:null}},setup(e){const t=(0,H.inject)("$xetable",{}),{xID:o,props:n,reactData:l,internalData:r}=t,{refTableHeader:i,refTableBody:s,refValidTooltip:c}=t.getRefMaps(),{computeTooltipOpts:u,computeColumnOpts:d}=t.getComputeMaps(),p=(0,H.ref)(),m=(0,H.ref)(),f=(0,H.ref)(),h=(0,H.ref)(),g=(0,H.ref)(),v=o=>{const{fixedType:n}=e,{scrollXLoad:a}=l,{lastScrollLeft:u}=r,d=c.value,m=i.value,f=s.value,h=m?m.$el:null,g=p.value,v=f.$el,x=g.scrollLeft,b=x!==u;r.lastScrollLeft=x,l.lastScrollTime=Date.now(),h&&(h.scrollLeft=x),v&&(v.scrollLeft=x),a&&b&&t.triggerScrollXEvent(o),b&&d&&d.reactData.visible&&d.updatePlacement(),t.dispatchEvent("scroll",{type:vr,fixed:n,scrollTop:v.scrollTop,scrollLeft:x,isX:b,isY:!1},o)};(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{fixedType:t}=e,{elemStore:o}=r,n=`${t||"main"}-footer-`;o[`${n}wrapper`]=p,o[`${n}table`]=m,o[`${n}colgroup`]=f,o[`${n}list`]=h,o[`${n}xSpace`]=g}))})),(0,H.onUnmounted)((()=>{const{fixedType:t}=e,{elemStore:o}=r,n=`${t||"main"}-footer-`;o[`${n}wrapper`]=null,o[`${n}table`]=null,o[`${n}colgroup`]=null,o[`${n}list`]=null,o[`${n}xSpace`]=null}));const x=()=>{let{fixedType:i,fixedColumn:s,tableColumn:c,footerTableData:x}=e;const{footerRowClassName:b,footerCellClassName:w,footerRowStyle:C,footerCellStyle:y,footerAlign:T,footerSpanMethod:E,align:S,columnKey:k,showFooterOverflow:R}=n,{visibleColumn:O}=r,{scrollXLoad:M,overflowX:$,scrollbarWidth:I,currentColumn:D,mergeFooterList:F}=l,N=u.value,P=d.value;return i&&(c=l.expandColumn||!M&&!R||F.length&&E?O:s),(0,H.h)("div",{ref:p,class:["vxe-table--footer-wrapper",i?`fixed-${i}--wrapper`:"body--wrapper"],xid:o,onScroll:v},[i?(0,H.createCommentVNode)():(0,H.h)("div",{ref:g,class:"vxe-body--x-space"}),(0,H.h)("table",{ref:m,class:"vxe-table--footer",xid:o,cellspacing:0,cellpadding:0,border:0},[(0,H.h)("colgroup",{ref:f},c.map(((e,t)=>(0,H.h)("col",{name:e.id,key:t}))).concat(I?[(0,H.h)("col",{name:"col_gutter"})]:[])),(0,H.h)("tfoot",{ref:h},x.map(((e,o)=>{const n=o,l={$table:t,row:e,_rowIndex:o,$rowIndex:n,fixed:i,type:vr};return(0,H.h)("tr",{class:["vxe-footer--row",b?a().isFunction(b)?b(l):b:""],style:C?a().isFunction(C)?C(l):C:null},c.map(((l,r)=>{const{type:s,showFooterOverflow:u,footerAlign:d,align:p,footerClassName:m}=l,f=N.showAll,h=l.children&&l.children.length,g=i?l.fixed!==i&&!h:l.fixed&&$,v=a().isUndefined(u)||a().isNull(u)?R:u,b=d||p||T||S;let C="ellipsis"===v;const O="title"===v,I=!0===v||"tooltip"===v;let L=O||I||C;const A={colid:l.id},V={},B=t.getColumnIndex(l),_=t.getVTColumnIndex(l),j=_,z={$table:t,$grid:t.xegrid,row:e,rowIndex:o,_rowIndex:o,$rowIndex:n,column:l,columnIndex:B,$columnIndex:r,_columnIndex:_,itemIndex:j,items:e,fixed:i,type:vr,data:x};if(M&&!L&&(C=L=!0),(O||I||f)&&(V.onMouseenter=e=>{O?we(e.currentTarget,l):(I||f)&&t.triggerFooterTooltipEvent(e,z)}),(I||f)&&(V.onMouseleave=e=>{(I||f)&&t.handleTargetLeaveEvent(e)}),V.onClick=e=>{t.dispatchEvent("footer-cell-click",Object.assign({cell:e.currentTarget},z),e)},V.onDblclick=e=>{t.dispatchEvent("footer-cell-dblclick",Object.assign({cell:e.currentTarget},z),e)},F.length){const e=xr(F,o,_);if(e){const{rowspan:t,colspan:o}=e;if(!t||!o)return null;t>1&&(A.rowspan=t),o>1&&(A.colspan=o)}}else if(E){const{rowspan:e=1,colspan:t=1}=E(z)||{};if(!e||!t)return null;e>1&&(A.rowspan=e),t>1&&(A.colspan=t)}return(0,H.h)("td",{class:["vxe-footer--column",l.id,{[`col--${b}`]:b,[`col--${s}`]:s,"col--last":r===c.length-1,"fixed--hidden":g,"col--ellipsis":L,"col--current":D===l},ie(m,z),ie(w,z)],...A,style:y?a().isFunction(y)?y(z):y:null,...V,key:k||P.useKey?l.id:r},[(0,H.h)("div",{class:["vxe-cell",{"c--title":O,"c--tooltip":I,"c--ellipsis":C}]},l.renderFooter(z))])})).concat(I?[(0,H.h)("td",{class:"vxe-footer--gutter col--gutter"})]:[]))})))])])};return x}}),wr={id:String,data:Array,height:[Number,String],minHeight:{type:[Number,String],default:()=>c.table.minHeight},maxHeight:[Number,String],resizable:{type:Boolean,default:()=>c.table.resizable},stripe:{type:Boolean,default:()=>c.table.stripe},border:{type:[Boolean,String],default:()=>c.table.border},round:{type:Boolean,default:()=>c.table.round},size:{type:String,default:()=>c.table.size||c.size},fit:{type:Boolean,default:()=>c.table.fit},loading:Boolean,align:{type:String,default:()=>c.table.align},headerAlign:{type:String,default:()=>c.table.headerAlign},footerAlign:{type:String,default:()=>c.table.footerAlign},showHeader:{type:Boolean,default:()=>c.table.showHeader},highlightCurrentRow:{type:Boolean,default:()=>c.table.highlightCurrentRow},highlightHoverRow:{type:Boolean,default:()=>c.table.highlightHoverRow},highlightCurrentColumn:{type:Boolean,default:()=>c.table.highlightCurrentColumn},highlightHoverColumn:{type:Boolean,default:()=>c.table.highlightHoverColumn},highlightCell:Boolean,showFooter:Boolean,footerData:Array,footerMethod:Function,rowClassName:[String,Function],cellClassName:[String,Function],headerRowClassName:[String,Function],headerCellClassName:[String,Function],footerRowClassName:[String,Function],footerCellClassName:[String,Function],cellStyle:[Object,Function],headerCellStyle:[Object,Function],footerCellStyle:[Object,Function],rowStyle:[Object,Function],headerRowStyle:[Object,Function],footerRowStyle:[Object,Function],mergeCells:Array,mergeFooterItems:Array,spanMethod:Function,footerSpanMethod:Function,showOverflow:{type:[Boolean,String],default:()=>c.table.showOverflow},showHeaderOverflow:{type:[Boolean,String],default:()=>c.table.showHeaderOverflow},showFooterOverflow:{type:[Boolean,String],default:()=>c.table.showFooterOverflow},columnKey:Boolean,rowKey:Boolean,rowId:{type:String,default:()=>c.table.rowId},zIndex:Number,emptyText:{type:String,default:()=>c.table.emptyText},keepSource:{type:Boolean,default:()=>c.table.keepSource},autoResize:{type:Boolean,default:()=>c.table.autoResize},syncResize:[Boolean,String,Number],resizeConfig:Object,columnConfig:Object,rowConfig:Object,resizableConfig:Object,seqConfig:Object,sortConfig:Object,filterConfig:Object,radioConfig:Object,checkboxConfig:Object,tooltipConfig:Object,exportConfig:Object,importConfig:Object,printConfig:Object,expandConfig:Object,treeConfig:Object,menuConfig:Object,mouseConfig:Object,areaConfig:Object,keyboardConfig:Object,clipConfig:Object,fnrConfig:Object,editConfig:Object,validConfig:Object,editRules:Object,loadingConfig:Object,emptyRender:Object,customConfig:Object,scrollX:Object,scrollY:Object,animat:{type:Boolean,default:()=>c.table.animat},delayHover:{type:Number,default:()=>c.table.delayHover},params:Object},Cr=["update:data","keydown-start","keydown","keydown-end","paste","copy","cut","current-change","radio-change","checkbox-change","checkbox-all","checkbox-range-start","checkbox-range-change","checkbox-range-end","checkbox-range-select","cell-click","cell-dblclick","cell-menu","cell-mouseenter","cell-mouseleave","cell-selected","cell-delete-value","header-cell-click","header-cell-dblclick","header-cell-menu","footer-cell-click","footer-cell-dblclick","footer-cell-menu","clear-merge","sort-change","clear-sort","filter-change","filter-visible","clear-filter","resizable-change","toggle-row-expand","toggle-tree-expand","menu-click","edit-closed","edit-actived","edit-activated","edit-disabled","valid-error","scroll","custom","change-fnr","open-fnr","show-fnr","hide-fnr","fnr-change","fnr-find","fnr-find-all","fnr-replace","fnr-replace-all","cell-area-copy","cell-area-cut","cell-area-paste","cell-area-merge","clear-cell-area-merge","header-cell-area-selection","cell-area-selection-invalid","cell-area-selection-start","cell-area-selection-drag","cell-area-selection-end","cell-area-extension-start","cell-area-extension-drag","cell-area-extension-end","cell-area-selection-all-start","cell-area-selection-all-end","cell-area-arrows-start","cell-area-arrows-end","active-cell-change-start","active-cell-change-end"];const yr=ae["-webkit"]&&!ae.edge,Tr="VXE_TABLE_CUSTOM_COLUMN_WIDTH",Er="VXE_TABLE_CUSTOM_COLUMN_VISIBLE",Sr="VXE_TABLE_CUSTOM_COLUMN_FIXED",kr="VXE_TABLE_CUSTOM_COLUMN_SORT";var Rr=(0,H.defineComponent)({name:"VxeTable",props:wr,emits:Cr,setup(e,t){const{slots:o,emit:n}=t,l=No.tooltip,r=a().uniqueId(),i=pn(e),s=(0,H.getCurrentInstance)(),u=(0,H.reactive)({staticColumns:[],tableGroupColumn:[],tableColumn:[],tableData:[],scrollXLoad:!1,scrollYLoad:!1,overflowY:!0,overflowX:!1,scrollbarWidth:0,scrollbarHeight:0,lastScrollTime:0,rowHeight:0,parentHeight:0,isGroup:!1,isAllOverflow:!1,isAllSelected:!1,isIndeterminate:!1,selectCheckboxMaps:{},currentRow:null,currentColumn:null,selectRadioRow:null,footerTableData:[],expandColumn:null,treeNodeColumn:null,hasFixedColumn:!1,rowExpandedMaps:{},rowExpandLazyLoadedMaps:{},treeExpandedMaps:{},treeExpandLazyLoadedMaps:{},treeIndeterminateMaps:{},mergeList:[],mergeFooterList:[],upDataFlag:0,reColumnFlag:0,pendingRowMaps:{},pendingRowList:[],initStore:{filter:!1,import:!1,export:!1,custom:!1},customStore:{btnEl:null,isAll:!1,isIndeterminate:!1,activeBtn:!1,activeWrapper:!1,visible:!1,maxHeight:0},customColumnList:[],filterStore:{isAllSelected:!1,isIndeterminate:!1,style:null,options:[],column:null,multiple:!1,visible:!1,maxHeight:null},columnStore:{leftList:[],centerList:[],rightList:[],resizeList:[],pxList:[],pxMinList:[],scaleList:[],scaleMinList:[],autoList:[]},ctxMenuStore:{selected:null,visible:!1,showChild:!1,selectChild:null,list:[],style:null},editStore:{indexs:{columns:[]},titles:{columns:[]},selected:{row:null,column:null},copyed:{cut:!1,rows:[],columns:[]},actived:{row:null,column:null},focused:{row:null,column:null},insertMaps:{},removeMaps:{}},tooltipStore:{row:null,column:null,content:null,visible:!1},validStore:{visible:!1},validErrorMaps:{},importStore:{inited:!1,file:null,type:"",modeList:[],typeList:[],filename:"",visible:!1},importParams:{mode:"",types:null,message:!0},exportStore:{inited:!1,name:"",modeList:[],typeList:[],columns:[],isPrint:!1,hasFooter:!1,hasMerge:!1,hasTree:!1,hasColgroup:!1,visible:!1},exportParams:{filename:"",sheetName:"",mode:"",type:"",isColgroup:!1,isMerge:!1,isAllExpand:!1,useStyle:!1,original:!1,message:!0,isHeader:!1,isFooter:!1},scrollVMLoading:!1,_isResize:!1}),d={tZindex:0,elemStore:{},scrollXStore:{offsetSize:0,visibleSize:0,startIndex:0,endIndex:0},scrollYStore:{rowHeight:0,offsetSize:0,visibleSize:0,startIndex:0,endIndex:0},tableWidth:0,tableHeight:0,headerHeight:0,footerHeight:0,customHeight:0,customMinHeight:0,customMaxHeight:0,hoverRow:null,lastScrollLeft:0,lastScrollTop:0,radioReserveRow:null,checkboxReserveRowMap:{},rowExpandedReserveRowMap:{},treeExpandedReserveRowMap:{},treeIndeterminateRowMaps:{},tableFullData:[],afterFullData:[],afterTreeFullData:[],afterFullRowMaps:{},tableFullTreeData:[],tableSynchData:[],tableSourceData:[],collectColumn:[],tableFullColumn:[],visibleColumn:[],fullAllDataRowIdData:{},sourceDataRowIdData:{},fullDataRowIdData:{},fullColumnIdData:{},fullColumnFieldData:{},columnStatusMaps:{},rowStatusMaps:{},inited:!1,tooltipTimeout:null,initStatus:!1,isActivated:!1};let p={},m={};const f=(0,H.ref)(),h=(0,H.ref)(),g=(0,H.ref)(),v=(0,H.ref)(),x=(0,H.ref)(),b=(0,H.ref)(),w=(0,H.ref)(),C=(0,H.ref)(),y=(0,H.ref)(),T=(0,H.ref)(),E=(0,H.ref)(),S=(0,H.ref)(),k=(0,H.ref)(),R=(0,H.ref)(),O=(0,H.ref)(),M=(0,H.ref)(),$=(0,H.ref)(),I=(0,H.ref)(),D=(0,H.ref)(),F=(0,H.ref)(),N=(0,H.inject)("$xegrid",null);let P;const L=(0,H.computed)((()=>Object.assign({},c.table.validConfig,e.validConfig))),A=(0,H.computed)((()=>Object.assign({},c.table.scrollX,e.scrollX))),_=(0,H.computed)((()=>Object.assign({},c.table.scrollY,e.scrollY))),j=(0,H.computed)((()=>({default:48,medium:44,small:40,mini:36}))),z=(0,H.computed)((()=>Object.assign({},c.table.columnConfig,e.columnConfig))),W=(0,H.computed)((()=>Object.assign({},c.table.rowConfig,e.rowConfig))),q=(0,H.computed)((()=>Object.assign({},c.table.resizeConfig,e.resizeConfig))),U=(0,H.computed)((()=>Object.assign({},c.table.resizableConfig,e.resizableConfig))),G=(0,H.computed)((()=>Object.assign({startIndex:0},c.table.seqConfig,e.seqConfig))),X=(0,H.computed)((()=>Object.assign({},c.table.radioConfig,e.radioConfig))),K=(0,H.computed)((()=>Object.assign({},c.table.checkboxConfig,e.checkboxConfig)));let Z=(0,H.ref)();Z=(0,H.computed)((()=>Object.assign({},c.tooltip,c.table.tooltipConfig,e.tooltipConfig)));const le=(0,H.computed)((()=>{const e=Z.value;return{...e}})),re=(0,H.computed)((()=>{const e=Z.value;return Object.assign({isArrow:!1},e)})),ie=(0,H.computed)((()=>Object.assign({},c.table.editConfig,e.editConfig))),se=(0,H.computed)((()=>Object.assign({orders:["asc","desc",null]},c.table.sortConfig,e.sortConfig))),ce=(0,H.computed)((()=>Object.assign({},c.table.filterConfig,e.filterConfig))),he=(0,H.computed)((()=>Object.assign({},c.table.mouseConfig,e.mouseConfig))),ge=(0,H.computed)((()=>Object.assign({},c.table.areaConfig,e.areaConfig))),we=(0,H.computed)((()=>Object.assign({},c.table.keyboardConfig,e.keyboardConfig))),ye=(0,H.computed)((()=>Object.assign({},c.table.clipConfig,e.clipConfig))),Te=(0,H.computed)((()=>Object.assign({},c.table.fnrConfig,e.fnrConfig))),Ee=(0,H.computed)((()=>Object.assign({},c.table.menuConfig,e.menuConfig))),Se=(0,H.computed)((()=>{const e=Ee.value,t=e.header;return t&&t.options?t.options:[]})),ke=(0,H.computed)((()=>{const e=Ee.value,t=e.body;return t&&t.options?t.options:[]})),Re=(0,H.computed)((()=>{const e=Ee.value,t=e.footer;return t&&t.options?t.options:[]})),Me=(0,H.computed)((()=>{const t=Ee.value,o=Se.value,n=ke.value,l=Re.value;return!!(e.menuConfig&&Y(t)&&(o.length||n.length||l.length))})),$e=(0,H.computed)((()=>{const{ctxMenuStore:e}=u,t=[];return e.list.forEach((e=>{e.forEach((e=>{t.push(e)}))})),t})),De=(0,H.computed)((()=>Object.assign({},c.table.exportConfig,e.exportConfig))),Ve=(0,H.computed)((()=>Object.assign({},c.table.importConfig,e.importConfig))),Be=(0,H.computed)((()=>Object.assign({},c.table.printConfig,e.printConfig))),_e=(0,H.computed)((()=>Object.assign({},c.table.expandConfig,e.expandConfig))),je=(0,H.computed)((()=>Object.assign({},c.table.treeConfig,e.treeConfig))),qe=(0,H.computed)((()=>Object.assign({},c.table.emptyRender,e.emptyRender))),Ue=(0,H.computed)((()=>Object.assign({},c.table.loadingConfig,e.loadingConfig))),Ge=(0,H.computed)((()=>e.border?Math.max(2,Math.ceil(u.scrollbarWidth/u.tableColumn.length)):1)),Xe=(0,H.computed)((()=>Object.assign({},c.table.customConfig,e.customConfig))),Ye=(0,H.computed)((()=>{const{collectColumn:e}=d;let t=0;return e.forEach((e=>{e.fixed&&t++})),t})),Ke=(0,H.computed)((()=>{const e=Ye.value,t=z.value,{maxFixedSize:o}=t;return!!o&&e>=o})),Je=(0,H.computed)((()=>{const{border:t}=e;return!0===t?"full":t||"default"})),Qe=(0,H.computed)((()=>{const{treeConfig:t}=e,{tableData:o}=u,{tableFullData:n}=d,l=K.value,{strict:r,checkMethod:a}=l;return!!r&&(!o.length&&!n.length||!!a&&n.every((e=>!a({row:e}))))})),nt={refElem:f,refTooltip:h,refValidTooltip:v,refTableFilter:b,refTableCustom:w,refTableMenu:x,refTableHeader:C,refTableBody:y,refTableFooter:T,refTableLeftHeader:E,refTableLeftBody:S,refTableLeftFooter:k,refTableRightHeader:R,refTableRightBody:O,refTableRightFooter:M,refLeftContainer:$,refRightContainer:I,refCellResizeBar:D},rt={computeSize:i,computeValidOpts:L,computeSXOpts:A,computeSYOpts:_,computeColumnOpts:z,computeRowOpts:W,computeResizeleOpts:q,computeResizableOpts:U,computeSeqOpts:G,computeRadioOpts:X,computeCheckboxOpts:K,computeTooltipOpts:Z,computeEditOpts:ie,computeSortOpts:se,computeFilterOpts:ce,computeMouseOpts:he,computeAreaOpts:ge,computeKeyboardOpts:we,computeClipOpts:ye,computeFNROpts:Te,computeHeaderMenu:Se,computeBodyMenu:ke,computeFooterMenu:Re,computeIsMenu:Me,computeMenuOpts:Ee,computeExportOpts:De,computeImportOpts:Ve,computePrintOpts:Be,computeExpandOpts:_e,computeTreeOpts:je,computeEmptyOpts:qe,computeLoadingOpts:Ue,computeCustomOpts:Xe,computeFixedColumnSize:Ye,computeIsMaxFixedColumn:Ke,computeIsAllCheckboxDisabled:Qe},at={xID:r,props:e,context:t,instance:s,reactData:u,internalData:d,getRefMaps:()=>nt,getComputeMaps:()=>rt,xegrid:N},it=(e,t,o)=>{const n=a().get(e,o),l=a().get(t,o);return!(!ne(n)||!ne(l))||(a().isString(n)||a().isNumber(n)?""+n===""+l:a().isEqual(n,l))},st=e=>{const t=se.value,{orders:o}=t,n=e.order||null,l=o.indexOf(n)+1;return o[l<o.length?l:0]},ct=e=>{const t=c.version,o=a().toStringJSON(localStorage.getItem(e)||"");return o&&o._v===t?o:{_v:t}},ut=e=>{const{fullAllDataRowIdData:t}=d,o={};return a().each(e,((e,n)=>{t[n]&&(o[n]=e)})),o},dt=e=>{const{fullDataRowIdData:t}=d,o=[];return a().each(e,((e,n)=>{t[n]&&-1===at.findRowIndexOf(o,t[n].row)&&o.push(t[n].row)})),o},pt=()=>{const{visibleColumn:e}=d,t=y.value,o=t?t.$el:null;if(o){const{scrollLeft:t,clientWidth:n}=o,l=t+n;let r=-1,a=0,i=0;for(let o=0,s=e.length;o<s;o++)if(a+=e[o].renderWidth,-1===r&&t<a&&(r=o),r>=0&&(i++,a>l))break;return{toVisibleIndex:Math.max(0,r),visibleSize:Math.max(8,i)}}return{toVisibleIndex:0,visibleSize:8}},mt=()=>{const e=C.value,t=y.value,o=t?t.$el:null,n=i.value,l=j.value;if(o){const t=e?e.$el:null;let r,a=0;r=o.querySelector("tr"),!r&&t&&(r=t.querySelector("tr")),r&&(a=r.clientHeight),a||(a=l[n||"default"]);const i=Math.max(8,Math.ceil(o.clientHeight/a)+2);return{rowHeight:a,visibleSize:i}}return{rowHeight:0,visibleSize:8}},ft=(e,t,o)=>{for(let n=0,l=e.length;n<l;n++){const l=e[n],{startIndex:r,endIndex:a}=t,i=l[o],s=l[o+"span"],c=i+s;i<r&&r<c&&(t.startIndex=i),i<a&&a<c&&(t.endIndex=c),t.startIndex===r&&t.endIndex===a||(n=-1)}},ht=(t,o,n)=>{if(t){const{treeConfig:l}=e,{visibleColumn:r}=d;a().isArray(t)||(t=[t]),l&&t.length&&B("vxe.error.noTree",["merge-cells | merge-footer-items"]),t.forEach((e=>{let{row:t,col:l,rowspan:i,colspan:s}=e;if(n&&a().isNumber(t)&&(t=n[t]),a().isNumber(l)&&(l=r[l]),(n?t:a().isNumber(t))&&l&&(i||s)&&(i=a().toNumber(i)||1,s=a().toNumber(s)||1,i>1||s>1)){const e=a().findIndexOf(o,(e=>(e._row===t||Le(at,e._row)===Le(at,t))&&(e._col.id===l||e._col.id===l.id))),r=o[e];if(r)r.rowspan=i,r.colspan=s,r._rowspan=i,r._colspan=s;else{const e=n?at.findRowIndexOf(n,t):t,r=p.getVTColumnIndex(l);o.push({row:e,col:r,rowspan:i,colspan:s,_row:t,_col:l,_rowspan:i,_colspan:s})}}}))}},gt=(t,o,n)=>{const l=[];if(t){const{treeConfig:r}=e,{visibleColumn:i}=d;a().isArray(t)||(t=[t]),r&&t.length&&B("vxe.error.noTree",["merge-cells | merge-footer-items"]),t.forEach((e=>{let{row:t,col:r}=e;n&&a().isNumber(t)&&(t=n[t]),a().isNumber(r)&&(r=i[r]);const s=a().findIndexOf(o,(e=>(e._row===t||Le(at,e._row)===Le(at,t))&&(e._col.id===r||e._col.id===r.id)));if(s>-1){const e=o.splice(s,1);l.push(e[0])}}))}return l},vt=()=>{const{tableFullColumn:e}=d;e.forEach((e=>{e.order=null}))},xt=t=>{const{parentHeight:o}=u,n=e[t];let l=0;if(n)if("100%"===n||"auto"===n)l=o;else{const e=at.getExcludeHeight();l=de(n)?Math.floor((a().toInteger(n)||1)/100*o):a().toNumber(n),l=Math.max(40,l-e)}return l},bt=()=>{const{id:t,customConfig:o}=e,n=Xe.value,{storage:l}=n,r=!0===l,i=r?{}:Object.assign({},l||{}),s=r||i.resizable,c=r||i.visible,u=r||i.fixed,p=r||i.sort;if(o&&(s||c||u||p)){const e={};if(!t)return void B("vxe.error.reqProp",["id"]);if(s){const o=ct(Tr)[t];o&&a().each(o,((t,o)=>{e[o]={resizeWidth:t}}))}if(u){const o=ct(Sr)[t];if(o){const t=o.split(",");t.forEach((t=>{const[o,n]=t.split("|");e[o]?e[o].fixed=n:e[o]={fixed:n}}))}}let o=!1;if(p){const n=ct(kr)[t];n&&a().each(n,((t,n)=>{e[n]?e[n].renderSortNumber=t:e[n]={renderSortNumber:t},o||(o=!0)}))}if(c){const o=ct(Er)[t];if(o){const t=o.split("|"),n=t[0]?t[0].split(","):[],l=t[1]?t[1].split(","):[];n.forEach((t=>{e[t]?e[t].visible=!1:e[t]={visible:!1}})),l.forEach((t=>{e[t]?e[t].visible=!0:e[t]={visible:!0}}))}}let{collectColumn:n}=d;const l={};a().eachTree(n,(e=>{const t=e.getKey();t&&(l[t]=e)})),a().each(e,(({visible:e,resizeWidth:t,fixed:o,renderSortNumber:n},r)=>{const i=l[r];i&&(a().isNumber(t)&&(i.resizeWidth=t),a().isBoolean(e)&&(i.visible=e),o&&(i.fixed=o),n&&(i.renderSortNumber=Number(n)))})),o&&(n=a().orderBy(n,"renderSortNumber"),d.collectColumn=n,d.tableFullColumn=Qt(n))}},wt=()=>{const{tableFullColumn:t,collectColumn:o}=d,n=d.fullColumnIdData={},l=d.fullColumnFieldData={},r=he.value,i=z.value,s=W.value,c=o.some(ee);let p,m,f,h,g,v,x=!!e.showOverflow;const b=(e,t,o,r,a)=>{const{id:i,field:s,fixed:c,type:u,treeNode:d}=e,b={column:e,colid:i,index:t,items:o,parent:a};s&&(l[s]&&V("vxe.error.colRepet",["field",s]),l[s]=b),!v&&c&&(v=c),g||"html"!==u||(g=e),d?(m&&V("vxe.error.colRepet",["tree-node",d]),m||(m=e)):"expand"===u&&(p&&V("vxe.error.colRepet",["type",u]),p||(p=e)),"checkbox"===u?(f&&V("vxe.error.colRepet",["type",u]),f||(f=e)):"radio"===u&&(h&&V("vxe.error.colRepet",["type",u]),h||(h=e)),x&&!1===e.showOverflow&&(x=!1),n[i]&&B("vxe.error.colRepet",["colId",i]),n[i]=b};c?a().eachTree(o,((e,t,o,n,l,r)=>{e.level=r.length,b(e,t,o,n,l)})):t.forEach(b),p&&r.area&&B("vxe.error.errConflicts",["mouse-config.area","column.type=expand"]),g&&(i.useKey||B("vxe.error.reqProp",["column-config.useKey","column.type=html"]),s.useKey||B("vxe.error.reqProp",["row-config.useKey","column.type=html"])),u.isGroup=c,u.treeNodeColumn=m,u.expandColumn=p,u.isAllOverflow=x},Ct=()=>{d.customHeight=xt("height"),d.customMinHeight=xt("minHeight"),d.customMaxHeight=xt("maxHeight")},yt=()=>{const t=C.value,o=y.value,n=T.value,l=o?o.$el:null,r=t?t.$el:null,i=n?n.$el:null;if(!l)return;let s=0;const c=40,p=l.clientWidth-1;let f=p,h=f/100;const{fit:g}=e,{columnStore:v}=u,{resizeList:x,pxMinList:b,pxList:w,scaleList:E,scaleMinList:S,autoList:k}=v;if(b.forEach((e=>{const t=a().toInteger(e.minWidth);s+=t,e.renderWidth=t})),S.forEach((e=>{const t=Math.floor(a().toInteger(e.minWidth)*h);s+=t,e.renderWidth=t})),E.forEach((e=>{const t=Math.floor(a().toInteger(e.width)*h);s+=t,e.renderWidth=t})),w.forEach((e=>{const t=a().toInteger(e.width);s+=t,e.renderWidth=t})),x.forEach((e=>{const t=a().toInteger(e.resizeWidth);s+=t,e.renderWidth=t})),f-=s,h=f>0?Math.floor(f/(S.length+b.length+k.length)):0,g?f>0&&S.concat(b).forEach((e=>{s+=h,e.renderWidth+=h})):h=c,k.forEach((e=>{const t=Math.max(h,c);e.renderWidth=t,s+=t})),g){const e=E.concat(S).concat(b).concat(k);let t=e.length-1;if(t>0){let o=p-s;if(o>0){while(o>0&&t>=0)o--,e[t--].renderWidth++;s=p}}}const R=l.offsetHeight,O=l.scrollHeight>l.clientHeight;let M=0;O&&(M=Math.max(l.offsetWidth-l.clientWidth,0)),u.scrollbarWidth=M,u.overflowY=O,d.tableWidth=s,d.tableHeight=R;let $=0;r&&($=r.clientHeight,(0,H.nextTick)((()=>{r&&l&&r.scrollLeft!==l.scrollLeft&&(r.scrollLeft=l.scrollLeft)}))),d.headerHeight=$;let I=!1,D=0,F=0;i?(D=i.offsetHeight,I=s>i.clientWidth,I&&(F=Math.max(D-i.clientHeight,0))):(I=s>p,I&&(F=Math.max(R-l.clientHeight,0))),d.footerHeight=D,u.overflowX=I,u.scrollbarHeight=F,Ct(),u.parentHeight=Math.max(d.headerHeight+D+20,m.getParentHeight()),I&&m.checkScrolling()},Tt=e=>{const{sortBy:t,sortType:o}=e;return n=>{let l;return l=t?a().isFunction(t)?t({row:n,column:e}):a().get(n,t):m.getCellLabel(n,e),o&&"auto"!==o?"number"===o?a().toNumber(l):"string"===o?a().toValueString(l):l:isNaN(l)?l:a().toNumber(l)}},Et=()=>{const{treeConfig:t}=e,{afterFullData:o,fullDataRowIdData:n,fullAllDataRowIdData:l}=d,{afterTreeFullData:r}=d,i=je.value,s=i.children||i.childrenField,c={};t?a().eachTree(r,((e,t,o,r)=>{const a=Le(at,e),i=l[a],s=r.map(((e,t)=>t%2===0?Number(e)+1:".")).join("");if(i)i.seq=s,i._index=t;else{const o={row:e,rowid:a,seq:s,index:-1,$index:-1,_index:t,items:[],parent:null,level:0};l[a]=o,n[a]=o}c[a]=e}),{children:i.transform?i.mapChildrenField:s}):o.forEach(((e,t)=>{const o=Le(at,e),r=l[o],a=t+1;if(r)r.seq=a,r._index=t;else{const r={row:e,rowid:o,seq:a,index:-1,$index:-1,_index:t,items:[],parent:null,level:0};l[o]=r,n[o]=r}c[o]=e})),d.afterFullRowMaps=c},St=()=>{const{treeConfig:t}=e,{treeExpandedMaps:o}=u,n=je.value;if(t&&n.transform){const e=[],t={};return a().eachTree(d.afterTreeFullData,((n,l,r,a,i)=>{const s=Le(at,n),c=Le(at,i);(!i||t[c]&&o[c])&&(t[s]=1,e.push(n))}),{children:n.mapChildrenField}),d.afterFullData=e,no(e),e}return d.afterFullData},kt=()=>{const{treeConfig:t}=e,{tableFullColumn:o,tableFullData:n,tableFullTreeData:l}=d,r=ce.value,i=se.value,s=je.value,{transform:c}=s,{remote:u,filterMethod:p}=r,{remote:m,sortMethod:f,multiple:h,chronological:g}=i;let v=[],x=[];if(u&&m)t&&c?(x=a().searchTree(l,(()=>!0),{...s,original:!0}),v=x):(v=t?l.slice(0):n.slice(0),x=v);else{const e=[];let r=[];if(o.forEach((t=>{const{field:o,sortable:n,order:l,filters:a}=t;if(!u&&a&&a.length){const o=[],n=[];a.forEach((e=>{e.checked&&(n.push(e),o.push(e.value))})),n.length&&e.push({column:t,valueList:o,itemList:n})}!m&&n&&l&&r.push({column:t,field:o,property:o,order:l,sortTime:t.sortTime})})),h&&g&&r.length>1&&(r=a().orderBy(r,"sortTime")),!u&&e.length){const o=t=>e.every((({column:e,valueList:o,itemList:n})=>{const{filterMethod:l,filterRender:r}=e,i=r?No.renderer.get(r.name):null,s=i?i.filterMethod:null,c=i?i.defaultFilterMethod:null,u=ze(t,e);return l?n.some((o=>l({value:o.value,option:o,cellValue:u,row:t,column:e,$table:at}))):s?n.some((o=>s({value:o.value,option:o,cellValue:u,row:t,column:e,$table:at}))):p?p({options:n,values:o,cellValue:u,row:t,column:e}):c?n.some((o=>c({value:o.value,option:o,cellValue:u,row:t,column:e,$table:at}))):o.indexOf(a().get(t,e.field))>-1}));t&&c?(x=a().searchTree(l,o,{...s,original:!0}),v=x):(v=t?l.filter(o):n.filter(o),x=v)}else t&&c?(x=a().searchTree(l,(()=>!0),{...s,original:!0}),v=x):(v=t?l.slice(0):n.slice(0),x=v);if(!m&&r.length)if(t&&c){if(f){const e=f({data:x,sortList:r,$table:at});x=a().isArray(e)?e:x}else x=a().orderBy(x,r.map((({column:e,order:t})=>[Tt(e),t])));v=x}else{if(f){const e=f({data:v,sortList:r,$table:at});v=a().isArray(e)?e:v}else v=a().orderBy(v,r.map((({column:e,order:t})=>[Tt(e),t])));x=v}}d.afterFullData=v,d.afterTreeFullData=x,Et()},Rt=()=>{const{border:t,showFooter:o,showOverflow:n,showHeaderOverflow:l,showFooterOverflow:r,mouseConfig:i,spanMethod:s,footerSpanMethod:c,keyboardConfig:m}=e,{isGroup:f,currentRow:h,tableColumn:g,scrollXLoad:v,scrollYLoad:x,scrollbarWidth:b,scrollbarHeight:w,columnStore:C,editStore:y,mergeList:T,mergeFooterList:E,isAllOverflow:S}=u;let{visibleColumn:k,fullColumnIdData:R,tableHeight:O,tableWidth:M,headerHeight:D,footerHeight:N,elemStore:P,customHeight:L,customMinHeight:A,customMaxHeight:V}=d;const B=["main","left","right"],_=F.value,j=Ge.value,z=he.value,W=we.value,q=P["main-body-wrapper"],U=q?q.value:null;return _&&(_.style.top=`${D}px`,_.style.height=U?U.offsetHeight-w+"px":""),L>0&&o&&(L+=w),B.forEach(((e,i)=>{const d=i>0?e:"",h=["header","body","footer"],y="left"===d;let F,B=[];d&&(B=y?C.leftList:C.rightList,F=y?$.value:I.value),h.forEach((i=>{const h=P[`${e}-${i}-wrapper`],C=h?h.value:null,$=P[`${e}-${i}-table`],I=$?$.value:null;if("header"===i){let o=M,n=g;f?n=k:d&&(v||l)&&(n=B),o=n.reduce(((e,t)=>e+t.renderWidth),0),I&&(I.style.width=o?`${o+b}px`:"");const r=P[`${e}-${i}-repair`],s=r?r.value:null;s&&(s.style.width=`${M}px`);const c=P[`${e}-${i}-list`],u=c?c.value:null;f&&u&&a().arrayEach(u.querySelectorAll(".col--group"),(e=>{const o=p.getColumnNode(e);if(o){const n=o.item,{showHeaderOverflow:r}=n,i=a().isBoolean(r)?r:l,s="ellipsis"===i,c="title"===i,u=!0===i||"tooltip"===i,d=c||u||s;let p=0,m=0;d&&a().eachTree(n.children,(e=>{e.children&&n.children.length||m++,p+=e.renderWidth}),{children:"children"}),e.style.width=d?p-m-(t?2:0)+"px":""}}))}else if("body"===i){const t=P[`${e}-${i}-emptyBlock`],l=t?t.value:null;if(Oe(C)){let e=0;const t=A-D-N;if(V&&(e=V-D-N,d&&(e-=o?0:w),e=Math.max(t,e),C.style.maxHeight=`${e}px`),L){let n=L-D-N;d&&(n-=o?0:w),e&&(n=Math.min(e,n)),C.style.height=`${Math.max(t,n)}px`}else C.style.height="";C.style.minHeight=`${t}px`}F&&(Oe(C)&&(C.style.top=`${D}px`),F.style.height=(L>0?L-D-N:O)+D+N-w*(o?2:1)+"px",F.style.width=`${B.reduce(((e,t)=>e+t.renderWidth),y?0:b)}px`);let r=M,a=g;d&&(a=u.expandColumn||!x&&!(n?S:n)||T.length||s||m&&W.isMerge?k:B),r=a.reduce(((e,t)=>e+t.renderWidth),0),I&&(I.style.width=r?`${r}px`:"",I.style.paddingRight=b&&d&&(ae["-moz"]||ae.safari)?`${b}px`:""),l&&(l.style.width=r?`${r}px`:"")}else if("footer"===i){let e=M,t=g;d&&(t=u.expandColumn||!v&&!r||E.length&&c?k:B),e=t.reduce(((e,t)=>e+t.renderWidth),0),Oe(C)&&(F&&(C.style.top=`${L>0?L-N:O+D}px`),C.style.marginTop=-Math.max(1,w)+"px"),I&&(I.style.width=e?`${e+b}px`:"")}const _=P[`${e}-${i}-colgroup`],H=_?_.value:null;H&&a().arrayEach(H.children,(t=>{const o=t.getAttribute("name");if("col_gutter"===o&&(t.style.width=`${b}px`),R[o]){const s=R[o].column,{showHeaderOverflow:c,showFooterOverflow:u,showOverflow:d}=s;let m;t.style.width=`${s.renderWidth}px`,m="header"===i?a().isUndefined(c)||a().isNull(c)?l:c:"footer"===i?a().isUndefined(u)||a().isNull(u)?r:u:a().isUndefined(d)||a().isNull(d)?n:d;const f="ellipsis"===m,h="title"===m,g=!0===m||"tooltip"===m;let v=h||g||f;const b=P[`${e}-${i}-list`],w=b?b.value:null;x&&!v&&(v=!0),w&&a().arrayEach(w.querySelectorAll(`.${s.id}`),(e=>{const t=parseInt(e.getAttribute("colspan")||1),o=e.querySelector(".vxe-cell");let n=s.renderWidth;if(o){if(t>1){const e=p.getColumnIndex(s);for(let o=1;o<t;o++){const t=p.getColumns(e+o);t&&(n+=t.renderWidth)}}o.style.width=v?n-j*t+"px":""}}))}}))}))})),h&&p.setCurrentRow(h),i&&z.selected&&y.selected.row&&y.selected.column&&at.addCellSelectedClass(),(0,H.nextTick)()},Ot=e=>at.triggerValidate?at.triggerValidate(e):(0,H.nextTick)(),Mt=(e,t)=>{Ot("blur").catch((e=>e)).then((()=>{at.handleActived(t,e).then((()=>Ot("change"))).catch((e=>e))}))},$t=()=>{const{sortConfig:t}=e;if(t){const e=se.value;let{defaultSort:o}=e;o&&(a().isArray(o)||(o=[o]),o.length&&((t.multiple?o:o.slice(0,1)).forEach(((e,t)=>{const{field:o,order:n}=e;if(o&&n){const e=p.getColumnByField(o);e&&e.sortable&&(e.order=n,e.sortTime=Date.now()+t)}})),e.remote||m.handleTableData(!0).then(Rt)))}},It=()=>{const{checkboxConfig:t}=e;if(t){const{fullDataRowIdData:e}=d,t=K.value,{checkAll:o,checkRowKeys:n}=t;if(o)Vt(!0,!0);else if(n){const t=[];n.forEach((o=>{e[o]&&t.push(e[o].row)})),At(t,!0,!0)}}},Dt=()=>{const{radioConfig:t}=e;if(t){const{fullDataRowIdData:e}=d,t=X.value,{checkRowKey:o,reserve:n}=t;if(o&&(e[o]&&Lt(e[o].row,!0),n)){const e=Pe(at);d.radioReserveRow={[e]:o}}}},Ft=()=>{const{expandConfig:t}=e;if(t){const{fullDataRowIdData:e}=d,t=_e.value,{expandAll:o,expandRowKeys:n}=t;if(o)p.setAllRowExpand(!0);else if(n){const t=[];n.forEach((o=>{e[o]&&t.push(e[o].row)})),p.setRowExpand(t,!0)}}},Nt=e=>{const t=X.value;t.reserve&&(d.radioReserveRow=e)},Pt=(e,t)=>{const{checkboxReserveRowMap:o}=d,n=K.value;if(n.reserve){const n=Le(at,e);t?o[n]=e:o[n]&&delete o[n]}},Lt=(e,t)=>{const o=X.value,{checkMethod:n}=o;return e&&(t||!n||n({row:e}))&&(u.selectRadioRow=e,Nt(e)),(0,H.nextTick)()},At=(e,t,o)=>(e&&!a().isArray(e)&&(e=[e]),e.forEach((e=>m.handleSelectRow({row:e},!!t,o))),(0,H.nextTick)()),Vt=(t,o)=>{const{treeConfig:n}=e,{selectCheckboxMaps:l}=u,{afterFullData:r,afterFullRowMaps:i,checkboxReserveRowMap:s}=d,c=je.value,p=c.children||c.childrenField,f=K.value,{checkField:h,reserve:g,checkStrictly:v,checkMethod:x}=f,b=f.indeterminateField||f.halfField,w={};if(n||a().each(l,((e,t)=>{i[t]||(w[t]=e)})),v)u.isAllSelected=t;else{if(h){const e=e=>{(o||!x||x({row:e}))&&(t&&(w[Le(at,e)]=e),a().set(e,h,t)),n&&b&&a().set(e,b,!1)};n?a().eachTree(r,e,{children:p}):r.forEach(e)}else n?t?a().eachTree(r,(e=>{(o||!x||x({row:e}))&&(w[Le(at,e)]=e)}),{children:p}):!o&&x&&a().eachTree(r,(e=>{const t=Le(at,e);!x({row:e})&&l[t]&&(w[t]=e)}),{children:p}):t?!o&&x?r.forEach((e=>{const t=Le(at,e);(l[t]||x({row:e}))&&(w[t]=e)})):r.forEach((e=>{w[Le(at,e)]=e})):!o&&x&&r.forEach((e=>{const t=Le(at,e);!x({row:e})&&l[t]&&(w[t]=e)}));g&&(t?a().each(w,((e,t)=>{s[t]=e})):r.forEach((e=>Pt(e,!1)))),u.selectCheckboxMaps=h?{}:w}return u.treeIndeterminateMaps={},d.treeIndeterminateRowMaps={},m.checkSelectionStatus(),(0,H.nextTick)()},Bt=()=>{const{treeConfig:t}=e,{expandColumn:o,currentRow:n,selectCheckboxMaps:l,selectRadioRow:r,rowExpandedMaps:a,treeExpandedMaps:i}=u,{fullDataRowIdData:s,fullAllDataRowIdData:c,radioReserveRow:m}=d,f=_e.value,h=je.value,g=X.value,v=K.value;if(r&&!c[Le(at,r)]&&(u.selectRadioRow=null),g.reserve&&m){const e=Le(at,m);s[e]&&Lt(s[e].row,!0)}u.selectCheckboxMaps=ut(l),v.reserve&&At(dt(d.checkboxReserveRowMap),!0,!0),n&&!c[Le(at,n)]&&(u.currentRow=null),u.rowExpandedMaps=o?ut(a):{},o&&f.reserve&&p.setRowExpand(dt(d.rowExpandedReserveRowMap),!0),u.treeExpandedMaps=t?ut(i):{},t&&h.reserve&&p.setTreeExpand(dt(d.treeExpandedReserveRowMap),!0)},_t=()=>{const{treeConfig:t}=e;if(t){const{tableFullData:e}=d,t=je.value,{expandAll:o,expandRowKeys:n}=t,l=t.children||t.childrenField;if(o)p.setAllTreeExpand(!0);else if(n){const t=[],o=Pe(at);n.forEach((n=>{const r=a().findTree(e,(e=>n===a().get(e,o)),{children:l});r&&t.push(r.item)})),p.setTreeExpand(t,!0)}}},jt=e=>{const t=je.value,o=K.value,{transform:n,loadMethod:l}=t,{checkStrictly:r}=o;return new Promise((t=>{if(l){const{treeExpandLazyLoadedMaps:o}=u,{fullAllDataRowIdData:i}=d,s=Le(at,e),c=i[s];o[s]=e,l({$table:at,row:e}).then((t=>{if(c.treeLoaded=!0,o[s]&&delete o[s],a().isArray(t)||(t=[]),t)return p.loadTreeChildren(e,t).then((t=>{const{treeExpandedMaps:o}=u;return t.length&&!o[s]&&(o[s]=e),!r&&p.isCheckedByCheckboxRow(e)&&At(t,!0),(0,H.nextTick)().then((()=>{if(n)return m.handleTableData()}))}))})).catch((()=>{const{treeExpandLazyLoadedMaps:e}=u;c.treeLoaded=!1,e[s]&&delete e[s]})).finally((()=>{(0,H.nextTick)().then((()=>p.recalculate())).then((()=>t()))}))}else t()}))},Ht=(e,t)=>{const{treeExpandedReserveRowMap:o}=d,n=je.value;if(n.reserve){const n=Le(at,e);t?o[n]=e:o[n]&&delete o[n]}},zt=e=>new Promise((t=>{const o=_e.value,{loadMethod:n}=o;if(n){const{fullAllDataRowIdData:o}=d,{rowExpandLazyLoadedMaps:l}=u,r=Le(at,e),a=o[r];l[r]=e,n({$table:at,row:e,rowIndex:p.getRowIndex(e),$rowIndex:p.getVMRowIndex(e)}).then((()=>{const{rowExpandedMaps:t}=u;a.expandLoaded=!0,t[r]=e})).catch((()=>{a.expandLoaded=!1})).finally((()=>{const{rowExpandLazyLoadedMaps:e}=u;e[r]&&delete e[r],(0,H.nextTick)().then((()=>p.recalculate())).then((()=>t()))}))}else t()})),Wt=(e,t)=>{const{rowExpandedReserveRowMap:o}=d,n=_e.value;if(n.reserve){const n=Le(at,e);t?o[n]=e:o[n]&&delete o[n]}},qt=()=>{const{mergeCells:t}=e;t&&p.setMergeCells(t)},Ut=()=>{const{mergeFooterItems:t}=e;t&&p.setMergeFooterItems(t)},Gt=()=>(0,H.nextTick)().then((()=>{const{scrollXLoad:e,scrollYLoad:t}=u,{scrollXStore:o,scrollYStore:n}=d,l=_.value,r=A.value;if(e){const{visibleSize:e}=pt(),t=r.oSize?a().toNumber(r.oSize):ae.edge?5:0;o.offsetSize=t,o.visibleSize=e,o.endIndex=Math.max(o.startIndex+o.visibleSize+t,o.endIndex),m.updateScrollXData()}else m.updateScrollXSpace();const{rowHeight:i,visibleSize:s}=mt();if(n.rowHeight=i,t){const e=l.oSize?a().toNumber(l.oSize):ae.edge?10:0;n.offsetSize=e,n.visibleSize=s,n.endIndex=Math.max(n.startIndex+s+e,n.endIndex),m.updateScrollYData()}else m.updateScrollYSpace();u.rowHeight=i,(0,H.nextTick)(Rt)})),Xt=t=>{const{keepSource:o,treeConfig:n}=e,{editStore:l,scrollYLoad:r}=u,{scrollYStore:i,scrollXStore:s,lastScrollLeft:c,lastScrollTop:f}=d,h=je.value,{transform:g}=h,v=h.children||h.childrenField;let x=[],b=(0,H.reactive)(t?t.slice(0):[]);n&&(g?(h.rowField||B("vxe.error.reqProp",["tree-config.rowField"]),h.parentField||B("vxe.error.reqProp",["tree-config.parentField"]),v||B("vxe.error.reqProp",["tree-config.childrenField"]),h.mapChildrenField||B("vxe.error.reqProp",["tree-config.mapChildrenField"]),v===h.mapChildrenField&&B("vxe.error.errConflicts",["tree-config.childrenField","tree-config.mapChildrenField"]),x=a().toArrayTree(b,{key:h.rowField,parentKey:h.parentField,children:v,mapChildren:h.mapChildrenField}),b=x.slice(0)):x=b.slice(0)),i.startIndex=0,i.endIndex=1,s.startIndex=0,s.endIndex=1,u.scrollVMLoading=!1,l.insertMaps={},l.removeMaps={};const w=no(b);return u.scrollYLoad=w,d.tableFullData=b,d.tableFullTreeData=x,m.cacheRowMap(!0),d.tableSynchData=t,o&&m.cacheSourceMap(b),w&&(e.height||e.maxHeight||B("vxe.error.reqProp",["table.height | table.max-height | table.scroll-y={enabled: false}"]),e.showOverflow||B("vxe.error.reqProp",["table.show-overflow"]),e.spanMethod&&V("vxe.error.scrollErrProp",["table.span-method"])),at.clearCellAreas&&e.mouseConfig&&(at.clearCellAreas(),at.clearCopyCellArea()),p.clearMergeCells(),p.clearMergeFooterItems(),m.handleTableData(!0),p.updateFooter(),(0,H.nextTick)().then((()=>{Ct(),Rt()})).then((()=>{Gt()})).then((()=>(w&&(i.endIndex=i.visibleSize),Bt(),m.checkSelectionStatus(),new Promise((e=>{(0,H.nextTick)().then((()=>p.recalculate())).then((()=>{let t=c,o=f;const n=A.value,l=_.value;n.scrollToLeftOnChange&&(t=0),l.scrollToTopOnChange&&(o=0),r===w?Ie(at,t,o).then(e):setTimeout((()=>Ie(at,t,o).then(e)))}))})))))},Yt=()=>{It(),Dt(),Ft(),_t(),qt(),Ut(),(0,H.nextTick)((()=>setTimeout((()=>p.recalculate()))))},Kt=()=>{$t()},Zt=()=>{const{scrollXLoad:e}=u,{visibleColumn:t,scrollXStore:o,fullColumnIdData:n}=d,l=e?t.slice(o.startIndex,o.endIndex):t.slice(0);l.forEach(((e,t)=>{const o=e.id,l=n[o];l&&(l.$index=t)})),u.tableColumn=l},Jt=()=>{const{mergeList:e,mergeFooterList:t}=u,{scrollXStore:o}=d,{startIndex:n,endIndex:l,offsetSize:r}=o,{toVisibleIndex:a,visibleSize:i}=pt(),s={startIndex:Math.max(0,a-1-r),endIndex:a+i+r};ft(e.concat(t),s,"col");const{startIndex:c,endIndex:f}=s;(a<=n||a>=l-i-1)&&(n===c&&l===f||(o.startIndex=c,o.endIndex=f,m.updateScrollXData())),p.closeTooltip()},Qt=e=>{const t=[];return e.forEach((e=>{t.push(...e.children&&e.children.length?Qt(e.children):[e])})),t},eo=()=>{const t=[],o=[],n=[],{isGroup:l,columnStore:r}=u,i=A.value,{collectColumn:s,tableFullColumn:c,scrollXStore:m,fullColumnIdData:f}=d;if(l){const e=[],l=[],r=[];a().eachTree(s,((e,l,r,i,s)=>{const c=ee(e);s&&s.fixed&&(e.fixed=s.fixed),s&&e.fixed!==s.fixed&&B("vxe.error.groupFixed"),c?e.visible=!!a().findTree(e.children,(e=>!ee(e)&&e.visible)):e.visible&&("left"===e.fixed?t.push(e):"right"===e.fixed?n.push(e):o.push(e))})),s.forEach((t=>{t.visible&&("left"===t.fixed?e.push(t):"right"===t.fixed?r.push(t):l.push(t))})),u.tableGroupColumn=e.concat(l).concat(r)}else c.forEach((e=>{e.visible&&("left"===e.fixed?t.push(e):"right"===e.fixed?n.push(e):o.push(e))}));const h=t.concat(o).concat(n),g=!!i.enabled&&i.gt>-1&&(0===i.gt||i.gt<c.length);if(u.hasFixedColumn=t.length>0||n.length>0,Object.assign(r,{leftList:t,centerList:o,rightList:n}),g){e.spanMethod&&V("vxe.error.scrollErrProp",["span-method"]),e.footerSpanMethod&&V("vxe.error.scrollErrProp",["footer-span-method"]);const{visibleSize:t}=pt();m.startIndex=0,m.endIndex=t,m.visibleSize=t}return h.length===d.visibleColumn.length&&d.visibleColumn.every(((e,t)=>e===h[t]))||(p.clearMergeCells(),p.clearMergeFooterItems()),u.scrollXLoad=g,h.forEach(((e,t)=>{const o=e.id,n=f[o];n&&(n._index=t)})),d.visibleColumn=h,Zt(),p.updateFooter().then((()=>p.recalculate())).then((()=>(p.updateCellAreas(),p.recalculate())))},to=()=>{const{collectColumn:e}=d;e.forEach(((e,t)=>{const o=t+1;e.sortNumber=o,e.renderSortNumber=o}))},oo=e=>{d.collectColumn=e;const t=Qt(e);return d.tableFullColumn=t,to(),bt(),wt(),eo().then((()=>{u.scrollXLoad&&Jt()})),p.clearMergeCells(),p.clearMergeFooterItems(),m.handleTableData(!0),(u.scrollXLoad||u.scrollYLoad)&&u.expandColumn&&V("vxe.error.scrollErrProp",["column.type=expand"]),(0,H.nextTick)().then((()=>(P&&P.syncUpdate({collectColumn:e,$table:at}),p.recalculate())))},no=t=>{const{treeConfig:o}=e,n=_.value,l=je.value,{transform:r}=l,a=t||d.tableFullData,i=(r||!o)&&!!n.enabled&&n.gt>-1&&(0===n.gt||n.gt<a.length);return u.scrollYLoad=i,i},lo=(e,t)=>{const{treeExpandedMaps:o,treeExpandLazyLoadedMaps:n,treeNodeColumn:l}=u,r={...o},{fullAllDataRowIdData:i,tableFullData:s}=d,c=je.value,{reserve:m,lazy:f,accordion:h,toggleMethod:g}=c,v=c.children||c.childrenField,x=c.hasChild||c.hasChildField,b=[],w=p.getColumnIndex(l),C=p.getVMColumnIndex(l);let y=g?e.filter((e=>g({$table:at,expanded:t,column:l,columnIndex:w,$columnIndex:C,row:e}))):e;if(h){y=y.length?[y[y.length-1]]:[];const e=a().findTree(s,(e=>e===y[0]),{children:v});e&&e.items.forEach((e=>{const t=Le(at,e);r[t]&&delete r[t]}))}return t?y.forEach((e=>{const t=Le(at,e);if(!r[t]){const o=i[t],l=f&&e[x]&&!o.treeLoaded&&!n[t];l?b.push(jt(e)):e[v]&&e[v].length&&(r[t]=e)}})):y.forEach((e=>{const t=Le(at,e);r[t]&&delete r[t]})),m&&y.forEach((e=>Ht(e,t))),u.treeExpandedMaps=r,Promise.all(b).then((()=>p.recalculate()))},ro=(e,t)=>lo(e,t).then((()=>(St(),m.handleTableData()))).then((()=>p.recalculate())),ao=e=>{const{mergeList:t}=u,{scrollYStore:o}=d,{startIndex:n,endIndex:l,visibleSize:r,offsetSize:a,rowHeight:i}=o,s=e.currentTarget||e.target,c=s.scrollTop,p=Math.floor(c/i),f={startIndex:Math.max(0,p-1-a),endIndex:p+r+a};ft(t,f,"row");const{startIndex:h,endIndex:g}=f;(p<=n||p>=l-r-1)&&(n===h&&l===g||(o.startIndex=h,o.endIndex=g,m.updateScrollYData()))},io=e=>function(t){const{fullAllDataRowIdData:o}=d;if(t){const n=Le(at,t),l=o[n];if(l)return l[e]}return-1},so=e=>function(t){const{fullColumnIdData:o}=d;if(t){const n=o[t.id];if(n)return n[e]}return-1},co=a().debounce((function(e){ao(e)}),20,{leading:!1,trailing:!0});let uo;p={dispatchEvent(e,t,o){n(e,Object.assign({$table:at,$grid:N,$event:o},t))},clearAll(){return et(at)},syncData(){return V("vxe.error.delFunc",["syncData","getData"]),(0,H.nextTick)().then((()=>(u.tableData=[],n("update:data",d.tableFullData),(0,H.nextTick)())))},updateData(){const{scrollXLoad:e,scrollYLoad:t}=u;return m.handleTableData(!0).then((()=>{if(p.updateFooter(),e||t)return e&&m.updateScrollXSpace(),t&&m.updateScrollYSpace(),p.refreshScroll()})).then((()=>(p.updateCellAreas(),p.recalculate(!0)))).then((()=>{setTimeout((()=>at.recalculate()),50)}))},loadData(e){const{inited:t,initStatus:o}=d;return Xt(e).then((()=>(d.inited=!0,d.initStatus=!0,o||Yt(),t||Kt(),p.recalculate())))},reloadData(e){const{inited:t}=d;return p.clearAll().then((()=>(d.inited=!0,d.initStatus=!0,Xt(e)))).then((()=>(Yt(),t||Kt(),p.recalculate())))},setRow(e,t){if(t){let o=e;a().isArray(e)||(o=[e]),o.forEach((e=>Object.assign(e,t)))}return(0,H.nextTick)()},reloadRow(t,o,n){const{keepSource:l}=e,{tableData:r}=u,{tableSourceData:i}=d;if(l){const e=p.getRowIndex(t),l=i[e];if(l&&t)if(n){const e=a().get(o||t,n);a().set(t,n,e),a().set(l,n,e)}else{const e=a().clone({...o},!0);a().destructuring(l,Object.assign(t,e))}u.tableData=r.slice(0)}else V("vxe.error.reqProp",["keep-source"]);return(0,H.nextTick)()},loadTreeChildren(t,o){const{keepSource:n}=e,{tableSourceData:l,fullDataRowIdData:r,fullAllDataRowIdData:i,sourceDataRowIdData:s}=d,c=je.value,{transform:u,mapChildrenField:m}=c,f=c.children||c.childrenField,h=i[Le(at,t)],g=h?h.level:0;return p.createData(o).then((e=>{if(n){const o=Le(at,t),n=a().findTree(l,(e=>o===Le(at,e)),{children:f});n&&(n.item[f]=a().clone(e,!0)),e.forEach((e=>{const t=Le(at,e);s[t]=a().clone(e,!0)}))}return a().eachTree(e,((e,t,o,n,l,a)=>{const s=Le(at,e),c=l||h.row,u={row:e,rowid:s,seq:-1,index:t,_index:-1,$index:-1,items:o,parent:c,level:g+a.length};r[s]=u,i[s]=u}),{children:f}),t[f]=e,u&&(t[m]=e),Et(),e}))},loadColumn(e){const t=a().mapTree(e,(e=>(0,H.reactive)(Zl.createColumn(at,e))));return oo(t)},reloadColumn(e){return p.clearAll().then((()=>p.loadColumn(e)))},getRowNode(e){if(e){const{fullAllDataRowIdData:t}=d,o=e.getAttribute("rowid");if(o){const e=t[o];if(e)return{rowid:e.rowid,item:e.row,index:e.index,items:e.items,parent:e.parent}}}return null},getColumnNode(e){if(e){const{fullColumnIdData:t}=d,o=e.getAttribute("colid");if(o){const e=t[o];if(e)return{colid:e.colid,item:e.column,index:e.index,items:e.items,parent:e.parent}}}return null},getRowSeq:io("seq"),getRowIndex:io("index"),getVTRowIndex:io("_index"),getVMRowIndex:io("$index"),getColumnIndex:so("index"),getVTColumnIndex:so("_index"),getVMColumnIndex:so("$index"),createData(e){return(0,H.nextTick)().then((()=>(0,H.reactive)(m.defineField(e))))},createRow(e){const t=a().isArray(e);return t||(e=[e||{}]),p.createData(e).then((e=>t?e:e[0]))},revertData(t,o){const{keepSource:n}=e,{tableSourceData:l,sourceDataRowIdData:r}=d;if(!n)return V("vxe.error.reqProp",["keep-source"]),(0,H.nextTick)();let i=t;return t?a().isArray(t)||(i=[t]):i=a().toArray(at.getUpdateRecords()),i.length&&i.forEach((e=>{if(!p.isInsertByRow(e)){const t=Le(at,e),n=r[t];n&&e&&(o?a().set(e,o,a().clone(a().get(n,o),!0)):a().destructuring(e,a().clone(n,!0)))}})),t?(0,H.nextTick)():p.reloadData(l)},clearData(e,t){const{tableFullData:o,visibleColumn:n}=d;return arguments.length?e&&!a().isArray(e)&&(e=[e]):e=o,t?e.forEach((e=>a().set(e,t,null))):e.forEach((e=>{n.forEach((t=>{t.field&&We(e,t,null)}))})),(0,H.nextTick)()},isInsertByRow(e){const{editStore:t}=u,o=Le(at,e);return t.insertMaps[o]},removeInsertRow(){const{editStore:e}=u;return e.insertMaps={},at.remove(at.getInsertRecords())},isUpdateByRow(t,o){const{keepSource:n}=e,{tableFullColumn:l,fullDataRowIdData:r,sourceDataRowIdData:a}=d;if(n){const e=Le(at,t);if(!r[e])return!1;const n=a[e];if(n){if(arguments.length>1)return!it(n,t,o);for(let e=0,o=l.length;e<o;e++){const o=l[e].field;if(o&&!it(n,t,o))return!0}}}return!1},getColumns(e){const t=d.visibleColumn;return a().isUndefined(e)?t.slice(0):t[e]},getColumnById(e){const t=d.fullColumnIdData;return e&&t[e]?t[e].column:null},getColumnByField(e){const t=d.fullColumnFieldData;return e&&t[e]?t[e].column:null},getTableColumn(){return{collectColumn:d.collectColumn.slice(0),fullColumn:d.tableFullColumn.slice(0),visibleColumn:d.visibleColumn.slice(0),tableColumn:u.tableColumn.slice(0)}},getData(t){const o=e.data||d.tableSynchData;return a().isUndefined(t)?o.slice(0):o[t]},getCheckboxRecords(t){const{treeConfig:o}=e,{tableFullData:n,afterFullData:l,afterTreeFullData:r,tableFullTreeData:i,fullDataRowIdData:s,afterFullRowMaps:c}=d,p=je.value,m=K.value,{transform:f,mapChildrenField:h}=p,{checkField:g}=m,v=p.children||p.childrenField;let x=[];const b=t?f?i:n:f?r:l;if(g)x=o?a().filterTree(b,(e=>a().get(e,g)),{children:f?h:v}):b.filter((e=>a().get(e,g)));else{const{selectCheckboxMaps:e}=u;a().each(e,((e,o)=>{t?s[o]&&x.push(s[o].row):c[o]&&x.push(c[o])}))}return x},getParentRow(t){const{treeConfig:o}=e,{fullDataRowIdData:n}=d;if(t&&o){let e;if(e=a().isString(t)?t:Le(at,t),e){const t=n[e];return t?t.parent:null}}return null},getRowById(e){const{fullDataRowIdData:t}=d,o=a().eqNull(e)?"":encodeURIComponent(e||"");return t[o]?t[o].row:null},getRowid(e){return Le(at,e)},getTableData(){const{tableData:t,footerTableData:o}=u,{tableFullData:n,afterFullData:l,tableFullTreeData:r}=d;return{fullData:e.treeConfig?r.slice(0):n.slice(0),visibleData:l.slice(0),tableData:t.slice(0),footerData:o.slice(0)}},setColumnFixed(e,t){const o=Ae(at,e),n=Ze(at,o),l=Ke.value,r=z.value,{maxFixedSize:i}=r;return n&&n.fixed!==t?!n.fixed&&l?(No.modal&&No.modal.message({status:"error",content:c.i18n("vxe.table.maxFixedCol",[i])}),(0,H.nextTick)()):(a().eachTree([n],(e=>{e.fixed=t})),m.saveCustomFixed(),p.refreshColumn()):(0,H.nextTick)()},clearColumnFixed(e){const t=Ae(at,e),o=Ze(at,t);return o&&o.fixed?(a().eachTree([o],(e=>{e.fixed=null})),m.saveCustomFixed(),p.refreshColumn()):(0,H.nextTick)()},hideColumn(e){const t=Ae(at,e);return t&&t.visible?(t.visible=!1,m.handleCustom()):(0,H.nextTick)()},showColumn(e){const t=Ae(at,e);return t&&!t.visible?(t.visible=!0,m.handleCustom()):(0,H.nextTick)()},setColumnWidth(e,t){const o=Ae(at,e);if(o){const e=a().toInteger(t);let n=e;if(de(t)){const t=y.value,o=t?t.$el:null,l=o?o.clientWidth-1:0;n=Math.floor(e*l)}o.renderWidth=n}return(0,H.nextTick)()},getColumnWidth(e){const t=Ae(at,e);return t?t.renderWidth:0},resetCustom(e){const{collectColumn:t}=d,o=Xe.value,{checkMethod:n}=o,l=Object.assign({visible:!0,resizable:!0===e,fixed:!0===e,sort:!0===e},e);return a().eachTree(t,(e=>{l.resizable&&(e.resizeWidth=0),l.fixed&&(e.fixed=e.defaultFixed),l.sort&&(e.renderSortNumber=e.sortNumber),n&&!n({column:e})||(e.visible=e.defaultVisible)})),l.resizable&&m.saveCustomResizable(!0),l.sort&&m.saveCustomSort(!0),l.fixed&&m.saveCustomFixed(),m.handleCustom()},resetColumn(e){return V("vxe.error.delFunc",["resetColumn","resetCustom"]),at.resetCustom(e)},refreshColumn(e){if(e){const e=a().orderBy(d.collectColumn,"renderSortNumber");d.collectColumn=e;const t=Qt(e);d.tableFullColumn=t,wt()}return eo().then((()=>p.refreshScroll())).then((()=>p.recalculate()))},refreshScroll(){const{lastScrollLeft:e,lastScrollTop:t}=d,o=y.value,n=T.value,l=S.value,r=O.value,a=o?o.$el:null,i=l?l.$el:null,s=r?r.$el:null,c=n?n.$el:null;return new Promise((o=>{if(e||t)return Ie(at,e,t).then().then((()=>{setTimeout(o,30)}));xe(a,t),xe(i,t),xe(s,t),be(c,e),setTimeout(o,30)}))},recalculate(e){return yt(),!0===e?Gt().then((()=>(yt(),Gt()))):Gt()},openTooltip(e,t){const o=g.value;return o?o.open(e,t):(0,H.nextTick)()},closeTooltip(){const{tooltipStore:e}=u,t=h.value,o=g.value;return e.visible&&(Object.assign(e,{row:null,column:null,content:null,visible:!1}),t&&t.close()),o&&o.close(),(0,H.nextTick)()},isAllCheckboxChecked(){return u.isAllSelected},isAllCheckboxIndeterminate(){return!u.isAllSelected&&u.isIndeterminate},getCheckboxIndeterminateRecords(t){const{treeConfig:o}=e,{fullDataRowIdData:n}=d,{treeIndeterminateMaps:l}=u;if(o){const e=[],o=[];return a().each(l,((t,l)=>{t&&(e.push(t),n[l]&&o.push(t))})),t?e:o}return[]},setCheckboxRow(e,t){return At(e,t,!0)},isCheckedByCheckboxRow(e){const{selectCheckboxMaps:t}=u,o=K.value,{checkField:n}=o;return n?a().get(e,n):!!t[Le(at,e)]},isIndeterminateByCheckboxRow(e){const{treeIndeterminateMaps:t}=u;return!!t[Le(at,e)]&&!p.isCheckedByCheckboxRow(e)},toggleCheckboxRow(e){const{selectCheckboxMaps:t}=u,o=K.value,{checkField:n}=o,l=n?!a().get(e,n):!t[Le(at,e)];return m.handleSelectRow({row:e},l,!0),(0,H.nextTick)()},setAllCheckboxRow(e){return Vt(e,!0)},getRadioReserveRecord(t){const{treeConfig:o}=e,{fullDataRowIdData:n,radioReserveRow:l,afterFullData:r}=d,i=X.value,s=je.value,c=s.children||s.childrenField;if(i.reserve&&l){const e=Le(at,l);if(t){if(!n[e])return l}else{const t=Pe(at);if(o){const o=a().findTree(r,(o=>e===a().get(o,t)),{children:c});if(o)return l}else if(!r.some((o=>e===a().get(o,t))))return l}}return null},clearRadioReserve(){return d.radioReserveRow=null,(0,H.nextTick)()},getCheckboxReserveRecords(t){const{treeConfig:o}=e,{afterFullData:n,fullDataRowIdData:l,checkboxReserveRowMap:r}=d,i=K.value,s=je.value,c=s.children||s.childrenField,u=[];if(i.reserve){const e={};o?a().eachTree(n,(t=>{e[Le(at,t)]=1}),{children:c}):n.forEach((t=>{e[Le(at,t)]=1})),a().each(r,((o,n)=>{o&&(t?l[n]||u.push(o):e[n]||u.push(o))}))}return u},clearCheckboxReserve(){return d.checkboxReserveRowMap={},(0,H.nextTick)()},toggleAllCheckboxRow(){return m.triggerCheckAllEvent(null,!u.isAllSelected),(0,H.nextTick)()},clearCheckboxRow(){const{treeConfig:t}=e,{tableFullData:o}=d,n=je.value,l=n.children||n.childrenField,r=K.value,{checkField:i,reserve:s}=r,c=r.indeterminateField||r.halfField;if(i){const e=e=>{t&&c&&a().set(e,c,!1),a().set(e,i,!1)};t?a().eachTree(o,e,{children:l}):o.forEach(e)}return s&&o.forEach((e=>Pt(e,!1))),u.isAllSelected=!1,u.isIndeterminate=!1,u.selectCheckboxMaps={},u.treeIndeterminateMaps={},(0,H.nextTick)()},setCurrentRow(t){const o=W.value,n=f.value;return p.clearCurrentRow(),u.currentRow=t,(o.isCurrent||e.highlightCurrentRow)&&n&&a().arrayEach(n.querySelectorAll(`[rowid="${Le(at,t)}"]`),(e=>fe(e,"row--current"))),(0,H.nextTick)()},isCheckedByRadioRow(e){return at.eqRow(u.selectRadioRow,e)},setRadioRow(e){return Lt(e,!0)},clearCurrentRow(){const e=f.value;return u.currentRow=null,d.hoverRow=null,e&&a().arrayEach(e.querySelectorAll(".row--current"),(e=>me(e,"row--current"))),(0,H.nextTick)()},clearRadioRow(){return u.selectRadioRow=null,(0,H.nextTick)()},getCurrentRecord(){const t=W.value;return t.isCurrent||e.highlightCurrentRow?u.currentRow:null},getRadioRecord(e){const{fullDataRowIdData:t,afterFullRowMaps:o}=d,{selectRadioRow:n}=u;if(n){const l=Le(at,n);if(e){if(t[l])return n}else if(o[l])return n}return null},getCurrentColumn(){const t=z.value;return t.isCurrent||e.highlightCurrentColumn?u.currentColumn:null},setCurrentColumn(e){const t=Ae(at,e);return t&&(p.clearCurrentColumn(),u.currentColumn=t),(0,H.nextTick)()},clearCurrentColumn(){return u.currentColumn=null,(0,H.nextTick)()},setPendingRow(e,t){const o={...u.pendingRowMaps},n=[...u.pendingRowList];return e&&!a().isArray(e)&&(e=[e]),t?e.forEach((e=>{const t=Le(at,e);t&&!o[t]&&(n.push(e),o[t]=e)})):e.forEach((e=>{const t=Le(at,e);if(t&&o[t]){const l=at.findRowIndexOf(n,e);l>-1&&n.splice(l,1),delete o[t]}})),u.pendingRowMaps=o,u.pendingRowList=n,(0,H.nextTick)()},togglePendingRow(e){const t={...u.pendingRowMaps},o=[...u.pendingRowList];return e&&!a().isArray(e)&&(e=[e]),e.forEach((e=>{const n=Le(at,e);if(n)if(t[n]){const l=at.findRowIndexOf(o,e);l>-1&&o.splice(l,1),delete t[n]}else o.push(e),t[n]=e})),u.pendingRowMaps=t,u.pendingRowList=o,(0,H.nextTick)()},hasPendingByRow(e){const{pendingRowMaps:t}=u,o=Le(at,e);return!!t[o]},getPendingRecords(){const{pendingRowList:e}=u;return e.slice(0)},clearPendingRow(){return u.pendingRowMaps={},u.pendingRowList=[],(0,H.nextTick)()},sort(e,t){const o=se.value,{multiple:n,remote:l,orders:r}=o;return e&&a().isString(e)&&(e=[{field:e,order:t}]),a().isArray(e)||(e=[e]),e.length?(n||vt(),(n?e:[e[0]]).forEach(((e,t)=>{let{field:o,order:n}=e,l=o;a().isString(o)&&(l=p.getColumnByField(o)),l&&l.sortable&&(-1===r.indexOf(n)&&(n=st(l)),l.order!==n&&(l.order=n),l.sortTime=Date.now()+t)})),l||m.handleTableData(!0),(0,H.nextTick)().then((()=>(p.updateCellAreas(),Rt())))):(0,H.nextTick)()},clearSort(e){const t=se.value;if(e){const t=Ae(at,e);t&&(t.order=null)}else vt();return t.remote||m.handleTableData(!0),(0,H.nextTick)().then(Rt)},isSort(e){if(e){const t=Ae(at,e);return!!t&&(t.sortable&&!!t.order)}return p.getSortColumns().length>0},getSortColumns(){const e=se.value,{multiple:t,chronological:o}=e,n=[],{tableFullColumn:l}=d;return l.forEach((e=>{const{field:t,order:o}=e;e.sortable&&o&&n.push({column:e,field:t,property:t,order:o,sortTime:e.sortTime})})),t&&o&&n.length>1?a().orderBy(n,"sortTime"):n},closeFilter(){const{filterStore:e}=u,{column:t,visible:o}=e;return Object.assign(e,{isAllSelected:!1,isIndeterminate:!1,options:[],visible:!1}),o&&at.dispatchEvent("filter-visible",{column:t,property:t.field,field:t.field,filterList:at.getCheckedFilters(),visible:!1},null),(0,H.nextTick)()},isActiveFilterByColumn(e){const t=Ae(at,e);return t?t.filters&&t.filters.some((e=>e.checked)):at.getCheckedFilters().length>0},isFilter(e){return p.isActiveFilterByColumn(e)},isRowExpandLoaded(e){const{fullAllDataRowIdData:t}=d,o=t[Le(at,e)];return o&&!!o.expandLoaded},clearRowExpandLoaded(e){const{rowExpandLazyLoadedMaps:t}=u,{fullAllDataRowIdData:o}=d,n=_e.value,{lazy:l}=n,r=Le(at,e),a=o[r];return l&&a&&(a.expandLoaded=!1,delete t[r]),(0,H.nextTick)()},reloadRowExpand(e){const{rowExpandLazyLoadedMaps:t}=u,o=_e.value,{lazy:n}=o,l=Le(at,e);return n&&!t[l]&&p.clearRowExpandLoaded(e).then((()=>zt(e))),(0,H.nextTick)()},reloadExpandContent(e){return V("vxe.error.delFunc",["reloadExpandContent","reloadRowExpand"]),p.reloadRowExpand(e)},toggleRowExpand(e){return p.setRowExpand(e,!p.isRowExpandByRow(e))},setAllRowExpand(t){const o=je.value,{tableFullData:n,tableFullTreeData:l}=d,r=o.children||o.childrenField;let i=[];return e.treeConfig?a().eachTree(l,(e=>{i.push(e)}),{children:r}):i=n,p.setRowExpand(i,t)},setRowExpand(e,t){const{rowExpandedMaps:o,rowExpandLazyLoadedMaps:n,expandColumn:l}=u,{fullAllDataRowIdData:r}=d;let i={...o};const s=_e.value,{reserve:c,lazy:m,accordion:f,toggleMethod:h}=s,g=[],v=p.getColumnIndex(l),x=p.getVMColumnIndex(l);if(e){a().isArray(e)||(e=[e]),f&&(i={},e=e.slice(e.length-1,e.length));const o=h?e.filter((e=>h({$table:at,expanded:t,column:l,columnIndex:v,$columnIndex:x,row:e,rowIndex:p.getRowIndex(e),$rowIndex:p.getVMRowIndex(e)}))):e;t?o.forEach((e=>{const t=Le(at,e);if(!i[t]){const o=r[t],l=m&&!o.expandLoaded&&!n[t];l?g.push(zt(e)):i[t]=e}})):o.forEach((e=>{const t=Le(at,e);i[t]&&delete i[t]})),c&&o.forEach((e=>Wt(e,t)))}return u.rowExpandedMaps=i,Promise.all(g).then((()=>p.recalculate()))},isRowExpandByRow(e){const{rowExpandedMaps:t}=u,o=Le(at,e);return!!t[o]},isExpandByRow(e){return V("vxe.error.delFunc",["isExpandByRow","isRowExpandByRow"]),p.isRowExpandByRow(e)},clearRowExpand(){const{tableFullData:e}=d,t=_e.value,{reserve:o}=t,n=p.getRowExpandRecords();return u.rowExpandedMaps={},o&&e.forEach((e=>Wt(e,!1))),(0,H.nextTick)().then((()=>{n.length&&p.recalculate()}))},clearRowExpandReserve(){return d.rowExpandedReserveRowMap={},(0,H.nextTick)()},getRowExpandRecords(){const e=[];return a().each(u.rowExpandedMaps,(t=>{t&&e.push(t)})),e},getTreeExpandRecords(){const e=[];return a().each(u.treeExpandedMaps,(t=>{t&&e.push(t)})),e},isTreeExpandLoaded(e){const{fullAllDataRowIdData:t}=d,o=t[Le(at,e)];return o&&!!o.treeLoaded},clearTreeExpandLoaded(e){const{treeExpandedMaps:t}=u,{fullAllDataRowIdData:o}=d,n=je.value,{transform:l,lazy:r}=n,a=Le(at,e),i=o[a];return r&&i&&(i.treeLoaded=!1,t[a]&&delete t[a]),l?(St(),m.handleTableData()):(0,H.nextTick)()},reloadTreeExpand(e){const{treeExpandLazyLoadedMaps:t}=u,o=je.value,n=o.hasChild||o.hasChildField,{transform:l,lazy:r}=o,a=Le(at,e);return r&&e[n]&&!t[a]&&p.clearTreeExpandLoaded(e).then((()=>jt(e))).then((()=>{if(l)return St(),m.handleTableData()})).then((()=>p.recalculate())),(0,H.nextTick)()},reloadTreeChilds(e){return V("vxe.error.delFunc",["reloadTreeChilds","reloadTreeExpand"]),p.reloadTreeExpand(e)},toggleTreeExpand(e){return p.setTreeExpand(e,!p.isTreeExpandByRow(e))},setAllTreeExpand(e){const{tableFullData:t}=d,o=je.value,{transform:n,lazy:l}=o,r=o.children||o.childrenField,i=[];return a().eachTree(t,(e=>{const t=e[r];(l||t&&t.length)&&i.push(e)}),{children:r}),p.setTreeExpand(i,e).then((()=>{if(n)return St(),p.recalculate()}))},setTreeExpand(e,t){const o=je.value,{transform:n}=o;return e&&(a().isArray(e)||(e=[e]),e.length)?n?ro(e,t):lo(e,t):(0,H.nextTick)()},isTreeExpandByRow(e){const{treeExpandedMaps:t}=u;return!!t[Le(at,e)]},clearTreeExpand(){const{tableFullTreeData:e}=d,t=je.value,o=t.children||t.childrenField,{transform:n,reserve:l}=t,r=p.getTreeExpandRecords();return u.treeExpandedMaps={},l&&a().eachTree(e,(e=>Ht(e,!1)),{children:o}),m.handleTableData().then((()=>{if(n)return St(),m.handleTableData()})).then((()=>{if(r.length)return p.recalculate()}))},clearTreeExpandReserve(){return d.treeExpandedReserveRowMap={},(0,H.nextTick)()},getScroll(){const{scrollXLoad:e,scrollYLoad:t}=u,o=y.value,n=o.$el;return{virtualX:e,virtualY:t,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}},scrollTo(e,t){const o=y.value,n=T.value,l=O.value,r=o?o.$el:null,i=l?l.$el:null,s=n?n.$el:null;return a().isNumber(e)&&be(s||r,e),a().isNumber(t)&&xe(i||r,t),u.scrollXLoad||u.scrollYLoad?new Promise((e=>{setTimeout((()=>{(0,H.nextTick)((()=>{e()}))}),50)})):(0,H.nextTick)()},scrollToRow(t,o){const n=[];return t&&(e.treeConfig?n.push(m.scrollToTreeRow(t)):n.push(tt(at,t))),o&&n.push(p.scrollToColumn(o)),Promise.all(n)},scrollToColumn(e){const{fullColumnIdData:t}=d,o=Ae(at,e);return o&&t[o.id]?ot(at,o):(0,H.nextTick)()},clearScroll(){const{scrollXStore:e,scrollYStore:t}=d,o=y.value,n=T.value,l=O.value,r=o?o.$el:null,a=l?l.$el:null,i=n?n.$el:null;return a&&(Fe(a),a.scrollTop=0),i&&(i.scrollLeft=0),r&&(Fe(r),r.scrollTop=0,r.scrollLeft=0),e.startIndex=0,t.startIndex=0,(0,H.nextTick)()},updateFooter(){const{showFooter:t,footerData:o,footerMethod:n}=e,{visibleColumn:l,afterFullData:r}=d;let a=[];return t&&o&&o.length?a=o.slice(0):t&&n&&(a=l.length?n({columns:l,data:r,$table:at,$grid:N}):[]),u.footerTableData=a,(0,H.nextTick)()},updateStatus(t,o){const n=!a().isUndefined(o);return(0,H.nextTick)().then((()=>{const{editRules:l}=e,{validStore:r}=u,a=y.value;if(t&&a&&l){const{row:e,column:l}=t,a="change";if(at.hasCellRules&&at.hasCellRules(a,e,l)){const t=m.getCell(e,l);if(t)return at.validCellRules(a,e,l,o).then((()=>{n&&r.visible&&We(e,l,o),at.clearValidate(e,l)})).catch((({rule:r})=>{n&&We(e,l,o),at.showValidTooltip({rule:r,row:e,column:l,cell:t})}))}}}))},setMergeCells(t){return e.spanMethod&&B("vxe.error.errConflicts",["merge-cells","span-method"]),ht(t,u.mergeList,d.afterFullData),(0,H.nextTick)().then((()=>(p.updateCellAreas(),Rt())))},removeMergeCells(t){e.spanMethod&&B("vxe.error.errConflicts",["merge-cells","span-method"]);const o=gt(t,u.mergeList,d.afterFullData);return(0,H.nextTick)().then((()=>(p.updateCellAreas(),Rt(),o)))},getMergeCells(){return u.mergeList.slice(0)},clearMergeCells(){return u.mergeList=[],(0,H.nextTick)().then((()=>Rt()))},setMergeFooterItems(t){return e.footerSpanMethod&&B("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]),ht(t,u.mergeFooterList),(0,H.nextTick)().then((()=>(p.updateCellAreas(),Rt())))},removeMergeFooterItems(t){e.footerSpanMethod&&B("vxe.error.errConflicts",["merge-footer-items","footer-span-method"]);const o=gt(t,u.mergeFooterList);return(0,H.nextTick)().then((()=>(p.updateCellAreas(),Rt(),o)))},getMergeFooterItems(){return u.mergeFooterList.slice(0)},clearMergeFooterItems(){return u.mergeFooterList=[],(0,H.nextTick)().then((()=>Rt()))},updateCellAreas(){const{mouseConfig:t}=e,o=he.value;return t&&o.area&&at.handleUpdateCellAreas?at.handleUpdateCellAreas():(0,H.nextTick)()},focus(){return d.isActivated=!0,(0,H.nextTick)()},blur(){return d.isActivated=!1,(0,H.nextTick)()},connect(e){return e?(P=e,P.syncUpdate({collectColumn:d.collectColumn,$table:at})):B("vxe.error.barUnableLink"),(0,H.nextTick)()}};const po=t=>{const{editStore:o,ctxMenuStore:n,filterStore:l,customStore:r}=u,{mouseConfig:a,editRules:i}=e,s=f.value,c=ie.value,h=L.value,g=ge.value,{actived:C}=o,y=v.value,T=b.value,E=w.value,S=x.value;if(T&&(Ce(t,s,"vxe-cell--filter").flag||Ce(t,T.$el).flag||Ce(t,document.body,"vxe-table--ignore-clear").flag||m.preventEvent(t,"event.clearFilter",l.args,p.closeFilter)),E&&(r.btnEl===t.target||Ce(t,document.body,"vxe-toolbar-custom-target").flag||Ce(t,E.$el).flag||Ce(t,document.body,"vxe-table--ignore-clear").flag||m.preventEvent(t,"event.clearCustom",{},(()=>{at.closeCustom&&at.closeCustom()}))),C.row){if(!1!==c.autoClear){const o=C.args.cell;o&&Ce(t,o).flag||y&&Ce(t,y.$el).flag||(!d._lastCallTime||d._lastCallTime+50<Date.now())&&(Ce(t,document.body,"vxe-table--ignore-clear").flag||m.preventEvent(t,"event.clearEdit",C.args,(()=>{let o;if("row"===c.mode){const e=Ce(t,s,"vxe-body--row"),n=e.flag?p.getRowNode(e.targetElem):null;o=!!n&&!at.eqRow(n.item,C.args.row)}else o=!Ce(t,s,"col--edit").flag;if(o||(o=Ce(t,s,"vxe-header--row").flag),o||(o=Ce(t,s,"vxe-footer--row").flag),!o&&e.height&&!u.overflowY){const e=t.target;pe(e,"vxe-table--body-wrapper")&&(o=t.offsetY<e.clientHeight)}!o&&Ce(t,s).flag||setTimeout((()=>at.clearEdit(t)))})))}}else a&&(Ce(t,s).flag||N&&Ce(t,N.getRefMaps().refElem.value).flag||S&&Ce(t,S.getRefMaps().refElem.value).flag||P&&Ce(t,P.getRefMaps().refElem.value).flag||(at.clearSelected&&at.clearSelected(),g.autoClear&&at.clearCellAreas&&(Ce(t,document.body,"vxe-table--ignore-areas-clear").flag||m.preventEvent(t,"event.clearAreas",{},(()=>{at.clearCellAreas(),at.clearCopyCellArea()})))));at.closeMenu&&n.visible&&S&&!Ce(t,S.getRefMaps().refElem.value).flag&&at.closeMenu();const k=Ce(t,N?N.getRefMaps().refElem.value:s).flag;!k&&i&&h.autoClear&&(u.validErrorMaps={}),d.isActivated=k},mo=()=>{p.closeFilter(),at.closeMenu&&at.closeMenu()},fo=()=>{p.closeTooltip(),at.closeMenu&&at.closeMenu()},ho=t=>{const{mouseConfig:o,keyboardConfig:n}=e,{filterStore:l,ctxMenuStore:r,editStore:a}=u,i=he.value,s=we.value,{actived:c}=a,d=Jo(t,Xo.ESCAPE);d&&m.preventEvent(t,"event.keydown",null,(()=>{if(p.dispatchEvent("keydown-start",{},t),n&&o&&i.area&&at.handleKeyboardEvent)at.handleKeyboardEvent(t);else if((c.row||l.visible||r.visible)&&(t.stopPropagation(),at.closeMenu&&at.closeMenu(),p.closeFilter(),n&&s.isEsc&&c.row)){const e=c.args;at.clearEdit(t),i.selected&&(0,H.nextTick)((()=>at.handleSelected(e,t)))}p.dispatchEvent("keydown",{},t),p.dispatchEvent("keydown-end",{},t)}))},go=t=>{d.isActivated&&m.preventEvent(t,"event.keydown",null,(()=>{const{mouseConfig:o,keyboardConfig:n,treeConfig:l,editConfig:r,highlightCurrentRow:i}=e,{ctxMenuStore:s,editStore:c,currentRow:f}=u,h=Me.value,g=ke.value,v=we.value,x=he.value,b=ie.value,w=je.value,C=$e.value,y=W.value,{selected:T,actived:E}=c,S=w.children||w.childrenField,k=t.keyCode,R=Jo(t,Xo.ESCAPE),O=Jo(t,Xo.BACKSPACE),M=Jo(t,Xo.TAB),$=Jo(t,Xo.ENTER),I=Jo(t,Xo.SPACEBAR),D=Jo(t,Xo.ARROW_LEFT),F=Jo(t,Xo.ARROW_UP),P=Jo(t,Xo.ARROW_RIGHT),L=Jo(t,Xo.ARROW_DOWN),A=Jo(t,Xo.DELETE),V=Jo(t,Xo.F2),B=Jo(t,Xo.CONTEXT_MENU),_=t.metaKey,j=t.ctrlKey,z=t.shiftKey,q=t.altKey,U=D||F||P||L,G=h&&s.visible&&($||I||U),X=Y(r)&&E.column&&E.row;let K;if(G)t.preventDefault(),s.showChild&&ee(s.selected)?at.moveCtxMenu(t,s,"selectChild",D,!1,s.selected.children):at.moveCtxMenu(t,s,"selected",P,!0,C);else if(n&&o&&x.area&&at.handleKeyboardEvent)at.handleKeyboardEvent(t);else if(R){if(at.closeMenu&&at.closeMenu(),p.closeFilter(),n&&v.isEsc&&E.row){const e=E.args;at.clearEdit(t),x.selected&&(0,H.nextTick)((()=>at.handleSelected(e,t)))}}else if(I&&n&&v.isChecked&&T.row&&T.column&&("checkbox"===T.column.type||"radio"===T.column.type))t.preventDefault(),"checkbox"===T.column.type?m.handleToggleCheckRowEvent(t,T.args):m.triggerRadioRowEvent(t,T.args);else if(V&&Y(r))X||T.row&&T.column&&(t.preventDefault(),at.handleActived(T.args,t));else if(B)d._keyCtx=T.row&&T.column&&g.length,clearTimeout(uo),uo=setTimeout((()=>{d._keyCtx=!1}),1e3);else if($&&!q&&n&&v.isEnter&&(T.row||E.row||l&&(y.isCurrent||i)&&f)){if(j)E.row&&(K=E.args,at.clearEdit(t),x.selected&&(0,H.nextTick)((()=>at.handleSelected(K,t))));else if(T.row||E.row){const e=T.row?T.args:E.args;z?v.enterToTab?at.moveTabSelected(e,z,t):at.moveSelected(e,D,!0,P,!1,t):v.enterToTab?at.moveTabSelected(e,z,t):at.moveSelected(e,D,!1,P,!0,t)}else if(l&&(y.isCurrent||i)&&f){const e=f[S];if(e&&e.length){t.preventDefault();const o=e[0];K={$table:at,row:o,rowIndex:p.getRowIndex(o),$rowIndex:p.getVMRowIndex(o)},p.setTreeExpand(f,!0).then((()=>p.scrollToRow(o))).then((()=>m.triggerCurrentRowEvent(t,K)))}}}else if(U&&n&&v.isArrow)X||(T.row&&T.column?at.moveSelected(T.args,D,F,P,L,t):(F||L)&&(y.isCurrent||i)&&at.moveCurrentRow(F,L,t));else if(M&&n&&v.isTab)T.row||T.column?at.moveTabSelected(T.args,z,t):(E.row||E.column)&&at.moveTabSelected(E.args,z,t);else if(n&&Y(r)&&(A||(l&&(y.isCurrent||i)&&f?O&&v.isArrow:O))){if(!X){const{delMethod:e,backMethod:o}=v;if(v.isDel&&(T.row||T.column)){const n={row:T.row,rowIndex:p.getRowIndex(T.row),column:T.column,columnIndex:p.getColumnIndex(T.column),$table:at};e?e(n):We(T.row,T.column,null),O?o?o({row:T.row,rowIndex:p.getRowIndex(T.row),column:T.column,columnIndex:p.getColumnIndex(T.column),$table:at}):at.handleActived(T.args,t):A&&p.updateFooter(),at.dispatchEvent("cell-delete-value",n,t)}else if(O&&v.isArrow&&l&&(y.isCurrent||i)&&f){const{parent:e}=a().findTree(d.afterFullData,(e=>e===f),{children:S});e&&(t.preventDefault(),K={$table:at,row:e,rowIndex:p.getRowIndex(e),$rowIndex:p.getVMRowIndex(e)},p.setTreeExpand(e,!1).then((()=>p.scrollToRow(e))).then((()=>m.triggerCurrentRowEvent(t,K))))}}}else if(n&&Y(r)&&v.isEdit&&!j&&!_&&(I||k>=48&&k<=57||k>=65&&k<=90||k>=96&&k<=111||k>=186&&k<=192||k>=219&&k<=222)){const{editMethod:e}=v;if(T.column&&T.row&&Y(T.column.editRender)){const o=b.beforeEditMethod||b.activeMethod;if(!o||o({...T.args,$table:at,$grid:N})){e?e({row:T.row,rowIndex:p.getRowIndex(T.row),column:T.column,columnIndex:p.getColumnIndex(T.column),$table:at,$grid:N}):(We(T.row,T.column,null),at.handleActived(T.args,t));const o=b.afterEditMethod;o&&(0,H.nextTick)((()=>{o({row:T.row,rowIndex:p.getRowIndex(T.row),column:T.column,columnIndex:p.getColumnIndex(T.column),$table:at,$grid:N})}))}}}p.dispatchEvent("keydown",{},t)}))},vo=t=>{const{keyboardConfig:o,mouseConfig:n}=e,{editStore:l,filterStore:r}=u,{isActivated:a}=d,i=he.value,s=we.value,{actived:c}=l;a&&!r.visible&&(c.row||c.column||o&&s.isClip&&n&&i.area&&at.handlePasteCellAreaEvent&&at.handlePasteCellAreaEvent(t),p.dispatchEvent("paste",{},t))},xo=t=>{const{keyboardConfig:o,mouseConfig:n}=e,{editStore:l,filterStore:r}=u,{isActivated:a}=d,i=he.value,s=we.value,{actived:c}=l;a&&!r.visible&&(c.row||c.column||o&&s.isClip&&n&&i.area&&at.handleCopyCellAreaEvent&&at.handleCopyCellAreaEvent(t),p.dispatchEvent("copy",{},t))},bo=t=>{const{keyboardConfig:o,mouseConfig:n}=e,{editStore:l,filterStore:r}=u,{isActivated:a}=d,i=he.value,s=we.value,{actived:c}=l;a&&!r.visible&&(c.row||c.column||o&&s.isClip&&n&&i.area&&at.handleCutCellAreaEvent&&at.handleCutCellAreaEvent(t),p.dispatchEvent("cut",{},t))},wo=()=>{at.closeMenu&&at.closeMenu(),p.updateCellAreas(),p.recalculate(!0)},Co=e=>{const t=h.value;clearTimeout(d.tooltipTimeout),e?p.closeTooltip():t&&t.setActived(!0)},yo=(e,t,o,n,l)=>{l.cell=t;const{tooltipStore:r}=u,i=Z.value,{column:s,row:c}=l,{showAll:d,contentMethod:p}=i,m=p?p(l):null,f=p&&!a().eqNull(m),g=f?m:a().toString("html"===s.type?o.innerText:o.textContent).trim(),v=o.scrollWidth>o.clientWidth;return g&&(d||f||v)&&(Object.assign(r,{row:c,column:s,visible:!0}),(0,H.nextTick)((()=>{const e=h.value;e&&e.open(v?o:n||o,oe(g))}))),(0,H.nextTick)()};m={getSetupOptions(){return c},updateAfterDataIndex:Et,callSlot(e,t){if(e){if(N)return N.callSlot(e,t);if(a().isFunction(e))return lt(e(t))}return[]},getParentElem(){const e=f.value;if(N){const e=N.getRefMaps().refElem.value;return e?e.parentNode:null}return e?e.parentNode:null},getParentHeight(){const{height:t}=e,o=f.value;if(o){const e=o.parentNode,n="100%"===t||"auto"===t?ve(e):0;return Math.floor(N?N.getParentHeight():a().toNumber(getComputedStyle(e).height)-n)}return 0},getExcludeHeight(){return N?N.getExcludeHeight():0},defineField(t){const{treeConfig:o}=e,n=_e.value,l=je.value,r=X.value,i=K.value,s=l.children||l.childrenField,c=Pe(at);return a().isArray(t)||(t=[t]),t.map((e=>{d.tableFullColumn.forEach((t=>{const{field:o,editRender:n}=t;if(o&&!a().has(e,o)&&!e[o]){let l=null;if(n){const{defaultValue:e}=n;a().isFunction(e)?l=e({column:t}):a().isUndefined(e)||(l=e)}a().set(e,o,l)}}));const t=[r.labelField,i.checkField,i.labelField,n.labelField];return t.forEach((t=>{t&&ne(a().get(e,t))&&a().set(e,t,null)})),o&&l.lazy&&a().isUndefined(e[s])&&(e[s]=null),ne(a().get(e,c))&&a().set(e,c,Ne()),e}))},handleTableData(e){const{scrollYLoad:t}=u,{scrollYStore:o,fullDataRowIdData:n}=d;let l=d.afterFullData;e&&(kt(),l=St());const r=t?l.slice(o.startIndex,o.endIndex):l.slice(0);return r.forEach(((e,t)=>{const o=Le(at,e),l=n[o];l&&(l.$index=t)})),u.tableData=r,(0,H.nextTick)()},cacheRowMap(t){const{treeConfig:o}=e,n=je.value,{fullAllDataRowIdData:l,tableFullData:r,tableFullTreeData:i}=d,s=n.children||n.childrenField,c=n.hasChild||n.hasChildField,u=Pe(at),p=o&&n.lazy,m={},f={},h=(e,n,r,i,d,h)=>{let g=Le(at,e);const v=o&&i?He(i):n+1,x=h?h.length-1:0;ne(g)&&(g=Ne(),a().set(e,u,g)),p&&e[c]&&a().isUndefined(e[s])&&(e[s]=null);let b=l[g];b||(b={row:e,rowid:g,seq:v,index:-1,_index:-1,$index:-1,items:r,parent:d,level:x}),t&&(b.index=o&&d?-1:n,f[g]=b),m[g]=b};t&&(d.fullDataRowIdData=f),d.fullAllDataRowIdData=m,o?a().eachTree(i,h,{children:s}):r.forEach(h)},cacheSourceMap(t){const{treeConfig:o}=e,n=je.value;let{sourceDataRowIdData:l}=d;const r=a().clone(t,!0),i=Pe(at);l=d.sourceDataRowIdData={};const s=e=>{let t=Le(at,e);ne(t)&&(t=Ne(),a().set(e,i,t)),l[t]=e};if(o){const e=n.children||n.childrenField;a().eachTree(r,s,{children:n.transform?n.mapChildrenField:e})}else r.forEach(s);d.tableSourceData=r},analyColumnWidth(){const{tableFullColumn:e}=d,t=z.value,{width:o,minWidth:n}=t,l=[],r=[],a=[],i=[],s=[],c=[];e.forEach((e=>{o&&!e.width&&(e.width=o),n&&!e.minWidth&&(e.minWidth=n),e.visible&&(e.resizeWidth?l.push(e):ue(e.width)?r.push(e):de(e.width)?i.push(e):ue(e.minWidth)?a.push(e):de(e.minWidth)?s.push(e):c.push(e))})),Object.assign(u.columnStore,{resizeList:l,pxList:r,pxMinList:a,scaleList:i,scaleMinList:s,autoList:c})},saveCustomResizable(t){const{id:o,customConfig:n}=e,l=Xe.value,{collectColumn:r}=d,{storage:i}=l,s=!0===i,c=s?{}:Object.assign({},i||{}),u=s||c.resizable;if(n&&u){const e=ct(Tr);let n;if(!o)return void B("vxe.error.reqProp",["id"]);t||(n=a().isPlainObject(e[o])?e[o]:{},a().eachTree(r,(e=>{if(e.resizeWidth){const t=e.getKey();t&&(n[t]=e.renderWidth)}}))),e[o]=a().isEmpty(n)?void 0:n,localStorage.setItem(Tr,a().toJSONString(e))}},saveCustomSort(t){const{id:o,customConfig:n}=e,l=Xe.value,{collectColumn:r}=d,{storage:i}=l,s=!0===i,c=s?{}:Object.assign({},i||{}),u=s||c.sort;if(n&&u){const e=ct(kr);let n;if(!o)return void B("vxe.error.reqProp",["id"]);t||(n=a().isPlainObject(e[o])?e[o]:{},r.forEach((e=>{if(e.sortNumber!==e.renderSortNumber){const t=e.getKey();t&&(n[t]=e.renderSortNumber)}}))),e[o]=a().isEmpty(n)?void 0:n,localStorage.setItem(kr,a().toJSONString(e))}},saveCustomFixed(){const{id:t,customConfig:o}=e,{collectColumn:n}=d,l=Xe.value,{storage:r}=l,i=!0===r,s=i?{}:Object.assign({},r||{}),c=i||s.fixed;if(o&&c){const e=ct(Sr),o=[];if(!t)return void B("vxe.error.reqProp",["id"]);a().eachTree(n,(e=>{if(e.fixed&&e.fixed!==e.defaultFixed){const t=e.getKey();t&&o.push(`${t}|${e.fixed}`)}})),e[t]=o.join(",")||void 0,localStorage.setItem(Sr,a().toJSONString(e))}},saveCustomVisible(){const{id:t,customConfig:o}=e,{collectColumn:n}=d,l=Xe.value,{checkMethod:r,storage:i}=l,s=!0===i,c=s?{}:Object.assign({},i||{}),u=s||c.visible;if(o&&u){const e=ct(Er),o=[],l=[];if(!t)return void B("vxe.error.reqProp",["id"]);a().eachTree(n,(e=>{if(!r||r({column:e}))if(!e.visible&&e.defaultVisible){const t=e.getKey();t&&o.push(t)}else if(e.visible&&!e.defaultVisible){const t=e.getKey();t&&l.push(t)}})),e[t]=[o.join(",")].concat(l.length?[l.join(",")]:[]).join("|")||void 0,localStorage.setItem(Er,a().toJSONString(e))}},handleCustom(){const{mouseConfig:t}=e;return t&&(at.clearSelected&&at.clearSelected(),at.clearCellAreas&&(at.clearCellAreas(),at.clearCopyCellArea())),m.saveCustomVisible(),m.saveCustomSort(),m.analyColumnWidth(),p.refreshColumn(!0)},handleUpdateDataQueue(){u.upDataFlag++},handleRefreshColumnQueue(){u.reColumnFlag++},preventEvent(e,t,o,n,l){let r,a=No.interceptor.get(t);return a.length||"event.clearEdit"!==t||(a=No.interceptor.get("event.clearActived"),a.length&&V("vxe.error.delEvent",["event.clearActived","event.clearEdit"])),a.some((t=>!1===t(Object.assign({$grid:N,$table:at,$event:e},o))))||n&&(r=n()),l&&l(),r},checkSelectionStatus(){const{treeConfig:t}=e,{selectCheckboxMaps:o,treeIndeterminateMaps:n}=u,{afterFullData:l}=d,r=K.value,{checkField:i,checkStrictly:s,checkMethod:c}=r,p=r.indeterminateField||r.halfField;if(!s){const e=[],r=[];let s=!1,d=!1,m=!1;i?(s=l.every(c?t=>c({row:t})?!!a().get(t,i)&&(r.push(t),!0):(e.push(t),!0):e=>a().get(e,i)),d=s&&l.length!==e.length,m=t?p?!d&&l.some((e=>a().get(e,i)||a().get(e,p)||!!n[Le(at,e)])):!d&&l.some((e=>a().get(e,i)||!!n[Le(at,e)])):p?!d&&l.some((e=>a().get(e,i)||a().get(e,p))):!d&&l.some((e=>a().get(e,i)))):(s=l.every(c?t=>c({row:t})?!!o[Le(at,t)]&&(r.push(t),!0):(e.push(t),!0):e=>o[Le(at,e)]),d=s&&l.length!==e.length,m=t?!d&&l.some((e=>{const t=Le(at,e);return n[t]||o[t]})):!d&&l.some((e=>o[Le(at,e)]))),u.isAllSelected=d,u.isIndeterminate=m}},handleSelectRow({row:t},o,n){const{treeConfig:l}=e,{selectCheckboxMaps:r,treeIndeterminateMaps:i}=u,s={...r},{afterFullData:c}=d,p=je.value,f=p.children||p.childrenField,h=K.value,{checkField:g,checkStrictly:v,checkMethod:x}=h,b=h.indeterminateField||h.halfField,w=Le(at,t);if(g)if(l&&!v){-1===o?(i[w]||(b&&a().set(t,b,!0),i[w]=t),a().set(t,g,!1)):a().eachTree([t],(e=>{(at.eqRow(e,t)||n||!x||x({row:e}))&&(a().set(e,g,o),b&&a().set(t,b,!1),delete i[Le(at,e)],Pt(t,o))}),{children:f});const e=a().findTree(c,(e=>at.eqRow(e,t)),{children:f});if(e&&e.parent){let t;const l=[],r={};!n&&x?e.items.forEach((e=>{if(x({row:e})){const t=Le(at,e);r[t]=e,l.push(e)}})):e.items.forEach((e=>{const t=Le(at,e);r[t]=e,l.push(e)}));const c=a().find(e.items,(e=>!!i[Le(at,e)]));if(c)t=-1;else{const n=[];e.items.forEach((e=>{a().get(e,g)&&n.push(e)})),t=n.filter((e=>r[Le(at,e)])).length===l.length||!(!n.length&&-1!==o)&&-1}return u.selectCheckboxMaps=s,m.handleSelectRow({row:e.parent},t,n)}}else(n||!x||x({row:t}))&&(a().set(t,g,o),Pt(t,o));else if(l&&!v){-1===o?(i[w]||(b&&a().set(t,b,!0),i[w]=t),s[w]&&delete s[w]):a().eachTree([t],(e=>{const l=Le(at,e);(at.eqRow(e,t)||n||!x||x({row:e}))&&(o?s[l]=e:s[l]&&delete s[l],b&&a().set(t,b,!1),delete i[Le(at,e)],Pt(t,o))}),{children:f});const e=a().findTree(c,(e=>at.eqRow(e,t)),{children:f});if(e&&e.parent){let t;const l=[],r={};!n&&x?e.items.forEach((e=>{if(x({row:e})){const t=Le(at,e);r[t]=e,l.push(e)}})):e.items.forEach((e=>{const t=Le(at,e);r[t]=e,l.push(e)}));const c=a().find(e.items,(e=>!!i[Le(at,e)]));if(c)t=-1;else{const n=[];e.items.forEach((e=>{const t=Le(at,e);s[t]&&n.push(e)})),t=n.filter((e=>r[Le(at,e)])).length===l.length||!(!n.length&&-1!==o)&&-1}return u.selectCheckboxMaps=s,m.handleSelectRow({row:e.parent},t,n)}}else(n||!x||x({row:t}))&&(o?s[w]||(s[w]=t):s[w]&&delete s[w],Pt(t,o));u.selectCheckboxMaps=s,m.checkSelectionStatus()},triggerHeaderTitleEvent(e,t,o){const n=t.content||t.message;if(n){const{tooltipStore:t}=u,{column:l}=o,r=te(n);Co(!0),t.row=null,t.column=l,t.visible=!0,(0,H.nextTick)((()=>{const t=h.value;t&&t.open(e.currentTarget,r)}))}},triggerHeaderTooltipEvent(e,t){const{tooltipStore:o}=u,{column:n}=t,l=e.currentTarget;Co(!0),o.column===n&&o.visible||yo(e,l,l,null,t)},triggerBodyTooltipEvent(t,o){const{editConfig:n}=e,{editStore:l}=u,{tooltipStore:r}=u,a=ie.value,{actived:i}=l,{row:s,column:c}=o,d=t.currentTarget;if(Co(r.column!==c||r.row!==s),c.editRender&&Y(n)){if("row"===a.mode&&i.row===s)return;if(i.row===s&&i.column===c)return}if(r.column!==c||r.row!==s||!r.visible){let e,n;c.treeNode?(e=d.querySelector(".vxe-tree-cell"),"html"===c.type&&(n=d.querySelector(".vxe-cell--html"))):n=d.querySelector("html"===c.type?".vxe-cell--html":".vxe-cell--label"),yo(t,d,e||d.children[0],n,o)}},triggerFooterTooltipEvent(e,t){const{column:o}=t,{tooltipStore:n}=u,l=e.currentTarget;Co(n.column!==o||!!n.row),n.column===o&&n.visible||yo(e,l,l.querySelector(".vxe-cell--item")||l.children[0],null,t)},handleTargetLeaveEvent(){const e=Z.value;let t=h.value;t&&t.setActived(!1),e.enterable?d.tooltipTimeout=setTimeout((()=>{t=h.value,t&&!t.isActived()&&p.closeTooltip()}),e.leaveDelay):p.closeTooltip()},triggerHeaderCellClickEvent(t,o){const{_lastResizeTime:n}=d,l=se.value,r=z.value,{column:a}=o,i=t.currentTarget,s=n&&n>Date.now()-300,c=Ce(t,i,"vxe-cell--sort").flag,u=Ce(t,i,"vxe-cell--filter").flag;"cell"!==l.trigger||s||c||u||m.triggerSortEvent(t,a,st(a)),p.dispatchEvent("header-cell-click",Object.assign({triggerResizable:s,triggerSort:c,triggerFilter:u,cell:i},o),t),(r.isCurrent||e.highlightCurrentColumn)&&p.setCurrentColumn(a)},triggerHeaderCellDblclickEvent(e,t){p.dispatchEvent("header-cell-dblclick",Object.assign({cell:e.currentTarget},t),e)},triggerCellClickEvent(t,o){const{highlightCurrentRow:n,editConfig:l}=e,{editStore:r}=u,a=_e.value,i=ie.value,s=je.value,c=X.value,d=K.value,f=we.value,h=W.value,{actived:g,focused:v}=r,{row:x,column:b}=o,{type:w,treeNode:C}=b,y="radio"===w,T="checkbox"===w,E="expand"===w,S=t.currentTarget,k=y&&Ce(t,S,"vxe-cell--radio").flag,R=T&&Ce(t,S,"vxe-cell--checkbox").flag,O=C&&Ce(t,S,"vxe-tree--btn-wrapper").flag,M=E&&Ce(t,S,"vxe-table--expanded").flag;o=Object.assign({cell:S,triggerRadio:k,triggerCheckbox:R,triggerTreeNode:O,triggerExpandNode:M},o),R||k||(!M&&("row"===a.trigger||E&&"cell"===a.trigger)&&m.triggerRowExpandEvent(t,o),("row"===s.trigger||C&&"cell"===s.trigger)&&m.triggerTreeExpandEvent(t,o)),O||(M||((h.isCurrent||n)&&(R||k||m.triggerCurrentRowEvent(t,o)),!k&&("row"===c.trigger||y&&"cell"===c.trigger)&&m.triggerRadioRowEvent(t,o),!R&&("row"===d.trigger||T&&"cell"===d.trigger)&&m.handleToggleCheckRowEvent(t,o)),Y(l)&&(f.arrowCursorLock&&t&&"cell"===i.mode&&t.target&&/^input|textarea$/i.test(t.target.tagName)&&(v.column=b,v.row=x),"manual"===i.trigger?g.args&&g.row===x&&b!==g.column&&Mt(t,o):g.args&&x===g.row&&b===g.column||("click"===i.trigger||"dblclick"===i.trigger&&"row"===i.mode&&g.row===x)&&Mt(t,o))),p.dispatchEvent("cell-click",o,t)},triggerCellDblclickEvent(t,o){const{editConfig:n}=e,{editStore:l}=u,r=ie.value,{actived:a}=l,i=t.currentTarget;o=Object.assign({cell:i},o),Y(n)&&"dblclick"===r.trigger&&(a.args&&t.currentTarget===a.args.cell||("row"===r.mode?Ot("blur").catch((e=>e)).then((()=>{at.handleActived(o,t).then((()=>Ot("change"))).catch((e=>e))})):"cell"===r.mode&&at.handleActived(o,t).then((()=>Ot("change"))).catch((e=>e)))),p.dispatchEvent("cell-dblclick",o,t)},handleToggleCheckRowEvent(e,t){const{selectCheckboxMaps:o}=u,n=K.value,{checkField:l}=n,{row:r}=t;let i=!1;i=l?!a().get(r,l):!o[Le(at,r)],e?m.triggerCheckRowEvent(e,t,i):m.handleSelectRow(t,i)},triggerCheckRowEvent(t,o,n){const l=K.value,{row:r}=o,{afterFullData:a}=d,{checkMethod:i}=l;if(l.isShiftKey&&t.shiftKey&&!e.treeConfig){const e=p.getCheckboxRecords();if(e.length){const n=e[0],l=p.getVTRowIndex(r),i=p.getVTRowIndex(n);if(l!==i){p.setAllCheckboxRow(!1);const e=l<i?a.slice(l,i+1):a.slice(i,l+1);return At(e,!0,!1),void p.dispatchEvent("checkbox-range-select",Object.assign({rangeRecords:e},o),t)}}}i&&!i({row:r})||(m.handleSelectRow(o,n),p.dispatchEvent("checkbox-change",Object.assign({records:p.getCheckboxRecords(),reserves:p.getCheckboxReserveRecords(),indeterminates:p.getCheckboxIndeterminateRecords(),checked:n},o),t))},triggerCheckAllEvent(e,t){Vt(t),e&&p.dispatchEvent("checkbox-all",{records:p.getCheckboxRecords(),reserves:p.getCheckboxReserveRecords(),indeterminates:p.getCheckboxIndeterminateRecords(),checked:t},e)},triggerRadioRowEvent(e,t){const{selectRadioRow:o}=u,{row:n}=t,l=X.value;let r=n,a=o!==r;a?Lt(r):l.strict||(a=o===r,a&&(r=null,p.clearRadioRow())),a&&p.dispatchEvent("radio-change",{oldValue:o,newValue:r,...t},e)},triggerCurrentRowEvent(e,t){const{currentRow:o}=u,{row:n}=t,l=o!==n;p.setCurrentRow(n),l&&p.dispatchEvent("current-change",{oldValue:o,newValue:n,...t},e)},triggerRowExpandEvent(e,t){const{rowExpandLazyLoadedMaps:o,expandColumn:n}=u,l=_e.value,{row:r}=t,{lazy:a}=l,i=Le(at,r);if(!a||!o[i]){const t=!p.isRowExpandByRow(r),o=p.getColumnIndex(n),l=p.getVMColumnIndex(n);p.setRowExpand(r,t),p.dispatchEvent("toggle-row-expand",{expanded:t,column:n,columnIndex:o,$columnIndex:l,row:r,rowIndex:p.getRowIndex(r),$rowIndex:p.getVMRowIndex(r)},e)}},triggerTreeExpandEvent(e,t){const{treeExpandLazyLoadedMaps:o}=u,n=je.value,{row:l,column:r}=t,{lazy:a}=n,i=Le(at,l);if(!a||!o[i]){const t=!p.isTreeExpandByRow(l),o=p.getColumnIndex(r),n=p.getVMColumnIndex(r);p.setTreeExpand(l,t),p.dispatchEvent("toggle-tree-expand",{expanded:t,column:r,columnIndex:o,$columnIndex:n,row:l},e)}},triggerSortEvent(t,o,n){const{mouseConfig:l}=e,r=se.value,a=he.value,{field:i,sortable:s}=o;if(s){n&&o.order!==n?p.sort({field:i,order:n}):p.clearSort(r.multiple?o:null);const e={$table:at,$event:t,column:o,field:i,property:i,order:o.order,sortList:p.getSortColumns(),sortTime:o.sortTime};l&&a.area&&at.handleSortEvent&&at.handleSortEvent(t,e),p.dispatchEvent("sort-change",e,t)}},triggerScrollXEvent(){Jt()},triggerScrollYEvent(e){const{scrollYStore:t}=d,{adaptive:o,offsetSize:n,visibleSize:l}=t;yr&&o&&2*n+l<=40?ao(e):co(e)},scrollToTreeRow(t){const{treeConfig:o}=e,{tableFullData:n}=d,l=[];if(o){const e=je.value,o=e.children||e.childrenField,r=a().findTree(n,(e=>at.eqRow(e,t)),{children:o});if(r){const e=r.nodes;e.forEach(((t,o)=>{o<e.length-1&&!p.isTreeExpandByRow(t)&&l.push(p.setTreeExpand(t,!0))}))}}return Promise.all(l).then((()=>tt(at,t)))},updateScrollYStatus:no,updateScrollXSpace(){const{isGroup:e,scrollXLoad:t,scrollbarWidth:o}=u,{visibleColumn:n,scrollXStore:l,elemStore:r,tableWidth:a}=d,i=C.value,s=y.value,c=T.value,p=s?s.$el:null;if(p){const s=i?i.$el:null,u=c?c.$el:null,d=s?s.querySelector(".vxe-table--header"):null,m=p.querySelector(".vxe-table--body"),f=u?u.querySelector(".vxe-table--footer"):null,h=n.slice(0,l.startIndex).reduce(((e,t)=>e+t.renderWidth),0);let g="";t&&(g=`${h}px`),d&&(d.style.marginLeft=e?"":g),m.style.marginLeft=g,f&&(f.style.marginLeft=g);const v=["main"];v.forEach((e=>{const n=["header","body","footer"];n.forEach((n=>{const l=r[`${e}-${n}-xSpace`],i=l?l.value:null;i&&(i.style.width=t?`${a+("header"===n?o:0)}px`:"")}))})),(0,H.nextTick)(Rt)}},updateScrollYSpace(){const{scrollYLoad:e}=u,{scrollYStore:t,elemStore:o,afterFullData:n}=d,{startIndex:l,rowHeight:r}=t,a=n.length*r,i=Math.max(0,l*r),s=["main","left","right"];let c="",p="";e&&(c=`${i}px`,p=`${a}px`),s.forEach((e=>{const t=["header","body","footer"],n=o[`${e}-body-table`],l=n?n.value:null;l&&(l.style.marginTop=c),t.forEach((t=>{const n=o[`${e}-${t}-ySpace`],l=n?n.value:null;l&&(l.style.height=p)}))})),(0,H.nextTick)(Rt)},updateScrollXData(){(0,H.nextTick)((()=>{Zt(),m.updateScrollXSpace()}))},updateScrollYData(){(0,H.nextTick)((()=>{m.handleTableData(),m.updateScrollYSpace()}))},checkScrolling(){const e=$.value,t=I.value,o=y.value,n=o?o.$el:null;n&&(e&&(n.scrollLeft>0?fe(e,"scrolling--middle"):me(e,"scrolling--middle")),t&&(n.clientWidth<n.scrollWidth-Math.ceil(n.scrollLeft)?fe(t,"scrolling--middle"):me(t,"scrolling--middle")))},updateZindex(){e.zIndex?d.tZindex=e.zIndex:d.tZindex<Q()&&(d.tZindex=J())},handleCheckedCheckboxRow:At,triggerHoverEvent(e,{row:t}){m.setHoverRow(t)},setHoverRow(e){const t=Le(at,e),o=f.value;m.clearHoverRow(),o&&a().arrayEach(o.querySelectorAll(`[rowid="${t}"]`),(e=>fe(e,"row--hover"))),d.hoverRow=e},clearHoverRow(){const e=f.value;e&&a().arrayEach(e.querySelectorAll(".vxe-body--row.row--hover"),(e=>me(e,"row--hover"))),d.hoverRow=null},getCell(e,t){const o=Le(at,e),n=y.value,l=S.value,r=O.value;let a;return t&&(t.fixed&&("left"===t.fixed?l&&(a=l.$el):r&&(a=r.$el)),a||(a=n.$el),a)?a.querySelector(`.vxe-body--row[rowid="${o}"] .${t.id}`):null},getCellLabel(e,t){const o=t.formatter,n=ze(e,t);let l=n;if(o){let r;const{fullAllDataRowIdData:i}=d,s=Le(at,e),c=t.id,u=i[s];if(u&&(r=u.formatData,r||(r=i[s].formatData={}),u&&r[c]&&r[c].value===n))return r[c].label;const m={cellValue:n,row:e,rowIndex:p.getRowIndex(e),column:t,columnIndex:p.getColumnIndex(t)};if(a().isString(o)){const e=No.formats.get(o),t=e?e.tableCellFormatMethod||e.cellFormatMethod:null;l=t?t(m):""}else if(a().isArray(o)){const e=No.formats.get(o[0]),t=e?e.tableCellFormatMethod||e.cellFormatMethod:null;l=t?t(m,...o.slice(1)):""}else l=o(m);r&&(r[c]={value:n,label:l})}return l},findRowIndexOf(e,t){return t?a().findIndexOf(e,(e=>at.eqRow(e,t))):-1},eqRow(e,t){return!(!e||!t)&&(e===t||Le(at,e)===Le(at,t))}},"openExport,openPrint,exportData,openImport,importData,saveFile,readFile,importByFile,print".split(",").forEach((e=>{at[e]=function(){B("vxe.error.reqModule",["VxeTableExportModule"])}})),"clearValidate,fullValidate,validate".split(",").forEach((e=>{at[e]=function(){B("vxe.error.reqModule",["VxeTableValidatorModule"])}})),Object.assign(at,p,m);const To=t=>{const{showHeader:o,showFooter:n}=e,{tableData:l,tableColumn:r,tableGroupColumn:a,columnStore:i,footerTableData:s}=u,c="left"===t,d=c?i.leftList:i.rightList;return(0,H.h)("div",{ref:c?$:I,class:`vxe-table--fixed-${t}-wrapper`},[o?(0,H.h)(gr,{ref:c?E:R,fixedType:t,tableData:l,tableColumn:r,tableGroupColumn:a,fixedColumn:d}):(0,H.createCommentVNode)(),(0,H.h)(fr,{ref:c?S:O,fixedType:t,tableData:l,tableColumn:r,fixedColumn:d}),n?(0,H.h)(br,{ref:c?k:M,footerTableData:s,tableColumn:r,fixedColumn:d,fixedType:t}):(0,H.createCommentVNode)()])},Eo=()=>{const t=qe.value,n={$table:at};if(o.empty)return o.empty(n);{const e=t.name?No.renderer.get(t.name):null,o=e?e.renderTableEmptyView||e.renderEmpty:null;if(o)return lt(o(t,n))}return te(e.emptyText)||c.i18n("vxe.table.emptyText")};function So(){const e=f.value;e&&e.clientWidth&&e.clientHeight&&p.recalculate()}const ko=(0,H.ref)(0);(0,H.watch)((()=>e.data?e.data.length:-1),(()=>{ko.value++})),(0,H.watch)((()=>e.data),(()=>{ko.value++})),(0,H.watch)(ko,(()=>{const{inited:t,initStatus:o}=d;Xt(e.data||[]).then((()=>{const{scrollXLoad:e,scrollYLoad:n,expandColumn:l}=u;d.inited=!0,d.initStatus=!0,o||Yt(),t||Kt(),(e||n)&&l&&V("vxe.error.scrollErrProp",["column.type=expand"]),p.recalculate()}))}));const Ro=(0,H.ref)(0);(0,H.watch)((()=>u.staticColumns.length),(()=>{Ro.value++})),(0,H.watch)((()=>u.staticColumns),(()=>{Ro.value++})),(0,H.watch)(Ro,(()=>{oo(u.staticColumns)}));const Oo=(0,H.ref)(0);(0,H.watch)((()=>u.tableColumn.length),(()=>{Oo.value++})),(0,H.watch)((()=>u.tableColumn),(()=>{Oo.value++})),(0,H.watch)(Oo,(()=>{m.analyColumnWidth()})),(0,H.watch)((()=>u.upDataFlag),(()=>{(0,H.nextTick)((()=>{p.updateData()}))})),(0,H.watch)((()=>u.reColumnFlag),(()=>{(0,H.nextTick)((()=>{p.refreshColumn()}))})),(0,H.watch)((()=>e.showHeader),(()=>{(0,H.nextTick)((()=>{p.recalculate(!0).then((()=>p.refreshScroll()))}))})),(0,H.watch)((()=>e.showFooter),(()=>{(0,H.nextTick)((()=>{p.recalculate(!0).then((()=>p.refreshScroll()))}))}));const Mo=(0,H.ref)(0);(0,H.watch)((()=>e.footerData?e.footerData.length:-1),(()=>{Mo.value++})),(0,H.watch)((()=>e.footerData),(()=>{Mo.value++})),(0,H.watch)(Mo,(()=>{p.updateFooter()})),(0,H.watch)((()=>e.height),(()=>{(0,H.nextTick)((()=>p.recalculate(!0)))})),(0,H.watch)((()=>e.maxHeight),(()=>{(0,H.nextTick)((()=>p.recalculate(!0)))})),(0,H.watch)((()=>e.syncResize),(e=>{e&&(So(),(0,H.nextTick)((()=>{So(),setTimeout((()=>So()))})))}));const $o=(0,H.ref)(0);(0,H.watch)((()=>e.mergeCells?e.mergeCells.length:-1),(()=>{$o.value++})),(0,H.watch)((()=>e.mergeCells),(()=>{$o.value++})),(0,H.watch)($o,(()=>{p.clearMergeCells(),(0,H.nextTick)((()=>{e.mergeCells&&p.setMergeCells(e.mergeCells)}))}));const Io=(0,H.ref)(0);let Do;(0,H.watch)((()=>e.mergeFooterItems?e.mergeFooterItems.length:-1),(()=>{Io.value++})),(0,H.watch)((()=>e.mergeFooterItems),(()=>{Io.value++})),(0,H.watch)(Io,(()=>{p.clearMergeFooterItems(),(0,H.nextTick)((()=>{e.mergeFooterItems&&p.setMergeFooterItems(e.mergeFooterItems)}))})),No.hooks.forEach((e=>{const{setupTable:t}=e;if(t){const e=t(at);e&&a().isObject(e)&&Object.assign(at,e)}})),m.preventEvent(null,"created",{$table:at}),(0,H.onActivated)((()=>{p.recalculate().then((()=>p.refreshScroll())),m.preventEvent(null,"activated",{$table:at})})),(0,H.onDeactivated)((()=>{d.isActivated=!1,m.preventEvent(null,"deactivated",{$table:at})})),(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{data:t,treeConfig:o,showOverflow:n}=e,{scrollXStore:l,scrollYStore:r}=d,i=_.value,s=ie.value,c=je.value,u=X.value,h=K.value,g=_e.value,v=W.value;{e.rowId&&V("vxe.error.delProp",["row-id","row-config.keyField"]),e.rowKey&&V("vxe.error.delProp",["row-key","row-config.useKey"]),e.columnKey&&V("vxe.error.delProp",["column-id","column-config.useKey"]),e.rowId||v.keyField||!(h.reserve||h.checkRowKeys||u.reserve||u.checkRowKey||g.expandRowKeys||c.expandRowKeys)||V("vxe.error.reqProp",["row-config.keyField"]),e.editConfig&&(s.showStatus||s.showUpdateStatus||s.showInsertStatus)&&!e.keepSource&&V("vxe.error.reqProp",["keep-source"]),!o||!c.showLine&&!c.line||(e.rowKey||v.useKey)&&n||V("vxe.error.reqProp",["row-config.useKey | show-overflow"]),o&&e.stripe&&V("vxe.error.noTree",["stripe"]),!e.showFooter||e.footerMethod||e.footerData||V("vxe.error.reqProp",["footer-data | footer-method"]);const{exportConfig:t,importConfig:l}=e,r=De.value,i=Ve.value;l&&i.types&&!i.importMethod&&!a().includeArrays(No.globalConfs.importTypes,i.types)&&V("vxe.error.errProp",[`export-config.types=${i.types.join(",")}`,i.types.filter((e=>a().includes(No.globalConfs.importTypes,e))).join(",")||No.globalConfs.importTypes.join(",")]),t&&r.types&&!r.exportMethod&&!a().includeArrays(No.globalConfs.exportTypes,r.types)&&V("vxe.error.errProp",[`export-config.types=${r.types.join(",")}`,r.types.filter((e=>a().includes(No.globalConfs.exportTypes,e))).join(",")||No.globalConfs.exportTypes.join(",")])}{const t=Xe.value,o=he.value,n=W.value;if(!e.id&&e.customConfig&&(!0===t.storage||t.storage&&t.storage.resizable||t.storage&&t.storage.visible)&&B("vxe.error.reqProp",["id"]),e.treeConfig&&h.range&&B("vxe.error.noTree",["checkbox-config.range"]),n.height&&!e.showOverflow&&V("vxe.error.notProp",["table.show-overflow"]),!at.handleUpdateCellAreas&&(e.clipConfig&&V("vxe.error.notProp",["clip-config"]),e.fnrConfig&&V("vxe.error.notProp",["fnr-config"]),o.area))return void B("vxe.error.notProp",["mouse-config.area"]);e.treeConfig&&c.children&&V("vxe.error.delProp",["tree-config.children","tree-config.childrenField"]),e.treeConfig&&c.line&&V("vxe.error.delProp",["tree-config.line","tree-config.showLine"]),o.area&&o.selected&&V("vxe.error.errConflicts",["mouse-config.area","mouse-config.selected"]),o.area&&h.range&&V("vxe.error.errConflicts",["mouse-config.area","checkbox-config.range"]),e.treeConfig&&o.area&&B("vxe.error.noTree",["mouse-config.area"]),e.editConfig&&s.activeMethod&&V("vxe.error.delProp",["edit-config.activeMethod","edit-config.beforeEditMethod"]),e.treeConfig&&h.isShiftKey&&B("vxe.error.errConflicts",["tree-config","checkbox-config.isShiftKey"]),h.halfField&&V("vxe.error.delProp",["checkbox-config.halfField","checkbox-config.indeterminateField"])}if(e.editConfig&&!at.insert&&B("vxe.error.reqModule",["Edit"]),e.editRules&&!at.validate&&B("vxe.error.reqModule",["Validator"]),(h.range||e.keyboardConfig||e.mouseConfig)&&!at.triggerCellMousedownEvent&&B("vxe.error.reqModule",["Keyboard"]),(e.printConfig||e.importConfig||e.exportConfig)&&!at.exportData&&B("vxe.error.reqModule",["Export"]),Object.assign(r,{startIndex:0,endIndex:0,visibleSize:0,adaptive:!1!==i.adaptive}),Object.assign(l,{startIndex:0,endIndex:0,visibleSize:0}),Xt(t||[]).then((()=>{t&&t.length&&(d.inited=!0,d.initStatus=!0,Yt(),Kt()),Rt()})),e.autoResize){const t=q.value,{refreshDelay:o}=t,n=f.value,l=m.getParentElem(),r=o?a().throttle((()=>p.recalculate(!0)),o,{leading:!0,trailing:!0}):null;Do=dr(r?()=>{e.autoResize&&requestAnimationFrame(r)}:()=>{e.autoResize&&p.recalculate(!0)}),n&&Do.observe(n),l&&Do.observe(l)}})),en.on(at,"paste",vo),en.on(at,"copy",xo),en.on(at,"cut",bo),en.on(at,"mousedown",po),en.on(at,"blur",mo),en.on(at,"mousewheel",fo),en.on(at,"keydown",go),en.on(at,"resize",wo),at.handleGlobalContextmenuEvent&&en.on(at,"contextmenu",at.handleGlobalContextmenuEvent),m.preventEvent(null,"mounted",{$table:at})})),(0,H.onBeforeUnmount)((()=>{Do&&Do.disconnect(),p.closeFilter(),at.closeMenu&&at.closeMenu(),m.preventEvent(null,"beforeUnmount",{$table:at})})),(0,H.onUnmounted)((()=>{en.off(at,"paste"),en.off(at,"copy"),en.off(at,"cut"),en.off(at,"mousedown"),en.off(at,"blur"),en.off(at,"mousewheel"),en.off(at,"keydown"),en.off(at,"resize"),en.off(at,"contextmenu"),m.preventEvent(null,"unmounted",{$table:at})}));const Fo=()=>{const{loading:t,stripe:n,showHeader:a,height:s,treeConfig:d,mouseConfig:p,showFooter:m,highlightCell:E,highlightHoverRow:S,highlightHoverColumn:k,editConfig:R,editRules:O}=e,{isGroup:M,overflowX:$,overflowY:I,scrollXLoad:P,scrollYLoad:A,scrollbarHeight:V,tableData:B,tableColumn:_,tableGroupColumn:j,footerTableData:q,initStore:U,columnStore:G,filterStore:X,customStore:Y}=u,{leftList:K,rightList:Z}=G,J=o.loading,Q=le.value,ee=L.value,te=je.value,oe=W.value,ne=z.value,ae=i.value,ie=Je.value,se=he.value,ce=re.value,ue=Ue.value,de=Me.value;return(0,H.h)("div",{ref:f,class:["vxe-table","vxe-table--render-default",`tid_${r}`,`border--${ie}`,{[`size--${ae}`]:ae,[`valid-msg--${ee.msgMode}`]:!!O,"vxe-editable":!!R,"old-cell-valid":O&&"obsolete"===c.cellVaildMode,"cell--highlight":E,"cell--selected":p&&se.selected,"cell--area":p&&se.area,"row--highlight":oe.isHover||S,"column--highlight":ne.isHover||k,"is--header":a,"is--footer":m,"is--group":M,"is--tree-line":d&&(te.showLine||te.line),"is--fixed-left":K.length,"is--fixed-right":Z.length,"is--animat":!!e.animat,"is--round":e.round,"is--stripe":!d&&n,"is--loading":t,"is--empty":!t&&!B.length,"is--scroll-y":I,"is--scroll-x":$,"is--virtual-x":P,"is--virtual-y":A}],onKeydown:ho},[(0,H.h)("div",{class:"vxe-table-slots"},o.default?o.default({}):[]),(0,H.h)("div",{class:"vxe-table--render-wrapper"},[(0,H.h)("div",{class:"vxe-table--main-wrapper"},[a?(0,H.h)(gr,{ref:C,tableData:B,tableColumn:_,tableGroupColumn:j}):(0,H.createCommentVNode)(),(0,H.h)(fr,{ref:y,tableData:B,tableColumn:_}),m?(0,H.h)(br,{ref:T,footerTableData:q,tableColumn:_}):(0,H.createCommentVNode)()]),(0,H.h)("div",{class:"vxe-table--fixed-wrapper"},[K&&K.length&&$?To("left"):(0,H.createCommentVNode)(),Z&&Z.length&&$?To("right"):(0,H.createCommentVNode)()])]),(0,H.h)("div",{ref:F,class:"vxe-table--empty-placeholder"},[(0,H.h)("div",{class:"vxe-table--empty-content"},Eo())]),(0,H.h)("div",{class:"vxe-table--border-line"}),(0,H.h)("div",{ref:D,class:"vxe-table--resizable-bar",style:$?{"padding-bottom":`${V}px`}:null}),(0,H.h)(gn,{class:"vxe-table--loading",modelValue:t,icon:ue.icon,text:ue.text},J?{default:()=>J({$table:at,$grid:N})}:{}),U.custom?(0,H.h)((0,H.resolveComponent)("vxe-table-custom-panel"),{ref:w,customStore:Y}):(0,H.createCommentVNode)(),U.filter?(0,H.h)((0,H.resolveComponent)("vxe-table-filter-panel"),{ref:b,filterStore:X}):(0,H.createCommentVNode)(),U.import&&e.importConfig?(0,H.h)((0,H.resolveComponent)("vxe-table-import-panel"),{defaultOptions:u.importParams,storeData:u.importStore}):(0,H.createCommentVNode)(),U.export&&(e.exportConfig||e.printConfig)?(0,H.h)((0,H.resolveComponent)("vxe-table-export-panel"),{defaultOptions:u.exportParams,storeData:u.exportStore}):(0,H.createCommentVNode)(),de?(0,H.h)((0,H.resolveComponent)("vxe-table-menu-panel"),{ref:x}):(0,H.createCommentVNode)(),l?(0,H.h)((0,H.resolveComponent)("vxe-tooltip"),{ref:g,isArrow:!1,enterable:!1}):(0,H.createCommentVNode)(),l?(0,H.h)((0,H.resolveComponent)("vxe-tooltip"),{ref:h,...Q}):(0,H.createCommentVNode)(),l&&e.editRules&&ee.showMessage&&("default"===ee.message?!s:"tooltip"===ee.message)?(0,H.h)((0,H.resolveComponent)("vxe-tooltip"),{ref:v,class:[{"old-cell-valid":O&&"obsolete"===c.cellVaildMode},"vxe-table--valid-error"],..."tooltip"===ee.message||1===B.length?ce:{}}):(0,H.createCommentVNode)()])};return at.renderVN=Fo,(0,H.provide)("xecolgroup",null),(0,H.provide)("$xetable",at),at},render(){return this.renderVN()}});const Or=Object.assign(Rr,{install:function(e){e.component(Rr.name,Rr)}}),Mr=Or;zo.component(Rr.name,Rr);var $r=Or;const Ir=Object.assign(In,{install:function(e){e.component(In.name,In)}}),Dr=Ir;zo.component(In.name,In);var Fr=Ir,Nr=(0,H.defineComponent)({name:"VxePager",props:{size:{type:String,default:()=>c.pager.size||c.size},layouts:{type:Array,default:()=>c.pager.layouts||["PrevJump","PrevPage","Jump","PageCount","NextPage","NextJump","Sizes","Total"]},currentPage:{type:Number,default:1},loading:Boolean,pageSize:{type:Number,default:()=>c.pager.pageSize||10},total:{type:Number,default:0},pagerCount:{type:Number,default:()=>c.pager.pagerCount||7},pageSizes:{type:Array,default:()=>c.pager.pageSizes||[10,15,20,50,100]},align:{type:String,default:()=>c.pager.align},border:{type:Boolean,default:()=>c.pager.border},background:{type:Boolean,default:()=>c.pager.background},perfect:{type:Boolean,default:()=>c.pager.perfect},autoHidden:{type:Boolean,default:()=>c.pager.autoHidden},transfer:{type:Boolean,default:()=>c.pager.transfer},className:[String,Function],iconPrevPage:String,iconJumpPrev:String,iconJumpNext:String,iconNextPage:String,iconJumpMore:String,iconHomePage:String,iconEndPage:String},emits:["update:pageSize","update:currentPage","page-change"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.inject)("$xegrid",null),s=(0,H.reactive)({inpCurrPage:e.currentPage}),u=(0,H.ref)(),d={refElem:u},p={xID:l,props:e,context:t,getRefMaps:()=>d};let m={},f={};const h=(e,t)=>Math.max(Math.ceil(e/t),1),g=(0,H.computed)((()=>h(e.total,e.pageSize))),v=(t,o)=>{n("update:currentPage",o),t&&o!==e.currentPage&&m.dispatchEvent("page-change",{type:"current",pageSize:e.pageSize,currentPage:o},t)},x=(t,o)=>{n("update:currentPage",t),o&&t!==e.currentPage&&m.dispatchEvent("page-change",{type:"current",pageSize:e.pageSize,currentPage:t},o)},b=e=>{const t=e.target,o=a().toInteger(t.value),n=g.value,l=o<=0?1:o>=n?n:o,r=a().toValueString(l);t.value=r,s.inpCurrPage=r,x(l,e)},w=(0,H.computed)((()=>{const{pagerCount:t}=e,o=g.value,n=o>t?t-2:t,l=[];for(let e=0;e<n;e++)l.push(e);return l})),C=(0,H.computed)((()=>Math.floor((e.pagerCount-2)/2))),y=(0,H.computed)((()=>e.pageSizes.map((e=>a().isNumber(e)?{value:e,label:`${c.i18n("vxe.pager.pagesize",[e])}`}:{value:"",label:"",...e})))),T=t=>{const{currentPage:o}=e;o>1&&x(1,t)},E=t=>{const{currentPage:o}=e,n=g.value;o<n&&x(n,t)},S=t=>{const{currentPage:o}=e,n=g.value;o>1&&x(Math.min(n,Math.max(o-1,1)),t)},k=t=>{const{currentPage:o}=e,n=g.value;o<n&&x(Math.min(n,o+1),t)},R=t=>{const o=w.value;x(Math.max(e.currentPage-o.length,1),t)},O=t=>{const o=g.value,n=w.value;x(Math.min(e.currentPage+n.length,o),t)},M=t=>{const{value:o}=t,l=a().toNumber(o),r=h(e.total,l);let i=e.currentPage;i>r&&(i=r,n("update:currentPage",r)),n("update:pageSize",l),m.dispatchEvent("page-change",{type:"size",pageSize:l,currentPage:i})},$=e=>{const t=e.target;s.inpCurrPage=t.value},I=e=>{Jo(e,Xo.ENTER)?b(e):Jo(e,Xo.ARROW_UP)?(e.preventDefault(),k(e)):Jo(e,Xo.ARROW_DOWN)&&(e.preventDefault(),S(e))},D=()=>(0,H.h)("button",{class:["vxe-pager--prev-btn",{"is--disabled":e.currentPage<=1}],type:"button",title:c.i18n("vxe.pager.homePageTitle"),onClick:T},[(0,H.h)("i",{class:["vxe-pager--btn-icon",e.iconHomePage||c.icon.PAGER_HOME]})]),F=()=>(0,H.h)("button",{class:["vxe-pager--prev-btn",{"is--disabled":e.currentPage<=1}],type:"button",title:c.i18n("vxe.pager.prevPageTitle"),onClick:S},[(0,H.h)("i",{class:["vxe-pager--btn-icon",e.iconPrevPage||c.icon.PAGER_PREV_PAGE]})]),N=t=>(0,H.h)(t||"button",{class:["vxe-pager--jump-prev",{"is--fixed":!t,"is--disabled":e.currentPage<=1}],type:"button",title:c.i18n("vxe.pager.prevJumpTitle"),onClick:R},[t?(0,H.h)("i",{class:["vxe-pager--jump-more-icon",e.iconJumpMore||c.icon.PAGER_JUMP_MORE]}):null,(0,H.h)("i",{class:["vxe-pager--jump-icon",e.iconJumpPrev||c.icon.PAGER_JUMP_PREV]})]),P=t=>{const o=g.value;return(0,H.h)(t||"button",{class:["vxe-pager--jump-next",{"is--fixed":!t,"is--disabled":e.currentPage>=o}],type:"button",title:c.i18n("vxe.pager.nextJumpTitle"),onClick:O},[t?(0,H.h)("i",{class:["vxe-pager--jump-more-icon",e.iconJumpMore||c.icon.PAGER_JUMP_MORE]}):null,(0,H.h)("i",{class:["vxe-pager--jump-icon",e.iconJumpNext||c.icon.PAGER_JUMP_NEXT]})])},L=()=>{const t=g.value;return(0,H.h)("button",{class:["vxe-pager--next-btn",{"is--disabled":e.currentPage>=t}],type:"button",title:c.i18n("vxe.pager.nextPageTitle"),onClick:k},[(0,H.h)("i",{class:["vxe-pager--btn-icon",e.iconNextPage||c.icon.PAGER_NEXT_PAGE]})])},A=()=>{const t=g.value;return(0,H.h)("button",{class:["vxe-pager--prev-btn",{"is--disabled":e.currentPage>=t}],type:"button",title:c.i18n("vxe.pager.endPageTitle"),onClick:E},[(0,H.h)("i",{class:["vxe-pager--btn-icon",e.iconEndPage||c.icon.PAGER_END]})])},V=t=>{const{currentPage:o,pagerCount:n}=e,l=[],r=g.value,a=w.value,i=C.value,s=r>n,c=s&&o>i+1,u=s&&o<r-i;let d=1;return s&&(d=o>=r-i?Math.max(r-a.length+1,1):Math.max(o-i,1)),t&&c&&l.push((0,H.h)("button",{class:"vxe-pager--num-btn",type:"button",onClick:e=>v(e,1)},1),N("span")),a.forEach(((e,t)=>{const n=d+t;n<=r&&l.push((0,H.h)("button",{key:n,class:["vxe-pager--num-btn",{"is--active":o===n}],type:"button",onClick:e=>v(e,n)},n))})),t&&u&&l.push(P("button"),(0,H.h)("button",{class:"vxe-pager--num-btn",type:"button",onClick:e=>v(e,r)},r)),(0,H.h)("span",{class:"vxe-pager--btn-wrapper"},l)},_=()=>V(!0),j=()=>{const t=y.value;return(0,H.h)(Fr,{class:"vxe-pager--sizes",modelValue:e.pageSize,placement:"top",transfer:e.transfer,options:t,onChange:M})},z=e=>(0,H.h)("span",{class:"vxe-pager--jump"},[e?(0,H.h)("span",{class:"vxe-pager--goto-text"},c.i18n("vxe.pager.goto")):null,(0,H.h)("input",{class:"vxe-pager--goto",value:s.inpCurrPage,type:"text",autocomplete:"off",onInput:$,onKeydown:I,onBlur:b}),e?(0,H.h)("span",{class:"vxe-pager--classifier-text"},c.i18n("vxe.pager.pageClassifier")):null]),W=()=>z(!0),q=()=>{const e=g.value;return(0,H.h)("span",{class:"vxe-pager--count"},[(0,H.h)("span",{class:"vxe-pager--separator"}),(0,H.h)("span",e)])},U=()=>(0,H.h)("span",{class:"vxe-pager--total"},c.i18n("vxe.pager.total",[e.total]));m={dispatchEvent(e,t,o){n(e,Object.assign({$pager:p,$event:o},t))},homePage(){return T(),(0,H.nextTick)()},endPage(){return E(),(0,H.nextTick)()},prevPage(){return S(),(0,H.nextTick)()},nextPage(){return k(),(0,H.nextTick)()},prevJump(){return R(),(0,H.nextTick)()},nextJump(){return O(),(0,H.nextTick)()}},f={handlePrevPage:S,handleNextPage:k,handlePrevJump:R,handleNextJump:O},Object.assign(p,m,f),(0,H.watch)((()=>e.currentPage),(e=>{s.inpCurrPage=e}));const G=()=>{const{align:t,layouts:n,className:l}=e,s=[],c=r.value,d=g.value;return o.left&&s.push((0,H.h)("span",{class:"vxe-pager--left-wrapper"},o.left({$grid:i}))),n.forEach((e=>{let t;switch(e){case"Home":t=D;break;case"PrevJump":t=N;break;case"PrevPage":t=F;break;case"Number":t=V;break;case"JumpNumber":t=_;break;case"NextPage":t=L;break;case"NextJump":t=P;break;case"End":t=A;break;case"Sizes":t=j;break;case"FullJump":t=W;break;case"Jump":t=z;break;case"PageCount":t=q;break;case"Total":t=U;break}t?s.push(t()):B("vxe.error.notProp",[`layouts -> ${e}`])})),o.right&&s.push((0,H.h)("span",{class:"vxe-pager--right-wrapper"},o.right({$grid:i}))),(0,H.h)("div",{ref:u,class:["vxe-pager",l?a().isFunction(l)?l({$pager:p}):l:"",{[`size--${c}`]:c,[`align--${t}`]:t,"is--border":e.border,"is--background":e.background,"is--perfect":e.perfect,"is--hidden":e.autoHidden&&1===d,"is--loading":e.loading}]},[(0,H.h)("div",{class:"vxe-pager--wrapper"},s)])};return p.renderVN=G,p},render(){return this.renderVN()}});const Pr=Object.assign(Nr,{install:function(e){e.component(Nr.name,Nr)}}),Lr=Pr;zo.component(Nr.name,Nr);var Ar=Pr;const Vr=Object.assign(mn,{install(e){e.component(mn.name,mn)}}),Br=Vr;zo.component(mn.name,mn);var _r=Vr,jr=(0,H.defineComponent)({name:"VxeToolbar",props:{loading:Boolean,refresh:[Boolean,Object],import:[Boolean,Object],export:[Boolean,Object],print:[Boolean,Object],zoom:[Boolean,Object],custom:[Boolean,Object],buttons:{type:Array,default:()=>c.toolbar.buttons},tools:{type:Array,default:()=>c.toolbar.tools},perfect:{type:Boolean,default:()=>c.toolbar.perfect},size:{type:String,default:()=>c.toolbar.size||c.size},className:[String,Function]},emits:["button-click","tool-click"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.reactive)({isRefresh:!1,columns:[]}),s=(0,H.ref)(),u={refElem:s},d={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>u};let p={};const m=(0,H.inject)("$xegrid",null);let f;const h=(0,H.ref)(0),g=(0,H.computed)((()=>Object.assign({},c.toolbar.refresh,e.refresh))),v=(0,H.computed)((()=>Object.assign({},c.toolbar.import,e.import))),x=(0,H.computed)((()=>Object.assign({},c.toolbar.export,e.export))),b=(0,H.computed)((()=>Object.assign({},c.toolbar.print,e.print))),w=(0,H.computed)((()=>Object.assign({},c.toolbar.zoom,e.zoom))),C=(0,H.computed)((()=>Object.assign({},c.toolbar.custom,e.custom))),y=(0,H.computed)((()=>{if((h.value||f)&&f){const{computeCustomOpts:e}=f.getComputeMaps();return e.value}return{}})),T=(0,H.computed)((()=>{const e=y.value;return e.trigger})),E=()=>{if(f)return!0;B("vxe.error.barUnableLink")},S=({$event:e})=>{f&&(f.triggerCustomEvent?f.triggerCustomEvent(e):B("vxe.error.reqModule",["VxeTableCustomModule"]))},k=({$event:e})=>{f?f.customOpenEvent(e):B("vxe.error.reqModule",["VxeTableCustomModule"])},R=({$event:e})=>{const{customStore:t}=f.reactData;t.activeBtn=!1,setTimeout((()=>{t.activeBtn||t.activeWrapper||f.customColseEvent(e)}),350)},O=e=>{const{isRefresh:t}=i,o=g.value;if(!t){const t=o.queryMethod||o.query;if(t){i.isRefresh=!0;try{Promise.resolve(t({})).catch((e=>e)).then((()=>{i.isRefresh=!1}))}catch(n){i.isRefresh=!1}}else m&&(i.isRefresh=!0,m.triggerToolbarCommitEvent({code:o.code||"reload"},e).catch((e=>e)).then((()=>{i.isRefresh=!1})))}},M=e=>{m&&m.triggerZoomEvent(e)},$=(e,t)=>{const{code:o}=t;if(o)if(m)m.triggerToolbarBtnEvent(t,e);else{const n=No.commands.get(o),l={code:o,button:t,$table:f,$grid:m,$event:e};n&&(n.commandMethod?n.commandMethod(l):B("vxe.error.notCommands",[o])),d.dispatchEvent("button-click",l,e)}},I=(e,t)=>{const{code:o}=t;if(o)if(m)m.triggerToolbarTolEvent(t,e);else{const n=No.commands.get(o),l={code:o,tool:t,$table:f,$grid:m,$event:e};n&&(n.commandMethod?n.commandMethod(l):B("vxe.error.notCommands",[o])),d.dispatchEvent("tool-click",l,e)}},D=()=>{E()&&f.openImport()},F=()=>{E()&&f.openExport()},N=()=>{E()&&f.openPrint()},P=(e,t)=>{const{dropdowns:o}=e,n=[];return o?o.map(((e,o)=>!1===e.visible?(0,H.createCommentVNode)():(0,H.h)(_r,{key:o,disabled:e.disabled,loading:e.loading,type:e.type,icon:e.icon,circle:e.circle,round:e.round,status:e.status,content:e.name,onClick:o=>t?$(o,e):I(o,e)}))):n},L=()=>{const{buttons:t}=e,n=o.buttons;if(n)return lt(n({$grid:m,$table:f}));const l=[];return t&&t.forEach((e=>{const{dropdowns:t,buttonRender:o}=e;if(!1!==e.visible){const n=o?No.renderer.get(o.name):null;if(o&&n&&n.renderToolbarButton){const t=n.toolbarButtonClassName,r={$grid:m,$table:f,button:e};l.push((0,H.h)("span",{class:["vxe-button--item",t?a().isFunction(t)?t(r):t:""]},lt(n.renderToolbarButton(o,r))))}else l.push((0,H.h)(_r,{disabled:e.disabled,loading:e.loading,type:e.type,icon:e.icon,circle:e.circle,round:e.round,status:e.status,content:e.name,destroyOnClose:e.destroyOnClose,placement:e.placement,transfer:e.transfer,onClick:t=>$(t,e)},t&&t.length?{dropdowns:()=>P(e,!0)}:{}))}})),l},A=()=>{const{tools:t}=e,n=o.tools;if(n)return lt(n({$grid:m,$table:f}));const l=[];return t&&t.forEach(((e,t)=>{const{dropdowns:o,toolRender:n}=e;if(!1!==e.visible){const r=n?n.name:null,i=n?No.renderer.get(r):null;if(n&&i&&i.renderToolbarTool){const t=i.toolbarToolClassName,o={$grid:m,$table:f,tool:e};l.push((0,H.h)("span",{key:r,class:["vxe-tool--item",t?a().isFunction(t)?t(o):t:""]},lt(i.renderToolbarTool(n,o))))}else l.push((0,H.h)(_r,{key:t,disabled:e.disabled,loading:e.loading,type:e.type,icon:e.icon,circle:e.circle,round:e.round,status:e.status,content:e.name,destroyOnClose:e.destroyOnClose,placement:e.placement,transfer:e.transfer,onClick:t=>I(t,e)},o&&o.length?{dropdowns:()=>P(e,!1)}:{}))}})),l},_=()=>{const e=v.value;return(0,H.h)(_r,{key:"import",circle:!0,icon:e.icon||c.icon.TOOLBAR_TOOLS_IMPORT,title:c.i18n("vxe.toolbar.import"),onClick:D})},j=()=>{const e=x.value;return(0,H.h)(_r,{key:"export",circle:!0,icon:e.icon||c.icon.TOOLBAR_TOOLS_EXPORT,title:c.i18n("vxe.toolbar.export"),onClick:F})},z=()=>{const e=b.value;return(0,H.h)(_r,{key:"print",circle:!0,icon:e.icon||c.icon.TOOLBAR_TOOLS_PRINT,title:c.i18n("vxe.toolbar.print"),onClick:N})},W=()=>{const e=g.value;return(0,H.h)(_r,{key:"refresh",circle:!0,icon:i.isRefresh?e.iconLoading||c.icon.TOOLBAR_TOOLS_REFRESH_LOADING:e.icon||c.icon.TOOLBAR_TOOLS_REFRESH,title:c.i18n("vxe.toolbar.refresh"),onClick:O})},q=()=>{const e=w.value;return m?(0,H.h)(_r,{key:"zoom",circle:!0,icon:m.isMaximized()?e.iconOut||c.icon.TOOLBAR_TOOLS_MINIMIZE:e.iconIn||c.icon.TOOLBAR_TOOLS_FULLSCREEN,title:c.i18n("vxe.toolbar.zoom"+(m.isMaximized()?"Out":"In")),onClick:M}):(0,H.createCommentVNode)()},U=()=>{const e=C.value,t=T.value,o={};return"manual"===t||("hover"===t?(o.onMouseenter=k,o.onMouseleave=R):o.onClick=S),(0,H.h)(_r,{key:"custom",circle:!0,icon:e.icon||c.icon.TOOLBAR_TOOLS_CUSTOM,title:c.i18n("vxe.toolbar.custom"),className:"vxe-toolbar-custom-target",...o})};p={dispatchEvent(e,t,o){n(e,Object.assign({$toolbar:d,$event:o},t))},syncUpdate(e){const{collectColumn:t}=e;f=e.$table,i.columns=t,h.value++}},Object.assign(d,p),(0,H.nextTick)((()=>{const{refresh:t}=e,o=g.value,n=o.queryMethod||o.query;!t||m||n||V("vxe.error.notFunc",["queryMethod"]);const l=C.value;l.isFooter&&V("vxe.error.delProp",["toolbar.custom.isFooter","table.custom-config.showFooter"]),l.showFooter&&V("vxe.error.delProp",["toolbar.custom.showFooter","table.custom-config.showFooter"]),l.immediate&&V("vxe.error.delProp",["toolbar.custom.immediate","table.custom-config.immediate"]),l.trigger&&V("vxe.error.delProp",["toolbar.custom.trigger","table.custom-config.trigger"])}));const G=()=>{const{perfect:t,loading:o,refresh:n,zoom:l,custom:i,className:c}=e,u=r.value;return(0,H.h)("div",{ref:s,class:["vxe-toolbar",c?a().isFunction(c)?c({$toolbar:d}):c:"",{[`size--${u}`]:u,"is--perfect":t,"is--loading":o}]},[(0,H.h)("div",{class:"vxe-buttons--wrapper"},L()),(0,H.h)("div",{class:"vxe-tools--wrapper"},A()),(0,H.h)("div",{class:"vxe-tools--operate"},[e.import?_():(0,H.createCommentVNode)(),e.export?j():(0,H.createCommentVNode)(),e.print?z():(0,H.createCommentVNode)(),n?W():(0,H.createCommentVNode)(),l&&m?q():(0,H.createCommentVNode)(),i?U():(0,H.createCommentVNode)()])])};return d.renderVN=G,d},render(){return this.renderVN()}});const Hr=Object.assign(jr,{install:function(e){e.component(jr.name,jr)}}),zr=Hr;zo.component(jr.name,jr);var Wr=Hr;class qr{constructor(e,t){Object.assign(this,{id:a().uniqueId("item_"),title:t.title,field:t.field,span:t.span,align:t.align,titleAlign:t.titleAlign,titleWidth:t.titleWidth,titleColon:t.titleColon,titleAsterisk:t.titleAsterisk,titlePrefix:t.titlePrefix,titleSuffix:t.titleSuffix,titleOverflow:t.titleOverflow,showTitle:t.showTitle,resetValue:t.resetValue,visibleMethod:t.visibleMethod,visible:t.visible,folding:t.folding,collapseNode:t.collapseNode,className:t.className,contentClassName:t.contentClassName,contentStyle:t.contentStyle,titleClassName:t.titleClassName,titleStyle:t.titleStyle,itemRender:t.itemRender,rules:t.rules,showError:!1,errRule:null,slots:t.slots,children:[]})}update(e,t){this[e]=t}}function Ur(e){return e instanceof qr}function Gr(e,t){return Ur(t)?t:new qr(e,t)}function Xr(e,t){return t?a().isString(t)?e.getItemByField(t):t:null}function Yr(e,t){const{reactData:o}=e,{collapseAll:n}=o,{folding:l,visible:r}=t;return!1===r||l&&n}function Kr(e,t){let{visibleMethod:o,itemRender:n,visible:l,field:r}=t;if(!1===l)return l;const a=Y(n)?No.renderer.get(n.name):null;if(!o&&a&&a.itemVisibleMethod&&(o=a.itemVisibleMethod),!o)return!0;const{data:i}=e.props;return o({data:i,field:r,property:r,item:t,$form:e,$grid:e.xegrid})}function Zr(e,t){Object.keys(e).forEach((o=>{(0,H.watch)((()=>e[o]),(e=>{t.update(o,e)}))}))}function Jr(e,t,o,n){const{reactData:l}=e,{staticItems:r}=l,i=t.parentNode,s=n?n.formItem:null,c=s?s.children:r;i&&(c.splice(a().arrayIndexOf(i.children,t),0,o),l.staticItems=r.slice(0))}function Qr(e,t){const{reactData:o}=e,{staticItems:n}=o,l=a().findIndexOf(n,(e=>e.id===t.id));l>-1&&n.splice(l,1),o.staticItems=n.slice(0)}const ea=Object.assign(Pl,{install:function(e){No.tooltip=!0,e.component(Pl.name,Pl)}}),ta=ea;zo.component(Pl.name,Pl);var oa=ea;function na(e){return(0,H.h)("span",{class:"vxe-form--item-title-prefix"},[(0,H.h)("i",{class:e.icon||c.icon.FORM_PREFIX})])}function la(e){return(0,H.h)("span",{class:"vxe-form--item-title-suffix"},[(0,H.h)("i",{class:e.icon||c.icon.FORM_SUFFIX})])}function ra(e,t){const{data:o}=e.props,{computeTooltipOpts:n}=e.getComputeMaps(),{slots:l,field:r,itemRender:a,titlePrefix:i,titleSuffix:s}=t,c=n.value,u=Y(a)?No.renderer.get(a.name):null,d={data:o,field:r,property:r,item:t,$form:e,$grid:e.xegrid},p=l?l.title:null,m=[],f=[];i&&f.push(i.content||i.message?(0,H.h)(oa,{...c,...i,content:te(i.content||i.message)},{default:()=>na(i)}):na(i)),f.push((0,H.h)("span",{class:"vxe-form--item-title-label"},u&&u.renderItemTitle?lt(u.renderItemTitle(a,d)):p?e.callSlot(p,d):te(t.title))),m.push((0,H.h)("div",{class:"vxe-form--item-title-content"},f));const h=[];return s&&h.push(s.content||s.message?(0,H.h)(oa,{...c,...s,content:te(s.content||s.message)},{default:()=>la(s)}):la(s)),m.push((0,H.h)("div",{class:"vxe-form--item-title-postfix"},h)),m}const aa=(0,H.defineComponent)({name:"VxeFormConfigItem",props:{itemConfig:Object},setup(e){const t=(0,H.inject)("$xeform",{}),o={itemConfig:e.itemConfig};(0,H.provide)("$xeformiteminfo",o),(0,H.provide)("$xeformgather",null);const n=()=>{const{reactData:o}=t,{data:n,rules:l,span:r,align:i,titleAlign:s,titleWidth:u,titleColon:d,titleAsterisk:p,titleOverflow:m,vertical:f}=t.props,{computeValidOpts:h}=t.getComputeMaps(),g=e.itemConfig,{collapseAll:v}=o,x=h.value,{slots:b,title:w,visible:C,folding:y,field:T,collapseNode:E,itemRender:S,showError:k,errRule:R,className:O,titleOverflow:M,vertical:$,children:I,showTitle:D,contentClassName:F,contentStyle:N,titleClassName:P,titleStyle:L}=g,A=Y(S)?No.renderer.get(S.name):null,V=A?A.itemClassName:"",B=A?A.itemStyle:null,_=A?A.itemContentClassName:"",j=A?A.itemContentStyle:null,z=A?A.itemTitleClassName:"",W=A?A.itemTitleStyle:null,q=b?b.default:null,U=b?b.title:null,G=g.span||r,X=g.align||i,K=a().eqNull(g.titleAlign)?s:g.titleAlign,Z=a().eqNull(g.titleWidth)?u:g.titleWidth,J=a().eqNull(g.titleColon)?d:g.titleColon,Q=a().eqNull(g.titleAsterisk)?p:g.titleAsterisk,ee=a().isUndefined(M)||a().isNull(M)?m:M,oe=a().isUndefined($)||a().isNull($)?f:$,ne="ellipsis"===ee,le="title"===ee,re=!0===ee||"tooltip"===ee,ae=le||re||ne,ie={data:n,field:T,property:T,item:g,$form:t,$grid:t.xegrid};if(!1===C)return(0,H.createCommentVNode)();let se=!1;if(l){const e=l[T];e&&(se=e.some((e=>e.required)))}const ce=I&&I.length>0;if(ce){const e=I.map(((e,t)=>(0,H.h)(aa,{key:t,itemConfig:e})));return e.length?(0,H.h)("div",{class:["vxe-form--gather vxe-form--item-row",g.id,G?`vxe-form--item-col_${G} is--span`:"",O?a().isFunction(O)?O(ie):O:""]},e):(0,H.createCommentVNode)()}let ue=[];q?ue=t.callSlot(q,ie):A&&A.renderItemContent?ue=lt(A.renderItemContent(S,ie)):T&&(ue=[a().toValueString(a().get(n,T))]),E&&ue.push((0,H.h)("div",{class:"vxe-form--item-trigger-node",onClick:t.toggleCollapseEvent},[(0,H.h)("span",{class:"vxe-form--item-trigger-text"},v?c.i18n("vxe.form.unfolding"):c.i18n("vxe.form.folding")),(0,H.h)("i",{class:["vxe-form--item-trigger-icon",v?c.icon.FORM_FOLDING:c.icon.FORM_UNFOLDING]})])),R&&x.showMessage&&ue.push((0,H.h)("div",{class:"vxe-form--item-valid",style:R.maxWidth?{width:`${R.maxWidth}px`}:null},R.content));const de=re?{onMouseenter(e){t.triggerTitleTipEvent(e,ie)},onMouseleave:t.handleTitleTipLeaveEvent}:{};return(0,H.h)("div",{class:["vxe-form--item",g.id,G?`vxe-form--item-col_${G} is--span`:"",O?a().isFunction(O)?O(ie):O:"",V?a().isFunction(V)?V(ie):V:"",{"is--title":w,"is--colon":J,"is--vertical":oe,"is--asterisk":Q,"is--required":se,"is--hidden":y&&v,"is--active":Kr(t,g),"is--error":k}],style:a().isFunction(B)?B(ie):B},[(0,H.h)("div",{class:"vxe-form--item-inner"},[!1!==D&&(w||U)?(0,H.h)("div",{class:["vxe-form--item-title",K?`align--${K}`:"",ae?"is--ellipsis":"",z?a().isFunction(z)?z(ie):z:"",P?a().isFunction(P)?P(ie):P:""],style:Object.assign({},a().isFunction(W)?W(ie):W,a().isFunction(L)?L(ie):L,Z?{width:isNaN(Z)?Z:`${Z}px`}:null),title:le?te(w):null,...de},ra(t,g)):null,(0,H.h)("div",{class:["vxe-form--item-content",X?`align--${X}`:"",_?a().isFunction(_)?_(ie):_:"",F?a().isFunction(F)?F(ie):F:""],style:Object.assign({},a().isFunction(j)?j(ie):j,a().isFunction(N)?N(ie):N)},ue)])])},l={renderVN:n};return l},render(){return this.renderVN()}});var ia=aa;class sa{constructor(e){Object.assign(this,{$options:e,required:e.required,min:e.min,max:e.min,type:e.type,pattern:e.pattern,validator:e.validator,trigger:e.trigger,maxWidth:e.maxWidth})}get content(){return te(this.$options.content||this.$options.message)}get message(){return this.content}}const ca=(e,t)=>{const{type:o,min:n,max:l,pattern:r}=e,i="number"===o,s=i?a().toNumber(t):a().getSize(t);return!(!i||!isNaN(t))||(!a().eqNull(n)&&s<a().toNumber(n)||(!a().eqNull(l)&&s>a().toNumber(l)||!(!r||(a().isRegExp(r)?r:new RegExp(r)).test(t))))};function ua(e,t){return a().isArray(e)&&(t=[]),t}var da=(0,H.defineComponent)({name:"VxeForm",props:{collapseStatus:{type:Boolean,default:!0},loading:Boolean,data:Object,size:{type:String,default:()=>c.form.size||c.size},span:{type:[String,Number],default:()=>c.form.span},align:{type:String,default:()=>c.form.align},titleAlign:{type:String,default:()=>c.form.titleAlign},titleWidth:{type:[String,Number],default:()=>c.form.titleWidth},titleColon:{type:Boolean,default:()=>c.form.titleColon},titleAsterisk:{type:Boolean,default:()=>c.form.titleAsterisk},titleOverflow:{type:[Boolean,String],default:null},vertical:{type:Boolean,default:null},className:[String,Function],readonly:Boolean,items:Array,rules:Object,preventSubmit:{type:Boolean,default:()=>c.form.preventSubmit},validConfig:Object,tooltipConfig:Object,customLayout:{type:Boolean,default:()=>c.form.customLayout}},emits:["update:collapseStatus","collapse","toggle-collapse","submit","submit-invalid","reset"],setup(e,t){const o=No.tooltip,{slots:n,emit:l}=t,r=a().uniqueId(),i=pn(e),s=(0,H.reactive)({collapseAll:e.collapseStatus,staticItems:[],formItems:[]}),u=(0,H.reactive)({tooltipTimeout:null,tooltipStore:{item:null,visible:!1}}),d=(0,H.inject)("$xegrid",null),p=(0,H.ref)(),m=(0,H.ref)();let f={};const h=(0,H.computed)((()=>Object.assign({},c.form.validConfig,e.validConfig))),g=(0,H.computed)((()=>Object.assign({},c.tooltip,c.form.tooltipConfig,e.tooltipConfig))),v={refElem:p},x={computeSize:i,computeValidOpts:h,computeTooltipOpts:g},b={xID:r,props:e,context:t,reactData:s,xegrid:d,getRefMaps:()=>v,getComputeMaps:()=>x},w=(e,t)=>e&&(a().isString(e)&&(e=n[e]||null),a().isFunction(e))?lt(e(t)):[],C=e=>(e.length&&e.forEach((e=>{e.slots&&a().each(e.slots,(e=>{a().isFunction(e)||n[e]||B("vxe.error.notSlot",[e])}))})),s.staticItems=a().mapTree(e,(e=>Gr(b,e)),{children:"children"}),(0,H.nextTick)()),y=()=>{const e=[];return a().eachTree(s.formItems,(t=>{e.push(t)}),{children:"children"}),e},T=e=>{const t=a().findTree(s.formItems,(t=>t.field===e),{children:"children"});return t?t.item:null},E=()=>s.collapseAll,S=()=>{const e=!E();return s.collapseAll=e,l("update:collapseStatus",e),(0,H.nextTick)()},k=t=>{S();const o=E();f.dispatchEvent("toggle-collapse",{status:o,collapse:o,data:e.data},t),f.dispatchEvent("collapse",{status:o,collapse:o,data:e.data},t)},R=e=>{if(e){let t=e;a().isArray(e)||(t=[e]),t.forEach((e=>{if(e){const t=Xr(b,e);t&&(t.showError=!1)}}))}else y().forEach((e=>{e.showError=!1}));return(0,H.nextTick)()},O=()=>{const{data:t}=e,o=y();return t&&o.forEach((e=>{const{field:o,resetValue:n,itemRender:l}=e;if(Y(l)){const r=No.renderer.get(l.name);r&&r.itemResetMethod?r.itemResetMethod({data:t,field:o,property:o,item:e,$form:b,$grid:b.xegrid}):o&&a().set(t,o,null===n?ua(a().get(t,o),void 0):a().clone(n,!0))}})),R()},M=t=>{t.preventDefault(),O(),f.dispatchEvent("reset",{data:e.data},t)},$=e=>{const t=p.value;for(let o=0;o<e.length;o++){const n=e[o],l=T(n);if(l&&Y(l.itemRender)){const{itemRender:e}=l,n=No.renderer.get(e.name);let r=null;if(o||ke(t.querySelector(`.${l.id}`)),e.autofocus&&(r=t.querySelector(`.${l.id} ${e.autofocus}`)),!r&&n&&n.autofocus&&(r=t.querySelector(`.${l.id} ${n.autofocus}`)),r){r.focus();break}}}},I=(t,o,n)=>{const{data:l,rules:r}=e,i={};return a().isArray(o)||(o=[o]),Promise.all(o.map((e=>{const o=[],s=[];if(e&&r){const i=a().get(r,e);if(i){const r=a().isUndefined(n)?a().get(l,e):n;i.forEach((n=>{const{type:c,trigger:u,required:d,validator:p}=n;if("all"===t||!u||t===u)if(p){const t={itemValue:r,rule:n,rules:i,data:l,field:e,property:e,$form:b};let c;if(a().isString(p)){const e=No.validators.get(p);e?e.itemValidatorMethod?c=e.itemValidatorMethod(t):V("vxe.error.notValidators",[p]):B("vxe.error.notValidators",[p])}else c=p(t);c&&(a().isError(c)?o.push(new sa({type:"custom",trigger:u,content:c.message,rule:new sa(n)})):c.catch&&s.push(c.catch((e=>{o.push(new sa({type:"custom",trigger:u,content:e?e.message:n.content||n.message,rule:new sa(n)}))}))))}else{const e="array"===c,t=a().isArray(r);let l=!0;l=e||t?!t||!r.length:a().isString(r)?ne(r.trim()):ne(r),(d?l||ca(n,r):!l&&ca(n,r))&&o.push(new sa(n))}}))}}return Promise.all(s).then((()=>{o.length&&(i[e]=o.map((t=>({$form:b,rule:t,data:l,field:e,property:e}))))}))}))).then((()=>{if(!a().isEmpty(i))return Promise.reject(i)}))};let D;const F=(t,o,n)=>{const{data:l,rules:r}=e,a=h.value,i={},s=[],c=[];return clearTimeout(D),l&&r?(t.forEach((e=>{const{field:t}=e;t&&!Yr(b,e)&&Kr(b,e)&&c.push(I(o||"all",t).then((()=>{e.errRule=null})).catch((o=>{const n=o[t];return i[t]||(i[t]=[]),i[t].push(n),s.push(t),e.errRule=n[0].rule,Promise.reject(n)})))})),Promise.all(c).then((()=>{n&&n()})).catch((()=>new Promise((e=>{D=window.setTimeout((()=>{t.forEach((e=>{e.errRule&&(e.showError=!0)}))}),20),!1!==a.autoPos&&(0,H.nextTick)((()=>{$(s)})),n?(n(i),e()):e(i)}))))):(n&&n(),Promise.resolve())},N=e=>(R(),F(y(),"",e)),P=(e,t)=>{let o=[];return o=a().isArray(e)?e:[e],F(o.map((e=>Xr(b,e))),"",t)},L=t=>{t.preventDefault(),e.preventSubmit||(R(),F(y()).then((o=>{o?f.dispatchEvent("submit-invalid",{data:e.data,errMap:o},t):f.dispatchEvent("submit",{data:e.data},t)})))},A=()=>{const{tooltipStore:e}=u,t=m.value;return e.visible&&(Object.assign(e,{item:null,visible:!1}),t&&t.close()),(0,H.nextTick)()},_=(e,t)=>{const{item:o}=t,{tooltipStore:n}=u,l=m.value,r=e.currentTarget.children[0],a=(r.textContent||"").trim(),i=r.scrollWidth>r.clientWidth;clearTimeout(u.tooltipTimeout),n.item!==o&&A(),a&&i&&(Object.assign(n,{item:o,visible:!0}),l&&l.open(r,a))},j=()=>{const e=g.value;let t=m.value;t&&t.setActived(!1),e.enterable?u.tooltipTimeout=setTimeout((()=>{t=m.value,t&&!t.isActived()&&A()}),e.leaveDelay):A()},z=(e,t,o)=>t?I(e?["blur"].includes(e.type)?"blur":"change":"all",t,o).then((()=>{R(t)})).catch((e=>{const o=e[t],n=T(t);o&&n&&(n.showError=!0,n.errRule=o[0].rule)})):(0,H.nextTick)(),W=(e,t)=>{const{field:o}=e;return z(new Event("change"),o,t)};f={dispatchEvent(e,t,o){l(e,Object.assign({$form:b,$grid:d,$event:o},t))},reset:O,validate:N,validateField:P,clearValidate:R,updateStatus:W,toggleCollapse:S,getItems:y,getItemByField:T,closeTooltip:A};const q={callSlot:w,triggerItemEvent:z,toggleCollapseEvent:k,triggerTitleTipEvent:_,handleTitleTipLeaveEvent:j};Object.assign(b,f,q);const U=(0,H.ref)(0);(0,H.watch)((()=>s.staticItems.length),(()=>{U.value++})),(0,H.watch)((()=>s.staticItems),(()=>{U.value++})),(0,H.watch)(U,(()=>{s.formItems=s.staticItems}));const G=(0,H.ref)(0);(0,H.watch)((()=>e.items?e.items.length:-1),(()=>{G.value++})),(0,H.watch)((()=>e.items),(()=>{G.value++})),(0,H.watch)(G,(()=>{C(e.items||[])})),(0,H.watch)((()=>e.collapseStatus),(e=>{s.collapseAll=!!e}));const X=()=>{const{loading:t,className:l,data:r,customLayout:c}=e,{formItems:u}=s,d=i.value,f=g.value,h=n.default;return(0,H.h)("form",{ref:p,class:["vxe-form",l?a().isFunction(l)?l({items:u,data:r,$form:b}):l:"",{[`size--${d}`]:d,"is--loading":t}],onSubmit:L,onReset:M},[(0,H.h)("div",{class:"vxe-form--wrapper vxe-form--item-row"},c?h?h({}):[]:u.map(((e,t)=>(0,H.h)(ia,{key:t,itemConfig:e})))),(0,H.h)("div",{class:"vxe-form-slots",ref:"hideItem"},c?[]:h?h({}):[]),(0,H.h)(gn,{class:"vxe-form--loading",modelValue:t}),o?(0,H.h)(oa,{ref:m,...f}):(0,H.createCommentVNode)()])};return b.renderVN=X,e.items&&C(e.items),(0,H.provide)("$xeform",b),(0,H.provide)("$xeformgather",null),(0,H.provide)("$xeformitem",null),(0,H.provide)("$xeformiteminfo",null),b},render(){return this.renderVN()}});const pa=Object.assign(da,{install(e){e.component(da.name,da)}}),ma=pa;zo.component(da.name,da);var fa=pa;const ha=Object.keys(wr),ga=["clearAll","syncData","updateData","loadData","reloadData","reloadRow","loadColumn","reloadColumn","getRowNode","getColumnNode","getRowIndex","getVTRowIndex","getVMRowIndex","getColumnIndex","getVTColumnIndex","getVMColumnIndex","createData","createRow","revertData","clearData","isInsertByRow","isUpdateByRow","getColumns","getColumnById","getColumnByField","getTableColumn","getData","getCheckboxRecords","getParentRow","getRowSeq","getRowById","getRowid","getTableData","setColumnFixed","clearColumnFixed","setColumnWidth","getColumnWidth","hideColumn","showColumn","resetColumn","refreshColumn","refreshScroll","recalculate","closeTooltip","isAllCheckboxChecked","isAllCheckboxIndeterminate","getCheckboxIndeterminateRecords","setCheckboxRow","isCheckedByCheckboxRow","isIndeterminateByCheckboxRow","toggleCheckboxRow","setAllCheckboxRow","getRadioReserveRecord","clearRadioReserve","getCheckboxReserveRecords","clearCheckboxReserve","toggleAllCheckboxRow","clearCheckboxRow","setCurrentRow","isCheckedByRadioRow","setRadioRow","clearCurrentRow","clearRadioRow","getCurrentRecord","getRadioRecord","getCurrentColumn","setCurrentColumn","clearCurrentColumn","setPendingRow","togglePendingRow","getPendingRecords","clearPendingRow","sort","clearSort","isSort","getSortColumns","closeFilter","isFilter","isActiveFilterByColumn","isRowExpandLoaded","clearRowExpandLoaded","reloadRowExpand","reloadRowExpand","toggleRowExpand","setAllRowExpand","setRowExpand","isExpandByRow","isRowExpandByRow","clearRowExpand","clearRowExpandReserve","getRowExpandRecords","getTreeExpandRecords","isTreeExpandLoaded","clearTreeExpandLoaded","reloadTreeExpand","reloadTreeChilds","toggleTreeExpand","setAllTreeExpand","setTreeExpand","isTreeExpandByRow","clearTreeExpand","clearTreeExpandReserve","getScroll","scrollTo","scrollToRow","scrollToColumn","clearScroll","updateFooter","updateStatus","setMergeCells","removeInsertRow","removeMergeCells","getMergeCells","clearMergeCells","setMergeFooterItems","removeMergeFooterItems","getMergeFooterItems","clearMergeFooterItems","openTooltip","focus","blur","connect"],va=[...Cr,"page-change","form-submit","form-submit-invalid","form-reset","form-collapse","form-toggle-collapse","proxy-query","proxy-delete","proxy-save","toolbar-button-click","toolbar-tool-click","zoom"];var xa=(0,H.defineComponent)({name:"VxeGrid",props:{...wr,layouts:Array,columns:Array,pagerConfig:Object,proxyConfig:Object,toolbarConfig:Object,formConfig:Object,zoomConfig:Object,size:{type:String,default:()=>c.grid.size||c.size}},emits:va,setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=(0,H.getCurrentInstance)(),i=pn(e),s=(0,H.reactive)({tableLoading:!1,proxyInited:!1,isZMax:!1,tableData:[],filterData:[],formData:{},sortData:[],tZindex:0,tablePage:{total:0,pageSize:c.pager.pageSize||10,currentPage:1}}),u=(0,H.ref)(),d=(0,H.ref)(),p=(0,H.ref)(),m=(0,H.ref)(),f=(0,H.ref)(),h=(0,H.ref)(),g=(0,H.ref)(),v=(0,H.ref)(),x=(0,H.ref)(),b=(0,H.ref)(),w=e=>{const t={};return e.forEach((e=>{t[e]=(...t)=>{const o=d.value;if(o&&o[e])return o[e](...t)}})),t},C=w(ga);ga.forEach((e=>{C[e]=(...t)=>{const o=d.value;if(o&&o[e])return o&&o[e](...t)}}));const y=(0,H.computed)((()=>Object.assign({},c.grid.proxyConfig,e.proxyConfig))),T=(0,H.computed)((()=>{const e=y.value;return!1!==e.message})),E=(0,H.computed)((()=>Object.assign({},c.grid.pagerConfig,e.pagerConfig))),S=(0,H.computed)((()=>Object.assign({},c.grid.formConfig,e.formConfig))),k=(0,H.computed)((()=>Object.assign({},c.grid.toolbarConfig,e.toolbarConfig))),R=(0,H.computed)((()=>Object.assign({},c.grid.zoomConfig,e.zoomConfig))),O=(0,H.computed)((()=>s.isZMax?{zIndex:s.tZindex}:null)),M=(0,H.computed)((()=>{const t={},o=e;return ha.forEach((e=>{t[e]=o[e]})),t})),$={refElem:u,refTable:d,refForm:p,refToolbar:m,refPager:f},I={computeProxyOpts:y,computePagerOpts:E,computeFormOpts:S,computeToolbarOpts:k,computeZoomOpts:R},D={xID:l,props:e,context:t,instance:r,reactData:s,getRefMaps:()=>$,getComputeMaps:()=>I};let F={};const N=(0,H.computed)((()=>{const{seqConfig:t,pagerConfig:o,loading:n,editConfig:l,proxyConfig:r}=e,{isZMax:a,tableLoading:i,tablePage:c,tableData:u}=s,d=M.value,p=y.value,m=E.value,f=Object.assign({},d);return a&&(d.maxHeight?f.maxHeight="100%":f.height="100%"),r&&Y(p)&&(f.loading=n||i,f.data=u,o&&p.seq&&Y(m)&&(f.seqConfig=Object.assign({},t,{startIndex:(c.currentPage-1)*c.pageSize}))),l&&(f.editConfig=Object.assign({},l)),f})),P=()=>{const t=k.value;e.toolbarConfig&&Y(t)&&(0,H.nextTick)((()=>{const e=d.value,t=m.value;e&&t&&e.connect(t)}))},L=()=>{const{tablePage:t}=s,{pagerConfig:o}=e,n=E.value,{currentPage:l,pageSize:r}=n;o&&Y(n)&&(l&&(t.currentPage=l),r&&(t.pageSize=r))},A=e=>{const t=T.value,o=d.value,n=o.getCheckboxRecords();n.length?(o.togglePendingRow(n),C.clearCheckboxRow()):t&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({id:e,content:c.i18n("vxe.grid.selectOneRecord"),status:"warning"}))},V=(e,t)=>{const o=y.value,n=o.response||o.props||{},l=n.message;let r;return e&&l&&(r=a().isFunction(l)?l({data:e,$grid:D}):a().get(e,l)),r||c.i18n(t)},_=(e,t,o)=>{const n=T.value,l=C.getCheckboxRecords();if(n){if(l.length)return No.modal.confirm({id:`cfm_${e}`,content:c.i18n(t),escClosable:!0}).then((e=>{if("confirm"===e)return o()}));No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({id:`msg_${e}`,content:c.i18n("vxe.grid.selectOneRecord"),status:"warning"})}else l.length&&o();return Promise.resolve()},j=t=>{const{proxyConfig:o}=e,{tablePage:n}=s,{currentPage:l,pageSize:r}=t,a=y.value;n.currentPage=l,n.pageSize=r,F.dispatchEvent("page-change",t),o&&Y(a)&&F.commitProxy("query").then((e=>{F.dispatchEvent("proxy-query",e,t.$event)}))},z=t=>{const o=d.value,{proxyConfig:n}=e,{computeSortOpts:l}=o.getComputeMaps(),r=y.value,a=l.value;a.remote&&(s.sortData=t.sortList,n&&Y(r)&&(s.tablePage.currentPage=1,F.commitProxy("query").then((e=>{F.dispatchEvent("proxy-query",e,t.$event)})))),F.dispatchEvent("sort-change",t)},W=t=>{const o=d.value,{proxyConfig:n}=e,{computeFilterOpts:l}=o.getComputeMaps(),r=y.value,a=l.value;a.remote&&(s.filterData=t.filterList,n&&Y(r)&&(s.tablePage.currentPage=1,F.commitProxy("query").then((e=>{F.dispatchEvent("proxy-query",e,t.$event)})))),F.dispatchEvent("filter-change",t)},q=t=>{const{proxyConfig:o}=e,n=y.value;o&&Y(n)&&F.commitProxy("reload").then((e=>{F.dispatchEvent("proxy-query",{...e,isReload:!0},t.$event)})),F.dispatchEvent("form-submit",t)},U=t=>{const{proxyConfig:o}=e,n=y.value;o&&Y(n)&&F.commitProxy("reload").then((e=>{F.dispatchEvent("proxy-query",{...e,isReload:!0},t.$event)})),F.dispatchEvent("form-reset",t)},G=e=>{F.dispatchEvent("form-submit-invalid",e)},X=e=>{(0,H.nextTick)((()=>C.recalculate(!0))),F.dispatchEvent("form-toggle-collapse",e),F.dispatchEvent("form-collapse",e)},K=e=>{const{isZMax:t}=s;return(e?!t:t)&&(s.isZMax=!t,s.tZindex<Q()&&(s.tZindex=J())),(0,H.nextTick)().then((()=>C.recalculate(!0))).then((()=>s.isZMax))},Z=(e,t)=>{const n=e[t];if(n){if(!a().isString(n))return n;if(o[n])return o[n];B("vxe.error.notSlot",[n])}return null},ee=()=>{const{formConfig:t,proxyConfig:n}=e,{formData:l}=s,r=y.value,i=S.value,c=[];if(t&&Y(i)||o.form){let e=[];if(o.form)e=o.form({$grid:D});else if(i.items){const t={};if(!i.inited){i.inited=!0;const e=r.beforeItem;r&&e&&i.items.forEach((t=>{e({$grid:D,item:t})}))}i.items.forEach((e=>{a().each(e.slots,(e=>{a().isFunction(e)||o[e]&&(t[e]=o[e])}))})),e.push((0,H.h)(fa,{ref:p,...Object.assign({},i,{data:n&&Y(r)&&r.form?l:i.data}),onSubmit:q,onReset:U,onSubmitInvalid:G,onCollapse:X},t))}c.push((0,H.h)("div",{ref:h,key:"form",class:"vxe-grid--form-wrapper"},e))}return c},te=()=>{const{toolbarConfig:t}=e,n=k.value,l=[];if(t&&Y(n)||o.toolbar){let e=[];if(o.toolbar)e=o.toolbar({$grid:D});else{const t=n.slots;let o,l;const r={};t&&(o=Z(t,"buttons"),l=Z(t,"tools"),o&&(r.buttons=o),l&&(r.tools=l)),e.push((0,H.h)(Wr,{ref:m,...n},r))}l.push((0,H.h)("div",{ref:g,key:"toolbar",class:"vxe-grid--toolbar-wrapper"},e))}return l},oe=()=>o.top?[(0,H.h)("div",{ref:v,key:"top",class:"vxe-grid--top-wrapper"},o.top({$grid:D}))]:[],ne=["Form","Toolbar","Top","Table","Bottom","Pager"],le=()=>{const{layouts:t}=e,o=[],n=t&&t.length?t:c.grid.layouts||ne;return n.forEach((e=>{switch(e){case"Form":o.push(ee());break;case"Toolbar":o.push(te());break;case"Top":o.push(oe());break;case"Table":o.push(ae());break;case"Bottom":o.push(ie());break;case"Pager":o.push(se());break;default:B("vxe.error.notProp",[`layouts -> ${e}`]);break}})),o},re={};Cr.forEach((e=>{const t=a().camelCase(`on-${e}`);re[t]=(...t)=>n(e,...t)}));const ae=()=>{const{proxyConfig:t}=e,n=N.value,l=y.value,r=Object.assign({},re),a=o.empty,i=o.loading;t&&Y(l)&&(l.sort&&(r.onSortChange=z),l.filter&&(r.onFilterChange=W));const s={};return a&&(s.empty=()=>a({})),i&&(s.loading=()=>i({})),[(0,H.h)($r,{ref:d,key:"table",...n,...r},s)]},ie=()=>o.bottom?[(0,H.h)("div",{ref:x,key:"bottom",class:"vxe-grid--bottom-wrapper"},o.bottom({$grid:D}))]:[],se=()=>{const{proxyConfig:t,pagerConfig:n}=e,l=y.value,r=E.value,a=[];if(n&&Y(r)||o.pager){let e=[];if(o.pager)e=o.pager({$grid:D});else{const o=r.slots,n={};let a,i;o&&(a=Z(o,"left"),i=Z(o,"right"),a&&(n.left=a),i&&(n.right=i)),e.push((0,H.h)(Ar,{ref:f,...r,...t&&Y(l)?s.tablePage:{},onPageChange:j},n))}a.push((0,H.h)("div",{ref:b,key:"pager",class:"vxe-grid--pager-wrapper"},e))}return a},ce=()=>{const{proxyConfig:t,formConfig:o}=e,{proxyInited:n}=s,l=y.value,r=S.value;if(t&&Y(l)){if(o&&Y(r)&&l.form&&r.items){const e={};r.items.forEach((t=>{const{field:o,itemRender:n}=t;if(o){let l=null;if(n){const{defaultValue:e}=n;a().isFunction(e)?l=e({item:t}):a().isUndefined(e)||(l=e)}e[o]=l}})),s.formData=e}n||(s.proxyInited=!0,!1!==l.autoLoad&&(0,H.nextTick)().then((()=>F.commitProxy("_init"))).then((e=>{F.dispatchEvent("proxy-query",{...e,isInited:!0},new Event("init"))})))}};F={dispatchEvent(e,t,o){n(e,Object.assign({$grid:D,$event:o},t))},commitProxy(t,...o){const{toolbarConfig:n,pagerConfig:l,editRules:r,validConfig:i}=e,{tablePage:u,formData:p}=s,m=T.value,f=y.value,h=E.value,g=k.value,{beforeQuery:v,afterQuery:x,beforeDelete:b,afterDelete:w,beforeSave:S,afterSave:R,ajax:O={}}=f,M=f.response||f.props||{},$=d.value;let I=null,N=null;if(a().isString(t)){const{buttons:e}=g,o=n&&Y(g)&&e?a().findTree(e,(e=>e.code===t),{children:"dropdowns"}):null;I=o?o.item:null,N=t}else I=t,N=I.code;const P=I?I.params:null;switch(N){case"insert":return $.insert({});case"insert_edit":return $.insert({}).then((({row:e})=>$.setEditRow(e)));case"insert_actived":return $.insert({}).then((({row:e})=>$.setEditRow(e)));case"mark_cancel":A(N);break;case"remove":return _(N,"vxe.grid.removeSelectRecord",(()=>$.removeCheckboxRow()));case"import":$.importData(P);break;case"open_import":$.openImport(P);break;case"export":$.exportData(P);break;case"open_export":$.openExport(P);break;case"reset_custom":return $.resetColumn(!0);case"_init":case"reload":case"query":{const e=O.query;if(e){const t="_init"===N,n="reload"===N;let r=[],i=[],c={};if(l&&((t||n)&&(u.currentPage=1),Y(h)&&(c={...u})),t){const{computeSortOpts:e}=$.getComputeMaps(),t=e.value;let o=t.defaultSort;o&&(a().isArray(o)||(o=[o]),r=o.map((e=>({field:e.field,property:e.field,order:e.order})))),i=$.getCheckedFilters()}else n?$.clearAll():(r=$.getSortColumns(),i=$.getCheckedFilters());const d={code:N,button:I,isInited:t,isReload:n,$grid:D,page:c,sort:r.length?r[0]:{},sorts:r,filters:i,form:p,options:e};s.sortData=r,s.filterData=i,s.tableLoading=!0;const m=[d].concat(o);return Promise.resolve((v||e)(...m)).then((e=>{if(s.tableLoading=!1,e)if(l&&Y(h)){const t=M.total,o=(a().isFunction(t)?t({data:e,$grid:D}):a().get(e,t||"page.total"))||0;u.total=a().toNumber(o);const n=M.result;s.tableData=(a().isFunction(n)?n({data:e,$grid:D}):a().get(e,n||"result"))||[];const l=Math.max(Math.ceil(o/u.pageSize),1);u.currentPage>l&&(u.currentPage=l)}else{const t=M.list;s.tableData=(t?a().isFunction(t)?t({data:e,$grid:D}):a().get(e,t):e)||[]}else s.tableData=[];return x&&x(...m),{status:!0}})).catch((()=>(s.tableLoading=!1,{status:!1})))}B("vxe.error.notFunc",["proxy-config.ajax.query"]);break}case"delete":{const e=O.delete;if(e){const t=C.getCheckboxRecords(),n=t.filter((e=>!$.isInsertByRow(e))),l={removeRecords:n},r={$grid:D,code:N,button:I,body:l,form:p,options:e},a=[r].concat(o);if(t.length)return _(N,"vxe.grid.deleteSelectRecord",(()=>n.length?(s.tableLoading=!0,Promise.resolve((b||e)(...a)).then((e=>(s.tableLoading=!1,$.setPendingRow(n,!1),m&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:V(e,"vxe.grid.delSuccess"),status:"success"})),w?w(...a):F.commitProxy("query"),{status:!0}))).catch((e=>(s.tableLoading=!1,m&&(No.modal.message||B("vxe.error.reqModule",["Modal"]),No.modal.message({id:N,content:V(e,"vxe.grid.operError"),status:"error"})),{status:!1})))):$.remove(t)));m&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({id:N,content:c.i18n("vxe.grid.selectOneRecord"),status:"warning"}))}else B("vxe.error.notFunc",["proxy-config.ajax.delete"]);break}case"save":{const e=O.save;if(e){const t=$.getRecordset(),{insertRecords:n,removeRecords:l,updateRecords:a,pendingRecords:u}=t,d={$grid:D,code:N,button:I,body:t,form:p,options:e},f=[d].concat(o);n.length&&(t.pendingRecords=u.filter((e=>-1===$.findRowIndexOf(n,e)))),u.length&&(t.insertRecords=n.filter((e=>-1===$.findRowIndexOf(u,e))));let h=Promise.resolve();return r&&(h=$[i&&"full"===i.msgMode?"fullValidate":"validate"](t.insertRecords.concat(a))),h.then((o=>{if(!o)return t.insertRecords.length||l.length||a.length||t.pendingRecords.length?(s.tableLoading=!0,Promise.resolve((S||e)(...f)).then((e=>(s.tableLoading=!1,$.clearPendingRow(),m&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({content:V(e,"vxe.grid.saveSuccess"),status:"success"})),R?R(...f):F.commitProxy("query"),{status:!0}))).catch((e=>(s.tableLoading=!1,m&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({id:N,content:V(e,"vxe.grid.operError"),status:"error"})),{status:!1})))):void(m&&(No.modal||B("vxe.error.reqModule",["Modal"]),No.modal.message({id:N,content:c.i18n("vxe.grid.dataUnchanged"),status:"info"})))}))}B("vxe.error.notFunc",["proxy-config.ajax.save"]);break}default:{const e=No.commands.get(N);e&&(e.commandMethod?e.commandMethod({code:N,button:I,$grid:D,$table:$},...o):B("vxe.error.notCommands",[N]))}}return(0,H.nextTick)()},zoom(){return s.isZMax?F.revert():F.maximize()},isMaximized(){return s.isZMax},maximize(){return K(!0)},revert(){return K()},getFormItems(t){const o=S.value,{formConfig:n}=e,{items:l}=o,r=[];return a().eachTree(n&&Y(o)&&l?l:[],(e=>{r.push(e)}),{children:"children"}),a().isUndefined(t)?r:r[t]},getProxyInfo(){const t=d.value;if(e.proxyConfig){const{sortData:e}=s;return{data:s.tableData,filter:s.filterData,form:s.formData,sort:e.length?e[0]:{},sorts:e,pager:s.tablePage,pendingRecords:t?t.getPendingRecords():[]}}return null}},F.loadColumn=e=>{const t=d.value;return a().eachTree(e,(e=>{e.slots&&a().each(e.slots,(e=>{a().isFunction(e)||o[e]||B("vxe.error.notSlot",[e])}))})),t?t.loadColumn(e):(0,H.nextTick)()},F.reloadColumn=e=>(C.clearAll(),F.loadColumn(e));const ue={extendTableMethods:w,callSlot(e,t){return e&&(a().isString(e)&&(e=o[e]||null),a().isFunction(e))?lt(e(t)):[]},getExcludeHeight(){const{height:t}=e,{isZMax:o}=s,n=u.value,l=h.value,r=g.value,a=v.value,i=x.value,c=b.value,d=o||"auto"!==t&&"100%"!==t?0:ve(n.parentNode);return d+ve(n)+ge(l)+ge(r)+ge(a)+ge(i)+ge(c)},getParentHeight(){const e=u.value;return e?(s.isZMax?he().visibleHeight:a().toNumber(getComputedStyle(e.parentNode).height))-ue.getExcludeHeight():0},triggerToolbarCommitEvent(e,t){const{code:o}=e;return F.commitProxy(e,t).then((e=>{o&&e&&e.status&&["query","reload","delete","save"].includes(o)&&F.dispatchEvent("delete"===o||"save"===o?`proxy-${o}`:"proxy-query",{...e,isReload:"reload"===o},t)}))},triggerToolbarBtnEvent(e,t){ue.triggerToolbarCommitEvent(e,t),F.dispatchEvent("toolbar-button-click",{code:e.code,button:e},t)},triggerToolbarTolEvent(e,t){ue.triggerToolbarCommitEvent(e,t),F.dispatchEvent("toolbar-tool-click",{code:e.code,tool:e,$event:t})},triggerZoomEvent(e){F.zoom(),F.dispatchEvent("zoom",{type:s.isZMax?"max":"revert"},e)}};Object.assign(D,C,F,ue);const de=(0,H.ref)(0);(0,H.watch)((()=>e.columns?e.columns.length:-1),(()=>{de.value++})),(0,H.watch)((()=>e.columns),(()=>{de.value++})),(0,H.watch)(de,(()=>{(0,H.nextTick)((()=>D.loadColumn(e.columns||[])))})),(0,H.watch)((()=>e.toolbarConfig),(()=>{P()})),(0,H.watch)((()=>e.pagerConfig),(()=>{L()})),(0,H.watch)((()=>e.proxyConfig),(()=>{ce()}));const pe=e=>{const t=R.value,o=Jo(e,Xo.ESCAPE);o&&s.isZMax&&!1!==t.escRestore&&ue.triggerZoomEvent(e)};No.hooks.forEach((e=>{const{setupGrid:t}=e;if(t){const e=t(D);e&&a().isObject(e)&&Object.assign(D,e)}})),L(),(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{const{data:t,columns:o,proxyConfig:n}=e,l=y.value,r=S.value;Y(n)&&(t||l.form&&r.data)&&B("vxe.error.errConflicts",["grid.data","grid.proxy-config"]),o&&o.length&&D.loadColumn(o),P()})),en.on(D,"keydown",pe)})),(0,H.onUnmounted)((()=>{en.off(D,"keydown")})),(0,H.nextTick)((()=>{ce()}));const me=()=>{const t=i.value,o=O.value;return(0,H.h)("div",{ref:u,class:["vxe-grid",{[`size--${t}`]:t,"is--animat":!!e.animat,"is--round":e.round,"is--maximize":s.isZMax,"is--loading":e.loading||s.tableLoading}],style:o},le())};return D.renderVN=me,(0,H.provide)("$xegrid",D),D},render(){return this.renderVN()}});const ba=Object.assign(xa,{install(e){e.component(xa.name,xa)}}),wa=ba;zo.component(xa.name,xa);const Ca=Object.assign(On,{install(e){e.component(On.name,On)}}),ya=Ca;zo.component(On.name,On);var Ta=(0,H.defineComponent)({name:"VxeCheckboxGroup",props:{modelValue:Array,options:Array,optionProps:Object,disabled:Boolean,max:{type:[String,Number],default:null},size:{type:String,default:()=>c.checkboxGroup.size||c.size}},emits:["update:modelValue","change"],setup(e,t){const{slots:o,emit:n}=t,l=(0,H.inject)("$xeform",null),r=(0,H.inject)("$xeformiteminfo",null),i=a().uniqueId(),s=(0,H.computed)((()=>{const{modelValue:t,max:o}=e;return!!o&&(t?t.length:0)>=a().toNumber(o)})),c=(0,H.computed)((()=>e.optionProps||{})),u=(0,H.computed)((()=>{const e=c.value;return e.label||"label"})),d=(0,H.computed)((()=>{const e=c.value;return e.value||"value"})),p=(0,H.computed)((()=>{const e=c.value;return e.disabled||"disabled"})),m={computeIsMaximize:s},f={xID:i,props:e,context:t,getComputeMaps:()=>m};pn(e);const h={dispatchEvent(e,t,o){n(e,Object.assign({$checkboxGroup:f,$event:o},t))}},g={handleChecked(t,o){const{checked:a,label:i}=t,s=e.modelValue||[],c=s.indexOf(i);a?-1===c&&s.push(i):s.splice(c,1),n("update:modelValue",s),f.dispatchEvent("change",Object.assign({checklist:s},t),o),l&&r&&l.triggerItemEvent(o,r.itemConfig.field,s)}};Object.assign(f,h,g);const v=()=>{const{options:t}=e,n=o.default,l=d.value,r=u.value,a=p.value;return(0,H.h)("div",{class:"vxe-checkbox-group"},n?n({}):t?t.map((e=>(0,H.h)(On,{label:e[l],content:e[r],disabled:e[a]}))):[])};return f.renderVN=v,(0,H.provide)("$xecheckboxgroup",f),v}});const Ea=Object.assign(Ta,{install(e){e.component(Ta.name,Ta)}}),Sa=Ea;zo.component(Ta.name,Ta);const ka=Object.assign(Fn,{install:function(e){e.component(Fn.name,Fn)}}),Ra=ka;zo.component(Fn.name,Fn);const Oa=Object.assign(Pn,{install:function(e){e.component(Pn.name,Pn)}}),Ma=Oa;zo.component(Pn.name,Pn);const $a=Object.assign(Nn,{install:function(e){e.component(Nn.name,Nn)}}),Ia=$a;zo.component(Nn.name,Nn);const Da=Object.assign(Rn,{install(e){e.component(Rn.name,Rn)}}),Fa=Da;zo.component(Rn.name,Rn);let Na;var Pa=(0,H.defineComponent)({name:"VxeTextarea",props:{modelValue:[String,Number],className:String,immediate:{type:Boolean,default:!0},name:String,readonly:Boolean,disabled:Boolean,placeholder:{type:String,default:()=>a().eqNull(c.textarea.placeholder)?c.i18n("vxe.base.pleaseInput"):c.textarea.placeholder},maxlength:[String,Number],rows:{type:[String,Number],default:2},cols:{type:[String,Number],default:null},showWordCount:Boolean,countMethod:Function,autosize:[Boolean,Object],form:String,resize:{type:String,default:()=>c.textarea.resize},size:{type:String,default:()=>c.textarea.size||c.size}},emits:["update:modelValue","input","keydown","keyup","click","change","focus","blur"],setup(e,t){const{emit:o}=t,n=(0,H.inject)("$xeform",null),l=(0,H.inject)("$xeformiteminfo",null),r=a().uniqueId(),i=pn(e),s=(0,H.reactive)({inputValue:e.modelValue}),u=(0,H.ref)(),d=(0,H.ref)(),p={refElem:u,refTextarea:d},m={xID:r,props:e,context:t,reactData:s,getRefMaps:()=>p};let f={};const h=(0,H.computed)((()=>a().getSize(s.inputValue))),g=(0,H.computed)((()=>{const t=h.value;return e.maxlength&&t>a().toNumber(e.maxlength)})),v=(0,H.computed)((()=>Object.assign({minRows:1,maxRows:10},c.textarea.autosize,e.autosize))),x=()=>{const{size:t,autosize:o}=e,{inputValue:n}=s;if(o){Na||(Na=document.createElement("div")),Na.parentNode||document.body.appendChild(Na);const e=d.value,o=getComputedStyle(e);Na.className=["vxe-textarea--autosize",t?`size--${t}`:""].join(" "),Na.style.width=`${e.clientWidth}px`,Na.style.padding=o.padding,Na.innerText=(""+(n||"　")).replace(/\n$/,"\n　")}},b=()=>{e.autosize&&(0,H.nextTick)((()=>{const e=v.value,{minRows:t,maxRows:o}=e,n=d.value,l=Na.clientHeight,r=getComputedStyle(n),i=a().toNumber(r.lineHeight),s=a().toNumber(r.paddingTop),c=a().toNumber(r.paddingBottom),u=a().toNumber(r.borderTopWidth),p=a().toNumber(r.borderBottomWidth),m=s+c+u+p,f=(l-m)/i,h=f&&/[0-9]/.test(""+f)?f:Math.floor(f)+1;let g=h;h<t?g=t:h>o&&(g=o),n.style.height=`${g*i+m}px`}))},w=e=>{const t=s.inputValue;m.dispatchEvent(e.type,{value:t},e)},C=(t,r)=>{s.inputValue=t,o("update:modelValue",t),a().toValueString(e.modelValue)!==t&&(f.dispatchEvent("change",{value:t},r),n&&l&&n.triggerItemEvent(r,l.itemConfig.field,t))},y=t=>{const{immediate:o}=e,n=t.target,l=n.value;s.inputValue=l,o&&C(l,t),m.dispatchEvent("input",{value:l},t),b()},T=t=>{const{immediate:o}=e;o?w(t):C(s.inputValue,t)},E=t=>{const{immediate:o}=e,{inputValue:n}=s;o||C(n,t),m.dispatchEvent("blur",{value:n},t)};f={dispatchEvent(e,t,n){o(e,Object.assign({$textarea:m,$event:n},t))},focus(){const e=d.value;return e.focus(),(0,H.nextTick)()},blur(){const e=d.value;return e.blur(),(0,H.nextTick)()}},Object.assign(m,f),(0,H.watch)((()=>e.modelValue),(e=>{s.inputValue=e,x()})),(0,H.nextTick)((()=>{const{autosize:t}=e;t&&(x(),b())}));const S=()=>{const{className:t,resize:o,placeholder:n,disabled:l,maxlength:r,autosize:c,showWordCount:p,countMethod:m,rows:f,cols:v}=e,{inputValue:x}=s,b=i.value,C=g.value,S=h.value;return(0,H.h)("div",{ref:u,class:["vxe-textarea",t,{[`size--${b}`]:b,"is--autosize":c,"is--count":p,"is--disabled":l,"def--rows":!a().eqNull(f),"def--cols":!a().eqNull(v)}]},[(0,H.h)("textarea",{ref:d,class:"vxe-textarea--inner",value:x,name:e.name,placeholder:n?te(n):null,maxlength:r,readonly:e.readonly,disabled:l,rows:f,cols:v,style:o?{resize:o}:null,onInput:y,onChange:T,onKeydown:w,onKeyup:w,onClick:w,onFocus:w,onBlur:E}),p?(0,H.h)("span",{class:["vxe-textarea--count",{"is--error":C}]},m?`${m({value:x})}`:`${S}${r?`/${r}`:""}`):null])};return m.renderVN=S,m},render(){return this.renderVN()}});const La=Object.assign(Pa,{install:function(e){e.component(Pa.name,Pa)}}),Aa=La;zo.component(Pa.name,Pa);var Va=(0,H.defineComponent)({name:"VxeButtonGroup",props:{options:Array,mode:String,status:String,round:Boolean,circle:Boolean,className:[String,Function],disabled:Boolean,size:{type:String,default:()=>c.buttonGroup.size||c.size}},emits:["click"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r={},i={xID:l,props:e,context:t,getComputeMaps:()=>r};pn(e);const s={dispatchEvent(e,t,o){n(e,Object.assign({$buttonGroup:i,$event:o},t))}},c={handleClick(t,o){const{options:n}=e,{name:l}=t,r=n?n.find((e=>e.name===l)):null;s.dispatchEvent("click",{...t,option:r},o)}};Object.assign(i,s,c);const u=()=>{const{className:t,options:n}=e,l=o.default;return(0,H.h)("div",{class:["vxe-button-group",t?a().isFunction(t)?t({$buttonGroup:i}):t:""]},l?l({}):n?n.map(((e,t)=>(0,H.h)(mn,{key:t,...e}))):[])};return i.renderVN=u,(0,H.provide)("$xebuttongroup",i),u}});const Ba=Object.assign(Va,{install(e){e.component(Va.name,Va)}}),_a=Ba;zo.component(Va.name,Va);function ja(e){return Wo(),new Promise((t=>{if(e&&e.id&&vn.some((t=>t.props.id===e.id)))t("exist");else{const o=e.onHide,n=Object.assign(e,{key:a().uniqueId(),modelValue:!0,onHide(e){const l=jo.modals;o&&o(e),jo.modals=l.filter((e=>e.key!==n.key)),t(e.type)}});jo.modals.push(n)}}))}function Ha(e){return a().find(vn,(t=>t.props.id===e))}function za(e){const t=e?[Ha(e)]:vn,o=[];return t.forEach((e=>{e&&o.push(e.close())})),Promise.all(o)}function Wa(e,t,o,n){let l;return l=a().isObject(t)?t:{content:a().toValueString(t),title:o},ja({...e,...n,...l})}function qa(e,t,o){return Wa({type:"alert",showFooter:!0},e,t,o)}function Ua(e,t,o){return Wa({type:"confirm",status:"question",showFooter:!0},e,t,o)}function Ga(e,t){return Wa({type:"message",mask:!1,lockView:!1,showHeader:!1},e,"",t)}const Xa={get:Ha,close:za,open:ja,alert:qa,confirm:Ua,message:Ga},Ya=Xa,Ka=Object.assign(bn,{install:function(e){e.component(bn.name,bn),No.modal=Xa}}),Za=Ka;zo.component(bn.name,bn);const Ja=[];var Qa=(0,H.defineComponent)({name:"VxeDrawer",props:{modelValue:Boolean,id:String,title:String,loading:{type:Boolean,default:null},className:String,position:[String,Object],lockView:{type:Boolean,default:()=>c.drawer.lockView},lockScroll:Boolean,mask:{type:Boolean,default:()=>c.drawer.mask},maskClosable:{type:Boolean,default:()=>c.drawer.maskClosable},escClosable:{type:Boolean,default:()=>c.drawer.escClosable},showHeader:{type:Boolean,default:()=>c.drawer.showHeader},showFooter:{type:Boolean,default:()=>c.drawer.showFooter},showClose:{type:Boolean,default:()=>c.drawer.showClose},content:[Number,String],showCancelButton:{type:Boolean,default:null},cancelButtonText:{type:String,default:()=>c.drawer.cancelButtonText},showConfirmButton:{type:Boolean,default:()=>c.drawer.showConfirmButton},confirmButtonText:{type:String,default:()=>c.drawer.confirmButtonText},destroyOnClose:{type:Boolean,default:()=>c.drawer.destroyOnClose},showTitleOverflow:{type:Boolean,default:()=>c.drawer.showTitleOverflow},width:[Number,String],height:[Number,String],zIndex:Number,transfer:{type:Boolean,default:()=>c.drawer.transfer},size:{type:String,default:()=>c.drawer.size||c.size},beforeHideMethod:{type:Function,default:()=>c.drawer.beforeHideMethod},slots:Number},emits:["update:modelValue","show","hide","before-hide","close","confirm","cancel"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.ref)(),s=(0,H.ref)(),u=(0,H.ref)(),d=(0,H.ref)(),p=(0,H.reactive)({inited:!1,visible:!1,contentVisible:!1,drawerZIndex:0,firstOpen:!0}),m={refElem:i},f={},h={xID:l,props:e,context:t,reactData:p,getRefMaps:()=>m,getComputeMaps:()=>f},g=()=>{const e=s.value;return e},v=()=>{const{width:t,height:o}=e,n=g();return n.style.width=`${t?isNaN(t)?t:`${t}px`:""}`,n.style.height=`${o?isNaN(o)?o:`${o}px`:""}`,(0,H.nextTick)()},x=()=>{const{zIndex:t}=e,{drawerZIndex:o}=p;t?p.drawerZIndex=t:o<Q()&&(p.drawerZIndex=J())},b=()=>(0,H.nextTick)().then((()=>{})),w=t=>{const{beforeHideMethod:o}=e,{visible:l}=p,r={type:t};return l&&Promise.resolve(o?o(r):null).then((e=>{a().isError(e)||(p.contentVisible=!1,a().remove(Ja,(e=>e===h)),S.dispatchEvent("before-hide",r),setTimeout((()=>{p.visible=!1,n("update:modelValue",!1),S.dispatchEvent("hide",r)}),200))})).catch((e=>e)),(0,H.nextTick)()},C=e=>{const t="close";S.dispatchEvent(t,{type:t},e),w(t)},y=e=>{const t="confirm";S.dispatchEvent(t,{type:t},e),w(t)},T=e=>{const t="cancel";S.dispatchEvent(t,{type:t},e),w(t)},E=()=>{const{showFooter:t}=e,{inited:o,visible:l}=p;return o||(p.inited=!0),l||(v(),p.visible=!0,p.contentVisible=!1,x(),Ja.push(h),setTimeout((()=>{p.contentVisible=!0,(0,H.nextTick)((()=>{if(t){const e=u.value,t=d.value,o=e||t;o&&o.focus()}const e="",o={type:e};n("update:modelValue",!0),S.dispatchEvent("show",o)}))}),10),(0,H.nextTick)((()=>{const{firstOpen:e}=p;e&&b().then((()=>{setTimeout((()=>b()),20)})),e&&(p.firstOpen=!1)}))),(0,H.nextTick)()},S={dispatchEvent(e,t,o){n(e,Object.assign({$drawer:h,$event:o},t))},open:E,close(){return w("close")},getBox:g},k=t=>{const o=i.value;if(e.maskClosable&&t.target===o){const e="mask";w(e)}},R=e=>{const t=Jo(e,Xo.ESCAPE);if(t){const e=a().max(Ja,(e=>e.reactData.drawerZIndex));e&&setTimeout((()=>{e===h&&e.props.escClosable&&w("exit")}),10)}},O=()=>{const{drawerZIndex:e}=p;Ja.some((t=>t.reactData.visible&&t.reactData.drawerZIndex>e))&&x()},M={};Object.assign(h,S,M);const $=()=>{const{slots:t={},showClose:n,title:l}=e,r=o.title||t.title,a=o.corner||t.corner,i=[(0,H.h)("div",{class:"vxe-drawer--header-title"},r?lt(r({$drawer:h})):l?te(l):c.i18n("vxe.alert.title"))],s=[];return a&&s.push((0,H.h)("span",{class:"vxe-drawer--corner-wrapper"},lt(a({$drawer:h})))),n&&s.push((0,H.h)("i",{class:["vxe-drawer--close-btn","trigger--btn",c.icon.MODAL_CLOSE],title:c.i18n("vxe.drawer.close"),onClick:C})),i.push((0,H.h)("div",{class:"vxe-drawer--header-right"},s)),i},I=()=>{const{slots:t={},showTitleOverflow:n}=e,l=o.header||t.header,r=[];return e.showHeader&&r.push((0,H.h)("div",{class:["vxe-drawer--header",{"is--ellipsis":n}]},l?!p.inited||e.destroyOnClose&&!p.visible?[]:lt(l({$drawer:h})):$())),r},D=()=>{const{slots:t={},content:n}=e,l=o.default||t.default;return[(0,H.h)("div",{class:"vxe-drawer--body"},[(0,H.h)("div",{class:"vxe-drawer--content"},l?!p.inited||e.destroyOnClose&&!p.visible?[]:lt(l({$drawer:h})):te(n)),(0,H.h)(gn,{class:"vxe-drawer--loading",modelValue:e.loading})])]},F=()=>{const{showCancelButton:t,showConfirmButton:o}=e,n=[];return t&&n.push((0,H.h)(mn,{key:1,ref:d,content:e.cancelButtonText||c.i18n("vxe.button.cancel"),onClick:T})),o&&n.push((0,H.h)(mn,{key:2,ref:u,status:"primary",content:e.confirmButtonText||c.i18n("vxe.button.confirm"),onClick:y})),n},N=()=>{const{slots:t={}}=e,n=o.footer||t.footer,l=[];return e.showFooter&&l.push((0,H.h)("div",{class:"vxe-drawer--footer"},n?!p.inited||e.destroyOnClose&&!p.visible?[]:lt(n({$drawer:h})):F())),l},P=()=>{const{className:t,position:o,loading:n,lockScroll:l,lockView:a,mask:c}=e,{inited:u,contentVisible:d,visible:m}=p,f=r.value;return(0,H.h)(H.Teleport,{to:"body",disabled:!e.transfer||!u},[(0,H.h)("div",{ref:i,class:["vxe-drawer--wrapper",`pos--${o}`,t||"",{[`size--${f}`]:f,"lock--scroll":l,"lock--view":a,"is--mask":c,"is--visible":d,"is--active":m,"is--loading":n}],style:{zIndex:p.drawerZIndex},onClick:k},[(0,H.h)("div",{ref:s,class:"vxe-drawer--box",onMousedown:O},I().concat(D(),N()))])])};return h.renderVN=P,(0,H.watch)((()=>e.width),v),(0,H.watch)((()=>e.height),v),(0,H.watch)((()=>e.modelValue),(e=>{e?E():w("model")})),(0,H.onMounted)((()=>{(0,H.nextTick)((()=>{e.modelValue&&E(),v()})),e.escClosable&&en.on(h,"keydown",R)})),(0,H.onUnmounted)((()=>{en.off(h,"keydown")})),h},render(){return this.renderVN()}});function ei(e){return Wo(),new Promise((t=>{if(e&&e.id&&Ja.some((t=>t.props.id===e.id)))t("exist");else{const o=e.onHide,n=Object.assign(e,{key:a().uniqueId(),modelValue:!0,onHide(e){const l=jo.drawers;o&&o(e),jo.drawers=l.filter((e=>e.key!==n.key)),t(e.type)}});jo.drawers.push(n)}}))}function ti(e){return a().find(Ja,(t=>t.props.id===e))}function oi(e){const t=e?[ti(e)]:Ja,o=[];return t.forEach((e=>{e&&o.push(e.close())})),Promise.all(o)}const ni={get:ti,close:oi,open:ei},li=ni,ri=Object.assign(Qa,{install:function(e){e.component(Qa.name,Qa),No.drawer=ni}});zo.component(Qa.name,Qa);const ai=ri;const ii={title:String,field:String,span:[String,Number],align:String,titleAlign:{type:String,default:null},titleWidth:{type:[String,Number],default:null},titleColon:{type:Boolean,default:null},titleAsterisk:{type:Boolean,default:null},showTitle:{type:Boolean,default:!0},vertical:{type:Boolean,default:null},className:[String,Function],contentClassName:[String,Function],contentStyle:[Object,Function],titleClassName:[String,Function],titleStyle:[Object,Function],titleOverflow:{type:[Boolean,String],default:null},titlePrefix:Object,titleSuffix:Object,resetValue:{default:null},visibleMethod:Function,visible:{type:Boolean,default:null},folding:Boolean,collapseNode:Boolean,itemRender:Object,rules:Array};var si=(0,H.defineComponent)({name:"VxeFormItem",props:ii,setup(e,{slots:t}){const o=(0,H.ref)(),n=(0,H.inject)("$xeform",{}),l=(0,H.inject)("$xeformgather",null),r=(0,H.reactive)(Gr(n,e)),i={formItem:r},s={itemConfig:r};r.slots=t,(0,H.provide)("$xeformiteminfo",s),(0,H.provide)("$xeformitem",i),(0,H.provide)("$xeformgather",null),Zr(e,r),(0,H.onMounted)((()=>{Jr(n,o.value,r,l)})),(0,H.onUnmounted)((()=>{Qr(n,r)}));const u=(e,t)=>{const{props:n,reactData:l}=e,{data:r,rules:i,titleAlign:s,titleWidth:u,titleColon:d,titleAsterisk:p,titleOverflow:m,vertical:f}=n,{collapseAll:h}=l,{computeValidOpts:g}=e.getComputeMaps(),v=g.value,{slots:x,title:b,visible:w,folding:C,field:y,collapseNode:T,itemRender:E,showError:S,errRule:k,className:R,titleOverflow:O,vertical:M,showTitle:$,contentClassName:I,contentStyle:D,titleClassName:F,titleStyle:N}=t,P=Y(E)?No.renderer.get(E.name):null,L=P?P.itemClassName:"",A=P?P.itemStyle:null,V=P?P.itemContentClassName:"",B=P?P.itemContentStyle:null,_=P?P.itemTitleClassName:"",j=P?P.itemTitleStyle:null,z=x?x.default:null,W=x?x.title:null,q=t.span||n.span,U=t.align||n.align,G=a().eqNull(t.titleAlign)?s:t.titleAlign,X=a().eqNull(t.titleWidth)?u:t.titleWidth,K=a().eqNull(t.titleColon)?d:t.titleColon,Z=a().eqNull(t.titleAsterisk)?p:t.titleAsterisk,J=a().isUndefined(O)||a().isNull(O)?m:O,Q=a().isUndefined(M)||a().isNull(M)?f:M,ee="ellipsis"===J,oe="title"===J,ne=!0===J||"tooltip"===J,le=oe||ne||ee,re={data:r,field:y,property:y,item:t,$form:e,$grid:e.xegrid};let ae=!1;if(!1===w)return(0,H.createCommentVNode)();if(i){const e=i[y];e&&(ae=e.some((e=>e.required)))}let ie=[];z?ie=e.callSlot(z,re):P&&P.renderItemContent?ie=lt(P.renderItemContent(E,re)):y&&(ie=[`${a().get(r,y)}`]),T&&ie.push((0,H.h)("div",{class:"vxe-form--item-trigger-node",onClick:e.toggleCollapseEvent},[(0,H.h)("span",{class:"vxe-form--item-trigger-text"},h?c.i18n("vxe.form.unfolding"):c.i18n("vxe.form.folding")),(0,H.h)("i",{class:["vxe-form--item-trigger-icon",h?c.icon.FORM_FOLDING:c.icon.FORM_UNFOLDING]})])),k&&v.showMessage&&ie.push((0,H.h)("div",{class:"vxe-form--item-valid",style:k.maxWidth?{width:`${k.maxWidth}px`}:null},k.message));const se=ne?{onMouseenter(t){e.triggerTitleTipEvent(t,re)},onMouseleave:e.handleTitleTipLeaveEvent}:{};return(0,H.h)("div",{ref:o,class:["vxe-form--item",t.id,q?`vxe-form--item-col--${q} is--span`:"",R?a().isFunction(R)?R(re):R:"",L?a().isFunction(L)?L(re):L:"",{"is--title":b,"is--colon":K,"is--vertical":Q,"is--asterisk":Z,"is--required":ae,"is--hidden":C&&h,"is--active":Kr(e,t),"is--error":S}],style:a().isFunction(A)?A(re):A},[(0,H.h)("div",{class:"vxe-form--item-inner"},[!1!==$&&(b||W)?(0,H.h)("div",{class:["vxe-form--item-title",G?`align--${G}`:"",le?"is--ellipsis":"",_?a().isFunction(_)?_(re):_:"",F?a().isFunction(F)?F(re):F:""],style:Object.assign({},a().isFunction(j)?j(re):j,a().isFunction(N)?N(re):N,X?{width:isNaN(X)?X:`${X}px`}:null),title:oe?te(b):null,...se},ra(e,t)):null,(0,H.h)("div",{class:["vxe-form--item-content",U?`align--${U}`:"",V?a().isFunction(V)?V(re):V:"",I?a().isFunction(I)?I(re):I:""],style:Object.assign({},a().isFunction(B)?B(re):B,a().isFunction(D)?D(re):D)},ie)])])},d=()=>{const e=n?n.props:null;return e&&e.customLayout?u(n,r):(0,H.h)("div",{ref:o})},p={renderVN:d};return p},render(){return this.renderVN()}});const ci=Object.assign(si,{install(e){e.component(si.name,si)}}),ui=ci;zo.component(si.name,si);var di=(0,H.defineComponent)({name:"VxeFormGather",props:ii,setup(e,{slots:t}){const o=(0,H.ref)(),n=(0,H.inject)("$xeform",{}),l=(0,H.inject)("$xeformgather",null),r=(0,H.reactive)(Gr(n,e)),i={formItem:r},s={itemConfig:r};r.children=[],(0,H.provide)("$xeformiteminfo",s),(0,H.provide)("$xeformgather",i),(0,H.provide)("$xeformitem",null),Zr(e,r),(0,H.onMounted)((()=>{Jr(n,o.value,r,l)})),(0,H.onUnmounted)((()=>{Qr(n,r)}));const c=()=>{const{className:l,field:i}=e,s=e.span||(n?n.props.span:null),c=t.default;return(0,H.h)("div",{ref:o,class:["vxe-form--gather vxe-form--item-row",r.id,s?`vxe-form--item-col_${s} is--span`:"",l?a().isFunction(l)?l({$form:n,data:n?n.props.data:{},item:r,field:i,property:i}):l:""]},c?c():[])},u={renderVN:c};return u},render(){return this.renderVN()}});const pi=Object.assign(di,{install(e){e.component(di.name,di)}}),mi=pi;zo.component(di.name,di);class fi{constructor(e,t){Object.assign(this,{id:a().uniqueId("option_"),value:t.value,label:t.label,visible:t.visible,className:t.className,disabled:t.disabled})}update(e,t){this[e]=t}}function hi(e){return e instanceof fi}function gi(e,t){return hi(t)?t:new fi(e,t)}function vi(e,t){Object.keys(e).forEach((o=>{(0,H.watch)((()=>e[o]),(e=>{t.update(o,e)}))}))}function xi(e,t,o,n){const{reactData:l}=e,{staticOptions:r}=l,i=t.parentNode,s=n?n.option:null,c=s?s.options:r;i&&c&&(c.splice(a().arrayIndexOf(i.children,t),0,o),l.staticOptions=r.slice(0))}function bi(e,t){const{reactData:o}=e,{staticOptions:n}=o,l=a().findTree(n,(e=>e.id===t.id),{children:"options"});l&&l.items.splice(l.index,1),o.staticOptions=n.slice(0)}var wi=(0,H.defineComponent)({name:"VxeOptgroup",props:{label:{type:[String,Number,Boolean],default:""},visible:{type:Boolean,default:null},className:[String,Function],disabled:Boolean},setup(e,{slots:t}){const o=(0,H.ref)(),n=(0,H.inject)("$xeselect",{}),l=gi(n,e),r={option:l};return l.options=[],(0,H.provide)("xeoptgroup",r),vi(e,l),(0,H.onMounted)((()=>{xi(n,o.value,l)})),(0,H.onUnmounted)((()=>{bi(n,l)})),()=>(0,H.h)("div",{ref:o},t.default?t.default():[])}});const Ci=Object.assign(wi,{install:function(e){e.component(wi.name,wi)}}),yi=Ci;zo.component(wi.name,wi);var Ti=(0,H.defineComponent)({name:"VxeOption",props:{value:null,label:{type:[String,Number,Boolean],default:""},visible:{type:Boolean,default:null},className:[String,Function],disabled:Boolean},setup(e,{slots:t}){const o=(0,H.ref)(),n=(0,H.inject)("$xeselect",{}),l=(0,H.inject)("xeoptgroup",null),r=gi(n,e);return r.slots=t,vi(e,r),(0,H.onMounted)((()=>{xi(n,o.value,r,l)})),(0,H.onUnmounted)((()=>{bi(n,r)})),()=>(0,H.h)("div",{ref:o})}});const Ei=Object.assign(Ti,{install:function(e){e.component(Ti.name,Ti)}}),Si=Ei;zo.component(Ti.name,Ti);var ki=(0,H.defineComponent)({name:"VxeSwitch",props:{modelValue:[String,Number,Boolean],disabled:Boolean,size:{type:String,default:()=>c.switch.size||c.size},openLabel:String,closeLabel:String,openValue:{type:[String,Number,Boolean],default:!0},closeValue:{type:[String,Number,Boolean],default:!1},openIcon:String,closeIcon:String,openActiveIcon:String,closeActiveIcon:String},emits:["update:modelValue","change","focus","blur"],setup(e,t){const{emit:o}=t,n=(0,H.inject)("$xeform",null),l=(0,H.inject)("$xeformiteminfo",null),r=a().uniqueId(),i=pn(e),s=(0,H.reactive)({isActivated:!1,hasAnimat:!1,offsetLeft:0}),c={xID:r,props:e,context:t,reactData:s},u=(0,H.ref)();let d={};const p=(0,H.computed)((()=>te(e.openLabel))),m=(0,H.computed)((()=>te(e.closeLabel))),f=(0,H.computed)((()=>e.modelValue===e.openValue));let h;const g=t=>{if(!e.disabled){const r=f.value;clearTimeout(h);const a=r?e.closeValue:e.openValue;s.hasAnimat=!0,o("update:modelValue",a),d.dispatchEvent("change",{value:a},t),n&&l&&n.triggerItemEvent(t,l.itemConfig.field,a),h=setTimeout((()=>{s.hasAnimat=!1}),400)}},v=t=>{s.isActivated=!0,d.dispatchEvent("focus",{value:e.modelValue},t)},x=t=>{s.isActivated=!1,d.dispatchEvent("blur",{value:e.modelValue},t)};d={dispatchEvent(e,t,n){o(e,Object.assign({$switch:c,$event:n},t))},focus(){const e=u.value;return s.isActivated=!0,e.focus(),(0,H.nextTick)()},blur(){const e=u.value;return e.blur(),s.isActivated=!1,(0,H.nextTick)()}},Object.assign(c,d);const b=()=>{const{disabled:t,openIcon:o,closeIcon:n,openActiveIcon:l,closeActiveIcon:r}=e,a=f.value,c=i.value,d=p.value,h=m.value;return(0,H.h)("div",{class:["vxe-switch",a?"is--on":"is--off",{[`size--${c}`]:c,"is--disabled":t,"is--animat":s.hasAnimat}]},[(0,H.h)("button",{ref:u,class:"vxe-switch--button",type:"button",disabled:t,onClick:g,onFocus:v,onBlur:x},[(0,H.h)("span",{class:"vxe-switch--label vxe-switch--label-on"},[o?(0,H.h)("i",{class:["vxe-switch--label-icon",o]}):(0,H.createCommentVNode)(),d]),(0,H.h)("span",{class:"vxe-switch--label vxe-switch--label-off"},[n?(0,H.h)("i",{class:["vxe-switch--label-icon",n]}):(0,H.createCommentVNode)(),h]),(0,H.h)("span",{class:"vxe-switch--icon"},l||r?[(0,H.h)("i",{class:a?l:r})]:[])])])};return c.renderVN=b,c},render(){return this.renderVN()}});const Ri=Object.assign(ki,{install:function(e){e.component(ki.name,ki)}}),Oi=Ri;zo.component(ki.name,ki);var Mi=(0,H.defineComponent)({name:"VxeList",props:{data:Array,height:[Number,String],maxHeight:[Number,String],loading:Boolean,className:[String,Function],size:{type:String,default:()=>c.list.size||c.size},autoResize:{type:Boolean,default:()=>c.list.autoResize},syncResize:[Boolean,String,Number],scrollY:Object},emits:["scroll"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.reactive)({scrollYLoad:!1,bodyHeight:0,rowHeight:0,topSpaceHeight:0,items:[]}),s=(0,H.ref)(),u=(0,H.ref)(),d=(0,H.ref)(),p={fullData:[],lastScrollLeft:0,lastScrollTop:0,scrollYStore:{startIndex:0,endIndex:0,visibleSize:0,offsetSize:0,rowHeight:0}},m={refElem:s},f={xID:l,props:e,context:t,reactData:i,internalData:p,getRefMaps:()=>m};let h={};const g=(0,H.computed)((()=>Object.assign({},c.list.scrollY,e.scrollY))),v=(0,H.computed)((()=>{const{height:t,maxHeight:o}=e,n={};return t?n.height=`${isNaN(t)?t:`${t}px`}`:o&&(n.height="auto",n.maxHeight=`${isNaN(o)?o:`${o}px`}`),n})),x=()=>{const{scrollYLoad:e}=i,{scrollYStore:t,fullData:o}=p;i.bodyHeight=e?o.length*t.rowHeight:0,i.topSpaceHeight=e?Math.max(t.startIndex*t.rowHeight,0):0},b=()=>{const{scrollYLoad:e}=i,{fullData:t,scrollYStore:o}=p;return i.items=e?t.slice(o.startIndex,o.endIndex):t.slice(0),(0,H.nextTick)()},w=()=>{b(),x()},C=()=>(0,H.nextTick)().then((()=>{const{scrollYLoad:e}=i,{scrollYStore:t}=p,o=d.value,n=g.value;let l,r=0;if(o&&(n.sItem&&(l=o.querySelector(n.sItem)),l||(l=o.children[0])),l&&(r=l.offsetHeight),r=Math.max(20,r),t.rowHeight=r,e){const e=u.value,o=Math.max(8,Math.ceil(e.clientHeight/r)),l=n.oSize?a().toNumber(n.oSize):ae.edge?10:0;t.offsetSize=l,t.visibleSize=o,t.endIndex=Math.max(t.startIndex,o+l,t.endIndex),w()}else x();i.rowHeight=r})),y=()=>{const e=u.value;return e&&(e.scrollTop=0),(0,H.nextTick)()},T=(e,t)=>{const o=u.value;return a().isNumber(e)&&(o.scrollLeft=e),a().isNumber(t)&&(o.scrollTop=t),i.scrollYLoad?new Promise((e=>{setTimeout((()=>{(0,H.nextTick)((()=>{e()}))}),50)})):(0,H.nextTick)()},E=()=>{const{lastScrollLeft:e,lastScrollTop:t}=p;return y().then((()=>{if(e||t)return p.lastScrollLeft=0,p.lastScrollTop=0,T(e,t)}))},S=()=>{const e=s.value;return e.clientWidth&&e.clientHeight?C():Promise.resolve()},k=e=>{const{scrollYStore:t}=p,{startIndex:o,endIndex:n,visibleSize:l,offsetSize:r,rowHeight:a}=t,i=e.target,s=i.scrollTop,c=Math.floor(s/a),u=Math.max(0,c-1-r),d=c+l+r;(c<=o||c>=n-l-1)&&(o===u&&n===d||(t.startIndex=u,t.endIndex=d,w()))},R=e=>{const t=e.target,o=t.scrollTop,n=t.scrollLeft,l=n!==p.lastScrollLeft,r=o!==p.lastScrollTop;p.lastScrollTop=o,p.lastScrollLeft=n,i.scrollYLoad&&k(e),h.dispatchEvent("scroll",{scrollLeft:n,scrollTop:o,isX:l,isY:r},e)};h={dispatchEvent(e,t,o){n(e,Object.assign({$list:f,$event:o},t))},loadData(e){const{scrollYStore:t}=p,o=g.value,n=e||[];return Object.assign(t,{startIndex:0,endIndex:1,visibleSize:0}),p.fullData=n,i.scrollYLoad=!!o.enabled&&o.gt>-1&&(0===o.gt||o.gt<=n.length),b(),C().then((()=>{E()}))},reloadData(e){return y(),h.loadData(e)},recalculate:S,scrollTo:T,refreshScroll:E,clearScroll:y},Object.assign(f,h);const O=(0,H.ref)(0);let M;(0,H.watch)((()=>e.data?e.data.length:-1),(()=>{O.value++})),(0,H.watch)((()=>e.data),(()=>{O.value++})),(0,H.watch)(O,(()=>{h.loadData(e.data||[])})),(0,H.watch)((()=>e.syncResize),(e=>{e&&(S(),(0,H.nextTick)((()=>setTimeout((()=>S())))))})),(0,H.onActivated)((()=>{S().then((()=>E()))})),(0,H.nextTick)((()=>{if(en.on(f,"resize",(()=>{S()})),e.autoResize){const e=s.value;M=dr((()=>S())),M.observe(e)}h.loadData(e.data||[])})),(0,H.onUnmounted)((()=>{M&&M.disconnect(),en.off(f,"resize")}));const $=()=>{const{className:t,loading:n}=e,{bodyHeight:l,topSpaceHeight:c,items:p}=i,m=r.value,h=v.value;return(0,H.h)("div",{ref:s,class:["vxe-list",t?a().isFunction(t)?t({$list:f}):t:"",{[`size--${m}`]:m,"is--loading":n}]},[(0,H.h)("div",{ref:u,class:"vxe-list--virtual-wrapper",style:h,onScroll:R},[(0,H.h)("div",{class:"vxe-list--y-space",style:{height:l?`${l}px`:""}}),(0,H.h)("div",{ref:d,class:"vxe-list--body",style:{marginTop:c?`${c}px`:""}},o.default?o.default({items:p,$list:f}):[])]),(0,H.h)(gn,{class:"vxe-list--loading",modelValue:n})])};return f.renderVN=$,f},render(){return this.renderVN()}});const $i=Object.assign(Mi,{install(e){e.component(Mi.name,Mi)}}),Ii=$i;zo.component(Mi.name,Mi);var Di=(0,H.defineComponent)({name:"VxePulldown",props:{modelValue:Boolean,disabled:Boolean,placement:String,size:{type:String,default:()=>c.size},className:[String,Function],popupClassName:[String,Function],destroyOnClose:Boolean,transfer:Boolean},emits:["update:modelValue","hide-panel"],setup(e,t){const{slots:o,emit:n}=t,l=a().uniqueId(),r=pn(e),i=(0,H.reactive)({inited:!1,panelIndex:0,panelStyle:null,panelPlacement:null,visiblePanel:!1,animatVisible:!1,isActivated:!1}),s=(0,H.ref)(),c=(0,H.ref)(),u=(0,H.ref)(),d={refElem:s},p={xID:l,props:e,context:t,reactData:i,getRefMaps:()=>d};let m={};const f=()=>{i.panelIndex<Q()&&(i.panelIndex=J())},h=()=>i.visiblePanel,g=()=>(0,H.nextTick)().then((()=>{const{transfer:t,placement:o}=e,{panelIndex:n,visiblePanel:l}=i;if(l){const e=c.value,l=u.value;if(l&&e){const r=e.offsetHeight,a=e.offsetWidth,s=l.offsetHeight,c=l.offsetWidth,u=5,d={zIndex:n},{boundingTop:p,boundingLeft:m,visibleHeight:f,visibleWidth:h}=Te(e);let g="bottom";if(t){let e=m,t=p+r;"top"===o?(g="top",t=p-s):o||(t+s+u>f&&(g="top",t=p-s),t<u&&(g="bottom",t=p+r)),e+c+u>h&&(e-=e+c+u-h),e<u&&(e=u),Object.assign(d,{left:`${e}px`,top:`${t}px`,minWidth:`${a}px`})}else"top"===o?(g="top",d.bottom=`${r}px`):o||p+r+s>f&&p-r-s>u&&(g="top",d.bottom=`${r}px`);i.panelStyle=d,i.panelPlacement=g}}return(0,H.nextTick)()}));let v;const x=()=>(i.inited||(i.inited=!0),new Promise((t=>{e.disabled?(0,H.nextTick)((()=>{t()})):(clearTimeout(v),i.isActivated=!0,i.animatVisible=!0,setTimeout((()=>{i.visiblePanel=!0,n("update:modelValue",!0),g(),setTimeout((()=>{t(g())}),40)}),10),f())}))),b=()=>(i.visiblePanel=!1,n("update:modelValue",!1),new Promise((e=>{i.animatVisible?v=window.setTimeout((()=>{i.animatVisible=!1,(0,H.nextTick)((()=>{e()}))}),350):(0,H.nextTick)((()=>{e()}))}))),w=()=>i.visiblePanel?b():x(),C=t=>{const{disabled:o}=e,{visiblePanel:n}=i,l=u.value;o||n&&(Ce(t,l).flag?g():(b(),m.dispatchEvent("hide-panel",{},t)))},y=t=>{const{disabled:o}=e,{visiblePanel:n}=i,l=s.value,r=u.value;o||(i.isActivated=Ce(t,l).flag||Ce(t,r).flag,n&&!i.isActivated&&(b(),m.dispatchEvent("hide-panel",{},t)))},T=e=>{i.visiblePanel&&(i.isActivated=!1,b(),m.dispatchEvent("hide-panel",{},e))};m={dispatchEvent(e,t,o){n(e,Object.assign({$pulldown:p,$event:o},t))},isPanelVisible:h,togglePanel:w,showPanel:x,hidePanel:b},Object.assign(p,m),(0,H.watch)((()=>e.modelValue),(e=>{e?x():b()})),(0,H.nextTick)((()=>{en.on(p,"mousewheel",C),en.on(p,"mousedown",y),en.on(p,"blur",T)})),(0,H.onUnmounted)((()=>{en.off(p,"mousewheel"),en.off(p,"mousedown"),en.off(p,"blur")}));const E=()=>{const{className:t,popupClassName:n,destroyOnClose:l,transfer:d,disabled:m}=e,{inited:f,isActivated:h,animatVisible:g,visiblePanel:v,panelStyle:x,panelPlacement:b}=i,w=r.value,C=o.default,y=o.header,T=o.footer,E=o.dropdown;return(0,H.h)("div",{ref:s,class:["vxe-pulldown",t?a().isFunction(t)?t({$pulldown:p}):t:"",{[`size--${w}`]:w,"is--visivle":v,"is--disabled":m,"is--active":h}]},[(0,H.h)("div",{ref:c,class:"vxe-pulldown--content"},C?C({$pulldown:p}):[]),(0,H.h)(H.Teleport,{to:"body",disabled:!d||!f},[(0,H.h)("div",{ref:u,class:["vxe-table--ignore-clear vxe-pulldown--panel",n?a().isFunction(n)?n({$pulldown:p}):n:"",{[`size--${w}`]:w,"is--transfer":d,"animat--leave":g,"animat--enter":v}],placement:b,style:x},E?[(0,H.h)("div",{class:"vxe-pulldown--panel-wrapper"},!f||l&&!v&&!g?[]:[y?(0,H.h)("div",{class:"vxe-pulldown--panel-header"},y({$pulldown:p})):(0,H.createCommentVNode)(),(0,H.h)("div",{class:"vxe-pulldown--panel-body"},E({$pulldown:p})),T?(0,H.h)("div",{class:"vxe-pulldown--panel-footer"},T({$pulldown:p})):(0,H.createCommentVNode)()])]:[])])])};return p.renderVN=E,p},render(){return this.renderVN()}});const Fi=Object.assign(Di,{install:function(e){e.component(Di.name,Di)}}),Ni=Fi;zo.component(Di.name,Di);var Pi={vxe:{base:{pleaseInput:"请输入",pleaseSelect:"请选择",comma:"，",fullStop:"。"},loading:{text:"加载中..."},error:{downErr:"下载失败",groupFixed:"如果使用分组表头，冻结列必须按组设置",groupMouseRange:'分组表头与 "{0}" 不能同时使用，这可能会出现错误',groupTag:'分组列头应该使用 "{0}" 而不是 "{1}"，这可能会出现错误',scrollErrProp:'启用虚拟滚动后不支持该参数 "{0}"',errConflicts:'参数 "{0}" 与 "{1}" 有冲突',unableInsert:"无法插入到指定位置，请检查参数是否正确",useErr:'安装 "{0}" 模块时发生错误，可能顺序不正确，依赖的模块需要在 Table 之前安装',barUnableLink:"工具栏无法关联表格",expandContent:'展开行的插槽应该是 "content"，请检查是否正确',reqComp:'缺少 "{0}" 组件，请检查是否正确安装。 https://vxeui.com/#/start/useGlobal',reqModule:'缺少 "{0}" 模块',reqProp:'缺少必要的 "{0}" 参数，这可能会导致出现错误',emptyProp:'参数 "{0}" 不允许为空',errProp:'不支持的参数 "{0}"，可能为 "{1}"',colRepet:'column.{0}="{1}" 重复了，这可能会导致某些功能无法使用',notFunc:'方法 "{0}" 不存在',errFunc:'参数 "{0}" 不是一个方法',notValidators:'全局校验 "{0}" 不存在',notFormats:'全局格式化 "{0}" 不存在',notCommands:'全局指令 "{0}" 不存在',notSlot:'插槽 "{0}" 不存在',noTree:'树结构不支持 "{0}"',notProp:'不支持的参数 "{0}"',checkProp:'当数据量过大时可能会导致复选框卡顿，建议设置参数 "{0}" 提升渲染速度',coverProp:'"{0}" 的参数 "{1}" 重复定义，这可能会出现错误',uniField:'字段名 "{0}" 重复定义，这可能会出现错误',delFunc:'方法 "{0}" 已废弃，请使用 "{1}"',delProp:'参数 "{0}" 已废弃，请使用 "{1}"',delEvent:'事件 "{0}" 已废弃，请使用 "{1}"',removeProp:'参数 "{0}" 已废弃，不建议使用，这可能会导致出现错误',errFormat:'全局的格式化内容应该使用 "VXETable.formats" 定义，挂载 "formatter={0}" 的方式已不建议使用',notType:'不支持的文件类型 "{0}"',notExp:"该浏览器不支持导入/导出功能",impFields:"导入失败，请检查字段名和数据格式是否正确",treeNotImp:"树表格不支持导入"},table:{emptyText:"暂无数据",allTitle:"全选/取消",seqTitle:"序号",actionTitle:"操作",confirmFilter:"筛选",resetFilter:"重置",allFilter:"全部",sortAsc:"升序：最低到最高",sortDesc:"降序：最高到最低",filter:"对所选的列启用筛选",impSuccess:"成功导入 {0} 条记录",expLoading:"正在导出中",expSuccess:"导出成功",expFilename:"导出_{0}",expOriginFilename:"导出_源_{0}",customTitle:"列设置",customAll:"全部",customConfirm:"确认",customClose:"关闭",customCancel:"取消",customRestore:"恢复默认",maxFixedCol:"最大冻结列的数量不能超过 {0} 个"},grid:{selectOneRecord:"请至少选择一条记录！",deleteSelectRecord:"您确定要删除所选记录吗？",removeSelectRecord:"您确定要移除所选记录吗？",dataUnchanged:"数据未改动！",delSuccess:"成功删除所选记录！",saveSuccess:"保存成功！",operError:"发生错误，操作失败！"},select:{search:"搜索",loadingText:"加载中",emptyText:"暂无数据"},pager:{goto:"前往",gotoTitle:"页数",pagesize:"{0}条/页",total:"共 {0} 条记录",pageClassifier:"页",homePage:"首页",homePageTitle:"首页",prevPage:"上一页",prevPageTitle:"上一页",nextPage:"下一页",nextPageTitle:"下一页",prevJump:"向上跳页",prevJumpTitle:"向上跳页",nextJump:"向下跳页",nextJumpTitle:"向下跳页",endPage:"末页",endPageTitle:"末页"},alert:{title:"系统提示"},button:{confirm:"确认",cancel:"取消"},filter:{search:"搜索"},custom:{cstmTitle:"列设置",cstmRestore:"恢复默认",cstmCancel:"取消",cstmConfirm:"确定",cstmConfirmRestore:"请确认是否恢复成默认列配置？",cstmDragTarget:"移动目标：{0}",setting:{colSort:"排序",sortHelpTip:"点击并拖动图标可以调整列的排序",colTitle:"标题",colResizable:"列宽（像素）",colVisible:"是否显示",colFixed:"冻结列",colFixedMax:"冻结列（最多 {0} 列）",fixedLeft:"左侧",fixedUnset:"不设置",fixedRight:"右侧"}},import:{modes:{covering:"覆盖方式（直接覆盖表格数据）",insert:"底部追加（在表格的底部追加新数据）",insertTop:"顶部追加（在表格的顶部追加新数据）",insertBottom:"底部追加（在表格的底部追加新数据）"},impTitle:"导入数据",impFile:"文件名",impSelect:"选择文件",impType:"文件类型",impOpts:"参数设置",impMode:"导入模式",impConfirm:"导入",impCancel:"取消"},export:{types:{csv:"CSV (逗号分隔)(*.csv)",html:"网页(*.html)",xml:"XML 数据(*.xml)",txt:"文本文件(制表符分隔)(*.txt)",xls:"Excel 97-2003 工作簿(*.xls)",xlsx:"Excel 工作簿(*.xlsx)",pdf:"PDF (*.pdf)"},modes:{current:"当前数据（当前页的数据）",selected:"选中数据（当前页选中的数据）",all:"全量数据（包括所有分页的数据）"},printTitle:"打印数据",expTitle:"导出数据",expName:"文件名",expNamePlaceholder:"请输入文件名",expSheetName:"标题",expSheetNamePlaceholder:"请输入标题",expType:"保存类型",expMode:"选择数据",expCurrentColumn:"全部字段",expColumn:"选择字段",expOpts:"参数设置",expOptHeader:"表头",expHeaderTitle:"是否需要表头",expOptFooter:"表尾",expFooterTitle:"是否需要表尾",expOptColgroup:"分组表头",expColgroupTitle:"如果存在，则支持带有分组结构的表头",expOptMerge:"合并",expMergeTitle:"如果存在，则支持带有合并结构的单元格",expOptAllExpand:"展开层级",expAllExpandTitle:"如果存在，则支持将带有层级结构的数据全部展开",expOptUseStyle:"样式",expUseStyleTitle:"如果存在，则支持带样式的单元格",expOptOriginal:"源数据",expOriginalTitle:"如果为源数据，则支持导入到表格中",expPrint:"打印",expConfirm:"导出",expCancel:"取消"},modal:{errTitle:"错误提示",zoomMin:"最小化",zoomIn:"最大化",zoomOut:"还原",close:"关闭",miniMaxSize:"最小化窗口的数量不能超过 {0} 个",footPropErr:"show-footer 仅用于启用表尾，需配合 show-confirm-button | show-cancel-button | 插槽使用"},drawer:{close:"关闭"},form:{folding:"收起",unfolding:"展开"},toolbar:{import:"导入",export:"导出",print:"打印",refresh:"刷新",zoomIn:"全屏",zoomOut:"还原",custom:"列设置",customAll:"全部",customConfirm:"确认",customRestore:"重置",fixedLeft:"冻结在左侧",fixedRight:"冻结在右侧",cancelFixed:"取消冻结列"},input:{date:{m1:"01 月",m2:"02 月",m3:"03 月",m4:"04 月",m5:"05 月",m6:"06 月",m7:"07 月",m8:"08 月",m9:"09 月",m10:"10 月",m11:"11 月",m12:"12 月",quarterLabel:"{0} 年",monthLabel:"{0} 年",dayLabel:"{0} 年 {1}",labelFormat:{date:"yyyy-MM-dd",time:"HH:mm:ss",datetime:"yyyy-MM-dd HH:mm:ss",week:"yyyy 年第 WW 周",month:"yyyy-MM",quarter:"yyyy 年第 q 季度",year:"yyyy"},weeks:{w:"周",w0:"周日",w1:"周一",w2:"周二",w3:"周三",w4:"周四",w5:"周五",w6:"周六"},months:{m0:"一月",m1:"二月",m2:"三月",m3:"四月",m4:"五月",m5:"六月",m6:"七月",m7:"八月",m8:"九月",m9:"十月",m10:"十一月",m11:"十二月"},quarters:{q1:"第一季度",q2:"第二季度",q3:"第三季度",q4:"第四季度"}}},imagePreview:{popupTitle:"预览",operBtn:{zoomOut:"缩小",zoomIn:"放大",pctFull:"等比例缩放",pct11:"显示原始尺寸",rotateLeft:"向左旋转",rotateRight:"向右旋转",print:"点击打印图片",download:"点击下载图片"}},upload:{fileBtnText:"点击或拖拽上传",imgBtnText:"点击或拖拽上传",dragPlaceholder:"请把文件拖放到这个区域即可上传",imgSizeHint:"单张{0}",imgCountHint:"最多{0}张",fileTypeHint:"支持 {0} 文件类型",fileSizeHint:"单个文件大小不超过{0}",fileCountHint:"最多可上传{0}个文件",overCountErr:"最多只能选择{0}个文件！",overCountExtraErr:"已超出最大数量{0}个，超出的{1}个文件将被忽略！",overSizeErr:"文件大小最大不能超过{0}！",reUpload:"重新上传",uploadProgress:"上传中 {0}%",uploadErr:"上传失败",uploadSuccess:"上传成功",moreBtnText:"更多（{0}）",viewItemTitle:"点击查看",morePopup:{readTitle:"查看列表",imageTitle:"上传图片",fileTitle:"上传文件"}},formDesign:{formName:"表单名称",defFormTitle:"未命名的表单",widgetPropTab:"控件属性",widgetFormTab:"表单属性",error:{wdFormUni:"该类型的控件在表单中只允许添加一个",wdSubUni:"该类型的控件在子表中只允许添加一个"},styleSetting:{btn:"样式设置",title:"表单的样式设置",layoutTitle:"控件布局",verticalLayout:"上下布局",horizontalLayout:"横向布局",styleTitle:"标题样式",boldTitle:"标题加粗",fontBold:"加粗",fontNormal:"常规",colonTitle:"显示冒号",colonVisible:"显示",colonHidden:"隐藏",alignTitle:"对齐方式",widthTitle:"标题宽度",alignLeft:"居左",alignRight:"居右",unitPx:"像素",unitPct:"百分比"},widget:{group:{base:"基础控件",layout:"布局控件",system:"系统控件",module:"模块控件",chart:"图表控件",advanced:"高级控件"},copyTitle:"副本_{0}",component:{input:"输入框",textarea:"文本域",select:"下拉选择",row:"一行多列",title:"标题",text:"文本",subtable:"子表",VxeSwitch:"是/否",VxeInput:"输入框",VxeNumberInput:"数字",VxeDatePicker:"日期",VxeTextarea:"文本域",VxeSelect:"下拉选择",VxeTreeSelect:"树形选择",VxeRadioGroup:"单选框",VxeCheckboxGroup:"复选框",VxeUploadFile:"文件",VxeUploadImage:"图片"}},widgetProp:{name:"控件名称",placeholder:"提示语",required:"必填校验",multiple:"允许多选",displaySetting:{name:"显示设置",pc:"电脑端",mobile:"手机端",visible:"显示",hidden:"隐藏"},dataSource:{name:"数据源",defValue:"选项{0}",addOption:"添加选项",batchEditOption:"批量编辑",batchEditTip:"每行对应一个选项，支持从表格、Excel、WPS 中直接复制粘贴。",batchEditSubTip:"每行对应一个选项，如果是分组，子项可以是空格或制表键开头，支持从表格、Excel、WPS 中直接复制粘贴。",buildOption:"生成选项"},rowProp:{colSize:"列数",col2:"两列",col3:"三列",col4:"四列",col6:"六列",layout:"布局"},textProp:{name:"内容",alignTitle:"对齐方式",alignLeft:"居左",alignCenter:"居中",alignRight:"居右",colorTitle:"字体颜色",sizeTitle:"字体大小",boldTitle:"字体加粗",fontNormal:"常规",fontBold:"加粗"},subtableProp:{seqTitle:"序号",showSeq:"显示序号",showCheckbox:"允许多选",errSubDrag:"子表不支持该控件，请使用其他控件",colPlace:"将控件拖拽进来"},uploadProp:{limitFileCount:"文件数量限制",limitFileSize:"文件大小限制",multiFile:"允许上传多个文件",limitImgCount:"图片数量限制",limitImgSize:"图片大小限制",multiImg:"允许上传多张图片"}}},listDesign:{fieldSettingTab:"字段设置",listSettingTab:"参数设置",searchTitle:"查询条件",listTitle:"列表字段",searchField:"查询字段",listField:"列表字段",activeBtn:{ActionButtonUpdate:"编辑",ActionButtonDelete:"删除"},search:{addBtn:"编辑",emptyText:"未配置查询条件",editPopupTitle:"编辑查询字段"},searchPopup:{colTitle:"标题",saveBtn:"保存"}},text:{copySuccess:"已复制到剪贴板",copyError:"当前环境不支持该操作"},countdown:{formats:{yyyy:"年",MM:"月",dd:"天",HH:"时",mm:"分",ss:"秒"}},plugins:{extendCellArea:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作",cpInvalidErr:"该操作无法进行，您选择的区域中存在被禁止的列（{0}）"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},filterComplexInput:{menus:{fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结左侧",fixedRight:"冻结右侧"},cases:{equal:"等于",gt:"大于",lt:"小于",begin:"开头是",endin:"结尾是",include:"包含",isSensitive:"区分大小写"}},filterCombination:{menus:{clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"冻结列",fixedGroup:"冻结分组",cancelFixed:"取消冻结",fixedLeft:"冻结左侧",fixedRight:"冻结右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},empty:"(空白)",notData:"无匹配项"}},pro:{area:{mergeErr:"无法对合并单元格进行该操作",multiErr:"无法对多重选择区域进行该操作",extendErr:"如果延伸的区域包含被合并的单元格，所有合并的单元格需大小相同",pasteMultiErr:"无法粘贴，需要相同大小的复制的区域和粘贴的区域才能执行此操作"},fnr:{title:"查找和替换",findLabel:"查找",replaceLabel:"替换",findTitle:"查找内容：",replaceTitle:"替换为：",tabs:{find:"查找",replace:"替换"},filter:{re:"正则表达式",whole:"全词匹配",sensitive:"区分大小写"},btns:{findNext:"查找下一个",findAll:"查找全部",replace:"替换",replaceAll:"替换全部",cancel:"取消"},header:{seq:"#",cell:"单元格",value:"值"},empty:"(空值)",reError:"无效的正则表达式",recordCount:"已找到 {0} 个单元格",notCell:"找不到匹配的单元格",replaceSuccess:"成功替换 {0} 个单元格"}},renderer:{search:"搜索",cases:{equal:"等于",unequal:"不等于",gt:"大于",ge:"大于或等于",lt:"小于",le:"小于或等于",begin:"开头是",notbegin:"开头不是",endin:"结尾是",notendin:"结尾不是",include:"包含",exclude:"不包含",between:"介于",custom:"自定义筛选",insensitive:"不区分大小写",isSensitive:"区分大小写"},combination:{menus:{clearSort:"清除排序",sortAsc:"升序",sortDesc:"降序",fixedColumn:"锁定列",fixedGroup:"锁定组",cancelFixed:"取消锁定",fixedLeft:"锁定左侧",fixedRight:"锁定右侧",clearFilter:"清除筛选",textOption:"文本筛选",numberOption:"数值筛选"},popup:{title:"自定义筛选的方式",currColumnTitle:"当前列：",and:"与",or:"或",describeHtml:"可用 ? 代表单个字符<br/>用 * 代表任意多个字符"},empty:"(空白)",notData:"无匹配项"}}}};const Li=[qo,ln,un,yl,Rl,Fl,_l,zl,er,nr,ba,Hr,Pr,Ca,Ea,ka,Oa,$a,Da,La,Vr,Ba,Ka,ri,ea,pa,ci,pi,Ir,Ci,Ei,Ri,$i,Fi,Or];function Ai(e,t){a().isPlainObject(t)&&(Co(t),t.theme&&fo(t.theme)),Li.forEach((t=>t.install(e)))}Co({i18n:(e,t)=>a().toFormatString(a().get(Pi,e),t)}),"undefined"!==typeof window&&window.Vue&&(window.VxeUITable||(window.VxeUITable=e));var Vi=e,Bi=Vi}(),r}()}));