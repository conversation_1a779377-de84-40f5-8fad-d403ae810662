import { Module } from '@nestjs/common';
import { OAuthService } from './oauth.service';
import { OAuthController } from './oauth.controller';
import { UsersModule } from '../users/users.module';
import { UsersService } from '../users/users.service';
import { RolesModule } from '../roles/roles.module';

@Module({
  imports: [UsersModule, RolesModule],
  controllers: [OAuthController],
  providers: [OAuthService, UsersService],
  exports: [OAuthService, UsersService]
})
export class OAuthModule {}
