import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, Equal } from 'typeorm';
import { Template } from './entities/template.entity';
import { TemplateDsl } from './entities/template-dsl.entity';
import { TemplateDto } from './dto/template.dto';
import { LoginUserDto } from '../users/dto/login-user.dto';
import { QueryTemplateDto } from './dto/query-template.dto';
import { OssService } from '../oss/oss.service';
import { DictsService } from '../dicts/dicts.service';
import { pager, PlatformType } from '../shared';

@Injectable()
export class TemplatesService {
  constructor(
    @InjectRepository(Template) private template: Repository<Template>,
    @InjectRepository(TemplateDsl) private dsl: Repository<TemplateDsl>,
    protected oss: OssService,
    protected dicts: DictsService
  ) {}

  save(dto: TemplateDto, user: LoginUserDto) {
    const template = new Template();
    Object.assign(template, dto);
    template.author = user.name;
    template.userId = user.id;

    return this.template.save(template);
  }

  findOne(id: string) {
    return this.template.findOne({
      where: { id }
    });
  }

  async increaseInstalled(id: string) {
    const model = await this.findOne(id);
    return await this.template.update(id, { installed: model.installed + 1 });
  }

  async find(dto: QueryTemplateDto) {
    const { page, limit, skip, take } = pager(dto);
    const map: Record<string, PlatformType> = {
      web: PlatformType.Web,
      h5: PlatformType.H5,
      uniapp: PlatformType.UniApp
    };
    const platform = dto.platform ? map[dto.platform.toLowerCase()] : undefined;
    const share = dto.share ? dto.share === 'true' : undefined;
    const [list, total] = await this.template.findAndCount({
      skip,
      take,
      select: [
        'id',
        'name',
        'label',
        'category',
        'cover',
        'vip',
        'share',
        'createdAt',
        'author',
        'userId',
        'installed',
        'latest',
        'platform'
      ],
      where: [
        {
          name: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          platform,
          share: dto.share ? Equal(share) : undefined
        },
        {
          label: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          platform,
          share: dto.share ? Equal(share) : undefined
        },
        {
          category: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          platform,
          share: dto.share ? Equal(share) : undefined
        }
      ],
      order: {
        createdAt: 'DESC'
      }
    });
    return {
      page,
      limit,
      total,
      list
    };
  }

  async findByUserId(flatformDto: string, id?: string) {
    const map: Record<string, PlatformType> = {
      web: PlatformType.Web,
      h5: PlatformType.H5,
      uniapp: PlatformType.UniApp
    };

    const platform = map[flatformDto.toLowerCase()] || PlatformType.Web;
    const where: any = [
      {
        share: true,
        platform
      }
    ];
    if (id) {
      where.push({
        userId: id,
        platform
      });
    }

    const result = await this.template.find({
      select: [
        'id',
        'name',
        'label',
        'category',
        'cover',
        'vip',
        'share',
        'createdAt',
        'author',
        'userId',
        'installed',
        'latest',
        'platform'
      ],
      where,
      order: {
        updatedAt: 'DESC'
      }
    });
    return result.map((n) => {
      n.cover = this.oss.sign(n.cover, 1800, 300);
      return n;
    });
  }

  findOneByUserId(userId: string, templateId: string) {
    return this.template.findOne({
      where: { id: templateId, userId: userId }
    });
  }

  async remove(id: string | string[]) {
    return this.template.softDelete(id);
  }

  async removeByUserId(userId: string, id: string) {
    return this.template.softDelete({
      id,
      userId
    });
  }

  getLatest(templateId: string) {
    return this.dsl.findOne({
      where: {
        templateId
      },
      order: {
        version: 'DESC'
      }
    });
  }

  async saveDsl(dsl: TemplateDsl) {
    const { templateId } = dsl;
    const latest = await this.getLatest(templateId);
    if (latest) {
      dsl.version = latest.version + 1;
    } else {
      dsl.version = 1;
    }
    dsl.content = JSON.parse(dsl.content as any);
    const result = await this.dsl.save(dsl);
    if (result) {
      await this.template.update(templateId, { latest: dsl.label });
    }
    return result;
  }

  async findDsl(templateId: string, page: number = 1, limit: number = 50) {
    const skip = (page - 1) * limit;
    const [list, total] = await this.dsl.findAndCount({
      take: limit,
      skip,
      where: {
        templateId
      },
      order: {
        version: 'DESC'
      }
    });

    return {
      page,
      limit,
      total,
      list
    };
  }

  async countTemplates(start: string, end: string) {
    const builder = this.template
      .createQueryBuilder('templates')
      .select(`COUNT(DISTINCT templates.id)`, 'count')
      .where('templates.created_at > :start and templates.created_at < :end', {
        start,
        end
      });
    const res = await builder.getRawOne();
    return Number(res?.count || 0) as number;
  }
}
