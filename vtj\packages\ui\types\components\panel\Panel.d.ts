import { ComponentInternalInstance, VNodeProps, AllowedComponentProps, ComponentCustomProps, Slot, ComponentPublicInstance, ComponentOptionsBase, ExtractPropTypes, PropType, ComputedRef, ComponentOptionsMixin, GlobalComponents, GlobalDirectives, ComponentProvideOptions, DebuggerEvent, nextTick, WatchOptions, WatchStopHandle, ShallowUnwrapRef, ComponentCustomProperties, DefineComponent, Ref, PublicProps } from 'vue';
import { ContainerWrap, ContainerDirection, ContainerJustifyContent, ContainerAlignItems, ContainerAlignContent, ContainerProps } from '../container';
import { OnCleanup } from '@vue/reactivity';
import { PanelBadge } from './types';
import { BaseSize } from '../shared';
import { HeaderProps } from '../header';
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        header?(_: {}): any;
        title?(_: {}): any;
        actions?(_: {}): any;
        default?(_: {}): any;
        footer?(_: {}): any;
    };
    refs: {
        bodyRef: ({
            $: ComponentInternalInstance;
            $data: {};
            $props: Partial<{
                padding: boolean;
                tag: string;
                wrap: ContainerWrap;
                fit: boolean;
                flex: boolean;
                inline: boolean;
                direction: ContainerDirection;
                justify: ContainerJustifyContent;
                align: ContainerAlignItems;
                alignContent: ContainerAlignContent;
                grow: boolean;
                shrink: boolean;
                alignSelf: "auto" | ContainerAlignItems;
                gap: boolean;
                autoPointer: boolean;
            }> & Omit<{
                readonly padding: boolean;
                readonly tag: string;
                readonly wrap: ContainerWrap;
                readonly fit: boolean;
                readonly flex: boolean;
                readonly inline: boolean;
                readonly direction: ContainerDirection;
                readonly justify: ContainerJustifyContent;
                readonly align: ContainerAlignItems;
                readonly alignContent: ContainerAlignContent;
                readonly grow: boolean;
                readonly shrink: boolean;
                readonly alignSelf: "auto" | ContainerAlignItems;
                readonly gap: boolean;
                readonly autoPointer: boolean;
                readonly width?: string | number | undefined;
                readonly height?: string | number | undefined;
                readonly overflow?: "hidden" | "auto" | "visible" | undefined;
            } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer">;
            $attrs: {
                [x: string]: unknown;
            };
            $refs: {
                [x: string]: unknown;
            } & {
                elRef: unknown;
            };
            $slots: Readonly<{
                [name: string]: Slot<any> | undefined;
            }>;
            $root: ComponentPublicInstance | null;
            $parent: ComponentPublicInstance | null;
            $host: Element | null;
            $emit: (event: string, ...args: any[]) => void;
            $el: any;
            $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                tag: {
                    type: StringConstructor;
                    default: string;
                };
                fit: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                width: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                height: {
                    type: (StringConstructor | NumberConstructor)[];
                };
                flex: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                inline: {
                    type: BooleanConstructor;
                };
                direction: {
                    type: PropType<ContainerDirection>;
                    default: string;
                };
                wrap: {
                    type: PropType<ContainerWrap>;
                    default: string;
                };
                justify: {
                    type: PropType<ContainerJustifyContent>;
                    default: string;
                };
                align: {
                    type: PropType<ContainerAlignItems>;
                    default: string;
                };
                alignContent: {
                    type: PropType<ContainerAlignContent>;
                    default: string;
                };
                grow: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                shrink: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                alignSelf: {
                    type: PropType<"auto" | ContainerAlignItems>;
                    default: string;
                };
                overflow: {
                    type: PropType<"auto" | "hidden" | "visible">;
                };
                padding: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                gap: {
                    type: BooleanConstructor;
                };
                autoPointer: {
                    type: BooleanConstructor;
                };
            }>> & Readonly<{}>, {
                $vtjEl: ComputedRef<any>;
            }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, {
                padding: boolean;
                tag: string;
                wrap: ContainerWrap;
                fit: boolean;
                flex: boolean;
                inline: boolean;
                direction: ContainerDirection;
                justify: ContainerJustifyContent;
                align: ContainerAlignItems;
                alignContent: ContainerAlignContent;
                grow: boolean;
                shrink: boolean;
                alignSelf: "auto" | ContainerAlignItems;
                gap: boolean;
                autoPointer: boolean;
            }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                beforeCreate?: (() => void) | (() => void)[];
                created?: (() => void) | (() => void)[];
                beforeMount?: (() => void) | (() => void)[];
                mounted?: (() => void) | (() => void)[];
                beforeUpdate?: (() => void) | (() => void)[];
                updated?: (() => void) | (() => void)[];
                activated?: (() => void) | (() => void)[];
                deactivated?: (() => void) | (() => void)[];
                beforeDestroy?: (() => void) | (() => void)[];
                beforeUnmount?: (() => void) | (() => void)[];
                destroyed?: (() => void) | (() => void)[];
                unmounted?: (() => void) | (() => void)[];
                renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
            };
            $forceUpdate: () => void;
            $nextTick: nextTick;
            $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
        } & Readonly<{
            padding: boolean;
            tag: string;
            wrap: ContainerWrap;
            fit: boolean;
            flex: boolean;
            inline: boolean;
            direction: ContainerDirection;
            justify: ContainerJustifyContent;
            align: ContainerAlignItems;
            alignContent: ContainerAlignContent;
            grow: boolean;
            shrink: boolean;
            alignSelf: "auto" | ContainerAlignItems;
            gap: boolean;
            autoPointer: boolean;
        }> & Omit<Readonly< ExtractPropTypes<{
            tag: {
                type: StringConstructor;
                default: string;
            };
            fit: {
                type: BooleanConstructor;
                default: boolean;
            };
            width: {
                type: (StringConstructor | NumberConstructor)[];
            };
            height: {
                type: (StringConstructor | NumberConstructor)[];
            };
            flex: {
                type: BooleanConstructor;
                default: boolean;
            };
            inline: {
                type: BooleanConstructor;
            };
            direction: {
                type: PropType<ContainerDirection>;
                default: string;
            };
            wrap: {
                type: PropType<ContainerWrap>;
                default: string;
            };
            justify: {
                type: PropType<ContainerJustifyContent>;
                default: string;
            };
            align: {
                type: PropType<ContainerAlignItems>;
                default: string;
            };
            alignContent: {
                type: PropType<ContainerAlignContent>;
                default: string;
            };
            grow: {
                type: BooleanConstructor;
                default: boolean;
            };
            shrink: {
                type: BooleanConstructor;
                default: boolean;
            };
            alignSelf: {
                type: PropType<"auto" | ContainerAlignItems>;
                default: string;
            };
            overflow: {
                type: PropType<"auto" | "hidden" | "visible">;
            };
            padding: {
                type: BooleanConstructor;
                default: boolean;
            };
            gap: {
                type: BooleanConstructor;
            };
            autoPointer: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, ("padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer") | "$vtjEl"> & ShallowUnwrapRef<{
            $vtjEl: ComputedRef<any>;
        }> & {} & ComponentCustomProperties & {} & {
            $slots: {
                default?(_: {}): any;
            };
        }) | null;
    };
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: DefineComponent<ExtractPropTypes<{
    badge: {
        type: PropType<PanelBadge>;
    };
    fit: {
        type: BooleanConstructor;
        default: boolean;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    height: {
        type: (StringConstructor | NumberConstructor)[];
    };
    border: {
        type: BooleanConstructor;
        default: boolean;
    };
    radius: {
        type: BooleanConstructor;
        default: boolean;
    };
    card: {
        type: BooleanConstructor;
    };
    size: {
        type: PropType<BaseSize>;
    };
    shadow: {
        type: PropType<"none" | "always" | "hover">;
    };
    header: {
        type: PropType<string | HeaderProps | null>;
    };
    body: {
        type: PropType<ContainerProps>;
    };
    footer: {
        type: PropType<ContainerProps>;
    };
}>, {
    bodyRef: Ref<any, any>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly< ExtractPropTypes<{
    badge: {
        type: PropType<PanelBadge>;
    };
    fit: {
        type: BooleanConstructor;
        default: boolean;
    };
    width: {
        type: (StringConstructor | NumberConstructor)[];
    };
    height: {
        type: (StringConstructor | NumberConstructor)[];
    };
    border: {
        type: BooleanConstructor;
        default: boolean;
    };
    radius: {
        type: BooleanConstructor;
        default: boolean;
    };
    card: {
        type: BooleanConstructor;
    };
    size: {
        type: PropType<BaseSize>;
    };
    shadow: {
        type: PropType<"none" | "always" | "hover">;
    };
    header: {
        type: PropType<string | HeaderProps | null>;
    };
    body: {
        type: PropType<ContainerProps>;
    };
    footer: {
        type: PropType<ContainerProps>;
    };
}>> & Readonly<{}>, {
    radius: boolean;
    fit: boolean;
    border: boolean;
    card: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, true, {
    bodyRef: ({
        $: ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            padding: boolean;
            tag: string;
            wrap: ContainerWrap;
            fit: boolean;
            flex: boolean;
            inline: boolean;
            direction: ContainerDirection;
            justify: ContainerJustifyContent;
            align: ContainerAlignItems;
            alignContent: ContainerAlignContent;
            grow: boolean;
            shrink: boolean;
            alignSelf: "auto" | ContainerAlignItems;
            gap: boolean;
            autoPointer: boolean;
        }> & Omit<{
            readonly padding: boolean;
            readonly tag: string;
            readonly wrap: ContainerWrap;
            readonly fit: boolean;
            readonly flex: boolean;
            readonly inline: boolean;
            readonly direction: ContainerDirection;
            readonly justify: ContainerJustifyContent;
            readonly align: ContainerAlignItems;
            readonly alignContent: ContainerAlignContent;
            readonly grow: boolean;
            readonly shrink: boolean;
            readonly alignSelf: "auto" | ContainerAlignItems;
            readonly gap: boolean;
            readonly autoPointer: boolean;
            readonly width?: string | number | undefined;
            readonly height?: string | number | undefined;
            readonly overflow?: "hidden" | "auto" | "visible" | undefined;
        } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        } & {
            elRef: unknown;
        };
        $slots: Readonly<{
            [name: string]: Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
            tag: {
                type: StringConstructor;
                default: string;
            };
            fit: {
                type: BooleanConstructor;
                default: boolean;
            };
            width: {
                type: (StringConstructor | NumberConstructor)[];
            };
            height: {
                type: (StringConstructor | NumberConstructor)[];
            };
            flex: {
                type: BooleanConstructor;
                default: boolean;
            };
            inline: {
                type: BooleanConstructor;
            };
            direction: {
                type: PropType<ContainerDirection>;
                default: string;
            };
            wrap: {
                type: PropType<ContainerWrap>;
                default: string;
            };
            justify: {
                type: PropType<ContainerJustifyContent>;
                default: string;
            };
            align: {
                type: PropType<ContainerAlignItems>;
                default: string;
            };
            alignContent: {
                type: PropType<ContainerAlignContent>;
                default: string;
            };
            grow: {
                type: BooleanConstructor;
                default: boolean;
            };
            shrink: {
                type: BooleanConstructor;
                default: boolean;
            };
            alignSelf: {
                type: PropType<"auto" | ContainerAlignItems>;
                default: string;
            };
            overflow: {
                type: PropType<"auto" | "hidden" | "visible">;
            };
            padding: {
                type: BooleanConstructor;
                default: boolean;
            };
            gap: {
                type: BooleanConstructor;
            };
            autoPointer: {
                type: BooleanConstructor;
            };
        }>> & Readonly<{}>, {
            $vtjEl: ComputedRef<any>;
        }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, {
            padding: boolean;
            tag: string;
            wrap: ContainerWrap;
            fit: boolean;
            flex: boolean;
            inline: boolean;
            direction: ContainerDirection;
            justify: ContainerJustifyContent;
            align: ContainerAlignItems;
            alignContent: ContainerAlignContent;
            grow: boolean;
            shrink: boolean;
            alignSelf: "auto" | ContainerAlignItems;
            gap: boolean;
            autoPointer: boolean;
        }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
    } & Readonly<{
        padding: boolean;
        tag: string;
        wrap: ContainerWrap;
        fit: boolean;
        flex: boolean;
        inline: boolean;
        direction: ContainerDirection;
        justify: ContainerJustifyContent;
        align: ContainerAlignItems;
        alignContent: ContainerAlignContent;
        grow: boolean;
        shrink: boolean;
        alignSelf: "auto" | ContainerAlignItems;
        gap: boolean;
        autoPointer: boolean;
    }> & Omit<Readonly< ExtractPropTypes<{
        tag: {
            type: StringConstructor;
            default: string;
        };
        fit: {
            type: BooleanConstructor;
            default: boolean;
        };
        width: {
            type: (StringConstructor | NumberConstructor)[];
        };
        height: {
            type: (StringConstructor | NumberConstructor)[];
        };
        flex: {
            type: BooleanConstructor;
            default: boolean;
        };
        inline: {
            type: BooleanConstructor;
        };
        direction: {
            type: PropType<ContainerDirection>;
            default: string;
        };
        wrap: {
            type: PropType<ContainerWrap>;
            default: string;
        };
        justify: {
            type: PropType<ContainerJustifyContent>;
            default: string;
        };
        align: {
            type: PropType<ContainerAlignItems>;
            default: string;
        };
        alignContent: {
            type: PropType<ContainerAlignContent>;
            default: string;
        };
        grow: {
            type: BooleanConstructor;
            default: boolean;
        };
        shrink: {
            type: BooleanConstructor;
            default: boolean;
        };
        alignSelf: {
            type: PropType<"auto" | ContainerAlignItems>;
            default: string;
        };
        overflow: {
            type: PropType<"auto" | "hidden" | "visible">;
        };
        padding: {
            type: BooleanConstructor;
            default: boolean;
        };
        gap: {
            type: BooleanConstructor;
        };
        autoPointer: {
            type: BooleanConstructor;
        };
    }>> & Readonly<{}>, ("padding" | "tag" | "wrap" | "fit" | "flex" | "inline" | "direction" | "justify" | "align" | "alignContent" | "grow" | "shrink" | "alignSelf" | "gap" | "autoPointer") | "$vtjEl"> & ShallowUnwrapRef<{
        $vtjEl: ComputedRef<any>;
    }> & {} & ComponentCustomProperties & {} & {
        $slots: {
            default?(_: {}): any;
        };
    }) | null;
}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
