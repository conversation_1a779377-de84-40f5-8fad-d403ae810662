import { template } from '@vtj/node';
import { BlockSchema, ProjectSchema } from '@vtj/core';
import OpenAI from 'openai';
import { Observable } from 'rxjs';
import { UserTopicDto } from './dto/user-topic.dto';
import { Topic } from './entities/topic.entity';
import { Chat } from './entities/chat.entify';
import { ChatStatus, PromptTemplateData, RESPONSE_PREFIX } from './types';
import { SettingsService } from '../settings/settings.service';
import { AI } from '../shared';

export interface UIMetaDataJson {
  type: 'sketch' | 'figma' | 'Sketch' | 'Figma';
  created: string;
  layers: any[];
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class AIHepler {
  private openai: OpenAI;
  private completionControllers: Record<string, AbortController> = {};
  private keyIndex: number = -1;
  constructor(
    private setting: SettingsService,
    private aiConfig: AI
  ) {
    const { apiKey, baseURL } = aiConfig;
    this.openai = new OpenAI({
      apiKey,
      baseURL
    });
  }

  private createOpenAiClient() {
    const { apiKeys, baseURL, apiKey } = this.aiConfig;
    let index = ++this.keyIndex;
    if (index > apiKeys.length - 1) {
      this.keyIndex = index = 0;
    }
    return new OpenAI({
      baseURL,
      apiKey: apiKeys[index] || apiKey
    });
  }

  async createTopic(dto: UserTopicDto, title?: string) {
    const data = this.createTemplateData(dto);
    const content = await this.createSystemContent(data);
    const topic = new Topic();
    topic.model = dto.model;
    topic.title = title || dto.prompt.replace(/\n/g, '').substring(0, 100);
    topic.prompt = dto.prompt;
    topic.content = content;
    topic.isHot = false;
    topic.projectId = data.projectId;
    topic.projectName = data.projectId;
    topic.userId = dto.userId;
    topic.userName = dto.userName;
    topic.appId = data.appId;
    topic.fileId = data.fileId;
    topic.fileName = data.fileName;
    topic.platform = data.platform;
    topic.type = dto.type;

    const chat = new Chat();
    chat.userId = dto.userId;
    chat.userName = dto.userName;
    chat.prompt = dto.prompt;
    chat.status = ChatStatus.Pending;

    return {
      topic,
      chat
    };
  }

  completion(topic: Topic, chats: Chat[]) {
    return new Observable((ob) => {
      const abortController = new AbortController();
      const chatId = chats[chats.length - 1]?.id;
      if (chatId) {
        this.completionControllers[chatId] = abortController;
      }
      (async () => {
        const params = this.createCompletionParams(topic, chats);
        const openai = this.createOpenAiClient();
        const stream = await openai.chat.completions
          .create(params, {
            signal: abortController.signal // 关键：绑定中止信号
          })
          .catch((err) => {
            ob.error(err);
            return null;
          });
        if (!stream) return;
        try {
          for await (const chunk of stream as AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>) {
            ob.next(chunk);
          }
          ob.complete();
        } catch (err) {
          // 区分主动取消和其他错误
          ob.error({
            error: abortController.signal.aborted
              ? 'AbortError'
              : err.message || 'OpenAI API Error',
            statusCode: err.status || 500
          });
        }
        delete this.completionControllers[chatId];
      })();
      // 客户端断开时的清理逻辑
      return () => {
        abortController.abort(); // 关键：触发请求中止
      };
    });
  }

  cancelCompletion(chatId: string) {
    const abortController = this.completionControllers[chatId];
    if (abortController) {
      abortController.abort();
    }
  }

  private createTemplateData(dto: UserTopicDto): PromptTemplateData {
    const dsl: BlockSchema =
      typeof dto.dsl === 'string' ? JSON.parse(dto.dsl) : dto.dsl;
    const project: ProjectSchema =
      typeof dto.project === 'string' ? JSON.parse(dto.project) : dto.project;
    const { id: fileId, name: fileName } = dsl;
    const dependencies = project.dependencies
      .filter((n) => !!n.enabled)
      .map((n) => n.package)
      .join(', ');
    return {
      fileId,
      fileName,
      dependencies,
      source: dto.source,
      platform: (project.platform || 'web') as any,
      projectId: (project as any).__UID__ || project.id,
      projectName: project.name,
      appId: project.id
    };
  }

  private createCompletionParams(topic: Topic, chats: Chat[]) {
    const messages = this.createMessages(topic, chats);
    return {
      model: topic.model,
      temperature: 0,
      max_tokens: 8 * 1024,
      stream: true,
      messages
    } as OpenAI.Chat.Completions.ChatCompletionCreateParamsStreaming;
  }

  private createMessages(topic: Topic, chats: Chat[]) {
    const messages = this.createUserMessages(chats);
    return [
      {
        role: 'system',
        content: topic.content
      },
      ...messages
    ];
  }

  private async createSystemContent(data: PromptTemplateData) {
    const setting = await this.setting.get();
    const compiled = template(setting?.promptTemplate || '');
    return compiled(data);
  }

  private createUserMessages(chats: Chat[]) {
    const messages: AIMessage[] = [];
    chats.forEach((chat, index) => {
      if (chat.prompt) {
        messages.push({
          role: 'user',
          content: chat.prompt
        });
      }
      // 最后一个不需要 assistant
      if (index < chats.length - 1) {
        if (chat.content) {
          messages.push({
            role: 'assistant',
            content: chat.content
          });
        }
      }
    });
    return messages;
  }

  private async createImageSystemContent(data: any = {}) {
    const setting = await this.setting.get();
    const compiled = template(setting?.imagePromptTemplate || '');
    return compiled({
      RESPONSE_PREFIX,
      ...data
    });
  }

  async generateImagePrompt(base64Image: string) {
    let result: string = '';
    let tokens: number = 0;
    const setting = await this.setting.get();
    const systemContent = await this.createImageSystemContent({});
    const messages = [
      {
        role: 'system',
        content: systemContent
      },
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `please generate a prompt for a frontend developer to implement an web application based on the image.`
          },
          {
            type: 'image_url',
            image_url: {
              url: `data:image/jpeg;base64,${base64Image}`
            }
          }
        ]
      }
    ];
    try {
      const openapi = new OpenAI({
        baseURL: this.aiConfig.openrouter,
        apiKey: setting.imageApiKey
      });
      const stream = await openapi.chat.completions.create({
        model: setting.imageModel,
        messages: messages as any,
        temperature: 0.2,
        stream: true
      });

      if (stream) {
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || '';
          result += content;
          if (chunk.usage) {
            tokens = chunk.usage.total_tokens || 0;
          }
        }
      }
      return {
        content: result,
        tokens
      };
    } catch (error) {
      console.error(`Error calling ${setting.jsonModel} API:`, error);
      throw error;
    }
  }

  private async createJsonSystemContent(data: UIMetaDataJson) {
    const setting = await this.setting.get();
    const compiled = template(setting?.jsonPromptTemplate || '');
    return compiled(data);
  }

  async generateJsonPrompt(data: UIMetaDataJson) {
    let result: string = '';
    let tokens: number = 0;
    const setting = await this.setting.get();
    const systemContent = await this.createJsonSystemContent(data);
    const messages = [
      {
        role: 'system',
        content: systemContent
      },
      {
        role: 'user',
        content: JSON.stringify(data)
      }
    ];
    try {
      const openapi = new OpenAI({
        baseURL: this.aiConfig.openrouter,
        apiKey: setting.jsonApiKey
      });
      const stream = await openapi.chat.completions.create({
        model: setting.jsonModel,
        messages: messages as any,
        temperature: 0.2,
        stream: true
      });

      if (stream) {
        for await (const chunk of stream) {
          const content = chunk.choices[0]?.delta?.content || '';
          result += content;
          if (chunk.usage) {
            tokens = chunk.usage.total_tokens || 0;
          }
        }
      }
      return {
        content: result,
        tokens
      };
    } catch (error) {
      console.error(`Error calling ${setting.jsonModel} API:`, error);
      throw error;
    }
  }
}
