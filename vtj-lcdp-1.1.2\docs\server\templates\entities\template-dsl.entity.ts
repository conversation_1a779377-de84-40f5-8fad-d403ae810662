import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../../shared';

@Entity({
  name: 'template_dsl',
  comment: 'DSL',
  orderBy: {
    createdAt: 'DESC'
  }
})
export class TemplateDsl extends BaseEntity {
  @Column({ name: 'template_id', comment: '关联id' })
  templateId: string;

  @Column({ comment: '自增版本号', default: 1 })
  version: number;

  @Column({ comment: '语义化版本号：0.0.0' })
  label: string;

  @Column({ type: 'json', comment: '内容' })
  content: object;

  @Column({ type: 'text', comment: '更新日志' })
  changelog: string;
}
