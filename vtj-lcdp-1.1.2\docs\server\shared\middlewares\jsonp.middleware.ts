import { Injectable, NestMiddleware } from '@nestjs/common';
import type { Request } from 'express';

@Injectable()
export class JsonpMiddleware implements NestMiddleware {
  use(req: Request, res: any, next: any) {
    if (req.query && req.query.hasOwnProperty('callback')) {
      const callback = req.query.callback;
      res.jsonp = (data: any) => {
        res.setHeader('Content-Type', 'application/javascript');
        res.send(`${callback}(${JSON.stringify(data)})`);
      };
    }
    next();
  }
}
