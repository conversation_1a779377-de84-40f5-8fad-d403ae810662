(function(n,c){typeof exports=="object"&&typeof module!="undefined"?c(exports):typeof define=="function"&&define.amd?define(["exports"],c):(n=typeof globalThis!="undefined"?globalThis:n||self,c(n.UniApp={}))})(this,function(n){"use strict";const c=Object.assign,d=Object.prototype.hasOwnProperty,u=(o,e)=>d.call(o,e),h=Object.prototype.toString,g=o=>h.call(o),O=o=>g(o)==="[object Object]",A=(o=>{const e=Object.create(null);return a=>e[a]||(e[a]=o(a))})(o=>o.charAt(0).toUpperCase()+o.slice(1)),R="__uniSSR",N="data",T="globalData",f="onShow",I="onHide",E="onLaunch",C="onError",B="onThemeChange",P="onPageNotFound",v="onUnhandledRejection",w="onExit",b="onLoad",m="onReady",D="onUnload",H="onInit",L="onSaveExitState",U="onResize",p="onBackPress",y="onPageScroll",F="onTabItemTap",V="onReachBottom",j="onPullDownRefresh",G="onShareTimeline",M="onAddToFavorites",k="onShareAppMessage",z="onNavigationBarButtonTap",q="onNavigationBarSearchInputClicked",J="onNavigationBarSearchInputChanged",K="onNavigationBarSearchInputConfirmed",W="onNavigationBarSearchInputFocusChanged",X=o=>o&&JSON.parse(JSON.stringify(o))||o;function Y(){return Vue.getCurrentInstance()?N:T}function Z(o,e=!1){if(!o)throw new Error(`${e?"shallowSsrRef":"ssrRef"}: You must provide a key.`)}const S=(o,e,a=!1)=>{const i=a?Vue.shallowRef(o):Vue.ref(o);if(typeof window=="undefined")return i;const s=window[R];if(!s)return i;const r=Y();return Z(e,a),u(s[r],e)&&(i.value=s[r][e],r===N&&delete s[r][e]),i},$={},Q=(o,e)=>S(o,e),x=(o,e)=>S(o,e,!0);function nn(){return X($)}function on(){return uni.getSubNVueById(plus.webview.currentWebview().id)}function en(o){return weex.requireModule(o)}function tn(o,e,...a){uni.__log__?uni.__log__(o,e,...a):console[o].apply(console,[...a,e])}function an(o,e,...a){e&&a.push(e),console[o].apply(console,a)}function cn(o,e){return typeof o=="string"?e:o}const t=o=>(e,a=Vue.getCurrentInstance())=>{!Vue.isInSSRComponentSetup&&Vue.injectHook(o,e,a)},_=t(f),l=t(I),sn=t(E),rn=t(C),un=t(B),Nn=t(P),Sn=t(v),_n=t(w),ln=t(H),dn=t(b),hn=t(m),gn=t(D),On=t(U),An=t(p),Rn=t(y),Tn=t(F),fn=t(V),In=t(j),En=t(L),Cn=t(G),Bn=t(M),Pn=t(k),vn=t(z),wn=t(J),bn=t(q),mn=t(K),Dn=t(W),Hn=l,Ln=_;function Un(o,e,a=null){return o[e]?o[e](a):null}n.capitalize=A,n.extend=c,n.formatAppLog=tn,n.formatLog=an,n.getCurrentSubNVue=on,n.getSsrGlobalData=nn,n.hasOwn=u,n.isPlainObject=O,n.onAddToFavorites=Bn,n.onBackPress=An,n.onError=rn,n.onExit=_n,n.onHide=l,n.onInit=ln,n.onLaunch=sn,n.onLoad=dn,n.onNavigationBarButtonTap=vn,n.onNavigationBarSearchInputChanged=wn,n.onNavigationBarSearchInputClicked=bn,n.onNavigationBarSearchInputConfirmed=mn,n.onNavigationBarSearchInputFocusChanged=Dn,n.onPageHide=Hn,n.onPageNotFound=Nn,n.onPageScroll=Rn,n.onPageShow=Ln,n.onPullDownRefresh=In,n.onReachBottom=fn,n.onReady=hn,n.onResize=On,n.onSaveExitState=En,n.onShareAppMessage=Pn,n.onShareTimeline=Cn,n.onShow=_,n.onTabItemTap=Tn,n.onThemeChange=un,n.onUnhandledRejection=Sn,n.onUnload=gn,n.renderComponentSlot=Un,n.requireNativePlugin=en,n.resolveEasycom=cn,n.shallowSsrRef=x,n.ssrRef=Q,Object.defineProperty(n,Symbol.toStringTag,{value:"Module"})});
