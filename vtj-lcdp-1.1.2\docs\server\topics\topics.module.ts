import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TopicsService } from './topics.service';
import { TopicsController } from './topics.controller';
import { Topic } from './entities/topic.entity';
import { Chat } from './entities/chat.entify';
import { OrdersModule } from '../orders/orders.module';
import { OrdersService } from '../orders/orders.service';

@Module({
  imports: [TypeOrmModule.forFeature([Topic, Chat]), OrdersModule],
  controllers: [TopicsController],
  providers: [TopicsService, OrdersService],
  exports: [TypeOrmModule, TopicsService]
})
export class TopicsModule {}
