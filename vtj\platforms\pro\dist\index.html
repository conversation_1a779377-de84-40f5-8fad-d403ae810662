<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="./logo.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>低代码设计器 - VTJ.PRO</title>
           <script>
           var __BUILD_TIME__ = '1754272953728';
           (function () {
            const regex = /<!--{{(\d*)}}-->/;
            window.fetch('?t=' + Date.now()).then(async (res) => {
              const content = await res.text();
              if (content) {
                const result = content.match(regex);
                if (result && result[1]) {
                  if (result[1] !== __BUILD_TIME__) {
                    top.location.reload(true);
                  }
                }
              }
            });
          })();
           </script>
           <!--{{1754272953728}}-->
          
    <script type="module" crossorigin src="./assets/index-BPeVewtC.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/vue-ipWmmxHk.js">
    <link rel="modulepreload" crossorigin href="./assets/lodash-es-BL-d_OSa.js">
    <link rel="modulepreload" crossorigin href="./assets/@vueuse-WO_0ftym.js">
    <link rel="modulepreload" crossorigin href="./assets/@element-plus-icons-vue-0BR09xN9.js">
    <link rel="modulepreload" crossorigin href="./assets/shared-Bnc4f-Fv.js">
    <link rel="modulepreload" crossorigin href="./assets/dayjs-DO-COJPZ.js">
    <link rel="modulepreload" crossorigin href="./assets/element-plus-COProxbp.js">
    <link rel="modulepreload" crossorigin href="./assets/monaco-editor-B8sWZqMY.js">
    <link rel="modulepreload" crossorigin href="./assets/vue-router-CezURGfh.js">
    <link rel="stylesheet" crossorigin href="./assets/element-plus-CxJJf0IB.css">
    <link rel="stylesheet" crossorigin href="./assets/monaco-editor-Cpw7ccEV.css">
    <link rel="stylesheet" crossorigin href="./assets/index-1XTA0ffa.css">
  
<style>
.dark {
  background-color: #141414;
}
#vtj-ide-loading {
  width: 240px;
  height: 100px;
  position: fixed;
  left:50%;
  top: 50%;
  transform: translate(-50%,-50%);
  z-index: 9999;
  border-radius: 4px;
  background-color: rgba(255,255,255,0.1);
  box-shadow: 0 0 10px rgba(0,0,0,0.2);
  padding: 20px;
  font-size: 12px;
  box-sizing: border-box;
}
.vtj-ide-loading__bar {
  height: 14px;
  border-radius: 4px;
  margin-top: 10px;
  border: 1px solid #eee;
  overflow: hidden;
}
.vtj-ide-loading__title {
  display: flex;
  justify-content: space-between;
}
.dark .vtj-ide-loading__title {
  color:#fff;
}
.vtj-ide-loading__value {
  display: block;
  height: 100%;
  background-color: #409eff;
  border-radius: 4px;
  font-size: 0;
}
</style>
</head>
  <body>
<div id="vtj-ide-loading">
<div class="vtj-ide-loading__title"><span>正在加载资源.... </span><span id="vtj-ide-loading-count"></span></div>
<div class="vtj-ide-loading__bar">
  <span class="vtj-ide-loading__value" id="vtj-ide-loading-value" style="width:0%"></span>
</div>
</div>

    <div id="app"></div>

  
<script>
(function(){
  var loading = document.querySelector('#vtj-ide-loading');
  var countEl = document.querySelector('#vtj-ide-loading-count');
  var valueEl = document.querySelector('#vtj-ide-loading-value');
  var links = document.querySelectorAll('link,script');
  var total = links.length;
  var setValue = function(current) {
    countEl.innerHTML = current + '/' +total;
    valueEl.style.width = (current * 100 / total) + '%';
    if(!total || current === total) {
      setTimeout(function() {
         if(loading.parentNode){
           loading.parentNode.removeChild(loading);
         }
         loading = null;
         countEl = null;
         valueEl = null;
      }, 100);
    }
  }

  var current = 0;
  links.forEach(function(link) {
    link.onload = function() {
        ++current;
        setValue(current);
        link.onload = null;
    }
  })
  window.addEventListener('load',function() {
    current = total
    setValue(current)
  });

})()

</script> 

        <script>
        (function () {
          window._hmt = window._hmt || [];
          const hm = document.createElement('script');
          hm.src = 'https://hm.baidu.com/hm.js?42f2469b4aa27c3f8978f634c0c19d24';
          var s = document.getElementsByTagName('script')[0];
          s.parentNode.insertBefore(hm, s);
        })();       
        </script>
        
</html>
