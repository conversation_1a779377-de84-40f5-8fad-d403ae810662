<template>
  <component v-if="renderer" :is="renderer"></component>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useProvider } from '@vtj/renderer';
const route = useRoute();
const router = useRouter();
const provider = useProvider();
const renderer = ref();

// 从url获取文件id，如果无id即获取主页id
const fileId = (route.params.id || provider.getHomepage()?.id) as string;
if (fileId) {
  renderer.value = await provider.getRenderComponent(fileId, (file) => {
    // 设置页面标题
    document.title = file.title;
  });
  if (!renderer.value) {
    router.push(`/${route.params.app}/404`);
  }
} else {
  router.push(`/${route.params.app}/404`);
}
</script>
