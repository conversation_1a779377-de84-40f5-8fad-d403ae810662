import { IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto, OAuthType } from '../../shared';
import { User } from '../entities/user.entity';

export class QueryUserDto extends QueryDto<User> {
  keyword?: string;

  @IsOptional()
  @IsBoolean()
  @Transform((v) => v.value === true || v.value === 'true')
  freeze?: boolean;

  oauthType?: OAuthType;
}
