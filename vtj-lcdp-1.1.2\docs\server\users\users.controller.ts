import { Controller, Body, Post, Delete, Get, Query } from '@nestjs/common';
import { UsersService } from './users.service';
import { UserDto } from './dto/user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { Public, User } from '../shared';
import { RegisterUserDto } from './dto/register-user.dto';

@Controller('users')
export class UsersController {
  constructor(private readonly service: UsersService) {}

  @Post()
  save(@Body() dto: UserDto) {
    return this.service.save(dto);
  }

  @Delete()
  remove(@Body() ids: string[]) {
    return this.service.remove(ids);
  }

  @Get()
  find(@Query() dto: QueryUserDto) {
    return this.service.find(dto);
  }

  @Post('resetPassword')
  resetPassword(@Body('id') id: string) {
    return this.service.resetPassword(id);
  }

  @Public()
  @Post('login')
  async login(
    @Body('account') account: string,
    @Body('password') password: string
  ) {
    return this.service.loginByPassword(account, password);
  }

  @Post('modifyPassword')
  async modifyPassword(
    @Body('oldPassword') oldPassword: string,
    @Body('newPassword') newPassword: string,
    @User() user: any
  ) {
    return await this.service.modifyPassword(user.id, oldPassword, newPassword);
  }

  @Public()
  @Get('logout')
  logout(@User('token') token: string) {
    return this.service.logout(token);
  }

  @Get('sign')
  async getUserSign(@User('token') token: string) {
    const user = (await this.service.getLoginUserByToken(token, false)) as any;
    return await this.service.getUserSign(user.id);
  }

  @Public()
  @Post('send/mail/code')
  async mailVaildateCode(@Body('email') email: string) {
    return await this.service.sendMailVaildateCode(email);
  }

  @Public()
  @Post('register')
  async register(@Body() dto: RegisterUserDto) {
    return await this.service.userRegister(dto);
  }

  @Public()
  @Post('password')
  async userFindPassword(@Body() dto: RegisterUserDto) {
    return await this.service.userFindPassword(dto);
  }
}
