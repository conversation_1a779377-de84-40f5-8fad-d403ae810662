var Ue=Object.defineProperty,Ke=Object.defineProperties;var qe=Object.getOwnPropertyDescriptors;var K=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,he=Object.prototype.propertyIsEnumerable;var pe=(e,t,n)=>t in e?Ue(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,V=(e,t)=>{for(var n in t||(t={}))me.call(t,n)&&pe(e,n,t[n]);if(K)for(var n of K(t))he.call(t,n)&&pe(e,n,t[n]);return e},Y=(e,t)=>Ke(e,qe(t));var G=(e,t)=>{var n={};for(var o in e)me.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&K)for(var o of K(e))t.indexOf(o)<0&&he.call(e,o)&&(n[o]=e[o]);return n};import{g as $e,b as Me,s as C,w as We,r as ee,e as b,f as R,h as U,i as ue,n as ae,u as Ae,j as le,k as x,t as Ze,l as et,m as tt,p as S,q as Ie,v as nt}from"./vue-ipWmmxHk.js";var ot=Object.defineProperty,rt=Object.defineProperties,st=Object.getOwnPropertyDescriptors,ge=Object.getOwnPropertySymbols,it=Object.prototype.hasOwnProperty,ut=Object.prototype.propertyIsEnumerable,ye=(e,t,n)=>t in e?ot(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,at=(e,t)=>{for(var n in t||(t={}))it.call(t,n)&&ye(e,n,t[n]);if(ge)for(var n of ge(t))ut.call(t,n)&&ye(e,n,t[n]);return e},lt=(e,t)=>rt(e,st(t));function fn(e,t){var n;const o=C();return We(()=>{o.value=e()},lt(at({},t),{flush:(n=void 0)!=null?n:"sync"})),ee(o)}var we;const te=typeof window!="undefined",ct=e=>typeof e!="undefined",ie=e=>typeof e=="function",ft=e=>typeof e=="string",Q=()=>{},dt=te&&((we=window==null?void 0:window.navigator)==null?void 0:we.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function H(e){return typeof e=="function"?e():Ae(e)}function De(e,t){function n(...o){return new Promise((s,r)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(s).catch(r)})}return n}function vt(e,t={}){let n,o,s=Q;const r=i=>{clearTimeout(i),s(),s=Q};return i=>{const a=H(e),f=H(t.maxWait);return n&&r(n),a<=0||f!==void 0&&f<=0?(o&&(r(o),o=null),Promise.resolve(i())):new Promise((v,l)=>{s=t.rejectOnCancel?l:v,f&&!o&&(o=setTimeout(()=>{n&&r(n),o=null,v(i())},f)),n=setTimeout(()=>{o&&r(o),o=null,v(i())},a)})}}function pt(e,t=!0,n=!0,o=!1){let s=0,r,u=!0,i=Q,a;const f=()=>{r&&(clearTimeout(r),r=void 0,i(),i=Q)};return l=>{const c=H(e),d=Date.now()-s,p=()=>a=l();return f(),c<=0?(s=Date.now(),p()):(d>c&&(n||!u)?(s=Date.now(),p()):t&&(a=new Promise((g,h)=>{i=o?h:g,r=setTimeout(()=>{s=Date.now(),u=!0,g(p()),f()},Math.max(0,c-d))})),!n&&!r&&(r=setTimeout(()=>u=!0,c)),u=!1,a)}}function mt(e){return e}function ht(e,t){let n,o,s;const r=b(!0),u=()=>{r.value=!0,s()};R(e,u,{flush:"sync"});const i=ie(t)?t:t.get,a=ie(t)?void 0:t.set,f=le((v,l)=>(o=v,s=l,{get(){return r.value&&(n=i(),r.value=!1),o(),n},set(c){a==null||a(c)}}));return Object.isExtensible(f)&&(f.trigger=u),f}function ne(e){return $e()?(Me(e),!0):!1}function gt(e,t=200,n={}){return De(vt(t,n),e)}function dn(e,t=200,n={}){const o=b(e.value),s=gt(()=>{o.value=e.value},t,n);return R(e,()=>s()),o}function vn(e,t=200,n=!1,o=!0,s=!1){return De(pt(t,n,o,s),e)}function ce(e,t=!0){U()?ue(e):t?e():ae(e)}function pn(e,t,n={}){const{immediate:o=!0}=n,s=b(!1);let r=null;function u(){r&&(clearTimeout(r),r=null)}function i(){s.value=!1,u()}function a(...f){u(),s.value=!0,r=setTimeout(()=>{s.value=!1,r=null,e(...f)},H(t))}return o&&(s.value=!0,te&&a()),ne(i),{isPending:ee(s),start:a,stop:i}}function L(e){var t;const n=H(e);return(t=n==null?void 0:n.$el)!=null?t:n}const B=te?window:void 0,yt=te?window.document:void 0;function F(...e){let t,n,o,s;if(ft(e[0])||Array.isArray(e[0])?([n,o,s]=e,t=B):[t,n,o,s]=e,!t)return Q;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const r=[],u=()=>{r.forEach(v=>v()),r.length=0},i=(v,l,c,d)=>(v.addEventListener(l,c,d),()=>v.removeEventListener(l,c,d)),a=R(()=>[L(t),H(s)],([v,l])=>{u(),v&&r.push(...n.flatMap(c=>o.map(d=>i(v,c,d,l))))},{immediate:!0,flush:"post"}),f=()=>{a(),u()};return ne(f),f}let be=!1;function mn(e,t,n={}){const{window:o=B,ignore:s=[],capture:r=!0,detectIframe:u=!1}=n;if(!o)return;dt&&!be&&(be=!0,Array.from(o.document.body.children).forEach(c=>c.addEventListener("click",Q)));let i=!0;const a=c=>s.some(d=>{if(typeof d=="string")return Array.from(o.document.querySelectorAll(d)).some(p=>p===c.target||c.composedPath().includes(p));{const p=L(d);return p&&(c.target===p||c.composedPath().includes(p))}}),v=[F(o,"click",c=>{const d=L(e);if(!(!d||d===c.target||c.composedPath().includes(d))){if(c.detail===0&&(i=!a(c)),!i){i=!0;return}t(c)}},{passive:!0,capture:r}),F(o,"pointerdown",c=>{const d=L(e);d&&(i=!c.composedPath().includes(d)&&!a(c))},{passive:!0}),u&&F(o,"blur",c=>{var d;const p=L(e);((d=o.document.activeElement)==null?void 0:d.tagName)==="IFRAME"&&!(p!=null&&p.contains(o.document.activeElement))&&t(c)})].filter(Boolean);return()=>v.forEach(c=>c())}function hn(e={}){var t;const{window:n=B}=e,o=(t=e.document)!=null?t:n==null?void 0:n.document,s=ht(()=>null,()=>o==null?void 0:o.activeElement);return n&&(F(n,"blur",r=>{r.relatedTarget===null&&s.trigger()},!0),F(n,"focus",s.trigger,!0)),s}function Le(e,t=!1){const n=b(),o=()=>n.value=!!e();return o(),ce(o,t),n}function wt(e){return JSON.parse(JSON.stringify(e))}const Oe=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},_e="__vueuse_ssr_handlers__";Oe[_e]=Oe[_e]||{};function gn(e,t,{window:n=B,initialValue:o=""}={}){const s=b(o),r=x(()=>{var u;return L(t)||((u=n==null?void 0:n.document)==null?void 0:u.documentElement)});return R([r,()=>H(e)],([u,i])=>{var a;if(u&&n){const f=(a=n.getComputedStyle(u).getPropertyValue(i))==null?void 0:a.trim();s.value=f||o}},{immediate:!0}),R(s,u=>{var i;(i=r.value)!=null&&i.style&&r.value.style.setProperty(H(e),u)}),s}function yn({document:e=yt}={}){if(!e)return b("visible");const t=b(e.visibilityState);return F(e,"visibilitychange",()=>{t.value=e.visibilityState}),t}var Se=Object.getOwnPropertySymbols,bt=Object.prototype.hasOwnProperty,Ot=Object.prototype.propertyIsEnumerable,_t=(e,t)=>{var n={};for(var o in e)bt.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&Se)for(var o of Se(e))t.indexOf(o)<0&&Ot.call(e,o)&&(n[o]=e[o]);return n};function ze(e,t,n={}){const o=n,{window:s=B}=o,r=_t(o,["window"]);let u;const i=Le(()=>s&&"ResizeObserver"in s),a=()=>{u&&(u.disconnect(),u=void 0)},f=R(()=>L(e),l=>{a(),i.value&&s&&l&&(u=new ResizeObserver(t),u.observe(l,r))},{immediate:!0,flush:"post"}),v=()=>{a(),f()};return ne(v),{isSupported:i,stop:v}}function wn(e,t={}){const{reset:n=!0,windowResize:o=!0,windowScroll:s=!0,immediate:r=!0}=t,u=b(0),i=b(0),a=b(0),f=b(0),v=b(0),l=b(0),c=b(0),d=b(0);function p(){const g=L(e);if(!g){n&&(u.value=0,i.value=0,a.value=0,f.value=0,v.value=0,l.value=0,c.value=0,d.value=0);return}const h=g.getBoundingClientRect();u.value=h.height,i.value=h.bottom,a.value=h.left,f.value=h.right,v.value=h.top,l.value=h.width,c.value=h.x,d.value=h.y}return ze(e,p),R(()=>L(e),g=>!g&&p()),s&&F("scroll",p,{capture:!0,passive:!0}),o&&F("resize",p,{passive:!0}),ce(()=>{r&&p()}),{height:u,bottom:i,left:a,right:f,top:v,width:l,x:c,y:d,update:p}}function bn(e,t={width:0,height:0},n={}){const{window:o=B,box:s="content-box"}=n,r=x(()=>{var a,f;return(f=(a=L(e))==null?void 0:a.namespaceURI)==null?void 0:f.includes("svg")}),u=b(t.width),i=b(t.height);return ze(e,([a])=>{const f=s==="border-box"?a.borderBoxSize:s==="content-box"?a.contentBoxSize:a.devicePixelContentBoxSize;if(o&&r.value){const v=L(e);if(v){const l=o.getComputedStyle(v);u.value=parseFloat(l.width),i.value=parseFloat(l.height)}}else if(f){const v=Array.isArray(f)?f:[f];u.value=v.reduce((l,{inlineSize:c})=>l+c,0),i.value=v.reduce((l,{blockSize:c})=>l+c,0)}else u.value=a.contentRect.width,i.value=a.contentRect.height},n),R(()=>L(e),a=>{u.value=a?t.width:0,i.value=a?t.height:0}),{width:u,height:i}}var Ee=Object.getOwnPropertySymbols,St=Object.prototype.hasOwnProperty,Et=Object.prototype.propertyIsEnumerable,xt=(e,t)=>{var n={};for(var o in e)St.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&Ee)for(var o of Ee(e))t.indexOf(o)<0&&Et.call(e,o)&&(n[o]=e[o]);return n};function On(e,t,n={}){const o=n,{window:s=B}=o,r=xt(o,["window"]);let u;const i=Le(()=>s&&"MutationObserver"in s),a=()=>{u&&(u.disconnect(),u=void 0)},f=R(()=>L(e),l=>{a(),i.value&&s&&l&&(u=new MutationObserver(t),u.observe(l,r))},{immediate:!0}),v=()=>{a(),f()};return ne(v),{isSupported:i,stop:v}}var xe;(function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"})(xe||(xe={}));var Pt=Object.defineProperty,Pe=Object.getOwnPropertySymbols,Tt=Object.prototype.hasOwnProperty,Ct=Object.prototype.propertyIsEnumerable,Te=(e,t,n)=>t in e?Pt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Rt=(e,t)=>{for(var n in t||(t={}))Tt.call(t,n)&&Te(e,n,t[n]);if(Pe)for(var n of Pe(t))Ct.call(t,n)&&Te(e,n,t[n]);return e};const $t={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};Rt({linear:mt},$t);function _n(e,t,n,o={}){var s,r,u;const{clone:i=!1,passive:a=!1,eventName:f,deep:v=!1,defaultValue:l}=o,c=U(),d=n||(c==null?void 0:c.emit)||((s=c==null?void 0:c.$emit)==null?void 0:s.bind(c))||((u=(r=c==null?void 0:c.proxy)==null?void 0:r.$emit)==null?void 0:u.bind(c==null?void 0:c.proxy));let p=f;t||(t="modelValue"),p=f||p||`update:${t.toString()}`;const g=y=>i?ie(i)?i(y):wt(y):y,h=()=>ct(e[t])?g(e[t]):l;if(a){const y=h(),O=b(y);return R(()=>e[t],E=>O.value=g(E)),R(O,E=>{(E!==e[t]||v)&&d(p,E)},{deep:v}),O}else return x({get(){return h()},set(y){d(p,y)}})}function Sn({window:e=B}={}){if(!e)return b(!1);const t=b(e.document.hasFocus());return F(e,"blur",()=>{t.value=!1}),F(e,"focus",()=>{t.value=!0}),t}function En(e={}){const{window:t=B,initialWidth:n=1/0,initialHeight:o=1/0,listenOrientation:s=!0,includeScrollbar:r=!0}=e,u=b(n),i=b(o),a=()=>{t&&(r?(u.value=t.innerWidth,i.value=t.innerHeight):(u.value=t.document.documentElement.clientWidth,i.value=t.document.documentElement.clientHeight))};return a(),ce(a),F("resize",a,{passive:!0}),s&&F("orientationchange",a,{passive:!0}),{width:u,height:i}}function oe(e){return $e()?(Me(e),!0):!1}const se=new WeakMap,Mt=(...e)=>{var t;const n=e[0],o=(t=U())==null?void 0:t.proxy;if(o==null&&!Ie())throw new Error("injectLocal must be called in setup");return o&&se.has(o)&&n in se.get(o)?se.get(o)[n]:nt(...e)},fe=typeof window!="undefined"&&typeof document!="undefined";typeof WorkerGlobalScope!="undefined"&&globalThis instanceof WorkerGlobalScope;const Wt=e=>e!=null,At=Object.prototype.toString,It=e=>At.call(e)==="[object Object]",Dt=()=>{};function de(...e){if(e.length!==1)return Ze(...e);const t=e[0];return typeof t=="function"?ee(le(()=>({get:t,set:Dt}))):b(t)}function Lt(e,t){function n(...o){return new Promise((s,r)=>{Promise.resolve(e(()=>t.apply(this,o),{fn:t,thisArg:this,args:o})).then(s).catch(r)})}return n}const Fe=e=>e();function zt(e=Fe,t={}){const{initialState:n="active"}=t,o=de(n==="active");function s(){o.value=!1}function r(){o.value=!0}const u=(...i)=>{o.value&&e(...i)};return{isActive:ee(o),pause:s,resume:r,eventFilter:u}}function Ce(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function J(e){return Array.isArray(e)?e:[e]}function Ft(e){return U()}function kt(e,t,n={}){const r=n,{eventFilter:o=Fe}=r,s=G(r,["eventFilter"]);return R(e,Lt(o,t),s)}function Nt(e,t,n={}){const l=n,{eventFilter:o,initialState:s="active"}=l,r=G(l,["eventFilter","initialState"]),{eventFilter:u,pause:i,resume:a,isActive:f}=zt(o,{initialState:s});return{stop:kt(e,t,Y(V({},r),{eventFilter:u})),pause:i,resume:a,isActive:f}}function Vt(e,t={}){if(!et(e))return tt(e);const n=Array.isArray(e.value)?Array.from({length:e.value.length}):{};for(const o in e.value)n[o]=le(()=>({get(){return e.value[o]},set(s){var r;if((r=S(t.replaceRef))!=null?r:!0)if(Array.isArray(e.value)){const i=[...e.value];i[o]=s,e.value=i}else{const i=Y(V({},e.value),{[o]:s});Object.setPrototypeOf(i,Object.getPrototypeOf(e.value)),e.value=i}else e.value[o]=s}}));return n}function re(e,t=!0,n){Ft()?ue(e,n):t?e():ae(e)}function jt(e,t,n){return R(e,t,Y(V({},n),{immediate:!0}))}const N=fe?window:void 0,Bt=fe?window.document:void 0;function j(e){var t;const n=S(e);return(t=n==null?void 0:n.$el)!=null?t:n}function z(...e){const t=[],n=()=>{t.forEach(i=>i()),t.length=0},o=(i,a,f,v)=>(i.addEventListener(a,f,v),()=>i.removeEventListener(a,f,v)),s=x(()=>{const i=J(S(e[0])).filter(a=>a!=null);return i.every(a=>typeof a!="string")?i:void 0}),r=jt(()=>{var i,a;return[(a=(i=s.value)==null?void 0:i.map(f=>j(f)))!=null?a:[N].filter(f=>f!=null),J(S(s.value?e[1]:e[0])),J(Ae(s.value?e[2]:e[1])),S(s.value?e[3]:e[2])]},([i,a,f,v])=>{if(n(),!(i!=null&&i.length)||!(a!=null&&a.length)||!(f!=null&&f.length))return;const l=It(v)?V({},v):v;t.push(...i.flatMap(c=>a.flatMap(d=>f.map(p=>o(c,d,p,l)))))},{flush:"post"}),u=()=>{r(),n()};return oe(n),u}function Ht(){const e=C(!1),t=U();return t&&ue(()=>{e.value=!0},t),e}function ve(e){const t=Ht();return x(()=>(t.value,!!e()))}function ke(e,t,n={}){const c=n,{window:o=N}=c,s=G(c,["window"]);let r;const u=ve(()=>o&&"MutationObserver"in o),i=()=>{r&&(r.disconnect(),r=void 0)},a=x(()=>{const d=S(e),p=J(d).map(j).filter(Wt);return new Set(p)}),f=R(()=>a.value,d=>{i(),u.value&&d.size&&(r=new MutationObserver(t),d.forEach(p=>r.observe(p,s)))},{immediate:!0,flush:"post"}),v=()=>r==null?void 0:r.takeRecords(),l=()=>{f(),i()};return oe(l),{isSupported:u,stop:l,takeRecords:v}}const Xt=Symbol("vueuse-ssr-width");function Yt(){const e=Ie()?Mt(Xt,null):null;return typeof e=="number"?e:void 0}function Qt(e,t={}){const{window:n=N,ssrWidth:o=Yt()}=t,s=ve(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),r=C(typeof o=="number"),u=C(),i=C(!1),a=f=>{i.value=f.matches};return We(()=>{if(r.value){r.value=!s.value;const f=S(e).split(",");i.value=f.some(v=>{const l=v.includes("not all"),c=v.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),d=v.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let p=!!(c||d);return c&&p&&(p=o>=Ce(c[1])),d&&p&&(p=o<=Ce(d[1])),l?!p:p});return}s.value&&(u.value=n.matchMedia(S(e)),i.value=u.value.matches)}),z(u,"change",a,{passive:!0}),x(()=>i.value)}const q=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},Z="__vueuse_ssr_handlers__",Gt=Jt();function Jt(){return Z in q||(q[Z]=q[Z]||{}),q[Z]}function Ne(e,t){return Gt[e]||t}function Ut(e){return Qt("(prefers-color-scheme: dark)",e)}function Kt(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const qt={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Re="vueuse-storage";function Zt(e,t,n,o={}){var s;const{flush:r="pre",deep:u=!0,listenToStorageChanges:i=!0,writeDefaults:a=!0,mergeDefaults:f=!1,shallow:v,window:l=N,eventFilter:c,onError:d=w=>{console.error(w)},initOnMounted:p}=o,g=(v?C:b)(typeof t=="function"?t():t),h=x(()=>S(e));if(!n)try{n=Ne("getDefaultStorage",()=>{var w;return(w=N)==null?void 0:w.localStorage})()}catch(w){d(w)}if(!n)return g;const y=S(t),O=Kt(y),E=(s=o.serializer)!=null?s:qt[O],{pause:M,resume:$}=Nt(g,()=>W(g.value),{flush:r,deep:u,eventFilter:c});R(h,()=>D(),{flush:r});let I=!1;const m=w=>{p&&!I||D(w)},_=w=>{p&&!I||X(w)};l&&i&&(n instanceof Storage?z(l,"storage",m,{passive:!0}):z(l,Re,_)),p?re(()=>{I=!0,D()}):D();function P(w,A){if(l){const k={key:h.value,oldValue:w,newValue:A,storageArea:n};l.dispatchEvent(n instanceof Storage?new StorageEvent("storage",k):new CustomEvent(Re,{detail:k}))}}function W(w){try{const A=n.getItem(h.value);if(w==null)P(A,null),n.removeItem(h.value);else{const k=E.write(w);A!==k&&(n.setItem(h.value,k),P(A,k))}}catch(A){d(A)}}function T(w){const A=w?w.newValue:n.getItem(h.value);if(A==null)return a&&y!=null&&n.setItem(h.value,E.write(y)),y;if(!w&&f){const k=E.read(A);return typeof f=="function"?f(k,y):O==="object"&&!Array.isArray(k)?V(V({},y),k):k}else return typeof A!="string"?A:E.read(A)}function D(w){if(!(w&&w.storageArea!==n)){if(w&&w.key==null){g.value=y;return}if(!(w&&w.key!==h.value)){M();try{(w==null?void 0:w.newValue)!==E.write(g.value)&&(g.value=T(w))}catch(A){d(A)}finally{w?ae($):$()}}}}function X(w){D(w.detail)}return g}const en="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function tn(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:s=N,storage:r,storageKey:u="vueuse-color-scheme",listenToStorageChanges:i=!0,storageRef:a,emitAuto:f,disableTransition:v=!0}=e,l=V({auto:"",light:"light",dark:"dark"},e.modes||{}),c=Ut({window:s}),d=x(()=>c.value?"dark":"light"),p=a||(u==null?de(o):Zt(u,o,r,{window:s,listenToStorageChanges:i})),g=x(()=>p.value==="auto"?d.value:p.value),h=Ne("updateHTMLAttrs",(M,$,I)=>{const m=typeof M=="string"?s==null?void 0:s.document.querySelector(M):j(M);if(!m)return;const _=new Set,P=new Set;let W=null;if($==="class"){const D=I.split(/\s/g);Object.values(l).flatMap(X=>(X||"").split(/\s/g)).filter(Boolean).forEach(X=>{D.includes(X)?_.add(X):P.add(X)})}else W={key:$,value:I};if(_.size===0&&P.size===0&&W===null)return;let T;v&&(T=s.document.createElement("style"),T.appendChild(document.createTextNode(en)),s.document.head.appendChild(T));for(const D of _)m.classList.add(D);for(const D of P)m.classList.remove(D);W&&m.setAttribute(W.key,W.value),v&&(s.getComputedStyle(T).opacity,document.head.removeChild(T))});function y(M){var $;h(t,n,($=l[M])!=null?$:M)}function O(M){e.onChanged?e.onChanged(M,y):y(M)}R(g,O,{flush:"post",immediate:!0}),re(()=>O(g.value));const E=x({get(){return f?p.value:g.value},set(M){p.value=M}});return Object.assign(E,{store:p,system:d,state:g})}function xn(e={}){const{valueDark:t="dark",valueLight:n=""}=e,o=tn(Y(V({},e),{onChanged:(u,i)=>{var a;e.onChanged?(a=e.onChanged)==null||a.call(e,u==="dark",i,u):i(u)},modes:{dark:t,light:n}})),s=x(()=>o.system.value);return x({get(){return o.value==="dark"},set(u){const i=u?"dark":"light";s.value===i?o.value="auto":o.value=i}})}function Pn(e,t={}){var n;const{pointerTypes:o,preventDefault:s,stopPropagation:r,exact:u,onMove:i,onEnd:a,onStart:f,initialValue:v,axis:l="both",draggingElement:c=N,containerElement:d,handle:p=e,buttons:g=[0]}=t,h=b((n=S(v))!=null?n:{x:0,y:0}),y=b(),O=m=>o?o.includes(m.pointerType):!0,E=m=>{S(s)&&m.preventDefault(),S(r)&&m.stopPropagation()},M=m=>{var _;if(!S(g).includes(m.button)||S(t.disabled)||!O(m)||S(u)&&m.target!==S(e))return;const P=S(d),W=(_=P==null?void 0:P.getBoundingClientRect)==null?void 0:_.call(P),T=S(e).getBoundingClientRect(),D={x:m.clientX-(P?T.left-W.left+P.scrollLeft:T.left),y:m.clientY-(P?T.top-W.top+P.scrollTop:T.top)};(f==null?void 0:f(D,m))!==!1&&(y.value=D,E(m))},$=m=>{if(S(t.disabled)||!O(m)||!y.value)return;const _=S(d),P=S(e).getBoundingClientRect();let{x:W,y:T}=h.value;(l==="x"||l==="both")&&(W=m.clientX-y.value.x,_&&(W=Math.min(Math.max(0,W),_.scrollWidth-P.width))),(l==="y"||l==="both")&&(T=m.clientY-y.value.y,_&&(T=Math.min(Math.max(0,T),_.scrollHeight-P.height))),h.value={x:W,y:T},i==null||i(h.value,m),E(m)},I=m=>{S(t.disabled)||!O(m)||y.value&&(y.value=void 0,a==null||a(h.value,m),E(m))};if(fe){const m=()=>{var _;return{capture:(_=t.capture)!=null?_:!0,passive:!S(s)}};z(p,"pointerdown",M,m),z(c,"pointermove",$,m),z(c,"pointerup",I,m)}return Y(V({},Vt(h)),{position:h,isDragging:x(()=>!!y.value),style:x(()=>`left:${h.value.x}px;top:${h.value.y}px;`)})}function Ve(e,t,n={}){const l=n,{window:o=N}=l,s=G(l,["window"]);let r;const u=ve(()=>o&&"ResizeObserver"in o),i=()=>{r&&(r.disconnect(),r=void 0)},a=x(()=>{const c=S(e);return Array.isArray(c)?c.map(d=>j(d)):[j(c)]}),f=R(a,c=>{if(i(),u.value&&o){r=new ResizeObserver(t);for(const d of c)d&&r.observe(d,s)}},{immediate:!0,flush:"post"}),v=()=>{i(),f()};return oe(v),{isSupported:u,stop:v}}function nn(e,t={width:0,height:0},n={}){const{window:o=N,box:s="content-box"}=n,r=x(()=>{var l,c;return(c=(l=j(e))==null?void 0:l.namespaceURI)==null?void 0:c.includes("svg")}),u=C(t.width),i=C(t.height),{stop:a}=Ve(e,([l])=>{const c=s==="border-box"?l.borderBoxSize:s==="content-box"?l.contentBoxSize:l.devicePixelContentBoxSize;if(o&&r.value){const d=j(e);if(d){const p=d.getBoundingClientRect();u.value=p.width,i.value=p.height}}else if(c){const d=J(c);u.value=d.reduce((p,{inlineSize:g})=>p+g,0),i.value=d.reduce((p,{blockSize:g})=>p+g,0)}else u.value=l.contentRect.width,i.value=l.contentRect.height},n);re(()=>{const l=j(e);l&&(u.value="offsetWidth"in l?l.offsetWidth:t.width,i.value="offsetHeight"in l?l.offsetHeight:t.height)});const f=R(()=>j(e),l=>{u.value=l?t.width:0,i.value=l?t.height:0});function v(){a(),f()}return{width:u,height:i,stop:v}}const on={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof MouseEvent?[e.movementX,e.movementY]:null};function rn(e={}){const{type:t="page",touch:n=!0,resetOnTouchEnds:o=!1,initialValue:s={x:0,y:0},window:r=N,target:u=r,scroll:i=!0,eventFilter:a}=e;let f=null,v=0,l=0;const c=C(s.x),d=C(s.y),p=C(null),g=typeof t=="function"?t:on[t],h=m=>{const _=g(m);f=m,_&&([c.value,d.value]=_,p.value="mouse"),r&&(v=r.scrollX,l=r.scrollY)},y=m=>{if(m.touches.length>0){const _=g(m.touches[0]);_&&([c.value,d.value]=_,p.value="touch")}},O=()=>{if(!f||!r)return;const m=g(f);f instanceof MouseEvent&&m&&(c.value=m[0]+r.scrollX-v,d.value=m[1]+r.scrollY-l)},E=()=>{c.value=s.x,d.value=s.y},M=a?m=>a(()=>h(m),{}):m=>h(m),$=a?m=>a(()=>y(m),{}):m=>y(m),I=a?()=>a(()=>O(),{}):()=>O();if(u){const m={passive:!0};z(u,["mousemove","dragover"],M,m),n&&t!=="movement"&&(z(u,["touchstart","touchmove"],$,m),o&&z(u,"touchend",E,m)),i&&t==="page"&&z(r,"scroll",I,m)}return{x:c,y:d,sourceType:p}}function Tn(e,t={}){const{windowResize:n=!0,windowScroll:o=!0,handleOutside:s=!0,window:r=N}=t,u=t.type||"page",{x:i,y:a,sourceType:f}=rn(t),v=C(e!=null?e:r==null?void 0:r.document.body),l=C(0),c=C(0),d=C(0),p=C(0),g=C(0),h=C(0),y=C(!0);function O(){if(!r)return;const $=j(v);if(!$||!($ instanceof Element))return;const{left:I,top:m,width:_,height:P}=$.getBoundingClientRect();d.value=I+(u==="page"?r.pageXOffset:0),p.value=m+(u==="page"?r.pageYOffset:0),g.value=P,h.value=_;const W=i.value-d.value,T=a.value-p.value;y.value=_===0||P===0||W<0||T<0||W>_||T>P,s&&(l.value=W,c.value=T)}const E=[];function M(){E.forEach($=>$()),E.length=0}if(re(()=>{O()}),r){const{stop:$}=Ve(v,O),{stop:I}=ke(v,O,{attributeFilter:["style","class"]}),m=R([v,i,a],O);E.push($,I,m),z(document,"mouseleave",()=>y.value=!0,{passive:!0}),o&&E.push(z("scroll",O,{capture:!0,passive:!0})),n&&E.push(z("resize",O,{passive:!0}))}return{x:i,y:a,sourceType:f,elementX:l,elementY:c,elementPositionX:d,elementPositionY:p,elementHeight:g,elementWidth:h,isOutside:y,stop:M}}function Cn(e=null,t={}){var n,o,s;const{document:r=Bt,restoreOnUnmount:u=l=>l}=t,i=(n=r==null?void 0:r.title)!=null?n:"",a=de((o=e!=null?e:r==null?void 0:r.title)!=null?o:null),f=!!(e&&typeof e=="function");function v(l){if(!("titleTemplate"in t))return l;const c=t.titleTemplate||"%s";return typeof c=="function"?c(l):S(c).replace(/%s/g,l)}return R(a,(l,c)=>{l!==c&&r&&(r.title=v(l!=null?l:""))},{immediate:!0}),t.observe&&!t.titleTemplate&&r&&!f&&ke((s=r.head)==null?void 0:s.querySelector("title"),()=>{r&&r.title!==a.value&&(a.value=v(r.title))},{childList:!0}),oe(()=>{if(u){const l=u(i,a.value||"");l!=null&&r&&(r.title=l)}}),a}function Rn(e,t){const{containerStyle:n,wrapperProps:o,scrollTo:s,calculateRange:r,currentList:u,containerRef:i}="itemHeight"in t?an(t,e):un(t,e);return{list:u,scrollTo:s,containerProps:{ref:i,onScroll:()=>{r()},style:n},wrapperProps:o}}function je(e){const t=C(null),n=nn(t),o=b([]),s=C(e);return{state:b({start:0,end:10}),source:s,currentList:o,size:n,containerRef:t}}function Be(e,t,n){return o=>{if(typeof n=="number")return Math.ceil(o/n);const{start:s=0}=e.value;let r=0,u=0;for(let i=s;i<t.value.length;i++){const a=n(i);if(r+=a,u=i,r>o)break}return u-s}}function He(e,t){return n=>{if(typeof t=="number")return Math.floor(n/t)+1;let o=0,s=0;for(let r=0;r<e.value.length;r++){const u=t(r);if(o+=u,o>=n){s=r;break}}return s+1}}function Xe(e,t,n,o,{containerRef:s,state:r,currentList:u,source:i}){return()=>{const a=s.value;if(a){const f=n(e==="vertical"?a.scrollTop:a.scrollLeft),v=o(e==="vertical"?a.clientHeight:a.clientWidth),l=f-t,c=f+v+t;r.value={start:l<0?0:l,end:c>i.value.length?i.value.length:c},u.value=i.value.slice(r.value.start,r.value.end).map((d,p)=>({data:d,index:p+r.value.start}))}}}function Ye(e,t){return n=>typeof e=="number"?n*e:t.value.slice(0,n).reduce((s,r,u)=>s+e(u),0)}function Qe(e,t,n,o){R([e.width,e.height,t,n],()=>{o()})}function Ge(e,t){return x(()=>typeof e=="number"?t.value.length*e:t.value.reduce((n,o,s)=>n+e(s),0))}const sn={horizontal:"scrollLeft",vertical:"scrollTop"};function Je(e,t,n,o){return s=>{o.value&&(o.value[sn[e]]=n(s),t())}}function un(e,t){const n=je(t),{state:o,source:s,currentList:r,size:u,containerRef:i}=n,a={overflowX:"auto"},{itemWidth:f,overscan:v=5}=e,l=Be(o,s,f),c=He(s,f),d=Xe("horizontal",v,c,l,n),p=Ye(f,s),g=x(()=>p(o.value.start)),h=Ge(f,s);Qe(u,t,i,d);const y=Je("horizontal",d,p,i),O=x(()=>({style:{height:"100%",width:`${h.value-g.value}px`,marginLeft:`${g.value}px`,display:"flex"}}));return{scrollTo:y,calculateRange:d,wrapperProps:O,containerStyle:a,currentList:r,containerRef:i}}function an(e,t){const n=je(t),{state:o,source:s,currentList:r,size:u,containerRef:i}=n,a={overflowY:"auto"},{itemHeight:f,overscan:v=5}=e,l=Be(o,s,f),c=He(s,f),d=Xe("vertical",v,c,l,n),p=Ye(f,s),g=x(()=>p(o.value.start)),h=Ge(f,s);Qe(u,t,i,d);const y=Je("vertical",d,p,i),O=x(()=>({style:{width:"100%",height:`${h.value-g.value}px`,marginTop:`${g.value}px`}}));return{calculateRange:d,scrollTo:y,containerStyle:a,wrapperProps:O,currentList:r,containerRef:i}}export{xn as A,wn as a,F as b,fn as c,ze as d,L as e,vn as f,gn as g,pn as h,te as i,dt as j,On as k,yn as l,Sn as m,_n as n,mn as o,hn as p,bn as q,dn as r,Cn as s,ne as t,En as u,Pn as v,Tn as w,z as x,Rn as y,nn as z};
