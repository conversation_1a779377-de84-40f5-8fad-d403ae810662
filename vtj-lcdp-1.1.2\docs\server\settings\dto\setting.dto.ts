import { IsOptional, IsN<PERSON>ber, IsEnum } from 'class-validator';
import { SettingMode } from '../types';
export class SettingDto {
  @IsEnum(SettingMode)
  mode: SettingMode;

  @IsOptional()
  max?: number;

  @IsNumber()
  @IsOptional()
  limit?: number;

  @IsOptional()
  price?: number;

  @IsOptional()
  promptTemplate?: string;

  @IsOptional()
  payQr?: string;

  @IsOptional()
  contactQr?: string;

  @IsOptional()
  mailPass?: string;
}
