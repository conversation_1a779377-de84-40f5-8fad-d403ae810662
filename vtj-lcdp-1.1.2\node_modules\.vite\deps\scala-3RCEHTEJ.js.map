{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/scala/scala.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/scala/scala.ts\nvar conf = {\n  /*\n   * `...` is allowed as an identifier.\n   * $ is allowed in identifiers.\n   * unary_<op> is allowed as an identifier.\n   * <name>_= is allowed as an identifier.\n   */\n  wordPattern: /(unary_[@~!#%^&*()\\-=+\\\\|:<>\\/?]+)|([a-zA-Z_$][\\w$]*?_=)|(`[^`]+`)|([a-zA-Z_$][\\w$]*)/g,\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?region\\\\b)|(?:<editor-fold\\\\b))\"),\n      end: new RegExp(\"^\\\\s*//\\\\s*(?:(?:#?endregion\\\\b)|(?:</editor-fold>))\")\n    }\n  }\n};\nvar language = {\n  tokenPostfix: \".scala\",\n  // We can't easily add everything from Dotty, but we can at least add some of its keywords\n  keywords: [\n    \"asInstanceOf\",\n    \"catch\",\n    \"class\",\n    \"classOf\",\n    \"def\",\n    \"do\",\n    \"else\",\n    \"extends\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"forSome\",\n    \"if\",\n    \"import\",\n    \"isInstanceOf\",\n    \"macro\",\n    \"match\",\n    \"new\",\n    \"object\",\n    \"package\",\n    \"return\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"type\",\n    \"until\",\n    \"val\",\n    \"var\",\n    \"while\",\n    \"with\",\n    \"yield\",\n    // Dotty-specific:\n    \"given\",\n    \"enum\",\n    \"then\"\n  ],\n  // Dotty-specific:\n  softKeywords: [\"as\", \"export\", \"extension\", \"end\", \"derives\", \"on\"],\n  constants: [\"true\", \"false\", \"null\", \"this\", \"super\"],\n  modifiers: [\n    \"abstract\",\n    \"final\",\n    \"implicit\",\n    \"lazy\",\n    \"override\",\n    \"private\",\n    \"protected\",\n    \"sealed\"\n  ],\n  // Dotty-specific:\n  softModifiers: [\"inline\", \"opaque\", \"open\", \"transparent\", \"using\"],\n  name: /(?:[a-z_$][\\w$]*|`[^`]+`)/,\n  type: /(?:[A-Z][\\w$]*)/,\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/^\\\\%@#]+/,\n  digits: /\\d+(_+\\d+)*/,\n  hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,\n  // C# style strings\n  escapes: /\\\\(?:[btnfr\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  fstring_conv: /[bBhHsScCdoxXeEfgGaAt]|[Tn](?:[HIklMSLNpzZsQ]|[BbhAaCYyjmde]|[RTrDFC])/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // strings\n      [/\\braw\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstringt\" }],\n      [/\\braw\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@rawstring\" }],\n      [/\\bs\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstringt\" }],\n      [/\\bs\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@sstring\" }],\n      [/\\bf\"\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstringt\" }],\n      [/\\bf\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@fstring\" }],\n      [/\"\"\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@stringt\" }],\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }],\n      // numbers\n      [/(@digits)[eE]([\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)\\.(@digits)([eE][\\-+]?(@digits))?[fFdD]?/, \"number.float\", \"@allowMethod\"],\n      [/0[xX](@hexdigits)[Ll]?/, \"number.hex\", \"@allowMethod\"],\n      [/(@digits)[fFdD]/, \"number.float\", \"@allowMethod\"],\n      [/(@digits)[lL]?/, \"number\", \"@allowMethod\"],\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_)\\b/, \"keyword\", \"@allowMethod\"],\n      // identifiers and keywords\n      [/\\bimport\\b/, \"keyword\", \"@import\"],\n      [/\\b(case)([ \\t]+)(class)\\b/, [\"keyword.modifier\", \"white\", \"keyword\"]],\n      [/\\bcase\\b/, \"keyword\", \"@case\"],\n      [/\\bva[lr]\\b/, \"keyword\", \"@vardef\"],\n      [\n        /\\b(def)([ \\t]+)((?:unary_)?@symbols|@name(?:_=)|@name)/,\n        [\"keyword\", \"white\", \"identifier\"]\n      ],\n      [/@name(?=[ \\t]*:(?!:))/, \"variable\"],\n      [/(\\.)(@name|@symbols)/, [\"operator\", { token: \"@rematch\", next: \"@allowMethod\" }]],\n      [/([{(])(\\s*)(@name(?=\\s*=>))/, [\"@brackets\", \"white\", \"variable\"]],\n      [\n        /@name/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@softKeywords\": \"keyword\",\n            \"@modifiers\": \"keyword.modifier\",\n            \"@softModifiers\": \"keyword.modifier\",\n            \"@constants\": {\n              token: \"constant\",\n              next: \"@allowMethod\"\n            },\n            \"@default\": {\n              token: \"identifier\",\n              next: \"@allowMethod\"\n            }\n          }\n        }\n      ],\n      [/@type/, \"type\", \"@allowMethod\"],\n      // whitespace\n      { include: \"@whitespace\" },\n      // @ annotations.\n      [/@[a-zA-Z_$][\\w$]*(?:\\.[a-zA-Z_$][\\w$]*)*/, \"annotation\"],\n      // delimiters and operators\n      [/[{(]/, \"@brackets\"],\n      [/[})]/, \"@brackets\", \"@allowMethod\"],\n      [/\\[/, \"operator.square\"],\n      [/](?!\\s*(?:va[rl]|def|type)\\b)/, \"operator.square\", \"@allowMethod\"],\n      [/]/, \"operator.square\"],\n      [/([=-]>|<-|>:|<:|:>|<%)(?=[\\s\\w()[\\]{},\\.\"'`])/, \"keyword\"],\n      [/@symbols/, \"operator\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,\\.]/, \"delimiter\"],\n      // symbols\n      [/'[a-zA-Z$][\\w$]*(?!')/, \"attribute.name\"],\n      // characters\n      [/'[^\\\\']'/, \"string\", \"@allowMethod\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", { token: \"string\", next: \"@allowMethod\" }]],\n      [/'/, \"string.invalid\"]\n    ],\n    import: [\n      [/;/, \"delimiter\", \"@pop\"],\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/@name|@type/, \"type\"],\n      [/[(){}]/, \"@brackets\"],\n      [/[[\\]]/, \"operator.square\"],\n      [/[\\.,]/, \"delimiter\"]\n    ],\n    allowMethod: [\n      [/^|$/, \"\", \"@pop\"],\n      [/[ \\t]+/, \"white\"],\n      [/[\\n\\r]+/, \"white\", \"@pop\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/(?==>[\\s\\w([{])/, \"keyword\", \"@pop\"],\n      [\n        /(@name|@symbols)(?=[ \\t]*[[({\"'`]|[ \\t]+(?:[+-]?\\.?\\d|\\w))/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword\", next: \"@pop\" },\n            \"->|<-|>:|<:|<%\": { token: \"keyword\", next: \"@pop\" },\n            \"@default\": { token: \"@rematch\", next: \"@pop\" }\n          }\n        }\n      ],\n      [\"\", \"\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [/\\*\\//, \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    case: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\", \"@allowMethod\"],\n      [/\\bif\\b|=>/, \"keyword\", \"@pop\"],\n      [/`[^`]+`/, \"identifier\", \"@allowMethod\"],\n      [/@name/, \"variable\", \"@allowMethod\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      { include: \"@root\" }\n    ],\n    vardef: [\n      [/\\b_\\*/, \"key\"],\n      [/\\b(_|true|false|null|this|super)\\b/, \"keyword\"],\n      [/@name/, \"variable\"],\n      [/:::?|\\||@(?![a-z_$])/, \"keyword\"],\n      [/=|:(?!:)/, \"operator\", \"@pop\"],\n      [/$/, \"white\", \"@pop\"],\n      { include: \"@root\" }\n    ],\n    string: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    stringt: [\n      [/[^\\\\\"\\n\\r]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    fstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    fstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/%%/, \"string\"],\n      [\n        /(%)([\\-#+ 0,(])(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/,\n        [\"metatag\", \"keyword.modifier\", \"number\", \"metatag\"]\n      ],\n      [/(%)(\\d+|\\.\\d+|\\d+\\.\\d+)(@fstring_conv)/, [\"metatag\", \"number\", \"metatag\"]],\n      [/(%)([\\-#+ 0,(])(@fstring_conv)/, [\"metatag\", \"keyword.modifier\", \"metatag\"]],\n      [/(%)(@fstring_conv)/, [\"metatag\", \"metatag\"]],\n      [/./, \"string\"]\n    ],\n    sstring: [\n      [/@escapes/, \"string.escape\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    sstringt: [\n      [/@escapes/, \"string.escape\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\\$\\$/, \"string\"],\n      [/(\\$)([a-z_]\\w*)/, [\"operator\", \"identifier\"]],\n      [/\\$\\{/, \"operator\", \"@interp\"],\n      [/./, \"string\"]\n    ],\n    interp: [[/{/, \"operator\", \"@push\"], [/}/, \"operator\", \"@pop\"], { include: \"@root\" }],\n    rawstring: [\n      [/[^\"]/, \"string\"],\n      [\n        /\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ]\n    ],\n    rawstringt: [\n      [/[^\"]/, \"string\"],\n      [/\"(?=\"\"\")/, \"string\"],\n      [\n        /\"\"\"/,\n        {\n          token: \"string.quote\",\n          bracket: \"@close\",\n          switchTo: \"@allowMethod\"\n        }\n      ],\n      [/\"/, \"string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\/\\/.*$/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOT,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,oDAAoD;AAAA,MACtE,KAAK,IAAI,OAAO,sDAAsD;AAAA,IACxE;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA;AAAA,EAEd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,CAAC,MAAM,UAAU,aAAa,OAAO,WAAW,IAAI;AAAA,EAClE,WAAW,CAAC,QAAQ,SAAS,QAAQ,QAAQ,OAAO;AAAA,EACpD,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,CAAC,UAAU,UAAU,QAAQ,eAAe,OAAO;AAAA,EAClE,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA;AAAA,EAEX,SAAS;AAAA,EACT,cAAc;AAAA;AAAA,EAEd,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,CAAC,YAAY,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,cAAc,CAAC;AAAA,MAC7E,CAAC,UAAU,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,MAC1E,CAAC,UAAU,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,YAAY,CAAC;AAAA,MACzE,CAAC,QAAQ,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,MACtE,CAAC,WAAW,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,YAAY,CAAC;AAAA,MAC1E,CAAC,QAAQ,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,MACtE,CAAC,OAAO,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,WAAW,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA;AAAA,MAElE,CAAC,0CAA0C,gBAAgB,cAAc;AAAA,MACzE,CAAC,qDAAqD,gBAAgB,cAAc;AAAA,MACpF,CAAC,0BAA0B,cAAc,cAAc;AAAA,MACvD,CAAC,mBAAmB,gBAAgB,cAAc;AAAA,MAClD,CAAC,kBAAkB,UAAU,cAAc;AAAA,MAC3C,CAAC,SAAS,KAAK;AAAA,MACf,CAAC,WAAW,WAAW,cAAc;AAAA;AAAA,MAErC,CAAC,cAAc,WAAW,SAAS;AAAA,MACnC,CAAC,6BAA6B,CAAC,oBAAoB,SAAS,SAAS,CAAC;AAAA,MACtE,CAAC,YAAY,WAAW,OAAO;AAAA,MAC/B,CAAC,cAAc,WAAW,SAAS;AAAA,MACnC;AAAA,QACE;AAAA,QACA,CAAC,WAAW,SAAS,YAAY;AAAA,MACnC;AAAA,MACA,CAAC,yBAAyB,UAAU;AAAA,MACpC,CAAC,wBAAwB,CAAC,YAAY,EAAE,OAAO,YAAY,MAAM,eAAe,CAAC,CAAC;AAAA,MAClF,CAAC,+BAA+B,CAAC,aAAa,SAAS,UAAU,CAAC;AAAA,MAClE;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,iBAAiB;AAAA,YACjB,cAAc;AAAA,YACd,kBAAkB;AAAA,YAClB,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY;AAAA,cACV,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,SAAS,QAAQ,cAAc;AAAA;AAAA,MAEhC,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,4CAA4C,YAAY;AAAA;AAAA,MAEzD,CAAC,QAAQ,WAAW;AAAA,MACpB,CAAC,QAAQ,aAAa,cAAc;AAAA,MACpC,CAAC,MAAM,iBAAiB;AAAA,MACxB,CAAC,iCAAiC,mBAAmB,cAAc;AAAA,MACnE,CAAC,KAAK,iBAAiB;AAAA,MACvB,CAAC,iDAAiD,SAAS;AAAA,MAC3D,CAAC,YAAY,UAAU;AAAA;AAAA,MAEvB,CAAC,UAAU,WAAW;AAAA;AAAA,MAEtB,CAAC,yBAAyB,gBAAgB;AAAA;AAAA,MAE1C,CAAC,YAAY,UAAU,cAAc;AAAA,MACrC,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,EAAE,OAAO,UAAU,MAAM,eAAe,CAAC,CAAC;AAAA,MAC3F,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,KAAK,aAAa,MAAM;AAAA,MACzB,CAAC,OAAO,IAAI,MAAM;AAAA,MAClB,CAAC,UAAU,OAAO;AAAA,MAClB,CAAC,WAAW,SAAS,MAAM;AAAA,MAC3B,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,UAAU,WAAW;AAAA,MACtB,CAAC,SAAS,iBAAiB;AAAA,MAC3B,CAAC,SAAS,WAAW;AAAA,IACvB;AAAA,IACA,aAAa;AAAA,MACX,CAAC,OAAO,IAAI,MAAM;AAAA,MAClB,CAAC,UAAU,OAAO;AAAA,MAClB,CAAC,WAAW,SAAS,MAAM;AAAA,MAC3B,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,mBAAmB,WAAW,MAAM;AAAA,MACrC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,WAAW,MAAM,OAAO;AAAA,YAC9C,kBAAkB,EAAE,OAAO,WAAW,MAAM,OAAO;AAAA,YACnD,YAAY,EAAE,OAAO,YAAY,MAAM,OAAO;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,IAAI,IAAI,MAAM;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,OAAO;AAAA;AAAA,MAE3B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,MAAM;AAAA,MACJ,CAAC,SAAS,KAAK;AAAA,MACf,CAAC,sCAAsC,WAAW,cAAc;AAAA,MAChE,CAAC,aAAa,WAAW,MAAM;AAAA,MAC/B,CAAC,WAAW,cAAc,cAAc;AAAA,MACxC,CAAC,SAAS,YAAY,cAAc;AAAA,MACpC,CAAC,wBAAwB,SAAS;AAAA,MAClC,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,SAAS,KAAK;AAAA,MACf,CAAC,sCAAsC,SAAS;AAAA,MAChD,CAAC,SAAS,UAAU;AAAA,MACpB,CAAC,wBAAwB,SAAS;AAAA,MAClC,CAAC,YAAY,YAAY,MAAM;AAAA,MAC/B,CAAC,KAAK,SAAS,MAAM;AAAA,MACrB,EAAE,SAAS,QAAQ;AAAA,IACrB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,eAAe,QAAQ;AAAA,MACxB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,eAAe,QAAQ;AAAA,MACxB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,YAAY,QAAQ;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,YAAY,eAAe;AAAA,MAC5B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,QAAQ;AAAA,MACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,MAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,MAC9B,CAAC,MAAM,QAAQ;AAAA,MACf;AAAA,QACE;AAAA,QACA,CAAC,WAAW,oBAAoB,UAAU,SAAS;AAAA,MACrD;AAAA,MACA,CAAC,0CAA0C,CAAC,WAAW,UAAU,SAAS,CAAC;AAAA,MAC3E,CAAC,kCAAkC,CAAC,WAAW,oBAAoB,SAAS,CAAC;AAAA,MAC7E,CAAC,sBAAsB,CAAC,WAAW,SAAS,CAAC;AAAA,MAC7C,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,UAAU;AAAA,MACR,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,YAAY,QAAQ;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,QAAQ;AAAA,MACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,MAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,MAC9B,CAAC,MAAM,QAAQ;AAAA,MACf;AAAA,QACE;AAAA,QACA,CAAC,WAAW,oBAAoB,UAAU,SAAS;AAAA,MACrD;AAAA,MACA,CAAC,0CAA0C,CAAC,WAAW,UAAU,SAAS,CAAC;AAAA,MAC3E,CAAC,kCAAkC,CAAC,WAAW,oBAAoB,SAAS,CAAC;AAAA,MAC7E,CAAC,sBAAsB,CAAC,WAAW,SAAS,CAAC;AAAA,MAC7C,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,YAAY,eAAe;AAAA,MAC5B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,QAAQ;AAAA,MACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,MAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,MAC9B,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,UAAU;AAAA,MACR,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,YAAY,QAAQ;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,QAAQ,QAAQ;AAAA,MACjB,CAAC,mBAAmB,CAAC,YAAY,YAAY,CAAC;AAAA,MAC9C,CAAC,QAAQ,YAAY,SAAS;AAAA,MAC9B,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,QAAQ,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,CAAC,KAAK,YAAY,MAAM,GAAG,EAAE,SAAS,QAAQ,CAAC;AAAA,IACpF,WAAW;AAAA,MACT,CAAC,QAAQ,QAAQ;AAAA,MACjB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV,CAAC,QAAQ,QAAQ;AAAA,MACjB,CAAC,YAAY,QAAQ;AAAA,MACrB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,CAAC,KAAK,QAAQ;AAAA,IAChB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,EACF;AACF;", "names": []}