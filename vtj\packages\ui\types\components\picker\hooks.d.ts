import { Emits } from '../shared';
import { PickerProps, PickerEmits, PickerOption } from './types';
import { Ref, ComputedRef } from 'vue';
import { VxeTableDataRow } from 'vxe-table';
export declare function useOptions(props: PickerP<PERSON>, emit: Emits<PickerEmits>): {
    current: Ref<any, any>;
    options: Ref<{
        label: string;
        value: any;
    }[], PickerOption[] | {
        label: string;
        value: any;
    }[]>;
    setOptions: (rows: any, append?: boolean) => void;
};
export declare function useGridColumns(props: PickerProps): ComputedRef<import("vxe-table").VxeTableDefines.ColumnOptions<VxeTableDataRow>[]>;
export declare function useModel(props: PickerProps): {
    formModel: Ref<Record<string, any>, Record<string, any>>;
};
