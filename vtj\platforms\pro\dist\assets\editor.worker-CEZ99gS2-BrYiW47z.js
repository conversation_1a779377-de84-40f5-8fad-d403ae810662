const i='(function(){"use strict";class qs{constructor(){this.listeners=[],this.unexpectedErrorHandler=function(e){setTimeout(()=>{throw e.stack?ke.isErrorNoTelemetry(e)?new ke(e.message+`\n\n`+e.stack):new Error(e.message+`\n\n`+e.stack):e},0)}}emit(e){this.listeners.forEach(n=>{n(e)})}onUnexpectedError(e){this.unexpectedErrorHandler(e),this.emit(e)}onUnexpectedExternalError(e){this.unexpectedErrorHandler(e)}}const Us=new qs;function Ye(t){$s(t)||Us.onUnexpectedError(t)}function An(t){if(t instanceof Error){const{name:e,message:n}=t,r=t.stacktrace||t.stack;return{$isError:!0,name:e,message:n,stack:r,noTelemetry:ke.isErrorNoTelemetry(t)}}return t}const qt="Canceled";function $s(t){return t instanceof Hs?!0:t instanceof Error&&t.name===qt&&t.message===qt}class Hs extends Error{constructor(){super(qt),this.name=this.message}}class ke extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof ke)return e;const n=new ke;return n.message=e.message,n.stack=e.stack,n}static isErrorNoTelemetry(e){return e.name==="CodeExpectedError"}}class se extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,se.prototype)}}function Ws(t,e){const n=this;let r=!1,s;return function(){return r||(r=!0,s=t.apply(n,arguments)),s}}var ht;(function(t){function e(y){return y&&typeof y=="object"&&typeof y[Symbol.iterator]=="function"}t.is=e;const n=Object.freeze([]);function r(){return n}t.empty=r;function*s(y){yield y}t.single=s;function i(y){return e(y)?y:s(y)}t.wrap=i;function o(y){return y||n}t.from=o;function*l(y){for(let b=y.length-1;b>=0;b--)yield y[b]}t.reverse=l;function u(y){return!y||y[Symbol.iterator]().next().done===!0}t.isEmpty=u;function c(y){return y[Symbol.iterator]().next().value}t.first=c;function f(y,b){let w=0;for(const C of y)if(b(C,w++))return!0;return!1}t.some=f;function h(y,b){for(const w of y)if(b(w))return w}t.find=h;function*m(y,b){for(const w of y)b(w)&&(yield w)}t.filter=m;function*d(y,b){let w=0;for(const C of y)yield b(C,w++)}t.map=d;function*g(y,b){let w=0;for(const C of y)yield*b(C,w++)}t.flatMap=g;function*p(...y){for(const b of y)yield*b}t.concat=p;function x(y,b,w){let C=w;for(const R of y)C=b(C,R);return C}t.reduce=x;function*L(y,b,w=y.length){for(b<0&&(b+=y.length),w<0?w+=y.length:w>y.length&&(w=y.length);b<w;b++)yield y[b]}t.slice=L;function N(y,b=Number.POSITIVE_INFINITY){const w=[];if(b===0)return[w,y];const C=y[Symbol.iterator]();for(let R=0;R<b;R++){const B=C.next();if(B.done)return[w,t.empty()];w.push(B.value)}return[w,{[Symbol.iterator](){return C}}]}t.consume=N;async function v(y){const b=[];for await(const w of y)b.push(w);return Promise.resolve(b)}t.asyncToArray=v})(ht||(ht={}));function A1(t){return t}function R1(t,e){}function Rn(t){if(ht.is(t)){const e=[];for(const n of t)if(n)try{n.dispose()}catch(r){e.push(r)}if(e.length===1)throw e[0];if(e.length>1)throw new AggregateError(e,"Encountered errors while disposing of store");return Array.isArray(t)?[]:t}else if(t)return t.dispose(),t}function zs(...t){return ft(()=>Rn(t))}function ft(t){return{dispose:Ws(()=>{t()})}}const kt=class kt{constructor(){this._toDispose=new Set,this._isDisposed=!1}dispose(){this._isDisposed||(this._isDisposed=!0,this.clear())}get isDisposed(){return this._isDisposed}clear(){if(this._toDispose.size!==0)try{Rn(this._toDispose)}finally{this._toDispose.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return this._isDisposed?kt.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(e),e}deleteAndLeak(e){e&&this._toDispose.has(e)&&this._toDispose.delete(e)}};kt.DISABLE_DISPOSED_WARNING=!1;let Je=kt;const Cn=class Cn{constructor(){this._store=new Je,this._store}dispose(){this._store.dispose()}_register(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(e)}};Cn.None=Object.freeze({dispose(){}});let Pe=Cn;const ze=class ze{constructor(e){this.element=e,this.next=ze.Undefined,this.prev=ze.Undefined}};ze.Undefined=new ze(void 0);let O=ze;class Os{constructor(){this._first=O.Undefined,this._last=O.Undefined,this._size=0}get size(){return this._size}isEmpty(){return this._first===O.Undefined}clear(){let e=this._first;for(;e!==O.Undefined;){const n=e.next;e.prev=O.Undefined,e.next=O.Undefined,e=n}this._first=O.Undefined,this._last=O.Undefined,this._size=0}unshift(e){return this._insert(e,!1)}push(e){return this._insert(e,!0)}_insert(e,n){const r=new O(e);if(this._first===O.Undefined)this._first=r,this._last=r;else if(n){const i=this._last;this._last=r,r.prev=i,i.next=r}else{const i=this._first;this._first=r,r.next=i,i.prev=r}this._size+=1;let s=!1;return()=>{s||(s=!0,this._remove(r))}}shift(){if(this._first!==O.Undefined){const e=this._first.element;return this._remove(this._first),e}}pop(){if(this._last!==O.Undefined){const e=this._last.element;return this._remove(this._last),e}}_remove(e){if(e.prev!==O.Undefined&&e.next!==O.Undefined){const n=e.prev;n.next=e.next,e.next.prev=n}else e.prev===O.Undefined&&e.next===O.Undefined?(this._first=O.Undefined,this._last=O.Undefined):e.next===O.Undefined?(this._last=this._last.prev,this._last.next=O.Undefined):e.prev===O.Undefined&&(this._first=this._first.next,this._first.prev=O.Undefined);this._size-=1}*[Symbol.iterator](){let e=this._first;for(;e!==O.Undefined;)yield e.element,e=e.next}}const Gs=globalThis.performance&&typeof globalThis.performance.now=="function";class mt{static create(e){return new mt(e)}constructor(e){this._now=Gs&&e===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this._startTime=this._now(),this._stopTime=-1}stop(){this._stopTime=this._now()}reset(){this._startTime=this._now(),this._stopTime=-1}elapsed(){return this._stopTime!==-1?this._stopTime-this._startTime:this._now()-this._startTime}}var dt;(function(t){t.None=()=>Pe.None;function e(S,_){return m(S,()=>{},0,void 0,!0,void 0,_)}t.defer=e;function n(S){return(_,E=null,A)=>{let P=!1,I;return I=S(H=>{if(!P)return I?I.dispose():P=!0,_.call(E,H)},null,A),P&&I.dispose(),I}}t.once=n;function r(S,_){return t.once(t.filter(S,_))}t.onceIf=r;function s(S,_,E){return f((A,P=null,I)=>S(H=>A.call(P,_(H)),null,I),E)}t.map=s;function i(S,_,E){return f((A,P=null,I)=>S(H=>{_(H),A.call(P,H)},null,I),E)}t.forEach=i;function o(S,_,E){return f((A,P=null,I)=>S(H=>_(H)&&A.call(P,H),null,I),E)}t.filter=o;function l(S){return S}t.signal=l;function u(...S){return(_,E=null,A)=>{const P=zs(...S.map(I=>I(H=>_.call(E,H))));return h(P,A)}}t.any=u;function c(S,_,E,A){let P=E;return s(S,I=>(P=_(P,I),P),A)}t.reduce=c;function f(S,_){let E;const A={onWillAddFirstListener(){E=S(P.fire,P)},onDidRemoveLastListener(){E?.dispose()}},P=new oe(A);return _?.add(P),P.event}function h(S,_){return _ instanceof Array?_.push(S):_&&_.add(S),S}function m(S,_,E=100,A=!1,P=!1,I,H){let J,ee,Qe,Vt=0,ct;const N1={leakWarningThreshold:I,onWillAddFirstListener(){J=S(S1=>{Vt++,ee=_(ee,S1),A&&!Qe&&(Bt.fire(ee),ee=void 0),ct=()=>{const C1=ee;ee=void 0,Qe=void 0,(!A||Vt>1)&&Bt.fire(C1),Vt=0},typeof E=="number"?(clearTimeout(Qe),Qe=setTimeout(ct,E)):Qe===void 0&&(Qe=0,queueMicrotask(ct))})},onWillRemoveListener(){P&&Vt>0&&ct?.()},onDidRemoveLastListener(){ct=void 0,J.dispose()}},Bt=new oe(N1);return H?.add(Bt),Bt.event}t.debounce=m;function d(S,_=0,E){return t.debounce(S,(A,P)=>A?(A.push(P),A):[P],_,void 0,!0,void 0,E)}t.accumulate=d;function g(S,_=(A,P)=>A===P,E){let A=!0,P;return o(S,I=>{const H=A||!_(I,P);return A=!1,P=I,H},E)}t.latch=g;function p(S,_,E){return[t.filter(S,_,E),t.filter(S,A=>!_(A),E)]}t.split=p;function x(S,_=!1,E=[],A){let P=E.slice(),I=S(ee=>{P?P.push(ee):J.fire(ee)});A&&A.add(I);const H=()=>{P?.forEach(ee=>J.fire(ee)),P=null},J=new oe({onWillAddFirstListener(){I||(I=S(ee=>J.fire(ee)),A&&A.add(I))},onDidAddFirstListener(){P&&(_?setTimeout(H):H())},onDidRemoveLastListener(){I&&I.dispose(),I=null}});return A&&A.add(J),J.event}t.buffer=x;function L(S,_){return(A,P,I)=>{const H=_(new v);return S(function(J){const ee=H.evaluate(J);ee!==N&&A.call(P,ee)},void 0,I)}}t.chain=L;const N=Symbol("HaltChainable");class v{constructor(){this.steps=[]}map(_){return this.steps.push(_),this}forEach(_){return this.steps.push(E=>(_(E),E)),this}filter(_){return this.steps.push(E=>_(E)?E:N),this}reduce(_,E){let A=E;return this.steps.push(P=>(A=_(A,P),A)),this}latch(_=(E,A)=>E===A){let E=!0,A;return this.steps.push(P=>{const I=E||!_(P,A);return E=!1,A=P,I?P:N}),this}evaluate(_){for(const E of this.steps)if(_=E(_),_===N)break;return _}}function y(S,_,E=A=>A){const A=(...J)=>H.fire(E(...J)),P=()=>S.on(_,A),I=()=>S.removeListener(_,A),H=new oe({onWillAddFirstListener:P,onDidRemoveLastListener:I});return H.event}t.fromNodeEventEmitter=y;function b(S,_,E=A=>A){const A=(...J)=>H.fire(E(...J)),P=()=>S.addEventListener(_,A),I=()=>S.removeEventListener(_,A),H=new oe({onWillAddFirstListener:P,onDidRemoveLastListener:I});return H.event}t.fromDOMEventEmitter=b;function w(S){return new Promise(_=>n(S)(_))}t.toPromise=w;function C(S){const _=new oe;return S.then(E=>{_.fire(E)},()=>{_.fire(void 0)}).finally(()=>{_.dispose()}),_.event}t.fromPromise=C;function R(S,_){return S(E=>_.fire(E))}t.forward=R;function B(S,_,E){return _(E),S(A=>_(A))}t.runAndSubscribe=B;class Q{constructor(_,E){this._observable=_,this._counter=0,this._hasChanged=!1;const A={onWillAddFirstListener:()=>{_.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{_.removeObserver(this)}};this.emitter=new oe(A),E&&E.add(this.emitter)}beginUpdate(_){this._counter++}handlePossibleChange(_){}handleChange(_,E){this._hasChanged=!0}endUpdate(_){this._counter--,this._counter===0&&(this._observable.reportChanges(),this._hasChanged&&(this._hasChanged=!1,this.emitter.fire(this._observable.get())))}}function q(S,_){return new Q(S,_).emitter.event}t.fromObservable=q;function F(S){return(_,E,A)=>{let P=0,I=!1;const H={beginUpdate(){P++},endUpdate(){P--,P===0&&(S.reportChanges(),I&&(I=!1,_.call(E)))},handlePossibleChange(){},handleChange(){I=!0}};S.addObserver(H),S.reportChanges();const J={dispose(){S.removeObserver(H)}};return A instanceof Je?A.add(J):Array.isArray(A)&&A.push(J),J}}t.fromObservableLight=F})(dt||(dt={}));const Oe=class Oe{constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${Oe._idPool++}`,Oe.all.add(this)}start(e){this._stopWatch=new mt,this.listenerCount=e}stop(){if(this._stopWatch){const e=this._stopWatch.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this._stopWatch=void 0}}};Oe.all=new Set,Oe._idPool=0;let Ut=Oe,js=-1;const Pt=class Pt{constructor(e,n,r=(Pt._idPool++).toString(16).padStart(3,"0")){this._errorHandler=e,this.threshold=n,this.name=r,this._warnCountdown=0}dispose(){this._stacks?.clear()}check(e,n){const r=this.threshold;if(r<=0||n<r)return;this._stacks||(this._stacks=new Map);const s=this._stacks.get(e.value)||0;if(this._stacks.set(e.value,s+1),this._warnCountdown-=1,this._warnCountdown<=0){this._warnCountdown=r*.5;const[i,o]=this.getMostFrequentStack(),l=`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${o}):`;console.warn(l),console.warn(i);const u=new Xs(l,i);this._errorHandler(u)}return()=>{const i=this._stacks.get(e.value)||0;this._stacks.set(e.value,i-1)}}getMostFrequentStack(){if(!this._stacks)return;let e,n=0;for(const[r,s]of this._stacks)(!e||n<s)&&(e=[r,s],n=s);return e}};Pt._idPool=1;let $t=Pt;class Ht{static create(){const e=new Error;return new Ht(e.stack??"")}constructor(e){this.value=e}print(){console.warn(this.value.split(`\n`).slice(2).join(`\n`))}}class Xs extends Error{constructor(e,n){super(e),this.name="ListenerLeakError",this.stack=n}}class Qs extends Error{constructor(e,n){super(e),this.name="ListenerRefusalError",this.stack=n}}class Wt{constructor(e){this.value=e}}const Ys=2;class oe{constructor(e){this._size=0,this._options=e,this._leakageMon=this._options?.leakWarningThreshold?new $t(e?.onListenerError??Ye,this._options?.leakWarningThreshold??js):void 0,this._perfMon=this._options?._profName?new Ut(this._options._profName):void 0,this._deliveryQueue=this._options?.deliveryQueue}dispose(){this._disposed||(this._disposed=!0,this._deliveryQueue?.current===this&&this._deliveryQueue.reset(),this._listeners&&(this._listeners=void 0,this._size=0),this._options?.onDidRemoveLastListener?.(),this._leakageMon?.dispose())}get event(){return this._event??(this._event=(e,n,r)=>{if(this._leakageMon&&this._size>this._leakageMon.threshold**2){const l=`[${this._leakageMon.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this._size} vs ${this._leakageMon.threshold})`;console.warn(l);const u=this._leakageMon.getMostFrequentStack()??["UNKNOWN stack",-1],c=new Qs(`${l}. HINT: Stack shows most frequent listener (${u[1]}-times)`,u[0]);return(this._options?.onListenerError||Ye)(c),Pe.None}if(this._disposed)return Pe.None;n&&(e=e.bind(n));const s=new Wt(e);let i;this._leakageMon&&this._size>=Math.ceil(this._leakageMon.threshold*.2)&&(s.stack=Ht.create(),i=this._leakageMon.check(s.stack,this._size+1)),this._listeners?this._listeners instanceof Wt?(this._deliveryQueue??(this._deliveryQueue=new Js),this._listeners=[this._listeners,s]):this._listeners.push(s):(this._options?.onWillAddFirstListener?.(this),this._listeners=s,this._options?.onDidAddFirstListener?.(this)),this._size++;const o=ft(()=>{i?.(),this._removeListener(s)});return r instanceof Je?r.add(o):Array.isArray(r)&&r.push(o),o}),this._event}_removeListener(e){if(this._options?.onWillRemoveListener?.(this),!this._listeners)return;if(this._size===1){this._listeners=void 0,this._options?.onDidRemoveLastListener?.(this),this._size=0;return}const n=this._listeners,r=n.indexOf(e);if(r===-1)throw console.log("disposed?",this._disposed),console.log("size?",this._size),console.log("arr?",JSON.stringify(this._listeners)),new Error("Attempted to dispose unknown listener");this._size--,n[r]=void 0;const s=this._deliveryQueue.current===this;if(this._size*Ys<=n.length){let i=0;for(let o=0;o<n.length;o++)n[o]?n[i++]=n[o]:s&&(this._deliveryQueue.end--,i<this._deliveryQueue.i&&this._deliveryQueue.i--);n.length=i}}_deliver(e,n){if(!e)return;const r=this._options?.onListenerError||Ye;if(!r){e.value(n);return}try{e.value(n)}catch(s){r(s)}}_deliverQueue(e){const n=e.current._listeners;for(;e.i<e.end;)this._deliver(n[e.i++],e.value);e.reset()}fire(e){if(this._deliveryQueue?.current&&(this._deliverQueue(this._deliveryQueue),this._perfMon?.stop()),this._perfMon?.start(this._size),this._listeners)if(this._listeners instanceof Wt)this._deliver(this._listeners,e);else{const n=this._deliveryQueue;n.enqueue(this,e,this._listeners.length),this._deliverQueue(n)}this._perfMon?.stop()}hasListeners(){return this._size>0}}class Js{constructor(){this.i=-1,this.end=0}enqueue(e,n,r){this.i=0,this.end=r,this.current=e,this.value=n}reset(){this.i=this.end,this.current=void 0,this.value=void 0}}function Zs(){return globalThis._VSCODE_NLS_MESSAGES}function En(){return globalThis._VSCODE_NLS_LANGUAGE}const Ks=En()==="pseudo"||typeof document<"u"&&document.location&&document.location.hash.indexOf("pseudo=true")>=0;function Mn(t,e){let n;return e.length===0?n=t:n=t.replace(/\\{(\\d+)\\}/g,(r,s)=>{const i=s[0],o=e[i];let l=r;return typeof o=="string"?l=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(l=String(o)),l}),Ks&&(n="［"+n.replace(/[aouei]/g,"$&$&")+"］"),n}function z(t,e,...n){return Mn(typeof t=="number"?ei(t,e):e,n)}function ei(t,e){const n=Zs()?.[t];if(typeof n!="string"){if(typeof e=="string")return e;throw new Error(`!!! NLS MISSING: ${t} !!!`)}return n}const Fe="en";let zt=!1,Ot=!1,Gt=!1,kn=!1,jt=!1,gt,Xt=Fe,Pn=Fe,ti,pe;const be=globalThis;let ie;typeof be.vscode<"u"&&typeof be.vscode.process<"u"?ie=be.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(ie=process);const ni=typeof ie?.versions?.electron=="string"&&ie?.type==="renderer";if(typeof ie=="object"){zt=ie.platform==="win32",Ot=ie.platform==="darwin",Gt=ie.platform==="linux",Gt&&ie.env.SNAP&&ie.env.SNAP_REVISION,ie.env.CI||ie.env.BUILD_ARTIFACTSTAGINGDIRECTORY,gt=Fe,Xt=Fe;const t=ie.env.VSCODE_NLS_CONFIG;if(t)try{const e=JSON.parse(t);gt=e.userLocale,Pn=e.osLocale,Xt=e.resolvedLanguage||Fe,ti=e.languagePack?.translationsConfigFile}catch{}kn=!0}else typeof navigator=="object"&&!ni?(pe=navigator.userAgent,zt=pe.indexOf("Windows")>=0,Ot=pe.indexOf("Macintosh")>=0,(pe.indexOf("Macintosh")>=0||pe.indexOf("iPad")>=0||pe.indexOf("iPhone")>=0)&&navigator.maxTouchPoints&&navigator.maxTouchPoints>0,Gt=pe.indexOf("Linux")>=0,pe?.indexOf("Mobi")>=0,jt=!0,Xt=En()||Fe,gt=navigator.language.toLowerCase(),Pn=gt):console.error("Unable to resolve platform.");const Ze=zt,ri=Ot,si=kn,ii=jt,ai=jt&&typeof be.importScripts=="function"?be.origin:void 0,me=pe,oi=typeof be.postMessage=="function"&&!be.importScripts;(()=>{if(oi){const t=[];be.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,s=t.length;r<s;r++){const i=t[r];if(i.id===n.data.vscodeScheduleAsyncWork){t.splice(r,1),i.callback();return}}});let e=0;return n=>{const r=++e;t.push({id:r,callback:n}),be.postMessage({vscodeScheduleAsyncWork:r},"*")}}return t=>setTimeout(t)})();const li=!!(me&&me.indexOf("Chrome")>=0);me&&me.indexOf("Firefox")>=0,!li&&me&&me.indexOf("Safari")>=0,me&&me.indexOf("Edg/")>=0,me&&me.indexOf("Android")>=0;function ui(t){return t}class ci{constructor(e,n){this.lastCache=void 0,this.lastArgKey=void 0,typeof e=="function"?(this._fn=e,this._computeKey=ui):(this._fn=n,this._computeKey=e.getCacheKey)}get(e){const n=this._computeKey(e);return this.lastArgKey!==n&&(this.lastArgKey=n,this.lastCache=this._fn(e)),this.lastCache}}class Fn{constructor(e){this.executor=e,this._didRun=!1}get value(){if(!this._didRun)try{this._value=this.executor()}catch(e){this._error=e}finally{this._didRun=!0}if(this._error)throw this._error;return this._value}get rawValue(){return this._value}}function hi(t){return t.replace(/[\\\\\\{\\}\\*\\+\\?\\|\\^\\$\\.\\[\\]\\(\\)]/g,"\\\\$&")}function fi(t){return t.split(/\\r\\n|\\r|\\n/)}function mi(t){for(let e=0,n=t.length;e<n;e++){const r=t.charCodeAt(e);if(r!==32&&r!==9)return e}return-1}function di(t,e=t.length-1){for(let n=e;n>=0;n--){const r=t.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function Dn(t){return t>=65&&t<=90}function Qt(t){return 55296<=t&&t<=56319}function gi(t){return 56320<=t&&t<=57343}function pi(t,e){return(t-55296<<10)+(e-56320)+65536}function bi(t,e,n){const r=t.charCodeAt(n);if(Qt(r)&&n+1<e){const s=t.charCodeAt(n+1);if(gi(s))return pi(r,s)}return r}const yi=/^[\\t\\n\\r\\x20-\\x7E]*$/;function xi(t){return yi.test(t)}const ge=class ge{static getInstance(e){return ge.cache.get(Array.from(e))}static getLocales(){return ge._locales.value}constructor(e){this.confusableDictionary=e}isAmbiguous(e){return this.confusableDictionary.has(e)}getPrimaryConfusable(e){return this.confusableDictionary.get(e)}getConfusableCodePoints(){return new Set(this.confusableDictionary.keys())}};ge.ambiguousCharacterData=new Fn(()=>JSON.parse(\'{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,8218,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,8242,96,1370,96,1523,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71922,67,71913,67,65315,67,8557,67,8450,67,8493,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71919,87,71910,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,66293,90,71909,90,65338,90,8484,90,8488,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65297,49,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"cs":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"es":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"fr":[65374,126,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"it":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ja":[8211,45,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65292,44,65307,59],"ko":[8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"pt-BR":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"ru":[65374,126,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65288,40,65289,41,65292,44,65307,59,65311,63],"zh-hans":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41],"zh-hant":[8211,45,65374,126,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65283,35,65307,59]}\')),ge.cache=new ci({getCacheKey:JSON.stringify},e=>{function n(f){const h=new Map;for(let m=0;m<f.length;m+=2)h.set(f[m],f[m+1]);return h}function r(f,h){const m=new Map(f);for(const[d,g]of h)m.set(d,g);return m}function s(f,h){if(!f)return h;const m=new Map;for(const[d,g]of f)h.has(d)&&m.set(d,g);return m}const i=ge.ambiguousCharacterData.value;let o=e.filter(f=>!f.startsWith("_")&&f in i);o.length===0&&(o=["_default"]);let l;for(const f of o){const h=n(i[f]);l=s(l,h)}const u=n(i._common),c=r(u,l);return new ge(c)}),ge._locales=new Fn(()=>Object.keys(ge.ambiguousCharacterData.value).filter(e=>!e.startsWith("_")));let Ke=ge;const Ge=class Ge{static getRawData(){return JSON.parse("[9,10,11,12,13,32,127,160,173,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8203,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12288,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999]")}static getData(){return this._data||(this._data=new Set(Ge.getRawData())),this._data}static isInvisibleCharacter(e){return Ge.getData().has(e)}static get codePoints(){return Ge.getData()}};Ge._data=void 0;let et=Ge;var Tn={ENV_TYPE:"local",NODE_ENV:"production"};let De;const Yt=globalThis.vscode;if(typeof Yt<"u"&&typeof Yt.process<"u"){const t=Yt.process;De={get platform(){return t.platform},get arch(){return t.arch},get env(){return t.env},cwd(){return t.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?De={get platform(){return process.platform},get arch(){return process.arch},get env(){return Tn},cwd(){return Tn.VSCODE_CWD||process.cwd()}}:De={get platform(){return Ze?"win32":ri?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};const pt=De.cwd,_i=De.env,wi=De.platform,Li=65,vi=97,Ni=90,Si=122,Le=46,Y=47,ne=92,ve=58,Ci=63;class In extends Error{constructor(e,n,r){let s;typeof n=="string"&&n.indexOf("not ")===0?(s="must not be",n=n.replace(/^not /,"")):s="must be";const i=e.indexOf(".")!==-1?"property":"argument";let o=`The "${e}" ${i} ${s} of type ${n}`;o+=`. Received type ${typeof r}`,super(o),this.code="ERR_INVALID_ARG_TYPE"}}function Ai(t,e){if(t===null||typeof t!="object")throw new In(e,"Object",t)}function j(t,e){if(typeof t!="string")throw new In(e,"string",t)}const ye=wi==="win32";function T(t){return t===Y||t===ne}function Jt(t){return t===Y}function Ne(t){return t>=Li&&t<=Ni||t>=vi&&t<=Si}function bt(t,e,n,r){let s="",i=0,o=-1,l=0,u=0;for(let c=0;c<=t.length;++c){if(c<t.length)u=t.charCodeAt(c);else{if(r(u))break;u=Y}if(r(u)){if(!(o===c-1||l===1))if(l===2){if(s.length<2||i!==2||s.charCodeAt(s.length-1)!==Le||s.charCodeAt(s.length-2)!==Le){if(s.length>2){const f=s.lastIndexOf(n);f===-1?(s="",i=0):(s=s.slice(0,f),i=s.length-1-s.lastIndexOf(n)),o=c,l=0;continue}else if(s.length!==0){s="",i=0,o=c,l=0;continue}}e&&(s+=s.length>0?`${n}..`:"..",i=2)}else s.length>0?s+=`${n}${t.slice(o+1,c)}`:s=t.slice(o+1,c),i=c-o-1;o=c,l=0}else u===Le&&l!==-1?++l:l=-1}return s}function Ri(t){return t?`${t[0]==="."?"":"."}${t}`:""}function Vn(t,e){Ai(e,"pathObject");const n=e.dir||e.root,r=e.base||`${e.name||""}${Ri(e.ext)}`;return n?n===e.root?`${n}${r}`:`${n}${t}${r}`:r}const Z={resolve(...t){let e="",n="",r=!1;for(let s=t.length-1;s>=-1;s--){let i;if(s>=0){if(i=t[s],j(i,`paths[${s}]`),i.length===0)continue}else e.length===0?i=pt():(i=_i[`=${e}`]||pt(),(i===void 0||i.slice(0,2).toLowerCase()!==e.toLowerCase()&&i.charCodeAt(2)===ne)&&(i=`${e}\\\\`));const o=i.length;let l=0,u="",c=!1;const f=i.charCodeAt(0);if(o===1)T(f)&&(l=1,c=!0);else if(T(f))if(c=!0,T(i.charCodeAt(1))){let h=2,m=h;for(;h<o&&!T(i.charCodeAt(h));)h++;if(h<o&&h!==m){const d=i.slice(m,h);for(m=h;h<o&&T(i.charCodeAt(h));)h++;if(h<o&&h!==m){for(m=h;h<o&&!T(i.charCodeAt(h));)h++;(h===o||h!==m)&&(u=`\\\\\\\\${d}\\\\${i.slice(m,h)}`,l=h)}}}else l=1;else Ne(f)&&i.charCodeAt(1)===ve&&(u=i.slice(0,2),l=2,o>2&&T(i.charCodeAt(2))&&(c=!0,l=3));if(u.length>0)if(e.length>0){if(u.toLowerCase()!==e.toLowerCase())continue}else e=u;if(r){if(e.length>0)break}else if(n=`${i.slice(l)}\\\\${n}`,r=c,c&&e.length>0)break}return n=bt(n,!r,"\\\\",T),r?`${e}\\\\${n}`:`${e}${n}`||"."},normalize(t){j(t,"path");const e=t.length;if(e===0)return".";let n=0,r,s=!1;const i=t.charCodeAt(0);if(e===1)return Jt(i)?"\\\\":t;if(T(i))if(s=!0,T(t.charCodeAt(1))){let l=2,u=l;for(;l<e&&!T(t.charCodeAt(l));)l++;if(l<e&&l!==u){const c=t.slice(u,l);for(u=l;l<e&&T(t.charCodeAt(l));)l++;if(l<e&&l!==u){for(u=l;l<e&&!T(t.charCodeAt(l));)l++;if(l===e)return`\\\\\\\\${c}\\\\${t.slice(u)}\\\\`;l!==u&&(r=`\\\\\\\\${c}\\\\${t.slice(u,l)}`,n=l)}}}else n=1;else Ne(i)&&t.charCodeAt(1)===ve&&(r=t.slice(0,2),n=2,e>2&&T(t.charCodeAt(2))&&(s=!0,n=3));let o=n<e?bt(t.slice(n),!s,"\\\\",T):"";return o.length===0&&!s&&(o="."),o.length>0&&T(t.charCodeAt(e-1))&&(o+="\\\\"),r===void 0?s?`\\\\${o}`:o:s?`${r}\\\\${o}`:`${r}${o}`},isAbsolute(t){j(t,"path");const e=t.length;if(e===0)return!1;const n=t.charCodeAt(0);return T(n)||e>2&&Ne(n)&&t.charCodeAt(1)===ve&&T(t.charCodeAt(2))},join(...t){if(t.length===0)return".";let e,n;for(let i=0;i<t.length;++i){const o=t[i];j(o,"path"),o.length>0&&(e===void 0?e=n=o:e+=`\\\\${o}`)}if(e===void 0)return".";let r=!0,s=0;if(typeof n=="string"&&T(n.charCodeAt(0))){++s;const i=n.length;i>1&&T(n.charCodeAt(1))&&(++s,i>2&&(T(n.charCodeAt(2))?++s:r=!1))}if(r){for(;s<e.length&&T(e.charCodeAt(s));)s++;s>=2&&(e=`\\\\${e.slice(s)}`)}return Z.normalize(e)},relative(t,e){if(j(t,"from"),j(e,"to"),t===e)return"";const n=Z.resolve(t),r=Z.resolve(e);if(n===r||(t=n.toLowerCase(),e=r.toLowerCase(),t===e))return"";let s=0;for(;s<t.length&&t.charCodeAt(s)===ne;)s++;let i=t.length;for(;i-1>s&&t.charCodeAt(i-1)===ne;)i--;const o=i-s;let l=0;for(;l<e.length&&e.charCodeAt(l)===ne;)l++;let u=e.length;for(;u-1>l&&e.charCodeAt(u-1)===ne;)u--;const c=u-l,f=o<c?o:c;let h=-1,m=0;for(;m<f;m++){const g=t.charCodeAt(s+m);if(g!==e.charCodeAt(l+m))break;g===ne&&(h=m)}if(m!==f){if(h===-1)return r}else{if(c>f){if(e.charCodeAt(l+m)===ne)return r.slice(l+m+1);if(m===2)return r.slice(l+m)}o>f&&(t.charCodeAt(s+m)===ne?h=m:m===2&&(h=3)),h===-1&&(h=0)}let d="";for(m=s+h+1;m<=i;++m)(m===i||t.charCodeAt(m)===ne)&&(d+=d.length===0?"..":"\\\\..");return l+=h,d.length>0?`${d}${r.slice(l,u)}`:(r.charCodeAt(l)===ne&&++l,r.slice(l,u))},toNamespacedPath(t){if(typeof t!="string"||t.length===0)return t;const e=Z.resolve(t);if(e.length<=2)return t;if(e.charCodeAt(0)===ne){if(e.charCodeAt(1)===ne){const n=e.charCodeAt(2);if(n!==Ci&&n!==Le)return`\\\\\\\\?\\\\UNC\\\\${e.slice(2)}`}}else if(Ne(e.charCodeAt(0))&&e.charCodeAt(1)===ve&&e.charCodeAt(2)===ne)return`\\\\\\\\?\\\\${e}`;return t},dirname(t){j(t,"path");const e=t.length;if(e===0)return".";let n=-1,r=0;const s=t.charCodeAt(0);if(e===1)return T(s)?t:".";if(T(s)){if(n=r=1,T(t.charCodeAt(1))){let l=2,u=l;for(;l<e&&!T(t.charCodeAt(l));)l++;if(l<e&&l!==u){for(u=l;l<e&&T(t.charCodeAt(l));)l++;if(l<e&&l!==u){for(u=l;l<e&&!T(t.charCodeAt(l));)l++;if(l===e)return t;l!==u&&(n=r=l+1)}}}}else Ne(s)&&t.charCodeAt(1)===ve&&(n=e>2&&T(t.charCodeAt(2))?3:2,r=n);let i=-1,o=!0;for(let l=e-1;l>=r;--l)if(T(t.charCodeAt(l))){if(!o){i=l;break}}else o=!1;if(i===-1){if(n===-1)return".";i=n}return t.slice(0,i)},basename(t,e){e!==void 0&&j(e,"suffix"),j(t,"path");let n=0,r=-1,s=!0,i;if(t.length>=2&&Ne(t.charCodeAt(0))&&t.charCodeAt(1)===ve&&(n=2),e!==void 0&&e.length>0&&e.length<=t.length){if(e===t)return"";let o=e.length-1,l=-1;for(i=t.length-1;i>=n;--i){const u=t.charCodeAt(i);if(T(u)){if(!s){n=i+1;break}}else l===-1&&(s=!1,l=i+1),o>=0&&(u===e.charCodeAt(o)?--o===-1&&(r=i):(o=-1,r=l))}return n===r?r=l:r===-1&&(r=t.length),t.slice(n,r)}for(i=t.length-1;i>=n;--i)if(T(t.charCodeAt(i))){if(!s){n=i+1;break}}else r===-1&&(s=!1,r=i+1);return r===-1?"":t.slice(n,r)},extname(t){j(t,"path");let e=0,n=-1,r=0,s=-1,i=!0,o=0;t.length>=2&&t.charCodeAt(1)===ve&&Ne(t.charCodeAt(0))&&(e=r=2);for(let l=t.length-1;l>=e;--l){const u=t.charCodeAt(l);if(T(u)){if(!i){r=l+1;break}continue}s===-1&&(i=!1,s=l+1),u===Le?n===-1?n=l:o!==1&&(o=1):n!==-1&&(o=-1)}return n===-1||s===-1||o===0||o===1&&n===s-1&&n===r+1?"":t.slice(n,s)},format:Vn.bind(null,"\\\\"),parse(t){j(t,"path");const e={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return e;const n=t.length;let r=0,s=t.charCodeAt(0);if(n===1)return T(s)?(e.root=e.dir=t,e):(e.base=e.name=t,e);if(T(s)){if(r=1,T(t.charCodeAt(1))){let h=2,m=h;for(;h<n&&!T(t.charCodeAt(h));)h++;if(h<n&&h!==m){for(m=h;h<n&&T(t.charCodeAt(h));)h++;if(h<n&&h!==m){for(m=h;h<n&&!T(t.charCodeAt(h));)h++;h===n?r=h:h!==m&&(r=h+1)}}}}else if(Ne(s)&&t.charCodeAt(1)===ve){if(n<=2)return e.root=e.dir=t,e;if(r=2,T(t.charCodeAt(2))){if(n===3)return e.root=e.dir=t,e;r=3}}r>0&&(e.root=t.slice(0,r));let i=-1,o=r,l=-1,u=!0,c=t.length-1,f=0;for(;c>=r;--c){if(s=t.charCodeAt(c),T(s)){if(!u){o=c+1;break}continue}l===-1&&(u=!1,l=c+1),s===Le?i===-1?i=c:f!==1&&(f=1):i!==-1&&(f=-1)}return l!==-1&&(i===-1||f===0||f===1&&i===l-1&&i===o+1?e.base=e.name=t.slice(o,l):(e.name=t.slice(o,i),e.base=t.slice(o,l),e.ext=t.slice(i,l))),o>0&&o!==r?e.dir=t.slice(0,o-1):e.dir=e.root,e},sep:"\\\\",delimiter:";",win32:null,posix:null},Ei=(()=>{if(ye){const t=/\\\\/g;return()=>{const e=pt().replace(t,"/");return e.slice(e.indexOf("/"))}}return()=>pt()})(),K={resolve(...t){let e="",n=!1;for(let r=t.length-1;r>=-1&&!n;r--){const s=r>=0?t[r]:Ei();j(s,`paths[${r}]`),s.length!==0&&(e=`${s}/${e}`,n=s.charCodeAt(0)===Y)}return e=bt(e,!n,"/",Jt),n?`/${e}`:e.length>0?e:"."},normalize(t){if(j(t,"path"),t.length===0)return".";const e=t.charCodeAt(0)===Y,n=t.charCodeAt(t.length-1)===Y;return t=bt(t,!e,"/",Jt),t.length===0?e?"/":n?"./":".":(n&&(t+="/"),e?`/${t}`:t)},isAbsolute(t){return j(t,"path"),t.length>0&&t.charCodeAt(0)===Y},join(...t){if(t.length===0)return".";let e;for(let n=0;n<t.length;++n){const r=t[n];j(r,"path"),r.length>0&&(e===void 0?e=r:e+=`/${r}`)}return e===void 0?".":K.normalize(e)},relative(t,e){if(j(t,"from"),j(e,"to"),t===e||(t=K.resolve(t),e=K.resolve(e),t===e))return"";const n=1,r=t.length,s=r-n,i=1,o=e.length-i,l=s<o?s:o;let u=-1,c=0;for(;c<l;c++){const h=t.charCodeAt(n+c);if(h!==e.charCodeAt(i+c))break;h===Y&&(u=c)}if(c===l)if(o>l){if(e.charCodeAt(i+c)===Y)return e.slice(i+c+1);if(c===0)return e.slice(i+c)}else s>l&&(t.charCodeAt(n+c)===Y?u=c:c===0&&(u=0));let f="";for(c=n+u+1;c<=r;++c)(c===r||t.charCodeAt(c)===Y)&&(f+=f.length===0?"..":"/..");return`${f}${e.slice(i+u)}`},toNamespacedPath(t){return t},dirname(t){if(j(t,"path"),t.length===0)return".";const e=t.charCodeAt(0)===Y;let n=-1,r=!0;for(let s=t.length-1;s>=1;--s)if(t.charCodeAt(s)===Y){if(!r){n=s;break}}else r=!1;return n===-1?e?"/":".":e&&n===1?"//":t.slice(0,n)},basename(t,e){e!==void 0&&j(e,"ext"),j(t,"path");let n=0,r=-1,s=!0,i;if(e!==void 0&&e.length>0&&e.length<=t.length){if(e===t)return"";let o=e.length-1,l=-1;for(i=t.length-1;i>=0;--i){const u=t.charCodeAt(i);if(u===Y){if(!s){n=i+1;break}}else l===-1&&(s=!1,l=i+1),o>=0&&(u===e.charCodeAt(o)?--o===-1&&(r=i):(o=-1,r=l))}return n===r?r=l:r===-1&&(r=t.length),t.slice(n,r)}for(i=t.length-1;i>=0;--i)if(t.charCodeAt(i)===Y){if(!s){n=i+1;break}}else r===-1&&(s=!1,r=i+1);return r===-1?"":t.slice(n,r)},extname(t){j(t,"path");let e=-1,n=0,r=-1,s=!0,i=0;for(let o=t.length-1;o>=0;--o){const l=t.charCodeAt(o);if(l===Y){if(!s){n=o+1;break}continue}r===-1&&(s=!1,r=o+1),l===Le?e===-1?e=o:i!==1&&(i=1):e!==-1&&(i=-1)}return e===-1||r===-1||i===0||i===1&&e===r-1&&e===n+1?"":t.slice(e,r)},format:Vn.bind(null,"/"),parse(t){j(t,"path");const e={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return e;const n=t.charCodeAt(0)===Y;let r;n?(e.root="/",r=1):r=0;let s=-1,i=0,o=-1,l=!0,u=t.length-1,c=0;for(;u>=r;--u){const f=t.charCodeAt(u);if(f===Y){if(!l){i=u+1;break}continue}o===-1&&(l=!1,o=u+1),f===Le?s===-1?s=u:c!==1&&(c=1):s!==-1&&(c=-1)}if(o!==-1){const f=i===0&&n?1:i;s===-1||c===0||c===1&&s===o-1&&s===i+1?e.base=e.name=t.slice(f,o):(e.name=t.slice(f,s),e.base=t.slice(f,o),e.ext=t.slice(s,o))}return i>0?e.dir=t.slice(0,i-1):n&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};K.win32=Z.win32=Z,K.posix=Z.posix=K,ye?Z.normalize:K.normalize;const Mi=ye?Z.join:K.join;ye?Z.resolve:K.resolve,ye?Z.relative:K.relative,ye?Z.dirname:K.dirname,ye?Z.basename:K.basename,ye?Z.extname:K.extname,ye?Z.sep:K.sep;const ki=/^\\w[\\w\\d+.-]*$/,Pi=/^\\//,Fi=/^\\/\\//;function Di(t,e){if(!t.scheme&&e)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${t.authority}", path: "${t.path}", query: "${t.query}", fragment: "${t.fragment}"}`);if(t.scheme&&!ki.test(t.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(t.path){if(t.authority){if(!Pi.test(t.path))throw new Error(\'[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character\')}else if(Fi.test(t.path))throw new Error(\'[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")\')}}function Ti(t,e){return!t&&!e?"file":t}function Ii(t,e){switch(t){case"https":case"http":case"file":e?e[0]!==he&&(e=he+e):e=he;break}return e}const W="",he="/",Vi=/^(([^:/?#]+?):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;class re{static isUri(e){return e instanceof re?!0:e?typeof e.authority=="string"&&typeof e.fragment=="string"&&typeof e.path=="string"&&typeof e.query=="string"&&typeof e.scheme=="string"&&typeof e.fsPath=="string"&&typeof e.with=="function"&&typeof e.toString=="function":!1}constructor(e,n,r,s,i,o=!1){typeof e=="object"?(this.scheme=e.scheme||W,this.authority=e.authority||W,this.path=e.path||W,this.query=e.query||W,this.fragment=e.fragment||W):(this.scheme=Ti(e,o),this.authority=n||W,this.path=Ii(this.scheme,r||W),this.query=s||W,this.fragment=i||W,Di(this,o))}get fsPath(){return Zt(this,!1)}with(e){if(!e)return this;let{scheme:n,authority:r,path:s,query:i,fragment:o}=e;return n===void 0?n=this.scheme:n===null&&(n=W),r===void 0?r=this.authority:r===null&&(r=W),s===void 0?s=this.path:s===null&&(s=W),i===void 0?i=this.query:i===null&&(i=W),o===void 0?o=this.fragment:o===null&&(o=W),n===this.scheme&&r===this.authority&&s===this.path&&i===this.query&&o===this.fragment?this:new Te(n,r,s,i,o)}static parse(e,n=!1){const r=Vi.exec(e);return r?new Te(r[2]||W,yt(r[4]||W),yt(r[5]||W),yt(r[7]||W),yt(r[9]||W),n):new Te(W,W,W,W,W)}static file(e){let n=W;if(Ze&&(e=e.replace(/\\\\/g,he)),e[0]===he&&e[1]===he){const r=e.indexOf(he,2);r===-1?(n=e.substring(2),e=he):(n=e.substring(2,r),e=e.substring(r)||he)}return new Te("file",n,e,W,W)}static from(e,n){return new Te(e.scheme,e.authority,e.path,e.query,e.fragment,n)}static joinPath(e,...n){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return Ze&&e.scheme==="file"?r=re.file(Z.join(Zt(e,!0),...n)).path:r=K.join(e.path,...n),e.with({path:r})}toString(e=!1){return Kt(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof re)return e;{const n=new Te(e);return n._formatted=e.external??null,n._fsPath=e._sep===Bn?e.fsPath??null:null,n}}else return e}}const Bn=Ze?1:void 0;class Te extends re{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Zt(this,!1)),this._fsPath}toString(e=!1){return e?Kt(this,!0):(this._formatted||(this._formatted=Kt(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=Bn),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}}const qn={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Un(t,e,n){let r,s=-1;for(let i=0;i<t.length;i++){const o=t.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||e&&o===47||n&&o===91||n&&o===93||n&&o===58)s!==-1&&(r+=encodeURIComponent(t.substring(s,i)),s=-1),r!==void 0&&(r+=t.charAt(i));else{r===void 0&&(r=t.substr(0,i));const l=qn[o];l!==void 0?(s!==-1&&(r+=encodeURIComponent(t.substring(s,i)),s=-1),r+=l):s===-1&&(s=i)}}return s!==-1&&(r+=encodeURIComponent(t.substring(s))),r!==void 0?r:t}function Bi(t){let e;for(let n=0;n<t.length;n++){const r=t.charCodeAt(n);r===35||r===63?(e===void 0&&(e=t.substr(0,n)),e+=qn[r]):e!==void 0&&(e+=t[n])}return e!==void 0?e:t}function Zt(t,e){let n;return t.authority&&t.path.length>1&&t.scheme==="file"?n=`//${t.authority}${t.path}`:t.path.charCodeAt(0)===47&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&t.path.charCodeAt(2)===58?e?n=t.path.substr(1):n=t.path[1].toLowerCase()+t.path.substr(2):n=t.path,Ze&&(n=n.replace(/\\//g,"\\\\")),n}function Kt(t,e){const n=e?Bi:Un;let r="",{scheme:s,authority:i,path:o,query:l,fragment:u}=t;if(s&&(r+=s,r+=":"),(i||s==="file")&&(r+=he,r+=he),i){let c=i.indexOf("@");if(c!==-1){const f=i.substr(0,c);i=i.substr(c+1),c=f.lastIndexOf(":"),c===-1?r+=n(f,!1,!1):(r+=n(f.substr(0,c),!1,!1),r+=":",r+=n(f.substr(c+1),!1,!0)),r+="@"}i=i.toLowerCase(),c=i.lastIndexOf(":"),c===-1?r+=n(i,!1,!0):(r+=n(i.substr(0,c),!1,!0),r+=i.substr(c))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const c=o.charCodeAt(1);c>=65&&c<=90&&(o=`/${String.fromCharCode(c+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const c=o.charCodeAt(0);c>=65&&c<=90&&(o=`${String.fromCharCode(c+32)}:${o.substr(2)}`)}r+=n(o,!0,!1)}return l&&(r+="?",r+=n(l,!1,!1)),u&&(r+="#",r+=e?u:Un(u,!1,!1)),r}function $n(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+$n(t.substr(3)):t}}const Hn=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function yt(t){return t.match(Hn)?t.replace(Hn,e=>$n(e)):t}var Se;(function(t){t.inMemory="inmemory",t.vscode="vscode",t.internal="private",t.walkThrough="walkThrough",t.walkThroughSnippet="walkThroughSnippet",t.http="http",t.https="https",t.file="file",t.mailto="mailto",t.untitled="untitled",t.data="data",t.command="command",t.vscodeRemote="vscode-remote",t.vscodeRemoteResource="vscode-remote-resource",t.vscodeManagedRemoteResource="vscode-managed-remote-resource",t.vscodeUserData="vscode-userdata",t.vscodeCustomEditor="vscode-custom-editor",t.vscodeNotebookCell="vscode-notebook-cell",t.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",t.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",t.vscodeNotebookCellOutput="vscode-notebook-cell-output",t.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",t.vscodeNotebookMetadata="vscode-notebook-metadata",t.vscodeInteractiveInput="vscode-interactive-input",t.vscodeSettings="vscode-settings",t.vscodeWorkspaceTrust="vscode-workspace-trust",t.vscodeTerminal="vscode-terminal",t.vscodeChatCodeBlock="vscode-chat-code-block",t.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",t.vscodeChatSesssion="vscode-chat-editor",t.webviewPanel="webview-panel",t.vscodeWebview="vscode-webview",t.extension="extension",t.vscodeFileResource="vscode-file",t.tmp="tmp",t.vsls="vsls",t.vscodeSourceControl="vscode-scm",t.commentsInput="comment",t.codeSetting="code-setting",t.outputChannel="output"})(Se||(Se={}));const qi="tkn";class Ui{constructor(){this._hosts=Object.create(null),this._ports=Object.create(null),this._connectionTokens=Object.create(null),this._preferredWebSchema="http",this._delegate=null,this._serverRootPath="/"}setPreferredWebSchema(e){this._preferredWebSchema=e}get _remoteResourcesPath(){return K.join(this._serverRootPath,Se.vscodeRemoteResource)}rewrite(e){if(this._delegate)try{return this._delegate(e)}catch(l){return Ye(l),e}const n=e.authority;let r=this._hosts[n];r&&r.indexOf(":")!==-1&&r.indexOf("[")===-1&&(r=`[${r}]`);const s=this._ports[n],i=this._connectionTokens[n];let o=`path=${encodeURIComponent(e.path)}`;return typeof i=="string"&&(o+=`&${qi}=${encodeURIComponent(i)}`),re.from({scheme:ii?this._preferredWebSchema:Se.vscodeRemoteResource,authority:`${r}:${s}`,path:this._remoteResourcesPath,query:o})}}const $i=new Ui,Hi="vscode-app",ot=class ot{asBrowserUri(e){const n=this.toUri(e);return this.uriToBrowserUri(n)}uriToBrowserUri(e){return e.scheme===Se.vscodeRemote?$i.rewrite(e):e.scheme===Se.file&&(si||ai===`${Se.vscodeFileResource}://${ot.FALLBACK_AUTHORITY}`)?e.with({scheme:Se.vscodeFileResource,authority:e.authority||ot.FALLBACK_AUTHORITY,query:null,fragment:null}):e}toUri(e,n){if(re.isUri(e))return e;if(globalThis._VSCODE_FILE_ROOT){const r=globalThis._VSCODE_FILE_ROOT;if(/^\\w[\\w\\d+.-]*:\\/\\//.test(r))return re.joinPath(re.parse(r,!0),e);const s=Mi(r,e);return re.file(s)}return re.parse(n.toUrl(e))}};ot.FALLBACK_AUTHORITY=Hi;let en=ot;const Wn=new en;var zn;(function(t){const e=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);t.CoopAndCoep=Object.freeze(e.get("3"));const n="vscode-coi";function r(i){let o;typeof i=="string"?o=new URL(i).searchParams:i instanceof URL?o=i.searchParams:re.isUri(i)&&(o=new URL(i.toString(!0)).searchParams);const l=o?.get(n);if(l)return e.get(l)}t.getHeadersFromQuery=r;function s(i,o,l){if(!globalThis.crossOriginIsolated)return;const u=o&&l?"3":l?"2":"1";i instanceof URLSearchParams?i.set(n,u):i[n]=u}t.addSearchParam=s})(zn||(zn={}));const tn="default",Wi="$initialize";class zi{constructor(e,n,r,s,i){this.vsWorker=e,this.req=n,this.channel=r,this.method=s,this.args=i,this.type=0}}class On{constructor(e,n,r,s){this.vsWorker=e,this.seq=n,this.res=r,this.err=s,this.type=1}}class Oi{constructor(e,n,r,s,i){this.vsWorker=e,this.req=n,this.channel=r,this.eventName=s,this.arg=i,this.type=2}}class Gi{constructor(e,n,r){this.vsWorker=e,this.req=n,this.event=r,this.type=3}}class ji{constructor(e,n){this.vsWorker=e,this.req=n,this.type=4}}class Xi{constructor(e){this._workerId=-1,this._handler=e,this._lastSentReq=0,this._pendingReplies=Object.create(null),this._pendingEmitters=new Map,this._pendingEvents=new Map}setWorkerId(e){this._workerId=e}sendMessage(e,n,r){const s=String(++this._lastSentReq);return new Promise((i,o)=>{this._pendingReplies[s]={resolve:i,reject:o},this._send(new zi(this._workerId,s,e,n,r))})}listen(e,n,r){let s=null;const i=new oe({onWillAddFirstListener:()=>{s=String(++this._lastSentReq),this._pendingEmitters.set(s,i),this._send(new Oi(this._workerId,s,e,n,r))},onDidRemoveLastListener:()=>{this._pendingEmitters.delete(s),this._send(new ji(this._workerId,s)),s=null}});return i.event}handleMessage(e){!e||!e.vsWorker||this._workerId!==-1&&e.vsWorker!==this._workerId||this._handleMessage(e)}createProxyToRemoteChannel(e,n){const r={get:(s,i)=>(typeof i=="string"&&!s[i]&&(jn(i)?s[i]=o=>this.listen(e,i,o):Gn(i)?s[i]=this.listen(e,i,void 0):i.charCodeAt(0)===36&&(s[i]=async(...o)=>(await n?.(),this.sendMessage(e,i,o)))),s[i])};return new Proxy(Object.create(null),r)}_handleMessage(e){switch(e.type){case 1:return this._handleReplyMessage(e);case 0:return this._handleRequestMessage(e);case 2:return this._handleSubscribeEventMessage(e);case 3:return this._handleEventMessage(e);case 4:return this._handleUnsubscribeEventMessage(e)}}_handleReplyMessage(e){if(!this._pendingReplies[e.seq]){console.warn("Got reply to unknown seq");return}const n=this._pendingReplies[e.seq];if(delete this._pendingReplies[e.seq],e.err){let r=e.err;e.err.$isError&&(r=new Error,r.name=e.err.name,r.message=e.err.message,r.stack=e.err.stack),n.reject(r);return}n.resolve(e.res)}_handleRequestMessage(e){const n=e.req;this._handler.handleMessage(e.channel,e.method,e.args).then(s=>{this._send(new On(this._workerId,n,s,void 0))},s=>{s.detail instanceof Error&&(s.detail=An(s.detail)),this._send(new On(this._workerId,n,void 0,An(s)))})}_handleSubscribeEventMessage(e){const n=e.req,r=this._handler.handleEvent(e.channel,e.eventName,e.arg)(s=>{this._send(new Gi(this._workerId,n,s))});this._pendingEvents.set(n,r)}_handleEventMessage(e){if(!this._pendingEmitters.has(e.req)){console.warn("Got event for unknown req");return}this._pendingEmitters.get(e.req).fire(e.event)}_handleUnsubscribeEventMessage(e){if(!this._pendingEvents.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this._pendingEvents.get(e.req).dispose(),this._pendingEvents.delete(e.req)}_send(e){const n=[];if(e.type===0)for(let r=0;r<e.args.length;r++)e.args[r]instanceof ArrayBuffer&&n.push(e.args[r]);else e.type===1&&e.res instanceof ArrayBuffer&&n.push(e.res);this._handler.sendMessage(e,n)}}function Gn(t){return t[0]==="o"&&t[1]==="n"&&Dn(t.charCodeAt(2))}function jn(t){return/^onDynamic/.test(t)&&Dn(t.charCodeAt(9))}class Qi{constructor(e,n){this._localChannels=new Map,this._remoteChannels=new Map,this._requestHandlerFactory=n,this._requestHandler=null,this._protocol=new Xi({sendMessage:(r,s)=>{e(r,s)},handleMessage:(r,s,i)=>this._handleMessage(r,s,i),handleEvent:(r,s,i)=>this._handleEvent(r,s,i)})}onmessage(e){this._protocol.handleMessage(e)}_handleMessage(e,n,r){if(e===tn&&n===Wi)return this.initialize(r[0],r[1],r[2]);const s=e===tn?this._requestHandler:this._localChannels.get(e);if(!s)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof s[n]!="function")return Promise.reject(new Error(`Missing method ${n} on worker thread channel ${e}`));try{return Promise.resolve(s[n].apply(s,r))}catch(i){return Promise.reject(i)}}_handleEvent(e,n,r){const s=e===tn?this._requestHandler:this._localChannels.get(e);if(!s)throw new Error(`Missing channel ${e} on worker thread`);if(jn(n)){const i=s[n].call(s,r);if(typeof i!="function")throw new Error(`Missing dynamic event ${n} on request handler.`);return i}if(Gn(n)){const i=s[n];if(typeof i!="function")throw new Error(`Missing event ${n} on request handler.`);return i}throw new Error(`Malformed event name ${n}`)}getChannel(e){if(!this._remoteChannels.has(e)){const n=this._protocol.createProxyToRemoteChannel(e);this._remoteChannels.set(e,n)}return this._remoteChannels.get(e)}async initialize(e,n,r){if(this._protocol.setWorkerId(e),this._requestHandlerFactory){this._requestHandler=this._requestHandlerFactory(this);return}return n&&(typeof n.baseUrl<"u"&&delete n.baseUrl,typeof n.paths<"u"&&typeof n.paths.vs<"u"&&delete n.paths.vs,typeof n.trustedTypesPolicy<"u"&&delete n.trustedTypesPolicy,n.catchError=!0,globalThis.require.config(n)),import(`${Wn.asBrowserUri(`${r}.js`).toString(!0)}`).then(i=>{if(this._requestHandler=i.create(this),!this._requestHandler)throw new Error("No RequestHandler!")})}}class Ce{constructor(e,n,r,s){this.originalStart=e,this.originalLength=n,this.modifiedStart=r,this.modifiedLength=s}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}}function Xn(t,e){return(e<<5)-e+t|0}function Yi(t,e){e=Xn(149417,e);for(let n=0,r=t.length;n<r;n++)e=Xn(t.charCodeAt(n),e);return e}class Qn{constructor(e){this.source=e}getElements(){const e=this.source,n=new Int32Array(e.length);for(let r=0,s=e.length;r<s;r++)n[r]=e.charCodeAt(r);return n}}function Ji(t,e,n){return new Ae(new Qn(t),new Qn(e)).ComputeDiff(n).changes}class Ie{static Assert(e,n){if(!e)throw new Error(n)}}class Ve{static Copy(e,n,r,s,i){for(let o=0;o<i;o++)r[s+o]=e[n+o]}static Copy2(e,n,r,s,i){for(let o=0;o<i;o++)r[s+o]=e[n+o]}}class Yn{constructor(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}MarkNextChange(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new Ce(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824}AddOriginalElement(e,n){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++}AddModifiedElement(e,n){this.m_originalStart=Math.min(this.m_originalStart,e),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++}getChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes}getReverseChanges(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes}}class Ae{constructor(e,n,r=null){this.ContinueProcessingPredicate=r,this._originalSequence=e,this._modifiedSequence=n;const[s,i,o]=Ae._getElements(e),[l,u,c]=Ae._getElements(n);this._hasStrings=o&&c,this._originalStringElements=s,this._originalElementsOrHash=i,this._modifiedStringElements=l,this._modifiedElementsOrHash=u,this.m_forwardHistory=[],this.m_reverseHistory=[]}static _isStringArray(e){return e.length>0&&typeof e[0]=="string"}static _getElements(e){const n=e.getElements();if(Ae._isStringArray(n)){const r=new Int32Array(n.length);for(let s=0,i=n.length;s<i;s++)r[s]=Yi(n[s],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}ElementsAreEqual(e,n){return this._originalElementsOrHash[e]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[e]===this._modifiedStringElements[n]:!0}ElementsAreStrictEqual(e,n){if(!this.ElementsAreEqual(e,n))return!1;const r=Ae._getStrictElement(this._originalSequence,e),s=Ae._getStrictElement(this._modifiedSequence,n);return r===s}static _getStrictElement(e,n){return typeof e.getStrictElement=="function"?e.getStrictElement(n):null}OriginalElementsAreEqual(e,n){return this._originalElementsOrHash[e]!==this._originalElementsOrHash[n]?!1:this._hasStrings?this._originalStringElements[e]===this._originalStringElements[n]:!0}ModifiedElementsAreEqual(e,n){return this._modifiedElementsOrHash[e]!==this._modifiedElementsOrHash[n]?!1:this._hasStrings?this._modifiedStringElements[e]===this._modifiedStringElements[n]:!0}ComputeDiff(e){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,e)}_ComputeDiff(e,n,r,s,i){const o=[!1];let l=this.ComputeDiffRecursive(e,n,r,s,o);return i&&(l=this.PrettifyChanges(l)),{quitEarly:o[0],changes:l}}ComputeDiffRecursive(e,n,r,s,i){for(i[0]=!1;e<=n&&r<=s&&this.ElementsAreEqual(e,r);)e++,r++;for(;n>=e&&s>=r&&this.ElementsAreEqual(n,s);)n--,s--;if(e>n||r>s){let h;return r<=s?(Ie.Assert(e===n+1,"originalStart should only be one more than originalEnd"),h=[new Ce(e,0,r,s-r+1)]):e<=n?(Ie.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),h=[new Ce(e,n-e+1,r,0)]):(Ie.Assert(e===n+1,"originalStart should only be one more than originalEnd"),Ie.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}const o=[0],l=[0],u=this.ComputeRecursionPoint(e,n,r,s,o,l,i),c=o[0],f=l[0];if(u!==null)return u;if(!i[0]){const h=this.ComputeDiffRecursive(e,c,r,f,i);let m=[];return i[0]?m=[new Ce(c+1,n-(c+1)+1,f+1,s-(f+1)+1)]:m=this.ComputeDiffRecursive(c+1,n,f+1,s,i),this.ConcatenateChanges(h,m)}return[new Ce(e,n-e+1,r,s-r+1)]}WALKTRACE(e,n,r,s,i,o,l,u,c,f,h,m,d,g,p,x,L,N){let v=null,y=null,b=new Yn,w=n,C=r,R=d[0]-x[0]-s,B=-1073741824,Q=this.m_forwardHistory.length-1;do{const q=R+e;q===w||q<C&&c[q-1]<c[q+1]?(h=c[q+1],g=h-R-s,h<B&&b.MarkNextChange(),B=h,b.AddModifiedElement(h+1,g),R=q+1-e):(h=c[q-1]+1,g=h-R-s,h<B&&b.MarkNextChange(),B=h-1,b.AddOriginalElement(h,g+1),R=q-1-e),Q>=0&&(c=this.m_forwardHistory[Q],e=c[0],w=1,C=c.length-1)}while(--Q>=-1);if(v=b.getReverseChanges(),N[0]){let q=d[0]+1,F=x[0]+1;if(v!==null&&v.length>0){const S=v[v.length-1];q=Math.max(q,S.getOriginalEnd()),F=Math.max(F,S.getModifiedEnd())}y=[new Ce(q,m-q+1,F,p-F+1)]}else{b=new Yn,w=o,C=l,R=d[0]-x[0]-u,B=1073741824,Q=L?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{const q=R+i;q===w||q<C&&f[q-1]>=f[q+1]?(h=f[q+1]-1,g=h-R-u,h>B&&b.MarkNextChange(),B=h+1,b.AddOriginalElement(h+1,g+1),R=q+1-i):(h=f[q-1],g=h-R-u,h>B&&b.MarkNextChange(),B=h,b.AddModifiedElement(h+1,g+1),R=q-1-i),Q>=0&&(f=this.m_reverseHistory[Q],i=f[0],w=1,C=f.length-1)}while(--Q>=-1);y=b.getChanges()}return this.ConcatenateChanges(v,y)}ComputeRecursionPoint(e,n,r,s,i,o,l){let u=0,c=0,f=0,h=0,m=0,d=0;e--,r--,i[0]=0,o[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];const g=n-e+(s-r),p=g+1,x=new Int32Array(p),L=new Int32Array(p),N=s-r,v=n-e,y=e-r,b=n-s,C=(v-N)%2===0;x[N]=e,L[v]=n,l[0]=!1;for(let R=1;R<=g/2+1;R++){let B=0,Q=0;f=this.ClipDiagonalBound(N-R,R,N,p),h=this.ClipDiagonalBound(N+R,R,N,p);for(let F=f;F<=h;F+=2){F===f||F<h&&x[F-1]<x[F+1]?u=x[F+1]:u=x[F-1]+1,c=u-(F-N)-y;const S=u;for(;u<n&&c<s&&this.ElementsAreEqual(u+1,c+1);)u++,c++;if(x[F]=u,u+c>B+Q&&(B=u,Q=c),!C&&Math.abs(F-v)<=R-1&&u>=L[F])return i[0]=u,o[0]=c,S<=L[F]&&R<=1448?this.WALKTRACE(N,f,h,y,v,m,d,b,x,L,u,n,i,c,s,o,C,l):null}const q=(B-e+(Q-r)-R)/2;if(this.ContinueProcessingPredicate!==null&&!this.ContinueProcessingPredicate(B,q))return l[0]=!0,i[0]=B,o[0]=Q,q>0&&R<=1448?this.WALKTRACE(N,f,h,y,v,m,d,b,x,L,u,n,i,c,s,o,C,l):(e++,r++,[new Ce(e,n-e+1,r,s-r+1)]);m=this.ClipDiagonalBound(v-R,R,v,p),d=this.ClipDiagonalBound(v+R,R,v,p);for(let F=m;F<=d;F+=2){F===m||F<d&&L[F-1]>=L[F+1]?u=L[F+1]-1:u=L[F-1],c=u-(F-v)-b;const S=u;for(;u>e&&c>r&&this.ElementsAreEqual(u,c);)u--,c--;if(L[F]=u,C&&Math.abs(F-N)<=R&&u<=x[F])return i[0]=u,o[0]=c,S>=x[F]&&R<=1448?this.WALKTRACE(N,f,h,y,v,m,d,b,x,L,u,n,i,c,s,o,C,l):null}if(R<=1447){let F=new Int32Array(h-f+2);F[0]=N-f+1,Ve.Copy2(x,f,F,1,h-f+1),this.m_forwardHistory.push(F),F=new Int32Array(d-m+2),F[0]=v-m+1,Ve.Copy2(L,m,F,1,d-m+1),this.m_reverseHistory.push(F)}}return this.WALKTRACE(N,f,h,y,v,m,d,b,x,L,u,n,i,c,s,o,C,l)}PrettifyChanges(e){for(let n=0;n<e.length;n++){const r=e[n],s=n<e.length-1?e[n+1].originalStart:this._originalElementsOrHash.length,i=n<e.length-1?e[n+1].modifiedStart:this._modifiedElementsOrHash.length,o=r.originalLength>0,l=r.modifiedLength>0;for(;r.originalStart+r.originalLength<s&&r.modifiedStart+r.modifiedLength<i&&(!o||this.OriginalElementsAreEqual(r.originalStart,r.originalStart+r.originalLength))&&(!l||this.ModifiedElementsAreEqual(r.modifiedStart,r.modifiedStart+r.modifiedLength));){const c=this.ElementsAreStrictEqual(r.originalStart,r.modifiedStart);if(this.ElementsAreStrictEqual(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!c)break;r.originalStart++,r.modifiedStart++}const u=[null];if(n<e.length-1&&this.ChangesOverlap(e[n],e[n+1],u)){e[n]=u[0],e.splice(n+1,1),n--;continue}}for(let n=e.length-1;n>=0;n--){const r=e[n];let s=0,i=0;if(n>0){const h=e[n-1];s=h.originalStart+h.originalLength,i=h.modifiedStart+h.modifiedLength}const o=r.originalLength>0,l=r.modifiedLength>0;let u=0,c=this._boundaryScore(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let h=1;;h++){const m=r.originalStart-h,d=r.modifiedStart-h;if(m<s||d<i||o&&!this.OriginalElementsAreEqual(m,m+r.originalLength)||l&&!this.ModifiedElementsAreEqual(d,d+r.modifiedLength))break;const p=(m===s&&d===i?5:0)+this._boundaryScore(m,r.originalLength,d,r.modifiedLength);p>c&&(c=p,u=h)}r.originalStart-=u,r.modifiedStart-=u;const f=[null];if(n>0&&this.ChangesOverlap(e[n-1],e[n],f)){e[n-1]=f[0],e.splice(n,1),n++;continue}}if(this._hasStrings)for(let n=1,r=e.length;n<r;n++){const s=e[n-1],i=e[n],o=i.originalStart-s.originalStart-s.originalLength,l=s.originalStart,u=i.originalStart+i.originalLength,c=u-l,f=s.modifiedStart,h=i.modifiedStart+i.modifiedLength,m=h-f;if(o<5&&c<20&&m<20){const d=this._findBetterContiguousSequence(l,c,f,m,o);if(d){const[g,p]=d;(g!==s.originalStart+s.originalLength||p!==s.modifiedStart+s.modifiedLength)&&(s.originalLength=g-s.originalStart,s.modifiedLength=p-s.modifiedStart,i.originalStart=g+o,i.modifiedStart=p+o,i.originalLength=u-i.originalStart,i.modifiedLength=h-i.modifiedStart)}}}return e}_findBetterContiguousSequence(e,n,r,s,i){if(n<i||s<i)return null;const o=e+n-i+1,l=r+s-i+1;let u=0,c=0,f=0;for(let h=e;h<o;h++)for(let m=r;m<l;m++){const d=this._contiguousSequenceScore(h,m,i);d>0&&d>u&&(u=d,c=h,f=m)}return u>0?[c,f]:null}_contiguousSequenceScore(e,n,r){let s=0;for(let i=0;i<r;i++){if(!this.ElementsAreEqual(e+i,n+i))return 0;s+=this._originalStringElements[e+i].length}return s}_OriginalIsBoundary(e){return e<=0||e>=this._originalElementsOrHash.length-1?!0:this._hasStrings&&/^\\s*$/.test(this._originalStringElements[e])}_OriginalRegionIsBoundary(e,n){if(this._OriginalIsBoundary(e)||this._OriginalIsBoundary(e-1))return!0;if(n>0){const r=e+n;if(this._OriginalIsBoundary(r-1)||this._OriginalIsBoundary(r))return!0}return!1}_ModifiedIsBoundary(e){return e<=0||e>=this._modifiedElementsOrHash.length-1?!0:this._hasStrings&&/^\\s*$/.test(this._modifiedStringElements[e])}_ModifiedRegionIsBoundary(e,n){if(this._ModifiedIsBoundary(e)||this._ModifiedIsBoundary(e-1))return!0;if(n>0){const r=e+n;if(this._ModifiedIsBoundary(r-1)||this._ModifiedIsBoundary(r))return!0}return!1}_boundaryScore(e,n,r,s){const i=this._OriginalRegionIsBoundary(e,n)?1:0,o=this._ModifiedRegionIsBoundary(r,s)?1:0;return i+o}ConcatenateChanges(e,n){const r=[];if(e.length===0||n.length===0)return n.length>0?n:e;if(this.ChangesOverlap(e[e.length-1],n[0],r)){const s=new Array(e.length+n.length-1);return Ve.Copy(e,0,s,0,e.length-1),s[e.length-1]=r[0],Ve.Copy(n,1,s,e.length,n.length-1),s}else{const s=new Array(e.length+n.length);return Ve.Copy(e,0,s,0,e.length),Ve.Copy(n,0,s,e.length,n.length),s}}ChangesOverlap(e,n,r){if(Ie.Assert(e.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),Ie.Assert(e.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),e.originalStart+e.originalLength>=n.originalStart||e.modifiedStart+e.modifiedLength>=n.modifiedStart){const s=e.originalStart;let i=e.originalLength;const o=e.modifiedStart;let l=e.modifiedLength;return e.originalStart+e.originalLength>=n.originalStart&&(i=n.originalStart+n.originalLength-e.originalStart),e.modifiedStart+e.modifiedLength>=n.modifiedStart&&(l=n.modifiedStart+n.modifiedLength-e.modifiedStart),r[0]=new Ce(s,i,o,l),!0}else return r[0]=null,!1}ClipDiagonalBound(e,n,r,s){if(e>=0&&e<s)return e;const i=r,o=s-r-1,l=n%2===0;if(e<0){const u=i%2===0;return l===u?0:1}else{const u=o%2===0;return l===u?s-1:s-2}}}class U{constructor(e,n){this.lineNumber=e,this.column=n}with(e=this.lineNumber,n=this.column){return e===this.lineNumber&&n===this.column?this:new U(e,n)}delta(e=0,n=0){return this.with(this.lineNumber+e,this.column+n)}equals(e){return U.equals(this,e)}static equals(e,n){return!e&&!n?!0:!!e&&!!n&&e.lineNumber===n.lineNumber&&e.column===n.column}isBefore(e){return U.isBefore(this,e)}static isBefore(e,n){return e.lineNumber<n.lineNumber?!0:n.lineNumber<e.lineNumber?!1:e.column<n.column}isBeforeOrEqual(e){return U.isBeforeOrEqual(this,e)}static isBeforeOrEqual(e,n){return e.lineNumber<n.lineNumber?!0:n.lineNumber<e.lineNumber?!1:e.column<=n.column}static compare(e,n){const r=e.lineNumber|0,s=n.lineNumber|0;if(r===s){const i=e.column|0,o=n.column|0;return i-o}return r-s}clone(){return new U(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(e){return new U(e.lineNumber,e.column)}static isIPosition(e){return e&&typeof e.lineNumber=="number"&&typeof e.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}}class k{constructor(e,n,r,s){e>r||e===r&&n>s?(this.startLineNumber=r,this.startColumn=s,this.endLineNumber=e,this.endColumn=n):(this.startLineNumber=e,this.startColumn=n,this.endLineNumber=r,this.endColumn=s)}isEmpty(){return k.isEmpty(this)}static isEmpty(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn}containsPosition(e){return k.containsPosition(this,e)}static containsPosition(e,n){return!(n.lineNumber<e.startLineNumber||n.lineNumber>e.endLineNumber||n.lineNumber===e.startLineNumber&&n.column<e.startColumn||n.lineNumber===e.endLineNumber&&n.column>e.endColumn)}static strictContainsPosition(e,n){return!(n.lineNumber<e.startLineNumber||n.lineNumber>e.endLineNumber||n.lineNumber===e.startLineNumber&&n.column<=e.startColumn||n.lineNumber===e.endLineNumber&&n.column>=e.endColumn)}containsRange(e){return k.containsRange(this,e)}static containsRange(e,n){return!(n.startLineNumber<e.startLineNumber||n.endLineNumber<e.startLineNumber||n.startLineNumber>e.endLineNumber||n.endLineNumber>e.endLineNumber||n.startLineNumber===e.startLineNumber&&n.startColumn<e.startColumn||n.endLineNumber===e.endLineNumber&&n.endColumn>e.endColumn)}strictContainsRange(e){return k.strictContainsRange(this,e)}static strictContainsRange(e,n){return!(n.startLineNumber<e.startLineNumber||n.endLineNumber<e.startLineNumber||n.startLineNumber>e.endLineNumber||n.endLineNumber>e.endLineNumber||n.startLineNumber===e.startLineNumber&&n.startColumn<=e.startColumn||n.endLineNumber===e.endLineNumber&&n.endColumn>=e.endColumn)}plusRange(e){return k.plusRange(this,e)}static plusRange(e,n){let r,s,i,o;return n.startLineNumber<e.startLineNumber?(r=n.startLineNumber,s=n.startColumn):n.startLineNumber===e.startLineNumber?(r=n.startLineNumber,s=Math.min(n.startColumn,e.startColumn)):(r=e.startLineNumber,s=e.startColumn),n.endLineNumber>e.endLineNumber?(i=n.endLineNumber,o=n.endColumn):n.endLineNumber===e.endLineNumber?(i=n.endLineNumber,o=Math.max(n.endColumn,e.endColumn)):(i=e.endLineNumber,o=e.endColumn),new k(r,s,i,o)}intersectRanges(e){return k.intersectRanges(this,e)}static intersectRanges(e,n){let r=e.startLineNumber,s=e.startColumn,i=e.endLineNumber,o=e.endColumn;const l=n.startLineNumber,u=n.startColumn,c=n.endLineNumber,f=n.endColumn;return r<l?(r=l,s=u):r===l&&(s=Math.max(s,u)),i>c?(i=c,o=f):i===c&&(o=Math.min(o,f)),r>i||r===i&&s>o?null:new k(r,s,i,o)}equalsRange(e){return k.equalsRange(this,e)}static equalsRange(e,n){return!e&&!n?!0:!!e&&!!n&&e.startLineNumber===n.startLineNumber&&e.startColumn===n.startColumn&&e.endLineNumber===n.endLineNumber&&e.endColumn===n.endColumn}getEndPosition(){return k.getEndPosition(this)}static getEndPosition(e){return new U(e.endLineNumber,e.endColumn)}getStartPosition(){return k.getStartPosition(this)}static getStartPosition(e){return new U(e.startLineNumber,e.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(e,n){return new k(this.startLineNumber,this.startColumn,e,n)}setStartPosition(e,n){return new k(e,n,this.endLineNumber,this.endColumn)}collapseToStart(){return k.collapseToStart(this)}static collapseToStart(e){return new k(e.startLineNumber,e.startColumn,e.startLineNumber,e.startColumn)}collapseToEnd(){return k.collapseToEnd(this)}static collapseToEnd(e){return new k(e.endLineNumber,e.endColumn,e.endLineNumber,e.endColumn)}delta(e){return new k(this.startLineNumber+e,this.startColumn,this.endLineNumber+e,this.endColumn)}static fromPositions(e,n=e){return new k(e.lineNumber,e.column,n.lineNumber,n.column)}static lift(e){return e?new k(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):null}static isIRange(e){return e&&typeof e.startLineNumber=="number"&&typeof e.startColumn=="number"&&typeof e.endLineNumber=="number"&&typeof e.endColumn=="number"}static areIntersectingOrTouching(e,n){return!(e.endLineNumber<n.startLineNumber||e.endLineNumber===n.startLineNumber&&e.endColumn<n.startColumn||n.endLineNumber<e.startLineNumber||n.endLineNumber===e.startLineNumber&&n.endColumn<e.startColumn)}static areIntersecting(e,n){return!(e.endLineNumber<n.startLineNumber||e.endLineNumber===n.startLineNumber&&e.endColumn<=n.startColumn||n.endLineNumber<e.startLineNumber||n.endLineNumber===e.startLineNumber&&n.endColumn<=e.startColumn)}static compareRangesUsingStarts(e,n){if(e&&n){const i=e.startLineNumber|0,o=n.startLineNumber|0;if(i===o){const l=e.startColumn|0,u=n.startColumn|0;if(l===u){const c=e.endLineNumber|0,f=n.endLineNumber|0;if(c===f){const h=e.endColumn|0,m=n.endColumn|0;return h-m}return c-f}return l-u}return i-o}return(e?1:0)-(n?1:0)}static compareRangesUsingEnds(e,n){return e.endLineNumber===n.endLineNumber?e.endColumn===n.endColumn?e.startLineNumber===n.startLineNumber?e.startColumn-n.startColumn:e.startLineNumber-n.startLineNumber:e.endColumn-n.endColumn:e.endLineNumber-n.endLineNumber}static spansMultipleLines(e){return e.endLineNumber>e.startLineNumber}toJSON(){return this}}function Jn(t){return t<0?0:t>255?255:t|0}function Be(t){return t<0?0:t>4294967295?4294967295:t|0}class nn{constructor(e){const n=Jn(e);this._defaultValue=n,this._asciiMap=nn._createAsciiMap(n),this._map=new Map}static _createAsciiMap(e){const n=new Uint8Array(256);return n.fill(e),n}set(e,n){const r=Jn(n);e>=0&&e<256?this._asciiMap[e]=r:this._map.set(e,r)}get(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue}clear(){this._asciiMap.fill(this._defaultValue),this._map.clear()}}class Zi{constructor(e,n,r){const s=new Uint8Array(e*n);for(let i=0,o=e*n;i<o;i++)s[i]=r;this._data=s,this.rows=e,this.cols=n}get(e,n){return this._data[e*this.cols+n]}set(e,n,r){this._data[e*this.cols+n]=r}}class Ki{constructor(e){let n=0,r=0;for(let i=0,o=e.length;i<o;i++){const[l,u,c]=e[i];u>n&&(n=u),l>r&&(r=l),c>r&&(r=c)}n++,r++;const s=new Zi(r,n,0);for(let i=0,o=e.length;i<o;i++){const[l,u,c]=e[i];s.set(l,u,c)}this._states=s,this._maxCharCode=n}nextState(e,n){return n<0||n>=this._maxCharCode?0:this._states.get(e,n)}}let rn=null;function ea(){return rn===null&&(rn=new Ki([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),rn}let tt=null;function ta(){if(tt===null){tt=new nn(0);const t=` 	<>\'"、。｡､，．：；‘〈「『〔（［｛｢｣｝］）〕』」〉’｀～…`;for(let n=0;n<t.length;n++)tt.set(t.charCodeAt(n),1);const e=".,;:";for(let n=0;n<e.length;n++)tt.set(e.charCodeAt(n),2)}return tt}class xt{static _createLink(e,n,r,s,i){let o=i-1;do{const l=n.charCodeAt(o);if(e.get(l)!==2)break;o--}while(o>s);if(s>0){const l=n.charCodeAt(s-1),u=n.charCodeAt(o);(l===40&&u===41||l===91&&u===93||l===123&&u===125)&&o--}return{range:{startLineNumber:r,startColumn:s+1,endLineNumber:r,endColumn:o+2},url:n.substring(s,o+1)}}static computeLinks(e,n=ea()){const r=ta(),s=[];for(let i=1,o=e.getLineCount();i<=o;i++){const l=e.getLineContent(i),u=l.length;let c=0,f=0,h=0,m=1,d=!1,g=!1,p=!1,x=!1;for(;c<u;){let L=!1;const N=l.charCodeAt(c);if(m===13){let v;switch(N){case 40:d=!0,v=0;break;case 41:v=d?0:1;break;case 91:p=!0,g=!0,v=0;break;case 93:p=!1,v=g?0:1;break;case 123:x=!0,v=0;break;case 125:v=x?0:1;break;case 39:case 34:case 96:h===N?v=1:h===39||h===34||h===96?v=0:v=1;break;case 42:v=h===42?1:0;break;case 124:v=h===124?1:0;break;case 32:v=p?0:1;break;default:v=r.get(N)}v===1&&(s.push(xt._createLink(r,l,i,f,c)),L=!0)}else if(m===12){let v;N===91?(g=!0,v=0):v=r.get(N),v===1?L=!0:m=13}else m=n.nextState(m,N),m===0&&(L=!0);L&&(m=1,d=!1,g=!1,x=!1,f=c+1,h=N),c++}m===13&&s.push(xt._createLink(r,l,i,f,u))}return s}}function na(t){return!t||typeof t.getLineCount!="function"||typeof t.getLineContent!="function"?[]:xt.computeLinks(t)}const Ft=class Ft{constructor(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}navigateValueSet(e,n,r,s,i){if(e&&n){const o=this.doNavigateValueSet(n,i);if(o)return{range:e,value:o}}if(r&&s){const o=this.doNavigateValueSet(s,i);if(o)return{range:r,value:o}}return null}doNavigateValueSet(e,n){const r=this.numberReplace(e,n);return r!==null?r:this.textReplace(e,n)}numberReplace(e,n){const r=Math.pow(10,e.length-(e.lastIndexOf(".")+1));let s=Number(e);const i=parseFloat(e);return!isNaN(s)&&!isNaN(i)&&s===i?s===0&&!n?null:(s=Math.floor(s*r),s+=n?r:-r,String(s/r)):null}textReplace(e,n){return this.valueSetsReplace(this._defaultValueSet,e,n)}valueSetsReplace(e,n,r){let s=null;for(let i=0,o=e.length;s===null&&i<o;i++)s=this.valueSetReplace(e[i],n,r);return s}valueSetReplace(e,n,r){let s=e.indexOf(n);return s>=0?(s+=r?1:-1,s<0?s=e.length-1:s%=e.length,e[s]):null}};Ft.INSTANCE=new Ft;let sn=Ft;const Zn=Object.freeze(function(t,e){const n=setTimeout(t.bind(e),0);return{dispose(){clearTimeout(n)}}});var _t;(function(t){function e(n){return n===t.None||n===t.Cancelled||n instanceof wt?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}t.isCancellationToken=e,t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:dt.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Zn})})(_t||(_t={}));class wt{constructor(){this._isCancelled=!1,this._emitter=null}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?Zn:(this._emitter||(this._emitter=new oe),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=null)}}class ra{constructor(e){this._token=void 0,this._parentListener=void 0,this._parentListener=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this._token||(this._token=new wt),this._token}cancel(){this._token?this._token instanceof wt&&this._token.cancel():this._token=_t.Cancelled}dispose(e=!1){e&&this.cancel(),this._parentListener?.dispose(),this._token?this._token instanceof wt&&this._token.dispose():this._token=_t.None}}class an{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,n){this._keyCodeToStr[e]=n,this._strToKeyCode[n.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}}const Lt=new an,on=new an,ln=new an,sa=new Array(230),ia=Object.create(null),aa=Object.create(null);(function(){const e=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN","",""],[1,1,"Hyper",0,"",0,"","",""],[1,2,"Super",0,"",0,"","",""],[1,3,"Fn",0,"",0,"","",""],[1,4,"FnLock",0,"",0,"","",""],[1,5,"Suspend",0,"",0,"","",""],[1,6,"Resume",0,"",0,"","",""],[1,7,"Turbo",0,"",0,"","",""],[1,8,"Sleep",0,"",0,"VK_SLEEP","",""],[1,9,"WakeUp",0,"",0,"","",""],[0,10,"KeyA",31,"A",65,"VK_A","",""],[0,11,"KeyB",32,"B",66,"VK_B","",""],[0,12,"KeyC",33,"C",67,"VK_C","",""],[0,13,"KeyD",34,"D",68,"VK_D","",""],[0,14,"KeyE",35,"E",69,"VK_E","",""],[0,15,"KeyF",36,"F",70,"VK_F","",""],[0,16,"KeyG",37,"G",71,"VK_G","",""],[0,17,"KeyH",38,"H",72,"VK_H","",""],[0,18,"KeyI",39,"I",73,"VK_I","",""],[0,19,"KeyJ",40,"J",74,"VK_J","",""],[0,20,"KeyK",41,"K",75,"VK_K","",""],[0,21,"KeyL",42,"L",76,"VK_L","",""],[0,22,"KeyM",43,"M",77,"VK_M","",""],[0,23,"KeyN",44,"N",78,"VK_N","",""],[0,24,"KeyO",45,"O",79,"VK_O","",""],[0,25,"KeyP",46,"P",80,"VK_P","",""],[0,26,"KeyQ",47,"Q",81,"VK_Q","",""],[0,27,"KeyR",48,"R",82,"VK_R","",""],[0,28,"KeyS",49,"S",83,"VK_S","",""],[0,29,"KeyT",50,"T",84,"VK_T","",""],[0,30,"KeyU",51,"U",85,"VK_U","",""],[0,31,"KeyV",52,"V",86,"VK_V","",""],[0,32,"KeyW",53,"W",87,"VK_W","",""],[0,33,"KeyX",54,"X",88,"VK_X","",""],[0,34,"KeyY",55,"Y",89,"VK_Y","",""],[0,35,"KeyZ",56,"Z",90,"VK_Z","",""],[0,36,"Digit1",22,"1",49,"VK_1","",""],[0,37,"Digit2",23,"2",50,"VK_2","",""],[0,38,"Digit3",24,"3",51,"VK_3","",""],[0,39,"Digit4",25,"4",52,"VK_4","",""],[0,40,"Digit5",26,"5",53,"VK_5","",""],[0,41,"Digit6",27,"6",54,"VK_6","",""],[0,42,"Digit7",28,"7",55,"VK_7","",""],[0,43,"Digit8",29,"8",56,"VK_8","",""],[0,44,"Digit9",30,"9",57,"VK_9","",""],[0,45,"Digit0",21,"0",48,"VK_0","",""],[1,46,"Enter",3,"Enter",13,"VK_RETURN","",""],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE","",""],[1,48,"Backspace",1,"Backspace",8,"VK_BACK","",""],[1,49,"Tab",2,"Tab",9,"VK_TAB","",""],[1,50,"Space",10,"Space",32,"VK_SPACE","",""],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\\\",220,"VK_OEM_5","\\\\","OEM_5"],[0,56,"IntlHash",0,"",0,"","",""],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"\'",222,"VK_OEM_7","\'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL","",""],[1,64,"F1",59,"F1",112,"VK_F1","",""],[1,65,"F2",60,"F2",113,"VK_F2","",""],[1,66,"F3",61,"F3",114,"VK_F3","",""],[1,67,"F4",62,"F4",115,"VK_F4","",""],[1,68,"F5",63,"F5",116,"VK_F5","",""],[1,69,"F6",64,"F6",117,"VK_F6","",""],[1,70,"F7",65,"F7",118,"VK_F7","",""],[1,71,"F8",66,"F8",119,"VK_F8","",""],[1,72,"F9",67,"F9",120,"VK_F9","",""],[1,73,"F10",68,"F10",121,"VK_F10","",""],[1,74,"F11",69,"F11",122,"VK_F11","",""],[1,75,"F12",70,"F12",123,"VK_F12","",""],[1,76,"PrintScreen",0,"",0,"","",""],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL","",""],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE","",""],[1,79,"Insert",19,"Insert",45,"VK_INSERT","",""],[1,80,"Home",14,"Home",36,"VK_HOME","",""],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR","",""],[1,82,"Delete",20,"Delete",46,"VK_DELETE","",""],[1,83,"End",13,"End",35,"VK_END","",""],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT","",""],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",""],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",""],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",""],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",""],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK","",""],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE","",""],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY","",""],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT","",""],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD","",""],[1,94,"NumpadEnter",3,"",0,"","",""],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1","",""],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2","",""],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3","",""],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4","",""],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5","",""],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6","",""],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7","",""],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8","",""],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9","",""],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0","",""],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL","",""],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102","",""],[1,107,"ContextMenu",58,"ContextMenu",93,"","",""],[1,108,"Power",0,"",0,"","",""],[1,109,"NumpadEqual",0,"",0,"","",""],[1,110,"F13",71,"F13",124,"VK_F13","",""],[1,111,"F14",72,"F14",125,"VK_F14","",""],[1,112,"F15",73,"F15",126,"VK_F15","",""],[1,113,"F16",74,"F16",127,"VK_F16","",""],[1,114,"F17",75,"F17",128,"VK_F17","",""],[1,115,"F18",76,"F18",129,"VK_F18","",""],[1,116,"F19",77,"F19",130,"VK_F19","",""],[1,117,"F20",78,"F20",131,"VK_F20","",""],[1,118,"F21",79,"F21",132,"VK_F21","",""],[1,119,"F22",80,"F22",133,"VK_F22","",""],[1,120,"F23",81,"F23",134,"VK_F23","",""],[1,121,"F24",82,"F24",135,"VK_F24","",""],[1,122,"Open",0,"",0,"","",""],[1,123,"Help",0,"",0,"","",""],[1,124,"Select",0,"",0,"","",""],[1,125,"Again",0,"",0,"","",""],[1,126,"Undo",0,"",0,"","",""],[1,127,"Cut",0,"",0,"","",""],[1,128,"Copy",0,"",0,"","",""],[1,129,"Paste",0,"",0,"","",""],[1,130,"Find",0,"",0,"","",""],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE","",""],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP","",""],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN","",""],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR","",""],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1","",""],[1,136,"KanaMode",0,"",0,"","",""],[0,137,"IntlYen",0,"",0,"","",""],[1,138,"Convert",0,"",0,"","",""],[1,139,"NonConvert",0,"",0,"","",""],[1,140,"Lang1",0,"",0,"","",""],[1,141,"Lang2",0,"",0,"","",""],[1,142,"Lang3",0,"",0,"","",""],[1,143,"Lang4",0,"",0,"","",""],[1,144,"Lang5",0,"",0,"","",""],[1,145,"Abort",0,"",0,"","",""],[1,146,"Props",0,"",0,"","",""],[1,147,"NumpadParenLeft",0,"",0,"","",""],[1,148,"NumpadParenRight",0,"",0,"","",""],[1,149,"NumpadBackspace",0,"",0,"","",""],[1,150,"NumpadMemoryStore",0,"",0,"","",""],[1,151,"NumpadMemoryRecall",0,"",0,"","",""],[1,152,"NumpadMemoryClear",0,"",0,"","",""],[1,153,"NumpadMemoryAdd",0,"",0,"","",""],[1,154,"NumpadMemorySubtract",0,"",0,"","",""],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR","",""],[1,156,"NumpadClearEntry",0,"",0,"","",""],[1,0,"",5,"Ctrl",17,"VK_CONTROL","",""],[1,0,"",4,"Shift",16,"VK_SHIFT","",""],[1,0,"",6,"Alt",18,"VK_MENU","",""],[1,0,"",57,"Meta",91,"VK_COMMAND","",""],[1,157,"ControlLeft",5,"",0,"VK_LCONTROL","",""],[1,158,"ShiftLeft",4,"",0,"VK_LSHIFT","",""],[1,159,"AltLeft",6,"",0,"VK_LMENU","",""],[1,160,"MetaLeft",57,"",0,"VK_LWIN","",""],[1,161,"ControlRight",5,"",0,"VK_RCONTROL","",""],[1,162,"ShiftRight",4,"",0,"VK_RSHIFT","",""],[1,163,"AltRight",6,"",0,"VK_RMENU","",""],[1,164,"MetaRight",57,"",0,"VK_RWIN","",""],[1,165,"BrightnessUp",0,"",0,"","",""],[1,166,"BrightnessDown",0,"",0,"","",""],[1,167,"MediaPlay",0,"",0,"","",""],[1,168,"MediaRecord",0,"",0,"","",""],[1,169,"MediaFastForward",0,"",0,"","",""],[1,170,"MediaRewind",0,"",0,"","",""],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK","",""],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK","",""],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP","",""],[1,174,"Eject",0,"",0,"","",""],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE","",""],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT","",""],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL","",""],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2","",""],[1,179,"LaunchApp1",0,"",0,"VK_MEDIA_LAUNCH_APP1","",""],[1,180,"SelectTask",0,"",0,"","",""],[1,181,"LaunchScreenSaver",0,"",0,"","",""],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH","",""],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME","",""],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK","",""],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD","",""],[1,186,"BrowserStop",0,"",0,"VK_BROWSER_STOP","",""],[1,187,"BrowserRefresh",0,"",0,"VK_BROWSER_REFRESH","",""],[1,188,"BrowserFavorites",0,"",0,"VK_BROWSER_FAVORITES","",""],[1,189,"ZoomToggle",0,"",0,"","",""],[1,190,"MailReply",0,"",0,"","",""],[1,191,"MailForward",0,"",0,"","",""],[1,192,"MailSend",0,"",0,"","",""],[1,0,"",114,"KeyInComposition",229,"","",""],[1,0,"",116,"ABNT_C2",194,"VK_ABNT_C2","",""],[1,0,"",96,"OEM_8",223,"VK_OEM_8","",""],[1,0,"",0,"",0,"VK_KANA","",""],[1,0,"",0,"",0,"VK_HANGUL","",""],[1,0,"",0,"",0,"VK_JUNJA","",""],[1,0,"",0,"",0,"VK_FINAL","",""],[1,0,"",0,"",0,"VK_HANJA","",""],[1,0,"",0,"",0,"VK_KANJI","",""],[1,0,"",0,"",0,"VK_CONVERT","",""],[1,0,"",0,"",0,"VK_NONCONVERT","",""],[1,0,"",0,"",0,"VK_ACCEPT","",""],[1,0,"",0,"",0,"VK_MODECHANGE","",""],[1,0,"",0,"",0,"VK_SELECT","",""],[1,0,"",0,"",0,"VK_PRINT","",""],[1,0,"",0,"",0,"VK_EXECUTE","",""],[1,0,"",0,"",0,"VK_SNAPSHOT","",""],[1,0,"",0,"",0,"VK_HELP","",""],[1,0,"",0,"",0,"VK_APPS","",""],[1,0,"",0,"",0,"VK_PROCESSKEY","",""],[1,0,"",0,"",0,"VK_PACKET","",""],[1,0,"",0,"",0,"VK_DBE_SBCSCHAR","",""],[1,0,"",0,"",0,"VK_DBE_DBCSCHAR","",""],[1,0,"",0,"",0,"VK_ATTN","",""],[1,0,"",0,"",0,"VK_CRSEL","",""],[1,0,"",0,"",0,"VK_EXSEL","",""],[1,0,"",0,"",0,"VK_EREOF","",""],[1,0,"",0,"",0,"VK_PLAY","",""],[1,0,"",0,"",0,"VK_ZOOM","",""],[1,0,"",0,"",0,"VK_NONAME","",""],[1,0,"",0,"",0,"VK_PA1","",""],[1,0,"",0,"",0,"VK_OEM_CLEAR","",""]],n=[],r=[];for(const s of e){const[i,o,l,u,c,f,h,m,d]=s;if(r[o]||(r[o]=!0,ia[l]=o,aa[l.toLowerCase()]=o),!n[u]){if(n[u]=!0,!c)throw new Error(`String representation missing for key code ${u} around scan code ${l}`);Lt.define(u,c),on.define(u,m||c),ln.define(u,d||m||c)}f&&(sa[f]=u)}})();var Kn;(function(t){function e(l){return Lt.keyCodeToStr(l)}t.toString=e;function n(l){return Lt.strToKeyCode(l)}t.fromString=n;function r(l){return on.keyCodeToStr(l)}t.toUserSettingsUS=r;function s(l){return ln.keyCodeToStr(l)}t.toUserSettingsGeneral=s;function i(l){return on.strToKeyCode(l)||ln.strToKeyCode(l)}t.fromUserSettings=i;function o(l){if(l>=98&&l<=113)return null;switch(l){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Lt.keyCodeToStr(l)}t.toElectronAccelerator=o})(Kn||(Kn={}));function oa(t,e){const n=(e&65535)<<16>>>0;return(t|n)>>>0}class ae extends k{constructor(e,n,r,s){super(e,n,r,s),this.selectionStartLineNumber=e,this.selectionStartColumn=n,this.positionLineNumber=r,this.positionColumn=s}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(e){return ae.selectionsEqual(this,e)}static selectionsEqual(e,n){return e.selectionStartLineNumber===n.selectionStartLineNumber&&e.selectionStartColumn===n.selectionStartColumn&&e.positionLineNumber===n.positionLineNumber&&e.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(e,n){return this.getDirection()===0?new ae(this.startLineNumber,this.startColumn,e,n):new ae(e,n,this.startLineNumber,this.startColumn)}getPosition(){return new U(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new U(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(e,n){return this.getDirection()===0?new ae(e,n,this.endLineNumber,this.endColumn):new ae(this.endLineNumber,this.endColumn,e,n)}static fromPositions(e,n=e){return new ae(e.lineNumber,e.column,n.lineNumber,n.column)}static fromRange(e,n){return n===0?new ae(e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn):new ae(e.endLineNumber,e.endColumn,e.startLineNumber,e.startColumn)}static liftSelection(e){return new ae(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)}static selectionsArrEqual(e,n){if(e&&!n||!e&&n)return!1;if(!e&&!n)return!0;if(e.length!==n.length)return!1;for(let r=0,s=e.length;r<s;r++)if(!this.selectionsEqual(e[r],n[r]))return!1;return!0}static isISelection(e){return e&&typeof e.selectionStartLineNumber=="number"&&typeof e.selectionStartColumn=="number"&&typeof e.positionLineNumber=="number"&&typeof e.positionColumn=="number"}static createWithDirection(e,n,r,s,i){return i===0?new ae(e,n,r,s):new ae(r,s,e,n)}}function la(t){return typeof t=="string"}const er=Object.create(null);function a(t,e){if(la(e)){const n=er[e];if(n===void 0)throw new Error(`${t} references an unknown codicon: ${e}`);e=n}return er[t]=e,{id:t}}const ua={add:a("add",6e4),plus:a("plus",6e4),gistNew:a("gist-new",6e4),repoCreate:a("repo-create",6e4),lightbulb:a("lightbulb",60001),lightBulb:a("light-bulb",60001),repo:a("repo",60002),repoDelete:a("repo-delete",60002),gistFork:a("gist-fork",60003),repoForked:a("repo-forked",60003),gitPullRequest:a("git-pull-request",60004),gitPullRequestAbandoned:a("git-pull-request-abandoned",60004),recordKeys:a("record-keys",60005),keyboard:a("keyboard",60005),tag:a("tag",60006),gitPullRequestLabel:a("git-pull-request-label",60006),tagAdd:a("tag-add",60006),tagRemove:a("tag-remove",60006),person:a("person",60007),personFollow:a("person-follow",60007),personOutline:a("person-outline",60007),personFilled:a("person-filled",60007),gitBranch:a("git-branch",60008),gitBranchCreate:a("git-branch-create",60008),gitBranchDelete:a("git-branch-delete",60008),sourceControl:a("source-control",60008),mirror:a("mirror",60009),mirrorPublic:a("mirror-public",60009),star:a("star",60010),starAdd:a("star-add",60010),starDelete:a("star-delete",60010),starEmpty:a("star-empty",60010),comment:a("comment",60011),commentAdd:a("comment-add",60011),alert:a("alert",60012),warning:a("warning",60012),search:a("search",60013),searchSave:a("search-save",60013),logOut:a("log-out",60014),signOut:a("sign-out",60014),logIn:a("log-in",60015),signIn:a("sign-in",60015),eye:a("eye",60016),eyeUnwatch:a("eye-unwatch",60016),eyeWatch:a("eye-watch",60016),circleFilled:a("circle-filled",60017),primitiveDot:a("primitive-dot",60017),closeDirty:a("close-dirty",60017),debugBreakpoint:a("debug-breakpoint",60017),debugBreakpointDisabled:a("debug-breakpoint-disabled",60017),debugHint:a("debug-hint",60017),terminalDecorationSuccess:a("terminal-decoration-success",60017),primitiveSquare:a("primitive-square",60018),edit:a("edit",60019),pencil:a("pencil",60019),info:a("info",60020),issueOpened:a("issue-opened",60020),gistPrivate:a("gist-private",60021),gitForkPrivate:a("git-fork-private",60021),lock:a("lock",60021),mirrorPrivate:a("mirror-private",60021),close:a("close",60022),removeClose:a("remove-close",60022),x:a("x",60022),repoSync:a("repo-sync",60023),sync:a("sync",60023),clone:a("clone",60024),desktopDownload:a("desktop-download",60024),beaker:a("beaker",60025),microscope:a("microscope",60025),vm:a("vm",60026),deviceDesktop:a("device-desktop",60026),file:a("file",60027),fileText:a("file-text",60027),more:a("more",60028),ellipsis:a("ellipsis",60028),kebabHorizontal:a("kebab-horizontal",60028),mailReply:a("mail-reply",60029),reply:a("reply",60029),organization:a("organization",60030),organizationFilled:a("organization-filled",60030),organizationOutline:a("organization-outline",60030),newFile:a("new-file",60031),fileAdd:a("file-add",60031),newFolder:a("new-folder",60032),fileDirectoryCreate:a("file-directory-create",60032),trash:a("trash",60033),trashcan:a("trashcan",60033),history:a("history",60034),clock:a("clock",60034),folder:a("folder",60035),fileDirectory:a("file-directory",60035),symbolFolder:a("symbol-folder",60035),logoGithub:a("logo-github",60036),markGithub:a("mark-github",60036),github:a("github",60036),terminal:a("terminal",60037),console:a("console",60037),repl:a("repl",60037),zap:a("zap",60038),symbolEvent:a("symbol-event",60038),error:a("error",60039),stop:a("stop",60039),variable:a("variable",60040),symbolVariable:a("symbol-variable",60040),array:a("array",60042),symbolArray:a("symbol-array",60042),symbolModule:a("symbol-module",60043),symbolPackage:a("symbol-package",60043),symbolNamespace:a("symbol-namespace",60043),symbolObject:a("symbol-object",60043),symbolMethod:a("symbol-method",60044),symbolFunction:a("symbol-function",60044),symbolConstructor:a("symbol-constructor",60044),symbolBoolean:a("symbol-boolean",60047),symbolNull:a("symbol-null",60047),symbolNumeric:a("symbol-numeric",60048),symbolNumber:a("symbol-number",60048),symbolStructure:a("symbol-structure",60049),symbolStruct:a("symbol-struct",60049),symbolParameter:a("symbol-parameter",60050),symbolTypeParameter:a("symbol-type-parameter",60050),symbolKey:a("symbol-key",60051),symbolText:a("symbol-text",60051),symbolReference:a("symbol-reference",60052),goToFile:a("go-to-file",60052),symbolEnum:a("symbol-enum",60053),symbolValue:a("symbol-value",60053),symbolRuler:a("symbol-ruler",60054),symbolUnit:a("symbol-unit",60054),activateBreakpoints:a("activate-breakpoints",60055),archive:a("archive",60056),arrowBoth:a("arrow-both",60057),arrowDown:a("arrow-down",60058),arrowLeft:a("arrow-left",60059),arrowRight:a("arrow-right",60060),arrowSmallDown:a("arrow-small-down",60061),arrowSmallLeft:a("arrow-small-left",60062),arrowSmallRight:a("arrow-small-right",60063),arrowSmallUp:a("arrow-small-up",60064),arrowUp:a("arrow-up",60065),bell:a("bell",60066),bold:a("bold",60067),book:a("book",60068),bookmark:a("bookmark",60069),debugBreakpointConditionalUnverified:a("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:a("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:a("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:a("debug-breakpoint-data-unverified",60072),debugBreakpointData:a("debug-breakpoint-data",60073),debugBreakpointDataDisabled:a("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:a("debug-breakpoint-log-unverified",60074),debugBreakpointLog:a("debug-breakpoint-log",60075),debugBreakpointLogDisabled:a("debug-breakpoint-log-disabled",60075),briefcase:a("briefcase",60076),broadcast:a("broadcast",60077),browser:a("browser",60078),bug:a("bug",60079),calendar:a("calendar",60080),caseSensitive:a("case-sensitive",60081),check:a("check",60082),checklist:a("checklist",60083),chevronDown:a("chevron-down",60084),chevronLeft:a("chevron-left",60085),chevronRight:a("chevron-right",60086),chevronUp:a("chevron-up",60087),chromeClose:a("chrome-close",60088),chromeMaximize:a("chrome-maximize",60089),chromeMinimize:a("chrome-minimize",60090),chromeRestore:a("chrome-restore",60091),circleOutline:a("circle-outline",60092),circle:a("circle",60092),debugBreakpointUnverified:a("debug-breakpoint-unverified",60092),terminalDecorationIncomplete:a("terminal-decoration-incomplete",60092),circleSlash:a("circle-slash",60093),circuitBoard:a("circuit-board",60094),clearAll:a("clear-all",60095),clippy:a("clippy",60096),closeAll:a("close-all",60097),cloudDownload:a("cloud-download",60098),cloudUpload:a("cloud-upload",60099),code:a("code",60100),collapseAll:a("collapse-all",60101),colorMode:a("color-mode",60102),commentDiscussion:a("comment-discussion",60103),creditCard:a("credit-card",60105),dash:a("dash",60108),dashboard:a("dashboard",60109),database:a("database",60110),debugContinue:a("debug-continue",60111),debugDisconnect:a("debug-disconnect",60112),debugPause:a("debug-pause",60113),debugRestart:a("debug-restart",60114),debugStart:a("debug-start",60115),debugStepInto:a("debug-step-into",60116),debugStepOut:a("debug-step-out",60117),debugStepOver:a("debug-step-over",60118),debugStop:a("debug-stop",60119),debug:a("debug",60120),deviceCameraVideo:a("device-camera-video",60121),deviceCamera:a("device-camera",60122),deviceMobile:a("device-mobile",60123),diffAdded:a("diff-added",60124),diffIgnored:a("diff-ignored",60125),diffModified:a("diff-modified",60126),diffRemoved:a("diff-removed",60127),diffRenamed:a("diff-renamed",60128),diff:a("diff",60129),diffSidebyside:a("diff-sidebyside",60129),discard:a("discard",60130),editorLayout:a("editor-layout",60131),emptyWindow:a("empty-window",60132),exclude:a("exclude",60133),extensions:a("extensions",60134),eyeClosed:a("eye-closed",60135),fileBinary:a("file-binary",60136),fileCode:a("file-code",60137),fileMedia:a("file-media",60138),filePdf:a("file-pdf",60139),fileSubmodule:a("file-submodule",60140),fileSymlinkDirectory:a("file-symlink-directory",60141),fileSymlinkFile:a("file-symlink-file",60142),fileZip:a("file-zip",60143),files:a("files",60144),filter:a("filter",60145),flame:a("flame",60146),foldDown:a("fold-down",60147),foldUp:a("fold-up",60148),fold:a("fold",60149),folderActive:a("folder-active",60150),folderOpened:a("folder-opened",60151),gear:a("gear",60152),gift:a("gift",60153),gistSecret:a("gist-secret",60154),gist:a("gist",60155),gitCommit:a("git-commit",60156),gitCompare:a("git-compare",60157),compareChanges:a("compare-changes",60157),gitMerge:a("git-merge",60158),githubAction:a("github-action",60159),githubAlt:a("github-alt",60160),globe:a("globe",60161),grabber:a("grabber",60162),graph:a("graph",60163),gripper:a("gripper",60164),heart:a("heart",60165),home:a("home",60166),horizontalRule:a("horizontal-rule",60167),hubot:a("hubot",60168),inbox:a("inbox",60169),issueReopened:a("issue-reopened",60171),issues:a("issues",60172),italic:a("italic",60173),jersey:a("jersey",60174),json:a("json",60175),kebabVertical:a("kebab-vertical",60176),key:a("key",60177),law:a("law",60178),lightbulbAutofix:a("lightbulb-autofix",60179),linkExternal:a("link-external",60180),link:a("link",60181),listOrdered:a("list-ordered",60182),listUnordered:a("list-unordered",60183),liveShare:a("live-share",60184),loading:a("loading",60185),location:a("location",60186),mailRead:a("mail-read",60187),mail:a("mail",60188),markdown:a("markdown",60189),megaphone:a("megaphone",60190),mention:a("mention",60191),milestone:a("milestone",60192),gitPullRequestMilestone:a("git-pull-request-milestone",60192),mortarBoard:a("mortar-board",60193),move:a("move",60194),multipleWindows:a("multiple-windows",60195),mute:a("mute",60196),noNewline:a("no-newline",60197),note:a("note",60198),octoface:a("octoface",60199),openPreview:a("open-preview",60200),package:a("package",60201),paintcan:a("paintcan",60202),pin:a("pin",60203),play:a("play",60204),run:a("run",60204),plug:a("plug",60205),preserveCase:a("preserve-case",60206),preview:a("preview",60207),project:a("project",60208),pulse:a("pulse",60209),question:a("question",60210),quote:a("quote",60211),radioTower:a("radio-tower",60212),reactions:a("reactions",60213),references:a("references",60214),refresh:a("refresh",60215),regex:a("regex",60216),remoteExplorer:a("remote-explorer",60217),remote:a("remote",60218),remove:a("remove",60219),replaceAll:a("replace-all",60220),replace:a("replace",60221),repoClone:a("repo-clone",60222),repoForcePush:a("repo-force-push",60223),repoPull:a("repo-pull",60224),repoPush:a("repo-push",60225),report:a("report",60226),requestChanges:a("request-changes",60227),rocket:a("rocket",60228),rootFolderOpened:a("root-folder-opened",60229),rootFolder:a("root-folder",60230),rss:a("rss",60231),ruby:a("ruby",60232),saveAll:a("save-all",60233),saveAs:a("save-as",60234),save:a("save",60235),screenFull:a("screen-full",60236),screenNormal:a("screen-normal",60237),searchStop:a("search-stop",60238),server:a("server",60240),settingsGear:a("settings-gear",60241),settings:a("settings",60242),shield:a("shield",60243),smiley:a("smiley",60244),sortPrecedence:a("sort-precedence",60245),splitHorizontal:a("split-horizontal",60246),splitVertical:a("split-vertical",60247),squirrel:a("squirrel",60248),starFull:a("star-full",60249),starHalf:a("star-half",60250),symbolClass:a("symbol-class",60251),symbolColor:a("symbol-color",60252),symbolConstant:a("symbol-constant",60253),symbolEnumMember:a("symbol-enum-member",60254),symbolField:a("symbol-field",60255),symbolFile:a("symbol-file",60256),symbolInterface:a("symbol-interface",60257),symbolKeyword:a("symbol-keyword",60258),symbolMisc:a("symbol-misc",60259),symbolOperator:a("symbol-operator",60260),symbolProperty:a("symbol-property",60261),wrench:a("wrench",60261),wrenchSubaction:a("wrench-subaction",60261),symbolSnippet:a("symbol-snippet",60262),tasklist:a("tasklist",60263),telescope:a("telescope",60264),textSize:a("text-size",60265),threeBars:a("three-bars",60266),thumbsdown:a("thumbsdown",60267),thumbsup:a("thumbsup",60268),tools:a("tools",60269),triangleDown:a("triangle-down",60270),triangleLeft:a("triangle-left",60271),triangleRight:a("triangle-right",60272),triangleUp:a("triangle-up",60273),twitter:a("twitter",60274),unfold:a("unfold",60275),unlock:a("unlock",60276),unmute:a("unmute",60277),unverified:a("unverified",60278),verified:a("verified",60279),versions:a("versions",60280),vmActive:a("vm-active",60281),vmOutline:a("vm-outline",60282),vmRunning:a("vm-running",60283),watch:a("watch",60284),whitespace:a("whitespace",60285),wholeWord:a("whole-word",60286),window:a("window",60287),wordWrap:a("word-wrap",60288),zoomIn:a("zoom-in",60289),zoomOut:a("zoom-out",60290),listFilter:a("list-filter",60291),listFlat:a("list-flat",60292),listSelection:a("list-selection",60293),selection:a("selection",60293),listTree:a("list-tree",60294),debugBreakpointFunctionUnverified:a("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:a("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:a("debug-breakpoint-function-disabled",60296),debugStackframeActive:a("debug-stackframe-active",60297),circleSmallFilled:a("circle-small-filled",60298),debugStackframeDot:a("debug-stackframe-dot",60298),terminalDecorationMark:a("terminal-decoration-mark",60298),debugStackframe:a("debug-stackframe",60299),debugStackframeFocused:a("debug-stackframe-focused",60299),debugBreakpointUnsupported:a("debug-breakpoint-unsupported",60300),symbolString:a("symbol-string",60301),debugReverseContinue:a("debug-reverse-continue",60302),debugStepBack:a("debug-step-back",60303),debugRestartFrame:a("debug-restart-frame",60304),debugAlt:a("debug-alt",60305),callIncoming:a("call-incoming",60306),callOutgoing:a("call-outgoing",60307),menu:a("menu",60308),expandAll:a("expand-all",60309),feedback:a("feedback",60310),gitPullRequestReviewer:a("git-pull-request-reviewer",60310),groupByRefType:a("group-by-ref-type",60311),ungroupByRefType:a("ungroup-by-ref-type",60312),account:a("account",60313),gitPullRequestAssignee:a("git-pull-request-assignee",60313),bellDot:a("bell-dot",60314),debugConsole:a("debug-console",60315),library:a("library",60316),output:a("output",60317),runAll:a("run-all",60318),syncIgnored:a("sync-ignored",60319),pinned:a("pinned",60320),githubInverted:a("github-inverted",60321),serverProcess:a("server-process",60322),serverEnvironment:a("server-environment",60323),pass:a("pass",60324),issueClosed:a("issue-closed",60324),stopCircle:a("stop-circle",60325),playCircle:a("play-circle",60326),record:a("record",60327),debugAltSmall:a("debug-alt-small",60328),vmConnect:a("vm-connect",60329),cloud:a("cloud",60330),merge:a("merge",60331),export:a("export",60332),graphLeft:a("graph-left",60333),magnet:a("magnet",60334),notebook:a("notebook",60335),redo:a("redo",60336),checkAll:a("check-all",60337),pinnedDirty:a("pinned-dirty",60338),passFilled:a("pass-filled",60339),circleLargeFilled:a("circle-large-filled",60340),circleLarge:a("circle-large",60341),circleLargeOutline:a("circle-large-outline",60341),combine:a("combine",60342),gather:a("gather",60342),table:a("table",60343),variableGroup:a("variable-group",60344),typeHierarchy:a("type-hierarchy",60345),typeHierarchySub:a("type-hierarchy-sub",60346),typeHierarchySuper:a("type-hierarchy-super",60347),gitPullRequestCreate:a("git-pull-request-create",60348),runAbove:a("run-above",60349),runBelow:a("run-below",60350),notebookTemplate:a("notebook-template",60351),debugRerun:a("debug-rerun",60352),workspaceTrusted:a("workspace-trusted",60353),workspaceUntrusted:a("workspace-untrusted",60354),workspaceUnknown:a("workspace-unknown",60355),terminalCmd:a("terminal-cmd",60356),terminalDebian:a("terminal-debian",60357),terminalLinux:a("terminal-linux",60358),terminalPowershell:a("terminal-powershell",60359),terminalTmux:a("terminal-tmux",60360),terminalUbuntu:a("terminal-ubuntu",60361),terminalBash:a("terminal-bash",60362),arrowSwap:a("arrow-swap",60363),copy:a("copy",60364),personAdd:a("person-add",60365),filterFilled:a("filter-filled",60366),wand:a("wand",60367),debugLineByLine:a("debug-line-by-line",60368),inspect:a("inspect",60369),layers:a("layers",60370),layersDot:a("layers-dot",60371),layersActive:a("layers-active",60372),compass:a("compass",60373),compassDot:a("compass-dot",60374),compassActive:a("compass-active",60375),azure:a("azure",60376),issueDraft:a("issue-draft",60377),gitPullRequestClosed:a("git-pull-request-closed",60378),gitPullRequestDraft:a("git-pull-request-draft",60379),debugAll:a("debug-all",60380),debugCoverage:a("debug-coverage",60381),runErrors:a("run-errors",60382),folderLibrary:a("folder-library",60383),debugContinueSmall:a("debug-continue-small",60384),beakerStop:a("beaker-stop",60385),graphLine:a("graph-line",60386),graphScatter:a("graph-scatter",60387),pieChart:a("pie-chart",60388),bracket:a("bracket",60175),bracketDot:a("bracket-dot",60389),bracketError:a("bracket-error",60390),lockSmall:a("lock-small",60391),azureDevops:a("azure-devops",60392),verifiedFilled:a("verified-filled",60393),newline:a("newline",60394),layout:a("layout",60395),layoutActivitybarLeft:a("layout-activitybar-left",60396),layoutActivitybarRight:a("layout-activitybar-right",60397),layoutPanelLeft:a("layout-panel-left",60398),layoutPanelCenter:a("layout-panel-center",60399),layoutPanelJustify:a("layout-panel-justify",60400),layoutPanelRight:a("layout-panel-right",60401),layoutPanel:a("layout-panel",60402),layoutSidebarLeft:a("layout-sidebar-left",60403),layoutSidebarRight:a("layout-sidebar-right",60404),layoutStatusbar:a("layout-statusbar",60405),layoutMenubar:a("layout-menubar",60406),layoutCentered:a("layout-centered",60407),target:a("target",60408),indent:a("indent",60409),recordSmall:a("record-small",60410),errorSmall:a("error-small",60411),terminalDecorationError:a("terminal-decoration-error",60411),arrowCircleDown:a("arrow-circle-down",60412),arrowCircleLeft:a("arrow-circle-left",60413),arrowCircleRight:a("arrow-circle-right",60414),arrowCircleUp:a("arrow-circle-up",60415),layoutSidebarRightOff:a("layout-sidebar-right-off",60416),layoutPanelOff:a("layout-panel-off",60417),layoutSidebarLeftOff:a("layout-sidebar-left-off",60418),blank:a("blank",60419),heartFilled:a("heart-filled",60420),map:a("map",60421),mapHorizontal:a("map-horizontal",60421),foldHorizontal:a("fold-horizontal",60421),mapFilled:a("map-filled",60422),mapHorizontalFilled:a("map-horizontal-filled",60422),foldHorizontalFilled:a("fold-horizontal-filled",60422),circleSmall:a("circle-small",60423),bellSlash:a("bell-slash",60424),bellSlashDot:a("bell-slash-dot",60425),commentUnresolved:a("comment-unresolved",60426),gitPullRequestGoToChanges:a("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:a("git-pull-request-new-changes",60428),searchFuzzy:a("search-fuzzy",60429),commentDraft:a("comment-draft",60430),send:a("send",60431),sparkle:a("sparkle",60432),insert:a("insert",60433),mic:a("mic",60434),thumbsdownFilled:a("thumbsdown-filled",60435),thumbsupFilled:a("thumbsup-filled",60436),coffee:a("coffee",60437),snake:a("snake",60438),game:a("game",60439),vr:a("vr",60440),chip:a("chip",60441),piano:a("piano",60442),music:a("music",60443),micFilled:a("mic-filled",60444),repoFetch:a("repo-fetch",60445),copilot:a("copilot",60446),lightbulbSparkle:a("lightbulb-sparkle",60447),robot:a("robot",60448),sparkleFilled:a("sparkle-filled",60449),diffSingle:a("diff-single",60450),diffMultiple:a("diff-multiple",60451),surroundWith:a("surround-with",60452),share:a("share",60453),gitStash:a("git-stash",60454),gitStashApply:a("git-stash-apply",60455),gitStashPop:a("git-stash-pop",60456),vscode:a("vscode",60457),vscodeInsiders:a("vscode-insiders",60458),codeOss:a("code-oss",60459),runCoverage:a("run-coverage",60460),runAllCoverage:a("run-all-coverage",60461),coverage:a("coverage",60462),githubProject:a("github-project",60463),mapVertical:a("map-vertical",60464),foldVertical:a("fold-vertical",60464),mapVerticalFilled:a("map-vertical-filled",60465),foldVerticalFilled:a("fold-vertical-filled",60465),goToSearch:a("go-to-search",60466),percentage:a("percentage",60467),sortPercentage:a("sort-percentage",60467),attach:a("attach",60468)},ca={dialogError:a("dialog-error","error"),dialogWarning:a("dialog-warning","warning"),dialogInfo:a("dialog-info","info"),dialogClose:a("dialog-close","close"),treeItemExpanded:a("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:a("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:a("tree-filter-on-type-off","list-selection"),treeFilterClear:a("tree-filter-clear","close"),treeItemLoading:a("tree-item-loading","loading"),menuSelection:a("menu-selection","check"),menuSubmenu:a("menu-submenu","chevron-right"),menuBarMore:a("menubar-more","more"),scrollbarButtonLeft:a("scrollbar-button-left","triangle-left"),scrollbarButtonRight:a("scrollbar-button-right","triangle-right"),scrollbarButtonUp:a("scrollbar-button-up","triangle-up"),scrollbarButtonDown:a("scrollbar-button-down","triangle-down"),toolBarMore:a("toolbar-more","more"),quickInputBack:a("quick-input-back","arrow-left"),dropDownButton:a("drop-down-button",60084),symbolCustomColor:a("symbol-customcolor",60252),exportIcon:a("export",60332),workspaceUnspecified:a("workspace-unspecified",60355),newLine:a("newline",60394),thumbsDownFilled:a("thumbsdown-filled",60435),thumbsUpFilled:a("thumbsup-filled",60436),gitFetch:a("git-fetch",60445),lightbulbSparkleAutofix:a("lightbulb-sparkle-autofix",60447),debugBreakpointPending:a("debug-breakpoint-pending",60377)},M={...ua,...ca};class tr{constructor(){this._tokenizationSupports=new Map,this._factories=new Map,this._onDidChange=new oe,this.onDidChange=this._onDidChange.event,this._colorMap=null}handleChange(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})}register(e,n){return this._tokenizationSupports.set(e,n),this.handleChange([e]),ft(()=>{this._tokenizationSupports.get(e)===n&&(this._tokenizationSupports.delete(e),this.handleChange([e]))})}get(e){return this._tokenizationSupports.get(e)||null}registerFactory(e,n){this._factories.get(e)?.dispose();const r=new ha(this,e,n);return this._factories.set(e,r),ft(()=>{const s=this._factories.get(e);!s||s!==r||(this._factories.delete(e),s.dispose())})}async getOrCreate(e){const n=this.get(e);if(n)return n;const r=this._factories.get(e);return!r||r.isResolved?null:(await r.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;const r=this._factories.get(e);return!!(!r||r.isResolved)}setColorMap(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Array.from(this._tokenizationSupports.keys()),changedColorMap:!0})}getColorMap(){return this._colorMap}getDefaultBackground(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null}}class ha extends Pe{get isResolved(){return this._isResolved}constructor(e,n,r){super(),this._registry=e,this._languageId=n,this._factory=r,this._isDisposed=!1,this._resolvePromise=null,this._isResolved=!1}dispose(){this._isDisposed=!0,super.dispose()}async resolve(){return this._resolvePromise||(this._resolvePromise=this._create()),this._resolvePromise}async _create(){const e=await this._factory.tokenizationSupport;this._isResolved=!0,e&&!this._isDisposed&&this._register(this._registry.register(this._languageId,e))}}class fa{constructor(e,n,r){this.offset=e,this.type=n,this.language=r,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}}var nr;(function(t){t[t.Increase=0]="Increase",t[t.Decrease=1]="Decrease"})(nr||(nr={}));var rr;(function(t){const e=new Map;e.set(0,M.symbolMethod),e.set(1,M.symbolFunction),e.set(2,M.symbolConstructor),e.set(3,M.symbolField),e.set(4,M.symbolVariable),e.set(5,M.symbolClass),e.set(6,M.symbolStruct),e.set(7,M.symbolInterface),e.set(8,M.symbolModule),e.set(9,M.symbolProperty),e.set(10,M.symbolEvent),e.set(11,M.symbolOperator),e.set(12,M.symbolUnit),e.set(13,M.symbolValue),e.set(15,M.symbolEnum),e.set(14,M.symbolConstant),e.set(15,M.symbolEnum),e.set(16,M.symbolEnumMember),e.set(17,M.symbolKeyword),e.set(27,M.symbolSnippet),e.set(18,M.symbolText),e.set(19,M.symbolColor),e.set(20,M.symbolFile),e.set(21,M.symbolReference),e.set(22,M.symbolCustomColor),e.set(23,M.symbolFolder),e.set(24,M.symbolTypeParameter),e.set(25,M.account),e.set(26,M.issues);function n(i){let o=e.get(i);return o||(console.info("No codicon found for CompletionItemKind "+i),o=M.symbolProperty),o}t.toIcon=n;const r=new Map;r.set("method",0),r.set("function",1),r.set("constructor",2),r.set("field",3),r.set("variable",4),r.set("class",5),r.set("struct",6),r.set("interface",7),r.set("module",8),r.set("property",9),r.set("event",10),r.set("operator",11),r.set("unit",12),r.set("value",13),r.set("constant",14),r.set("enum",15),r.set("enum-member",16),r.set("enumMember",16),r.set("keyword",17),r.set("snippet",27),r.set("text",18),r.set("color",19),r.set("file",20),r.set("reference",21),r.set("customcolor",22),r.set("folder",23),r.set("type-parameter",24),r.set("typeParameter",24),r.set("account",25),r.set("issue",26);function s(i,o){let l=r.get(i);return typeof l>"u"&&!o&&(l=9),l}t.fromString=s})(rr||(rr={}));var sr;(function(t){t[t.Automatic=0]="Automatic",t[t.Explicit=1]="Explicit"})(sr||(sr={}));var ir;(function(t){t[t.Automatic=0]="Automatic",t[t.PasteAs=1]="PasteAs"})(ir||(ir={}));var ar;(function(t){t[t.Invoke=1]="Invoke",t[t.TriggerCharacter=2]="TriggerCharacter",t[t.ContentChange=3]="ContentChange"})(ar||(ar={}));var or;(function(t){t[t.Text=0]="Text",t[t.Read=1]="Read",t[t.Write=2]="Write"})(or||(or={})),z("Array","array"),z("Boolean","boolean"),z("Class","class"),z("Constant","constant"),z("Constructor","constructor"),z("Enum","enumeration"),z("EnumMember","enumeration member"),z("Event","event"),z("Field","field"),z("File","file"),z("Function","function"),z("Interface","interface"),z("Key","key"),z("Method","method"),z("Module","module"),z("Namespace","namespace"),z("Null","null"),z("Number","number"),z("Object","object"),z("Operator","operator"),z("Package","package"),z("Property","property"),z("String","string"),z("Struct","struct"),z("TypeParameter","type parameter"),z("Variable","variable");var lr;(function(t){const e=new Map;e.set(0,M.symbolFile),e.set(1,M.symbolModule),e.set(2,M.symbolNamespace),e.set(3,M.symbolPackage),e.set(4,M.symbolClass),e.set(5,M.symbolMethod),e.set(6,M.symbolProperty),e.set(7,M.symbolField),e.set(8,M.symbolConstructor),e.set(9,M.symbolEnum),e.set(10,M.symbolInterface),e.set(11,M.symbolFunction),e.set(12,M.symbolVariable),e.set(13,M.symbolConstant),e.set(14,M.symbolString),e.set(15,M.symbolNumber),e.set(16,M.symbolBoolean),e.set(17,M.symbolArray),e.set(18,M.symbolObject),e.set(19,M.symbolKey),e.set(20,M.symbolNull),e.set(21,M.symbolEnumMember),e.set(22,M.symbolStruct),e.set(23,M.symbolEvent),e.set(24,M.symbolOperator),e.set(25,M.symbolTypeParameter);function n(r){let s=e.get(r);return s||(console.info("No codicon found for SymbolKind "+r),s=M.symbolProperty),s}t.toIcon=n})(lr||(lr={}));const ce=class ce{static fromValue(e){switch(e){case"comment":return ce.Comment;case"imports":return ce.Imports;case"region":return ce.Region}return new ce(e)}constructor(e){this.value=e}};ce.Comment=new ce("comment"),ce.Imports=new ce("imports"),ce.Region=new ce("region");let ur=ce;var cr;(function(t){t[t.AIGenerated=1]="AIGenerated"})(cr||(cr={}));var hr;(function(t){t[t.Invoke=0]="Invoke",t[t.Automatic=1]="Automatic"})(hr||(hr={}));var fr;(function(t){function e(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}t.is=e})(fr||(fr={}));var mr;(function(t){t[t.Type=1]="Type",t[t.Parameter=2]="Parameter"})(mr||(mr={})),new tr,new tr;var dr;(function(t){t[t.Invoke=0]="Invoke",t[t.Automatic=1]="Automatic"})(dr||(dr={}));var gr;(function(t){t[t.Unknown=0]="Unknown",t[t.Disabled=1]="Disabled",t[t.Enabled=2]="Enabled"})(gr||(gr={}));var pr;(function(t){t[t.Invoke=1]="Invoke",t[t.Auto=2]="Auto"})(pr||(pr={}));var br;(function(t){t[t.None=0]="None",t[t.KeepWhitespace=1]="KeepWhitespace",t[t.InsertAsSnippet=4]="InsertAsSnippet"})(br||(br={}));var yr;(function(t){t[t.Method=0]="Method",t[t.Function=1]="Function",t[t.Constructor=2]="Constructor",t[t.Field=3]="Field",t[t.Variable=4]="Variable",t[t.Class=5]="Class",t[t.Struct=6]="Struct",t[t.Interface=7]="Interface",t[t.Module=8]="Module",t[t.Property=9]="Property",t[t.Event=10]="Event",t[t.Operator=11]="Operator",t[t.Unit=12]="Unit",t[t.Value=13]="Value",t[t.Constant=14]="Constant",t[t.Enum=15]="Enum",t[t.EnumMember=16]="EnumMember",t[t.Keyword=17]="Keyword",t[t.Text=18]="Text",t[t.Color=19]="Color",t[t.File=20]="File",t[t.Reference=21]="Reference",t[t.Customcolor=22]="Customcolor",t[t.Folder=23]="Folder",t[t.TypeParameter=24]="TypeParameter",t[t.User=25]="User",t[t.Issue=26]="Issue",t[t.Snippet=27]="Snippet"})(yr||(yr={}));var xr;(function(t){t[t.Deprecated=1]="Deprecated"})(xr||(xr={}));var _r;(function(t){t[t.Invoke=0]="Invoke",t[t.TriggerCharacter=1]="TriggerCharacter",t[t.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(_r||(_r={}));var wr;(function(t){t[t.EXACT=0]="EXACT",t[t.ABOVE=1]="ABOVE",t[t.BELOW=2]="BELOW"})(wr||(wr={}));var Lr;(function(t){t[t.NotSet=0]="NotSet",t[t.ContentFlush=1]="ContentFlush",t[t.RecoverFromMarkers=2]="RecoverFromMarkers",t[t.Explicit=3]="Explicit",t[t.Paste=4]="Paste",t[t.Undo=5]="Undo",t[t.Redo=6]="Redo"})(Lr||(Lr={}));var vr;(function(t){t[t.LF=1]="LF",t[t.CRLF=2]="CRLF"})(vr||(vr={}));var Nr;(function(t){t[t.Text=0]="Text",t[t.Read=1]="Read",t[t.Write=2]="Write"})(Nr||(Nr={}));var Sr;(function(t){t[t.None=0]="None",t[t.Keep=1]="Keep",t[t.Brackets=2]="Brackets",t[t.Advanced=3]="Advanced",t[t.Full=4]="Full"})(Sr||(Sr={}));var Cr;(function(t){t[t.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",t[t.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",t[t.accessibilitySupport=2]="accessibilitySupport",t[t.accessibilityPageSize=3]="accessibilityPageSize",t[t.ariaLabel=4]="ariaLabel",t[t.ariaRequired=5]="ariaRequired",t[t.autoClosingBrackets=6]="autoClosingBrackets",t[t.autoClosingComments=7]="autoClosingComments",t[t.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",t[t.autoClosingDelete=9]="autoClosingDelete",t[t.autoClosingOvertype=10]="autoClosingOvertype",t[t.autoClosingQuotes=11]="autoClosingQuotes",t[t.autoIndent=12]="autoIndent",t[t.automaticLayout=13]="automaticLayout",t[t.autoSurround=14]="autoSurround",t[t.bracketPairColorization=15]="bracketPairColorization",t[t.guides=16]="guides",t[t.codeLens=17]="codeLens",t[t.codeLensFontFamily=18]="codeLensFontFamily",t[t.codeLensFontSize=19]="codeLensFontSize",t[t.colorDecorators=20]="colorDecorators",t[t.colorDecoratorsLimit=21]="colorDecoratorsLimit",t[t.columnSelection=22]="columnSelection",t[t.comments=23]="comments",t[t.contextmenu=24]="contextmenu",t[t.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",t[t.cursorBlinking=26]="cursorBlinking",t[t.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",t[t.cursorStyle=28]="cursorStyle",t[t.cursorSurroundingLines=29]="cursorSurroundingLines",t[t.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",t[t.cursorWidth=31]="cursorWidth",t[t.disableLayerHinting=32]="disableLayerHinting",t[t.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",t[t.domReadOnly=34]="domReadOnly",t[t.dragAndDrop=35]="dragAndDrop",t[t.dropIntoEditor=36]="dropIntoEditor",t[t.emptySelectionClipboard=37]="emptySelectionClipboard",t[t.experimentalWhitespaceRendering=38]="experimentalWhitespaceRendering",t[t.extraEditorClassName=39]="extraEditorClassName",t[t.fastScrollSensitivity=40]="fastScrollSensitivity",t[t.find=41]="find",t[t.fixedOverflowWidgets=42]="fixedOverflowWidgets",t[t.folding=43]="folding",t[t.foldingStrategy=44]="foldingStrategy",t[t.foldingHighlight=45]="foldingHighlight",t[t.foldingImportsByDefault=46]="foldingImportsByDefault",t[t.foldingMaximumRegions=47]="foldingMaximumRegions",t[t.unfoldOnClickAfterEndOfLine=48]="unfoldOnClickAfterEndOfLine",t[t.fontFamily=49]="fontFamily",t[t.fontInfo=50]="fontInfo",t[t.fontLigatures=51]="fontLigatures",t[t.fontSize=52]="fontSize",t[t.fontWeight=53]="fontWeight",t[t.fontVariations=54]="fontVariations",t[t.formatOnPaste=55]="formatOnPaste",t[t.formatOnType=56]="formatOnType",t[t.glyphMargin=57]="glyphMargin",t[t.gotoLocation=58]="gotoLocation",t[t.hideCursorInOverviewRuler=59]="hideCursorInOverviewRuler",t[t.hover=60]="hover",t[t.inDiffEditor=61]="inDiffEditor",t[t.inlineSuggest=62]="inlineSuggest",t[t.inlineEdit=63]="inlineEdit",t[t.letterSpacing=64]="letterSpacing",t[t.lightbulb=65]="lightbulb",t[t.lineDecorationsWidth=66]="lineDecorationsWidth",t[t.lineHeight=67]="lineHeight",t[t.lineNumbers=68]="lineNumbers",t[t.lineNumbersMinChars=69]="lineNumbersMinChars",t[t.linkedEditing=70]="linkedEditing",t[t.links=71]="links",t[t.matchBrackets=72]="matchBrackets",t[t.minimap=73]="minimap",t[t.mouseStyle=74]="mouseStyle",t[t.mouseWheelScrollSensitivity=75]="mouseWheelScrollSensitivity",t[t.mouseWheelZoom=76]="mouseWheelZoom",t[t.multiCursorMergeOverlapping=77]="multiCursorMergeOverlapping",t[t.multiCursorModifier=78]="multiCursorModifier",t[t.multiCursorPaste=79]="multiCursorPaste",t[t.multiCursorLimit=80]="multiCursorLimit",t[t.occurrencesHighlight=81]="occurrencesHighlight",t[t.overviewRulerBorder=82]="overviewRulerBorder",t[t.overviewRulerLanes=83]="overviewRulerLanes",t[t.padding=84]="padding",t[t.pasteAs=85]="pasteAs",t[t.parameterHints=86]="parameterHints",t[t.peekWidgetDefaultFocus=87]="peekWidgetDefaultFocus",t[t.placeholder=88]="placeholder",t[t.definitionLinkOpensInPeek=89]="definitionLinkOpensInPeek",t[t.quickSuggestions=90]="quickSuggestions",t[t.quickSuggestionsDelay=91]="quickSuggestionsDelay",t[t.readOnly=92]="readOnly",t[t.readOnlyMessage=93]="readOnlyMessage",t[t.renameOnType=94]="renameOnType",t[t.renderControlCharacters=95]="renderControlCharacters",t[t.renderFinalNewline=96]="renderFinalNewline",t[t.renderLineHighlight=97]="renderLineHighlight",t[t.renderLineHighlightOnlyWhenFocus=98]="renderLineHighlightOnlyWhenFocus",t[t.renderValidationDecorations=99]="renderValidationDecorations",t[t.renderWhitespace=100]="renderWhitespace",t[t.revealHorizontalRightPadding=101]="revealHorizontalRightPadding",t[t.roundedSelection=102]="roundedSelection",t[t.rulers=103]="rulers",t[t.scrollbar=104]="scrollbar",t[t.scrollBeyondLastColumn=105]="scrollBeyondLastColumn",t[t.scrollBeyondLastLine=106]="scrollBeyondLastLine",t[t.scrollPredominantAxis=107]="scrollPredominantAxis",t[t.selectionClipboard=108]="selectionClipboard",t[t.selectionHighlight=109]="selectionHighlight",t[t.selectOnLineNumbers=110]="selectOnLineNumbers",t[t.showFoldingControls=111]="showFoldingControls",t[t.showUnused=112]="showUnused",t[t.snippetSuggestions=113]="snippetSuggestions",t[t.smartSelect=114]="smartSelect",t[t.smoothScrolling=115]="smoothScrolling",t[t.stickyScroll=116]="stickyScroll",t[t.stickyTabStops=117]="stickyTabStops",t[t.stopRenderingLineAfter=118]="stopRenderingLineAfter",t[t.suggest=119]="suggest",t[t.suggestFontSize=120]="suggestFontSize",t[t.suggestLineHeight=121]="suggestLineHeight",t[t.suggestOnTriggerCharacters=122]="suggestOnTriggerCharacters",t[t.suggestSelection=123]="suggestSelection",t[t.tabCompletion=124]="tabCompletion",t[t.tabIndex=125]="tabIndex",t[t.unicodeHighlighting=126]="unicodeHighlighting",t[t.unusualLineTerminators=127]="unusualLineTerminators",t[t.useShadowDOM=128]="useShadowDOM",t[t.useTabStops=129]="useTabStops",t[t.wordBreak=130]="wordBreak",t[t.wordSegmenterLocales=131]="wordSegmenterLocales",t[t.wordSeparators=132]="wordSeparators",t[t.wordWrap=133]="wordWrap",t[t.wordWrapBreakAfterCharacters=134]="wordWrapBreakAfterCharacters",t[t.wordWrapBreakBeforeCharacters=135]="wordWrapBreakBeforeCharacters",t[t.wordWrapColumn=136]="wordWrapColumn",t[t.wordWrapOverride1=137]="wordWrapOverride1",t[t.wordWrapOverride2=138]="wordWrapOverride2",t[t.wrappingIndent=139]="wrappingIndent",t[t.wrappingStrategy=140]="wrappingStrategy",t[t.showDeprecated=141]="showDeprecated",t[t.inlayHints=142]="inlayHints",t[t.editorClassName=143]="editorClassName",t[t.pixelRatio=144]="pixelRatio",t[t.tabFocusMode=145]="tabFocusMode",t[t.layoutInfo=146]="layoutInfo",t[t.wrappingInfo=147]="wrappingInfo",t[t.defaultColorDecorators=148]="defaultColorDecorators",t[t.colorDecoratorsActivatedOn=149]="colorDecoratorsActivatedOn",t[t.inlineCompletionsAccessibilityVerbose=150]="inlineCompletionsAccessibilityVerbose"})(Cr||(Cr={}));var Ar;(function(t){t[t.TextDefined=0]="TextDefined",t[t.LF=1]="LF",t[t.CRLF=2]="CRLF"})(Ar||(Ar={}));var Rr;(function(t){t[t.LF=0]="LF",t[t.CRLF=1]="CRLF"})(Rr||(Rr={}));var Er;(function(t){t[t.Left=1]="Left",t[t.Center=2]="Center",t[t.Right=3]="Right"})(Er||(Er={}));var Mr;(function(t){t[t.Increase=0]="Increase",t[t.Decrease=1]="Decrease"})(Mr||(Mr={}));var kr;(function(t){t[t.None=0]="None",t[t.Indent=1]="Indent",t[t.IndentOutdent=2]="IndentOutdent",t[t.Outdent=3]="Outdent"})(kr||(kr={}));var Pr;(function(t){t[t.Both=0]="Both",t[t.Right=1]="Right",t[t.Left=2]="Left",t[t.None=3]="None"})(Pr||(Pr={}));var Fr;(function(t){t[t.Type=1]="Type",t[t.Parameter=2]="Parameter"})(Fr||(Fr={}));var Dr;(function(t){t[t.Automatic=0]="Automatic",t[t.Explicit=1]="Explicit"})(Dr||(Dr={}));var Tr;(function(t){t[t.Invoke=0]="Invoke",t[t.Automatic=1]="Automatic"})(Tr||(Tr={}));var un;(function(t){t[t.DependsOnKbLayout=-1]="DependsOnKbLayout",t[t.Unknown=0]="Unknown",t[t.Backspace=1]="Backspace",t[t.Tab=2]="Tab",t[t.Enter=3]="Enter",t[t.Shift=4]="Shift",t[t.Ctrl=5]="Ctrl",t[t.Alt=6]="Alt",t[t.PauseBreak=7]="PauseBreak",t[t.CapsLock=8]="CapsLock",t[t.Escape=9]="Escape",t[t.Space=10]="Space",t[t.PageUp=11]="PageUp",t[t.PageDown=12]="PageDown",t[t.End=13]="End",t[t.Home=14]="Home",t[t.LeftArrow=15]="LeftArrow",t[t.UpArrow=16]="UpArrow",t[t.RightArrow=17]="RightArrow",t[t.DownArrow=18]="DownArrow",t[t.Insert=19]="Insert",t[t.Delete=20]="Delete",t[t.Digit0=21]="Digit0",t[t.Digit1=22]="Digit1",t[t.Digit2=23]="Digit2",t[t.Digit3=24]="Digit3",t[t.Digit4=25]="Digit4",t[t.Digit5=26]="Digit5",t[t.Digit6=27]="Digit6",t[t.Digit7=28]="Digit7",t[t.Digit8=29]="Digit8",t[t.Digit9=30]="Digit9",t[t.KeyA=31]="KeyA",t[t.KeyB=32]="KeyB",t[t.KeyC=33]="KeyC",t[t.KeyD=34]="KeyD",t[t.KeyE=35]="KeyE",t[t.KeyF=36]="KeyF",t[t.KeyG=37]="KeyG",t[t.KeyH=38]="KeyH",t[t.KeyI=39]="KeyI",t[t.KeyJ=40]="KeyJ",t[t.KeyK=41]="KeyK",t[t.KeyL=42]="KeyL",t[t.KeyM=43]="KeyM",t[t.KeyN=44]="KeyN",t[t.KeyO=45]="KeyO",t[t.KeyP=46]="KeyP",t[t.KeyQ=47]="KeyQ",t[t.KeyR=48]="KeyR",t[t.KeyS=49]="KeyS",t[t.KeyT=50]="KeyT",t[t.KeyU=51]="KeyU",t[t.KeyV=52]="KeyV",t[t.KeyW=53]="KeyW",t[t.KeyX=54]="KeyX",t[t.KeyY=55]="KeyY",t[t.KeyZ=56]="KeyZ",t[t.Meta=57]="Meta",t[t.ContextMenu=58]="ContextMenu",t[t.F1=59]="F1",t[t.F2=60]="F2",t[t.F3=61]="F3",t[t.F4=62]="F4",t[t.F5=63]="F5",t[t.F6=64]="F6",t[t.F7=65]="F7",t[t.F8=66]="F8",t[t.F9=67]="F9",t[t.F10=68]="F10",t[t.F11=69]="F11",t[t.F12=70]="F12",t[t.F13=71]="F13",t[t.F14=72]="F14",t[t.F15=73]="F15",t[t.F16=74]="F16",t[t.F17=75]="F17",t[t.F18=76]="F18",t[t.F19=77]="F19",t[t.F20=78]="F20",t[t.F21=79]="F21",t[t.F22=80]="F22",t[t.F23=81]="F23",t[t.F24=82]="F24",t[t.NumLock=83]="NumLock",t[t.ScrollLock=84]="ScrollLock",t[t.Semicolon=85]="Semicolon",t[t.Equal=86]="Equal",t[t.Comma=87]="Comma",t[t.Minus=88]="Minus",t[t.Period=89]="Period",t[t.Slash=90]="Slash",t[t.Backquote=91]="Backquote",t[t.BracketLeft=92]="BracketLeft",t[t.Backslash=93]="Backslash",t[t.BracketRight=94]="BracketRight",t[t.Quote=95]="Quote",t[t.OEM_8=96]="OEM_8",t[t.IntlBackslash=97]="IntlBackslash",t[t.Numpad0=98]="Numpad0",t[t.Numpad1=99]="Numpad1",t[t.Numpad2=100]="Numpad2",t[t.Numpad3=101]="Numpad3",t[t.Numpad4=102]="Numpad4",t[t.Numpad5=103]="Numpad5",t[t.Numpad6=104]="Numpad6",t[t.Numpad7=105]="Numpad7",t[t.Numpad8=106]="Numpad8",t[t.Numpad9=107]="Numpad9",t[t.NumpadMultiply=108]="NumpadMultiply",t[t.NumpadAdd=109]="NumpadAdd",t[t.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",t[t.NumpadSubtract=111]="NumpadSubtract",t[t.NumpadDecimal=112]="NumpadDecimal",t[t.NumpadDivide=113]="NumpadDivide",t[t.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",t[t.ABNT_C1=115]="ABNT_C1",t[t.ABNT_C2=116]="ABNT_C2",t[t.AudioVolumeMute=117]="AudioVolumeMute",t[t.AudioVolumeUp=118]="AudioVolumeUp",t[t.AudioVolumeDown=119]="AudioVolumeDown",t[t.BrowserSearch=120]="BrowserSearch",t[t.BrowserHome=121]="BrowserHome",t[t.BrowserBack=122]="BrowserBack",t[t.BrowserForward=123]="BrowserForward",t[t.MediaTrackNext=124]="MediaTrackNext",t[t.MediaTrackPrevious=125]="MediaTrackPrevious",t[t.MediaStop=126]="MediaStop",t[t.MediaPlayPause=127]="MediaPlayPause",t[t.LaunchMediaPlayer=128]="LaunchMediaPlayer",t[t.LaunchMail=129]="LaunchMail",t[t.LaunchApp2=130]="LaunchApp2",t[t.Clear=131]="Clear",t[t.MAX_VALUE=132]="MAX_VALUE"})(un||(un={}));var cn;(function(t){t[t.Hint=1]="Hint",t[t.Info=2]="Info",t[t.Warning=4]="Warning",t[t.Error=8]="Error"})(cn||(cn={}));var hn;(function(t){t[t.Unnecessary=1]="Unnecessary",t[t.Deprecated=2]="Deprecated"})(hn||(hn={}));var Ir;(function(t){t[t.Inline=1]="Inline",t[t.Gutter=2]="Gutter"})(Ir||(Ir={}));var Vr;(function(t){t[t.Normal=1]="Normal",t[t.Underlined=2]="Underlined"})(Vr||(Vr={}));var Br;(function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.TEXTAREA=1]="TEXTAREA",t[t.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",t[t.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",t[t.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",t[t.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",t[t.CONTENT_TEXT=6]="CONTENT_TEXT",t[t.CONTENT_EMPTY=7]="CONTENT_EMPTY",t[t.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",t[t.CONTENT_WIDGET=9]="CONTENT_WIDGET",t[t.OVERVIEW_RULER=10]="OVERVIEW_RULER",t[t.SCROLLBAR=11]="SCROLLBAR",t[t.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",t[t.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(Br||(Br={}));var qr;(function(t){t[t.AIGenerated=1]="AIGenerated"})(qr||(qr={}));var Ur;(function(t){t[t.Invoke=0]="Invoke",t[t.Automatic=1]="Automatic"})(Ur||(Ur={}));var $r;(function(t){t[t.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",t[t.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",t[t.TOP_CENTER=2]="TOP_CENTER"})($r||($r={}));var Hr;(function(t){t[t.Left=1]="Left",t[t.Center=2]="Center",t[t.Right=4]="Right",t[t.Full=7]="Full"})(Hr||(Hr={}));var Wr;(function(t){t[t.Word=0]="Word",t[t.Line=1]="Line",t[t.Suggest=2]="Suggest"})(Wr||(Wr={}));var zr;(function(t){t[t.Left=0]="Left",t[t.Right=1]="Right",t[t.None=2]="None",t[t.LeftOfInjectedText=3]="LeftOfInjectedText",t[t.RightOfInjectedText=4]="RightOfInjectedText"})(zr||(zr={}));var Or;(function(t){t[t.Off=0]="Off",t[t.On=1]="On",t[t.Relative=2]="Relative",t[t.Interval=3]="Interval",t[t.Custom=4]="Custom"})(Or||(Or={}));var Gr;(function(t){t[t.None=0]="None",t[t.Text=1]="Text",t[t.Blocks=2]="Blocks"})(Gr||(Gr={}));var jr;(function(t){t[t.Smooth=0]="Smooth",t[t.Immediate=1]="Immediate"})(jr||(jr={}));var Xr;(function(t){t[t.Auto=1]="Auto",t[t.Hidden=2]="Hidden",t[t.Visible=3]="Visible"})(Xr||(Xr={}));var fn;(function(t){t[t.LTR=0]="LTR",t[t.RTL=1]="RTL"})(fn||(fn={}));var Qr;(function(t){t.Off="off",t.OnCode="onCode",t.On="on"})(Qr||(Qr={}));var Yr;(function(t){t[t.Invoke=1]="Invoke",t[t.TriggerCharacter=2]="TriggerCharacter",t[t.ContentChange=3]="ContentChange"})(Yr||(Yr={}));var Jr;(function(t){t[t.File=0]="File",t[t.Module=1]="Module",t[t.Namespace=2]="Namespace",t[t.Package=3]="Package",t[t.Class=4]="Class",t[t.Method=5]="Method",t[t.Property=6]="Property",t[t.Field=7]="Field",t[t.Constructor=8]="Constructor",t[t.Enum=9]="Enum",t[t.Interface=10]="Interface",t[t.Function=11]="Function",t[t.Variable=12]="Variable",t[t.Constant=13]="Constant",t[t.String=14]="String",t[t.Number=15]="Number",t[t.Boolean=16]="Boolean",t[t.Array=17]="Array",t[t.Object=18]="Object",t[t.Key=19]="Key",t[t.Null=20]="Null",t[t.EnumMember=21]="EnumMember",t[t.Struct=22]="Struct",t[t.Event=23]="Event",t[t.Operator=24]="Operator",t[t.TypeParameter=25]="TypeParameter"})(Jr||(Jr={}));var Zr;(function(t){t[t.Deprecated=1]="Deprecated"})(Zr||(Zr={}));var Kr;(function(t){t[t.Hidden=0]="Hidden",t[t.Blink=1]="Blink",t[t.Smooth=2]="Smooth",t[t.Phase=3]="Phase",t[t.Expand=4]="Expand",t[t.Solid=5]="Solid"})(Kr||(Kr={}));var es;(function(t){t[t.Line=1]="Line",t[t.Block=2]="Block",t[t.Underline=3]="Underline",t[t.LineThin=4]="LineThin",t[t.BlockOutline=5]="BlockOutline",t[t.UnderlineThin=6]="UnderlineThin"})(es||(es={}));var ts;(function(t){t[t.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",t[t.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",t[t.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",t[t.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(ts||(ts={}));var ns;(function(t){t[t.None=0]="None",t[t.Same=1]="Same",t[t.Indent=2]="Indent",t[t.DeepIndent=3]="DeepIndent"})(ns||(ns={}));const je=class je{static chord(e,n){return oa(e,n)}};je.CtrlCmd=2048,je.Shift=1024,je.Alt=512,je.WinCtrl=256;let mn=je;function ma(){return{editor:void 0,languages:void 0,CancellationTokenSource:ra,Emitter:oe,KeyCode:un,KeyMod:mn,Position:U,Range:k,Selection:ae,SelectionDirection:fn,MarkerSeverity:cn,MarkerTag:hn,Uri:re,Token:fa}}const lt=class lt{static getChannel(e){return e.getChannel(lt.CHANNEL_NAME)}static setChannel(e,n){e.setChannel(lt.CHANNEL_NAME,n)}};lt.CHANNEL_NAME="editorWorkerHost";let dn=lt;var rs;class da{constructor(){this[rs]="LinkedMap",this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0,this._state=0}clear(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0,this._state++}isEmpty(){return!this._head&&!this._tail}get size(){return this._size}get first(){return this._head?.value}get last(){return this._tail?.value}has(e){return this._map.has(e)}get(e,n=0){const r=this._map.get(e);if(r)return n!==0&&this.touch(r,n),r.value}set(e,n,r=0){let s=this._map.get(e);if(s)s.value=n,r!==0&&this.touch(s,r);else{switch(s={key:e,value:n,next:void 0,previous:void 0},r){case 0:this.addItemLast(s);break;case 1:this.addItemFirst(s);break;case 2:this.addItemLast(s);break;default:this.addItemLast(s);break}this._map.set(e,s),this._size++}return this}delete(e){return!!this.remove(e)}remove(e){const n=this._map.get(e);if(n)return this._map.delete(e),this.removeItem(n),this._size--,n.value}shift(){if(!this._head&&!this._tail)return;if(!this._head||!this._tail)throw new Error("Invalid list");const e=this._head;return this._map.delete(e.key),this.removeItem(e),this._size--,e.value}forEach(e,n){const r=this._state;let s=this._head;for(;s;){if(n?e.bind(n)(s.value,s.key,this):e(s.value,s.key,this),this._state!==r)throw new Error("LinkedMap got modified during iteration.");s=s.next}}keys(){const e=this,n=this._state;let r=this._head;const s={[Symbol.iterator](){return s},next(){if(e._state!==n)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:r.key,done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return s}values(){const e=this,n=this._state;let r=this._head;const s={[Symbol.iterator](){return s},next(){if(e._state!==n)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:r.value,done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return s}entries(){const e=this,n=this._state;let r=this._head;const s={[Symbol.iterator](){return s},next(){if(e._state!==n)throw new Error("LinkedMap got modified during iteration.");if(r){const i={value:[r.key,r.value],done:!1};return r=r.next,i}else return{value:void 0,done:!0}}};return s}[(rs=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}trimOld(e){if(e>=this.size)return;if(e===0){this.clear();return}let n=this._head,r=this.size;for(;n&&r>e;)this._map.delete(n.key),n=n.next,r--;this._head=n,this._size=r,n&&(n.previous=void 0),this._state++}trimNew(e){if(e>=this.size)return;if(e===0){this.clear();return}let n=this._tail,r=this.size;for(;n&&r>e;)this._map.delete(n.key),n=n.previous,r--;this._tail=n,this._size=r,n&&(n.next=void 0),this._state++}addItemFirst(e){if(!this._head&&!this._tail)this._tail=e;else if(this._head)e.next=this._head,this._head.previous=e;else throw new Error("Invalid list");this._head=e,this._state++}addItemLast(e){if(!this._head&&!this._tail)this._head=e;else if(this._tail)e.previous=this._tail,this._tail.next=e;else throw new Error("Invalid list");this._tail=e,this._state++}removeItem(e){if(e===this._head&&e===this._tail)this._head=void 0,this._tail=void 0;else if(e===this._head){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this._head=e.next}else if(e===this._tail){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this._tail=e.previous}else{const n=e.next,r=e.previous;if(!n||!r)throw new Error("Invalid list");n.previous=r,r.next=n}e.next=void 0,e.previous=void 0,this._state++}touch(e,n){if(!this._head||!this._tail)throw new Error("Invalid list");if(!(n!==1&&n!==2)){if(n===1){if(e===this._head)return;const r=e.next,s=e.previous;e===this._tail?(s.next=void 0,this._tail=s):(r.previous=s,s.next=r),e.previous=void 0,e.next=this._head,this._head.previous=e,this._head=e,this._state++}else if(n===2){if(e===this._tail)return;const r=e.next,s=e.previous;e===this._head?(r.previous=void 0,this._head=r):(r.previous=s,s.next=r),e.next=void 0,e.previous=this._tail,this._tail.next=e,this._tail=e,this._state++}}}toJSON(){const e=[];return this.forEach((n,r)=>{e.push([r,n])}),e}fromJSON(e){this.clear();for(const[n,r]of e)this.set(n,r)}}class ga extends da{constructor(e,n=1){super(),this._limit=e,this._ratio=Math.min(Math.max(0,n),1)}get limit(){return this._limit}set limit(e){this._limit=e,this.checkTrim()}get(e,n=2){return super.get(e,n)}peek(e){return super.get(e,0)}set(e,n){return super.set(e,n,2),this}checkTrim(){this.size>this._limit&&this.trim(Math.round(this._limit*this._ratio))}}class pa extends ga{constructor(e,n=1){super(e,n)}trim(e){this.trimOld(e)}set(e,n){return super.set(e,n),this.checkTrim(),this}}class ba{constructor(){this.map=new Map}add(e,n){let r=this.map.get(e);r||(r=new Set,this.map.set(e,r)),r.add(n)}delete(e,n){const r=this.map.get(e);r&&(r.delete(n),r.size===0&&this.map.delete(e))}forEach(e,n){const r=this.map.get(e);r&&r.forEach(n)}get(e){const n=this.map.get(e);return n||new Set}}new pa(10);function ya(t){let e=[];for(;Object.prototype!==t;)e=e.concat(Object.getOwnPropertyNames(t)),t=Object.getPrototypeOf(t);return e}function ss(t){const e=[];for(const n of ya(t))typeof t[n]=="function"&&e.push(n);return e}function xa(t,e){const n=s=>function(){const i=Array.prototype.slice.call(arguments,0);return e(s,i)},r={};for(const s of t)r[s]=n(s);return r}var is;(function(t){t[t.Left=1]="Left",t[t.Center=2]="Center",t[t.Right=4]="Right",t[t.Full=7]="Full"})(is||(is={}));var as;(function(t){t[t.Left=1]="Left",t[t.Center=2]="Center",t[t.Right=3]="Right"})(as||(as={}));var os;(function(t){t[t.Both=0]="Both",t[t.Right=1]="Right",t[t.Left=2]="Left",t[t.None=3]="None"})(os||(os={}));function _a(t,e,n,r,s){if(r===0)return!0;const i=e.charCodeAt(r-1);if(t.get(i)!==0||i===13||i===10)return!0;if(s>0){const o=e.charCodeAt(r);if(t.get(o)!==0)return!0}return!1}function wa(t,e,n,r,s){if(r+s===n)return!0;const i=e.charCodeAt(r+s);if(t.get(i)!==0||i===13||i===10)return!0;if(s>0){const o=e.charCodeAt(r+s-1);if(t.get(o)!==0)return!0}return!1}function La(t,e,n,r,s){return _a(t,e,n,r,s)&&wa(t,e,n,r,s)}class va{constructor(e,n){this._wordSeparators=e,this._searchRegex=n,this._prevMatchStartIndex=-1,this._prevMatchLength=0}reset(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0}next(e){const n=e.length;let r;do{if(this._prevMatchStartIndex+this._prevMatchLength===n||(r=this._searchRegex.exec(e),!r))return null;const s=r.index,i=r[0].length;if(s===this._prevMatchStartIndex&&i===this._prevMatchLength){if(i===0){bi(e,n,this._searchRegex.lastIndex)>65535?this._searchRegex.lastIndex+=2:this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=s,this._prevMatchLength=i,!this._wordSeparators||La(this._wordSeparators,e,n,s,i))return r}while(r);return null}}function Na(t,e="Unreachable"){throw new Error(e)}function vt(t){if(!t()){debugger;t(),Ye(new se("Assertion Failed"))}}function ls(t,e){let n=0;for(;n<t.length-1;){const r=t[n],s=t[n+1];if(!e(r,s))return!1;n++}return!0}const Sa="`~!@#$%^&*()-=+[{]}\\\\|;:\'\\",.<>/?";function Ca(t=""){let e="(-?\\\\d*\\\\.\\\\d\\\\w*)|([^";for(const n of Sa)t.indexOf(n)>=0||(e+="\\\\"+n);return e+="\\\\s]+)",new RegExp(e,"g")}const us=Ca();function cs(t){let e=us;if(t&&t instanceof RegExp)if(t.global)e=t;else{let n="g";t.ignoreCase&&(n+="i"),t.multiline&&(n+="m"),t.unicode&&(n+="u"),e=new RegExp(t.source,n)}return e.lastIndex=0,e}const hs=new Os;hs.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function gn(t,e,n,r,s){if(e=cs(e),s||(s=ht.first(hs)),n.length>s.maxLen){let c=t-s.maxLen/2;return c<0?c=0:r+=c,n=n.substring(c,t+s.maxLen/2),gn(t,e,n,r,s)}const i=Date.now(),o=t-1-r;let l=-1,u=null;for(let c=1;!(Date.now()-i>=s.timeBudget);c++){const f=o-s.windowSize*c;e.lastIndex=Math.max(0,f);const h=Aa(e,n,o,l);if(!h&&u||(u=h,f<=0))break;l=f}if(u){const c={word:u[0],startColumn:r+1+u.index,endColumn:r+1+u.index+u[0].length};return e.lastIndex=0,c}return null}function Aa(t,e,n,r){let s;for(;s=t.exec(e);){const i=s.index||0;if(i<=n&&t.lastIndex>=n)return s;if(r>0&&i>r)return null}return null}class Ra{static computeUnicodeHighlights(e,n,r){const s=r?r.startLineNumber:1,i=r?r.endLineNumber:e.getLineCount(),o=new fs(n),l=o.getCandidateCodePoints();let u;l==="allNonBasicAscii"?u=new RegExp("[^\\\\t\\\\n\\\\r\\\\x20-\\\\x7E]","g"):u=new RegExp(`${Ea(Array.from(l))}`,"g");const c=new va(null,u),f=[];let h=!1,m,d=0,g=0,p=0;e:for(let x=s,L=i;x<=L;x++){const N=e.getLineContent(x),v=N.length;c.reset(0);do if(m=c.next(N),m){let y=m.index,b=m.index+m[0].length;if(y>0){const B=N.charCodeAt(y-1);Qt(B)&&y--}if(b+1<v){const B=N.charCodeAt(b-1);Qt(B)&&b++}const w=N.substring(y,b);let C=gn(y+1,us,N,0);C&&C.endColumn<=y+1&&(C=null);const R=o.shouldHighlightNonBasicASCII(w,C?C.word:null);if(R!==0){if(R===3?d++:R===2?g++:R===1?p++:Na(),f.length>=1e3){h=!0;break e}f.push(new k(x,y+1,x,b+1))}}while(m)}return{ranges:f,hasMore:h,ambiguousCharacterCount:d,invisibleCharacterCount:g,nonBasicAsciiCharacterCount:p}}static computeUnicodeHighlightReason(e,n){const r=new fs(n);switch(r.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const i=e.codePointAt(0),o=r.ambiguousCharacters.getPrimaryConfusable(i),l=Ke.getLocales().filter(u=>!Ke.getInstance(new Set([...n.allowedLocales,u])).isAmbiguous(i));return{kind:0,confusableWith:String.fromCodePoint(o),notAmbiguousInLocales:l}}case 1:return{kind:2}}}}function Ea(t,e){return`[${hi(t.map(r=>String.fromCodePoint(r)).join(""))}]`}class fs{constructor(e){this.options=e,this.allowedCodePoints=new Set(e.allowedCodePoints),this.ambiguousCharacters=Ke.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.options.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.options.invisibleCharacters)for(const n of et.codePoints)ms(String.fromCodePoint(n))||e.add(n);if(this.options.ambiguousCharacters)for(const n of this.ambiguousCharacters.getConfusableCodePoints())e.add(n);for(const n of this.allowedCodePoints)e.delete(n);return e}shouldHighlightNonBasicASCII(e,n){const r=e.codePointAt(0);if(this.allowedCodePoints.has(r))return 0;if(this.options.nonBasicASCII)return 1;let s=!1,i=!1;if(n)for(const o of n){const l=o.codePointAt(0),u=xi(o);s=s||u,!u&&!this.ambiguousCharacters.isAmbiguous(l)&&!et.isInvisibleCharacter(l)&&(i=!0)}return!s&&i?0:this.options.invisibleCharacters&&!ms(e)&&et.isInvisibleCharacter(r)?2:this.options.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(r)?3:0}}function ms(t){return t===" "||t===`\n`||t==="	"}class Nt{constructor(e,n,r){this.changes=e,this.moves=n,this.hitTimeout=r}}class Ma{constructor(e,n){this.lineRangeMapping=e,this.changes=n}}class V{static addRange(e,n){let r=0;for(;r<n.length&&n[r].endExclusive<e.start;)r++;let s=r;for(;s<n.length&&n[s].start<=e.endExclusive;)s++;if(r===s)n.splice(r,0,e);else{const i=Math.min(e.start,n[r].start),o=Math.max(e.endExclusive,n[s-1].endExclusive);n.splice(r,s-r,new V(i,o))}}static tryCreate(e,n){if(!(e>n))return new V(e,n)}static ofLength(e){return new V(0,e)}static ofStartAndLength(e,n){return new V(e,e+n)}constructor(e,n){if(this.start=e,this.endExclusive=n,e>n)throw new se(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(e){return new V(this.start+e,this.endExclusive+e)}deltaStart(e){return new V(this.start+e,this.endExclusive)}deltaEnd(e){return new V(this.start,this.endExclusive+e)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}contains(e){return this.start<=e&&e<this.endExclusive}join(e){return new V(Math.min(this.start,e.start),Math.max(this.endExclusive,e.endExclusive))}intersect(e){const n=Math.max(this.start,e.start),r=Math.min(this.endExclusive,e.endExclusive);if(n<=r)return new V(n,r)}intersects(e){const n=Math.max(this.start,e.start),r=Math.min(this.endExclusive,e.endExclusive);return n<r}isBefore(e){return this.endExclusive<=e.start}isAfter(e){return this.start>=e.endExclusive}slice(e){return e.slice(this.start,this.endExclusive)}substring(e){return e.substring(this.start,this.endExclusive)}clip(e){if(this.isEmpty)throw new se(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,e))}clipCyclic(e){if(this.isEmpty)throw new se(`Invalid clipping range: ${this.toString()}`);return e<this.start?this.endExclusive-(this.start-e)%this.length:e>=this.endExclusive?this.start+(e-this.start)%this.length:e}forEach(e){for(let n=this.start;n<this.endExclusive;n++)e(n)}}function qe(t,e){const n=nt(t,e);return n===-1?void 0:t[n]}function nt(t,e,n=0,r=t.length){let s=n,i=r;for(;s<i;){const o=Math.floor((s+i)/2);e(t[o])?s=o+1:i=o}return s-1}function ka(t,e){const n=pn(t,e);return n===t.length?void 0:t[n]}function pn(t,e,n=0,r=t.length){let s=n,i=r;for(;s<i;){const o=Math.floor((s+i)/2);e(t[o])?i=o:s=o+1}return s}const Dt=class Dt{constructor(e){this._array=e,this._findLastMonotonousLastIdx=0}findLastMonotonous(e){if(Dt.assertInvariants){if(this._prevFindLastPredicate){for(const r of this._array)if(this._prevFindLastPredicate(r)&&!e(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this._prevFindLastPredicate=e}const n=nt(this._array,e,this._findLastMonotonousLastIdx);return this._findLastMonotonousLastIdx=n+1,n===-1?void 0:this._array[n]}};Dt.assertInvariants=!1;let St=Dt;class D{static fromRangeInclusive(e){return new D(e.startLineNumber,e.endLineNumber+1)}static joinMany(e){if(e.length===0)return[];let n=new de(e[0].slice());for(let r=1;r<e.length;r++)n=n.getUnion(new de(e[r].slice()));return n.ranges}static join(e){if(e.length===0)throw new se("lineRanges cannot be empty");let n=e[0].startLineNumber,r=e[0].endLineNumberExclusive;for(let s=1;s<e.length;s++)n=Math.min(n,e[s].startLineNumber),r=Math.max(r,e[s].endLineNumberExclusive);return new D(n,r)}static ofLength(e,n){return new D(e,e+n)}static deserialize(e){return new D(e[0],e[1])}constructor(e,n){if(e>n)throw new se(`startLineNumber ${e} cannot be after endLineNumberExclusive ${n}`);this.startLineNumber=e,this.endLineNumberExclusive=n}contains(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(e){return new D(this.startLineNumber+e,this.endLineNumberExclusive+e)}deltaLength(e){return new D(this.startLineNumber,this.endLineNumberExclusive+e)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(e){return new D(Math.min(this.startLineNumber,e.startLineNumber),Math.max(this.endLineNumberExclusive,e.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(e){const n=Math.max(this.startLineNumber,e.startLineNumber),r=Math.min(this.endLineNumberExclusive,e.endLineNumberExclusive);if(n<=r)return new D(n,r)}intersectsStrict(e){return this.startLineNumber<e.endLineNumberExclusive&&e.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(e){return this.startLineNumber<=e.endLineNumberExclusive&&e.startLineNumber<=this.endLineNumberExclusive}equals(e){return this.startLineNumber===e.startLineNumber&&this.endLineNumberExclusive===e.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new k(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new k(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(e){const n=[];for(let r=this.startLineNumber;r<this.endLineNumberExclusive;r++)n.push(e(r));return n}forEach(e){for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)e(n)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(e){return this.startLineNumber<=e&&e<this.endLineNumberExclusive}toOffsetRange(){return new V(this.startLineNumber-1,this.endLineNumberExclusive-1)}}class de{constructor(e=[]){this._normalizedRanges=e}get ranges(){return this._normalizedRanges}addRange(e){if(e.length===0)return;const n=pn(this._normalizedRanges,s=>s.endLineNumberExclusive>=e.startLineNumber),r=nt(this._normalizedRanges,s=>s.startLineNumber<=e.endLineNumberExclusive)+1;if(n===r)this._normalizedRanges.splice(n,0,e);else if(n===r-1){const s=this._normalizedRanges[n];this._normalizedRanges[n]=s.join(e)}else{const s=this._normalizedRanges[n].join(this._normalizedRanges[r-1]).join(e);this._normalizedRanges.splice(n,r-n,s)}}contains(e){const n=qe(this._normalizedRanges,r=>r.startLineNumber<=e);return!!n&&n.endLineNumberExclusive>e}intersects(e){const n=qe(this._normalizedRanges,r=>r.startLineNumber<e.endLineNumberExclusive);return!!n&&n.endLineNumberExclusive>e.startLineNumber}getUnion(e){if(this._normalizedRanges.length===0)return e;if(e._normalizedRanges.length===0)return this;const n=[];let r=0,s=0,i=null;for(;r<this._normalizedRanges.length||s<e._normalizedRanges.length;){let o=null;if(r<this._normalizedRanges.length&&s<e._normalizedRanges.length){const l=this._normalizedRanges[r],u=e._normalizedRanges[s];l.startLineNumber<u.startLineNumber?(o=l,r++):(o=u,s++)}else r<this._normalizedRanges.length?(o=this._normalizedRanges[r],r++):(o=e._normalizedRanges[s],s++);i===null?i=o:i.endLineNumberExclusive>=o.startLineNumber?i=new D(i.startLineNumber,Math.max(i.endLineNumberExclusive,o.endLineNumberExclusive)):(n.push(i),i=o)}return i!==null&&n.push(i),new de(n)}subtractFrom(e){const n=pn(this._normalizedRanges,o=>o.endLineNumberExclusive>=e.startLineNumber),r=nt(this._normalizedRanges,o=>o.startLineNumber<=e.endLineNumberExclusive)+1;if(n===r)return new de([e]);const s=[];let i=e.startLineNumber;for(let o=n;o<r;o++){const l=this._normalizedRanges[o];l.startLineNumber>i&&s.push(new D(i,l.startLineNumber)),i=l.endLineNumberExclusive}return i<e.endLineNumberExclusive&&s.push(new D(i,e.endLineNumberExclusive)),new de(s)}toString(){return this._normalizedRanges.map(e=>e.toString()).join(", ")}getIntersection(e){const n=[];let r=0,s=0;for(;r<this._normalizedRanges.length&&s<e._normalizedRanges.length;){const i=this._normalizedRanges[r],o=e._normalizedRanges[s],l=i.intersect(o);l&&!l.isEmpty&&n.push(l),i.endLineNumberExclusive<o.endLineNumberExclusive?r++:s++}return new de(n)}getWithDelta(e){return new de(this._normalizedRanges.map(n=>n.delta(e)))}}const Ee=class Ee{static betweenPositions(e,n){return e.lineNumber===n.lineNumber?new Ee(0,n.column-e.column):new Ee(n.lineNumber-e.lineNumber,n.column-1)}static ofRange(e){return Ee.betweenPositions(e.getStartPosition(),e.getEndPosition())}static ofText(e){let n=0,r=0;for(const s of e)s===`\n`?(n++,r=0):r++;return new Ee(n,r)}constructor(e,n){this.lineCount=e,this.columnCount=n}isGreaterThanOrEqualTo(e){return this.lineCount!==e.lineCount?this.lineCount>e.lineCount:this.columnCount>=e.columnCount}createRange(e){return this.lineCount===0?new k(e.lineNumber,e.column,e.lineNumber,e.column+this.columnCount):new k(e.lineNumber,e.column,e.lineNumber+this.lineCount,this.columnCount+1)}addToPosition(e){return this.lineCount===0?new U(e.lineNumber,e.column+this.columnCount):new U(e.lineNumber+this.lineCount,this.columnCount+1)}toString(){return`${this.lineCount},${this.columnCount}`}};Ee.zero=new Ee(0,0);let ds=Ee;class Pa{constructor(e,n){this.range=e,this.text=n}toSingleEditOperation(){return{range:this.range,text:this.text}}}class le{static inverse(e,n,r){const s=[];let i=1,o=1;for(const u of e){const c=new le(new D(i,u.original.startLineNumber),new D(o,u.modified.startLineNumber));c.modified.isEmpty||s.push(c),i=u.original.endLineNumberExclusive,o=u.modified.endLineNumberExclusive}const l=new le(new D(i,n+1),new D(o,r+1));return l.modified.isEmpty||s.push(l),s}static clip(e,n,r){const s=[];for(const i of e){const o=i.original.intersect(n),l=i.modified.intersect(r);o&&!o.isEmpty&&l&&!l.isEmpty&&s.push(new le(o,l))}return s}constructor(e,n){this.original=e,this.modified=n}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new le(this.modified,this.original)}join(e){return new le(this.original.join(e.original),this.modified.join(e.modified))}toRangeMapping(){const e=this.original.toInclusiveRange(),n=this.modified.toInclusiveRange();if(e&&n)return new fe(e,n);if(this.original.startLineNumber===1||this.modified.startLineNumber===1){if(!(this.modified.startLineNumber===1&&this.original.startLineNumber===1))throw new se("not a valid diff");return new fe(new k(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new k(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1))}else return new fe(new k(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),new k(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER))}toRangeMapping2(e,n){if(gs(this.original.endLineNumberExclusive,e)&&gs(this.modified.endLineNumberExclusive,n))return new fe(new k(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new k(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1));if(!this.original.isEmpty&&!this.modified.isEmpty)return new fe(k.fromPositions(new U(this.original.startLineNumber,1),Ue(new U(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),e)),k.fromPositions(new U(this.modified.startLineNumber,1),Ue(new U(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),n)));if(this.original.startLineNumber>1&&this.modified.startLineNumber>1)return new fe(k.fromPositions(Ue(new U(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER),e),Ue(new U(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),e)),k.fromPositions(Ue(new U(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER),n),Ue(new U(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),n)));throw new se}}function Ue(t,e){if(t.lineNumber<1)return new U(1,1);if(t.lineNumber>e.length)return new U(e.length,e[e.length-1].length+1);const n=e[t.lineNumber-1];return t.column>n.length+1?new U(t.lineNumber,n.length+1):t}function gs(t,e){return t>=1&&t<=e.length}class xe extends le{static fromRangeMappings(e){const n=D.join(e.map(s=>D.fromRangeInclusive(s.originalRange))),r=D.join(e.map(s=>D.fromRangeInclusive(s.modifiedRange)));return new xe(n,r,e)}constructor(e,n,r){super(e,n),this.innerChanges=r}flip(){return new xe(this.modified,this.original,this.innerChanges?.map(e=>e.flip()))}withInnerChangesFromLineRanges(){return new xe(this.original,this.modified,[this.toRangeMapping()])}}class fe{static assertSorted(e){for(let n=1;n<e.length;n++){const r=e[n-1],s=e[n];if(!(r.originalRange.getEndPosition().isBeforeOrEqual(s.originalRange.getStartPosition())&&r.modifiedRange.getEndPosition().isBeforeOrEqual(s.modifiedRange.getStartPosition())))throw new se("Range mappings must be sorted")}}constructor(e,n){this.originalRange=e,this.modifiedRange=n}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new fe(this.modifiedRange,this.originalRange)}toTextEdit(e){const n=e.getValueOfRange(this.modifiedRange);return new Pa(this.originalRange,n)}}const Fa=3;class Da{computeDiff(e,n,r){const i=new Va(e,n,{maxComputationTime:r.maxComputationTimeMs,shouldIgnoreTrimWhitespace:r.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),o=[];let l=null;for(const u of i.changes){let c;u.originalEndLineNumber===0?c=new D(u.originalStartLineNumber+1,u.originalStartLineNumber+1):c=new D(u.originalStartLineNumber,u.originalEndLineNumber+1);let f;u.modifiedEndLineNumber===0?f=new D(u.modifiedStartLineNumber+1,u.modifiedStartLineNumber+1):f=new D(u.modifiedStartLineNumber,u.modifiedEndLineNumber+1);let h=new xe(c,f,u.charChanges?.map(m=>new fe(new k(m.originalStartLineNumber,m.originalStartColumn,m.originalEndLineNumber,m.originalEndColumn),new k(m.modifiedStartLineNumber,m.modifiedStartColumn,m.modifiedEndLineNumber,m.modifiedEndColumn))));l&&(l.modified.endLineNumberExclusive===h.modified.startLineNumber||l.original.endLineNumberExclusive===h.original.startLineNumber)&&(h=new xe(l.original.join(h.original),l.modified.join(h.modified),l.innerChanges&&h.innerChanges?l.innerChanges.concat(h.innerChanges):void 0),o.pop()),o.push(h),l=h}return vt(()=>ls(o,(u,c)=>c.original.startLineNumber-u.original.endLineNumberExclusive===c.modified.startLineNumber-u.modified.endLineNumberExclusive&&u.original.endLineNumberExclusive<c.original.startLineNumber&&u.modified.endLineNumberExclusive<c.modified.startLineNumber)),new Nt(o,[],i.quitEarly)}}function ps(t,e,n,r){return new Ae(t,e,n).ComputeDiff(r)}let bs=class{constructor(e){const n=[],r=[];for(let s=0,i=e.length;s<i;s++)n[s]=bn(e[s],1),r[s]=yn(e[s],1);this.lines=e,this._startColumns=n,this._endColumns=r}getElements(){const e=[];for(let n=0,r=this.lines.length;n<r;n++)e[n]=this.lines[n].substring(this._startColumns[n]-1,this._endColumns[n]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,n,r){const s=[],i=[],o=[];let l=0;for(let u=n;u<=r;u++){const c=this.lines[u],f=e?this._startColumns[u]:1,h=e?this._endColumns[u]:c.length+1;for(let m=f;m<h;m++)s[l]=c.charCodeAt(m-1),i[l]=u+1,o[l]=m,l++;!e&&u<r&&(s[l]=10,i[l]=u+1,o[l]=c.length+1,l++)}return new Ta(s,i,o)}};class Ta{constructor(e,n,r){this._charCodes=e,this._lineNumbers=n,this._columns=r}toString(){return"["+this._charCodes.map((e,n)=>(e===10?"\\\\n":String.fromCharCode(e))+`-(${this._lineNumbers[n]},${this._columns[n]})`).join(", ")+"]"}_assertIndex(e,n){if(e<0||e>=n.length)throw new Error("Illegal index")}getElements(){return this._charCodes}getStartLineNumber(e){return e>0&&e===this._lineNumbers.length?this.getEndLineNumber(e-1):(this._assertIndex(e,this._lineNumbers),this._lineNumbers[e])}getEndLineNumber(e){return e===-1?this.getStartLineNumber(e+1):(this._assertIndex(e,this._lineNumbers),this._charCodes[e]===10?this._lineNumbers[e]+1:this._lineNumbers[e])}getStartColumn(e){return e>0&&e===this._columns.length?this.getEndColumn(e-1):(this._assertIndex(e,this._columns),this._columns[e])}getEndColumn(e){return e===-1?this.getStartColumn(e+1):(this._assertIndex(e,this._columns),this._charCodes[e]===10?1:this._columns[e]+1)}}class $e{constructor(e,n,r,s,i,o,l,u){this.originalStartLineNumber=e,this.originalStartColumn=n,this.originalEndLineNumber=r,this.originalEndColumn=s,this.modifiedStartLineNumber=i,this.modifiedStartColumn=o,this.modifiedEndLineNumber=l,this.modifiedEndColumn=u}static createFromDiffChange(e,n,r){const s=n.getStartLineNumber(e.originalStart),i=n.getStartColumn(e.originalStart),o=n.getEndLineNumber(e.originalStart+e.originalLength-1),l=n.getEndColumn(e.originalStart+e.originalLength-1),u=r.getStartLineNumber(e.modifiedStart),c=r.getStartColumn(e.modifiedStart),f=r.getEndLineNumber(e.modifiedStart+e.modifiedLength-1),h=r.getEndColumn(e.modifiedStart+e.modifiedLength-1);return new $e(s,i,o,l,u,c,f,h)}}function Ia(t){if(t.length<=1)return t;const e=[t[0]];let n=e[0];for(let r=1,s=t.length;r<s;r++){const i=t[r],o=i.originalStart-(n.originalStart+n.originalLength),l=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(o,l)<Fa?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(e.push(i),n=i)}return e}class rt{constructor(e,n,r,s,i){this.originalStartLineNumber=e,this.originalEndLineNumber=n,this.modifiedStartLineNumber=r,this.modifiedEndLineNumber=s,this.charChanges=i}static createFromDiffResult(e,n,r,s,i,o,l){let u,c,f,h,m;if(n.originalLength===0?(u=r.getStartLineNumber(n.originalStart)-1,c=0):(u=r.getStartLineNumber(n.originalStart),c=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(f=s.getStartLineNumber(n.modifiedStart)-1,h=0):(f=s.getStartLineNumber(n.modifiedStart),h=s.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),o&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&i()){const d=r.createCharSequence(e,n.originalStart,n.originalStart+n.originalLength-1),g=s.createCharSequence(e,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(d.getElements().length>0&&g.getElements().length>0){let p=ps(d,g,i,!0).changes;l&&(p=Ia(p)),m=[];for(let x=0,L=p.length;x<L;x++)m.push($e.createFromDiffChange(p[x],d,g))}}return new rt(u,c,f,h,m)}}class Va{constructor(e,n,r){this.shouldComputeCharChanges=r.shouldComputeCharChanges,this.shouldPostProcessCharChanges=r.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=r.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=r.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=n,this.original=new bs(e),this.modified=new bs(n),this.continueLineDiff=ys(r.maxComputationTime),this.continueCharDiff=ys(r.maxComputationTime===0?0:Math.min(r.maxComputationTime,5e3))}computeDiff(){if(this.original.lines.length===1&&this.original.lines[0].length===0)return this.modified.lines.length===1&&this.modified.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:void 0}]};if(this.modified.lines.length===1&&this.modified.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=ps(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),n=e.changes,r=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){const l=[];for(let u=0,c=n.length;u<c;u++)l.push(rt.createFromDiffResult(this.shouldIgnoreTrimWhitespace,n[u],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:r,changes:l}}const s=[];let i=0,o=0;for(let l=-1,u=n.length;l<u;l++){const c=l+1<u?n[l+1]:null,f=c?c.originalStart:this.originalLines.length,h=c?c.modifiedStart:this.modifiedLines.length;for(;i<f&&o<h;){const m=this.originalLines[i],d=this.modifiedLines[o];if(m!==d){{let g=bn(m,1),p=bn(d,1);for(;g>1&&p>1;){const x=m.charCodeAt(g-2),L=d.charCodeAt(p-2);if(x!==L)break;g--,p--}(g>1||p>1)&&this._pushTrimWhitespaceCharChange(s,i+1,1,g,o+1,1,p)}{let g=yn(m,1),p=yn(d,1);const x=m.length+1,L=d.length+1;for(;g<x&&p<L;){const N=m.charCodeAt(g-1),v=m.charCodeAt(p-1);if(N!==v)break;g++,p++}(g<x||p<L)&&this._pushTrimWhitespaceCharChange(s,i+1,g,x,o+1,p,L)}}i++,o++}c&&(s.push(rt.createFromDiffResult(this.shouldIgnoreTrimWhitespace,c,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),i+=c.originalLength,o+=c.modifiedLength)}return{quitEarly:r,changes:s}}_pushTrimWhitespaceCharChange(e,n,r,s,i,o,l){if(this._mergeTrimWhitespaceCharChange(e,n,r,s,i,o,l))return;let u;this.shouldComputeCharChanges&&(u=[new $e(n,r,n,s,i,o,i,l)]),e.push(new rt(n,n,i,i,u))}_mergeTrimWhitespaceCharChange(e,n,r,s,i,o,l){const u=e.length;if(u===0)return!1;const c=e[u-1];return c.originalEndLineNumber===0||c.modifiedEndLineNumber===0?!1:c.originalEndLineNumber===n&&c.modifiedEndLineNumber===i?(this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new $e(n,r,n,s,i,o,i,l)),!0):c.originalEndLineNumber+1===n&&c.modifiedEndLineNumber+1===i?(c.originalEndLineNumber=n,c.modifiedEndLineNumber=i,this.shouldComputeCharChanges&&c.charChanges&&c.charChanges.push(new $e(n,r,n,s,i,o,i,l)),!0):!1}}function bn(t,e){const n=mi(t);return n===-1?e:n+1}function yn(t,e){const n=di(t);return n===-1?e:n+2}function ys(t){if(t===0)return()=>!0;const e=Date.now();return()=>Date.now()-e<t}function Ba(t,e,n=(r,s)=>r===s){if(t===e)return!0;if(!t||!e||t.length!==e.length)return!1;for(let r=0,s=t.length;r<s;r++)if(!n(t[r],e[r]))return!1;return!0}function*qa(t,e){let n,r;for(const s of t)r!==void 0&&e(r,s)?n.push(s):(n&&(yield n),n=[s]),r=s;n&&(yield n)}function Ua(t,e){for(let n=0;n<=t.length;n++)e(n===0?void 0:t[n-1],n===t.length?void 0:t[n])}function $a(t,e){for(let n=0;n<t.length;n++)e(n===0?void 0:t[n-1],t[n],n+1===t.length?void 0:t[n+1])}function Ha(t,e){for(const n of e)t.push(n)}var xn;(function(t){function e(i){return i<0}t.isLessThan=e;function n(i){return i<=0}t.isLessThanOrEqual=n;function r(i){return i>0}t.isGreaterThan=r;function s(i){return i===0}t.isNeitherLessOrGreaterThan=s,t.greaterThan=1,t.lessThan=-1,t.neitherLessOrGreaterThan=0})(xn||(xn={}));function Ct(t,e){return(n,r)=>e(t(n),t(r))}const At=(t,e)=>t-e;function Wa(t){return(e,n)=>-t(e,n)}const Xe=class Xe{constructor(e){this.iterate=e}toArray(){const e=[];return this.iterate(n=>(e.push(n),!0)),e}filter(e){return new Xe(n=>this.iterate(r=>e(r)?n(r):!0))}map(e){return new Xe(n=>this.iterate(r=>n(e(r))))}findLast(e){let n;return this.iterate(r=>(e(r)&&(n=r),!0)),n}findLastMaxBy(e){let n,r=!0;return this.iterate(s=>((r||xn.isGreaterThan(e(s,n)))&&(r=!1,n=s),!0)),n}};Xe.empty=new Xe(e=>{});let xs=Xe;class _e{static trivial(e,n){return new _e([new G(V.ofLength(e.length),V.ofLength(n.length))],!1)}static trivialTimedOut(e,n){return new _e([new G(V.ofLength(e.length),V.ofLength(n.length))],!0)}constructor(e,n){this.diffs=e,this.hitTimeout=n}}class G{static invert(e,n){const r=[];return Ua(e,(s,i)=>{r.push(G.fromOffsetPairs(s?s.getEndExclusives():we.zero,i?i.getStarts():new we(n,(s?s.seq2Range.endExclusive-s.seq1Range.endExclusive:0)+n)))}),r}static fromOffsetPairs(e,n){return new G(new V(e.offset1,n.offset1),new V(e.offset2,n.offset2))}static assertSorted(e){let n;for(const r of e){if(n&&!(n.seq1Range.endExclusive<=r.seq1Range.start&&n.seq2Range.endExclusive<=r.seq2Range.start))throw new se("Sequence diffs must be sorted");n=r}}constructor(e,n){this.seq1Range=e,this.seq2Range=n}swap(){return new G(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(e){return new G(this.seq1Range.join(e.seq1Range),this.seq2Range.join(e.seq2Range))}delta(e){return e===0?this:new G(this.seq1Range.delta(e),this.seq2Range.delta(e))}deltaStart(e){return e===0?this:new G(this.seq1Range.deltaStart(e),this.seq2Range.deltaStart(e))}deltaEnd(e){return e===0?this:new G(this.seq1Range.deltaEnd(e),this.seq2Range.deltaEnd(e))}intersect(e){const n=this.seq1Range.intersect(e.seq1Range),r=this.seq2Range.intersect(e.seq2Range);if(!(!n||!r))return new G(n,r)}getStarts(){return new we(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new we(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}}const Me=class Me{constructor(e,n){this.offset1=e,this.offset2=n}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(e){return e===0?this:new Me(this.offset1+e,this.offset2+e)}equals(e){return this.offset1===e.offset1&&this.offset2===e.offset2}};Me.zero=new Me(0,0),Me.max=new Me(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER);let we=Me;const Tt=class Tt{isValid(){return!0}};Tt.instance=new Tt;let st=Tt;class za{constructor(e){if(this.timeout=e,this.startTime=Date.now(),this.valid=!0,e<=0)throw new se("timeout must be positive")}isValid(){if(!(Date.now()-this.startTime<this.timeout)&&this.valid){this.valid=!1;debugger}return this.valid}}class _n{constructor(e,n){this.width=e,this.height=n,this.array=[],this.array=new Array(e*n)}get(e,n){return this.array[e+n*this.width]}set(e,n,r){this.array[e+n*this.width]=r}}function wn(t){return t===32||t===9}const ut=class ut{static getKey(e){let n=this.chrKeys.get(e);return n===void 0&&(n=this.chrKeys.size,this.chrKeys.set(e,n)),n}constructor(e,n,r){this.range=e,this.lines=n,this.source=r,this.histogram=[];let s=0;for(let i=e.startLineNumber-1;i<e.endLineNumberExclusive-1;i++){const o=n[i];for(let u=0;u<o.length;u++){s++;const c=o[u],f=ut.getKey(c);this.histogram[f]=(this.histogram[f]||0)+1}s++;const l=ut.getKey(`\n`);this.histogram[l]=(this.histogram[l]||0)+1}this.totalCount=s}computeSimilarity(e){let n=0;const r=Math.max(this.histogram.length,e.histogram.length);for(let s=0;s<r;s++)n+=Math.abs((this.histogram[s]??0)-(e.histogram[s]??0));return 1-n/(this.totalCount+e.totalCount)}};ut.chrKeys=new Map;let Rt=ut;class Oa{compute(e,n,r=st.instance,s){if(e.length===0||n.length===0)return _e.trivial(e,n);const i=new _n(e.length,n.length),o=new _n(e.length,n.length),l=new _n(e.length,n.length);for(let g=0;g<e.length;g++)for(let p=0;p<n.length;p++){if(!r.isValid())return _e.trivialTimedOut(e,n);const x=g===0?0:i.get(g-1,p),L=p===0?0:i.get(g,p-1);let N;e.getElement(g)===n.getElement(p)?(g===0||p===0?N=0:N=i.get(g-1,p-1),g>0&&p>0&&o.get(g-1,p-1)===3&&(N+=l.get(g-1,p-1)),N+=s?s(g,p):1):N=-1;const v=Math.max(x,L,N);if(v===N){const y=g>0&&p>0?l.get(g-1,p-1):0;l.set(g,p,y+1),o.set(g,p,3)}else v===x?(l.set(g,p,0),o.set(g,p,1)):v===L&&(l.set(g,p,0),o.set(g,p,2));i.set(g,p,v)}const u=[];let c=e.length,f=n.length;function h(g,p){(g+1!==c||p+1!==f)&&u.push(new G(new V(g+1,c),new V(p+1,f))),c=g,f=p}let m=e.length-1,d=n.length-1;for(;m>=0&&d>=0;)o.get(m,d)===3?(h(m,d),m--,d--):o.get(m,d)===1?m--:d--;return h(-1,-1),u.reverse(),new _e(u,!1)}}class _s{compute(e,n,r=st.instance){if(e.length===0||n.length===0)return _e.trivial(e,n);const s=e,i=n;function o(p,x){for(;p<s.length&&x<i.length&&s.getElement(p)===i.getElement(x);)p++,x++;return p}let l=0;const u=new Ga;u.set(0,o(0,0));const c=new ja;c.set(0,u.get(0)===0?null:new ws(null,0,0,u.get(0)));let f=0;e:for(;;){if(l++,!r.isValid())return _e.trivialTimedOut(s,i);const p=-Math.min(l,i.length+l%2),x=Math.min(l,s.length+l%2);for(f=p;f<=x;f+=2){const L=f===x?-1:u.get(f+1),N=f===p?-1:u.get(f-1)+1,v=Math.min(Math.max(L,N),s.length),y=v-f;if(v>s.length||y>i.length)continue;const b=o(v,y);u.set(f,b);const w=v===L?c.get(f+1):c.get(f-1);if(c.set(f,b!==v?new ws(w,v,y,b-v):w),u.get(f)===s.length&&u.get(f)-f===i.length)break e}}let h=c.get(f);const m=[];let d=s.length,g=i.length;for(;;){const p=h?h.x+h.length:0,x=h?h.y+h.length:0;if((p!==d||x!==g)&&m.push(new G(new V(p,d),new V(x,g))),!h)break;d=h.x,g=h.y,h=h.prev}return m.reverse(),new _e(m,!1)}}class ws{constructor(e,n,r,s){this.prev=e,this.x=n,this.y=r,this.length=s}}class Ga{constructor(){this.positiveArr=new Int32Array(10),this.negativeArr=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,n){if(e<0){if(e=-e-1,e>=this.negativeArr.length){const r=this.negativeArr;this.negativeArr=new Int32Array(r.length*2),this.negativeArr.set(r)}this.negativeArr[e]=n}else{if(e>=this.positiveArr.length){const r=this.positiveArr;this.positiveArr=new Int32Array(r.length*2),this.positiveArr.set(r)}this.positiveArr[e]=n}}}class ja{constructor(){this.positiveArr=[],this.negativeArr=[]}get(e){return e<0?(e=-e-1,this.negativeArr[e]):this.positiveArr[e]}set(e,n){e<0?(e=-e-1,this.negativeArr[e]=n):this.positiveArr[e]=n}}class Et{constructor(e,n,r){this.lines=e,this.range=n,this.considerWhitespaceChanges=r,this.elements=[],this.firstElementOffsetByLineIdx=[],this.lineStartOffsets=[],this.trimmedWsLengthsByLineIdx=[],this.firstElementOffsetByLineIdx.push(0);for(let s=this.range.startLineNumber;s<=this.range.endLineNumber;s++){let i=e[s-1],o=0;s===this.range.startLineNumber&&this.range.startColumn>1&&(o=this.range.startColumn-1,i=i.substring(o)),this.lineStartOffsets.push(o);let l=0;if(!r){const c=i.trimStart();l=i.length-c.length,i=c.trimEnd()}this.trimmedWsLengthsByLineIdx.push(l);const u=s===this.range.endLineNumber?Math.min(this.range.endColumn-1-o-l,i.length):i.length;for(let c=0;c<u;c++)this.elements.push(i.charCodeAt(c));s<this.range.endLineNumber&&(this.elements.push(10),this.firstElementOffsetByLineIdx.push(this.elements.length))}}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new V(0,this.length))}getText(e){return this.elements.slice(e.start,e.endExclusive).map(n=>String.fromCharCode(n)).join("")}getElement(e){return this.elements[e]}get length(){return this.elements.length}getBoundaryScore(e){const n=vs(e>0?this.elements[e-1]:-1),r=vs(e<this.elements.length?this.elements[e]:-1);if(n===7&&r===8)return 0;if(n===8)return 150;let s=0;return n!==r&&(s+=10,n===0&&r===1&&(s+=1)),s+=Ls(n),s+=Ls(r),s}translateOffset(e,n="right"){const r=nt(this.firstElementOffsetByLineIdx,i=>i<=e),s=e-this.firstElementOffsetByLineIdx[r];return new U(this.range.startLineNumber+r,1+this.lineStartOffsets[r]+s+(s===0&&n==="left"?0:this.trimmedWsLengthsByLineIdx[r]))}translateRange(e){const n=this.translateOffset(e.start,"right"),r=this.translateOffset(e.endExclusive,"left");return r.isBefore(n)?k.fromPositions(r,r):k.fromPositions(n,r)}findWordContaining(e){if(e<0||e>=this.elements.length||!Ln(this.elements[e]))return;let n=e;for(;n>0&&Ln(this.elements[n-1]);)n--;let r=e;for(;r<this.elements.length&&Ln(this.elements[r]);)r++;return new V(n,r)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,n){return this.elements[e]===this.elements[n]}extendToFullLines(e){const n=qe(this.firstElementOffsetByLineIdx,s=>s<=e.start)??0,r=ka(this.firstElementOffsetByLineIdx,s=>e.endExclusive<=s)??this.elements.length;return new V(n,r)}}function Ln(t){return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57}const Xa={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function Ls(t){return Xa[t]}function vs(t){return t===10?8:t===13?7:wn(t)?6:t>=97&&t<=122?0:t>=65&&t<=90?1:t>=48&&t<=57?2:t===-1?3:t===44||t===59?5:4}function Qa(t,e,n,r,s,i){let{moves:o,excludedChanges:l}=Ja(t,e,n,i);if(!i.isValid())return[];const u=t.filter(f=>!l.has(f)),c=Za(u,r,s,e,n,i);return Ha(o,c),o=Ka(o),o=o.filter(f=>{const h=f.original.toOffsetRange().slice(e).map(d=>d.trim());return h.join(`\n`).length>=15&&Ya(h,d=>d.length>=2)>=2}),o=e1(t,o),o}function Ya(t,e){let n=0;for(const r of t)e(r)&&n++;return n}function Ja(t,e,n,r){const s=[],i=t.filter(u=>u.modified.isEmpty&&u.original.length>=3).map(u=>new Rt(u.original,e,u)),o=new Set(t.filter(u=>u.original.isEmpty&&u.modified.length>=3).map(u=>new Rt(u.modified,n,u))),l=new Set;for(const u of i){let c=-1,f;for(const h of o){const m=u.computeSimilarity(h);m>c&&(c=m,f=h)}if(c>.9&&f&&(o.delete(f),s.push(new le(u.range,f.range)),l.add(u.source),l.add(f.source)),!r.isValid())return{moves:s,excludedChanges:l}}return{moves:s,excludedChanges:l}}function Za(t,e,n,r,s,i){const o=[],l=new ba;for(const m of t)for(let d=m.original.startLineNumber;d<m.original.endLineNumberExclusive-2;d++){const g=`${e[d-1]}:${e[d+1-1]}:${e[d+2-1]}`;l.add(g,{range:new D(d,d+3)})}const u=[];t.sort(Ct(m=>m.modified.startLineNumber,At));for(const m of t){let d=[];for(let g=m.modified.startLineNumber;g<m.modified.endLineNumberExclusive-2;g++){const p=`${n[g-1]}:${n[g+1-1]}:${n[g+2-1]}`,x=new D(g,g+3),L=[];l.forEach(p,({range:N})=>{for(const y of d)if(y.originalLineRange.endLineNumberExclusive+1===N.endLineNumberExclusive&&y.modifiedLineRange.endLineNumberExclusive+1===x.endLineNumberExclusive){y.originalLineRange=new D(y.originalLineRange.startLineNumber,N.endLineNumberExclusive),y.modifiedLineRange=new D(y.modifiedLineRange.startLineNumber,x.endLineNumberExclusive),L.push(y);return}const v={modifiedLineRange:x,originalLineRange:N};u.push(v),L.push(v)}),d=L}if(!i.isValid())return[]}u.sort(Wa(Ct(m=>m.modifiedLineRange.length,At)));const c=new de,f=new de;for(const m of u){const d=m.modifiedLineRange.startLineNumber-m.originalLineRange.startLineNumber,g=c.subtractFrom(m.modifiedLineRange),p=f.subtractFrom(m.originalLineRange).getWithDelta(d),x=g.getIntersection(p);for(const L of x.ranges){if(L.length<3)continue;const N=L,v=L.delta(-d);o.push(new le(v,N)),c.addRange(N),f.addRange(v)}}o.sort(Ct(m=>m.original.startLineNumber,At));const h=new St(t);for(let m=0;m<o.length;m++){const d=o[m],g=h.findLastMonotonous(w=>w.original.startLineNumber<=d.original.startLineNumber),p=qe(t,w=>w.modified.startLineNumber<=d.modified.startLineNumber),x=Math.max(d.original.startLineNumber-g.original.startLineNumber,d.modified.startLineNumber-p.modified.startLineNumber),L=h.findLastMonotonous(w=>w.original.startLineNumber<d.original.endLineNumberExclusive),N=qe(t,w=>w.modified.startLineNumber<d.modified.endLineNumberExclusive),v=Math.max(L.original.endLineNumberExclusive-d.original.endLineNumberExclusive,N.modified.endLineNumberExclusive-d.modified.endLineNumberExclusive);let y;for(y=0;y<x;y++){const w=d.original.startLineNumber-y-1,C=d.modified.startLineNumber-y-1;if(w>r.length||C>s.length||c.contains(C)||f.contains(w)||!Ns(r[w-1],s[C-1],i))break}y>0&&(f.addRange(new D(d.original.startLineNumber-y,d.original.startLineNumber)),c.addRange(new D(d.modified.startLineNumber-y,d.modified.startLineNumber)));let b;for(b=0;b<v;b++){const w=d.original.endLineNumberExclusive+b,C=d.modified.endLineNumberExclusive+b;if(w>r.length||C>s.length||c.contains(C)||f.contains(w)||!Ns(r[w-1],s[C-1],i))break}b>0&&(f.addRange(new D(d.original.endLineNumberExclusive,d.original.endLineNumberExclusive+b)),c.addRange(new D(d.modified.endLineNumberExclusive,d.modified.endLineNumberExclusive+b))),(y>0||b>0)&&(o[m]=new le(new D(d.original.startLineNumber-y,d.original.endLineNumberExclusive+b),new D(d.modified.startLineNumber-y,d.modified.endLineNumberExclusive+b)))}return o}function Ns(t,e,n){if(t.trim()===e.trim())return!0;if(t.length>300&&e.length>300)return!1;const s=new _s().compute(new Et([t],new k(1,1,1,t.length),!1),new Et([e],new k(1,1,1,e.length),!1),n);let i=0;const o=G.invert(s.diffs,t.length);for(const f of o)f.seq1Range.forEach(h=>{wn(t.charCodeAt(h))||i++});function l(f){let h=0;for(let m=0;m<t.length;m++)wn(f.charCodeAt(m))||h++;return h}const u=l(t.length>e.length?t:e);return i/u>.6&&u>10}function Ka(t){if(t.length===0)return t;t.sort(Ct(n=>n.original.startLineNumber,At));const e=[t[0]];for(let n=1;n<t.length;n++){const r=e[e.length-1],s=t[n],i=s.original.startLineNumber-r.original.endLineNumberExclusive,o=s.modified.startLineNumber-r.modified.endLineNumberExclusive;if(i>=0&&o>=0&&i+o<=2){e[e.length-1]=r.join(s);continue}e.push(s)}return e}function e1(t,e){const n=new St(t);return e=e.filter(r=>{const s=n.findLastMonotonous(l=>l.original.startLineNumber<r.original.endLineNumberExclusive)||new le(new D(1,1),new D(1,1)),i=qe(t,l=>l.modified.startLineNumber<r.modified.endLineNumberExclusive);return s!==i}),e}function Ss(t,e,n){let r=n;return r=Cs(t,e,r),r=Cs(t,e,r),r=t1(t,e,r),r}function Cs(t,e,n){if(n.length===0)return n;const r=[];r.push(n[0]);for(let i=1;i<n.length;i++){const o=r[r.length-1];let l=n[i];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){const u=l.seq1Range.start-o.seq1Range.endExclusive;let c;for(c=1;c<=u&&!(t.getElement(l.seq1Range.start-c)!==t.getElement(l.seq1Range.endExclusive-c)||e.getElement(l.seq2Range.start-c)!==e.getElement(l.seq2Range.endExclusive-c));c++);if(c--,c===u){r[r.length-1]=new G(new V(o.seq1Range.start,l.seq1Range.endExclusive-u),new V(o.seq2Range.start,l.seq2Range.endExclusive-u));continue}l=l.delta(-c)}r.push(l)}const s=[];for(let i=0;i<r.length-1;i++){const o=r[i+1];let l=r[i];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){const u=o.seq1Range.start-l.seq1Range.endExclusive;let c;for(c=0;c<u&&!(!t.isStronglyEqual(l.seq1Range.start+c,l.seq1Range.endExclusive+c)||!e.isStronglyEqual(l.seq2Range.start+c,l.seq2Range.endExclusive+c));c++);if(c===u){r[i+1]=new G(new V(l.seq1Range.start+u,o.seq1Range.endExclusive),new V(l.seq2Range.start+u,o.seq2Range.endExclusive));continue}c>0&&(l=l.delta(c))}s.push(l)}return r.length>0&&s.push(r[r.length-1]),s}function t1(t,e,n){if(!t.getBoundaryScore||!e.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const s=r>0?n[r-1]:void 0,i=n[r],o=r+1<n.length?n[r+1]:void 0,l=new V(s?s.seq1Range.endExclusive+1:0,o?o.seq1Range.start-1:t.length),u=new V(s?s.seq2Range.endExclusive+1:0,o?o.seq2Range.start-1:e.length);i.seq1Range.isEmpty?n[r]=As(i,t,e,l,u):i.seq2Range.isEmpty&&(n[r]=As(i.swap(),e,t,u,l).swap())}return n}function As(t,e,n,r,s){let o=1;for(;t.seq1Range.start-o>=r.start&&t.seq2Range.start-o>=s.start&&n.isStronglyEqual(t.seq2Range.start-o,t.seq2Range.endExclusive-o)&&o<100;)o++;o--;let l=0;for(;t.seq1Range.start+l<r.endExclusive&&t.seq2Range.endExclusive+l<s.endExclusive&&n.isStronglyEqual(t.seq2Range.start+l,t.seq2Range.endExclusive+l)&&l<100;)l++;if(o===0&&l===0)return t;let u=0,c=-1;for(let f=-o;f<=l;f++){const h=t.seq2Range.start+f,m=t.seq2Range.endExclusive+f,d=t.seq1Range.start+f,g=e.getBoundaryScore(d)+n.getBoundaryScore(h)+n.getBoundaryScore(m);g>c&&(c=g,u=f)}return t.delta(u)}function n1(t,e,n){const r=[];for(const s of n){const i=r[r.length-1];if(!i){r.push(s);continue}s.seq1Range.start-i.seq1Range.endExclusive<=2||s.seq2Range.start-i.seq2Range.endExclusive<=2?r[r.length-1]=new G(i.seq1Range.join(s.seq1Range),i.seq2Range.join(s.seq2Range)):r.push(s)}return r}function r1(t,e,n){const r=G.invert(n,t.length),s=[];let i=new we(0,0);function o(u,c){if(u.offset1<i.offset1||u.offset2<i.offset2)return;const f=t.findWordContaining(u.offset1),h=e.findWordContaining(u.offset2);if(!f||!h)return;let m=new G(f,h);const d=m.intersect(c);let g=d.seq1Range.length,p=d.seq2Range.length;for(;r.length>0;){const x=r[0];if(!(x.seq1Range.intersects(m.seq1Range)||x.seq2Range.intersects(m.seq2Range)))break;const N=t.findWordContaining(x.seq1Range.start),v=e.findWordContaining(x.seq2Range.start),y=new G(N,v),b=y.intersect(x);if(g+=b.seq1Range.length,p+=b.seq2Range.length,m=m.join(y),m.seq1Range.endExclusive>=x.seq1Range.endExclusive)r.shift();else break}g+p<(m.seq1Range.length+m.seq2Range.length)*2/3&&s.push(m),i=m.getEndExclusives()}for(;r.length>0;){const u=r.shift();u.seq1Range.isEmpty||(o(u.getStarts(),u),o(u.getEndExclusives().delta(-1),u))}return s1(n,s)}function s1(t,e){const n=[];for(;t.length>0||e.length>0;){const r=t[0],s=e[0];let i;r&&(!s||r.seq1Range.start<s.seq1Range.start)?i=t.shift():i=e.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=i.seq1Range.start?n[n.length-1]=n[n.length-1].join(i):n.push(i)}return n}function i1(t,e,n){let r=n;if(r.length===0)return r;let s=0,i;do{i=!1;const o=[r[0]];for(let l=1;l<r.length;l++){let f=function(m,d){const g=new V(c.seq1Range.endExclusive,u.seq1Range.start);return t.getText(g).replace(/\\s/g,"").length<=4&&(m.seq1Range.length+m.seq2Range.length>5||d.seq1Range.length+d.seq2Range.length>5)};const u=r[l],c=o[o.length-1];f(c,u)?(i=!0,o[o.length-1]=o[o.length-1].join(u)):o.push(u)}r=o}while(s++<10&&i);return r}function a1(t,e,n){let r=n;if(r.length===0)return r;let s=0,i;do{i=!1;const l=[r[0]];for(let u=1;u<r.length;u++){let h=function(d,g){const p=new V(f.seq1Range.endExclusive,c.seq1Range.start);if(t.countLinesIn(p)>5||p.length>500)return!1;const L=t.getText(p).trim();if(L.length>20||L.split(/\\r\\n|\\r|\\n/).length>1)return!1;const N=t.countLinesIn(d.seq1Range),v=d.seq1Range.length,y=e.countLinesIn(d.seq2Range),b=d.seq2Range.length,w=t.countLinesIn(g.seq1Range),C=g.seq1Range.length,R=e.countLinesIn(g.seq2Range),B=g.seq2Range.length,Q=130;function q(F){return Math.min(F,Q)}return Math.pow(Math.pow(q(N*40+v),1.5)+Math.pow(q(y*40+b),1.5),1.5)+Math.pow(Math.pow(q(w*40+C),1.5)+Math.pow(q(R*40+B),1.5),1.5)>(Q**1.5)**1.5*1.3};const c=r[u],f=l[l.length-1];h(f,c)?(i=!0,l[l.length-1]=l[l.length-1].join(c)):l.push(c)}r=l}while(s++<10&&i);const o=[];return $a(r,(l,u,c)=>{let f=u;function h(L){return L.length>0&&L.trim().length<=3&&u.seq1Range.length+u.seq2Range.length>100}const m=t.extendToFullLines(u.seq1Range),d=t.getText(new V(m.start,u.seq1Range.start));h(d)&&(f=f.deltaStart(-d.length));const g=t.getText(new V(u.seq1Range.endExclusive,m.endExclusive));h(g)&&(f=f.deltaEnd(g.length));const p=G.fromOffsetPairs(l?l.getEndExclusives():we.zero,c?c.getStarts():we.max),x=f.intersect(p);o.length>0&&x.getStarts().equals(o[o.length-1].getEndExclusives())?o[o.length-1]=o[o.length-1].join(x):o.push(x)}),o}class Rs{constructor(e,n){this.trimmedHash=e,this.lines=n}getElement(e){return this.trimmedHash[e]}get length(){return this.trimmedHash.length}getBoundaryScore(e){const n=e===0?0:Es(this.lines[e-1]),r=e===this.lines.length?0:Es(this.lines[e]);return 1e3-(n+r)}getText(e){return this.lines.slice(e.start,e.endExclusive).join(`\n`)}isStronglyEqual(e,n){return this.lines[e]===this.lines[n]}}function Es(t){let e=0;for(;e<t.length&&(t.charCodeAt(e)===32||t.charCodeAt(e)===9);)e++;return e}class o1{constructor(){this.dynamicProgrammingDiffing=new Oa,this.myersDiffingAlgorithm=new _s}computeDiff(e,n,r){if(e.length<=1&&Ba(e,n,(b,w)=>b===w))return new Nt([],[],!1);if(e.length===1&&e[0].length===0||n.length===1&&n[0].length===0)return new Nt([new xe(new D(1,e.length+1),new D(1,n.length+1),[new fe(new k(1,1,e.length,e[e.length-1].length+1),new k(1,1,n.length,n[n.length-1].length+1))])],[],!1);const s=r.maxComputationTimeMs===0?st.instance:new za(r.maxComputationTimeMs),i=!r.ignoreTrimWhitespace,o=new Map;function l(b){let w=o.get(b);return w===void 0&&(w=o.size,o.set(b,w)),w}const u=e.map(b=>l(b.trim())),c=n.map(b=>l(b.trim())),f=new Rs(u,e),h=new Rs(c,n),m=f.length+h.length<1700?this.dynamicProgrammingDiffing.compute(f,h,s,(b,w)=>e[b]===n[w]?n[w].length===0?.1:1+Math.log(1+n[w].length):.99):this.myersDiffingAlgorithm.compute(f,h,s);let d=m.diffs,g=m.hitTimeout;d=Ss(f,h,d),d=i1(f,h,d);const p=[],x=b=>{if(i)for(let w=0;w<b;w++){const C=L+w,R=N+w;if(e[C]!==n[R]){const B=this.refineDiff(e,n,new G(new V(C,C+1),new V(R,R+1)),s,i);for(const Q of B.mappings)p.push(Q);B.hitTimeout&&(g=!0)}}};let L=0,N=0;for(const b of d){vt(()=>b.seq1Range.start-L===b.seq2Range.start-N);const w=b.seq1Range.start-L;x(w),L=b.seq1Range.endExclusive,N=b.seq2Range.endExclusive;const C=this.refineDiff(e,n,b,s,i);C.hitTimeout&&(g=!0);for(const R of C.mappings)p.push(R)}x(e.length-L);const v=Ms(p,e,n);let y=[];return r.computeMoves&&(y=this.computeMoves(v,e,n,u,c,s,i)),vt(()=>{function b(C,R){if(C.lineNumber<1||C.lineNumber>R.length)return!1;const B=R[C.lineNumber-1];return!(C.column<1||C.column>B.length+1)}function w(C,R){return!(C.startLineNumber<1||C.startLineNumber>R.length+1||C.endLineNumberExclusive<1||C.endLineNumberExclusive>R.length+1)}for(const C of v){if(!C.innerChanges)return!1;for(const R of C.innerChanges)if(!(b(R.modifiedRange.getStartPosition(),n)&&b(R.modifiedRange.getEndPosition(),n)&&b(R.originalRange.getStartPosition(),e)&&b(R.originalRange.getEndPosition(),e)))return!1;if(!w(C.modified,n)||!w(C.original,e))return!1}return!0}),new Nt(v,y,g)}computeMoves(e,n,r,s,i,o,l){return Qa(e,n,r,s,i,o).map(f=>{const h=this.refineDiff(n,r,new G(f.original.toOffsetRange(),f.modified.toOffsetRange()),o,l),m=Ms(h.mappings,n,r,!0);return new Ma(f,m)})}refineDiff(e,n,r,s,i){const l=u1(r).toRangeMapping2(e,n),u=new Et(e,l.originalRange,i),c=new Et(n,l.modifiedRange,i),f=u.length+c.length<500?this.dynamicProgrammingDiffing.compute(u,c,s):this.myersDiffingAlgorithm.compute(u,c,s);let h=f.diffs;return h=Ss(u,c,h),h=r1(u,c,h),h=n1(u,c,h),h=a1(u,c,h),{mappings:h.map(d=>new fe(u.translateRange(d.seq1Range),c.translateRange(d.seq2Range))),hitTimeout:f.hitTimeout}}}function Ms(t,e,n,r=!1){const s=[];for(const i of qa(t.map(o=>l1(o,e,n)),(o,l)=>o.original.overlapOrTouch(l.original)||o.modified.overlapOrTouch(l.modified))){const o=i[0],l=i[i.length-1];s.push(new xe(o.original.join(l.original),o.modified.join(l.modified),i.map(u=>u.innerChanges[0])))}return vt(()=>!r&&s.length>0&&(s[0].modified.startLineNumber!==s[0].original.startLineNumber||n.length-s[s.length-1].modified.endLineNumberExclusive!==e.length-s[s.length-1].original.endLineNumberExclusive)?!1:ls(s,(i,o)=>o.original.startLineNumber-i.original.endLineNumberExclusive===o.modified.startLineNumber-i.modified.endLineNumberExclusive&&i.original.endLineNumberExclusive<o.original.startLineNumber&&i.modified.endLineNumberExclusive<o.modified.startLineNumber)),s}function l1(t,e,n){let r=0,s=0;t.modifiedRange.endColumn===1&&t.originalRange.endColumn===1&&t.originalRange.startLineNumber+r<=t.originalRange.endLineNumber&&t.modifiedRange.startLineNumber+r<=t.modifiedRange.endLineNumber&&(s=-1),t.modifiedRange.startColumn-1>=n[t.modifiedRange.startLineNumber-1].length&&t.originalRange.startColumn-1>=e[t.originalRange.startLineNumber-1].length&&t.originalRange.startLineNumber<=t.originalRange.endLineNumber+s&&t.modifiedRange.startLineNumber<=t.modifiedRange.endLineNumber+s&&(r=1);const i=new D(t.originalRange.startLineNumber+r,t.originalRange.endLineNumber+1+s),o=new D(t.modifiedRange.startLineNumber+r,t.modifiedRange.endLineNumber+1+s);return new xe(i,o,[t])}function u1(t){return new le(new D(t.seq1Range.start+1,t.seq1Range.endExclusive+1),new D(t.seq2Range.start+1,t.seq2Range.endExclusive+1))}const ks={getLegacy:()=>new Da,getDefault:()=>new o1};function Re(t,e){const n=Math.pow(10,e);return Math.round(t*n)/n}class X{constructor(e,n,r,s=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,e))|0,this.g=Math.min(255,Math.max(0,n))|0,this.b=Math.min(255,Math.max(0,r))|0,this.a=Re(Math.max(Math.min(1,s),0),3)}static equals(e,n){return e.r===n.r&&e.g===n.g&&e.b===n.b&&e.a===n.a}}class ue{constructor(e,n,r,s){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,e),0)|0,this.s=Re(Math.max(Math.min(1,n),0),3),this.l=Re(Math.max(Math.min(1,r),0),3),this.a=Re(Math.max(Math.min(1,s),0),3)}static equals(e,n){return e.h===n.h&&e.s===n.s&&e.l===n.l&&e.a===n.a}static fromRGBA(e){const n=e.r/255,r=e.g/255,s=e.b/255,i=e.a,o=Math.max(n,r,s),l=Math.min(n,r,s);let u=0,c=0;const f=(l+o)/2,h=o-l;if(h>0){switch(c=Math.min(f<=.5?h/(2*f):h/(2-2*f),1),o){case n:u=(r-s)/h+(r<s?6:0);break;case r:u=(s-n)/h+2;break;case s:u=(n-r)/h+4;break}u*=60,u=Math.round(u)}return new ue(u,c,f,i)}static _hue2rgb(e,n,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+(n-e)*6*r:r<1/2?n:r<2/3?e+(n-e)*(2/3-r)*6:e}static toRGBA(e){const n=e.h/360,{s:r,l:s,a:i}=e;let o,l,u;if(r===0)o=l=u=s;else{const c=s<.5?s*(1+r):s+r-s*r,f=2*s-c;o=ue._hue2rgb(f,c,n+1/3),l=ue._hue2rgb(f,c,n),u=ue._hue2rgb(f,c,n-1/3)}return new X(Math.round(o*255),Math.round(l*255),Math.round(u*255),i)}}class He{constructor(e,n,r,s){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,e),0)|0,this.s=Re(Math.max(Math.min(1,n),0),3),this.v=Re(Math.max(Math.min(1,r),0),3),this.a=Re(Math.max(Math.min(1,s),0),3)}static equals(e,n){return e.h===n.h&&e.s===n.s&&e.v===n.v&&e.a===n.a}static fromRGBA(e){const n=e.r/255,r=e.g/255,s=e.b/255,i=Math.max(n,r,s),o=Math.min(n,r,s),l=i-o,u=i===0?0:l/i;let c;return l===0?c=0:i===n?c=((r-s)/l%6+6)%6:i===r?c=(s-n)/l+2:c=(n-r)/l+4,new He(Math.round(c*60),u,i,e.a)}static toRGBA(e){const{h:n,s:r,v:s,a:i}=e,o=s*r,l=o*(1-Math.abs(n/60%2-1)),u=s-o;let[c,f,h]=[0,0,0];return n<60?(c=o,f=l):n<120?(c=l,f=o):n<180?(f=o,h=l):n<240?(f=l,h=o):n<300?(c=l,h=o):n<=360&&(c=o,h=l),c=Math.round((c+u)*255),f=Math.round((f+u)*255),h=Math.round((h+u)*255),new X(c,f,h,i)}}const $=class ${static fromHex(e){return $.Format.CSS.parseHex(e)||$.red}static equals(e,n){return!e&&!n?!0:!e||!n?!1:e.equals(n)}get hsla(){return this._hsla?this._hsla:ue.fromRGBA(this.rgba)}get hsva(){return this._hsva?this._hsva:He.fromRGBA(this.rgba)}constructor(e){if(e)if(e instanceof X)this.rgba=e;else if(e instanceof ue)this._hsla=e,this.rgba=ue.toRGBA(e);else if(e instanceof He)this._hsva=e,this.rgba=He.toRGBA(e);else throw new Error("Invalid color ctor argument");else throw new Error("Color needs a value")}equals(e){return!!e&&X.equals(this.rgba,e.rgba)&&ue.equals(this.hsla,e.hsla)&&He.equals(this.hsva,e.hsva)}getRelativeLuminance(){const e=$._relativeLuminanceForComponent(this.rgba.r),n=$._relativeLuminanceForComponent(this.rgba.g),r=$._relativeLuminanceForComponent(this.rgba.b),s=.2126*e+.7152*n+.0722*r;return Re(s,4)}static _relativeLuminanceForComponent(e){const n=e/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(e){const n=this.getRelativeLuminance(),r=e.getRelativeLuminance();return n>r}isDarkerThan(e){const n=this.getRelativeLuminance(),r=e.getRelativeLuminance();return n<r}lighten(e){return new $(new ue(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*e,this.hsla.a))}darken(e){return new $(new ue(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*e,this.hsla.a))}transparent(e){const{r:n,g:r,b:s,a:i}=this.rgba;return new $(new X(n,r,s,i*e))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new $(new X(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}makeOpaque(e){if(this.isOpaque()||e.rgba.a!==1)return this;const{r:n,g:r,b:s,a:i}=this.rgba;return new $(new X(e.rgba.r-i*(e.rgba.r-n),e.rgba.g-i*(e.rgba.g-r),e.rgba.b-i*(e.rgba.b-s),1))}toString(){return this._toString||(this._toString=$.Format.CSS.format(this)),this._toString}static getLighterColor(e,n,r){if(e.isLighterThan(n))return e;r=r||.5;const s=e.getRelativeLuminance(),i=n.getRelativeLuminance();return r=r*(i-s)/i,e.lighten(r)}static getDarkerColor(e,n,r){if(e.isDarkerThan(n))return e;r=r||.5;const s=e.getRelativeLuminance(),i=n.getRelativeLuminance();return r=r*(s-i)/s,e.darken(r)}};$.white=new $(new X(255,255,255,1)),$.black=new $(new X(0,0,0,1)),$.red=new $(new X(255,0,0,1)),$.blue=new $(new X(0,0,255,1)),$.green=new $(new X(0,255,0,1)),$.cyan=new $(new X(0,255,255,1)),$.lightgrey=new $(new X(211,211,211,1)),$.transparent=new $(new X(0,0,0,0));let We=$;(function(t){(function(e){(function(n){function r(d){return d.rgba.a===1?`rgb(${d.rgba.r}, ${d.rgba.g}, ${d.rgba.b})`:t.Format.CSS.formatRGBA(d)}n.formatRGB=r;function s(d){return`rgba(${d.rgba.r}, ${d.rgba.g}, ${d.rgba.b}, ${+d.rgba.a.toFixed(2)})`}n.formatRGBA=s;function i(d){return d.hsla.a===1?`hsl(${d.hsla.h}, ${(d.hsla.s*100).toFixed(2)}%, ${(d.hsla.l*100).toFixed(2)}%)`:t.Format.CSS.formatHSLA(d)}n.formatHSL=i;function o(d){return`hsla(${d.hsla.h}, ${(d.hsla.s*100).toFixed(2)}%, ${(d.hsla.l*100).toFixed(2)}%, ${d.hsla.a.toFixed(2)})`}n.formatHSLA=o;function l(d){const g=d.toString(16);return g.length!==2?"0"+g:g}function u(d){return`#${l(d.rgba.r)}${l(d.rgba.g)}${l(d.rgba.b)}`}n.formatHex=u;function c(d,g=!1){return g&&d.rgba.a===1?t.Format.CSS.formatHex(d):`#${l(d.rgba.r)}${l(d.rgba.g)}${l(d.rgba.b)}${l(Math.round(d.rgba.a*255))}`}n.formatHexA=c;function f(d){return d.isOpaque()?t.Format.CSS.formatHex(d):t.Format.CSS.formatRGBA(d)}n.format=f;function h(d){const g=d.length;if(g===0||d.charCodeAt(0)!==35)return null;if(g===7){const p=16*m(d.charCodeAt(1))+m(d.charCodeAt(2)),x=16*m(d.charCodeAt(3))+m(d.charCodeAt(4)),L=16*m(d.charCodeAt(5))+m(d.charCodeAt(6));return new t(new X(p,x,L,1))}if(g===9){const p=16*m(d.charCodeAt(1))+m(d.charCodeAt(2)),x=16*m(d.charCodeAt(3))+m(d.charCodeAt(4)),L=16*m(d.charCodeAt(5))+m(d.charCodeAt(6)),N=16*m(d.charCodeAt(7))+m(d.charCodeAt(8));return new t(new X(p,x,L,N/255))}if(g===4){const p=m(d.charCodeAt(1)),x=m(d.charCodeAt(2)),L=m(d.charCodeAt(3));return new t(new X(16*p+p,16*x+x,16*L+L))}if(g===5){const p=m(d.charCodeAt(1)),x=m(d.charCodeAt(2)),L=m(d.charCodeAt(3)),N=m(d.charCodeAt(4));return new t(new X(16*p+p,16*x+x,16*L+L,(16*N+N)/255))}return null}n.parseHex=h;function m(d){switch(d){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(e.CSS||(e.CSS={}))})(t.Format||(t.Format={}))})(We||(We={}));function Ps(t){const e=[];for(const n of t){const r=Number(n);(r||r===0&&n.replace(/\\s/g,"")!=="")&&e.push(r)}return e}function vn(t,e,n,r){return{red:t/255,blue:n/255,green:e/255,alpha:r}}function it(t,e){const n=e.index,r=e[0].length;if(!n)return;const s=t.positionAt(n);return{startLineNumber:s.lineNumber,startColumn:s.column,endLineNumber:s.lineNumber,endColumn:s.column+r}}function c1(t,e){if(!t)return;const n=We.Format.CSS.parseHex(e);if(n)return{range:t,color:vn(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}}function Fs(t,e,n){if(!t||e.length!==1)return;const s=e[0].values(),i=Ps(s);return{range:t,color:vn(i[0],i[1],i[2],n?i[3]:1)}}function Ds(t,e,n){if(!t||e.length!==1)return;const s=e[0].values(),i=Ps(s),o=new We(new ue(i[0],i[1]/100,i[2]/100,n?i[3]:1));return{range:t,color:vn(o.rgba.r,o.rgba.g,o.rgba.b,o.rgba.a)}}function at(t,e){return typeof t=="string"?[...t.matchAll(e)]:t.findMatches(e)}function h1(t){const e=[],r=at(t,/\\b(rgb|rgba|hsl|hsla)(\\([0-9\\s,.\\%]*\\))|(#)([A-Fa-f0-9]{3})\\b|(#)([A-Fa-f0-9]{4})\\b|(#)([A-Fa-f0-9]{6})\\b|(#)([A-Fa-f0-9]{8})\\b/gm);if(r.length>0)for(const s of r){const i=s.filter(c=>c!==void 0),o=i[1],l=i[2];if(!l)continue;let u;if(o==="rgb"){const c=/^\\(\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*\\)$/gm;u=Fs(it(t,s),at(l,c),!1)}else if(o==="rgba"){const c=/^\\(\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\\s*,\\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\\s*\\)$/gm;u=Fs(it(t,s),at(l,c),!0)}else if(o==="hsl"){const c=/^\\(\\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*\\)$/gm;u=Ds(it(t,s),at(l,c),!1)}else if(o==="hsla"){const c=/^\\(\\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(100|\\d{1,2}[.]\\d*|\\d{1,2})%\\s*,\\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\\s*\\)$/gm;u=Ds(it(t,s),at(l,c),!0)}else o==="#"&&(u=c1(it(t,s),o+l));u&&e.push(u)}return e}function f1(t){return!t||typeof t.getValue!="function"||typeof t.positionAt!="function"?[]:h1(t)}const Ts=new RegExp("\\\\bMARK:\\\\s*(.*)$","d"),m1=/^-+|-+$/g;function d1(t,e){let n=[];if(e.findRegionSectionHeaders&&e.foldingRules?.markers){const r=g1(t,e);n=n.concat(r)}if(e.findMarkSectionHeaders){const r=p1(t);n=n.concat(r)}return n}function g1(t,e){const n=[],r=t.getLineCount();for(let s=1;s<=r;s++){const i=t.getLineContent(s),o=i.match(e.foldingRules.markers.start);if(o){const l={startLineNumber:s,startColumn:o[0].length+1,endLineNumber:s,endColumn:i.length+1};if(l.endColumn>l.startColumn){const u={range:l,...Is(i.substring(o[0].length)),shouldBeInComments:!1};(u.text||u.hasSeparatorLine)&&n.push(u)}}}return n}function p1(t){const e=[],n=t.getLineCount();for(let r=1;r<=n;r++){const s=t.getLineContent(r);b1(s,r,e)}return e}function b1(t,e,n){Ts.lastIndex=0;const r=Ts.exec(t);if(r){const s=r.indices[1][0]+1,i=r.indices[1][1]+1,o={startLineNumber:e,startColumn:s,endLineNumber:e,endColumn:i};if(o.endColumn>o.startColumn){const l={range:o,...Is(r[1]),shouldBeInComments:!0};(l.text||l.hasSeparatorLine)&&n.push(l)}}}function Is(t){t=t.trim();const e=t.startsWith("-");return t=t.replace(m1,""),{text:t,hasSeparatorLine:e}}var Vs;(function(t){async function e(r){let s;const i=await Promise.all(r.map(o=>o.then(l=>l,l=>{s||(s=l)})));if(typeof s<"u")throw s;return i}t.settled=e;function n(r){return new Promise(async(s,i)=>{try{await r(s,i)}catch(o){i(o)}})}t.withAsyncBody=n})(Vs||(Vs={}));const te=class te{static fromArray(e){return new te(n=>{n.emitMany(e)})}static fromPromise(e){return new te(async n=>{n.emitMany(await e)})}static fromPromises(e){return new te(async n=>{await Promise.all(e.map(async r=>n.emitOne(await r)))})}static merge(e){return new te(async n=>{await Promise.all(e.map(async r=>{for await(const s of r)n.emitOne(s)}))})}constructor(e,n){this._state=0,this._results=[],this._error=null,this._onReturn=n,this._onStateChanged=new oe,queueMicrotask(async()=>{const r={emitOne:s=>this.emitOne(s),emitMany:s=>this.emitMany(s),reject:s=>this.reject(s)};try{await Promise.resolve(e(r)),this.resolve()}catch(s){this.reject(s)}finally{r.emitOne=void 0,r.emitMany=void 0,r.reject=void 0}})}[Symbol.asyncIterator](){let e=0;return{next:async()=>{do{if(this._state===2)throw this._error;if(e<this._results.length)return{done:!1,value:this._results[e++]};if(this._state===1)return{done:!0,value:void 0};await dt.toPromise(this._onStateChanged.event)}while(!0)},return:async()=>(this._onReturn?.(),{done:!0,value:void 0})}}static map(e,n){return new te(async r=>{for await(const s of e)r.emitOne(n(s))})}map(e){return te.map(this,e)}static filter(e,n){return new te(async r=>{for await(const s of e)n(s)&&r.emitOne(s)})}filter(e){return te.filter(this,e)}static coalesce(e){return te.filter(e,n=>!!n)}coalesce(){return te.coalesce(this)}static async toPromise(e){const n=[];for await(const r of e)n.push(r);return n}toPromise(){return te.toPromise(this)}emitOne(e){this._state===0&&(this._results.push(e),this._onStateChanged.fire())}emitMany(e){this._state===0&&(this._results=this._results.concat(e),this._onStateChanged.fire())}resolve(){this._state===0&&(this._state=1,this._onStateChanged.fire())}reject(e){this._state===0&&(this._state=2,this._error=e,this._onStateChanged.fire())}};te.EMPTY=te.fromArray([]);let Bs=te;class y1{constructor(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}insertValues(e,n){e=Be(e);const r=this.values,s=this.prefixSum,i=n.length;return i===0?!1:(this.values=new Uint32Array(r.length+i),this.values.set(r.subarray(0,e),0),this.values.set(r.subarray(e),e+i),this.values.set(n,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(s.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}setValue(e,n){return e=Be(e),n=Be(n),this.values[e]===n?!1:(this.values[e]=n,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)}removeValues(e,n){e=Be(e),n=Be(n);const r=this.values,s=this.prefixSum;if(e>=r.length)return!1;const i=r.length-e;return n>=i&&(n=i),n===0?!1:(this.values=new Uint32Array(r.length-n),this.values.set(r.subarray(0,e),0),this.values.set(r.subarray(e+n),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(s.subarray(0,this.prefixSumValidIndex[0]+1)),!0)}getTotalSum(){return this.values.length===0?0:this._getPrefixSum(this.values.length-1)}getPrefixSum(e){return e<0?0:(e=Be(e),this._getPrefixSum(e))}_getPrefixSum(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];let n=this.prefixSumValidIndex[0]+1;n===0&&(this.prefixSum[0]=this.values[0],n++),e>=this.values.length&&(e=this.values.length-1);for(let r=n;r<=e;r++)this.prefixSum[r]=this.prefixSum[r-1]+this.values[r];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let n=0,r=this.values.length-1,s=0,i=0,o=0;for(;n<=r;)if(s=n+(r-n)/2|0,i=this.prefixSum[s],o=i-this.values[s],e<o)r=s-1;else if(e>=i)n=s+1;else break;return new x1(s,e-o)}}class x1{constructor(e,n){this.index=e,this.remainder=n,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=n}}class _1{constructor(e,n,r,s){this._uri=e,this._lines=n,this._eol=r,this._versionId=s,this._lineStarts=null,this._cachedTextValue=null}dispose(){this._lines.length=0}get version(){return this._versionId}getText(){return this._cachedTextValue===null&&(this._cachedTextValue=this._lines.join(this._eol)),this._cachedTextValue}onEvents(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);const n=e.changes;for(const r of n)this._acceptDeleteRange(r.range),this._acceptInsertText(new U(r.range.startLineNumber,r.range.startColumn),r.text);this._versionId=e.versionId,this._cachedTextValue=null}_ensureLineStarts(){if(!this._lineStarts){const e=this._eol.length,n=this._lines.length,r=new Uint32Array(n);for(let s=0;s<n;s++)r[s]=this._lines[s].length+e;this._lineStarts=new y1(r)}}_setLineText(e,n){this._lines[e]=n,this._lineStarts&&this._lineStarts.setValue(e,this._lines[e].length+this._eol.length)}_acceptDeleteRange(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1));return}this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}_acceptInsertText(e,n){if(n.length===0)return;const r=fi(n);if(r.length===1){this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+r[0]+this._lines[e.lineNumber-1].substring(e.column-1));return}r[r.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+r[0]);const s=new Uint32Array(r.length-1);for(let i=1;i<r.length;i++)this._lines.splice(e.lineNumber+i-1,0,r[i]),s[i-1]=r[i].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,s)}}class w1{constructor(){this._models=Object.create(null)}getModel(e){return this._models[e]}getModels(){const e=[];return Object.keys(this._models).forEach(n=>e.push(this._models[n])),e}$acceptNewModel(e){this._models[e.url]=new L1(re.parse(e.url),e.lines,e.EOL,e.versionId)}$acceptModelChanged(e,n){if(!this._models[e])return;this._models[e].onEvents(n)}$acceptRemovedModel(e){this._models[e]&&delete this._models[e]}}class L1 extends _1{get uri(){return this._uri}get eol(){return this._eol}getValue(){return this.getText()}findMatches(e){const n=[];for(let r=0;r<this._lines.length;r++){const s=this._lines[r],i=this.offsetAt(new U(r+1,1)),o=s.matchAll(e);for(const l of o)(l.index||l.index===0)&&(l.index=l.index+i),n.push(l)}return n}getLinesContent(){return this._lines.slice(0)}getLineCount(){return this._lines.length}getLineContent(e){return this._lines[e-1]}getWordAtPosition(e,n){const r=gn(e.column,cs(n),this._lines[e.lineNumber-1],0);return r?new k(e.lineNumber,r.startColumn,e.lineNumber,r.endColumn):null}words(e){const n=this._lines,r=this._wordenize.bind(this);let s=0,i="",o=0,l=[];return{*[Symbol.iterator](){for(;;)if(o<l.length){const u=i.substring(l[o].start,l[o].end);o+=1,yield u}else if(s<n.length)i=n[s],l=r(i,e),o=0,s+=1;else break}}}getLineWords(e,n){const r=this._lines[e-1],s=this._wordenize(r,n),i=[];for(const o of s)i.push({word:r.substring(o.start,o.end),startColumn:o.start+1,endColumn:o.end+1});return i}_wordenize(e,n){const r=[];let s;for(n.lastIndex=0;(s=n.exec(e))&&s[0].length!==0;)r.push({start:s.index,end:s.index+s[0].length});return r}getValueInRange(e){if(e=this._validateRange(e),e.startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const n=this._eol,r=e.startLineNumber-1,s=e.endLineNumber-1,i=[];i.push(this._lines[r].substring(e.startColumn-1));for(let o=r+1;o<s;o++)i.push(this._lines[o]);return i.push(this._lines[s].substring(0,e.endColumn-1)),i.join(n)}offsetAt(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();const n=this._lineStarts.getIndexOf(e),r=this._lines[n.index].length;return{lineNumber:1+n.index,column:1+Math.min(n.remainder,r)}}_validateRange(e){const n=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),r=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return n.lineNumber!==e.startLineNumber||n.column!==e.startColumn||r.lineNumber!==e.endLineNumber||r.column!==e.endColumn?{startLineNumber:n.lineNumber,startColumn:n.column,endLineNumber:r.lineNumber,endColumn:r.column}:e}_validatePosition(e){if(!U.isIPosition(e))throw new Error("bad position");let{lineNumber:n,column:r}=e,s=!1;if(n<1)n=1,r=1,s=!0;else if(n>this._lines.length)n=this._lines.length,r=this._lines[n-1].length+1,s=!0;else{const i=this._lines[n-1].length+1;r<1?(r=1,s=!0):r>i&&(r=i,s=!0)}return s?{lineNumber:n,column:r}:e}}const It=class It{constructor(){this._workerTextModelSyncServer=new w1}dispose(){}_getModel(e){return this._workerTextModelSyncServer.getModel(e)}_getModels(){return this._workerTextModelSyncServer.getModels()}$acceptNewModel(e){this._workerTextModelSyncServer.$acceptNewModel(e)}$acceptModelChanged(e,n){this._workerTextModelSyncServer.$acceptModelChanged(e,n)}$acceptRemovedModel(e){this._workerTextModelSyncServer.$acceptRemovedModel(e)}async $computeUnicodeHighlights(e,n,r){const s=this._getModel(e);return s?Ra.computeUnicodeHighlights(s,n,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async $findSectionHeaders(e,n){const r=this._getModel(e);return r?d1(r,n):[]}async $computeDiff(e,n,r,s){const i=this._getModel(e),o=this._getModel(n);return!i||!o?null:Mt.computeDiff(i,o,r,s)}static computeDiff(e,n,r,s){const i=s==="advanced"?ks.getDefault():ks.getLegacy(),o=e.getLinesContent(),l=n.getLinesContent(),u=i.computeDiff(o,l,r),c=u.changes.length>0?!1:this._modelsAreIdentical(e,n);function f(h){return h.map(m=>[m.original.startLineNumber,m.original.endLineNumberExclusive,m.modified.startLineNumber,m.modified.endLineNumberExclusive,m.innerChanges?.map(d=>[d.originalRange.startLineNumber,d.originalRange.startColumn,d.originalRange.endLineNumber,d.originalRange.endColumn,d.modifiedRange.startLineNumber,d.modifiedRange.startColumn,d.modifiedRange.endLineNumber,d.modifiedRange.endColumn])])}return{identical:c,quitEarly:u.hitTimeout,changes:f(u.changes),moves:u.moves.map(h=>[h.lineRangeMapping.original.startLineNumber,h.lineRangeMapping.original.endLineNumberExclusive,h.lineRangeMapping.modified.startLineNumber,h.lineRangeMapping.modified.endLineNumberExclusive,f(h.changes)])}}static _modelsAreIdentical(e,n){const r=e.getLineCount(),s=n.getLineCount();if(r!==s)return!1;for(let i=1;i<=r;i++){const o=e.getLineContent(i),l=n.getLineContent(i);if(o!==l)return!1}return!0}async $computeMoreMinimalEdits(e,n,r){const s=this._getModel(e);if(!s)return n;const i=[];let o;n=n.slice(0).sort((u,c)=>{if(u.range&&c.range)return k.compareRangesUsingStarts(u.range,c.range);const f=u.range?0:1,h=c.range?0:1;return f-h});let l=0;for(let u=1;u<n.length;u++)k.getEndPosition(n[l].range).equals(k.getStartPosition(n[u].range))?(n[l].range=k.fromPositions(k.getStartPosition(n[l].range),k.getEndPosition(n[u].range)),n[l].text+=n[u].text):(l++,n[l]=n[u]);n.length=l+1;for(let{range:u,text:c,eol:f}of n){if(typeof f=="number"&&(o=f),k.isEmpty(u)&&!c)continue;const h=s.getValueInRange(u);if(c=c.replace(/\\r\\n|\\n|\\r/g,s.eol),h===c)continue;if(Math.max(c.length,h.length)>Mt._diffLimit){i.push({range:u,text:c});continue}const m=Ji(h,c,r),d=s.offsetAt(k.lift(u).getStartPosition());for(const g of m){const p=s.positionAt(d+g.originalStart),x=s.positionAt(d+g.originalStart+g.originalLength),L={text:c.substr(g.modifiedStart,g.modifiedLength),range:{startLineNumber:p.lineNumber,startColumn:p.column,endLineNumber:x.lineNumber,endColumn:x.column}};s.getValueInRange(L.range)!==L.text&&i.push(L)}}return typeof o=="number"&&i.push({eol:o,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}async $computeLinks(e){const n=this._getModel(e);return n?na(n):null}async $computeDefaultDocumentColors(e){const n=this._getModel(e);return n?f1(n):null}async $textualSuggest(e,n,r,s){const i=new mt,o=new RegExp(r,s),l=new Set;e:for(const u of e){const c=this._getModel(u);if(c){for(const f of c.words(o))if(!(f===n||!isNaN(Number(f)))&&(l.add(f),l.size>Mt._suggestionsLimit))break e}}return{words:Array.from(l),duration:i.elapsed()}}async $computeWordRanges(e,n,r,s){const i=this._getModel(e);if(!i)return Object.create(null);const o=new RegExp(r,s),l=Object.create(null);for(let u=n.startLineNumber;u<n.endLineNumber;u++){const c=i.getLineWords(u,o);for(const f of c){if(!isNaN(Number(f.word)))continue;let h=l[f.word];h||(h=[],l[f.word]=h),h.push({startLineNumber:u,startColumn:f.startColumn,endLineNumber:u,endColumn:f.endColumn})}}return l}async $navigateValueSet(e,n,r,s,i){const o=this._getModel(e);if(!o)return null;const l=new RegExp(s,i);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const u=o.getValueInRange(n),c=o.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},l);if(!c)return null;const f=o.getValueInRange(c);return sn.INSTANCE.navigateValueSet(n,u,c,f,r)}};It._diffLimit=1e5,It._suggestionsLimit=1e4;let Nn=It;class Mt extends Nn{constructor(e,n){super(),this._host=e,this._foreignModuleFactory=n,this._foreignModule=null}async $ping(){return"pong"}$loadForeignModule(e,n,r){const o={host:xa(r,(l,u)=>this._host.$fhr(l,u)),getMirrorModels:()=>this._getModels()};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(o,n),Promise.resolve(ss(this._foreignModule))):new Promise((l,u)=>{const c=f=>{this._foreignModule=f.create(o,n),l(ss(this._foreignModule))};import(`${Wn.asBrowserUri(`${e}.js`).toString(!0)}`).then(c).catch(u)})}$fmr(e,n){if(!this._foreignModule||typeof this._foreignModule[e]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,n))}catch(r){return Promise.reject(r)}}}typeof importScripts=="function"&&(globalThis.monaco=ma());let Sn=!1;function v1(t){if(Sn)return;Sn=!0;const e=new Qi(n=>{globalThis.postMessage(n)},n=>new Mt(dn.getChannel(n),t));globalThis.onmessage=n=>{e.onmessage(n.data)}}globalThis.onmessage=t=>{Sn||v1(null)}})();\n',r=typeof self<"u"&&self.Blob&&new Blob([i],{type:"text/javascript;charset=utf-8"});function s(e){let t;try{if(t=r&&(self.URL||self.webkitURL).createObjectURL(r),!t)throw"";const n=new Worker(t,{name:e==null?void 0:e.name});return n.addEventListener("error",()=>{(self.URL||self.webkitURL).revokeObjectURL(t)}),n}catch(n){return new Worker("data:text/javascript;charset=utf-8,"+encodeURIComponent(i),{name:e==null?void 0:e.name})}finally{t&&(self.URL||self.webkitURL).revokeObjectURL(t)}}export{s as default};
