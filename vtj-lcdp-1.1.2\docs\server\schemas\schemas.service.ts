import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { toArray, fs } from '@vtj/node';
import JSZip from 'jszip';
import { resolve } from 'path';
import { Schema } from './entities/schema.entity';
import { SchemaDto } from './dto/schema.dto';
import { SchemaType, pager } from '../shared';
import { OssService } from '../oss/oss.service';
import { QuerySchemaDto } from './dto/query-app.dto';

import type {
  ProjectSchema,
  BlockSchema,
  MaterialDescription,
  Dependencie,
  PlatformType
} from '@vtj/core';
import { generator, vueFormatter, tsFormatter, cssFormatter } from '@vtj/coder';
import { parseVue as vue2Dsl } from '@vtj/parser';
import { ParseVueDto } from './dto/parse-vue.dto';

@Injectable()
export class SchemasService {
  constructor(
    @InjectRepository(Schema) private repo: Repository<Schema>,
    private oss: OssService
  ) {}

  async save(dto: SchemaDto) {
    const model = new Schema();
    const schema = await this.findOne(dto.app, dto.type, dto.name);
    Object.assign(model, schema || {}, dto);
    model.content =
      typeof model.content === 'string'
        ? JSON.parse(model.content)
        : model.content;
    if (model.id) {
      return !!(await this.repo.update(model.id, model));
    }
    return !!(await this.repo.save(model));
  }

  findOne(app: string, type: SchemaType, name: string) {
    return this.repo.findOneBy({
      type,
      name,
      app
    });
  }

  findOneById(id: string) {
    return this.repo.findOneBy({
      id
    });
  }

  async findOneContent(app: string, type: SchemaType, name: string) {
    const item = await this.findOne(app, type, name);
    return typeof item?.content === 'string'
      ? JSON.parse(item.content)
      : item?.content;
  }

  find(app: string, type: SchemaType, name: string) {
    return this.repo.find({
      select: ['id', 'app', 'type', 'name', 'createdAt'],
      where: {
        app,
        type,
        name
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async search(dto: QuerySchemaDto) {
    const { page, limit, skip, take } = pager(dto);
    const [list, total] = await this.repo.findAndCount({
      skip,
      take,
      select: ['id', 'app', 'type', 'name', 'createdAt'],
      where: {
        app: dto.app || undefined,
        type: dto.type || undefined,
        name: dto.name || undefined
      },
      order: {
        createdAt: 'DESC'
      }
    });
    return {
      page,
      limit,
      total,
      list
    };
  }

  remove(app: string, type: SchemaType, name: string | string[]) {
    return this.repo.delete({
      app,
      type,
      name: In(toArray(name))
    });
  }

  removeApp(app: string) {
    return this.repo.delete({
      app
    });
  }

  removeByIds(ids: string[]) {
    return this.repo.delete(ids);
  }

  async genVueContent(app: string, dsl: BlockSchema, platform: PlatformType) {
    const project: ProjectSchema = await this.findOneContent(
      app,
      SchemaType.Project,
      app
    );
    const materials = await this.findOneContent(app, SchemaType.Material, app);
    const componentMap = new Map<string, MaterialDescription>(
      Object.entries(materials)
    );
    return await generator(
      dsl,
      componentMap,
      project.dependencies,
      platform
    ).catch((e) => {
      throw e;
    });
  }

  async createVueContent(
    dsl: BlockSchema,
    app: string,
    componentMap: Map<string, MaterialDescription>,
    dependencies: Dependencie[],
    platform: PlatformType
  ) {
    if (dsl) {
      return await generator(dsl, componentMap, dependencies, platform).catch(
        (e) => {
          throw e;
        }
      );
    }
    return null;
  }

  async parseVue(dto: ParseVueDto) {
    const { id, name, source, project } = dto;
    const dsl = await vue2Dsl({
      id,
      name,
      source,
      project: typeof project === 'string' ? JSON.parse(project) : project
    }).catch((e: any) => {
      return e;
    });

    return dsl;
  }

  createProjectPages(project: ProjectSchema) {
    const oPages = (project.uniConfig?.pagesJson?.pages || []).filter(
      (n: any) => !n.vtj
    );
    const pages = project.pages || [];

    const json: any[] = [];
    for (const page of pages) {
      if (page.id === project.homepage) {
        json.unshift({
          path: `pages/${project.id}/${page.id}`,
          style: page.style || {},
          needLogin: page.needLogin,
          vtj: true
        });
      } else {
        json.push({
          path: `pages/${project.id}/${page.id}`,
          style: page.style || {},
          needLogin: page.needLogin,
          vtj: true
        });
      }
    }
    for (const item of oPages) {
      const exist = json.find((n) => n.path === item.path);
      if (!exist) {
        json.push(item);
      }
    }
    return json;
  }

  async createUniApp(project: ProjectSchema) {
    const uniConfig = project.uniConfig || {};
    const names: string[] = [];
    const content: string[] = [];
    const APP_LIFE_CYCLE = [
      'onLaunch',
      'onShow',
      'onHide',
      'onError',
      'onPageNotFound',
      'onUnhandledRejection',
      'onThemeChange',
      'onPageNotFound',
      'onUniNViewMessage',
      'onExit'
    ];

    const vueAppTempalte = `
    <script setup lang="ts">
    {{ts}}
    </script>
    <style>
    {{css}}
    </style>
    `;
    for (const [name, value] of Object.entries(uniConfig)) {
      if (APP_LIFE_CYCLE.includes(name) && value.value) {
        names.push(name);
        const code = value.value.replace(/;\n/g, '\n');
        content.push(`${name}(${code})`);
      }
    }
    const tsCode = names.length
      ? `import {${names.join(',')}} from '@dcloudio/uni-app';${content.join(';\n')}`
      : `import { onLaunch } from '@dcloudio/uni-app';\n onLaunch(()=>{console.log('Launch')})`;
    const css = uniConfig.css || '';
    let result = vueAppTempalte.replace('{{ts}}', await tsFormatter(tsCode));
    result = result.replace('{{css}}', await cssFormatter(css));

    return await vueFormatter(result);
  }

  async genProject(app: string, project: ProjectSchema) {
    const platform = project.platform || 'web';
    const tempalte = `zip/${platform}/template.zip`;
    const zipFile = fs.readFileSync(resolve(tempalte));
    const zip = await JSZip.loadAsync(zipFile as any);
    const projectDsl: ProjectSchema = await this.findOneContent(
      app,
      SchemaType.Project,
      app
    );
    const materials = await this.findOneContent(app, SchemaType.Material, app);
    const componentMap = new Map<string, MaterialDescription>(
      Object.entries(materials)
    );

    const dirPath: string =
      projectDsl.platform === 'uniapp' ? './src/.vtj' : './.vtj';

    zip.file(
      `${dirPath}/projects/${project.id}.json`,
      JSON.stringify(projectDsl)
    );
    zip.file(
      `${dirPath}/materials/${project.id}.json`,
      JSON.stringify(materials)
    );

    const contents: any[] = [];
    for (const block of project.blocks) {
      const dsl: BlockSchema = await this.findOneContent(
        app,
        SchemaType.File,
        block.id
      );
      const content = await this.createVueContent(
        dsl,
        app,
        componentMap,
        project.dependencies,
        project.platform
      );
      if (content) {
        zip.file(
          `${dirPath}/files/${block.id}.json`,
          JSON.stringify(dsl, null, 2)
        );
        contents.push({ id: block.id, content });
      }
    }
    for (const page of project.pages) {
      const dsl: BlockSchema = await this.findOneContent(
        app,
        SchemaType.File,
        page.id
      );
      const content = await this.createVueContent(
        dsl,
        app,
        componentMap,
        project.dependencies,
        project.platform
      );
      if (content) {
        zip.file(`${dirPath}/files/${page.id}.json`, JSON.stringify(dsl));
        contents.push({ id: page.id, content });
      }
    }

    for (const item of contents) {
      const vueFilePath =
        platform === 'uniapp'
          ? `./src/pages/${project.id}/${item.id}.vue`
          : `./.vtj/vue/${item.id}.vue`;
      zip.file(vueFilePath, item.content);
    }

    if (platform === 'uniapp') {
      const { pagesJson, manifestJson } = projectDsl.uniConfig || {};
      const pages = this.createProjectPages(projectDsl);
      pagesJson.pages = pages;
      zip.file(`./src/pages.json`, JSON.stringify(pagesJson, null, 2));
      zip.file(`./src/manifest.json`, JSON.stringify(manifestJson, null, 2));
      zip.file('./src/App.vue', await this.createUniApp(projectDsl));
    }

    const pkg = fs.readJSONSync(resolve(`./zip/${platform}/package.json`));
    pkg.name = projectDsl.id;
    pkg.description = projectDsl.name;
    if (platform !== 'uniapp') {
      pkg.vtj.base = `/${projectDsl.id}`;
    } else {
      pkg.vtj.pageBasePath = '/pages';
      pkg.vtj.pageRouteName = projectDsl.id;
      pkg.vtj.noMask = true;
    }
    pkg.vtj.remote = 'https://lcdp.vtj.pro';
    zip.file(`./package.json`, JSON.stringify(pkg, null, 2));

    const blob = await zip
      .generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: { level: 9 }
      })
      .then((e) => e);
    const file = new File([blob], `${project.id}.zip`, {
      type: blob.type,
      lastModified: Date.now()
    });
    (file as any).buffer = Buffer.from(await blob.arrayBuffer());
    const { name } = await this.oss.upload(file as any, 'zip', '.zip');
    return this.oss.sign(name);
  }

  async count(start: string, end: string, type: string) {
    const builder = this.repo
      .createQueryBuilder('schemas')
      .select(`COUNT(DISTINCT schemas.id)`, 'count')
      .where('schemas.created_at > :start and schemas.created_at < :end', {
        start,
        end
      })
      .andWhere('schemas.type = :type', { type });

    const res = await builder.getRawOne();
    return Number(res?.count || 0) as number;
  }
}
