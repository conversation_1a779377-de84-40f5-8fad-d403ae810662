import { Entity, Column } from 'typeorm';
import { BaseEntity, PlatformType } from '../../shared';

@Entity({
  name: 'templates',
  comment: '模板',
  orderBy: {
    createdAt: 'DESC'
  }
})
export class Template extends BaseEntity {
  @Column({ comment: '模板名称编码' })
  name: string;

  @Column({ comment: '模板描述' })
  label: string;

  @Column({ comment: '模板分类' })
  category: string;

  @Column({ comment: '封面图片' })
  cover: string;

  @Column({ comment: '是否vip', default: false })
  vip: boolean;

  @Column({ comment: '简介详情', type: 'text', nullable: true })
  detail: string;

  @Column({ comment: '作者', nullable: true })
  author: string;

  @Column({ name: 'user_id', comment: 'UserId', nullable: true })
  userId: string;

  @Column({ comment: '是否公开', default: false })
  share: boolean;

  @Column({ comment: '安装次数', default: 0 })
  installed: number;

  @Column({ comment: '最新版本', nullable: true })
  latest: string;

  @Column('enum', {
    name: 'platform',
    enum: PlatformType,
    comment: '支持平台',
    nullable: true,
    default: 'Web'
  })
  platform: PlatformType;
}
