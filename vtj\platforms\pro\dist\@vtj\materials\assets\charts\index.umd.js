(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.VtjChartsMaterial=e())})(this,function(){"use strict";var p=Object.defineProperty,m=Object.defineProperties;var u=Object.getOwnPropertyDescriptors;var s=Object.getOwnPropertySymbols;var b=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable;var r=(t,e,a)=>e in t?p(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,o=(t,e)=>{for(var a in e||(e={}))b.call(e,a)&&r(t,a,e[a]);if(s)for(var a of s(e))y.call(e,a)&&r(t,a,e[a]);return t},i=(t,e)=>m(t,u(e));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const t="0.12.70";function e(c,g){return c.map(h=>i(o({},h),{package:g}))}const a={name:"XChart",label:"图表",categoryId:"base",props:[{name:"option",label:"option",title:"ECharts option",setters:"ObjectSetter"},{name:"width",label:"width",setters:["StringNumber"]},{name:"height",label:"height",setters:["StringNumber"]}],events:["highlight","downplay","selectchanged","legendselectchanged","legendselected","legendunselected","legendselectall","legendinverseselect","legendscroll","datazoom","datarangeselected","graphroam","georoam","treeroam","timelinechanged","timelineplaychanged","restore","dataviewchanged","magictypechanged","geoselectchanged","geoselected","geounselected","axisareaselected","brush","brushEnd","brushselected","globalcursortaken","rendered","finished"],snippet:{props:{width:"100%",height:"400px",option:{xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]},yAxis:{type:"value"},series:[{data:[150,230,224,218,135,147,260],type:"line"}]}}}},l={name:"XMapChart",label:"Geo地图",categoryId:"map",props:[{name:"option",label:"option",title:"ECharts option",setters:"ObjectSetter"},{name:"width",label:"width",setters:["StringSetter"]},{name:"height",label:"height",setters:["StringSetter"]},{name:"name",label:"地图名称",defaultValue:"china",setters:["StringSetter"]},{name:"geo-json",title:"文件路径或GeoJSON对象",defaultValue:"https://unpkg.com/vtj-geojson@0.1.3/geo/100000/100000.geoJson",setters:["StringSetter","ObjectSetter"]}],events:["ready",...a.events||[]],snippet:{props:{width:"100%",height:"400px",option:{series:[{data:[],type:"map"}]}}}},n="@vtj/charts",d=[a,l].flat();return{name:n,version:t,label:"图表",library:"VtjChartsMaterial",order:7,categories:[{id:"base",category:"基础图表"},{id:"map",category:"地图"},{id:"3D",category:"3D"}],components:e(d,n)}});
