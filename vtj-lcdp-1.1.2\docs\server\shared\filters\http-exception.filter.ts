import {
  ExceptionFilter,
  Catch,
  HttpException,
  HttpStatus,
  ArgumentsHost
} from '@nestjs/common';
import { Response } from 'express';
import { IResponseFail } from '../types';

@Catch()
export class HttpExecptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    const { message, cause } = exception;
    const causeInfo = (cause as any)?.message || cause;

    const res: IResponseFail = {
      code: status,
      success: false,
      timestamp: Date.now(),
      message,
      cause: causeInfo
    };

    response.status(status).json(res);
  }
}
