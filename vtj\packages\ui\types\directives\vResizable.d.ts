import { Directive, Ref } from 'vue';
import { useMouseInElement, Fn } from '@vueuse/core';
declare global {
    interface HTMLElement {
        __resizable__?: Resizable | null;
    }
}
export type UseMouseInElementReturn = ReturnType<typeof useMouseInElement>;
export type ResizableDir = 'n' | 's' | 'w' | 'e';
export interface ResizableOptions {
    disabled?: boolean;
    edge?: number;
    dirs?: ResizableDir[];
    onStart?: (dir: string, mie: UseMouseInElementReturn) => void;
    onEnd?: (dir: string, mie: UseMouseInElementReturn) => void;
    onResizing?: (dir: string, mie: UseMouseInElementReturn) => void;
    minWidth?: number;
    minHeight?: number;
    maxWidth?: number;
    maxHeight?: number;
}
export declare class Resizable {
    el: HTMLElement;
    options: ResizableOptions;
    private scope;
    resizing: Ref<boolean>;
    direction: Ref<string>;
    MIE: UseMouseInElementReturn | null;
    cleanMousedown?: Fn;
    cleanMouseup?: Fn;
    constructor(el: HTMLElement, options?: ResizableOptions);
    init(): void;
    resize(): void;
    getDirection(): string;
    destory(): void;
}
export declare const vResizable: Directive<HTMLElement, ResizableOptions>;
