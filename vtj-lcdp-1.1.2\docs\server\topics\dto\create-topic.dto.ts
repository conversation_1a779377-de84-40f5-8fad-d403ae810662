import { IsNotEmpty, IsEnum } from 'class-validator';
import { PlatformType } from '../types/constants';

export class CreateTopicDto {
  @IsNotEmpty()
  model: string;

  @IsNotEmpty()
  title: string;

  @IsNotEmpty()
  content: string;

  @IsNotEmpty()
  appId: string;

  @IsNotEmpty()
  projectId: string;

  @IsNotEmpty()
  projectName: string;

  @IsNotEmpty()
  userId: string;

  @IsNotEmpty()
  userName: string;

  @IsNotEmpty()
  fileId: string;

  @IsNotEmpty()
  fileName: string;

  @IsEnum(PlatformType)
  platform: PlatformType;
}
