import { PropType, InjectionKey, DefineComponent } from 'vue';
import { MenuDataItem, ActionBarItems, ActionProps, ActionMenuItem, ComponentPropsType, DialogProps } from '../';
import { Emits } from '../shared';
import { default as Mask } from './Mask';
export declare const TAB_ITEM_WIDTH = 140;
export declare const maskProps: {
    /**
     * 系统logo
     */
    logo: {
        type: StringConstructor;
        default: string;
    };
    /**
     * 系统标题
     */
    title: {
        type: StringConstructor;
        default: string;
    };
    /**
     * 菜单项数据
     */
    menus: {
        type: PropType<MenuDataItem[] | (() => Promise<MenuDataItem[]> | MenuDataItem[])>;
        default(): never[];
    };
    /**
     * 收藏菜单项
     */
    favorites: {
        type: PropType<MenuDataItem[] | (() => Promise<MenuDataItem[]> | MenuDataItem[])>;
        default(): never[];
    };
    /**
     * 菜单项数据适配函数，用作转换菜单项数据
     */
    menuAdapter: {
        type: PropType<(menu: MenuDataItem) => MenuDataItem>;
    };
    /**
     * 主页Tab配置
     */
    home: {
        type: PropType<string | MaskTab>;
        default: string;
    };
    /**
     * 最大tabs数量
     */
    tabs: {
        type: NumberConstructor;
        default: number;
    };
    /**
     * 右上角工具条配置
     */
    actions: {
        type: PropType<ActionBarItems>;
    };
    /**
     * 用户头像图片url
     */
    avatar: {
        type: StringConstructor;
    };
    /**
     * 开启主题切换
     */
    theme: {
        type: BooleanConstructor;
    };
    /**
     * 禁用框架容器
     */
    disabled: {
        type: BooleanConstructor;
    };
    addFavorite: {
        type: PropType<(menu: MenuDataItem) => void>;
    };
    removeFavorite: {
        type: PropType<(menu: MenuDataItem) => void>;
    };
    userCardWidth: {
        type: NumberConstructor;
        default: number;
    };
    /**
     * 内嵌纯净页面，不加背景和边距
     */
    pure: {
        type: BooleanConstructor;
    };
};
export type MaskProps = ComponentPropsType<typeof maskProps>;
export type MaskInstance = InstanceType<typeof Mask>;
export type MaskEmits = {
    select: [menu: MenuDataItem];
    actionClick: [action: ActionProps];
    actionCommand: [action: ActionProps, item: ActionMenuItem];
};
export type MaskEmitsFn = Emits<MaskEmits>;
export type MaskSlots = {
    default(): any;
    user(): any;
};
export interface MaskTab {
    id: string;
    name: string;
    url: string;
    title?: string;
    icon?: string | Record<string, any> | DefineComponent<any, any, any, any>;
    closable?: boolean;
    dialog?: DialogProps & {
        [index: string]: any;
    };
    menu?: MenuDataItem;
}
export type TabCreator = () => Promise<Partial<MaskTab>> | Partial<MaskTab>;
export declare const TAB_CREATORS_KEY: InjectionKey<Record<string, TabCreator>>;
export declare const MASK_KEY = "$mask";
