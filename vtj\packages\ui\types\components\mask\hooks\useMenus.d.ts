import { Ref, ShallowRef, ComputedRef } from 'vue';
import { MaskProps, MaskEmitsFn } from '../types';
import { MenuDataItem } from '../../';
export declare function useMenus(props: MaskProps, emit: MaskEmitsFn): {
    menus: ShallowRef<MenuDataItem[], MenuDataItem[]>;
    favorites: ShallowRef<MenuDataItem[], MenuDataItem[]>;
    flatMenus: ComputedRef<MenuDataItem[]>;
    active: Ref<MenuDataItem | null, MenuDataItem | null>;
    select: (id: string | number | MenuDataItem) => void;
    toggleFavorite: (item: MenuDataItem) => void;
};
