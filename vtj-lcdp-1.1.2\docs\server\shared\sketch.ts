import { isPlainObject } from '@vtj/node';

const excludes = [
  'id',
  'exportFormats',
  'selected',
  'locked',
  'symbolId',
  'path',
  'horizontalSizing',
  'verticalSizing',
  'horizontalPins',
  'verticalPins',
  'groupBehavior',
  'sharedStyleId',
  'blendingMode',
  'hidden',
  'closed',
  'isDefault',
  'editable',
  'symbolOverride',
  'lineSpacing',
  'points',
  'borderOptions',
  'enabled',
  'pattern',
  'styleType',
  'paragraphSpacing',
  'kerning',
  'textTransform',
  'boundVariables',
  'constraints'
];

const compressObject = (obj: any) => {
  for (const [key, value] of Object.entries(obj)) {
    if (excludes.includes(key)) {
      delete obj[key];
    }
    if (
      value === 0 ||
      value === null ||
      value === false ||
      value === 'Normal' ||
      (Array.isArray(value) && value.length === 0)
    ) {
      delete obj[key];
    }

    if (value && typeof value === 'object' && Object.keys(value).length === 0) {
      delete obj[key];
    }
  }
  return obj;
};

export const compressSketchJson = (data: any) => {
  compressObject(data);
  for (const value of Object.values(data)) {
    if (Array.isArray(value)) {
      for (const item of value) {
        if (isPlainObject(item)) {
          compressSketchJson(item);
        }
      }
    } else if (isPlainObject(value)) {
      compressSketchJson(value);
    }
  }
  if (data.type === 'Override') {
    delete data.value;
  }
};
