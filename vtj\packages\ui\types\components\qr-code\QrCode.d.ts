import { QrCodeProps } from './types';
import { DefineComponent, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: {
        tip?(_: {}): any;
    };
    refs: {};
    rootEl: HTMLDivElement;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: DefineComponent<QrCodeProps, {
    refresh: () => void;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    expired: () => any;
    draw: (value: string) => any;
}, string, PublicProps, Readonly<QrCodeProps> & Readonly<{
    onExpired?: (() => any) | undefined;
    onDraw?: ((value: string) => any) | undefined;
}>, {
    size: number;
    tip: string;
}, {}, {}, {}, string, ComponentProvideOptions, false, {}, HTMLDivElement>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
