import {
  Controller,
  Get,
  Post,
  Body,
  Delete,
  Query,
  Param
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { RoleDto } from './dto/role.dto';
import { QueryRoleDto } from './dto/quey-role.dto';
import { Access } from '../access/entities/access.entity';

@Controller('roles')
export class RolesController {
  constructor(private readonly service: RolesService) {}

  @Post()
  save(@Body() dto: RoleDto) {
    return this.service.save(dto);
  }

  @Get()
  find(@Query() dto: QueryRoleDto) {
    return this.service.find(dto);
  }

  @Delete()
  remove(@Body() ids: string[]) {
    return this.service.remove(ids);
  }

  @Post('default/:id')
  setDefault(@Param('id') id: string) {
    return this.service.setDefault(id);
  }

  @Get('access/:id')
  getAccess(@Param('id') id: string) {
    return this.service.getAccess([id]);
  }

  @Post('access/:id')
  saveAccess(@Param('id') id: string, @Body() access: Access[]) {
    return this.service.saveAccess(id, access);
  }
}
