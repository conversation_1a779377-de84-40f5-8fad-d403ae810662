import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { uid, dateFormat } from '@vtj/node';
import OSS from 'ali-oss';
import { AliOSS } from '../shared';

@Injectable()
export class OssService {
  private client: InstanceType<typeof OSS>;
  constructor(config: ConfigService) {
    const oss = config.get<AliOSS>('oss');
    this.client = new OSS(oss);
  }
  /**
   * 文件签名
   * @param path
   * @param expires
   * @param width
   * @returns
   */
  sign(path: string, expires: number = 1800, width?: number | string) {
    const url = this.client.signatureUrl(path, {
      expires,
      process: width ? `image/resize,w_${width}` : undefined
    });
    return url.replace('http://', 'https://');
  }
  /**
   * 上传文件
   * @param file 文件对象
   * @param dir 文件存放目录
   * @param extname 文件后缀名，如：.jpg
   * @returns
   */
  async upload(file: Express.Multer.File, dir?: string, extname?: string) {
    const ext = extname || '.' + file.mimetype.split('/')[1];
    const id = uid();
    const filename = id + ext;
    const date = dateFormat(new Date(), 'YYYY-MM-DD');
    const path = dir ? `${dir}/${date}/${filename}` : `${date}/${filename}`;
    return await this.client.put(path, file.buffer || file);
  }
}
