import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { Role } from './entities/role.entity';
import { AccessModule } from '../access/access.module';
import { AccessService } from '../access/access.service';

@Module({
  imports: [TypeOrmModule.forFeature([Role]), AccessModule],
  controllers: [RolesController],
  providers: [RolesService, AccessService],
  exports: [TypeOrmModule, RolesService, AccessService]
})
export class RolesModule {}
