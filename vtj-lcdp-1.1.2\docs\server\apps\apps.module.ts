import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppsService } from './apps.service';
import { AppsController } from './apps.controller';
import { App } from './entities/app.entity';
import { AccessModule } from '../access/access.module';
import { AccessService } from '../access/access.service';
import { SchemasModule } from '../schemas/schemas.module';
import { SchemasService } from '../schemas/schemas.service';

@Module({
  imports: [TypeOrmModule.forFeature([App]), AccessModule, SchemasModule],
  controllers: [AppsController],
  providers: [AppsService, AccessService, SchemasService],
  exports: [TypeOrmModule, AppsService, AccessService, SchemasService]
})
export class AppsModule {}
