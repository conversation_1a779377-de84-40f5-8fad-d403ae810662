import {
  Controller,
  Post,
  Body,
  Get,
  Query,
  Delete,
  Req
} from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportDto } from './dto/report.dto';
import { QueryReportDto } from './dto/query-report.dto';
import { Request } from 'express';
import { getIP } from '../shared';

@Controller('report')
export class ReportController {
  constructor(private readonly service: ReportService) {}

  @Post()
  save(@Body() dto: ReportDto, @Req() req: Request) {
    dto.ip = getIP(req);
    dto.source =
      typeof dto.source === 'string' ? JSON.parse(dto.source) : dto.source;
    return this.service.save(dto);
  }

  @Get()
  find(@Query() dto: QueryReportDto) {
    return this.service.find(dto);
  }

  @Delete()
  removeGroups(@Body() ids: string[]) {
    return this.service.remove(ids);
  }
}
