import {
  <PERSON>,
  Get,
  Param,
  Res,
  Query,
  Post,
  UseInterceptors,
  UploadedFile,
  Body,
  Req,
  Sse,
  HttpException
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { unBase64 } from '@vtj/node';
import { OpenService } from './open.service';
import { Public, getIP } from '../shared';
import type { Response, Request } from 'express';
import { PublishTemplateDto } from './dto/publish-template.dto';
import { UserTopicDto } from '../topics/dto/user-topic.dto';
import { UserChatDto } from '../topics/dto/user-chat.dto';

@Controller('open')
export class OpenController {
  constructor(private readonly open: OpenService) {}

  response(res: Response, result: any, fail?: boolean) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Content-Type', 'application/json');
    let ret = {
      code: 0,
      success: !fail,
      data: fail ? null : result,
      message: ''
    };
    if (result instanceof HttpException) {
      ret = {
        code: result.getStatus(),
        success: false,
        data: null,
        message: result.message
      };
    }
    res.send(JSON.stringify(ret));
  }

  @Public()
  @Get('auth/:code')
  async auth(@Param('code') code: string, @Res() res: any) {
    return this.open.loginBySign(code, res);
  }

  @Public()
  @Get('user/:token')
  getLoginUser(@Param('token') token: string, @Res() res: any) {
    return this.open.getUserByToken(token, res);
  }

  @Public()
  @Get('templates')
  getTemplates(
    @Query('platform') platform: string,
    @Query('token') token: string,
    @Res() res: any
  ) {
    return this.open.getTemplates(res, platform || 'web', token);
  }

  @Public()
  @Get('template/:token')
  getTemplate(
    @Param('token') token: string,
    @Query('id') templateId: string,
    @Res() res: any
  ) {
    return this.open.getTemplateById(token, templateId, res);
  }

  @Public()
  @Get('template/remove/:token')
  removeTemplate(
    @Param('token') token: string,
    @Query('id') templateId: string,
    @Res() res: any
  ) {
    return this.open.removeOwnerTemplate(token, templateId, res);
  }

  @Public()
  @Get('dsl/:token')
  getLatestDsl(
    @Param('token') token: string,
    @Query('id') templateId: string,
    @Res() res: any
  ) {
    return this.open.getLatestTemplateDsl(token, templateId, res);
  }

  @Public()
  @Get('dict/:code')
  async getDict(@Param('code') code: string, @Res() res: any) {
    return this.open.getDict(code, res);
  }

  @Public()
  @Post('template/publish/:token')
  @UseInterceptors(FileInterceptor('cover'))
  async publishTemplate(
    @Param('token') token: string,
    @UploadedFile() cover: Express.Multer.File,
    @Body() body: PublishTemplateDto,
    @Res() res: Response
  ) {
    const dto = {
      ...body,
      cover
    };
    const result = await this.open.publishTemplate(dto, token);
    this.response(res, result);
    return true;
  }

  @Public()
  @Post('report')
  async report(
    @Body('data') data: any,
    @Res() res: Response,
    @Req() req: Request
  ) {
    const jsonStr = unBase64(data);
    const json = JSON.parse(jsonStr);
    json.ip = getIP(req);
    const result = await this.open.reportData(json);
    this.response(res, !!result);
  }

  @Public()
  @Get('report')
  reportJsonp(
    @Query('data') data: string,
    @Req() req: Request,
    @Res() res: any
  ) {
    const jsonStr = unBase64(data);
    const json = JSON.parse(jsonStr);
    json.ip = getIP(req);
    return this.open.reportDataJsop(json, res);
  }

  @Public()
  @Post('topic/post/:token')
  async postTopic(
    @Param('token') token: string,
    @Body() body: UserTopicDto,
    @Res() res: Response
  ) {
    const result = await this.open.createTopic(body, token);
    this.response(res, result);
  }

  @Public()
  @Post('topic/image/:token')
  @UseInterceptors(FileInterceptor('file'))
  async postImageTopic(
    @Param('token') token: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Res() res: Response
  ) {
    const result = await this.open.postImageTopic(token, file, body);
    this.response(res, result);
  }

  @Public()
  @Post('topic/json/:token')
  @UseInterceptors(FileInterceptor('file'))
  async postJsonTopic(
    @Param('token') token: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Res() res: Response
  ) {
    const result = await this.open.postJsonTopic(token, file, body);
    this.response(res, result);
  }

  @Public()
  @Get('topic/list/:token')
  async getTopics(
    @Param('token') token: string,
    @Query('id') fileId: string,
    @Res() res: Response
  ) {
    const result = await this.open.getTopics(fileId, token);
    this.response(res, result);
  }

  @Public()
  @Get('topic/hot')
  async getHotTopics(@Res() res: Response) {
    const result = await this.open.getHotTopics();
    this.response(res, result);
  }

  @Public()
  @Get('chat/list/:token')
  async getChats(
    @Param('token') token: string,
    @Query('id') topicId: string,
    @Res() res: Response
  ) {
    const result = await this.open.getChats(topicId, token);
    this.response(res, result);
  }

  @Public()
  @Post('chat/post/:token')
  async postChat(
    @Param('token') token: string,
    @Body() body: UserChatDto,
    @Res() res: Response
  ) {
    const result = await this.open.createChat(body, token);
    this.response(res, result);
  }

  @Public()
  @Post('chat/save/:token')
  async saveChat(
    @Param('token') token: string,
    @Body() body: any,
    @Res() res: Response
  ) {
    const result = await this.open.saveChat(body, token);
    this.response(res, result);
  }

  @Public()
  @Sse('completions/:token')
  async chat(
    @Param('token') token: string,
    @Query('id') chatId: string,
    @Query('tid') topicId: string
  ) {
    return await this.open.completion(topicId, chatId, token);
  }

  @Public()
  @Get('chat/cancel/:token')
  async cancelCompletions(
    @Param('token') token: string,
    @Query('id') chatId: string
  ) {
    return await this.open.cancelCompletions(token, chatId);
  }

  @Public()
  @Get('topic/remove/:token')
  async removeTopic(
    @Param('token') token: string,
    @Query('id') topicId: string,
    @Res() res: Response
  ) {
    const result = await this.open.removeTopic(topicId, token);
    this.response(res, result);
  }

  @Public()
  @Get('settings/:token')
  async getSettings(@Param('token') token: string, @Res() res: Response) {
    const result = await this.open.getSettings(token);
    this.response(res, result);
  }

  @Public()
  @Post('order/:token')
  async createOrder(@Param('token') token: string, @Res() res: Response) {
    const result = await this.open.createOrder(token);
    this.response(res, result);
  }

  @Public()
  @Get('order/:token')
  async getOrder(
    @Param('token') token: string,
    @Query('id') id: string,
    @Res() res: Response
  ) {
    const result = await this.open.getOrder(token, id);
    this.response(res, result);
  }

  @Public()
  @Get('order/cancel/:token')
  async cancelOrder(
    @Param('token') token: string,
    @Query('id') id: string,
    @Res() res: Response
  ) {
    const result = await this.open.cancelOrder(token, id);
    this.response(res, result);
  }

  @Public()
  @Post('upload/:token')
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @Param('token') token: string,
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response
  ) {
    const result = await this.open.upload(token, file);
    this.response(res, result);
    return true;
  }
}
