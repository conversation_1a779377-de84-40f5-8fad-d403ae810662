import { Controller, Get, Body, Delete, Query } from '@nestjs/common';
import { CacheService } from './cache.service';
import { QueryCacheDto } from './dto/query-cache.dto';

@Controller('cache')
export class CacheController {
  constructor(private readonly service: CacheService) {}

  @Get()
  find(@Query() dto: QueryCacheDto) {
    return this.service.find(dto);
  }

  @Delete()
  remove(@Body() idArray: number[]) {
    return this.service.remove(idArray);
  }

  @Delete('clear')
  clear() {
    return this.service.removeAll();
  }
}
