import { Directive, MaybeRef } from 'vue';
import { UseDraggableOptions } from '@vueuse/core';
declare global {
    interface HTMLElement {
        __draggable__?: Draggable | null;
    }
}
export interface DraggableOptions extends UseDraggableOptions {
    /**
     * 限制在元素范围内拖拽
     */
    target?: MaybeRef<HTMLElement> | string;
    /**
     * 拖拽 handle 选择器
     */
    selector?: string;
    /**
     *  禁用
     */
    disabled?: boolean;
    /**
     * 延时
     */
    delay?: number;
    /**
     * 目标边缘距离
     */
    edge?: number;
}
export declare class Draggable {
    el: HTMLElement;
    options: DraggableOptions;
    private scope;
    dragging: boolean;
    constructor(el: HTMLElement, options?: DraggableOptions);
    private getHandle;
    private getTarget;
    init(): void;
    getPosition(targetRect: DOMRect, rect: DOMRect, x: number, y: number): {
        x: number;
        y: number;
    };
    destory(): void;
}
export declare const vDraggable: Directive<HTMLElement, DraggableOptions>;
