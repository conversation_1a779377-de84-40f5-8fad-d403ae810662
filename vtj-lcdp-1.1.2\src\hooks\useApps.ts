import { ref, type Ref } from 'vue';
import { findLowCodeApps } from '@/apis';
import type { LowCodeAppVO } from '@/shared';
export function useApps() {
  const list: Ref<LowCodeAppVO[]> = ref([]);

  const load = () => {
    findLowCodeApps({ page: 1, limit: 9999 })
      .then((res) => {
        const _list: LowCodeAppVO[] = res?.list.map((item) => {
          return {
            ...item
          } as LowCodeAppVO;
        });
        list.value = _list;
      })
      .catch(() => {
        list.value = [];
      });
  };

  load();

  return {
    list,
    load
  };
}
