import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../../shared';
import { SettingMode } from '../types';

@Entity({
  name: 'settings',
  comment: 'AI配置信息',
  orderBy: {
    createdAt: 'DESC'
  }
})
export class Setting extends BaseEntity {
  @Column({
    type: 'enum',
    comment: '运营模式',
    enum: SettingMode,
    default: SettingMode.Free
  })
  mode: SettingMode;

  @Column({
    name: 'limit',
    comment: '免费体验次数',
    default: 0
  })
  limit: number;

  @Column({
    type: 'decimal',
    precision: 4,
    scale: 1,
    name: 'max',
    comment: '月最大token数量，单位百万',
    default: 0
  })
  max: number;

  @Column({
    type: 'decimal',
    name: 'price',
    precision: 6,
    scale: 2,
    comment: '月订阅价格',
    default: 0
  })
  price: number;

  @Column({
    type: 'text',
    name: 'prompt_template',
    comment: '系统提示词模版',
    nullable: true
  })
  promptTemplate: string;

  @Column({
    type: 'text',
    name: 'image_prompt_template',
    comment: '图片识别系统提示词模版',
    nullable: true
  })
  imagePromptTemplate: string;

  @Column({
    type: 'text',
    name: 'json_prompt_template',
    comment: '元数据识别系统提示词模版',
    nullable: true
  })
  jsonPromptTemplate: string;

  @Column({ name: 'pay_qr', comment: '支付二维码', nullable: true })
  payQr: string;

  @Column({ name: 'contact_qr', comment: '联系人二维码', nullable: true })
  contactQr: string;

  @Column({ name: 'group_qr', comment: '微信群二维码', nullable: true })
  groupQr: string;

  @Column({
    name: 'image_model',
    comment: '图片识别大模型名称',
    nullable: true
  })
  imageModel: string;

  @Column({
    name: 'image_api_key',
    comment: '图片识别大模型apiKey',
    nullable: true
  })
  imageApiKey: string;

  @Column({
    name: 'json_model',
    comment: '元数据识别大模型名称',
    nullable: true
  })
  jsonModel: string;

  @Column({
    name: 'json_api_key',
    comment: '元数据识别大模型apiKey',
    nullable: true
  })
  jsonApiKey: string;

  @Column({ name: 'mail_pass', comment: '邮件服务登登录码', nullable: true })
  mailPass: string;
}
