import { request } from '@vtj/utils';

/**
 * 全局请求配置选项接口
 */
export interface GlobalRequestOptions {
  notify?: (msg: string) => void;
  loading?: () => any;
}

/**
 * 设置全局请求配置
 * @param options - 请求配置选项，包含通知和加载状态回调
 * @returns 配置后的request实例
 */
export function setGlobalRequest(options: GlobalRequestOptions) {
  let _loading: any = null;
  const { notify, loading } = options;
  request.setConfig({
    settings: {
      type: 'form', // 默认表单格式提交
      validSuccess: true, // 验证成功状态
      originResponse: false, // 不返回原始响应
      loading: true, // 启用加载状态
      validate: (res: any) => {
        // 响应验证函数
        return res.data?.code === 0 || !!res.data?.success;
      },
      failMessage: true, // 启用失败消息提示
      showError: (msg: string) => {
        // 错误消息显示回调
        if (notify) {
          notify(msg || '未知错误');
        }
      },
      showLoading: () => {
        // 显示加载状态回调
        if (_loading) {
          _loading.close();
        }
        if (loading) {
          _loading = loading();
        }
      },
      hideLoading: () => {
        // 隐藏加载状态回调
        if (_loading) {
          _loading.close();
          _loading = null;
        }
      }
    }
  });
  return request;
}
