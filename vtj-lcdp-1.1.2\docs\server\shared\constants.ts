/**
 * 查询条件操作符
 */
export const CONDITION_OPERATORS = [
  '=',
  '>',
  '>=',
  '<',
  '<=',
  'in',
  'like'
] as const;

/**
 * 数据库操作类型
 */
export enum OperationType {
  Insert = 'Insert',
  Update = 'Update',
  Delete = 'Delete',
  Login = 'Login'
}

/**
 * 权限类型
 */
export enum AccessType {
  App = 'App',
  Dir = 'Dir',
  Menu = 'Menu',
  Action = 'Action'
}

/**
 * 操作日志数据库表名称
 */
export const LOG_TABLE_NAME = 'logs';

/**
 * 不记录变更日志的表
 */
export const LOG_TABLE_EXCLUDES = [LOG_TABLE_NAME, 'caches'];

/**
 * 最小日期
 */
export const MIN_DATE = new Date('1900-01-01');

/**
 * 最大日期
 */
export const MAX_DATE = new Date('2099-12-30');

/**
 * 第三方登录方式
 */
export enum OAuthType {
  Gitee = 'Gitee',
  Github = 'Github'
}

/**
 * 加密盐
 */
export const SALT = '';

/**
 * 登录有效期一天
 */
export const USER_LOGIN_EXPIRED = 24 * 60 * 60 * 1000;

/**
 * 新增用户初始化密码
 */
export const USER_INIT_PASSWORD = '';

/**
 * 公开的接口标识
 */
export const IS_PUBLIC_KEY = 'IS_PUBLIC';

/**
 * 接口请求类型
 */
export enum ApiMethod {
  Get = 'Get',
  Post = 'Post',
  Delete = 'Delete',
  Patch = 'Patch',
  Put = 'Put'
}

/**
 * 加密公钥
 */
export const RSA_PUBLIC_KEY = `xxx`;

/**
 * 解密私钥
 */
export const RSA_PRIVATE_KEY = `xxx`;

/**
 * 平台类型
 */
export enum PlatformType {
  Web = 'Web',
  H5 = 'H5',
  UniApp = 'UniApp'
}

/**
 * DSL类型
 */
export enum SchemaType {
  Project = 'project',
  Material = 'material',
  File = 'file',
  History = 'history',
  HistoryItem = 'history-item'
}

/**
 * 报告类型
 */
export enum ReportType {
  Init = 'init',
  online = 'online',
  Event = 'event',
  Error = 'error'
}

/**
 * AI会话类型
 */
export enum ChatType {
  Create = 'create',
  Update = 'update'
}

/**
 * 对话角色
 */
export enum CompletionRole {
  System = 'system',
  User = 'user',
  Assistant = 'assistant',
  Tool = 'tool'
}

/**
 * 对话状态
 */
export enum CompletionStatus {
  Pending = 'pending',
  Success = 'success',
  Fail = 'fail',
  Error = 'error'
}

/**
 * 应用可访问范围
 */
export enum AppScopeType {
  // 公开，无需任何身份验证即可访问该项目
  Public = 'public',
  // 非公开，仅限自己访问
  Private = 'private',
  // 内部公开，任何登录的用户都可以访问该项目
  Protected = 'protected'
}
