export type LoggerLevel = 'debug' | 'log' | 'info' | 'warn' | 'error';
interface Options {
    level?: LoggerLevel;
    bizName?: string;
}
declare class Logger {
    config: {
        targetLevel: LoggerLevel;
        targetBizName: string;
    };
    options: Options;
    constructor(options: Options);
    private _log;
    debug(...args: any[]): any;
    log(...args: any[]): any;
    info(...args: any[]): any;
    warn(...args: any[]): any;
    error(...args: any[]): any;
}
export declare function getLogger(config: {
    level: LoggerLevel;
    bizName: string;
}): Logger;
declare const logger: Logger;
export { Logger, logger };
