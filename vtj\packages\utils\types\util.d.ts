/**
 * 是否浏览器环境
 */
export declare const isClient: boolean;
/**
 *  文件对象 File 转换为 base64
 * @param file
 * @returns
 */
export declare const fileToBase64: (file: File) => Promise<string>;
/**
 * FormData 转换为json
 * @param data
 * @returns
 */
export declare function formDataToJson(data: FormData): Record<string, any>;
/**
 *  Base64 转换 Blob
 * @param dataurl
 * @returns
 */
export declare function dataURLtoBlob(dataurl: string): Blob;
/**
 * Blob 转 File
 * @param blob
 * @param fileName
 * @returns
 */
export declare function blobToFile(blob: Blob, fileName: string): File;
