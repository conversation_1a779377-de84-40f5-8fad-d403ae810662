import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON><PERSON>, JoinTable } from 'typeorm';
import { BaseEntity } from '../../shared';
import { Access } from '../../access/entities/access.entity';

@Entity({
  name: 'roles',
  comment: '用户角色',
  orderBy: {
    order: 'ASC'
  }
})
export class Role extends BaseEntity {
  @Column({ comment: '角色名称编码', unique: true })
  name: string;

  @Column({ comment: '角色名称描述' })
  label: string;

  @Column({ comment: '排序' })
  order: number;

  @Column({ name: 'is_default', comment: '是否默认角色', default: false })
  isDefault: boolean;

  @Column({ name: 'is_super', comment: '是否超级管理员', default: false })
  isSuper: boolean;

  @Column({ comment: '备注' })
  notes: string;

  @ManyToMany(() => Access, {
    cascade: true
  })
  @JoinTable({ name: 'role_r_access' })
  access: Access[];
}
