import { ComponentPropsType } from '../shared';
export declare const testProps: {
    stringProp: {
        type: StringConstructor;
    };
    booleanProp: {
        type: BooleanConstructor;
    };
    numberProp: {
        type: NumberConstructor;
    };
    selectProp: {
        type: StringConstructor;
    };
    objectProp: {
        type: ObjectConstructor;
    };
    arrayProp: {
        type: ArrayConstructor;
    };
    iconProp: {
        type: StringConstructor;
    };
    colorProp: {
        type: StringConstructor;
    };
    modelValue: {
        type: StringConstructor;
    };
    syncProp: {
        type: StringConstructor;
    };
};
export type TestProps = ComponentPropsType<typeof testProps>;
export type TestEmits = {
    click: [props: TestProps];
    submit: [props: TestProps];
    change: [data: any];
    'update:modelValue': [value?: string];
    'update:syncProp': [value?: string];
};
