import { ComponentInternalInstance, VNodeProps, AllowedComponentProps, ComponentCustomProps, Slot, ComponentPublicInstance, ComponentOptionsBase, ExtractPropTypes, PropType, Reactive, ComponentOptionsMixin, GlobalComponents, GlobalDirectives, ComponentProvideOptions, DebuggerEvent, nextTick, WatchOptions, WatchStopHandle, ShallowUnwrapRef, ComponentCustomProperties, Ref, DefineComponent, PublicProps } from 'vue';
import { ElTooltipProps, FormItemRule, FormItemProp, FormValidateCallback, FormValidationResult, FormItemContext } from 'element-plus';
import { FormModel } from '../form';
import { OnCleanup } from '@vue/reactivity';
import { QueryFormItems } from './types';
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: Partial<Record<string, (_: {}) => any>> & {
        default?(_: {}): any;
    };
    refs: {
        formRef: ({
            $: ComponentInternalInstance;
            $data: {};
            $props: Partial<{
                footer: boolean;
                inline: boolean;
                footerAlign: "left" | "right" | "center";
                tooltipMessage: boolean | Partial< ElTooltipProps>;
                model: Record<string, any>;
                submitText: string | null;
                resetText: string | null;
                enterSubmit: boolean;
                sticky: boolean;
            }> & Omit<{
                readonly footer: boolean;
                readonly inline: boolean;
                readonly footerAlign: "left" | "right" | "center";
                readonly submitText: string | null;
                readonly resetText: string | null;
                readonly enterSubmit: boolean;
                readonly sticky: boolean;
                readonly tooltipMessage?: boolean | Partial< ElTooltipProps> | undefined;
                readonly model?: Record<string, any> | undefined;
                readonly inlineColumns?: number | undefined;
                readonly submitMethod?: ((model: FormModel) => Promise<any>) | undefined;
                readonly onReset?: (() => any) | undefined;
                readonly onSubmit?: ((model: Record<string, any>) => any) | undefined;
                readonly onChange?: ((model: Record<string, any>) => any) | undefined;
            } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky">;
            $attrs: {
                [x: string]: unknown;
            };
            $refs: {
                [x: string]: unknown;
            } & {
                formRef: ({
                    $: ComponentInternalInstance;
                    $data: {};
                    $props: Partial<{
                        readonly disabled: boolean;
                        readonly inline: boolean;
                        readonly labelWidth: string | number;
                        readonly labelPosition: "top" | "left" | "right";
                        readonly inlineMessage: boolean;
                        readonly showMessage: boolean;
                        readonly requireAsteriskPosition: "left" | "right";
                        readonly labelSuffix: string;
                        readonly validateOnRuleChange: boolean;
                        readonly scrollIntoViewOptions: boolean | Record<string, any>;
                        readonly statusIcon: boolean;
                        readonly hideRequiredAsterisk: boolean;
                        readonly scrollToError: boolean;
                    }> & Omit<{
                        readonly disabled: boolean;
                        readonly inline: boolean;
                        readonly labelWidth: string | number;
                        readonly labelPosition: "top" | "left" | "right";
                        readonly inlineMessage: boolean;
                        readonly showMessage: boolean;
                        readonly requireAsteriskPosition: "left" | "right";
                        readonly labelSuffix: string;
                        readonly statusIcon: boolean;
                        readonly validateOnRuleChange: boolean;
                        readonly hideRequiredAsterisk: boolean;
                        readonly scrollToError: boolean;
                        readonly scrollIntoViewOptions: boolean | Record<string, any>;
                        readonly size?: ("" | "large" | "default" | "small") | undefined;
                        readonly rules?: Partial<Record<string, FormItemRule | FormItemRule[]>> | undefined;
                        readonly model?: Record<string, any> | undefined;
                        onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined | undefined;
                    } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "disabled" | "inline" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions">;
                    $attrs: {
                        [x: string]: unknown;
                    };
                    $refs: {
                        [x: string]: unknown;
                    };
                    $slots: Readonly<{
                        [name: string]: Slot<any> | undefined;
                    }>;
                    $root: ComponentPublicInstance | null;
                    $parent: ComponentPublicInstance | null;
                    $host: Element | null;
                    $emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
                    $el: any;
                    $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                        readonly model: ObjectConstructor;
                        readonly rules: {
                            readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        };
                        readonly labelPosition: {
                            readonly type: PropType<"top" | "left" | "right">;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: "right";
                        };
                        readonly requireAsteriskPosition: {
                            readonly type: PropType<"left" | "right">;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: "left";
                        };
                        readonly labelWidth: {
                            readonly type: PropType<string | number>;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: "";
                        };
                        readonly labelSuffix: {
                            readonly type: PropType<string>;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: "";
                        };
                        readonly inline: BooleanConstructor;
                        readonly inlineMessage: BooleanConstructor;
                        readonly statusIcon: BooleanConstructor;
                        readonly showMessage: {
                            readonly type: PropType<boolean>;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: true;
                        };
                        readonly validateOnRuleChange: {
                            readonly type: PropType<boolean>;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: true;
                        };
                        readonly hideRequiredAsterisk: BooleanConstructor;
                        readonly scrollToError: BooleanConstructor;
                        readonly scrollIntoViewOptions: {
                            readonly type: PropType<boolean | Record<string, any>>;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        } & {
                            readonly default: true;
                        };
                        readonly size: {
                            readonly type: PropType<"" | "large" | "default" | "small">;
                            readonly required: false;
                            readonly validator: ((val: unknown) => boolean) | undefined;
                            __epPropKey: true;
                        };
                        readonly disabled: BooleanConstructor;
                    }>> & {
                        onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
                    }, {
                        validate: (callback?: FormValidateCallback) => FormValidationResult;
                        validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                        resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                        clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                        scrollToField: (prop: FormItemProp) => void;
                        getField: (prop: FormItemProp) => FormItemContext | undefined;
                        fields: Reactive< FormItemContext[]>;
                    }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
                        validate: (prop: FormItemProp, isValid: boolean, message: string) => void;
                    }, string, {
                        readonly disabled: boolean;
                        readonly inline: boolean;
                        readonly labelWidth: string | number;
                        readonly labelPosition: "top" | "left" | "right";
                        readonly inlineMessage: boolean;
                        readonly showMessage: boolean;
                        readonly requireAsteriskPosition: "left" | "right";
                        readonly labelSuffix: string;
                        readonly validateOnRuleChange: boolean;
                        readonly scrollIntoViewOptions: boolean | Record<string, any>;
                        readonly statusIcon: boolean;
                        readonly hideRequiredAsterisk: boolean;
                        readonly scrollToError: boolean;
                    }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                        beforeCreate?: (() => void) | (() => void)[];
                        created?: (() => void) | (() => void)[];
                        beforeMount?: (() => void) | (() => void)[];
                        mounted?: (() => void) | (() => void)[];
                        beforeUpdate?: (() => void) | (() => void)[];
                        updated?: (() => void) | (() => void)[];
                        activated?: (() => void) | (() => void)[];
                        deactivated?: (() => void) | (() => void)[];
                        beforeDestroy?: (() => void) | (() => void)[];
                        beforeUnmount?: (() => void) | (() => void)[];
                        destroyed?: (() => void) | (() => void)[];
                        unmounted?: (() => void) | (() => void)[];
                        renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                        renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                        errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
                    };
                    $forceUpdate: () => void;
                    $nextTick: nextTick;
                    $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
                } & Readonly<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }> & Omit<Readonly< ExtractPropTypes<{
                    readonly model: ObjectConstructor;
                    readonly rules: {
                        readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly labelPosition: {
                        readonly type: PropType<"top" | "left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "right";
                    };
                    readonly requireAsteriskPosition: {
                        readonly type: PropType<"left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "left";
                    };
                    readonly labelWidth: {
                        readonly type: PropType<string | number>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly labelSuffix: {
                        readonly type: PropType<string>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly inline: BooleanConstructor;
                    readonly inlineMessage: BooleanConstructor;
                    readonly statusIcon: BooleanConstructor;
                    readonly showMessage: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly validateOnRuleChange: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly hideRequiredAsterisk: BooleanConstructor;
                    readonly scrollToError: BooleanConstructor;
                    readonly scrollIntoViewOptions: {
                        readonly type: PropType<boolean | Record<string, any>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly size: {
                        readonly type: PropType<"" | "large" | "default" | "small">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly disabled: BooleanConstructor;
                }>> & {
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
                }, "disabled" | "inline" | "clearValidate" | "validate" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "fields" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions" | "validateField" | "resetFields" | "scrollToField" | "getField"> & ShallowUnwrapRef<{
                    validate: (callback?: FormValidateCallback) => FormValidationResult;
                    validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                    resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                    clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                    scrollToField: (prop: FormItemProp) => void;
                    getField: (prop: FormItemProp) => FormItemContext | undefined;
                    fields: Reactive< FormItemContext[]>;
                }> & {} & ComponentCustomProperties & {} & {
                    $slots: {
                        default?(_: {}): any;
                    };
                }) | null;
            };
            $slots: Readonly<{
                [name: string]: Slot<any> | undefined;
            }>;
            $root: ComponentPublicInstance | null;
            $parent: ComponentPublicInstance | null;
            $host: Element | null;
            $emit: ((event: "reset") => void) & ((event: "submit", model: Record<string, any>) => void) & ((event: "change", model: Record<string, any>) => void);
            $el: any;
            $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                model: {
                    type: PropType<Record<string, any>>;
                    default(): any;
                };
                inline: {
                    type: BooleanConstructor;
                };
                inlineColumns: {
                    type: NumberConstructor;
                };
                footer: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                submitText: {
                    type: PropType<string | null>;
                    default: string;
                };
                resetText: {
                    type: PropType<string | null>;
                    default: string;
                };
                submitMethod: {
                    type: PropType<(model: FormModel) => Promise<any>>;
                };
                tooltipMessage: {
                    type: PropType<boolean | Partial< ElTooltipProps>>;
                    default: undefined;
                };
                enterSubmit: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                sticky: {
                    type: BooleanConstructor;
                    default: boolean;
                };
                footerAlign: {
                    type: PropType<"left" | "center" | "right">;
                    default: string;
                };
            }>> & Readonly<{
                onReset?: (() => any) | undefined;
                onSubmit?: ((model: Record<string, any>) => any) | undefined;
                onChange?: ((model: Record<string, any>) => any) | undefined;
            }>, {
                formRef: Ref<any, any>;
                model: FormModel;
                submit: () => Promise<void>;
                reset: (fields?: string[] | string) => void;
                validate: () => Promise<any>;
                clearValidate: () => void;
            }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
                reset: () => any;
                submit: (model: Record<string, any>) => any;
                change: (model: Record<string, any>) => any;
            }, string, {
                footer: boolean;
                inline: boolean;
                footerAlign: "left" | "right" | "center";
                tooltipMessage: boolean | Partial< ElTooltipProps>;
                model: Record<string, any>;
                submitText: string | null;
                resetText: string | null;
                enterSubmit: boolean;
                sticky: boolean;
            }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                beforeCreate?: (() => void) | (() => void)[];
                created?: (() => void) | (() => void)[];
                beforeMount?: (() => void) | (() => void)[];
                mounted?: (() => void) | (() => void)[];
                beforeUpdate?: (() => void) | (() => void)[];
                updated?: (() => void) | (() => void)[];
                activated?: (() => void) | (() => void)[];
                deactivated?: (() => void) | (() => void)[];
                beforeDestroy?: (() => void) | (() => void)[];
                beforeUnmount?: (() => void) | (() => void)[];
                destroyed?: (() => void) | (() => void)[];
                unmounted?: (() => void) | (() => void)[];
                renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
            };
            $forceUpdate: () => void;
            $nextTick: nextTick;
            $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
        } & Readonly<{
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }> & Omit<Readonly< ExtractPropTypes<{
            model: {
                type: PropType<Record<string, any>>;
                default(): any;
            };
            inline: {
                type: BooleanConstructor;
            };
            inlineColumns: {
                type: NumberConstructor;
            };
            footer: {
                type: BooleanConstructor;
                default: boolean;
            };
            submitText: {
                type: PropType<string | null>;
                default: string;
            };
            resetText: {
                type: PropType<string | null>;
                default: string;
            };
            submitMethod: {
                type: PropType<(model: FormModel) => Promise<any>>;
            };
            tooltipMessage: {
                type: PropType<boolean | Partial< ElTooltipProps>>;
                default: undefined;
            };
            enterSubmit: {
                type: BooleanConstructor;
                default: boolean;
            };
            sticky: {
                type: BooleanConstructor;
                default: boolean;
            };
            footerAlign: {
                type: PropType<"left" | "center" | "right">;
                default: string;
            };
        }>> & Readonly<{
            onReset?: (() => any) | undefined;
            onSubmit?: ((model: Record<string, any>) => any) | undefined;
            onChange?: ((model: Record<string, any>) => any) | undefined;
        }>, "reset" | "submit" | "clearValidate" | "validate" | "formRef" | ("footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky")> & ShallowUnwrapRef<{
            formRef: Ref<any, any>;
            model: FormModel;
            submit: () => Promise<void>;
            reset: (fields?: string[] | string) => void;
            validate: () => Promise<any>;
            clearValidate: () => void;
        }> & {} & ComponentCustomProperties & {} & {
            $slots: {
                default?(_: {}): any;
                footer?(_: {}): any;
                action?(_: {}): any;
            };
        }) | null;
    };
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: DefineComponent<ExtractPropTypes<{
    collapsible: {
        type: BooleanConstructor;
        default: boolean;
    };
    items: {
        type: PropType<QueryFormItems>;
    };
    inlineColumns: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
    };
}>, {
    validate: () => Promise<any>;
    clearValidate: () => void;
    submit: () => Promise<void>;
    reset: (fields?: string[] | string) => void;
    formRef: Ref<({
        $: ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }> & Omit<{
            readonly footer: boolean;
            readonly inline: boolean;
            readonly footerAlign: "left" | "right" | "center";
            readonly submitText: string | null;
            readonly resetText: string | null;
            readonly enterSubmit: boolean;
            readonly sticky: boolean;
            readonly tooltipMessage?: boolean | Partial< ElTooltipProps> | undefined;
            readonly model?: Record<string, any> | undefined;
            readonly inlineColumns?: number | undefined;
            readonly submitMethod?: ((model: FormModel) => Promise<any>) | undefined;
            readonly onReset?: (() => any) | undefined;
            readonly onSubmit?: ((model: Record<string, any>) => any) | undefined;
            readonly onChange?: ((model: Record<string, any>) => any) | undefined;
        } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        } & {
            formRef: ({
                $: ComponentInternalInstance;
                $data: {};
                $props: Partial<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }> & Omit<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly statusIcon: boolean;
                    readonly validateOnRuleChange: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly size?: ("" | "large" | "default" | "small") | undefined;
                    readonly rules?: Partial<Record<string, FormItemRule | FormItemRule[]>> | undefined;
                    readonly model?: Record<string, any> | undefined;
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined | undefined;
                } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "disabled" | "inline" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions">;
                $attrs: {
                    [x: string]: unknown;
                };
                $refs: {
                    [x: string]: unknown;
                };
                $slots: Readonly<{
                    [name: string]: Slot<any> | undefined;
                }>;
                $root: ComponentPublicInstance | null;
                $parent: ComponentPublicInstance | null;
                $host: Element | null;
                $emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
                $el: any;
                $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                    readonly model: ObjectConstructor;
                    readonly rules: {
                        readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly labelPosition: {
                        readonly type: PropType<"top" | "left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "right";
                    };
                    readonly requireAsteriskPosition: {
                        readonly type: PropType<"left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "left";
                    };
                    readonly labelWidth: {
                        readonly type: PropType<string | number>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly labelSuffix: {
                        readonly type: PropType<string>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly inline: BooleanConstructor;
                    readonly inlineMessage: BooleanConstructor;
                    readonly statusIcon: BooleanConstructor;
                    readonly showMessage: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly validateOnRuleChange: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly hideRequiredAsterisk: BooleanConstructor;
                    readonly scrollToError: BooleanConstructor;
                    readonly scrollIntoViewOptions: {
                        readonly type: PropType<boolean | Record<string, any>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly size: {
                        readonly type: PropType<"" | "large" | "default" | "small">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly disabled: BooleanConstructor;
                }>> & {
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
                }, {
                    validate: (callback?: FormValidateCallback) => FormValidationResult;
                    validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                    resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                    clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                    scrollToField: (prop: FormItemProp) => void;
                    getField: (prop: FormItemProp) => FormItemContext | undefined;
                    fields: Reactive< FormItemContext[]>;
                }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
                    validate: (prop: FormItemProp, isValid: boolean, message: string) => void;
                }, string, {
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                    beforeCreate?: (() => void) | (() => void)[];
                    created?: (() => void) | (() => void)[];
                    beforeMount?: (() => void) | (() => void)[];
                    mounted?: (() => void) | (() => void)[];
                    beforeUpdate?: (() => void) | (() => void)[];
                    updated?: (() => void) | (() => void)[];
                    activated?: (() => void) | (() => void)[];
                    deactivated?: (() => void) | (() => void)[];
                    beforeDestroy?: (() => void) | (() => void)[];
                    beforeUnmount?: (() => void) | (() => void)[];
                    destroyed?: (() => void) | (() => void)[];
                    unmounted?: (() => void) | (() => void)[];
                    renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
                };
                $forceUpdate: () => void;
                $nextTick: nextTick;
                $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
            } & Readonly<{
                readonly disabled: boolean;
                readonly inline: boolean;
                readonly labelWidth: string | number;
                readonly labelPosition: "top" | "left" | "right";
                readonly inlineMessage: boolean;
                readonly showMessage: boolean;
                readonly requireAsteriskPosition: "left" | "right";
                readonly labelSuffix: string;
                readonly validateOnRuleChange: boolean;
                readonly scrollIntoViewOptions: boolean | Record<string, any>;
                readonly statusIcon: boolean;
                readonly hideRequiredAsterisk: boolean;
                readonly scrollToError: boolean;
            }> & Omit<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: {
                    readonly type: PropType<"top" | "left" | "right">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "right";
                };
                readonly requireAsteriskPosition: {
                    readonly type: PropType<"left" | "right">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "left";
                };
                readonly labelWidth: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly labelSuffix: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly validateOnRuleChange: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<boolean | Record<string, any>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly size: {
                    readonly type: PropType<"" | "large" | "default" | "small">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }, "disabled" | "inline" | "clearValidate" | "validate" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "fields" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions" | "validateField" | "resetFields" | "scrollToField" | "getField"> & ShallowUnwrapRef<{
                validate: (callback?: FormValidateCallback) => FormValidationResult;
                validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                scrollToField: (prop: FormItemProp) => void;
                getField: (prop: FormItemProp) => FormItemContext | undefined;
                fields: Reactive< FormItemContext[]>;
            }> & {} & ComponentCustomProperties & {} & {
                $slots: {
                    default?(_: {}): any;
                };
            }) | null;
        };
        $slots: Readonly<{
            [name: string]: Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: ((event: "reset") => void) & ((event: "submit", model: Record<string, any>) => void) & ((event: "change", model: Record<string, any>) => void);
        $el: any;
        $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
            model: {
                type: PropType<Record<string, any>>;
                default(): any;
            };
            inline: {
                type: BooleanConstructor;
            };
            inlineColumns: {
                type: NumberConstructor;
            };
            footer: {
                type: BooleanConstructor;
                default: boolean;
            };
            submitText: {
                type: PropType<string | null>;
                default: string;
            };
            resetText: {
                type: PropType<string | null>;
                default: string;
            };
            submitMethod: {
                type: PropType<(model: FormModel) => Promise<any>>;
            };
            tooltipMessage: {
                type: PropType<boolean | Partial< ElTooltipProps>>;
                default: undefined;
            };
            enterSubmit: {
                type: BooleanConstructor;
                default: boolean;
            };
            sticky: {
                type: BooleanConstructor;
                default: boolean;
            };
            footerAlign: {
                type: PropType<"left" | "center" | "right">;
                default: string;
            };
        }>> & Readonly<{
            onReset?: (() => any) | undefined;
            onSubmit?: ((model: Record<string, any>) => any) | undefined;
            onChange?: ((model: Record<string, any>) => any) | undefined;
        }>, {
            formRef: Ref<any, any>;
            model: FormModel;
            submit: () => Promise<void>;
            reset: (fields?: string[] | string) => void;
            validate: () => Promise<any>;
            clearValidate: () => void;
        }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            reset: () => any;
            submit: (model: Record<string, any>) => any;
            change: (model: Record<string, any>) => any;
        }, string, {
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
    } & Readonly<{
        footer: boolean;
        inline: boolean;
        footerAlign: "left" | "right" | "center";
        tooltipMessage: boolean | Partial< ElTooltipProps>;
        model: Record<string, any>;
        submitText: string | null;
        resetText: string | null;
        enterSubmit: boolean;
        sticky: boolean;
    }> & Omit<Readonly< ExtractPropTypes<{
        model: {
            type: PropType<Record<string, any>>;
            default(): any;
        };
        inline: {
            type: BooleanConstructor;
        };
        inlineColumns: {
            type: NumberConstructor;
        };
        footer: {
            type: BooleanConstructor;
            default: boolean;
        };
        submitText: {
            type: PropType<string | null>;
            default: string;
        };
        resetText: {
            type: PropType<string | null>;
            default: string;
        };
        submitMethod: {
            type: PropType<(model: FormModel) => Promise<any>>;
        };
        tooltipMessage: {
            type: PropType<boolean | Partial< ElTooltipProps>>;
            default: undefined;
        };
        enterSubmit: {
            type: BooleanConstructor;
            default: boolean;
        };
        sticky: {
            type: BooleanConstructor;
            default: boolean;
        };
        footerAlign: {
            type: PropType<"left" | "center" | "right">;
            default: string;
        };
    }>> & Readonly<{
        onReset?: (() => any) | undefined;
        onSubmit?: ((model: Record<string, any>) => any) | undefined;
        onChange?: ((model: Record<string, any>) => any) | undefined;
    }>, "reset" | "submit" | "clearValidate" | "validate" | "formRef" | ("footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky")> & ShallowUnwrapRef<{
        formRef: Ref<any, any>;
        model: FormModel;
        submit: () => Promise<void>;
        reset: (fields?: string[] | string) => void;
        validate: () => Promise<any>;
        clearValidate: () => void;
    }> & {} & ComponentCustomProperties & {} & {
        $slots: {
            default?(_: {}): any;
            footer?(_: {}): any;
            action?(_: {}): any;
        };
    }) | undefined, ({
        $: ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }> & Omit<{
            readonly footer: boolean;
            readonly inline: boolean;
            readonly footerAlign: "left" | "right" | "center";
            readonly submitText: string | null;
            readonly resetText: string | null;
            readonly enterSubmit: boolean;
            readonly sticky: boolean;
            readonly tooltipMessage?: boolean | Partial< ElTooltipProps> | undefined;
            readonly model?: Record<string, any> | undefined;
            readonly inlineColumns?: number | undefined;
            readonly submitMethod?: ((model: FormModel) => Promise<any>) | undefined;
            readonly onReset?: (() => any) | undefined;
            readonly onSubmit?: ((model: Record<string, any>) => any) | undefined;
            readonly onChange?: ((model: Record<string, any>) => any) | undefined;
        } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        } & {
            formRef: ({
                $: ComponentInternalInstance;
                $data: {};
                $props: Partial<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }> & Omit<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly statusIcon: boolean;
                    readonly validateOnRuleChange: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly size?: ("" | "large" | "default" | "small") | undefined;
                    readonly rules?: Partial<Record<string, FormItemRule | FormItemRule[]>> | undefined;
                    readonly model?: Record<string, any> | undefined;
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined | undefined;
                } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "disabled" | "inline" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions">;
                $attrs: {
                    [x: string]: unknown;
                };
                $refs: {
                    [x: string]: unknown;
                };
                $slots: Readonly<{
                    [name: string]: Slot<any> | undefined;
                }>;
                $root: ComponentPublicInstance | null;
                $parent: ComponentPublicInstance | null;
                $host: Element | null;
                $emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
                $el: any;
                $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                    readonly model: ObjectConstructor;
                    readonly rules: {
                        readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly labelPosition: {
                        readonly type: PropType<"top" | "left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "right";
                    };
                    readonly requireAsteriskPosition: {
                        readonly type: PropType<"left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "left";
                    };
                    readonly labelWidth: {
                        readonly type: PropType<string | number>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly labelSuffix: {
                        readonly type: PropType<string>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly inline: BooleanConstructor;
                    readonly inlineMessage: BooleanConstructor;
                    readonly statusIcon: BooleanConstructor;
                    readonly showMessage: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly validateOnRuleChange: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly hideRequiredAsterisk: BooleanConstructor;
                    readonly scrollToError: BooleanConstructor;
                    readonly scrollIntoViewOptions: {
                        readonly type: PropType<boolean | Record<string, any>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly size: {
                        readonly type: PropType<"" | "large" | "default" | "small">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly disabled: BooleanConstructor;
                }>> & {
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
                }, {
                    validate: (callback?: FormValidateCallback) => FormValidationResult;
                    validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                    resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                    clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                    scrollToField: (prop: FormItemProp) => void;
                    getField: (prop: FormItemProp) => FormItemContext | undefined;
                    fields: Reactive< FormItemContext[]>;
                }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
                    validate: (prop: FormItemProp, isValid: boolean, message: string) => void;
                }, string, {
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                    beforeCreate?: (() => void) | (() => void)[];
                    created?: (() => void) | (() => void)[];
                    beforeMount?: (() => void) | (() => void)[];
                    mounted?: (() => void) | (() => void)[];
                    beforeUpdate?: (() => void) | (() => void)[];
                    updated?: (() => void) | (() => void)[];
                    activated?: (() => void) | (() => void)[];
                    deactivated?: (() => void) | (() => void)[];
                    beforeDestroy?: (() => void) | (() => void)[];
                    beforeUnmount?: (() => void) | (() => void)[];
                    destroyed?: (() => void) | (() => void)[];
                    unmounted?: (() => void) | (() => void)[];
                    renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
                };
                $forceUpdate: () => void;
                $nextTick: nextTick;
                $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
            } & Readonly<{
                readonly disabled: boolean;
                readonly inline: boolean;
                readonly labelWidth: string | number;
                readonly labelPosition: "top" | "left" | "right";
                readonly inlineMessage: boolean;
                readonly showMessage: boolean;
                readonly requireAsteriskPosition: "left" | "right";
                readonly labelSuffix: string;
                readonly validateOnRuleChange: boolean;
                readonly scrollIntoViewOptions: boolean | Record<string, any>;
                readonly statusIcon: boolean;
                readonly hideRequiredAsterisk: boolean;
                readonly scrollToError: boolean;
            }> & Omit<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: {
                    readonly type: PropType<"top" | "left" | "right">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "right";
                };
                readonly requireAsteriskPosition: {
                    readonly type: PropType<"left" | "right">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "left";
                };
                readonly labelWidth: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly labelSuffix: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly validateOnRuleChange: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<boolean | Record<string, any>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly size: {
                    readonly type: PropType<"" | "large" | "default" | "small">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }, "disabled" | "inline" | "clearValidate" | "validate" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "fields" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions" | "validateField" | "resetFields" | "scrollToField" | "getField"> & ShallowUnwrapRef<{
                validate: (callback?: FormValidateCallback) => FormValidationResult;
                validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                scrollToField: (prop: FormItemProp) => void;
                getField: (prop: FormItemProp) => FormItemContext | undefined;
                fields: Reactive< FormItemContext[]>;
            }> & {} & ComponentCustomProperties & {} & {
                $slots: {
                    default?(_: {}): any;
                };
            }) | null;
        };
        $slots: Readonly<{
            [name: string]: Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: ((event: "reset") => void) & ((event: "submit", model: Record<string, any>) => void) & ((event: "change", model: Record<string, any>) => void);
        $el: any;
        $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
            model: {
                type: PropType<Record<string, any>>;
                default(): any;
            };
            inline: {
                type: BooleanConstructor;
            };
            inlineColumns: {
                type: NumberConstructor;
            };
            footer: {
                type: BooleanConstructor;
                default: boolean;
            };
            submitText: {
                type: PropType<string | null>;
                default: string;
            };
            resetText: {
                type: PropType<string | null>;
                default: string;
            };
            submitMethod: {
                type: PropType<(model: FormModel) => Promise<any>>;
            };
            tooltipMessage: {
                type: PropType<boolean | Partial< ElTooltipProps>>;
                default: undefined;
            };
            enterSubmit: {
                type: BooleanConstructor;
                default: boolean;
            };
            sticky: {
                type: BooleanConstructor;
                default: boolean;
            };
            footerAlign: {
                type: PropType<"left" | "center" | "right">;
                default: string;
            };
        }>> & Readonly<{
            onReset?: (() => any) | undefined;
            onSubmit?: ((model: Record<string, any>) => any) | undefined;
            onChange?: ((model: Record<string, any>) => any) | undefined;
        }>, {
            formRef: Ref<any, any>;
            model: FormModel;
            submit: () => Promise<void>;
            reset: (fields?: string[] | string) => void;
            validate: () => Promise<any>;
            clearValidate: () => void;
        }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            reset: () => any;
            submit: (model: Record<string, any>) => any;
            change: (model: Record<string, any>) => any;
        }, string, {
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
    } & Readonly<{
        footer: boolean;
        inline: boolean;
        footerAlign: "left" | "right" | "center";
        tooltipMessage: boolean | Partial< ElTooltipProps>;
        model: Record<string, any>;
        submitText: string | null;
        resetText: string | null;
        enterSubmit: boolean;
        sticky: boolean;
    }> & Omit<Readonly< ExtractPropTypes<{
        model: {
            type: PropType<Record<string, any>>;
            default(): any;
        };
        inline: {
            type: BooleanConstructor;
        };
        inlineColumns: {
            type: NumberConstructor;
        };
        footer: {
            type: BooleanConstructor;
            default: boolean;
        };
        submitText: {
            type: PropType<string | null>;
            default: string;
        };
        resetText: {
            type: PropType<string | null>;
            default: string;
        };
        submitMethod: {
            type: PropType<(model: FormModel) => Promise<any>>;
        };
        tooltipMessage: {
            type: PropType<boolean | Partial< ElTooltipProps>>;
            default: undefined;
        };
        enterSubmit: {
            type: BooleanConstructor;
            default: boolean;
        };
        sticky: {
            type: BooleanConstructor;
            default: boolean;
        };
        footerAlign: {
            type: PropType<"left" | "center" | "right">;
            default: string;
        };
    }>> & Readonly<{
        onReset?: (() => any) | undefined;
        onSubmit?: ((model: Record<string, any>) => any) | undefined;
        onChange?: ((model: Record<string, any>) => any) | undefined;
    }>, "reset" | "submit" | "clearValidate" | "validate" | "formRef" | ("footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky")> & ShallowUnwrapRef<{
        formRef: Ref<any, any>;
        model: FormModel;
        submit: () => Promise<void>;
        reset: (fields?: string[] | string) => void;
        validate: () => Promise<any>;
        clearValidate: () => void;
    }> & {} & ComponentCustomProperties & {} & {
        $slots: {
            default?(_: {}): any;
            footer?(_: {}): any;
            action?(_: {}): any;
        };
    }) | undefined>;
    $vtjDynamicSlots: () => boolean[];
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    collapsed: (Collapsed: boolean) => any;
}, string, PublicProps, Readonly< ExtractPropTypes<{
    collapsible: {
        type: BooleanConstructor;
        default: boolean;
    };
    items: {
        type: PropType<QueryFormItems>;
    };
    inlineColumns: {
        type: NumberConstructor;
        default: number;
    };
    disabled: {
        type: BooleanConstructor;
    };
}>> & Readonly<{
    onCollapsed?: ((Collapsed: boolean) => any) | undefined;
}>, {
    disabled: boolean;
    collapsible: boolean;
    inlineColumns: number;
}, {}, {}, {}, string, ComponentProvideOptions, true, {
    formRef: ({
        $: ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }> & Omit<{
            readonly footer: boolean;
            readonly inline: boolean;
            readonly footerAlign: "left" | "right" | "center";
            readonly submitText: string | null;
            readonly resetText: string | null;
            readonly enterSubmit: boolean;
            readonly sticky: boolean;
            readonly tooltipMessage?: boolean | Partial< ElTooltipProps> | undefined;
            readonly model?: Record<string, any> | undefined;
            readonly inlineColumns?: number | undefined;
            readonly submitMethod?: ((model: FormModel) => Promise<any>) | undefined;
            readonly onReset?: (() => any) | undefined;
            readonly onSubmit?: ((model: Record<string, any>) => any) | undefined;
            readonly onChange?: ((model: Record<string, any>) => any) | undefined;
        } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        } & {
            formRef: ({
                $: ComponentInternalInstance;
                $data: {};
                $props: Partial<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }> & Omit<{
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly statusIcon: boolean;
                    readonly validateOnRuleChange: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly size?: ("" | "large" | "default" | "small") | undefined;
                    readonly rules?: Partial<Record<string, FormItemRule | FormItemRule[]>> | undefined;
                    readonly model?: Record<string, any> | undefined;
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined | undefined;
                } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "disabled" | "inline" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions">;
                $attrs: {
                    [x: string]: unknown;
                };
                $refs: {
                    [x: string]: unknown;
                };
                $slots: Readonly<{
                    [name: string]: Slot<any> | undefined;
                }>;
                $root: ComponentPublicInstance | null;
                $parent: ComponentPublicInstance | null;
                $host: Element | null;
                $emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
                $el: any;
                $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
                    readonly model: ObjectConstructor;
                    readonly rules: {
                        readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly labelPosition: {
                        readonly type: PropType<"top" | "left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "right";
                    };
                    readonly requireAsteriskPosition: {
                        readonly type: PropType<"left" | "right">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "left";
                    };
                    readonly labelWidth: {
                        readonly type: PropType<string | number>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly labelSuffix: {
                        readonly type: PropType<string>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: "";
                    };
                    readonly inline: BooleanConstructor;
                    readonly inlineMessage: BooleanConstructor;
                    readonly statusIcon: BooleanConstructor;
                    readonly showMessage: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly validateOnRuleChange: {
                        readonly type: PropType<boolean>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly hideRequiredAsterisk: BooleanConstructor;
                    readonly scrollToError: BooleanConstructor;
                    readonly scrollIntoViewOptions: {
                        readonly type: PropType<boolean | Record<string, any>>;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    } & {
                        readonly default: true;
                    };
                    readonly size: {
                        readonly type: PropType<"" | "large" | "default" | "small">;
                        readonly required: false;
                        readonly validator: ((val: unknown) => boolean) | undefined;
                        __epPropKey: true;
                    };
                    readonly disabled: BooleanConstructor;
                }>> & {
                    onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
                }, {
                    validate: (callback?: FormValidateCallback) => FormValidationResult;
                    validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                    resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                    clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                    scrollToField: (prop: FormItemProp) => void;
                    getField: (prop: FormItemProp) => FormItemContext | undefined;
                    fields: Reactive< FormItemContext[]>;
                }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
                    validate: (prop: FormItemProp, isValid: boolean, message: string) => void;
                }, string, {
                    readonly disabled: boolean;
                    readonly inline: boolean;
                    readonly labelWidth: string | number;
                    readonly labelPosition: "top" | "left" | "right";
                    readonly inlineMessage: boolean;
                    readonly showMessage: boolean;
                    readonly requireAsteriskPosition: "left" | "right";
                    readonly labelSuffix: string;
                    readonly validateOnRuleChange: boolean;
                    readonly scrollIntoViewOptions: boolean | Record<string, any>;
                    readonly statusIcon: boolean;
                    readonly hideRequiredAsterisk: boolean;
                    readonly scrollToError: boolean;
                }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
                    beforeCreate?: (() => void) | (() => void)[];
                    created?: (() => void) | (() => void)[];
                    beforeMount?: (() => void) | (() => void)[];
                    mounted?: (() => void) | (() => void)[];
                    beforeUpdate?: (() => void) | (() => void)[];
                    updated?: (() => void) | (() => void)[];
                    activated?: (() => void) | (() => void)[];
                    deactivated?: (() => void) | (() => void)[];
                    beforeDestroy?: (() => void) | (() => void)[];
                    beforeUnmount?: (() => void) | (() => void)[];
                    destroyed?: (() => void) | (() => void)[];
                    unmounted?: (() => void) | (() => void)[];
                    renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
                    errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
                };
                $forceUpdate: () => void;
                $nextTick: nextTick;
                $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
            } & Readonly<{
                readonly disabled: boolean;
                readonly inline: boolean;
                readonly labelWidth: string | number;
                readonly labelPosition: "top" | "left" | "right";
                readonly inlineMessage: boolean;
                readonly showMessage: boolean;
                readonly requireAsteriskPosition: "left" | "right";
                readonly labelSuffix: string;
                readonly validateOnRuleChange: boolean;
                readonly scrollIntoViewOptions: boolean | Record<string, any>;
                readonly statusIcon: boolean;
                readonly hideRequiredAsterisk: boolean;
                readonly scrollToError: boolean;
            }> & Omit<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, FormItemRule | FormItemRule[]>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: {
                    readonly type: PropType<"top" | "left" | "right">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "right";
                };
                readonly requireAsteriskPosition: {
                    readonly type: PropType<"left" | "right">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "left";
                };
                readonly labelWidth: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly labelSuffix: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly validateOnRuleChange: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<boolean | Record<string, any>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly size: {
                    readonly type: PropType<"" | "large" | "default" | "small">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }, "disabled" | "inline" | "clearValidate" | "validate" | "labelWidth" | "labelPosition" | "inlineMessage" | "showMessage" | "fields" | "requireAsteriskPosition" | "labelSuffix" | "statusIcon" | "validateOnRuleChange" | "hideRequiredAsterisk" | "scrollToError" | "scrollIntoViewOptions" | "validateField" | "resetFields" | "scrollToField" | "getField"> & ShallowUnwrapRef<{
                validate: (callback?: FormValidateCallback) => FormValidationResult;
                validateField: (props?: FormItemProp | FormItemProp[], callback?: FormValidateCallback) => FormValidationResult;
                resetFields: (props?: FormItemProp | FormItemProp[]) => void;
                clearValidate: (props?: FormItemProp | FormItemProp[]) => void;
                scrollToField: (prop: FormItemProp) => void;
                getField: (prop: FormItemProp) => FormItemContext | undefined;
                fields: Reactive< FormItemContext[]>;
            }> & {} & ComponentCustomProperties & {} & {
                $slots: {
                    default?(_: {}): any;
                };
            }) | null;
        };
        $slots: Readonly<{
            [name: string]: Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: ((event: "reset") => void) & ((event: "submit", model: Record<string, any>) => void) & ((event: "change", model: Record<string, any>) => void);
        $el: any;
        $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
            model: {
                type: PropType<Record<string, any>>;
                default(): any;
            };
            inline: {
                type: BooleanConstructor;
            };
            inlineColumns: {
                type: NumberConstructor;
            };
            footer: {
                type: BooleanConstructor;
                default: boolean;
            };
            submitText: {
                type: PropType<string | null>;
                default: string;
            };
            resetText: {
                type: PropType<string | null>;
                default: string;
            };
            submitMethod: {
                type: PropType<(model: FormModel) => Promise<any>>;
            };
            tooltipMessage: {
                type: PropType<boolean | Partial< ElTooltipProps>>;
                default: undefined;
            };
            enterSubmit: {
                type: BooleanConstructor;
                default: boolean;
            };
            sticky: {
                type: BooleanConstructor;
                default: boolean;
            };
            footerAlign: {
                type: PropType<"left" | "center" | "right">;
                default: string;
            };
        }>> & Readonly<{
            onReset?: (() => any) | undefined;
            onSubmit?: ((model: Record<string, any>) => any) | undefined;
            onChange?: ((model: Record<string, any>) => any) | undefined;
        }>, {
            formRef: Ref<any, any>;
            model: FormModel;
            submit: () => Promise<void>;
            reset: (fields?: string[] | string) => void;
            validate: () => Promise<any>;
            clearValidate: () => void;
        }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            reset: () => any;
            submit: (model: Record<string, any>) => any;
            change: (model: Record<string, any>) => any;
        }, string, {
            footer: boolean;
            inline: boolean;
            footerAlign: "left" | "right" | "center";
            tooltipMessage: boolean | Partial< ElTooltipProps>;
            model: Record<string, any>;
            submitText: string | null;
            resetText: string | null;
            enterSubmit: boolean;
            sticky: boolean;
        }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
    } & Readonly<{
        footer: boolean;
        inline: boolean;
        footerAlign: "left" | "right" | "center";
        tooltipMessage: boolean | Partial< ElTooltipProps>;
        model: Record<string, any>;
        submitText: string | null;
        resetText: string | null;
        enterSubmit: boolean;
        sticky: boolean;
    }> & Omit<Readonly< ExtractPropTypes<{
        model: {
            type: PropType<Record<string, any>>;
            default(): any;
        };
        inline: {
            type: BooleanConstructor;
        };
        inlineColumns: {
            type: NumberConstructor;
        };
        footer: {
            type: BooleanConstructor;
            default: boolean;
        };
        submitText: {
            type: PropType<string | null>;
            default: string;
        };
        resetText: {
            type: PropType<string | null>;
            default: string;
        };
        submitMethod: {
            type: PropType<(model: FormModel) => Promise<any>>;
        };
        tooltipMessage: {
            type: PropType<boolean | Partial< ElTooltipProps>>;
            default: undefined;
        };
        enterSubmit: {
            type: BooleanConstructor;
            default: boolean;
        };
        sticky: {
            type: BooleanConstructor;
            default: boolean;
        };
        footerAlign: {
            type: PropType<"left" | "center" | "right">;
            default: string;
        };
    }>> & Readonly<{
        onReset?: (() => any) | undefined;
        onSubmit?: ((model: Record<string, any>) => any) | undefined;
        onChange?: ((model: Record<string, any>) => any) | undefined;
    }>, "reset" | "submit" | "clearValidate" | "validate" | "formRef" | ("footer" | "inline" | "footerAlign" | "tooltipMessage" | "model" | "submitText" | "resetText" | "enterSubmit" | "sticky")> & ShallowUnwrapRef<{
        formRef: Ref<any, any>;
        model: FormModel;
        submit: () => Promise<void>;
        reset: (fields?: string[] | string) => void;
        validate: () => Promise<any>;
        clearValidate: () => void;
    }> & {} & ComponentCustomProperties & {} & {
        $slots: {
            default?(_: {}): any;
            footer?(_: {}): any;
            action?(_: {}): any;
        };
    }) | null;
}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
