import { IsN<PERSON>ber, IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

export class QueryDto<T = any> {
  @IsNumber()
  @Transform((v) => Number(v.value || 1))
  page: number = 1;

  @IsNumber()
  @Transform((v) => Number(v.value || 10))
  limit: number = 10;

  @IsOptional()
  @Transform((v) => JSON.parse(v.value || '{}'))
  order?: Record<keyof T, 'DESC' | 'ASC'>;

  @IsOptional()
  @IsBoolean()
  @Transform((v) => v.value === true || v.value === 'true')
  withDeleted?: boolean;
}
