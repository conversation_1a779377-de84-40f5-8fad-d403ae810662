import { createApi, toArray } from '@vtj/utils';
import { SchemaType, REMOTE } from '@/shared';

export interface GetSchemaReq {
  type: SchemaType;
  app: string;
  name: string;
}

export interface SaveSchemaReq extends GetSchemaReq {
  content: Record<string, any>;
}

export interface RemoveSchemaReq extends Omit<GetSchemaReq, 'name'> {
  name: string | string[];
}

export interface FileGeneratorReq {
  app: string;
  platform: string;
  dsl: Record<string, any>;
}

export interface ProjectGeneratorReq {
  app: string;
  project: Record<string, any>;
}

export interface VueParserReq {
  // 文件id
  id: string;
  // 组件名称
  name: string;
  // Vue源码
  source: string;
  // 项目 dsl
  project: Record<string, any>;
}

export interface SchemaRes {
  id: string;
  app: string;
  name: string;
  type: SchemaType;
  content: Record<string, any>;
}

/**
 * 获取 Schema
 * @param req
 * @returns
 */
export const getSchema = (req: GetSchemaReq) => {
  const api = createApi<SchemaRes>({
    baseURL: REMOTE,
    url: '/api/schemas/info/:app/:type',
    method: 'get'
  });
  const { type, app, name } = req;
  return api({ name }, { params: { app, type } });
};

/**
 * 新建、更新Schema
 * @param req
 * @returns
 */
export const saveSchema = (req: SaveSchemaReq) => {
  const api = createApi<SchemaRes>({
    baseURL: REMOTE,
    url: '/api/schemas/:app/:type',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  const { type, app, name, content } = req;
  return api(
    {
      app,
      type,
      name,
      content: JSON.stringify(content)
    },
    { params: { app, type } }
  );
};

/**
 * 删除Schema
 * @param req
 * @returns
 */
export const removeSchema = (req: RemoveSchemaReq) => {
  const api = createApi<boolean>({
    baseURL: REMOTE,
    url: '/api/schemas/:app/:type',
    method: 'delete',
    settings: {
      type: 'json'
    }
  });
  const { type, app, name } = req;
  return api(toArray(name), { params: { app, type } });
};

/**
 * DSL出码，DSL -> Vue
 * @param req
 * @returns
 */
export const fileGenerator = (req: FileGeneratorReq) => {
  const api = createApi<string>({
    baseURL: REMOTE,
    url: '/api/schemas/generator/:app/vue',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  const { app, platform, dsl } = req;
  return api(dsl, { params: { app }, query: { platform } });
};

/**
 * 项目出码
 * @param req
 * @returns
 */
export const projectGenerator = (req: ProjectGeneratorReq) => {
  const api = createApi<string>({
    baseURL: REMOTE,
    url: '/api/schemas/generator/:app/project',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  const { app, project } = req;
  return api(project, { params: { app } });
};

/**
 * 解析Vue代码， Vue -> DSL
 * @param req
 * @returns
 */
export const vueParser = (req: VueParserReq) => {
  const api = createApi<Record<string, any>>({
    baseURL: REMOTE,
    url: '/api/schemas/parser',
    method: 'post',
    settings: {
      type: 'json'
    }
  });
  return api({
    ...req,
    project: JSON.stringify(req.project)
  });
};
