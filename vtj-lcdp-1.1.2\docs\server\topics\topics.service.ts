import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Like, Between } from 'typeorm';
import { Topic } from './entities/topic.entity';
import { Chat } from './entities/chat.entify';
import { CreateChatDto } from './dto/create-chat.dto';
import { SettingsService } from '../settings/settings.service';
import { AIHepler } from './ai';
import { ChatStatus } from './types/constants';
import { UserTopicDto } from './dto/user-topic.dto';
import {
  UserImageTopicDto,
  UserFileTopicDto
} from './dto/user-image-topic.dto';
import { OrdersService } from '../orders/orders.service';
import { AI, pager } from '../shared';
import { SettingMode } from '../settings/types';
import { QueryTopicDto } from './dto/query-topic.dto';
import { QueryChatDto } from './dto/query-chat.dto';
import { TopicType } from './types';

@Injectable()
export class TopicsService {
  private ai: AIHepler;
  constructor(
    @InjectRepository(Topic) private topics: Repository<Topic>,
    @InjectRepository(Chat) private chats: Repository<Chat>,
    private settingsService: SettingsService,
    private config: ConfigService,
    private orders: OrdersService
  ) {
    const aiConfig = this.config.get<AI>('ai');
    this.ai = new AIHepler(this.settingsService, aiConfig);
  }
  async validate(userId: string) {
    const settings = await this.settingsService.get();

    if (settings.mode === SettingMode.Invite) {
      const orders = await this.orders.getUserNowValidOrders(userId);
      if (!orders.length) {
        return new BadRequestException(
          '没有订单信息，如需继续使用请与客服联系！'
        );
      }
      const hasTokens = orders.some((n) => n.tokens > 0);
      if (!hasTokens) {
        return new BadRequestException(
          'Token已用完，如需继续使用请与客服联系！'
        );
      }
      return false;
    } else if (settings.mode === SettingMode.Pay) {
      const orders = await this.orders.getUserNowValidOrders(userId);
      if (!orders.length) {
        const count = await this.getUserChatCount(userId);
        if (count >= settings.limit) {
          return new BadRequestException(
            '您的免费体验次数已用完，如需继续使用请付费订阅！'
          );
        }
        return false;
      }
      const hasTokens = orders.some((n) => n.tokens > 0);
      if (!hasTokens) {
        return new BadRequestException(
          'Token已用完，如需继续使用请与客服联系！'
        );
      }
      return false;
    }
    return false;
  }

  async createTopic(dto: UserTopicDto) {
    const errors = await this.validate(dto.userId);
    if (errors) {
      return errors;
    }
    const { topic, chat } = await this.ai.createTopic(dto);
    const resultTopic = await this.topics.save(topic);
    chat.topicId = resultTopic.id;
    delete resultTopic.content;
    const resultChat = await this.chats.save(chat);
    return {
      topic: resultTopic,
      chat: resultChat
    };
  }

  async createChat(dto: CreateChatDto) {
    const errors = await this.validate(dto.userId);
    if (errors) {
      return errors;
    }
    const chat = new Chat();
    Object.assign(chat, dto);
    chat.status = ChatStatus.Pending;
    return this.chats.save(chat);
  }

  async saveChat(chat: Chat) {
    await this.orders.useTokens(chat.userId, chat.tokens);
    return this.chats.save(chat);
  }

  findOneTopic(id: string) {
    return this.topics.findOneBy({ id });
  }

  async findTopics(dto: QueryTopicDto) {
    const { page, limit, skip, take } = pager(dto);
    const isHot = dto.isHot ? !!dto.isHot : undefined;
    const [list, total] = await this.topics.findAndCount({
      skip,
      take,
      where: {
        title: dto.title ? Like(`%${dto.title}%`) : undefined,
        model: dto.model || undefined,
        isHot,
        platform: (dto.platform || undefined) as any,
        fileId: dto.fileId || undefined,
        appId: dto.appId || undefined,
        userId: dto.userId || undefined,
        userName: dto.userName || undefined
      },
      order: {
        createdAt: 'DESC'
      }
    });

    return {
      page,
      limit,
      total,
      list
    };
  }

  async removeTopics(ids: string[]) {
    await this.chats.delete({
      topicId: In(ids)
    });
    return this.topics.delete({
      id: In(ids)
    });
  }

  findTopicsByFileId(fileId: string) {
    return this.topics.find({
      select: [
        'id',
        'title',
        'prompt',
        'isHot',
        'fileId',
        'fileName',
        'projectId',
        'platform',
        'appId',
        'createdAt',
        'model',
        'image',
        'json',
        'type',
        'dataType'
      ],
      where: {
        fileId
      }
    });
  }

  findChatsByTopicId(topicId: string) {
    return this.chats.find({
      where: {
        topicId
      },
      order: {
        createdAt: 'ASC'
      }
    });
  }

  findHotTopics() {
    return this.topics.find({
      select: ['id', 'title', 'prompt'],
      where: {
        isHot: true
      },
      order: {
        createdAt: 'ASC'
      }
    });
  }

  async removeTopicsByUserId(topicIds: string[], userId: string) {
    await this.chats.delete({
      userId,
      topicId: In(topicIds)
    });
    return this.topics.delete({
      userId,
      id: In(topicIds)
    });
  }

  async completion(topicId: string, chatId: string) {
    const topic = await this.topics.findOneBy({ id: topicId });
    const topicChats = await this.chats.find({
      where: {
        topicId
      },
      order: {
        createdAt: 'ASC'
      }
    });
    if (!topic) {
      throw new BadRequestException('对话不存在');
    }
    const index = topicChats.findIndex((n) => n.id === chatId);
    const chats = topicChats.slice(0, index + 1);
    if (!chats.length) {
      throw new BadRequestException('对话不存在');
    }

    return this.ai.completion(topic, chats);
  }

  async cancelCompletions(chatId: string) {
    this.ai.cancelCompletion(chatId);
    const chat = await this.chats.findOneBy({ id: chatId });
    if (chat) {
      chat.status = ChatStatus.Canceled;
      chat.message = '对话已取消';
      await this.chats.save(chat);
      return chat;
    }
  }

  getUserChatCount(userId: string) {
    return this.chats.countBy({
      userId
    });
  }

  async toggleHotTopic(topicId: string, hot: boolean) {
    const topic = await this.topics.findOneBy({
      id: topicId
    });
    if (topic) {
      topic.isHot = hot;
    }
    return await this.topics.save(topic);
  }

  async findChats(dto: QueryChatDto) {
    const { page, limit, skip, take } = pager(dto);
    const [list, total] = await this.chats.findAndCount({
      skip,
      take,
      where: {
        prompt: dto.prompt ? Like(`%${dto.prompt}%`) : undefined,
        status: (dto.status || undefined) as any,
        userId: dto.userId || undefined,
        userName: dto.userName || undefined,
        topicId: dto.topicId || undefined,
        createdAt: Between(
          new Date(dto.startDate || '2000/01/01'),
          new Date(dto.endDate || '2099/12/30')
        )
      },
      order: {
        createdAt: 'DESC'
      }
    });

    return {
      page,
      limit,
      total,
      list
    };
  }

  async removeChats(ids: string[]) {
    await this.chats.delete({
      id: In(ids)
    });
  }

  async createImageTopic(
    image: string,
    base64Image: string,
    data: UserImageTopicDto
  ) {
    const { content: imagePrompt, tokens } =
      await this.ai.generateImagePrompt(base64Image);
    const titleRegex = /<summary_title>([\w\W]*)<\/summary_title>/;
    const contentRegex = /<image_analysis>([\w\W]*)<\/image_analysis>/;
    if (imagePrompt) {
      const title = imagePrompt.match(titleRegex)?.[1] || '';
      let prompt = imagePrompt.match(contentRegex)?.[1] || '';
      prompt = `根据以下的UI稿描述，生成页面，用中文回答。
      UI稿描述如下：
      :\n${prompt.trim()}`;
      const dto: UserTopicDto = {
        model: data.model,
        project: data.project,
        dsl: data.dsl,
        source: data.source,
        prompt,
        userId: data.userId,
        userName: data.userName,
        type: TopicType.Image
      };
      const { topic, chat } = await this.ai.createTopic(dto, title.trim());
      topic.image = image;
      const resultTopic = await this.topics.save(topic);
      chat.topicId = resultTopic.id;
      chat.tokens = tokens;
      delete resultTopic.content;
      const resultChat = await this.chats.save(chat);
      return {
        topic: resultTopic,
        chat: resultChat
      };
    }

    return null;
  }

  async createJsonTopic(filePath: string, json: any, data: UserFileTopicDto) {
    const { content: jsonPrompt, tokens } =
      await this.ai.generateJsonPrompt(json);
    if (jsonPrompt) {
      const title = jsonPrompt.substring(0, 30);
      const prompt = `${jsonPrompt.trim()}\n\n请根据以上提示词编写代码还原页面，增强响应式布局，移动端优化，适配终端。`;

      const dto: UserTopicDto = {
        model: data.model,
        project: data.project,
        dsl: data.dsl,
        source: data.source,
        prompt,
        userId: data.userId,
        userName: data.userName,
        type: TopicType.JSON
      };
      const { topic, chat } = await this.ai.createTopic(dto, title.trim());
      topic.json = filePath;
      const resultTopic = await this.topics.save(topic);
      chat.topicId = resultTopic.id;
      chat.tokens = tokens;
      delete resultTopic.content;
      const resultChat = await this.chats.save(chat);
      return {
        topic: resultTopic,
        chat: resultChat
      };
    }
    return null;
  }

  async countTopics(start: string, end: string, isImage?: boolean) {
    const builder = this.topics
      .createQueryBuilder('topics')
      .select(`COUNT(DISTINCT topics.id)`, 'count')
      .where('topics.created_at > :start and topics.created_at < :end', {
        start,
        end
      });
    if (isImage) {
      builder.andWhere('topics.image IS NOT NULL');
    } else {
      builder.andWhere('topics.image IS NULL');
    }

    const res = await builder.getRawOne();
    return Number(res?.count || 0) as number;
  }

  async countChats(start: string, end: string, isFail?: boolean) {
    const builder = this.chats
      .createQueryBuilder('chats')
      .select(`COUNT(DISTINCT chats.id)`, 'count')
      .where('chats.created_at > :start and chats.created_at < :end', {
        start,
        end
      });
    if (isFail) {
      builder.andWhere('chats.status IN (:...types)', {
        types: ['Failed', 'Error']
      });
    }
    const res = await builder.getRawOne();
    return Number(res?.count || 0) as number;
  }

  async userTokensUsed(userId: string) {
    const total = await this.chats.sum('tokens', { userId });
    return total;
  }
}
