import {
  <PERSON>tity,
  Column,
  OneToMany,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>umn,
  Index
} from 'typeorm';
import { BaseEntity, AccessType, ApiMethod } from '../../shared';

@Entity({
  name: 'access',
  comment: '权限表',
  orderBy: {
    order: 'ASC'
  }
})
export class Access extends BaseEntity {
  @Column({ comment: '权限编码', unique: true })
  code: string;

  @Column({ comment: '权限描述' })
  label: string;

  @Column({ comment: '排序', default: 0 })
  order: number;

  @Index()
  @Column({ comment: '父节点Id', name: 'parent_id', nullable: true })
  parentId: string;

  @Index()
  @Column({ comment: '根节点编码', nullable: true })
  root: string;

  @Index()
  @Column('enum', {
    enum: AccessType,
    comment: '权限类型'
  })
  type: AccessType;

  @Column({ comment: '备注', nullable: true })
  notes: string;

  @OneToMany(() => Api, (api) => api.access, { cascade: true })
  apis: Api[];
}

@Entity({
  name: 'apis',
  comment: '接口表'
})
export class Api extends BaseEntity {
  @Column('enum', {
    name: 'method',
    enum: ApiMethod,
    comment: '请求类型',
    default: ApiMethod.Get
  })
  method: string;

  @Column('varchar', { comment: '请求路径' })
  path: string;

  @ManyToOne(() => Access, (access) => access.apis)
  @JoinColumn({ name: 'access_id' })
  access: Access;
}
