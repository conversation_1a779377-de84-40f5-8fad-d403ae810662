# VTJ 后端实现参考

VTJ是一个前端项目，后端可自行设计实现，不限语言和框架。以下是NestJs的实现参考。

官方提供的两个包 `@vtj/coder` 和 `@vtj/parser` 可在 `NodeJs` 环境实现 `DSL转Vue` 和 `Vue转DSL` 功能，推荐使用。 如果您的服务不是`NodeJs`, 代码转换功能需要自行实现。

## 数据库设计

### 系统配置表 settings

![](./assets/settings.png)

### 应用表 apps

![](./assets/apps.png)

### Schemas

![](./assets/schemas.png)

### 模版信息表 templates

![](./assets/template.png)

### 模版发布版本记录 template_dsl

![](./assets/template_dsl.png)

### AI话题表 topics

![](./assets/topic.png)

### AI对话记录表 chats

![](./assets/chats.png)

## 关键逻辑实现

### Service 接口清单

```ts
@Controller('schemas')
export class SchemasController {
  constructor(private readonly service: SchemasService) {}

  @Post(':app/:type')
  save(@Body() dto: SchemaDto) {}

  @Get('info/:app/:type')
  findOne(
    @Param('app') app: string,
    @Param('type') type: string,
    @Query('name') name: string
  ) {}

  @Get(':app/:type')
  find(
    @Param('app') app: string,
    @Param('type') type: string,
    @Query('name') name: string
  ) {}

  @Get()
  search(@Query() dto: QuerySchemaDto) {}

  @Get(':id')
  findOneById(@Param('id') id: string) {}

  @Delete(':app/:type')
  remove(
    @Param('app') app: string,
    @Param('type') type: string,
    @Body() names: string[]
  ) {}

  @Delete()
  removeByIds(@Body() ids: string[]) {}

  @Post('generator/:app/vue')
  generator(
    @Param('app') app: string,
    @Query('platform') platform: PlatformType,
    @Body() dsl: any
  ) {}

  @Post('generator/:app/project')
  projectGenerator(@Param('app') app: string, @Body() project: any) {}

  @Post('parser')
  parseVue(@Body() dto: ParseVueDto) {}
}
```

### OpenApi 接口清单

```ts
@Controller('open')
export class OpenController {
  constructor(private readonly open: OpenService) {}

  @Public()
  @Get('auth/:code')
  async auth(@Param('code') code: string, @Res() res: any) {}

  @Public()
  @Get('user/:token')
  getLoginUser(@Param('token') token: string, @Res() res: any) {}

  @Public()
  @Get('templates')
  getTemplates(
    @Query('platform') platform: string,
    @Query('token') token: string,
    @Res() res: any
  ) {}

  @Public()
  @Get('template/:token')
  getTemplate(
    @Param('token') token: string,
    @Query('id') templateId: string,
    @Res() res: any
  ) {}

  @Public()
  @Get('template/remove/:token')
  removeTemplate(
    @Param('token') token: string,
    @Query('id') templateId: string,
    @Res() res: any
  ) {}

  @Public()
  @Get('dsl/:token')
  getLatestDsl(
    @Param('token') token: string,
    @Query('id') templateId: string,
    @Res() res: any
  ) {}

  @Public()
  @Get('dict/:code')
  async getDict(@Param('code') code: string, @Res() res: any) {}

  @Public()
  @Post('template/publish/:token')
  @UseInterceptors(FileInterceptor('cover'))
  async publishTemplate() {}

  @Public()
  @Post('report')
  async report(
    @Body('data') data: any,
    @Res() res: Response,
    @Req() req: Request
  ) {}

  @Public()
  @Get('report')
  reportJsonp(
    @Query('data') data: string,
    @Req() req: Request,
    @Res() res: any
  ) {}

  @Public()
  @Post('topic/post/:token')
  async postTopic(
    @Param('token') token: string,
    @Body() body: UserTopicDto,
    @Res() res: Response
  ) {}

  @Public()
  @Post('topic/image/:token')
  @UseInterceptors(FileInterceptor('file'))
  async postImageTopic(
    @Param('token') token: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Res() res: Response
  ) {}

  @Public()
  @Post('topic/json/:token')
  @UseInterceptors(FileInterceptor('file'))
  async postJsonTopic(
    @Param('token') token: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: any,
    @Res() res: Response
  ) {}

  @Public()
  @Get('topic/list/:token')
  async getTopics(
    @Param('token') token: string,
    @Query('id') fileId: string,
    @Res() res: Response
  ) {}

  @Public()
  @Get('topic/hot')
  async getHotTopics(@Res() res: Response) {}

  @Public()
  @Get('chat/list/:token')
  async getChats(
    @Param('token') token: string,
    @Query('id') topicId: string,
    @Res() res: Response
  ) {}

  @Public()
  @Post('chat/post/:token')
  async postChat(
    @Param('token') token: string,
    @Body() body: UserChatDto,
    @Res() res: Response
  ) {}

  @Public()
  @Post('chat/save/:token')
  async saveChat(
    @Param('token') token: string,
    @Body() body: any,
    @Res() res: Response
  ) {}

  @Public()
  @Sse('completions/:token')
  async chat(
    @Param('token') token: string,
    @Query('id') chatId: string,
    @Query('tid') topicId: string
  ) {}

  @Public()
  @Get('chat/cancel/:token')
  async cancelCompletions(
    @Param('token') token: string,
    @Query('id') chatId: string
  ) {}

  @Public()
  @Get('topic/remove/:token')
  async removeTopic(
    @Param('token') token: string,
    @Query('id') topicId: string,
    @Res() res: Response
  ) {}

  @Public()
  @Get('settings/:token')
  async getSettings(@Param('token') token: string, @Res() res: Response) {}

  @Public()
  @Post('order/:token')
  async createOrder(@Param('token') token: string, @Res() res: Response) {}

  @Public()
  @Get('order/:token')
  async getOrder(
    @Param('token') token: string,
    @Query('id') id: string,
    @Res() res: Response
  ) {}

  @Public()
  @Get('order/cancel/:token')
  async cancelOrder(
    @Param('token') token: string,
    @Query('id') id: string,
    @Res() res: Response
  ) {}

  @Public()
  @Post('upload/:token')
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @Param('token') token: string,
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response
  ) {}
}
```

### DSL 转 Vue

```ts
// 使用  @vtj/coder 的 generator
import { generator } from '@vtj/coder';

// 类型定义
export declare function generator(
  dsl: BlockSchema,
  componentMap?: Map<string, MaterialDescription>,
  dependencies?: Dependencie[],
  platform?: PlatformType,
  formatterDisabled?: boolean
): Promise<string>;
```

### Vue 转 DSL

```ts
// 使用  @vtj/parser 的 parseVue
import { parseVue } from '@vtj/parser';

// 类型定义
export declare function parseVue(
  options: IParseVueOptions
): Promise<BlockSchema>;
```

### 项目出码

```ts
  async genProject(app: string, project: ProjectSchema) {
    const platform = project.platform || 'web';
    const tempalte = `zip/${platform}/template.zip`;
    const zipFile = fs.readFileSync(resolve(tempalte));
    const zip = await JSZip.loadAsync(zipFile as any);
    const projectDsl: ProjectSchema = await this.findOneContent(
      app,
      SchemaType.Project,
      app
    );
    const materials = await this.findOneContent(app, SchemaType.Material, app);
    const componentMap = new Map<string, MaterialDescription>(
      Object.entries(materials)
    );

    const dirPath: string =
      projectDsl.platform === 'uniapp' ? './src/.vtj' : './.vtj';

    zip.file(
      `${dirPath}/projects/${project.id}.json`,
      JSON.stringify(projectDsl)
    );
    zip.file(
      `${dirPath}/materials/${project.id}.json`,
      JSON.stringify(materials)
    );

    const contents: any[] = [];
    for (const block of project.blocks) {
      const dsl: BlockSchema = await this.findOneContent(
        app,
        SchemaType.File,
        block.id
      );
      const content = await this.createVueContent(
        dsl,
        app,
        componentMap,
        project.dependencies,
        project.platform
      );
      if (content) {
        zip.file(
          `${dirPath}/files/${block.id}.json`,
          JSON.stringify(dsl, null, 2)
        );
        contents.push({ id: block.id, content });
      }
    }
    for (const page of project.pages) {
      const dsl: BlockSchema = await this.findOneContent(
        app,
        SchemaType.File,
        page.id
      );
      const content = await this.createVueContent(
        dsl,
        app,
        componentMap,
        project.dependencies,
        project.platform
      );
      if (content) {
        zip.file(`${dirPath}/files/${page.id}.json`, JSON.stringify(dsl));
        contents.push({ id: page.id, content });
      }
    }

    for (const item of contents) {
      const vueFilePath =
        platform === 'uniapp'
          ? `./src/pages/${project.id}/${item.id}.vue`
          : `./.vtj/vue/${item.id}.vue`;
      zip.file(vueFilePath, item.content);
    }

    if (platform === 'uniapp') {
      const { pagesJson, manifestJson } = projectDsl.uniConfig || {};
      const pages = this.createProjectPages(projectDsl);
      pagesJson.pages = pages;
      zip.file(`./src/pages.json`, JSON.stringify(pagesJson, null, 2));
      zip.file(`./src/manifest.json`, JSON.stringify(manifestJson, null, 2));
      zip.file('./src/App.vue', await this.createUniApp(projectDsl));
    }

    const pkg = fs.readJSONSync(resolve(`./zip/${platform}/package.json`));
    pkg.name = projectDsl.id;
    pkg.description = projectDsl.name;
    if (platform !== 'uniapp') {
      pkg.vtj.base = `/${projectDsl.id}`;
    } else {
      pkg.vtj.pageBasePath = '/pages';
      pkg.vtj.pageRouteName = projectDsl.id;
    }
    pkg.vtj.remote = 'https://lcdp.vtj.pro';
    zip.file(`./package.json`, JSON.stringify(pkg, null, 2));

    const blob = await zip
      .generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: { level: 9 }
      })
      .then((e) => e);
    const file = new File([blob], `${project.id}.zip`, {
      type: blob.type,
      lastModified: Date.now()
    });
    (file as any).buffer = Buffer.from(await blob.arrayBuffer());
    const { name } = await this.oss.upload(file as any, 'zip', '.zip');
    return this.oss.sign(name);
  }


```

### AI对话实现

```ts
  async completion(topicId: string, chatId: string) {
    const topic = await this.topics.findOneBy({ id: topicId });
    const topicChats = await this.chats.find({
      where: {
        topicId
      },
      order: {
        createdAt: 'ASC'
      }
    });
    if (!topic) {
      throw new BadRequestException('对话不存在');
    }
    const index = topicChats.findIndex((n) => n.id === chatId);
    const chats = topicChats.slice(0, index + 1);
    if (!chats.length) {
      throw new BadRequestException('对话不存在');
    }

    return this.ai.completion(topic, chats);
  }


```

```ts
 // this.ai.completion
  completion(topic: Topic, chats: Chat[]) {
    return new Observable((ob) => {
      const abortController = new AbortController();
      const chatId = chats[chats.length - 1]?.id;
      if (chatId) {
        this.completionControllers[chatId] = abortController;
      }
      (async () => {
        const params = this.createCompletionParams(topic, chats);
        const openai = this.createOpenAiClient();
        const stream = await openai.chat.completions
          .create(params, {
            signal: abortController.signal // 关键：绑定中止信号
          })
          .catch((err) => {
            ob.error(err);
            return null;
          });
        if (!stream) return;
        try {
          for await (const chunk of stream as AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>) {
            ob.next(chunk);
          }
          ob.complete();
        } catch (err) {
          // 区分主动取消和其他错误
          ob.error({
            error: abortController.signal.aborted
              ? 'AbortError'
              : err.message || 'OpenAI API Error',
            statusCode: err.status || 500
          });
        }
        delete this.completionControllers[chatId];
      })();
      // 客户端断开时的清理逻辑
      return () => {
        abortController.abort(); // 关键：触发请求中止
      };
    });
  }
```
