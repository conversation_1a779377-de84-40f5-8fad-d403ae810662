import nodemailer from 'nodemailer';

export interface ValidateCodeMail {
  host: string;
  port: number;
  user: string;
  pass: string;
  to: string;
  code: string;
}
export function sendMailValidateCode(mail: ValidateCodeMail) {
  const { host, port, user, pass, to, code } = mail;
  const nodeMail = nodemailer.createTransport({
    host,
    port,
    secure: true,
    auth: {
      user,
      pass
    }
  });

  const content = {
    from: `"VTJ.PRO"<${user}>`,
    subject: 'VTJ.PRO 验证码',
    to,
    html: `
             <p>您好！</p>
             <p>您的验证码是：<strong style="color:orangered;">${code}</strong></p>
             <p>如果不是您本人操作，请无视此邮件</p>
    `
  };

  return new Promise((resolve, reject) => {
    nodeMail.sendMail(content, (err, info) => {
      if (!err) {
        resolve(info);
      } else {
        reject(err);
      }
    });
  });
}
