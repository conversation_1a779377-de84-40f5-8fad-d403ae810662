import { PropType } from 'vue';
import { ComponentPropsType } from '../shared';
import { IconParam, ActionProps, ActionMenuItem } from '../';
export interface TabsItem {
    label: string;
    name?: string | number;
    icon?: IconParam;
    value?: string | number;
    data?: any;
    disabled?: boolean;
    closable?: boolean;
    lazy?: boolean;
    actions?: ActionProps[];
    component?: any;
    props?: Record<string, any>;
    /**
     * 自定义内容插槽名称
     */
    slot?: string;
}
export declare const tabsProps: {
    items: {
        type: PropType<TabsItem[]>;
        default(): never[];
    };
    border: {
        type: BooleanConstructor;
    };
    fit: {
        type: BooleanConstructor;
    };
    align: {
        type: PropType<"left" | "center" | "right">;
    };
};
export type TabsProps = ComponentPropsType<typeof tabsProps>;
export type TabsEmits = {
    actionClick: [props: ActionProps];
    actionCommand: [item: ActionMenuItem];
};
