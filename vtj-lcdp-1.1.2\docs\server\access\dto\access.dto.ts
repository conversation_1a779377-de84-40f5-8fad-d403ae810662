import { IsNotEmpty, IsOptional, IsInt, IsEnum } from 'class-validator';
import { AccessType } from '../../shared';

export class AccessDto {
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  code: string;

  @IsNotEmpty()
  label: string;

  @IsOptional()
  @IsInt()
  order?: number;

  @IsOptional()
  parentId?: string;

  @IsNotEmpty()
  @IsEnum(AccessType)
  type: AccessType;

  @IsOptional()
  notes?: string;
}
