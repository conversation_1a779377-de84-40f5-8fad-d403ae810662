<template>
  <ElCard class="app-card" shadow="hover" @click="onDesign">
    <div class="title">{{ props.item.label }}</div>
    <div class="name">{{ props.item.name }}</div>
    <ElTag class="platform" :type="tagType">{{ props.item.platform }}</ElTag>
    <template #footer>
      <div class="action">
        <ElButton size="small" :icon="View" plain @click.stop="onPreview">
          浏览
        </ElButton>
        <ElButton size="small" :icon="Setting" plain @click.stop="onDesign">
          设计
        </ElButton>
        <ElButton size="small" :icon="Edit" plain @click.stop="onEdit"
          >编辑</ElButton
        >
        <ElButton
          size="small"
          :icon="Delete"
          type="danger"
          plain
          @click.stop="onRemove">
          删除
        </ElButton>
      </div>
    </template>
  </ElCard>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { <PERSON><PERSON>ard, <PERSON>Button, ElTag, ElMessageBox } from 'element-plus';
import { Setting, Edit, View, Delete } from '@element-plus/icons-vue';
import type { PlatformType } from '@vtj/core';
import { type LowCodeAppVO } from '@/shared';
import { createPreviewPath } from '@/utils';
import { removeLowCodeApp } from '@/apis';

export interface Props {
  item: LowCodeAppVO;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  refresh: [];
  edit: [item: LowCodeAppVO];
}>();

const router = useRouter();

const tagType = computed(() => {
  const map = {
    Web: 'primary',
    H5: 'warning',
    UniApp: 'danger'
  };
  return map[props.item.platform] as any;
});

const onRemove = async () => {
  if (props.item.id) {
    const ret = await ElMessageBox.confirm('确认删除该应用？', {
      type: 'warning',
      title: '提示'
    }).catch(() => false);
    if (ret) {
      const res = await removeLowCodeApp(props.item.id).catch(() => false);
      if (res) {
        emit('refresh');
      }
    }
  }
};

const onEdit = () => {
  emit('edit', props.item);
};

const onDesign = () => {
  const { platform, name } = props.item;
  router.push(`/${platform.toLowerCase()}/${name}`);
};

const onPreview = () => {
  const { platform, name } = props.item;
  const url = createPreviewPath(name, platform.toLowerCase() as PlatformType);
  window.open(url);
};
</script>

<style lang="scss" scoped>
.app-card {
  margin-bottom: 20px;
  position: relative;

  :deep(.el-card__body) {
    background-color: var(--el-fill-color);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
    flex-direction: column;
    cursor: pointer;
  }
  .title {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .name {
    color: var(--el-text-color-secondary);
  }
  .platform {
    position: absolute;
    right: 20px;
    top: 20px;
  }
}
.action {
  display: flex;
  justify-content: space-around;
}
</style>
