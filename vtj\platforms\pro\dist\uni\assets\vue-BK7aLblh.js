/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/function y(e,t){const n=new Set(e.split(","));return t?o=>n.has(o.toLowerCase()):o=>n.has(o)}const V={},F=[],K=()=>{},$=()=>!1,G=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),H=e=>e.startsWith("onUpdate:"),J=Object.assign,q=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},A=Object.prototype.hasOwnProperty,k=(e,t)=>A.call(e,t),c=Array.isArray,w=e=>l(e)==="[object Map]",C=e=>l(e)==="[object Set]",b=e=>l(e)==="[object Date]",W=e=>l(e)==="[object RegExp]",p=e=>typeof e=="function",a=e=>typeof e=="string",u=e=>typeof e=="symbol",r=e=>e!==null&&typeof e=="object",Y=e=>(r(e)||p(e))&&p(e.then)&&p(e.catch),N=Object.prototype.toString,l=e=>N.call(e),Z=e=>l(e).slice(8,-1),R=e=>l(e)==="[object Object]",Q=e=>a(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,X=y(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),d=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},E=/-(\w)/g,v=d(e=>e.replace(E,(t,n)=>n?n.toUpperCase():"")),T=/\B([A-Z])/g,ee=d(e=>e.replace(T,"-$1").toLowerCase()),I=d(e=>e.charAt(0).toUpperCase()+e.slice(1)),te=d(e=>e?`on${I(e)}`:""),ne=(e,t)=>!Object.is(e,t),oe=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},se=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},re=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ie=e=>{const t=a(e)?Number(e):NaN;return isNaN(t)?e:t};let S;const ce=()=>S||(S=typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:typeof global!="undefined"?global:{}),B="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error",ae=y(B);function M(e){if(c(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],s=a(o)?z(o):M(o);if(s)for(const i in s)t[i]=s[i]}return t}else if(a(e)||r(e))return e}const P=/;(?![^(]*\))/g,U=/:([^]+)/,_=/\/\*[^]*?\*\//g;function z(e){const t={};return e.replace(_,"").split(P).forEach(n=>{if(n){const o=n.split(U);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function x(e){let t="";if(a(e))t=e;else if(c(e))for(let n=0;n<e.length;n++){const o=x(e[n]);o&&(t+=o+" ")}else if(r(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const D="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",le=y(D);function fe(e){return!!e||e===""}function L(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=h(e[o],t[o]);return n}function h(e,t){if(e===t)return!0;let n=b(e),o=b(t);if(n||o)return n&&o?e.getTime()===t.getTime():!1;if(n=u(e),o=u(t),n||o)return e===t;if(n=c(e),o=c(t),n||o)return n&&o?L(e,t):!1;if(n=r(e),o=r(t),n||o){if(!n||!o)return!1;const s=Object.keys(e).length,i=Object.keys(t).length;if(s!==i)return!1;for(const f in e){const m=e.hasOwnProperty(f),O=t.hasOwnProperty(f);if(m&&!O||!m&&O||!h(e[f],t[f]))return!1}}return String(e)===String(t)}function pe(e,t){return e.findIndex(n=>h(n,t))}const ue=e=>a(e)?e:e==null?"":c(e)||r(e)&&(e.toString===N||!p(e.toString))?JSON.stringify(e,j,2):String(e),j=(e,t)=>t&&t.__v_isRef?j(e,t.value):w(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[o,s],i)=>(n[g(o,i)+" =>"]=s,n),{})}:C(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>g(n))}:u(t)?g(t):r(t)&&!c(t)&&!R(t)?String(t):t,g=(e,t="")=>{var n;return u(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};export{R as A,q as B,Z as C,X as D,V as E,Q as F,u as G,ae as H,y as I,fe as J,le as K,ue as L,x as M,K as N,M as O,z as P,oe as a,r as b,c,v as d,J as e,Y as f,a as g,ee as h,p as i,ne as j,se as k,G as l,H as m,te as n,pe as o,C as p,h as q,re as r,W as s,ie as t,F as u,ce as v,I as w,k as x,$ as y,w as z};
