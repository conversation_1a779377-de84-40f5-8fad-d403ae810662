import {
  ProjectModel,
  type ProjectSchema,
  type MaterialDescription,
  type BlockSchema,
  type HistorySchema,
  type HistoryItem,
  type ParseVueOptions,
  type PageFile,
  type StaticFileInfo,
  type NodeFromPlugin
} from '@vtj/core';
import { BaseService } from '@vtj/renderer';
import { MANIFEST_JSON, PAGES_JSON } from '@vtj/uni';
import { mapToObject } from '@vtj/utils';
import {
  getSchema,
  saveSchema,
  removeSchema,
  fileGenerator,
  projectGenerator,
  vueParser,
  getLowCodeApp
} from '@/apis';
import { SchemaType } from './types';

export class LcdpService extends BaseService {
  constructor(private notify: (msg: string) => void) {
    super();
  }
  /**
   * 项目初始化，接收引擎传过来的项目启动参数，此处需要判断项目是否存在，如果不存在即创建项目，并返回完整的项目信息
   * @param project
   * @returns
   */
  async init(project: ProjectSchema, isInit?: boolean): Promise<ProjectSchema> {
    const { id: app } = project;

    if (!app) throw new Error(`project id [ ${app} ] is not exist!`);

    // 如果应用运行时，直接查dsl返回
    if (!isInit) {
      // 获取项目DSL
      const res = await getSchema({
        app,
        name: app,
        type: SchemaType.Project
      }).catch(() => null);
      return res?.content as ProjectSchema;
    }

    // 获取项目信息
    const lowcodeApp = await getLowCodeApp(app).catch(() => null);
    if (!lowcodeApp) {
      throw new Error(`app [ ${app} ] is not exist!`);
    }

    // 获取项目DSL
    const res = await getSchema({
      app,
      name: app,
      type: SchemaType.Project
    }).catch(() => null);
    const content = res?.content as ProjectSchema;
    const dsl = content || new ProjectModel(project).toDsl();

    // 同步应用描述
    dsl.name = lowcodeApp.label;
    if (dsl.config?.title) {
      // 为了兼用旧的数据
      dsl.config.title = lowcodeApp.label;
    }

    // 如果uniapp的项目信息缺失 pagesJson 和 manifestJson，填充默认值
    if (dsl.platform === 'uniapp' && !Object.keys(dsl.uniConfig || {}).length) {
      dsl.uniConfig = {
        ...dsl.uniConfig,
        pagesJson: PAGES_JSON,
        manifestJson: MANIFEST_JSON
      };
    }
    // 如果是引擎初始化，需要保存
    await saveSchema({
      app,
      type: SchemaType.Project,
      name: app,
      content: dsl
    });
    return dsl;
  }

  /**
   * 保存项目信息，当项目信息发生变化时，引擎会调用此方法保存
   * @param project
   * @returns
   */
  async saveProject(project: ProjectSchema): Promise<boolean> {
    const model = new ProjectModel(project);
    const ret = await saveSchema({
      type: SchemaType.Project,
      app: model.id,
      name: model.id,
      content: model.toDsl()
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 保存项目依赖的组件物料信息，在出码时需要用到这些数据， 当项目依赖发生变化时会调用此方法保存数据
   * @param project
   * @param materials
   * @returns
   */
  async saveMaterials(
    project: ProjectSchema,
    materials: Map<string, MaterialDescription>
  ): Promise<boolean> {
    const app = project.id as string;
    const ret = await saveSchema({
      type: SchemaType.Material,
      app: app,
      name: app,
      content: mapToObject(materials)
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 保存文件DSL， 区块和页面都是相同的方法保存
   * @param file
   * @param project
   * @returns
   */
  async saveFile(file: BlockSchema, project?: ProjectSchema): Promise<boolean> {
    const app = project?.id as string;
    const ret = await saveSchema({
      type: SchemaType.File,
      app,
      name: file.id as string,
      content: file
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 获取文件DSL
   * @param id
   * @param project
   * @returns
   */
  async getFile(id: string, project?: ProjectSchema): Promise<BlockSchema> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.File,
      app: app,
      name: id
    }).catch(() => null);

    return (res?.content || {}) as BlockSchema;
  }

  /**
   * 删除文件
   * @param id
   * @param project
   * @returns
   */
  async removeFile(id: string, project?: ProjectSchema): Promise<boolean> {
    const app = project?.id as string;
    const ret = await removeSchema({
      type: SchemaType.File,
      app,
      name: id
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 保存历史记录
   * @param history
   * @param project
   * @returns
   */
  async saveHistory(
    history: HistorySchema,
    project?: ProjectSchema
  ): Promise<boolean> {
    const app = project?.id as string;
    const ret = await saveSchema({
      type: SchemaType.History,
      app,
      name: history.id,
      content: history
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 获取历史记录
   * @param id
   * @param project
   * @returns
   */
  async getHistory(
    id: string,
    project?: ProjectSchema
  ): Promise<HistorySchema> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.History,
      app: app,
      name: id
    }).catch(() => null);

    return (res?.content || {}) as HistorySchema;
  }

  /**
   * 删除历史记录
   * @param id
   * @param project
   * @returns
   */
  async removeHistory(id: string, project?: ProjectSchema): Promise<boolean> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.History,
      app: app,
      name: id
    }).catch(() => null);
    const content = res?.content as HistorySchema;
    if (content) {
      // 删除历史记录项
      const ids = (content.items || []).map((n: any) => n.id);
      await removeSchema({ type: SchemaType.HistoryItem, app, name: ids });
    }
    const ret = await removeSchema({
      type: SchemaType.History,
      app,
      name: id
    }).catch(() => false);

    return !!ret;
  }

  /**
   *  获取历史记录项
   * @param _fId
   * @param id
   * @param project
   * @returns
   */
  async getHistoryItem(
    _fId: string,
    id: string,
    project?: ProjectSchema
  ): Promise<HistoryItem> {
    const app = project?.id as string;
    const res = await getSchema({
      type: SchemaType.HistoryItem,
      app,
      name: id
    });
    return (res?.content || {}) as HistoryItem;
  }

  /**
   * 保存历史记录项
   * @param _fId
   * @param item
   * @param project
   * @returns
   */
  async saveHistoryItem(
    _fId: string,
    item: HistoryItem,
    project?: ProjectSchema
  ): Promise<boolean> {
    const app = project?.id as string;
    const ret = await saveSchema({
      type: SchemaType.HistoryItem,
      app,
      name: item.id,
      content: item
    }).catch(() => false);
    return !!ret;
  }

  /**
   * 删除历史记录项
   * @param _fId
   * @param ids
   * @param project
   * @returns
   */
  async removeHistoryItem(
    _fId: string,
    ids: string[],
    project?: ProjectSchema
  ): Promise<boolean> {
    const app = project?.id as string;
    const ret = await removeSchema({
      type: SchemaType.HistoryItem,
      app,
      name: ids
    }).catch(() => false);
    return !!ret;
  }

  /**
   * dsl转vue代码
   * @param project
   * @param dsl
   * @returns
   */
  async genVueContent(
    project: ProjectSchema,
    dsl: BlockSchema
  ): Promise<string> {
    const app = project.id as string;
    const res = await fileGenerator({
      app,
      platform: project.platform as string,
      dsl
    }).catch(() => null);
    return res || '';
  }

  /**
   * 项目出码, 返回zip文件下载url
   * @param project
   * @returns
   */
  async genSource(project: ProjectSchema): Promise<string> {
    const app = project.id as string;
    const link = await projectGenerator({ project, app });
    return link || '';
  }

  /**
   * Vue源码转DSL
   * @param project
   * @param options
   * @returns
   */
  async parseVue(
    project: ProjectSchema,
    options: ParseVueOptions
  ): Promise<BlockSchema> {
    return await vueParser({
      project,
      ...options
    }).catch((e) => e.message);
  }

  async createRawPage(_file: PageFile): Promise<boolean> {
    this.notify('在线版本不支持源码模式页面，如需体验请使用本地版本');
    return Promise.resolve(false);
  }

  async removeRawPage(_id: string): Promise<boolean> {
    this.notify('在线演示版本不支持源码模式页面，如需体验请下载本地版本');
    return Promise.resolve(false);
  }

  async uploadStaticFile(
    _file: File,
    _rojectId: string
  ): Promise<StaticFileInfo | null> {
    this.notify('在线演示版本不支持文件上传，如需体验请下载本地版本');
    return Promise.resolve(null);
  }

  async getStaticFiles(_projectId: string): Promise<StaticFileInfo[]> {
    this.notify('在线版本不支持文件上传，如需体验请下载本地版本。');
    return Promise.resolve([]);
  }

  async getPluginMaterial(
    _from: NodeFromPlugin
  ): Promise<MaterialDescription | null> {
    this.notify('在线版本不支持引用和插件，如需体验请下载本地版本');
    return Promise.resolve(null);
  }
}
