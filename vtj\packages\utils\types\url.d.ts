/**
 * 获取当前页面的 host
 */
export declare function getCurrentHost(includePath: boolean): string | null;
/**
 * 获取指定url的host
 */
export declare function getHost(url?: string): string;
/**
 * 键值对转换成查询字符串
 */
export declare function stringify(query: Record<string, any>): string;
/**
 * 参数字符串转换成对象形式，如：a=1&b=2 转换成 {a:1, b:2}
 */
export declare function parse(str: string, sep?: string, eq?: string): Record<string, any>;
/**
 * 在url追加参数
 */
export declare function append(url: string, query: string | Record<string, any>): string;
