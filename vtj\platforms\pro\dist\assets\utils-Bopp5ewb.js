var f=Object.defineProperty;var p=Object.getOwnPropertySymbols;var d=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var g=(t,a,s)=>a in t?f(t,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[a]=s,E=(t,a)=>{for(var s in a||(a={}))d.call(a,s)&&g(t,s,a[s]);if(p)for(var s of p(a))T.call(a,s)&&g(t,s,a[s]);return t};var _=(t,a,s)=>g(t,typeof a!="symbol"?a+"":a,s);var S=(t,a,s)=>new Promise((o,n)=>{var e=r=>{try{i(s.next(r))}catch(l){n(l)}},c=r=>{try{i(s.throw(r))}catch(l){n(l)}},i=r=>r.done?o(r.value):Promise.resolve(r.value).then(e,c);i((s=s.apply(t,a)).next())});import{aA as O}from"./vue-ipWmmxHk.js";import{P as I,s as A,p as V,n as m,X as b,a as h,L as P,O as R,q as M,c as y,H as N,d as B,Y as L,N as D,W as K,B as x,M as H,K as k,F as w,e as U,f as W,D as Y,g as j,J as F,Q as v,Z as G,t as $,i as z,k as J,l as q,r as X,u as Z,x as C,z as Q,C as aa,E as sa,G as ea,I as ta,R as ra,T as na,U as ia,V as oa,_ as la,a0 as _a,a1 as Ea,a2 as ca,a3 as ga,a4 as pa,a5 as Sa,a6 as ua,a7 as fa,a8 as da,a9 as Ta,aa as Oa,ab as Ia,ac as Aa,ad as Va,ae as ma,af as ba,ag as ha,ah as Pa,ai as Ra,aj as Ma,ak as ya,al as Na,am as Ba,an as La,ao as Da,ap as Ka,h as xa,aq as Ha,ar as ka,as as wa,at as Ua,au as Wa,av as Ya,aw as ja,ax as Fa,ay as va,az as Ga,aA as $a}from"./Editor-Dgw5r9tb-C0tzAbRO.js";import{E as za,a as Ja,b as qa,c as Xa}from"./element-plus-COProxbp.js";const Za=Object.freeze(Object.defineProperty({__proto__:null,API_METHOD_TYPES:I,Assets:A,Binder:V,Box:m,Designer:b,DevTools:h,EVENT_MODIFIERS:P,Editor:R,Engine:M,EventBinder:y,GET_TOKENS_LINK:N,Icon:B,Item:L,KEYBOARD_EVENT_LIST:D,MAX_TOKENS:K,MOBILE_SIZE:x,MOUSE_EVENT_LIST:H,MicroApp:k,NAME_REGEX:w,NPM_REGISTRY_URL:U,OpenApi:W,PAD_SIZE:Y,Panel:j,REPORT_API:F,RegionType:v,RegionWrapper:G,Renderer:$,SAVE_BLOCK_FILE_FINISH:z,SESSION_ID_KEY:J,STATE_KEY:q,SetterView:X,SetterWrapper:Z,Simulator:C,Skeleton:Q,SkeletonWrapper:aa,SlotsPicker:sa,State:ea,Tabs:ta,VERSION_REGEX:ra,VTJ_DESIGNER_VERSION:na,VUE_DEVTOOLS_FRAME_STATE_KEY:ia,VUE_DEVTOOLS_OVERLAY_PATH:oa,VUE_DEVTOOLS_PATH:la,VariableBinder:_a,Viewer:Ea,Viewport:ca,WidgetGroup:ga,WidgetWrapper:pa,alert:Sa,builtInDeps:ua,builtInMaterials:fa,builtInSetters:da,builtInWidgets:Ta,confirm:Oa,createSlotsPicker:Ia,defaultSetter:Aa,depsManager:Va,engineKey:ma,expressionValidate:ba,getClassProperties:ha,message:Pa,normalizedStyle:Ra,notify:Ma,proxyContext:ya,readJsonFile:Na,regions:Ba,setterManager:La,setters:Da,useEngine:Ka,widgetManager:xa,widgets:Ha},Symbol.toStringTag,{value:"Module"}));class es{constructor(a){_(this,"urls",[]);_(this,"library","");_(this,"params",[]);_(this,"__BASE_PATH__","/");this.config=a;const s=E(E(E({},Ya),Za),Wa),o={Vue:O,__VTJ_PRO__:s,VtjUtils:Ua,VtjIcons:wa,VtjUI:ka,ElementPlus:za};for(const[l,u]of Object.entries(o))window[l]=u;const{extension:n,__BASE_PATH__:e="/"}=a||{},{urls:c=[],library:i,params:r=[]}=n||{};i&&(this.urls=c,this.library=i,this.params=r,this.__BASE_PATH__=e)}load(){return S(this,null,function*(){let a={};if(this.library){const s=this.__BASE_PATH__,o=this.urls.filter(e=>ja(e)).map(e=>`${s}${e}`),n=this.urls.filter(e=>Fa(e)).map(e=>`${s}${e}`);if(va(o),n.length){const e=yield Ga(n,this.library).catch(()=>null);e&&typeof e=="function"?a=e.call(e,this.config,...this.params):a=e||{}}}return Object.assign({},this.getEngineOptions(),a)})}getEngineOptions(){const a=["materialPath","pageBasePath","pageRouteName","remote","checkVersion","noMask"];return $a(this.config,a)}}function ts(){return Xa.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"})}function rs(t,a="",s="warning"){qa({title:a,message:t,type:s})}function ns(t,a){return Ja.alert(t,a)}export{es as E,ns as a,ts as l,rs as n};
