import { Module, Global } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CacheService } from './cache.service';
import { CacheController } from './cache.controller';
import { Cache } from './entities/cache.entity';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([Cache])],
  providers: [CacheService],
  controllers: [CacheController],
  exports: [TypeOrmModule, CacheService]
})
export class CacheModule {}
