{"extends": "./node_modules/@vtj/cli/config/tsconfig.web.json", "compilerOptions": {"allowJs": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "lib": ["esnext"], "types": ["node", "@dcloudio/types", "@uni-helper/uni-app-types", "@uni-helper/uni-ui-types"]}, "include": ["shims-uni.d.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "vueCompilerOptions": {"nativeTags": ["component", "template", "slot"]}}