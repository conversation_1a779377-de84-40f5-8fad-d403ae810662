import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Delete,
  Query
} from '@nestjs/common';
import { AccessService } from './access.service';
import { AccessDto } from './dto/access.dto';
import { ApiDto } from './dto/api.dto';
import { QueryDto } from '../shared';

@Controller('access')
export class AccessController {
  constructor(private readonly service: AccessService) {}

  @Post()
  save(@Body() dto: AccessDto) {
    return this.service.save(dto);
  }

  @Post('apis/:id')
  saveApis(
    @Param('id') accessId: string,
    @Body('records') apis: ApiDto[],
    @Body('removed') removed: string[]
  ) {
    return this.service.saveApis(accessId, apis, removed);
  }

  @Get('apps')
  findApps(@Query() dto?: QueryDto) {
    return this.service.findForRoot(dto);
  }

  @Get('apps/:code')
  findApp(@Param('code') code: string) {
    return this.service.findByRoot(code);
  }

  @Get('detail/:id')
  findOne(@Param('id') id: string) {
    return this, this.service.findOne(id);
  }

  @Delete()
  remove(@Query('code') code: string) {
    return this.service.remove(code);
  }
}
