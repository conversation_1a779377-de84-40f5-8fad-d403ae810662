import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  HttpException
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { tap, catchError } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(
    context: ExecutionContext,
    next: CallHandler<any>
  ): Observable<any> {
    const methodKey = context.getHandler().name;
    const className = context.getClass().name;
    const logger = new Logger(className, { timestamp: true });
    const ctx = context.switchToHttp();
    const req = ctx.getRequest<Request>();
    const { url, method, params, query, ip } = req;
    logger.log(
      `Request {${url}, ${method}} ${methodKey} [${ip}] ${JSON.stringify({ params, query })}`
    );
    return next.handle().pipe(
      tap((res) => {
        logger.log(
          `Response {${url}, ${method}} ${methodKey} code:${res?.code}`
        );
      }),
      catchError((err: any) => {
        if (err instanceof HttpException) {
          const response = err.getResponse();
          const message = err.message;
          const status = err.getStatus();
          logger.error(
            `Response {${url}, ${method}} ${methodKey} ${JSON.stringify({ status, message, response })}`,
            err.stack
          );
          throw response;
        } else {
          const msg = JSON.stringify(err);
          logger.error(
            `Response {${url}, ${method}} ${methodKey} ${msg}`,
            err.stack
          );
          throw err;
        }
      })
    );
  }
}
