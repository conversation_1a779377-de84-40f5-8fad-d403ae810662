import { QueryFormProps, QueryFormEmits } from './types';
import { Emits } from '../shared';
import { Ref, ComputedRef } from 'vue';
export declare function useCollapsed(props: QueryFormProps, emit: Emits<QueryFormEmits>): {
    collapsed: Ref<boolean, boolean>;
    toggleCollapsed: () => Promise<void>;
    collapsedClass: ComputedRef<{
        'is-collapsed': boolean | undefined;
    }>;
    collapsedStyle: ComputedRef<{
        height: string;
    } | null>;
    showCollapsible: ComputedRef<boolean | undefined>;
};
