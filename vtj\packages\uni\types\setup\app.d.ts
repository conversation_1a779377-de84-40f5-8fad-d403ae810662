import { UniConfig, JSFunction } from '@vtj/core';
import { Provider } from '@vtj/renderer';
import { SetupUniAppOptions, UniRoute } from '../types';
export declare function setupUniApp(options: SetupUniAppOptions): any;
export declare function createUniAppComponent(uniConfig: UniConfig, parser: (script: JSFunction) => Function): Record<string, Function>;
export declare function createUniLoader(provider: Provider, id: string): () => Promise<any>;
export declare function createUniRoutes(provider: Provider, includeBlock?: boolean, basePath?: string): Promise<UniRoute[]>;
