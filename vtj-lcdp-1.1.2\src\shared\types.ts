export type PlatformTypeVO = 'Web' | 'H5' | 'UniApp';

export type AppScope = 'public' | 'private' | 'protected';

export interface ApiListResponse<T = any> {
  list: T[];
  page: number;
  limit: number;
  total: number;
}

export interface LowCodeAppVO {
  id?: string;
  name: string;
  label: string;
  platform: PlatformTypeVO;
  scope: AppScope;
  userId?: string;
}

export enum SchemaType {
  Project = 'project',
  Material = 'material',
  File = 'file',
  History = 'history',
  HistoryItem = 'history-item'
}
