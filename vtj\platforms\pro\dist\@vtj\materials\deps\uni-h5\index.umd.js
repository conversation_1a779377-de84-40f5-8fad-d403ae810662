(function(y,B){typeof exports=="object"&&typeof module!="undefined"?B(exports):typeof define=="function"&&define.amd?define(["exports"],B):(y=typeof globalThis!="undefined"?globalThis:y||self,B(y.UniH5={}))})(this,function(y){"use strict";var FC=Object.defineProperty;var og=Object.getOwnPropertySymbols;var $C=Object.prototype.hasOwnProperty,zC=Object.prototype.propertyIsEnumerable;var ig=(y,B,Se)=>B in y?FC(y,B,{enumerable:!0,configurable:!0,writable:!0,value:Se}):y[B]=Se,ba=(y,B)=>{for(var Se in B||(B={}))$C.call(B,Se)&&ig(y,Se,B[Se]);if(og)for(var Se of og(B))zC.call(B,Se)&&ig(y,Se,B[Se]);return y};const B=Object.assign,Se=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},rg=Object.prototype.hasOwnProperty,le=(e,t)=>rg.call(e,t),K=Array.isArray,G=e=>typeof e=="function",J=e=>typeof e=="string",ag=e=>e!==null&&typeof e=="object",sg=e=>(ag(e)||G(e))&&G(e.then)&&G(e.catch),lg=Object.prototype.toString,cg=e=>lg.call(e),fe=e=>cg(e)==="[object Object]",hi=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},ug=/-(\w)/g,wa=hi(e=>e.replace(ug,(t,n)=>n?n.toUpperCase():"")),dg=/\B([A-Z])/g,Ct=hi(e=>e.replace(dg,"-$1").toLowerCase()),gi=hi(e=>e.charAt(0).toUpperCase()+e.slice(1)),At=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},fg=/;(?![^(]*\))/g,hg=/:([^]+)/,gg=/\/\*[^]*?\*\//g;function pg(e){const t={};return e.replace(gg,"").split(fg).forEach(n=>{if(n){const o=n.split(hg);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function mg(e){let t="";if(!e||J(e))return t;for(const n in e){const o=e[n],i=n.startsWith("--")?n:Ct(n);(J(o)||typeof o=="number")&&(t+=`${i}:${o};`)}return t}const vg=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view","location-picker","location-view"].map(e=>"uni-"+e),_g=["list-view","list-item","sticky-section","sticky-header","cloud-db-element"].map(e=>"uni-"+e),yg=["list-item"].map(e=>"uni-"+e);function bg(e){if(yg.indexOf(e)!==-1)return!1;const t="uni-"+e.replace("v-uni-","");return vg.indexOf(t)!==-1||_g.indexOf(t)!==-1}const Et=`
`,Sa=44,wg=50,Sg=768,Ta="UNI_LOCALE",pi=["%","%"],nn="#007aff",Tg=/^([a-z-]+:)?\/\//i,Vg=/^data:.*,.*/,Cg="WEB_INVOKE_APPSERVICE",lt="onShow",ct="onHide",Ag="onLaunch",ut="onError",Ye="onThemeChange",Eg="offThemeChange",jn="onPageNotFound",Yn="onUnhandledRejection",kg="onExit",Va="onLoad",Ig="onReady",Ca="onUnload",Ng="onInit",xg="onSaveExitState",Xn="onResize",mi="onBackPress",Jn="onPageScroll",Mg="onTabItemTap",vi="onReachBottom",Aa="onPullDownRefresh",Pg="onShareTimeline",Og="onShareChat",Rg="onAddToFavorites",Lg="onShareAppMessage",Ea="onNavigationBarButtonTap",ka="onNavigationBarChange",Ia="onNavigationBarSearchInputClicked",Na="onNavigationBarSearchInputChanged",xa="onNavigationBarSearchInputConfirmed",_i="onNavigationBarSearchInputFocusChanged",Ma="onAppEnterForeground",Pa="onAppEnterBackground",Oa="onWebInvokeAppService",Bg="onWxsInvokeCallMethod";function Dg(e=""){return(""+e).replace(/[^\x00-\xff]/g,"**").length}function Ra(e){return e.indexOf("/")===0}function qe(e){return Ra(e)?e:"/"+e}function La(e){return Ra(e)?e.slice(1):e}const Ug=(e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n};function yi(e,t){for(const n in t)e.style[n]=t[n]}function te(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}const Kn=e=>e>9?e:"0"+e;function Ba({date:e=new Date,mode:t="date"}){return t==="time"?Kn(e.getHours())+":"+Kn(e.getMinutes()):e.getFullYear()+"-"+Kn(e.getMonth()+1)+"-"+Kn(e.getDate())}function ye(e,t){e=e||{},J(t)&&(t={errMsg:t}),/:ok$/.test(t.errMsg)?G(e.success)&&e.success(t):G(e.fail)&&e.fail(t),G(e.complete)&&e.complete(t)}function Fg(e){let t={};return fe(e)&&Object.keys(e).sort().forEach(n=>{const o=n;t[o]=e[o]}),Object.keys(t)?t:e}function $g(e){return!!e.appContext}function on(e){return e&&($g(e)?e.proxy:e)}function Da(e){if(!e)return;let t=e.type.name;for(;t&&bg(Ct(t));)e=e.parent,t=e.type.name;return e.proxy}function Ua(e){return e.nodeType===1}function Qn(e,t=!1){const{vnode:n}=e;if(Ua(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(o.shapeFlag&16){const i=o.children.filter(r=>r.el&&Ua(r.el));if(i.length>0)return t?i.map(r=>r.el):i[0].el}return t?n.el?[n.el]:[]:n.el}function Fa(e){return wa(e.substring(5))}const zg=te(e=>{e=e||(i=>i.tagName.startsWith("UNI-"));const t=HTMLElement.prototype,n=t.setAttribute;t.setAttribute=function(i,r){if(i.startsWith("data-")&&e(this)){const a=this.__uniDataset||(this.__uniDataset={});a[Fa(i)]=r}n.call(this,i,r)};const o=t.removeAttribute;t.removeAttribute=function(i){this.__uniDataset&&i.startsWith("data-")&&e(this)&&delete this.__uniDataset[Fa(i)],o.call(this,i)}});function bi(e){return B({},e.dataset,e.__uniDataset)}function kt(e){return{passive:e}}function wi(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:bi(e),offsetTop:n,offsetLeft:o}}function Wg(e,t,n){const o=document.fonts;if(o){const i=new FontFace(e,t,n);return i.load().then(()=>{o.add&&o.add(i)})}return new Promise(i=>{const r=document.createElement("style"),a=[];if(n){const{style:s,weight:l,stretch:c,unicodeRange:u,variant:d,featureSettings:f}=n;s&&a.push(`font-style:${s}`),l&&a.push(`font-weight:${l}`),c&&a.push(`font-stretch:${c}`),u&&a.push(`unicode-range:${u}`),d&&a.push(`font-variant:${d}`),f&&a.push(`font-feature-settings:${f}`)}r.innerText=`@font-face{font-family:"${e}";src:${t};${a.join(";")}}`,document.head.appendChild(r),i()})}function Hg(e,t,n){if(J(e)){const s=document.querySelector(e);if(s){const{top:l}=s.getBoundingClientRect();e=l+window.pageYOffset;const c=document.querySelector("uni-page-head");c&&(e-=c.offsetHeight)}}e<0&&(e=0);const o=document.documentElement,{clientHeight:i,scrollHeight:r}=o;if(e=Math.min(e,r-i),t===0){o.scrollTop=document.body.scrollTop=e;return}if(window.scrollY===e)return;const a=s=>{if(s<=0){window.scrollTo(0,e);return}const l=e-window.scrollY;requestAnimationFrame(function(){window.scrollTo(0,window.scrollY+l/s*10),a(s-10)})};a(t)}function Si(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function $a(e={}){const t={};return Object.keys(e).forEach(n=>{try{t[n]=Si(e[n])}catch(o){t[n]=e[n]}}),t}const qg=/\+/g;function za(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<o.length;++i){const r=o[i].replace(qg," ");let a=r.indexOf("="),s=Si(a<0?r:r.slice(0,a)),l=a<0?null:Si(r.slice(a+1));if(s in t){let c=t[s];K(c)||(c=t[s]=[c]),c.push(l)}else t[s]=l}return t}function Gg(e){const[t,n]=e.split("?",2);return{path:t,query:za(n||"")}}function Ti(e,t,{clearTimeout:n,setTimeout:o}){let i;const r=function(){n(i),i=o(()=>e.apply(this,arguments),t)};return r.cancel=function(){n(i)},r}class Wa{constructor(t,n){this.id=t,this.listener={},this.emitCache=[],n&&Object.keys(n).forEach(o=>{this.on(o,n[o])})}emit(t,...n){const o=this.listener[t];if(!o)return this.emitCache.push({eventName:t,args:n});o.forEach(i=>{i.fn.apply(i.fn,n)}),this.listener[t]=o.filter(i=>i.type!=="once")}on(t,n){this._addListener(t,"on",n),this._clearCache(t)}once(t,n){this._addListener(t,"once",n),this._clearCache(t)}off(t,n){const o=this.listener[t];if(o)if(n)for(let i=0;i<o.length;)o[i].fn===n&&(o.splice(i,1),i--),i++;else delete this.listener[t]}_clearCache(t){for(let n=0;n<this.emitCache.length;n++){const o=this.emitCache[n],i=t?o.eventName===t?t:null:o.eventName;if(!i)continue;if(typeof this.emit.apply(this,[i,...o.args])=="number"){this.emitCache.pop();continue}this.emitCache.splice(n,1),n--}}_addListener(t,n,o){(this.listener[t]||(this.listener[t]=[])).push({fn:o,type:n})}}const Ha=[lt,ct,Ag,ut,Ye,jn,Yn,kg,Ng,Va,Ig,Ca,Xn,mi,Jn,Mg,vi,Aa,Pg,Rg,Lg,Og,xg,Ea,Ia,Na,xa,_i];function jg(e,t,n=!0){return n&&!G(t)?!1:Ha.indexOf(e)>-1?!0:e.indexOf("on")===0}let Vi;const qa=[];function Ga(e){if(Vi)return e(Vi);qa.push(e)}function Yg(e){Vi=e,qa.forEach(t=>t(e))}const Xg=te((e,t)=>t(e)),ja=function(){};ja.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function i(){o.off(e,i),t.apply(n,arguments)}return i._=t,this.on(e,i,n)},emit:function(e){var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,i=n.length;for(o;o<i;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],i=[];if(o&&t){for(var r=o.length-1;r>=0;r--)if(o[r].fn===t||o[r].fn._===t||o[r]._id===t){o.splice(r,1);break}i=o}return i.length?n[e]=i:delete n[e],this}};var Ya=ja;const Xa={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Jg(e){return e&&e in Xa?Xa[e]:e}function Kg(e){return e==="black"?"#000000":"#ffffff"}function Ja(e,t,n){if(J(t)&&t.startsWith("@")){const o=t.replace("@","");let i=e[o]||t;switch(n){case"titleColor":i=Kg(i);break;case"borderStyle":i=Jg(i);break}return i}return t}function Zn(e,t={},n="light"){const o=t[n],i={};return typeof o=="undefined"||!e?e:(Object.keys(e).forEach(r=>{const a=e[r],s=()=>fe(a)?Zn(a,t,n):K(a)?a.map(l=>typeof l=="object"?Zn(l,t,n):Ja(o,l)):Ja(o,a,r);i[r]=s()}),i)}const Qg=e=>e!==null&&typeof e=="object",Zg=["{","}"];class ep{constructor(){this._caches=Object.create(null)}interpolate(t,n,o=Zg){if(!n)return[t];let i=this._caches[t];return i||(i=op(t,o),this._caches[t]=i),ip(i,n)}}const tp=/^(?:\d)+/,np=/^(?:\w)+/;function op(e,[t,n]){const o=[];let i=0,r="";for(;i<e.length;){let a=e[i++];if(a===t){r&&o.push({type:"text",value:r}),r="";let s="";for(a=e[i++];a!==void 0&&a!==n;)s+=a,a=e[i++];const l=a===n,c=tp.test(s)?"list":l&&np.test(s)?"named":"unknown";o.push({value:s,type:c})}else r+=a}return r&&o.push({type:"text",value:r}),o}function ip(e,t){const n=[];let o=0;const i=Array.isArray(t)?"list":Qg(t)?"named":"unknown";if(i==="unknown")return n;for(;o<e.length;){const r=e[o];switch(r.type){case"text":n.push(r.value);break;case"list":n.push(t[parseInt(r.value,10)]);break;case"named":i==="named"&&n.push(t[r.value]);break}o++}return n}const Te="zh-Hans",Ce="zh-Hant",me="en",Pe="fr",Oe="es",rp=Object.prototype.hasOwnProperty,Ka=(e,t)=>rp.call(e,t),ap=new ep;function sp(e,t){return!!t.find(n=>e.indexOf(n)!==-1)}function lp(e,t){return t.find(n=>e.indexOf(n)===0)}function Qa(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if(e=e.toLowerCase(),e==="chinese")return Te;if(e.indexOf("zh")===0)return e.indexOf("-hans")>-1?Te:e.indexOf("-hant")>-1||sp(e,["-tw","-hk","-mo","-cht"])?Ce:Te;let n=[me,Pe,Oe];t&&Object.keys(t).length>0&&(n=Object.keys(t));const o=lp(e,n);if(o)return o}class cp{constructor({locale:t,fallbackLocale:n,messages:o,watcher:i,formater:r}){this.locale=me,this.fallbackLocale=me,this.message={},this.messages={},this.watchers=[],n&&(this.fallbackLocale=n),this.formater=r||ap,this.messages=o||{},this.setLocale(t||me),i&&this.watchLocale(i)}setLocale(t){const n=this.locale;this.locale=Qa(t,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],n!==this.locale&&this.watchers.forEach(o=>{o(this.locale,n)})}getLocale(){return this.locale}watchLocale(t){const n=this.watchers.push(t)-1;return()=>{this.watchers.splice(n,1)}}add(t,n,o=!0){const i=this.messages[t];i?o?Object.assign(i,n):Object.keys(n).forEach(r=>{Ka(i,r)||(i[r]=n[r])}):this.messages[t]=n}f(t,n,o){return this.formater.interpolate(t,n,o).join("")}t(t,n,o){let i=this.message;return typeof n=="string"?(n=Qa(n,this.messages),n&&(i=this.messages[n])):o=n,Ka(i,t)?this.formater.interpolate(i[t],o).join(""):(console.warn(`Cannot translate the value of keypath ${t}. Use the value of keypath as default.`),t)}}function up(e,t){e.$watchLocale?e.$watchLocale(n=>{t.setLocale(n)}):e.$watch(()=>e.$locale,n=>{t.setLocale(n)})}function dp(){return typeof uni!="undefined"&&uni.getLocale?uni.getLocale():typeof global!="undefined"&&global.getLocale?global.getLocale():me}function fp(e,t={},n,o){if(typeof e!="string"){const a=[t,e];e=a[0],t=a[1]}typeof e!="string"&&(e=dp()),typeof n!="string"&&(n=typeof __uniConfig!="undefined"&&__uniConfig.fallbackLocale||me);const i=new cp({locale:e,fallbackLocale:n,messages:t,watcher:o});let r=(a,s)=>{if(typeof getApp!="function")r=function(l,c){return i.t(l,c)};else{let l=!1;r=function(c,u){const d=getApp().$vm;return d&&(d.$locale,l||(l=!0,up(d,i))),i.t(c,u)}}return r(a,s)};return{i18n:i,f(a,s,l){return i.f(a,s,l)},t(a,s){return r(a,s)},add(a,s,l=!0){return i.add(a,s,l)},watch(a){return i.watchLocale(a)},getLocale(){return i.getLocale()},setLocale(a){return i.setLocale(a)}}}function Za(e,t){return e.indexOf(t[0])>-1}const Ci=te(()=>typeof __uniConfig!="undefined"&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length);let rn;function hp(){const e=uni.getLocale(),t=__uniConfig.locales;return t[e]||t[__uniConfig.fallbackLocale]||t.en||{}}function gp(e){return Za(e,pi)?D().f(e,hp(),pi):e}function es(e,t){if(t.length===1){if(e){const o=s=>J(s)&&Za(s,pi),i=t[0];let r=[];if(K(e)&&(r=e.filter(s=>o(s[i]))).length)return r;const a=e[t[0]];if(o(a))return e}return}const n=t.shift();return es(e&&e[n],t)}function pp(e,t){return t.map(n=>Ai(e,n))}function Ai(e,t){const n=es(e,t);if(!n)return!1;const o=t[t.length-1];if(K(n))n.forEach(i=>Ai(i,[o]));else{let i=n[o];Object.defineProperty(n,o,{get(){return gp(i)},set(r){i=r}})}return!0}function D(){if(!rn){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage[Ta]||__uniConfig.locale||navigator.language,rn=fp(e),Ci()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach(n=>rn.add(n,__uniConfig.locales[n])),rn.setLocale(e)}}return rn}function W(e,t,n){return t.reduce((o,i,r)=>(o[e+i]=n[r],o),{})}const mp=te(()=>{const e="uni.async.",t=["error"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["The connection timed out, click the screen to try again."]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["连接服务器超时，点击屏幕重试"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["連接服務器超時，點擊屏幕重試"]),!1)}),vp=te(()=>{const e="uni.showActionSheet.",t=["cancel"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Cancel"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Cancelar"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Annuler"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["取消"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["取消"]),!1)}),_p=te(()=>{const e="uni.showToast.",t=["unpaired"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Please note showToast must be paired with hideToast"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Tenga en cuenta que showToast debe estar emparejado con hideToast"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Veuillez noter que showToast doit être associé à hideToast"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["请注意 showToast 与 hideToast 必须配对使用"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["請注意 showToast 與 hideToast 必須配對使用"]),!1)}),yp=te(()=>{const e="uni.showLoading.",t=["unpaired"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Please note showLoading must be paired with hideLoading"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Tenga en cuenta que showLoading debe estar emparejado con hideLoading"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Veuillez noter que showLoading doit être associé à hideLoading"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["请注意 showLoading 与 hideLoading 必须配对使用"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["請注意 showLoading 與 hideLoading 必須配對使用"]),!1)}),bp=te(()=>{const e="uni.showModal.",t=["cancel","confirm"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Cancel","OK"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Cancelar","OK"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Annuler","OK"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["取消","确定"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["取消","確定"]),!1)}),Ei=te(()=>{const e="uni.chooseFile.",t=["notUserActivation"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["File chooser dialog can only be shown with a user activation"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["El cuadro de diálogo del selector de archivos solo se puede mostrar con la activación del usuario"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["La boîte de dialogue du sélecteur de fichier ne peut être affichée qu'avec une activation par l'utilisateur"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["文件选择器对话框只能在由用户激活时显示"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["文件選擇器對話框只能在由用戶激活時顯示"]),!1)}),wp=te(()=>{const e="uni.setClipboardData.",t=["success","fail"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Content copied","Copy failed, please copy manually"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Contenido copiado","Error al copiar, copie manualmente"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Contenu copié","Échec de la copie, copiez manuellement"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["内容已复制","复制失败，请手动复制"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["內容已復制","復制失敗，請手動復製"]),!1)}),Sp=te(()=>{const e="uni.getClipboardData.",t=["fail"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Reading failed, please paste manually"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Error de lectura, pegue manualmente"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Échec de la lecture, veuillez coller manuellement"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["读取失败，请手动粘贴"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["讀取失敗，請手動粘貼"]),!1)}),Tp=te(()=>{const e="uni.picker.",t=["done","cancel"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Done","Cancel"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["OK","Cancelar"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["OK","Annuler"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["完成","取消"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["完成","取消"]),!1)}),Vp=te(()=>{const e="uni.video.",t=["danmu","volume"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Danmu","Volume"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Danmu","Volumen"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Danmu","Le Volume"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["弹幕","音量"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["彈幕","音量"]),!1)}),Cp=te(()=>{const e="uni.chooseLocation.",t=["search","cancel"];__UNI_FEATURE_I18N_EN__&&D().add(me,W(e,t,["Find Place","Cancel"]),!1),__UNI_FEATURE_I18N_ES__&&D().add(Oe,W(e,t,["Encontrar","Cancelar"]),!1),__UNI_FEATURE_I18N_FR__&&D().add(Pe,W(e,t,["Trouve","Annuler"]),!1),__UNI_FEATURE_I18N_ZH_HANS__&&D().add(Te,W(e,t,["搜索地点","取消"]),!1),__UNI_FEATURE_I18N_ZH_HANT__&&D().add(Ce,W(e,t,["搜索地點","取消"]),!1)});function Ap(e){if(Ci())return pp(e,[["titleText"],["searchInput","placeholder"],["buttons","text"]])}function Ep(e){return Ci()&&e.list&&e.list.forEach(t=>{Ai(t,["text"])}),e}function ts(e){const t=new Ya;return{on(n,o){return t.on(n,o)},once(n,o){return t.once(n,o)},off(n,o){return t.off(n,o)},emit(n,...o){return t.emit(n,...o)},subscribe(n,o,i=!1){t[i?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,i){t.emit(`${e}.${n}`,o,i)}}}const It="invokeViewApi",ns="invokeServiceApi";let kp=1;const Ip=(e,t,n)=>{const{subscribe:o,publishHandler:i}=UniViewJSBridge,r=n?kp++:0;n&&o(ns+"."+r,n,!0),i(ns,{id:r,name:e,args:t})},Nt=Object.create(null);function an(e,t){return e+"."+t}function Np(e,t){UniViewJSBridge.subscribe(an(e,It),Pp)}function xp(e){UniViewJSBridge.unsubscribe(an(e,It)),Object.keys(Nt).forEach(t=>{t.indexOf(e+".")===0&&delete Nt[t]})}function os(e,t,n){t=an(e,t),Nt[t]||(Nt[t]=n)}function Mp(e,t){t=an(e,t),delete Nt[t]}function Pp({id:e,name:t,args:n},o){t=an(o,t);const i=a=>{e&&UniViewJSBridge.publishHandler(It+"."+e,a)},r=Nt[t];r?r(n,i):i({})}const Op=B(ts("service"),{invokeServiceMethod:Ip}),Rp=350,is=10,eo=kt(!0);let sn;function ln(){sn&&(clearTimeout(sn),sn=null)}let rs=0,as=0;function Lp(e){if(ln(),e.touches.length!==1)return;const{pageX:t,pageY:n}=e.touches[0];rs=t,as=n,sn=setTimeout(function(){const o=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});o.touches=e.touches,o.changedTouches=e.changedTouches,e.target.dispatchEvent(o)},Rp)}function Bp(e){if(!sn)return;if(e.touches.length!==1)return ln();const{pageX:t,pageY:n}=e.touches[0];if(Math.abs(t-rs)>is||Math.abs(n-as)>is)return ln()}function Dp(){window.addEventListener("touchstart",Lp,eo),window.addEventListener("touchmove",Bp,eo),window.addEventListener("touchend",ln,eo),window.addEventListener("touchcancel",ln,eo)}function ss(e,t){const n=Number(e);return isNaN(n)?t:n}function Up(){const e=/^Apple/.test(navigator.vendor)&&typeof window.orientation=="number",t=e&&Math.abs(window.orientation)===90;var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width,o=Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n;return o}function Fp(){const e=__uniConfig.globalStyle||{},t=ss(e.rpxCalcMaxDeviceWidth,960),n=ss(e.rpxCalcBaseDeviceWidth,375);function o(){let i=Up();i=i<=t?i:n,document.documentElement.style.fontSize=i/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function $p(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ls=["top","left","right","bottom"],ki,to={},Re;function Ii(){return!("CSS"in window)||typeof CSS.supports!="function"?Re="":CSS.supports("top: env(safe-area-inset-top)")?Re="env":CSS.supports("top: constant(safe-area-inset-top)")?Re="constant":Re="",Re}function cs(){if(Re=typeof Re=="string"?Re:Ii(),!Re){ls.forEach(function(s){to[s]=0});return}function e(s,l){var c=s.style;Object.keys(l).forEach(function(u){var d=l[u];c[u]=d})}var t=[];function n(s){s?t.push(s):t.forEach(function(l){l()})}var o=!1;try{var i=Object.defineProperty({},"passive",{get:function(){o={passive:!0}}});window.addEventListener("test",null,i)}catch(s){}function r(s,l){var c=document.createElement("div"),u=document.createElement("div"),d=document.createElement("div"),f=document.createElement("div"),h=100,_=1e4,v={position:"absolute",width:h+"px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:Re+"(safe-area-inset-"+l+")"};e(c,v),e(u,v),e(d,{transition:"0s",animation:"none",width:"400px",height:"400px"}),e(f,{transition:"0s",animation:"none",width:"250%",height:"250%"}),c.appendChild(d),u.appendChild(f),s.appendChild(c),s.appendChild(u),n(function(){c.scrollTop=u.scrollTop=_;var T=c.scrollTop,b=u.scrollTop;function g(){this.scrollTop!==(this===c?T:b)&&(c.scrollTop=u.scrollTop=_,T=c.scrollTop,b=u.scrollTop,zp(l))}c.addEventListener("scroll",g,o),u.addEventListener("scroll",g,o)});var m=getComputedStyle(c);Object.defineProperty(to,l,{configurable:!0,get:function(){return parseFloat(m.paddingBottom)}})}var a=document.createElement("div");e(a,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),ls.forEach(function(s){r(a,s)}),document.body.appendChild(a),n(),ki=!0}function no(e){return ki||cs(),to[e]}var oo=[];function zp(e){oo.length||setTimeout(function(){var t={};oo.forEach(function(n){t[n]=to[n]}),oo.length=0,io.forEach(function(n){n(t)})},0),oo.push(e)}var io=[];function Wp(e){Ii()&&(ki||cs(),typeof e=="function"&&io.push(e))}function Hp(e){var t=io.indexOf(e);t>=0&&io.splice(t,1)}var qp={get support(){return(typeof Re=="string"?Re:Ii()).length!=0},get top(){return no("top")},get left(){return no("left")},get right(){return no("right")},get bottom(){return no("bottom")},onChange:Wp,offChange:Hp},Gp=qp;const he=$p(Gp),Xe=Vue.withModifiers(()=>{},["prevent"]),cn=Vue.withModifiers(e=>{},["stop"]);function un(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function ro(){const e=document.documentElement.style,t=un(e,"--window-top");return t?t+he.top:0}function us(){const e=document.documentElement.style,t=ro(),n=un(e,"--window-bottom"),o=un(e,"--window-left"),i=un(e,"--window-right"),r=un(e,"--top-window-height");return{top:t,bottom:n?n+he.bottom:0,left:o?o+he.left:0,right:i?i+he.right:0,topWindowHeight:r||0}}function Ue(e){const t=document.documentElement.style;Object.keys(e).forEach(n=>{t.setProperty(n,e[n])})}function ds(e){return Ue(e)}const ao=new Map;function jp(e,t){let n=ao.get(e);n&&!(n instanceof HTMLStyleElement)&&(Yp(e),n=void 0),n?n.innerHTML=t:(n=document.createElement("style"),n.setAttribute("type","text/css"),n.innerHTML=t,document.head.appendChild(n)),ao.set(e,n)}function Yp(e){let t=ao.get(e);t&&(t instanceof CSSStyleSheet?document.adoptedStyleSheets=document.adoptedStyleSheets.filter(n=>n!==t):document.head.removeChild(t),ao.delete(e))}function dn(e){return Symbol(e)}function fs(e){return e=e+"",e.indexOf("rpx")!==-1||e.indexOf("upx")!==-1}function Je(e,t=!1){if(t)return Xp(e);if(J(e)){const n=parseInt(e)||0;return fs(e)?uni.upx2px(n):n}return e}function Xp(e){return fs(e)?e.replace(/(\d+(\.\d+)?)[ru]px/g,(t,n)=>uni.upx2px(parseFloat(n))+"px"):e}function hs(e){return e.$page}function Ni(e){return e.tagName.indexOf("UNI-")===0}const Jp="M20.928 10.176l-4.928 4.928-4.928-4.928-0.896 0.896 4.928 4.928-4.928 4.928 0.896 0.896 4.928-4.928 4.928 4.928 0.896-0.896-4.928-4.928 4.928-4.928-0.896-0.896zM16 2.080q-3.776 0-7.040 1.888-3.136 1.856-4.992 4.992-1.888 3.264-1.888 7.040t1.888 7.040q1.856 3.136 4.992 4.992 3.264 1.888 7.040 1.888t7.040-1.888q3.136-1.856 4.992-4.992 1.888-3.264 1.888-7.040t-1.888-7.040q-1.856-3.136-4.992-4.992-3.264-1.888-7.040-1.888zM16 28.64q-3.424 0-6.4-1.728-2.848-1.664-4.512-4.512-1.728-2.976-1.728-6.4t1.728-6.4q1.664-2.848 4.512-4.512 2.976-1.728 6.4-1.728t6.4 1.728q2.848 1.664 4.512 4.512 1.728 2.976 1.728 6.4t-1.728 6.4q-1.664 2.848-4.512 4.512-2.976 1.728-6.4 1.728z",Kp="M16 0q-4.352 0-8.064 2.176-3.616 2.144-5.76 5.76-2.176 3.712-2.176 8.064t2.176 8.064q2.144 3.616 5.76 5.76 3.712 2.176 8.064 2.176t8.064-2.176q3.616-2.144 5.76-5.76 2.176-3.712 2.176-8.064t-2.176-8.064q-2.144-3.616-5.76-5.76-3.712-2.176-8.064-2.176zM22.688 21.408q0.32 0.32 0.304 0.752t-0.336 0.736-0.752 0.304-0.752-0.32l-5.184-5.376-5.376 5.184q-0.32 0.32-0.752 0.304t-0.736-0.336-0.304-0.752 0.32-0.752l5.376-5.184-5.184-5.376q-0.32-0.32-0.304-0.752t0.336-0.752 0.752-0.304 0.752 0.336l5.184 5.376 5.376-5.184q0.32-0.32 0.752-0.304t0.752 0.336 0.304 0.752-0.336 0.752l-5.376 5.184 5.184 5.376z",Qp="M15.808 1.696q-3.776 0-7.072 1.984-3.2 1.888-5.088 5.152-1.952 3.392-1.952 7.36 0 3.776 1.952 7.072 1.888 3.2 5.088 5.088 3.296 1.952 7.072 1.952 3.968 0 7.36-1.952 3.264-1.888 5.152-5.088 1.984-3.296 1.984-7.072 0-4-1.984-7.36-1.888-3.264-5.152-5.152-3.36-1.984-7.36-1.984zM20.864 18.592l-3.776 4.928q-0.448 0.576-1.088 0.576t-1.088-0.576l-3.776-4.928q-0.448-0.576-0.24-0.992t0.944-0.416h2.976v-8.928q0-0.256 0.176-0.432t0.4-0.176h1.216q0.224 0 0.4 0.176t0.176 0.432v8.928h2.976q0.736 0 0.944 0.416t-0.24 0.992z",Zp="M15.808 0.128q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.176 3.776-2.176 8.16 0 4.224 2.176 7.872 2.080 3.552 5.632 5.632 3.648 2.176 7.872 2.176 4.384 0 8.16-2.176 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.416-2.176-8.16-2.112-3.616-5.728-5.728-3.744-2.176-8.16-2.176zM16.864 23.776q0 0.064-0.064 0.064h-1.568q-0.096 0-0.096-0.064l-0.256-11.328q0-0.064 0.064-0.064h2.112q0.096 0 0.064 0.064l-0.256 11.328zM16 10.88q-0.576 0-0.976-0.4t-0.4-0.96 0.4-0.96 0.976-0.4 0.976 0.4 0.4 0.96-0.4 0.96-0.976 0.4z",gs="M20.928 22.688q-1.696 1.376-3.744 2.112-2.112 0.768-4.384 0.768-3.488 0-6.464-1.728-2.88-1.696-4.576-4.608-1.76-2.976-1.76-6.464t1.76-6.464q1.696-2.88 4.576-4.576 2.976-1.76 6.464-1.76t6.464 1.76q2.912 1.696 4.608 4.576 1.728 2.976 1.728 6.464 0 2.272-0.768 4.384-0.736 2.048-2.112 3.744l9.312 9.28-1.824 1.824-9.28-9.312zM12.8 23.008q2.784 0 5.184-1.376 2.304-1.376 3.68-3.68 1.376-2.4 1.376-5.184t-1.376-5.152q-1.376-2.336-3.68-3.68-2.4-1.408-5.184-1.408t-5.152 1.408q-2.336 1.344-3.68 3.68-1.408 2.368-1.408 5.152t1.408 5.184q1.344 2.304 3.68 3.68 2.368 1.376 5.152 1.376zM12.8 23.008v0z",fn="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",em="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM24.832 11.328l-11.264 11.104q-0.032 0.032-0.112 0.032t-0.112-0.032l-5.216-5.376q-0.096-0.128 0-0.288l0.704-0.96q0.032-0.064 0.112-0.064t0.112 0.032l4.256 3.264q0.064 0.032 0.144 0.032t0.112-0.032l10.336-8.608q0.064-0.064 0.144-0.064t0.112 0.064l0.672 0.672q0.128 0.128 0 0.224z",tm="M15.84 0.096q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM23.008 21.92l-0.512 0.896q-0.096 0.128-0.224 0.064l-8-3.808q-0.096-0.064-0.16-0.128-0.128-0.096-0.128-0.288l0.512-12.096q0-0.064 0.048-0.112t0.112-0.048h1.376q0.064 0 0.112 0.048t0.048 0.112l0.448 10.848 6.304 4.256q0.064 0.064 0.080 0.128t-0.016 0.128z",ps="M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",hn="M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z",xi="M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z",ms="M31.562 4.9966666659375q0.435 0.399 0.435 0.87 0.036 0.58-0.399 0.98l-18.61 19.917q-0.145 0.145-0.327 0.217-0.073 0.037-0.145 0.11-0.254 0.035-0.472 0.035-0.29 0-0.544-0.036l-0.145-0.072q-0.109-0.073-0.217-0.182l-0.11-0.072L0.363 16.2786666659375q-0.327-0.399-0.363-0.907 0-0.544 0.363-1.016 0.435-0.326 0.961-0.362 0.527-0.036 0.962 0.362l9.722 9.542L29.712 5.0326666659375q0.399-0.363 0.943-0.363 0.544-0.036 0.907 0.327z";function ce(e,t="#000",n=27){return Vue.createVNode("svg",{width:n,height:n,viewBox:"0 0 32 32"},[Vue.createVNode("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function so(){{const{$pageInstance:e}=Vue.getCurrentInstance();return e&&lo(e.proxy)}}function Ae(e){const t=on(e);if(t.$page)return lo(t);if(!t.$)return;{const{$pageInstance:o}=t.$;if(o)return lo(o.proxy)}const n=t.$.root.proxy;if(n&&n.$page)return lo(n)}function dt(){const e=getCurrentPages(),t=e.length;if(t)return e[t-1]}function xt(){var e;const t=(e=dt())==null?void 0:e.$page;if(t)return t.meta}function ft(){const e=xt();return e?e.id:-1}function Ee(){const e=dt();if(e)return e.$vm}const nm=["navigationBar","pullToRefresh"];function om(){return JSON.parse(JSON.stringify(__uniConfig.globalStyle||{}))}function vs(e,t){const n=om(),o=B({id:t},n,e);nm.forEach(r=>{o[r]=B({},n[r],e[r])});const{navigationBar:i}=o;return i.titleText&&i.titleImage&&(i.titleText=""),o}function im(e){return e.offset&&(e.offset=Je(e.offset)),e.height&&(e.height=Je(e.height)),e.range&&(e.range=Je(e.range)),e}function _s(e,t,n,o,i,r){const{id:a,route:s}=o,l=Zn(o.navigationBar,__uniConfig.themeConfig,r).titleColor;return{id:a,path:qe(s),route:s,fullPath:t,options:n,meta:o,openType:e,eventChannel:i,statusBarStyle:l==="#ffffff"?"light":"dark"}}function lo(e){var t,n;return((t=e.$page)==null?void 0:t.id)||((n=e.$basePage)==null?void 0:n.id)}function rm(e,t,n){const o=e.$[t];K(o)&&n.__weh&&Se(o,n.__weh)}function ie(e,t,n){if(J(e))n=t,t=e,e=Ee();else if(typeof e=="number"){const i=getCurrentPages().find(r=>hs(r).id===e);i?e=i.$vm:e=Ee()}if(!e)return;const o=e.$[t];return o&&Ug(o,n)}function ys(e){e.preventDefault()}let bs,ws=0;function am({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,i=!1,r=!0;const a=()=>{const{scrollHeight:l}=document.documentElement,c=window.innerHeight,u=window.scrollY,d=u>0&&l>c&&u+c+n>=l,f=Math.abs(l-ws)>n;return d&&(!i||f)?(ws=l,i=!0,!0):(!d&&i&&(i=!1),!1)},s=()=>{e&&e(window.pageYOffset);function l(){if(a())return t&&t(),r=!1,setTimeout(function(){r=!0},350),!0}t&&r&&(l()||(bs=setTimeout(l,300))),o=!1};return function(){clearTimeout(bs),o||requestAnimationFrame(s),o=!0}}function sm(e){if(e.indexOf("/")===0||e.indexOf("uni:")===0)return e;let t="";const n=getCurrentPages();return n.length&&(t=hs(n[n.length-1]).route),Mi(t,e)}function Mi(e,t){if(t.indexOf("/")===0)return t;if(t.indexOf("./")===0)return Mi(e,t.slice(2));const n=t.split("/"),o=n.length;let i=0;for(;i<o&&n[i]==="..";i++);n.splice(0,i),t=n.join("/");const r=e.length>0?e.split("/"):[];return r.splice(r.length-i-1,i+1),qe(r.concat(n).join("/"))}function gn(e,t=!1){return t?__uniRoutes.find(n=>n.path===e||n.alias===e):__uniRoutes.find(n=>n.path===e)}function lm(e,t,n){const o=gn(qe(t));if(o){const{meta:r}=o;delete r.tabBarIndex,r.isQuit=r.isTabBar=!1}const i=gn(qe(n));if(i){const{meta:r}=i;r.tabBarIndex=e,r.isQuit=r.isTabBar=!0;const a=__uniConfig.tabBar;a&&a.list&&a.list[e]&&(a.list[e].pagePath=La(n))}}function cm(){Fp(),zg(Ni),__UNI_FEATURE_LONGPRESS__&&Dp()}class um{constructor(t){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=t,this.$el=Qn(t.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(t){if(!this.$el||!t)return;const n=Ts(this.$el.querySelector(t));if(n)return Pi(n,!1)}selectAllComponents(t){if(!this.$el||!t)return[];const n=[],o=this.$el.querySelectorAll(t);for(let i=0;i<o.length;i++){const r=Ts(o[i]);r&&n.push(Pi(r,!1))}return n}forceUpdate(t){t==="class"?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():t==="style"&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:t}=this.$el;t.length&&(this.$el.className=t.join(" "))}updateWxsStyle(){const{__wxsStyle:t}=this.$el;t&&this.$el.setAttribute("style",mg(t))}setStyle(t){return!this.$el||!t?this:(J(t)&&(t=pg(t)),fe(t)&&(this.$el.__wxsStyle=t,this.forceUpdate("style")),this)}addClass(t){if(!this.$el||!t)return this;const n=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return n.indexOf(t)===-1&&(n.push(t),this.forceUpdate("class")),this}removeClass(t){if(!this.$el||!t)return this;const{__wxsAddClass:n}=this.$el;if(n){const i=n.indexOf(t);i>-1&&n.splice(i,1)}const o=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return o.indexOf(t)===-1&&(o.push(t),this.forceUpdate("class")),this}hasClass(t){return this.$el&&this.$el.classList.contains(t)}getDataset(){return this.$el&&this.$el.dataset}callMethod(t,n={}){const o=this.$vm[t];G(o)?o(JSON.parse(JSON.stringify(n))):this.$vm.ownerId&&UniViewJSBridge.publishHandler(Bg,{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:t,args:n})}requestAnimationFrame(t){return window.requestAnimationFrame(t)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(t,n={}){return this.$vm.$emit(t,n),this}getComputedStyle(t){if(this.$el){const n=window.getComputedStyle(this.$el);return t&&t.length?t.reduce((o,i)=>(o[i]=n[i],o),{}):n}return{}}setTimeout(t,n){return window.setTimeout(t,n)}clearTimeout(t){return window.clearTimeout(t)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Pi(e,t=!0){if(t&&e&&(e=Da(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new um(e)),e.$el.__wxsComponentDescriptor}function Oi(e,t){return Pi(e,t)}function dm(e,t,n=!0){if(!t||n&&e.length<2)return!1;const o=Da(t);if(!o)return!1;const i=o.$.type;return!i.$wxs&&!i.$renderjs?!1:o}function Ss(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get(){return Oi(n.proxy,!1)}}));const i=dm(t,n,o);if(i)return[e,Oi(i,!1)]}}function Ts(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}const fm=e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent,hm=e=>e.type==="click",gm=e=>e.type.indexOf("mouse")===0||["contextmenu"].includes(e.type),pm=e=>typeof TouchEvent!="undefined"&&e instanceof TouchEvent||e.type.indexOf("touch")===0||["longpress"].indexOf(e.type)>=0;function mm(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event)||!(o instanceof HTMLElement))return[e];const i=!Ni(o);if(i)return Ss(e,t,n,!1)||[e];const r=Ri(e,i);if(hm(e))ym(r,e);else if(gm(e))bm(r,e);else if(pm(e)){const a=ro();r.touches=Cs(e.touches,a),r.changedTouches=Cs(e.changedTouches,a)}else fm(e)&&["key","code"].forEach(s=>{Object.defineProperty(r,s,{get(){return e[s]}})});return Ss(r,t,n)||[r]}function vm(e){for(;!Ni(e);)e=e.parentElement;return e}function Ri(e,t=!1){const{type:n,timeStamp:o,target:i,currentTarget:r}=e;let a,s;a=wi(t?i:vm(i)),s=wi(r);const l={type:n,timeStamp:o,target:a,detail:{},currentTarget:s};return e._stopped&&(l._stopped=!0),e.type.startsWith("touch")&&(l.touches=e.touches,l.changedTouches=e.changedTouches),_m(l,e),l}function _m(e,t){B(e,{preventDefault(){return t.preventDefault()},stopPropagation(){return t.stopPropagation()}})}function ym(e,t){const{x:n,y:o}=t,i=ro();e.detail={x:n,y:o-i},e.touches=e.changedTouches=[Vs(t,i)]}function bm(e,t){const n=ro();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Vs(t,n)]}function Vs(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function Cs(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:i,pageX:r,pageY:a,clientX:s,clientY:l,force:c}=e[o];n.push({identifier:i,pageX:r,pageY:a-t,clientX:s,clientY:l-t,force:c||0})}return n}const wm=Object.defineProperty({__proto__:null,$nne:mm,createNativeEvent:Ri},Symbol.toStringTag,{value:"Module"});function Sm(e){const t=e.globalProperties;B(t,wm),__UNI_FEATURE_WXS__&&(t.$gcd=Oi)}function Tm(e){Sm(e._context.config)}const Vm=(e,t)=>UniServiceJSBridge.emit("api."+e,t);let As=1;function Es(e){return(e||ft())+"."+It}const Cm=B(ts("view"),{invokeOnCallback:Vm,invokeViewMethod:(e,t,n,o)=>{const{subscribe:i,publishHandler:r}=UniServiceJSBridge,a=o?As++:0;o&&i(It+"."+a,o,!0),r(Es(n),{id:a,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:i,unsubscribe:r,publishHandler:a}=UniServiceJSBridge,s=As++,l=It+"."+s;return i(l,n),a(Es(o),{id:s,name:e,args:t},o),()=>{r(l)}}});function Am(){const{on:e}=UniServiceJSBridge;e(Xn,Em),e(Ma,km),e(Pa,Im)}function Em(e){const t=dt();ie(t,Xn,e),UniServiceJSBridge.invokeOnCallback("onWindowResize",e)}function km(e){const t=dt();ie(getApp(),lt,e),ie(t,lt)}function Im(){ie(getApp(),ct),ie(dt(),ct)}const Nm=[Jn,vi];function xm(){Nm.forEach(e=>UniServiceJSBridge.subscribe(e,Mm(e)))}function Mm(e){return(t,n)=>{ie(parseInt(n),e,t)}}function Pm(){Am(),xm()}function Om(e){e.$vm=e,e.$mpType="app";const t=Vue.ref(D().getLocale());Object.defineProperty(e,"$locale",{get(){return t.value},set(n){t.value=n}})}function Rm(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",e.$fontFamilySet=new Set,t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}function Lm(e,t){const n=e.$el.querySelector(t);return n&&n.__vue__}function Bm(e,t){const n=e.$el.querySelectorAll(t);return n?[...n].map(o=>o.__vue__).filter(Boolean):[]}function Dm(){return uni.createSelectorQuery().in(this)}function Um(){return uni.createMediaQueryObserver(this)}function Fm(e){return uni.createIntersectionObserver(this,e)}function $m(e){return Lm(this,e)}function zm(e){return Bm(this,e)}const Wm=Object.defineProperty({__proto__:null,createIntersectionObserver:Fm,createMediaQueryObserver:Um,createSelectorQuery:Dm,selectAllComponents:zm,selectComponent:$m},Symbol.toStringTag,{value:"Module"});function Hm(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new Wa(this.$page.id)),e.eventChannel}}function qm(e){const t=e.globalProperties;t.getOpenerEventChannel=Hm,__UNI_FEATURE_WX__&&B(t,Wm)}function Gm(e){qm(e._context.config)}function ks(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function jm(e,t){const n=e.$options||{};n.globalData=B(n.globalData||{},t),Object.defineProperty(e,"globalData",{get(){return n.globalData},set(o){n.globalData=o}})}function Is(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,(t,n)=>`${uni.upx2px(parseFloat(n))}px`):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function Ym(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`).replace("webkit","-webkit")}function Xm(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],i=["width","height","left","right","top","bottom"],r=e.animates,a=e.option,s=a.transition,l={},c=[];return r.forEach(u=>{let d=u.type,f=[...u.args];if(t.concat(n).includes(d))d.startsWith("rotate")||d.startsWith("skew")?f=f.map(h=>parseFloat(h)+"deg"):d.startsWith("translate")&&(f=f.map(Is)),n.indexOf(d)>=0&&(f.length=1),c.push(`${d}(${f.join(",")})`);else if(o.concat(i).includes(f[0])){d=f[0];const h=f[1];l[d]=i.includes(d)?Is(h):h}}),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map(u=>`${Ym(u)} ${s.duration}ms ${s.timingFunction} ${s.delay}ms`).join(","),l.transformOrigin=l.webkitTransformOrigin=a.transformOrigin,l}function Ns(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,i=t.actions.length;function r(){const a=o[n],s=a.option.transition,l=Xm(a);Object.keys(l).forEach(c=>{e.$el.style[c]=l[c]}),n+=1,n<i&&setTimeout(r,s.duration+s.delay)}setTimeout(()=>{r()},0)}const Jm={props:["animation"],watch:{animation:{deep:!0,handler(){Ns(this)}}},mounted(){Ns(this)}},X=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return(!t||!t.animation)&&(n||(e.mixins=[])).push(Jm),ge(e)},ge=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Vue.defineComponent(e)),Mt=e=>X({name:gi(wa(e)),setup(){return()=>(Vue.openBlock(),Vue.createElementBlock("uni-"+e,null,e+" is unsupported"))}});function ne(e){return e.__wwe=!0,e}function ve(e,t){return(n,o,i)=>{e.value&&t(n,Qm(n,o,e.value,i||{}))}}function Km(e){return(t,n)=>{e(t,Ri(n))}}function Qm(e,t,n,o){let i;return i=wi(n),{type:o.type||e,timeStamp:t.timeStamp||0,target:i,currentTarget:i,detail:o}}const Zm={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function Li(e){const t=Vue.ref(!1);let n=!1,o,i;function r(){requestAnimationFrame(()=>{clearTimeout(i),i=setTimeout(()=>{t.value=!1},parseInt(e.hoverStayTime))})}function a(_){_.touches.length>1||l(_)}function s(_){n||(l(_),window.addEventListener("mouseup",f))}function l(_){_._hoverPropagationStopped||!e.hoverClass||e.hoverClass==="none"||e.disabled||(e.hoverStopPropagation&&(_._hoverPropagationStopped=!0),n=!0,o=setTimeout(()=>{t.value=!0,n||r()},parseInt(e.hoverStartTime)))}function c(){d()}function u(){n&&f()}function d(){n=!1,t.value&&r()}function f(){d(),window.removeEventListener("mouseup",f)}function h(){n=!1,t.value=!1,clearTimeout(o)}return{hovering:t,binding:{onTouchstartPassive:ne(a),onMousedown:ne(s),onTouchend:ne(c),onMouseup:ne(u),onTouchcancel:ne(h)}}}function ht(e,t){return J(t)&&(t=[t]),t.reduce((n,o)=>(e[o]&&(n[o]=!0),n),Object.create(null))}const Fe=dn("uf"),e0=X({name:"Form",emits:["submit","reset"],setup(e,{slots:t,emit:n}){const o=Vue.ref(null);return t0(ve(o,n)),()=>Vue.createVNode("uni-form",{ref:o},[Vue.createVNode("span",null,[t.default&&t.default()])],512)}});function t0(e){const t=[];return Vue.provide(Fe,{addField(n){t.push(n)},removeField(n){t.splice(t.indexOf(n),1)},submit(n){e("submit",n,{value:t.reduce((o,i)=>{if(i.submit){const[r,a]=i.submit();r&&(o[r]=a)}return o},Object.create(null))})},reset(n){t.forEach(o=>o.reset&&o.reset()),e("reset",n)}}),t}const n0={for:{type:String,default:""}},pn=dn("ul");function o0(){const e=[];return Vue.provide(pn,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}const i0=X({name:"Label",props:n0,setup(e,{slots:t}){const n=Vue.ref(null),o=so(),i=o0(),r=Vue.computed(()=>e.for||t.default&&t.default.length),a=ne(s=>{const l=s.target;let c=/^uni-(checkbox|radio|switch)-/.test(l.className);c||(c=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(l.tagName)),!c&&(e.for?UniViewJSBridge.emit("uni-label-click-"+o+"-"+e.for,s,!0):i.length&&i[0](s,!0))});return()=>Vue.createVNode("uni-label",{ref:n,class:{"uni-label-pointer":r},onClick:a},[t.default&&t.default()],10,["onClick"])}});function co(e,t){xs(e.id,t),Vue.watch(()=>e.id,(n,o)=>{Ms(o,t,!0),xs(n,t,!0)}),Vue.onUnmounted(()=>{Ms(e.id,t)})}function xs(e,t,n){const o=so();n&&!e||fe(t)&&Object.keys(t).forEach(i=>{n?i.indexOf("@")!==0&&i.indexOf("uni-")!==0&&UniViewJSBridge.on(`uni-${i}-${o}-${e}`,t[i]):i.indexOf("uni-")===0?UniViewJSBridge.on(i,t[i]):e&&UniViewJSBridge.on(`uni-${i}-${o}-${e}`,t[i])})}function Ms(e,t,n){const o=so();n&&!e||fe(t)&&Object.keys(t).forEach(i=>{n?i.indexOf("@")!==0&&i.indexOf("uni-")!==0&&UniViewJSBridge.off(`uni-${i}-${o}-${e}`,t[i]):i.indexOf("uni-")===0?UniViewJSBridge.off(i,t[i]):e&&UniViewJSBridge.off(`uni-${i}-${o}-${e}`,t[i])})}const r0=X({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.inject(Fe,!1),{hovering:i,binding:r}=Li(e),a=ne((l,c)=>{if(e.disabled)return l.stopImmediatePropagation();c&&n.value.click();const u=e.formType;if(u){if(!o)return;u==="submit"?o.submit(l):u==="reset"&&o.reset(l);return}}),s=Vue.inject(pn,!1);return s&&(s.addHandler(a),Vue.onBeforeUnmount(()=>{s.removeHandler(a)})),co(e,{"label-click":a}),()=>{const l=e.hoverClass,c=ht(e,"disabled"),u=ht(e,"loading"),d=ht(e,"plain"),f=l&&l!=="none";return Vue.createVNode("uni-button",Vue.mergeProps({ref:n,onClick:a,id:e.id,class:f&&i.value?l:""},f&&r,c,u,d),[t.default&&t.default()],16,["onClick","id"])}}}),a0="",Ps=dn("upm");function Pt(){return Vue.inject(Ps)}function s0(e){const t=l0(e);return Vue.provide(Ps,t),t}function mn(){if(__UNI_FEATURE_PAGES__)return VueRouter.useRoute();const e=location.href,t=e.indexOf("?"),n=e.indexOf("#",t>-1?t:0);let o={};t>-1&&(o=za(e.slice(t+1,n>-1?n:e.length)));const{meta:i}=__uniRoutes[0],r=qe(i.route);return{meta:i,query:o,path:r,matched:[{path:r}]}}function l0(e){return __UNI_FEATURE_PAGES__?Vue.reactive(Os(JSON.parse(JSON.stringify(vs(VueRouter.useRoute().meta,e))))):Vue.reactive(Os(JSON.parse(JSON.stringify(vs(__uniRoutes[0].meta,e)))))}function Os(e){if(__UNI_FEATURE_PULL_DOWN_REFRESH__){const{enablePullDownRefresh:t,navigationBar:n}=e;if(t){const o=im(B({support:!0,color:"#2BD009",style:"circle",height:70,range:150,offset:0},e.pullToRefresh)),{type:i,style:r}=n;r!=="custom"&&i!=="transparent"&&(o.offset+=Sa+he.top),e.pullToRefresh=o}}if(__UNI_FEATURE_NAVIGATIONBAR__||__UNI_FEATURE_I18N_LOCALE__){const{navigationBar:t}=e,{titleSize:n,titleColor:o,backgroundColor:i}=t;t.titleText=t.titleText||"",t.type=t.type||"default",t.titleSize=n||"16px",t.titleColor=o||"#000000",t.backgroundColor=i||"#F8F8F8",__UNI_FEATURE_I18N_LOCALE__&&Ap(t)}if(__UNI_FEATURE_PAGES__&&history.state){const t=history.state.__type__;(t==="redirectTo"||t==="reLaunch")&&getCurrentPages().length===0&&(e.isEntry=!0,e.isQuit=!0)}return e}function c0(e){const t=window.screen,n=document.documentElement,o=[window.outerWidth,window.outerHeight,t.width,t.height,n.clientWidth,n.clientHeight];return Math.max.apply(null,o)>e}function Bi(){return history.state&&history.state.__id__||1}var Ot="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",uo=function(){const e=new Uint8Array(256);for(var t=0;t<Ot.length;t++)e[Ot.charCodeAt(t)]=t;return e}();function u0(e){var t=new Uint8Array(e),n,o=t.length,i="";for(n=0;n<o;n+=3)i+=Ot[t[n]>>2],i+=Ot[(t[n]&3)<<4|t[n+1]>>4],i+=Ot[(t[n+1]&15)<<2|t[n+2]>>6],i+=Ot[t[n+2]&63];return o%3===2?i=i.substring(0,i.length-1)+"=":o%3===1&&(i=i.substring(0,i.length-2)+"=="),i}function d0(e){var t=e.length*.75,n=e.length,o,i=0,r,a,s,l;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);var c=new ArrayBuffer(t),u=new Uint8Array(c);for(o=0;o<n;o+=4)r=uo[e.charCodeAt(o)],a=uo[e.charCodeAt(o+1)],s=uo[e.charCodeAt(o+2)],l=uo[e.charCodeAt(o+3)],u[i++]=r<<2|a>>4,u[i++]=(a&15)<<4|s>>2,u[i++]=(s&3)<<6|l&63;return c}const f0=["original","compressed"],Di=["album","camera"],fo=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function ho(e,t){return!e||t.indexOf(e)===-1?t[0]:e}function go(e,t){return!K(e)||e.length===0||e.find(n=>t.indexOf(n)===-1)?t:e}function h0(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let Rs=1;const $e={};function Ls(e,t,n,o=!1){return $e[e]={name:t,keepAlive:o,callback:n},e}function Bs(e,t,n){if(typeof e=="number"){const o=$e[e];if(o)return o.keepAlive||delete $e[e],o.callback(t,n)}return t}function Ds(e){for(const t in $e)if($e[t].name===e)return!0;return!1}function g0(e,t){for(const n in $e){const o=$e[n];o.callback===t&&o.name===e&&delete $e[n]}}function p0(e){UniServiceJSBridge.off("api."+e)}function m0(e){UniServiceJSBridge.on("api."+e,t=>{for(const n in $e){const o=$e[n];o.name===e&&o.callback(t)}})}function v0(e,t){return Ls(Rs++,e,t,!0)}const _0="success",y0="fail",b0="complete";function w0(e){const t={};for(const n in e){const o=e[n];G(o)&&(t[n]=h0(o),delete e[n])}return t}function S0(e,t){return!e||e.indexOf(":fail")===-1?t+":ok":t+e.substring(e.indexOf(":fail"))}function T0(e,t={},{beforeAll:n,beforeSuccess:o}={}){fe(t)||(t={});const{success:i,fail:r,complete:a}=w0(t),s=G(i),l=G(r),c=G(a),u=Rs++;return Ls(u,e,d=>{d=d||{},d.errMsg=S0(d.errMsg,e),G(n)&&n(d),d.errMsg===e+":ok"?(G(o)&&o(d,t),s&&i(d)):l&&r(d),c&&a(d)}),u}const V0="success",C0="fail",A0="complete",Rt={},Lt={};function E0(e,t){return function(n){return e(n,t)||n}}function Us(e,t,n){let o=!1;for(let i=0;i<e.length;i++){const r=e[i];if(o)o=Promise.resolve(E0(r,n));else{const a=r(t,n);if(sg(a)&&(o=Promise.resolve(a)),a===!1)return{then(){},catch(){}}}}return o||{then(i){return i(t)},catch(){}}}function Fs(e,t={}){return[V0,C0,A0].forEach(n=>{const o=e[n];if(!K(o))return;const i=t[n];t[n]=function(a){Us(o,a,t).then(s=>G(i)&&i(s)||s)}}),t}function $s(e,t){const n=[];K(Rt.returnValue)&&n.push(...Rt.returnValue);const o=Lt[e];return o&&K(o.returnValue)&&n.push(...o.returnValue),n.forEach(i=>{t=i(t)||t}),t}function zs(e){const t=Object.create(null);Object.keys(Rt).forEach(o=>{o!=="returnValue"&&(t[o]=Rt[o].slice())});const n=Lt[e];return n&&Object.keys(n).forEach(o=>{o!=="returnValue"&&(t[o]=(t[o]||[]).concat(n[o]))}),t}function Ws(e,t,n,o){const i=zs(e);return i&&Object.keys(i).length?K(i.invoke)?Us(i.invoke,n).then(a=>t(Fs(zs(e),a),...o)):t(Fs(i,n),...o):t(n,...o)}function k0(e){return!!(fe(e)&&[_0,y0,b0].find(t=>G(e[t])))}function jC(e){return e}function Hs(e,t){return(n={},...o)=>k0(n)?$s(e,Ws(e,t,n,o)):$s(e,new Promise((i,r)=>{Ws(e,t,B(n,{success:i,fail:r}),o)}))}function I0(e,t){const n=e[0];if(!t||!t.formatArgs||!fe(t.formatArgs)&&fe(n))return;const o=t.formatArgs,i=Object.keys(o);for(let r=0;r<i.length;r++){const a=i[r],s=o[a];if(G(s)){const l=s(e[0][a],n);if(J(l))return l}else le(n,a)||(n[a]=s)}}function N0(e,t,n){const o={errMsg:t+":ok"};return Bs(e,B(n||{},o))}function qs(e,t,n,o={}){const i=t+":fail";let r="";n?n.indexOf(i)===0?r=n:r=i+" "+n:r=i,delete o.errCode;let a=B({errMsg:r},o);return Bs(e,a)}function po(e,t,n,o){if(o&&o.beforeInvoke){const r=o.beforeInvoke(t);if(J(r))return r}const i=I0(t,o);if(i)return i}function Gs(e){if(!G(e))throw new Error('Invalid args: type check failed for args "callback". Expected Function')}function x0(e,t,n){return o=>{Gs(o);const i=po(e,[o],void 0,n);if(i)throw new Error(i);const r=!Ds(e);v0(e,o),r&&(m0(e),t())}}function M0(e,t,n){return o=>{Gs(o);const i=po(e,[o],void 0,n);if(i)throw new Error(i);e=e.replace("off","on"),g0(e,o),Ds(e)||(p0(e),t())}}function P0(e){return!e||J(e)?e:e.stack?((typeof globalThis=="undefined"||!globalThis.harmonyChannel)&&console.error(e.message+`
`+e.stack),e.message):e}function js(e,t,n,o){return i=>{const r=T0(e,i,o),a=po(e,[i],n,o);return a?qs(r,e,a):t(i,{resolve:s=>N0(r,e,s),reject:(s,l)=>qs(r,e,P0(s),l)})}}function O0(e,t,n,o){return(...i)=>{const r=po(e,i,n,o);if(r)throw new Error(r);return t.apply(null,i)}}function R0(e,t,n,o){return js(e,t,n,o)}function ke(e,t,n){return x0(e,t,n)}function gt(e,t,n){return M0(e,t,n)}function mo(e,t,n,o){return Hs(e,js(e,t,void 0,o))}function j(e,t,n,o){return O0(e,t,void 0,o)}function R(e,t,n,o){return Hs(e,R0(e,t,void 0,o))}function Ys(e){return`method 'uni.${e}' not supported`}function vo(e){return()=>{console.error(Ys(e))}}const Ui=vo;function be(e){return(t,{reject:n})=>n(Ys(e))}const L0="base64ToArrayBuffer",B0="arrayBufferToBase64",Xs=j(L0,e=>d0(e)),Js=j(B0,e=>u0(e)),D0="upx2px",U0=1e-4,F0=750;let Ks=!1,Fi=0,Qs=0,Zs=960,el=375,tl=750;function $0(){const{windowWidth:e,pixelRatio:t,platform:n}=Uc();Fi=e,Qs=t,Ks=n==="ios"}function $i(e,t){const n=Number(e);return isNaN(n)?t:n}function z0(){const e=__uniConfig.globalStyle||{};Zs=$i(e.rpxCalcMaxDeviceWidth,960),el=$i(e.rpxCalcBaseDeviceWidth,375),tl=$i(e.rpxCalcBaseDeviceWidth,750)}const _o=j(D0,(e,t)=>{if(Fi===0&&($0(),z0()),e=Number(e),e===0)return 0;let n=t||Fi;n=e===tl||n<=Zs?n:el;let o=e/F0*n;return o<0&&(o=-o),o=Math.floor(o+U0),o===0&&(Qs===1||!Ks?o=1:o=.5),e<0?-o:o}),W0="addInterceptor",H0="removeInterceptor";function nl(e,t){Object.keys(t).forEach(n=>{G(t[n])&&(e[n]=q0(e[n],t[n]))})}function ol(e,t){!e||!t||Object.keys(t).forEach(n=>{const o=e[n],i=t[n];K(o)&&G(i)&&Se(o,i)})}function q0(e,t){const n=t?e?e.concat(t):K(t)?t:[t]:e;return n&&G0(n)}function G0(e){const t=[];for(let n=0;n<e.length;n++)t.indexOf(e[n])===-1&&t.push(e[n]);return t}const il=j(W0,(e,t)=>{J(e)&&fe(t)?nl(Lt[e]||(Lt[e]={}),t):fe(e)&&nl(Rt,e)}),rl=j(H0,(e,t)=>{J(e)?fe(t)?ol(Lt[e],t):delete Lt[e]:fe(e)&&ol(Rt,e)}),al={},j0="$on",Y0="$once",X0="$off",J0="$emit";class K0{constructor(){this.$emitter=new Ya}on(t,n){return this.$emitter.on(t,n)}once(t,n){return this.$emitter.once(t,n)}off(t,n){if(!t){this.$emitter.e={};return}this.$emitter.off(t,n)}emit(t,...n){this.$emitter.emit(t,...n)}}const Bt=new K0,sl=j(j0,(e,t)=>(Bt.on(e,t),()=>Bt.off(e,t))),ll=j(Y0,(e,t)=>(Bt.once(e,t),()=>Bt.off(e,t))),cl=j(X0,(e,t)=>{K(e)||(e=e?[e]:[]),e.forEach(n=>{Bt.off(n,t)})}),ul=j(J0,(e,...t)=>{Bt.emit(e,...t)});function dl(e,t,...n){t&&n.push(t),console[e].apply(console,n)}const Q0="createVideoContext",Z0="createMapContext",ev="createCanvasContext",tv="createInnerAudioContext",nv=[.5,.8,1,1.25,1.5,2];class zi{constructor(t,n){this.id=t,this.pageId=n}play(){ze(this.id,this.pageId,"play")}pause(){ze(this.id,this.pageId,"pause")}stop(){ze(this.id,this.pageId,"stop")}seek(t){ze(this.id,this.pageId,"seek",{position:t})}sendDanmu(t){ze(this.id,this.pageId,"sendDanmu",t)}playbackRate(t){~nv.indexOf(t)||(t=1),ze(this.id,this.pageId,"playbackRate",{rate:t})}requestFullScreen(t={}){ze(this.id,this.pageId,"requestFullScreen",t)}exitFullScreen(){ze(this.id,this.pageId,"exitFullScreen")}showStatusBar(){ze(this.id,this.pageId,"showStatusBar")}hideStatusBar(){ze(this.id,this.pageId,"hideStatusBar")}}const fl=j(Q0,(e,t)=>t?new zi(e,Ae(t)):new zi(e,Ae(Ee()))),ov=(e,t)=>{const n=t.errMsg||"";new RegExp("\\:\\s*fail").test(n)?e.fail&&e.fail(t):e.success&&e.success(t),e.complete&&e.complete(t)},pe=(e,t,n,o)=>{Eb(e,t,n,o,i=>{o&&ov(o,i)})};class Wi{constructor(t,n){this.id=t,this.pageId=n}getCenterLocation(t){pe(this.id,this.pageId,"getCenterLocation",t)}moveToLocation(t){pe(this.id,this.pageId,"moveToLocation",t)}getScale(t){pe(this.id,this.pageId,"getScale",t)}getRegion(t){pe(this.id,this.pageId,"getRegion",t)}includePoints(t){pe(this.id,this.pageId,"includePoints",t)}translateMarker(t){pe(this.id,this.pageId,"translateMarker",t)}$getAppMap(){}addCustomLayer(t){pe(this.id,this.pageId,"addCustomLayer",t)}removeCustomLayer(t){pe(this.id,this.pageId,"removeCustomLayer",t)}addGroundOverlay(t){pe(this.id,this.pageId,"addGroundOverlay",t)}removeGroundOverlay(t){pe(this.id,this.pageId,"removeGroundOverlay",t)}updateGroundOverlay(t){pe(this.id,this.pageId,"updateGroundOverlay",t)}initMarkerCluster(t){pe(this.id,this.pageId,"initMarkerCluster",t)}addMarkers(t){pe(this.id,this.pageId,"addMarkers",t)}removeMarkers(t){pe(this.id,this.pageId,"removeMarkers",t)}moveAlong(t){pe(this.id,this.pageId,"moveAlong",t)}setLocMarkerIcon(t){pe(this.id,this.pageId,"setLocMarkerIcon",t)}openMapApp(t){pe(this.id,this.pageId,"openMapApp",t)}on(t,n){pe(this.id,this.pageId,"on",{name:t,callback:n})}}const hl=j(Z0,(e,t)=>t?new Wi(e,Ae(t)):new Wi(e,Ae(Ee())));function Ke(e,t){return function(n,o){n?o[e]=Math.round(n):typeof t!="undefined"&&(o[e]=t)}}const gl=Ke("width"),pl=Ke("height"),iv="canvasGetImageData",ml={formatArgs:{x:Ke("x"),y:Ke("y"),width:gl,height:pl}},vl={canvasId:{type:String,required:!0},x:{type:Number,required:!0},y:{type:Number,required:!0},width:{type:Number,required:!0},height:{type:Number,required:!0}},rv="canvasPutImageData",av=ml,sv=B({data:{type:Uint8ClampedArray,required:!0}},vl,{height:{type:Number}}),_l={PNG:"png",JPG:"jpg",JPEG:"jpg"},lv="canvasToTempFilePath",cv={formatArgs:{x:Ke("x",0),y:Ke("y",0),width:gl,height:pl,destWidth:Ke("destWidth"),destHeight:Ke("destHeight"),fileType(e,t){e=(e||"").toUpperCase();let n=_l[e];n||(n=_l.PNG),t.fileType=n},quality(e,t){t.quality=e&&e>0&&e<1?e:1}}},uv={};function yo(e,t,n,o,i){UniServiceJSBridge.invokeViewMethod(`canvas.${e}`,{type:n,data:o},t,r=>{i&&i(r)})}var dv=["scale","rotate","translate","setTransform","transform"],fv=["drawImage","fillText","fill","stroke","fillRect","strokeRect","clearRect","strokeText"],hv=["setFillStyle","setTextAlign","setStrokeStyle","setGlobalAlpha","setShadow","setFontSize","setLineCap","setLineJoin","setLineWidth","setMiterLimit","setTextBaseline","setLineDash"];function gv(e,t){const o=document.createElement("canvas").getContext("2d");return o.font=t,o.measureText(e).width||0}const yl={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32",transparent:"#00000000"};function Hi(e){e=e||"#000000";let t=null;if((t=/^#([0-9|A-F|a-f]{6})$/.exec(e))!=null){const o=parseInt(t[1].slice(0,2),16),i=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4),16);return[o,i,r,255]}if((t=/^#([0-9|A-F|a-f]{3})$/.exec(e))!=null){let o=t[1].slice(0,1),i=t[1].slice(1,2),r=t[1].slice(2,3);return o=parseInt(o+o,16),i=parseInt(i+i,16),r=parseInt(r+r,16),[o,i,r,255]}if((t=/^rgb\((.+)\)$/.exec(e))!=null)return t[1].split(",").map(function(o){return Math.min(255,parseInt(o.trim()))}).concat(255);if((t=/^rgba\((.+)\)$/.exec(e))!=null)return t[1].split(",").map(function(o,i){return i===3?Math.floor(255*parseFloat(o.trim())):Math.min(255,parseInt(o.trim()))});var n=e.toLowerCase();if(le(yl,n)){t=/^#([0-9|A-F|a-f]{6,8})$/.exec(yl[n]);const o=parseInt(t[1].slice(0,2),16),i=parseInt(t[1].slice(2,4),16),r=parseInt(t[1].slice(4,6),16);let a=parseInt(t[1].slice(6,8),16);return a=a>=0?a:255,[o,i,r,a]}return console.error("unsupported color:"+e),[0,0,0,255]}class bl{constructor(t,n){this.type=t,this.data=n,this.colorStop=[]}addColorStop(t,n){this.colorStop.push([t,Hi(n)])}}class pv{constructor(t,n){this.type="pattern",this.data=t,this.colorStop=n}}class mv{constructor(t){this.width=t}}const vv=()=>a0;class vn{constructor(t,n){this.id=t,this.pageId=n,this.actions=[],this.path=[],this.subpath=[],this.drawingState=[],this.state={lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}setFillStyle(t){console.log("initCanvasContextProperty implemented.")}setStrokeStyle(t){console.log("initCanvasContextProperty implemented.")}setShadow(t,n,o,i){console.log("initCanvasContextProperty implemented.")}addColorStop(t,n){console.log("initCanvasContextProperty implemented.")}setLineWidth(t){console.log("initCanvasContextProperty implemented.")}setLineCap(t){console.log("initCanvasContextProperty implemented.")}setLineJoin(t){console.log("initCanvasContextProperty implemented.")}setLineDash(t,n){console.log("initCanvasContextProperty implemented.")}setMiterLimit(t){console.log("initCanvasContextProperty implemented.")}fillRect(t,n,o,i){console.log("initCanvasContextProperty implemented.")}strokeRect(t,n,o,i){console.log("initCanvasContextProperty implemented.")}clearRect(t,n,o,i){console.log("initCanvasContextProperty implemented.")}fill(){console.log("initCanvasContextProperty implemented.")}stroke(){console.log("initCanvasContextProperty implemented.")}scale(t,n){console.log("initCanvasContextProperty implemented.")}rotate(t){console.log("initCanvasContextProperty implemented.")}translate(t,n){console.log("initCanvasContextProperty implemented.")}setFontSize(t){console.log("initCanvasContextProperty implemented.")}fillText(t,n,o,i){console.log("initCanvasContextProperty implemented.")}setTextAlign(t){console.log("initCanvasContextProperty implemented.")}setTextBaseline(t){console.log("initCanvasContextProperty implemented.")}drawImage(t,n,o,i,r,a,s,l,c){console.log("initCanvasContextProperty implemented.")}setGlobalAlpha(t){console.log("initCanvasContextProperty implemented.")}strokeText(t,n,o,i){console.log("initCanvasContextProperty implemented.")}setTransform(t,n,o,i,r,a){console.log("initCanvasContextProperty implemented.")}draw(t=!1,n){var o=[...this.actions];this.actions=[],this.path=[],yo(this.id,this.pageId,"actionsChanged",{actions:o,reserve:t},n)}createLinearGradient(t,n,o,i){return new bl("linear",[t,n,o,i])}createCircularGradient(t,n,o){return new bl("radial",[t,n,o])}createPattern(t,n){if(n===void 0)console.error("Failed to execute 'createPattern' on 'CanvasContext': 2 arguments required, but only 1 present.");else if(["repeat","repeat-x","repeat-y","no-repeat"].indexOf(n)<0)console.error("Failed to execute 'createPattern' on 'CanvasContext': The provided type ('"+n+"') is not one of 'repeat', 'no-repeat', 'repeat-x', or 'repeat-y'.");else return new pv(t,n)}measureText(t,n){const o=this.state.font;let i=0;return i=gv(t,o),new mv(i)}save(){this.actions.push({method:"save",data:[]}),this.drawingState.push(this.state)}restore(){this.actions.push({method:"restore",data:[]}),this.state=this.drawingState.pop()||{lineDash:[0,0],shadowOffsetX:0,shadowOffsetY:0,shadowBlur:0,shadowColor:[0,0,0,0],font:"10px sans-serif",fontSize:10,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif"}}beginPath(){this.path=[],this.subpath=[],this.path.push({method:"beginPath",data:[]})}moveTo(t,n){this.path.push({method:"moveTo",data:[t,n]}),this.subpath=[[t,n]]}lineTo(t,n){this.path.length===0&&this.subpath.length===0?this.path.push({method:"moveTo",data:[t,n]}):this.path.push({method:"lineTo",data:[t,n]}),this.subpath.push([t,n])}quadraticCurveTo(t,n,o,i){this.path.push({method:"quadraticCurveTo",data:[t,n,o,i]}),this.subpath.push([o,i])}bezierCurveTo(t,n,o,i,r,a){this.path.push({method:"bezierCurveTo",data:[t,n,o,i,r,a]}),this.subpath.push([r,a])}arc(t,n,o,i,r,a=!1){this.path.push({method:"arc",data:[t,n,o,i,r,a]}),this.subpath.push([t,n])}rect(t,n,o,i){this.path.push({method:"rect",data:[t,n,o,i]}),this.subpath=[[t,n]]}arcTo(t,n,o,i,r){this.path.push({method:"arcTo",data:[t,n,o,i,r]}),this.subpath.push([o,i])}clip(){this.actions.push({method:"clip",data:[...this.path]})}closePath(){this.path.push({method:"closePath",data:[]}),this.subpath.length&&(this.subpath=[this.subpath.shift()])}clearActions(){this.actions=[],this.path=[],this.subpath=[]}getActions(){var t=[...this.actions];return this.clearActions(),t}set lineDashOffset(t){this.actions.push({method:"setLineDashOffset",data:[t]})}set globalCompositeOperation(t){this.actions.push({method:"setGlobalCompositeOperation",data:[t]})}set shadowBlur(t){this.actions.push({method:"setShadowBlur",data:[t]})}set shadowColor(t){this.actions.push({method:"setShadowColor",data:[t]})}set shadowOffsetX(t){this.actions.push({method:"setShadowOffsetX",data:[t]})}set shadowOffsetY(t){this.actions.push({method:"setShadowOffsetY",data:[t]})}set font(t){var n=this;this.state.font=t;var o=t.match(/^(([\w\-]+\s)*)(\d+r?px)(\/(\d+\.?\d*(r?px)?))?\s+(.*)/);if(o){var i=o[1].trim().split(/\s/),r=parseFloat(o[3]),a=o[7],s=[];i.forEach(function(c,u){["italic","oblique","normal"].indexOf(c)>-1?(s.push({method:"setFontStyle",data:[c]}),n.state.fontStyle=c):["bold","normal"].indexOf(c)>-1?(s.push({method:"setFontWeight",data:[c]}),n.state.fontWeight=c):u===0?(s.push({method:"setFontStyle",data:["normal"]}),n.state.fontStyle="normal"):u===1&&l()}),i.length===1&&l(),i=s.map(function(c){return c.data[0]}).join(" "),this.state.fontSize=r,this.state.fontFamily=a,this.actions.push({method:"setFont",data:[`${i} ${r}px ${a}`]})}else console.warn("Failed to set 'font' on 'CanvasContext': invalid format.");function l(){s.push({method:"setFontWeight",data:["normal"]}),n.state.fontWeight="normal"}}get font(){return this.state.font}set fillStyle(t){this.setFillStyle(t)}set strokeStyle(t){this.setStrokeStyle(t)}set globalAlpha(t){t=Math.floor(255*parseFloat(t)),this.actions.push({method:"setGlobalAlpha",data:[t]})}set textAlign(t){this.actions.push({method:"setTextAlign",data:[t]})}set lineCap(t){this.actions.push({method:"setLineCap",data:[t]})}set lineJoin(t){this.actions.push({method:"setLineJoin",data:[t]})}set lineWidth(t){this.actions.push({method:"setLineWidth",data:[t]})}set miterLimit(t){this.actions.push({method:"setMiterLimit",data:[t]})}set textBaseline(t){this.actions.push({method:"setTextBaseline",data:[t]})}}const _v=te(()=>{[...dv,...fv].forEach(function(e){function t(n){switch(n){case"fill":case"stroke":return function(){this.actions.push({method:n+"Path",data:[...this.path]})};case"fillRect":return function(o,i,r,a){this.actions.push({method:"fillPath",data:[{method:"rect",data:[o,i,r,a]}]})};case"strokeRect":return function(o,i,r,a){this.actions.push({method:"strokePath",data:[{method:"rect",data:[o,i,r,a]}]})};case"fillText":case"strokeText":return function(o,i,r,a){var s=[o.toString(),i,r];typeof a=="number"&&s.push(a),this.actions.push({method:n,data:s})};case"drawImage":return function(o,i,r,a,s,l,c,u,d){d===void 0&&(l=i,c=r,u=a,d=s,i=void 0,r=void 0,a=void 0,s=void 0);var f;function h(_){return typeof _=="number"}f=h(i)&&h(r)&&h(a)&&h(s)?[o,l,c,u,d,i,r,a,s]:h(u)&&h(d)?[o,l,c,u,d]:[o,l,c],this.actions.push({method:n,data:f})};default:return function(...o){this.actions.push({method:n,data:o})}}}vn.prototype[e]=t(e)}),hv.forEach(function(e){function t(n){switch(n){case"setFillStyle":case"setStrokeStyle":return function(o){typeof o!="object"?this.actions.push({method:n,data:["normal",Hi(o)]}):this.actions.push({method:n,data:[o.type,o.data,o.colorStop]})};case"setGlobalAlpha":return function(o){o=Math.floor(255*parseFloat(o)),this.actions.push({method:n,data:[o]})};case"setShadow":return function(o,i,r,a){a=Hi(a),this.actions.push({method:n,data:[o,i,r,a]}),this.state.shadowBlur=r,this.state.shadowColor=a,this.state.shadowOffsetX=o,this.state.shadowOffsetY=i};case"setLineDash":return function(o,i){o=o||[0,0],i=i||0,this.actions.push({method:n,data:[o,i]}),this.state.lineDash=o};case"setFontSize":return function(o){this.state.font=this.state.font.replace(/\d+\.?\d*px/,o+"px"),this.state.fontSize=o,this.actions.push({method:n,data:[o]})};default:return function(...o){this.actions.push({method:n,data:o})}}}vn.prototype[e]=t(e)})}),wl=j(ev,(e,t)=>{if(_v(),t)return new vn(e,Ae(t));const n=Ae(Ee());if(n)return new vn(e,n);UniServiceJSBridge.emit(ut,"createCanvasContext:fail")}),Sl=R(iv,({canvasId:e,x:t,y:n,width:o,height:i},{resolve:r,reject:a})=>{const s=Ae(Ee());if(!s){a();return}function l(c){if(c.errMsg&&c.errMsg.indexOf("fail")!==-1){a("",c);return}let u=c.data;u&&u.length&&(c.data=new Uint8ClampedArray(u)),delete c.compressed,r(c)}yo(e,s,"getImageData",{x:t,y:n,width:o,height:i},l)},vl,ml),Tl=R(rv,({canvasId:e,data:t,x:n,y:o,width:i,height:r},{resolve:a,reject:s})=>{var l=Ae(Ee());if(!l){s();return}let c;const u=()=>{yo(e,l,"putImageData",{data:t,x:n,y:o,width:i,height:r,compressed:c},d=>{if(d.errMsg&&d.errMsg.indexOf("fail")!==-1){s();return}a(d)})};t=Array.prototype.slice.call(t),u()},sv,av),Vl=R(lv,({x:e=0,y:t=0,width:n,height:o,destWidth:i,destHeight:r,canvasId:a,fileType:s,quality:l},{resolve:c,reject:u})=>{var d=Ae(Ee());if(!d){u();return}let f=`${vv()}/canvas`;yo(a,d,"toTempFilePath",{x:e,y:t,width:n,height:o,destWidth:i,destHeight:r,fileType:s,quality:l,dirname:f},h=>{if(h.errMsg&&h.errMsg.indexOf("fail")!==-1){u("",h);return}c(h)})},uv,cv),Cl=["onCanplay","onPlay","onPause","onStop","onEnded","onTimeUpdate","onError","onWaiting","onSeeking","onSeeked"],yv=["offCanplay","offPlay","offPause","offStop","offEnded","offTimeUpdate","offError","offWaiting","offSeeking","offSeeked"],bv={thresholds:[0],initialRatio:0,observeAll:!1},wv=["top","right","bottom","left"];let Sv=1;function Al(e={}){return wv.map(t=>`${Number(e[t])||0}px`).join(" ")}class El{constructor(t,n){this._pageId=Ae(t),this._component=t,this._options=B({},bv,n)}relativeTo(t,n){return this._options.relativeToSelector=t,this._options.rootMargin=Al(n),this}relativeToViewport(t){return this._options.relativeToSelector=void 0,this._options.rootMargin=Al(t),this}observe(t,n){G(n)&&(this._options.selector=t,this._reqId=Sv++,Pb({reqId:this._reqId,component:this._component,options:this._options,callback:n},this._pageId))}disconnect(){this._reqId&&Ob({reqId:this._reqId,component:this._component},this._pageId)}}const kl=j("createIntersectionObserver",(e,t)=>(e=on(e),e&&!Ae(e)&&(t=e,e=null),e?new El(e,t):new El(Ee(),t)));let Tv=1;class Il{constructor(t){this._pageId=t.$page&&t.$page.id,this._component=t}observe(t,n){G(n)&&(this._reqId=Tv++,Lb({reqId:this._reqId,component:this._component,options:t,callback:n},this._pageId))}disconnect(){this._reqId&&Bb({reqId:this._reqId,component:this._component},this._pageId)}}const Nl=j("createMediaQueryObserver",e=>(e=on(e),e&&!Ae(e)&&(e=null),e?new Il(e):new Il(Ee())));let Vv=0,qi={};function Cv(e,t,n,o){const i={options:o},r=o&&("success"in o||"fail"in o||"complete"in o);if(r){const a=String(Vv++);i.callbackId=a,qi[a]=o}UniServiceJSBridge.invokeViewMethod(`editor.${e}`,{type:n,data:i},t,({callbackId:a,data:s})=>{r&&(ye(qi[a],s),delete qi[a])})}class Av{constructor(t,n){this.id=t,this.pageId=n}format(t,n){this._exec("format",{name:t,value:n})}insertDivider(){this._exec("insertDivider")}insertImage(t){this._exec("insertImage",t)}insertText(t){this._exec("insertText",t)}setContents(t){this._exec("setContents",t)}getContents(t){this._exec("getContents",t)}clear(t){this._exec("clear",t)}removeFormat(t){this._exec("removeFormat",t)}undo(t){this._exec("undo",t)}redo(t){this._exec("redo",t)}blur(t){this._exec("blur",t)}getSelectionText(t){this._exec("getSelectionText",t)}scrollIntoView(t){this._exec("scrollIntoView",t)}_exec(t,n){Cv(this.id,this.pageId,t,n)}}const Ev={canvas:vn,map:Wi,video:zi,editor:Av};function xl(e){if(e&&e.contextInfo){const{id:t,type:n,page:o}=e.contextInfo,i=Ev[n];e.context=new i(t,o),delete e.contextInfo}}class Gi{constructor(t,n,o,i){this._selectorQuery=t,this._component=n,this._selector=o,this._single=i}boundingClientRect(t){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,rect:!0,size:!0},t),this._selectorQuery}fields(t,n){return this._selectorQuery._push(this._selector,this._component,this._single,t,n),this._selectorQuery}scrollOffset(t){return this._selectorQuery._push(this._selector,this._component,this._single,{id:!0,dataset:!0,scrollOffset:!0},t),this._selectorQuery}context(t){return this._selectorQuery._push(this._selector,this._component,this._single,{context:!0},t),this._selectorQuery}node(t){return this._selectorQuery._push(this._selector,this._component,this._single,{node:!0},t),this._selectorQuery}}class kv{constructor(t){this._component=void 0,this._page=t,this._queue=[],this._queueCb=[]}exec(t){return xb(this._page,this._queue,n=>{const o=this._queueCb;n.forEach((i,r)=>{K(i)?i.forEach(xl):xl(i);const a=o[r];G(a)&&a.call(this,i)}),G(t)&&t.call(this,n)}),this._nodesRef}in(t){return this._component=on(t),this}select(t){return this._nodesRef=new Gi(this,this._component,t,!0)}selectAll(t){return this._nodesRef=new Gi(this,this._component,t,!1)}selectViewport(){return this._nodesRef=new Gi(this,null,"",!0)}_push(t,n,o,i,r){this._queue.push({component:n,selector:t,single:o,fields:i}),this._queueCb.push(r)}}const Ml=j("createSelectorQuery",e=>(e=on(e),e&&!Ae(e)&&(e=null),new kv(e||Ee()))),Iv="createAnimation",Nv={formatArgs:{}},xv={},Mv={duration:400,timingFunction:"linear",delay:0,transformOrigin:"50% 50% 0"};class Pl{constructor(t){this.actions=[],this.currentTransform={},this.currentStepAnimates=[],this.option=B({},Mv,t)}_getOption(t){const n={transition:B({},this.option,t),transformOrigin:""};return n.transformOrigin=n.transition.transformOrigin,delete n.transition.transformOrigin,n}_pushAnimates(t,n){this.currentStepAnimates.push({type:t,args:n})}_converType(t){return t.replace(/[A-Z]/g,n=>`-${n.toLowerCase()}`)}_getValue(t){return typeof t=="number"?`${t}px`:t}export(){const t=this.actions;return this.actions=[],{actions:t}}step(t){return this.currentStepAnimates.forEach(n=>{n.type!=="style"?this.currentTransform[n.type]=n:this.currentTransform[`${n.type}.${n.args[0]}`]=n}),this.actions.push({animates:Object.values(this.currentTransform),option:this._getOption(t)}),this.currentStepAnimates=[],this}}const Pv=te(()=>{const e=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"],t=["opacity","backgroundColor"],n=["width","height","left","right","top","bottom"];e.concat(t,n).forEach(o=>{Pl.prototype[o]=function(...i){return t.concat(n).includes(o)?this._pushAnimates("style",[this._converType(o),n.includes(o)?this._getValue(i[0]):i[0]]):this._pushAnimates(o,i),this}})}),Ol=j(Iv,e=>(Pv(),new Pl(e)),xv,Nv),Rl="onTabBarMidButtonTap",Ll=ke(Rl,()=>{}),Ov="onWindowResize",Rv="offWindowResize",Bl=ke(Ov,()=>{}),Dl=gt(Rv,()=>{}),Lv="setLocale",Bv="getLocale",Ul="onLocaleChange",bo=j(Bv,()=>{const e=getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:D().getLocale()}),Fl=ke(Ul,()=>{}),$l=j(Lv,e=>{const t=getApp();return t&&t.$vm.$locale!==e?(t.$vm.$locale=e,navigator.cookieEnabled&&window.localStorage&&(localStorage[Ta]=e),UniServiceJSBridge.invokeOnCallback(Ul,{locale:e}),!0):!1}),zl=R("setPageMeta",(e,{resolve:t})=>{t(Mb(Ee(),e))}),Wl="getSelectedTextRange",Hl=R(Wl,(e,{resolve:t,reject:n})=>{UniServiceJSBridge.invokeViewMethod(Wl,{},ft(),o=>{typeof o.end=="undefined"&&typeof o.start=="undefined"?n("no focused"):t(o)})}),wo={[Yn]:[],[jn]:[],[ut]:[],[lt]:[],[ct]:[]};function _n(e,t){const n=getApp({allowDefault:!0});if(n&&n.$vm)return Vue.injectHook(e,t,n.$vm.$);wo[e].push(t)}function Dv(e){Object.keys(wo).forEach(t=>{wo[t].forEach(n=>{Vue.injectHook(t,n,e)})})}function yn(e,t){const n=getApp({allowDefault:!0});if(n&&n.$vm)return rm(n.$vm,e,t);Se(wo[e],t)}function ql(e){_n(Yn,e)}function Gl(e){yn(Yn,e)}function jl(e){_n(jn,e)}function Yl(e){yn(jn,e)}function Xl(e){_n(ut,e)}function Jl(e){yn(ut,e)}function Kl(e){_n(lt,e)}function Ql(e){yn(lt,e)}function Zl(e){_n(ct,e)}function ec(e){yn(ct,e)}const tc=j("getEnterOptionsSync",()=>qc()),nc=j("getLaunchOptionsSync",()=>Wb());let bn,ji,Yi;function oc(e){try{return JSON.parse(e)}catch(t){}return e}function ic(e){if(e.type==="enabled")Yi=!0;else if(e.type==="clientId")bn=e.cid,ji=e.errMsg,rc(bn,e.errMsg);else if(e.type==="pushMsg"){const t={type:"receive",data:oc(e.message)};for(let n=0;n<Qe.length;n++){const o=Qe[n];if(o(t),t.stopped)break}}else e.type==="click"&&Qe.forEach(t=>{t({type:"click",data:oc(e.message)})})}const Xi=[];function rc(e,t){Xi.forEach(n=>{n(e,t)}),Xi.length=0}const ac=R("getPushClientId",(e,{resolve:t,reject:n})=>{Promise.resolve().then(()=>{typeof Yi=="undefined"&&(Yi=!1,bn="",ji="uniPush is not enabled"),Xi.push((o,i)=>{o?t({cid:o}):n(i)}),typeof bn!="undefined"&&rc(bn,ji)})}),Qe=[],sc=e=>{Qe.indexOf(e)===-1&&Qe.push(e)},lc=e=>{if(!e)Qe.length=0;else{const t=Qe.indexOf(e);t>-1&&Qe.splice(t,1)}},Uv="canIUse",Fv="makePhoneCall",$v="getClipboardData",zv="setClipboardData",Wv={formatArgs:{showToast:!0},beforeInvoke(){wp()},beforeSuccess(e,t){if(!t.showToast)return;const{t:n}=D(),o=n("uni.setClipboardData.success");o&&uni.showToast({title:o,icon:"success",mask:!1})}},Hv={},cc="onAccelerometer",qv="offAccelerometer",Gv="startAccelerometer",jv="stopAccelerometer",uc="onCompass",Yv="offCompass",Xv="startCompass",Jv="stopCompass",Kv="vibrateShort",Qv="vibrateLong",Zv="getStorage",e_="getStorageSync",t_="setStorage",n_="setStorageSync",dc="removeStorage",o_="getFileInfo",i_={formatArgs:{filePath(e,t){t.filePath=Q(e)}}},r_={},a_="openDocument",s_={formatArgs:{filePath(e,t){t.filePath=Q(e)}}},l_={},c_="hideKeyboard",u_="chooseLocation",d_="getLocation",fc=["wgs84","gcj02"],f_={formatArgs:{type(e,t){e=(e||"").toLowerCase(),fc.indexOf(e)===-1?t.type=fc[0]:t.type=e},altitude(e,t){t.altitude=e||!1}}},h_={},g_="openLocation",hc=(e,t)=>{if(t===void 0)return`${e} should not be empty.`;if(typeof t!="number"){let n=typeof t;return n=n[0].toUpperCase()+n.substring(1),`Expected Number, got ${n} with value ${JSON.stringify(t)}.`}},p_={formatArgs:{latitude(e,t){const n=hc("latitude",e);if(n)return n;t.latitude=e},longitude(e,t){const n=hc("longitude",e);if(n)return n;t.longitude=e},scale(e,t){e=Math.floor(e),t.scale=e>=5&&e<=18?e:18}}},m_={},v_="chooseImage",__={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=9)},sizeType(e,t){t.sizeType=go(e,f0)},sourceType(e,t){t.sourceType=go(e,Di)},extension(e,t){if(e instanceof Array&&e.length===0)return"param extension should not be empty.";e||(t.extension=["*"])}}},y_={},b_="chooseVideo",w_={formatArgs:{sourceType(e,t){t.sourceType=go(e,Di)},compressed:!0,maxDuration:60,camera:"back",extension(e,t){if(e instanceof Array&&e.length===0)return"param extension should not be empty.";e||(t.extension=["*"])}}},S_={},T_="chooseFile",V_=["all","image","video"],C_={formatArgs:{count(e,t){(!e||e<=0)&&(t.count=100)},sourceType(e,t){t.sourceType=go(e,Di)},type(e,t){t.type=ho(e,V_)},extension(e,t){if(e instanceof Array&&e.length===0)return"param extension should not be empty.";e||(t.type==="all"||!t.type?t.extension=[""]:t.extension=["*"])}}},A_={},E_="getImageInfo",k_={formatArgs:{src(e,t){t.src=Q(e)}}},I_={},N_="previewImage",x_={formatArgs:{urls(e,t){t.urls=e.map(n=>J(n)&&n?Q(n):"")},current(e,t){typeof e=="number"?t.current=e>0&&e<t.urls.length?e:0:J(e)&&e&&(t.current=Q(e))}}},M_={},P_="closePreviewImage",O_="getVideoInfo",R_={formatArgs:{src(e,t){t.src=Q(e)}}},L_={},gc="saveImageToPhotosAlbum",pc="saveVideoToPhotosAlbum",B_="request",D_={JSON:"json"},U_=["text","arraybuffer"],F_="text",mc=encodeURIComponent;function $_(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let i=n[1]||"";e=n[0];const r=i.split("&").filter(s=>s),a={};r.forEach(s=>{const l=s.split("=");a[l[0]]=l[1]});for(const s in t)if(le(t,s)){let l=t[s];typeof l=="undefined"||l===null?l="":fe(l)&&(l=JSON.stringify(l)),a[mc(s)]=mc(l)}return i=Object.keys(a).map(s=>`${s}=${a[s]}`).join("&"),e+(i?"?"+i:"")+(o?"#"+o:"")}const z_={},W_={formatArgs:{method(e,t){t.method=ho((e||"").toUpperCase(),fo)},data(e,t){t.data=e||""},url(e,t){t.method===fo[0]&&fe(t.data)&&Object.keys(t.data).length&&(t.url=$_(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==fo[0]&&(Object.keys(n).find(o=>o.toLowerCase()==="content-type")||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||D_.JSON).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),U_.indexOf(t.responseType)===-1&&(t.responseType=F_)}}},H_="downloadFile",q_={formatArgs:{header(e,t){t.header=e||{}}}},G_={},j_="uploadFile",Y_={formatArgs:{filePath(e,t){e&&(t.filePath=Q(e))},header(e,t){t.header=e||{}},formData(e,t){t.formData=e||{}}}},X_={},J_="connectSocket",K_={formatArgs:{header(e,t){t.header=e||{}},method(e,t){t.method=ho((e||"").toUpperCase(),fo)},protocols(e,t){J(e)&&(t.protocols=[e])}}},Q_={},Z_="sendSocketMessage",ey="closeSocket",ty="startLocationUpdate",vc="onLocationChange",ny="stopLocationUpdate",oy="offLocationChange",iy="offLocationChangeError",Ji="onLocationChangeError",_c=["wgs84","gcj02"],ry={},ay={formatArgs:{type(e,t){e=(e||"").toLowerCase(),_c.indexOf(e)===-1?t.type=_c[1]:t.type=e}}};function sy(e){if(!J(e))return e;const t=e.indexOf("?");if(t===-1)return e;const n=e.slice(t+1).trim().replace(/^(\?|#|&)/,"");if(!n)return e;e=e.slice(0,t);const o=[];return n.split("&").forEach(i=>{const r=i.replace(/\+/g," ").split("="),a=r.shift(),s=r.length>0?r.join("="):"";o.push(a+"="+encodeURIComponent(s))}),o.length?e+"?"+o.join("&"):e}const ly=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],cy=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],So={url:{type:String,required:!0}},wn="navigateTo",Sn="redirectTo",To="reLaunch",Dt="switchTab",uy="navigateBack",Ki="preloadPage",dy="unPreloadPage",fy=B({},So,yc(ly)),hy=B({delta:{type:Number}},yc(cy)),gy=So,py=So,my=So,vy=Co(wn),_y=Co(Sn),yy=Co(To),by=Co(Dt),wy={formatArgs:{delta(e,t){e=parseInt(e+"")||1,t.delta=Math.min(getCurrentPages().length-1,e)}}};function yc(e){return{animationType:{type:String,validator(t){if(t&&e.indexOf(t)===-1)return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}let Vo;function Sy(){Vo=""}function Co(e){return{formatArgs:{url:Ty(e)},beforeAll:Sy}}function Ty(e){return function(n,o){if(!n)return'Missing required args: "url"';n=sm(n);const i=n.split("?")[0],r=gn(i,!0);if(!r)return"page `"+n+"` is not found";if(e===wn||e===Sn){if(r.meta.isTabBar)return`can not ${e} a tabbar page`}else if(e===Dt&&!r.meta.isTabBar)return"can not switch to no-tabBar page";if((e===Dt||e===Ki)&&r.meta.isTabBar&&o.openType!=="appLaunch"&&(n=i),r.meta.isEntry&&(n=n.replace(r.alias,"/")),o.url=sy(n),e!==dy){if(e===Ki){if(r.meta.isTabBar){const a=getCurrentPages(),s=r.path.slice(1);if(a.find(l=>l.route===s))return"tabBar page `"+s+"` already exists"}return}if(Vo===n&&o.openType!=="appLaunch")return`${Vo} locked`;__uniConfig.ready&&(Vo=n)}}}const Vy="hideLoading",Cy="hideToast",Ay="loadFontFace",Qi="setNavigationBarColor",Ey={formatArgs:{animation(e,t){e||(e={duration:0,timingFunc:"linear"}),t.animation={duration:e.duration||0,timingFunc:e.timingFunc||"linear"}}}},ky={},Zi="setNavigationBarTitle",er="showNavigationBarLoading",tr="hideNavigationBarLoading",Iy="pageScrollTo",Ny={},xy={formatArgs:{duration:300}},My="showActionSheet",Py={},Oy={formatArgs:{itemColor:"#000"}},Ry="showLoading",Ly={},By={formatArgs:{title:"",mask:!1}},Dy="showModal",Uy={},Fy={beforeInvoke(){bp()},formatArgs:{title:"",content:"",placeholderText:"",showCancel:!0,editable:!1,cancelText(e,t){if(!le(t,"cancelText")){const{t:n}=D();t.cancelText=n("uni.showModal.cancel")}},cancelColor:"#000",confirmText(e,t){if(!le(t,"confirmText")){const{t:n}=D();t.confirmText=n("uni.showModal.confirm")}},confirmColor:nn}},$y="showToast",bc=["success","loading","none","error"],zy={},Wy={formatArgs:{title:"",icon(e,t){t.icon=ho(e,bc)},image(e,t){e?t.image=Q(e):t.image=""},duration:1500,mask:!1}},nr="startPullDownRefresh",or="stopPullDownRefresh",Tn={index:{type:Number,required:!0}},Ze={beforeInvoke(){const e=xt();if(e&&!e.isTabBar)return"not TabBar page"},formatArgs:{index(e){if(!__uniConfig.tabBar.list[e])return"tabbar item not found"}}},ir="setTabBarItem",Hy=B({text:String,iconPath:String,selectedIconPath:String,pagePath:String},Tn),qy={beforeInvoke:Ze.beforeInvoke,formatArgs:B({pagePath(e,t){e&&(t.pagePath=La(e))}},Ze.formatArgs)},rr="setTabBarStyle",Gy={},jy=/^(linear|radial)-gradient\(.+?\);?$/,Yy={beforeInvoke:Ze.beforeInvoke,formatArgs:{backgroundImage(e,t){e&&!jy.test(e)&&(t.backgroundImage=Q(e))},borderStyle(e,t){e&&(t.borderStyle=e==="white"?"white":"black")}}},ar="hideTabBar",sr="showTabBar",lr="hideTabBarRedDot",Xy=Tn,Jy=Ze,cr="showTabBarRedDot",Ky=Tn,Qy=Ze,ur="removeTabBarBadge",Zy=Tn,eb=Ze,dr="setTabBarBadge",tb=B({text:{type:String,required:!0}},Tn),nb={beforeInvoke:Ze.beforeInvoke,formatArgs:B({text(e,t){Dg(e)>=4&&(t.text="...")}},Ze.formatArgs)},ob=function(){if(typeof window!="object")return;if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype){"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});return}function e(g){try{return g.defaultView&&g.defaultView.frameElement||null}catch(p){return null}}var t=function(g){for(var p=g,V=e(p);V;)p=V.ownerDocument,V=e(p);return p}(window.document),n=[],o=null,i=null;function r(g){this.time=g.time,this.target=g.target,this.rootBounds=_(g.rootBounds),this.boundingClientRect=_(g.boundingClientRect),this.intersectionRect=_(g.intersectionRect||h()),this.isIntersecting=!!g.intersectionRect;var p=this.boundingClientRect,V=p.width*p.height,S=this.intersectionRect,w=S.width*S.height;V?this.intersectionRatio=Number((w/V).toFixed(4)):this.intersectionRatio=this.isIntersecting?1:0}function a(g,p){var V=p||{};if(typeof g!="function")throw new Error("callback must be a function");if(V.root&&V.root.nodeType!=1&&V.root.nodeType!=9)throw new Error("root must be a Document or Element");this._checkForIntersections=l(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=g,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(V.rootMargin),this.thresholds=this._initThresholds(V.threshold),this.root=V.root||null,this.rootMargin=this._rootMarginValues.map(function(S){return S.value+S.unit}).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}a.prototype.THROTTLE_TIMEOUT=100,a.prototype.POLL_INTERVAL=null,a.prototype.USE_MUTATION_OBSERVER=!0,a._setupCrossOriginUpdater=function(){return o||(o=function(g,p){!g||!p?i=h():i=v(g,p),n.forEach(function(V){V._checkForIntersections()})}),o},a._resetCrossOriginUpdater=function(){o=null,i=null},a.prototype.observe=function(g){var p=this._observationTargets.some(function(V){return V.element==g});if(!p){if(!(g&&g.nodeType==1))throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:g,entry:null}),this._monitorIntersections(g.ownerDocument),this._checkForIntersections()}},a.prototype.unobserve=function(g){this._observationTargets=this._observationTargets.filter(function(p){return p.element!=g}),this._unmonitorIntersections(g.ownerDocument),this._observationTargets.length==0&&this._unregisterInstance()},a.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},a.prototype.takeRecords=function(){var g=this._queuedEntries.slice();return this._queuedEntries=[],g},a.prototype._initThresholds=function(g){var p=g||[0];return Array.isArray(p)||(p=[p]),p.sort().filter(function(V,S,w){if(typeof V!="number"||isNaN(V)||V<0||V>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return V!==w[S-1]})},a.prototype._parseRootMargin=function(g){var p=g||"0px",V=p.split(/\s+/).map(function(S){var w=/^(-?\d*\.?\d+)(px|%)$/.exec(S);if(!w)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(w[1]),unit:w[2]}});return V[1]=V[1]||V[0],V[2]=V[2]||V[0],V[3]=V[3]||V[1],V},a.prototype._monitorIntersections=function(g){var p=g.defaultView;if(p&&this._monitoringDocuments.indexOf(g)==-1){var V=this._checkForIntersections,S=null,w=null;this.POLL_INTERVAL?S=p.setInterval(V,this.POLL_INTERVAL):(c(p,"resize",V,!0),c(g,"scroll",V,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in p&&(w=new p.MutationObserver(V),w.observe(g,{attributes:!0,childList:!0,characterData:!0,subtree:!0}))),this._monitoringDocuments.push(g),this._monitoringUnsubscribes.push(function(){var E=g.defaultView;E&&(S&&E.clearInterval(S),u(E,"resize",V,!0)),u(g,"scroll",V,!0),w&&w.disconnect()});var A=this.root&&(this.root.ownerDocument||this.root)||t;if(g!=A){var x=e(g);x&&this._monitorIntersections(x.ownerDocument)}}},a.prototype._unmonitorIntersections=function(g){var p=this._monitoringDocuments.indexOf(g);if(p!=-1){var V=this.root&&(this.root.ownerDocument||this.root)||t,S=this._observationTargets.some(function(x){var E=x.element.ownerDocument;if(E==g)return!0;for(;E&&E!=V;){var C=e(E);if(E=C&&C.ownerDocument,E==g)return!0}return!1});if(!S){var w=this._monitoringUnsubscribes[p];if(this._monitoringDocuments.splice(p,1),this._monitoringUnsubscribes.splice(p,1),w(),g!=V){var A=e(g);A&&this._unmonitorIntersections(A.ownerDocument)}}}},a.prototype._unmonitorAllIntersections=function(){var g=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var p=0;p<g.length;p++)g[p]()},a.prototype._checkForIntersections=function(){if(!(!this.root&&o&&!i)){var g=this._rootIsInDom(),p=g?this._getRootRect():h();this._observationTargets.forEach(function(V){var S=V.element,w=f(S),A=this._rootContainsTarget(S),x=V.entry,E=g&&A&&this._computeTargetAndRootIntersection(S,w,p),C=null;this._rootContainsTarget(S)?(!o||this.root)&&(C=p):C=h();var I=V.entry=new r({time:s(),target:S,boundingClientRect:w,rootBounds:C,intersectionRect:E});x?g&&A?this._hasCrossedThreshold(x,I)&&this._queuedEntries.push(I):x&&x.isIntersecting&&this._queuedEntries.push(I):this._queuedEntries.push(I)},this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},a.prototype._computeTargetAndRootIntersection=function(g,p,V){if(window.getComputedStyle(g).display!="none"){for(var S=p,w=T(g),A=!1;!A&&w;){var x=null,E=w.nodeType==1?window.getComputedStyle(w):{};if(E.display=="none")return null;if(w==this.root||w.nodeType==9)if(A=!0,w==this.root||w==t)o&&!this.root?!i||i.width==0&&i.height==0?(w=null,x=null,S=null):x=i:x=V;else{var C=T(w),I=C&&f(C),M=C&&this._computeTargetAndRootIntersection(C,I,V);I&&M?(w=C,x=v(I,M)):(w=null,S=null)}else{var F=w.ownerDocument;w!=F.body&&w!=F.documentElement&&E.overflow!="visible"&&(x=f(w))}if(x&&(S=d(x,S)),!S)break;w=w&&T(w)}return S}},a.prototype._getRootRect=function(){var g;if(this.root&&!b(this.root))g=f(this.root);else{var p=b(this.root)?this.root:t,V=p.documentElement,S=p.body;g={top:0,left:0,right:V.clientWidth||S.clientWidth,width:V.clientWidth||S.clientWidth,bottom:V.clientHeight||S.clientHeight,height:V.clientHeight||S.clientHeight}}return this._expandRectByRootMargin(g)},a.prototype._expandRectByRootMargin=function(g){var p=this._rootMarginValues.map(function(S,w){return S.unit=="px"?S.value:S.value*(w%2?g.width:g.height)/100}),V={top:g.top-p[0],right:g.right+p[1],bottom:g.bottom+p[2],left:g.left-p[3]};return V.width=V.right-V.left,V.height=V.bottom-V.top,V},a.prototype._hasCrossedThreshold=function(g,p){var V=g&&g.isIntersecting?g.intersectionRatio||0:-1,S=p.isIntersecting?p.intersectionRatio||0:-1;if(V!==S)for(var w=0;w<this.thresholds.length;w++){var A=this.thresholds[w];if(A==V||A==S||A<V!=A<S)return!0}},a.prototype._rootIsInDom=function(){return!this.root||m(t,this.root)},a.prototype._rootContainsTarget=function(g){var p=this.root&&(this.root.ownerDocument||this.root)||t;return m(p,g)&&(!this.root||p==g.ownerDocument)},a.prototype._registerInstance=function(){n.indexOf(this)<0&&n.push(this)},a.prototype._unregisterInstance=function(){var g=n.indexOf(this);g!=-1&&n.splice(g,1)};function s(){return window.performance&&performance.now&&performance.now()}function l(g,p){var V=null;return function(){V||(V=setTimeout(function(){g(),V=null},p))}}function c(g,p,V,S){typeof g.addEventListener=="function"?g.addEventListener(p,V,S):typeof g.attachEvent=="function"&&g.attachEvent("on"+p,V)}function u(g,p,V,S){typeof g.removeEventListener=="function"?g.removeEventListener(p,V,S):typeof g.detatchEvent=="function"&&g.detatchEvent("on"+p,V)}function d(g,p){var V=Math.max(g.top,p.top),S=Math.min(g.bottom,p.bottom),w=Math.max(g.left,p.left),A=Math.min(g.right,p.right),x=A-w,E=S-V;return x>=0&&E>=0&&{top:V,bottom:S,left:w,right:A,width:x,height:E}||null}function f(g){var p;try{p=g.getBoundingClientRect()}catch(V){}return p?(p.width&&p.height||(p={top:p.top,right:p.right,bottom:p.bottom,left:p.left,width:p.right-p.left,height:p.bottom-p.top}),p):h()}function h(){return{top:0,bottom:0,left:0,right:0,width:0,height:0}}function _(g){return!g||"x"in g?g:{top:g.top,y:g.top,bottom:g.bottom,left:g.left,x:g.left,right:g.right,width:g.width,height:g.height}}function v(g,p){var V=p.top-g.top,S=p.left-g.left;return{top:V,left:S,height:p.height,width:p.width,bottom:V+p.height,right:S+p.width}}function m(g,p){for(var V=p;V;){if(V==g)return!0;V=T(V)}return!1}function T(g){var p=g.parentNode;return g.nodeType==9&&g!=t?e(g):(p&&p.assignedSlot&&(p=p.assignedSlot.parentNode),p&&p.nodeType==11&&p.host?p.host:p)}function b(g){return g&&g.nodeType===9}window.IntersectionObserver=a,window.IntersectionObserverEntry=r};function fr(e){const{bottom:t,height:n,left:o,right:i,top:r,width:a}=e||{};return{bottom:t,height:n,left:o,right:i,top:r,width:a}}function ib(e){const{intersectionRatio:t,boundingClientRect:{height:n,width:o},intersectionRect:{height:i,width:r}}=e;return t!==0?t:i===n?r/o:i/n}function rb(e,t,n){ob();const o=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,i=new IntersectionObserver(r=>{r.forEach(a=>{n({intersectionRatio:ib(a),intersectionRect:fr(a.intersectionRect),boundingClientRect:fr(a.boundingClientRect),relativeRect:fr(a.rootBounds),time:Date.now(),dataset:bi(a.target),id:a.target.id})})},{root:o,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){i.USE_MUTATION_OBSERVER=!0;const r=e.querySelectorAll(t.selector);for(let a=0;a<r.length;a++)i.observe(r[a])}else{i.USE_MUTATION_OBSERVER=!1;const r=e.querySelector(t.selector);r?i.observe(r):console.warn(`Node ${t.selector} is not found. Intersection observer will not trigger.`)}return i}function Ao(){const e=Ee();if(!e)return;const t=br(),n=t.keys();for(const o of n){const i=t.get(o);i.$.__isTabBar?i.$.__isActive=!1:Ro(o)}e.$.__isTabBar&&(e.$.__isVisible=!1,ie(e,ct))}function ab(e,t){return e===t.fullPath||e==="/"&&t.meta.isEntry}function Eo(e){const t=br().values();for(const n of t){const o=nt(n);if(ab(e,o))return n.$.__isActive=!0,o.id}}const wc=R(Dt,({url:e,tabBarText:t,isAutomatedTesting:n},{resolve:o,reject:i})=>{if(!Ut.handledBeforeEntryPageRoutes){vr.push({args:{type:Dt,url:e,tabBarText:t,isAutomatedTesting:n},resolve:o,reject:i});return}return Ao(),et({type:Dt,url:e,tabBarText:t,isAutomatedTesting:n},Eo(e)).then(o).catch(i)},my,by);function ko(){const e=dt();if(!e)return;const t=nt(e);Ro(Lo(t.path,t.id))}const Sc=R(Sn,({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(!Ut.handledBeforeEntryPageRoutes){_r.push({args:{type:Sn,url:e,isAutomatedTesting:t},resolve:n,reject:o});return}return ko(),et({type:Sn,url:e,isAutomatedTesting:t}).then(n).catch(o)},gy,_y);function Io(){const e=br().keys();for(const t of e)Ro(t)}const Tc=R(To,({url:e,isAutomatedTesting:t},{resolve:n,reject:o})=>{if(!Ut.handledBeforeEntryPageRoutes){yr.push({args:{type:To,url:e,isAutomatedTesting:t},resolve:n,reject:o});return}return Io(),et({type:To,url:e,isAutomatedTesting:t}).then(n).catch(o)},py,yy);function et({type:e,url:t,tabBarText:n,events:o,isAutomatedTesting:i},r){const a=getApp().$router,{path:s,query:l}=Gg(t);return new Promise((c,u)=>{const d=hb(e,r);a[e==="navigateTo"?"push":"replace"]({path:s,query:l,state:d,force:!0}).then(f=>{if(VueRouter.isNavigationFailure(f))return u(f.message);if(e==="switchTab"&&(a.currentRoute.value.meta.tabBarText=n),e==="navigateTo"){const h=a.currentRoute.value.meta;return h.eventChannel?o&&(Object.keys(o).forEach(_=>{h.eventChannel._addListener(_,"on",o[_])}),h.eventChannel._clearCache()):h.eventChannel=new Wa(d.__id__,o),c(i?{__id__:d.__id__}:{eventChannel:h.eventChannel})}return i?c({__id__:d.__id__}):c()})})}function Vc(){if(Ut.handledBeforeEntryPageRoutes)return;Ut.handledBeforeEntryPageRoutes=!0;const e=[...mr];mr.length=0,e.forEach(({args:i,resolve:r,reject:a})=>et(i).then(r).catch(a));const t=[...vr];vr.length=0,t.forEach(({args:i,resolve:r,reject:a})=>(Ao(),et(i,Eo(i.url)).then(r).catch(a)));const n=[..._r];_r.length=0,n.forEach(({args:i,resolve:r,reject:a})=>(ko(),et(i).then(r).catch(a)));const o=[...yr];yr.length=0,o.forEach(({args:i,resolve:r,reject:a})=>(Io(),et(i).then(r).catch(a)))}let hr;function Vn(){return hr||(hr=__uniConfig.tabBar&&Vue.reactive(Ep(__uniConfig.tabBar))),hr}function No(e){const t=window.CSS&&window.CSS.supports;return t&&(t(e)||t.apply(window.CSS,e.split(":")))}const gr=No("--a:0"),xo=No("top:env(a)"),Mo=No("top:constant(a)"),Po=No("backdrop-filter:blur(10px)"),Cc={"css.var":gr,"css.env":xo,"css.constant":Mo,"css.backdrop-filter":Po},Ac=j(Uv,e=>le(Cc,e)?Cc[e]:!0),Oo=xo?"env":Mo?"constant":"";function sb(e){let t=0,n=0;if(__UNI_FEATURE_NAVIGATIONBAR__&&e.navigationBar.style!=="custom"&&["default","float"].indexOf(e.navigationBar.type)>-1&&(t=Sa),__UNI_FEATURE_TABBAR__&&e.isTabBar){const o=Vn();o.shown&&(n=parseInt(o.height))}ds({"--window-top":lb(t),"--window-bottom":Ec(n)})}function lb(e){return Oo?`calc(${e}px + ${Oo}(safe-area-inset-top))`:`${e}px`}function Ec(e){return Oo?`calc(${e}px + ${Oo}(safe-area-inset-bottom))`:`${e}px`}const pr="$$",tt=new Map;function nt(e){return e.$page}const Ut={handledBeforeEntryPageRoutes:!1},mr=[],vr=[],_r=[],yr=[];function cb(){tt.forEach((e,t)=>{e.$.isUnmounted&&tt.delete(t)})}function br(){return tt}function ub(){return Ft()}function Ft(){const e=[],t=tt.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function db(e){const t=pt.get(e);t&&(pt.delete(e),Cn.pruneCacheEntry(t))}function Ro(e,t=!0){const n=tt.get(e);n.$.__isUnload=!0,ie(n,Ca),tt.delete(e),t&&db(e)}let fb=Bi();function hb(e,t){return{__id__:t||++fb,__type__:e}}function gb(e){const t=Pt();if(!__UNI_FEATURE_PAGES__)return _s("navigateTo",__uniRoutes[0].path,{},t);let n=e.fullPath;return e.meta.isEntry&&n.indexOf(e.meta.route)===-1&&(n="/"+e.meta.route+n.replace("/","")),_s("navigateTo",n,{},t)}function pb(e){const t=e.$route,n=gb(t);Rm(e,n),tt.set(Lo(n.path,n.id),e),tt.size===1&&setTimeout(()=>{Vc()},0)}function Lo(e,t){return e+pr+t}function mb(){const e=VueRouter.useRoute(),t=Vue.computed(()=>Lo("/"+e.meta.route,Bi())),n=Vue.computed(()=>e.meta.isTabBar);return{routeKey:t,isTabBar:n,routeCache:Cn}}const pt=new Map,Cn={get(e){return pt.get(e)},set(e,t){_b(e),pt.set(e,t)},delete(e){pt.get(e)&&pt.delete(e)},forEach(e){pt.forEach(e)}};function vb(e){return e.props.type==="tabBar"}function _b(e){const t=parseInt(e.split(pr)[1]);t&&Cn.forEach((n,o)=>{const i=parseInt(o.split(pr)[1]);if(i&&i>t){if(__UNI_FEATURE_TABBAR__&&vb(n))return;Cn.delete(o),Cn.pruneCacheEntry(n),Vue.nextTick(()=>cb())}})}function yb(e){{const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}}function kc(e,t){Sb(e),sb(t),yb(t),Nc(e,t)}function bb(e){const t=Ic(e);t&&wb(t)}function wb(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}function Ic(e){return e.type.__scopeId}let wr;function Sb(e){const t=Ic(e),{body:n}=document;wr&&n.removeAttribute(wr),t&&n.setAttribute(t,""),wr=t}let Bo;function Nc(e,t){if(document.removeEventListener("touchmove",ys),Bo&&document.removeEventListener("scroll",Bo),t.disableScroll)return document.addEventListener("touchmove",ys);const{onPageScroll:n,onReachBottom:o}=e,i=t.navigationBar.type==="transparent";if(!(n!=null&&n.length)&&!(o!=null&&o.length)&&!i)return;const r={},a=nt(e.proxy).id;(n||i)&&(r.onPageScroll=Tb(a,n,i)),o!=null&&o.length&&(r.onReachBottomDistance=t.onReachBottomDistance||wg,r.onReachBottom=()=>UniViewJSBridge.publishHandler(vi,{},a)),Bo=am(r),requestAnimationFrame(()=>document.addEventListener("scroll",Bo))}function Tb(e,t,n){return o=>{t&&UniViewJSBridge.publishHandler(Jn,{scrollTop:o},e),n&&UniViewJSBridge.emit(e+"."+Jn,{scrollTop:o})}}function xc(e){return e.$el}function Mc(e){const{base:t}=__uniConfig.router;return qe(e).indexOf(t)===0?qe(e):t+e}function Q(e){const{base:t,assets:n}=__uniConfig.router;if(t==="./"&&e.indexOf("./")===0&&(e.includes("/static/")||e.indexOf("./"+(n||"assets")+"/")===0)&&(e=e.slice(1)),e.indexOf("/")===0)if(e.indexOf("//")===0)e="https:"+e;else return Mc(e.slice(1));if(Tg.test(e)||Vg.test(e)||e.indexOf("blob:")===0)return e;const o=Ft();return o.length?Mc(Mi(nt(o[o.length-1]).route,e).slice(1)):e}const we=navigator.userAgent,Vb=/android/i.test(we),Pc=/iphone|ipad|ipod/i.test(we),Sr=we.match(/Windows NT ([\d|\d.\d]*)/i),Tr=/Macintosh|Mac/i.test(we),Oc=/Linux|X11/i.test(we),Cb=Tr&&navigator.maxTouchPoints>0;function Rc(){return/^Apple/.test(navigator.vendor)&&typeof window.orientation=="number"}function Lc(e){return e&&Math.abs(window.orientation)===90}function Bc(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function Ab(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}function Dc(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}function Uc(){const e=Rc(),t=Dc(Bc(e,Lc(e)));return{platform:Pc?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}function ze(e,t,n,o){UniServiceJSBridge.invokeViewMethod("video."+e,{videoId:e,type:n,data:o},t)}function Eb(e,t,n,o,i){UniServiceJSBridge.invokeViewMethod("map."+e,{type:n,data:o},t,i)}function kb(e){const t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){const n=document.documentElement,o=document.body;t.scrollLeft=n.scrollLeft||o.scrollLeft||0,t.scrollTop=n.scrollTop||o.scrollTop||0,t.scrollHeight=n.scrollHeight||o.scrollHeight||0,t.scrollWidth=n.scrollWidth||o.scrollWidth||0}return t}function Vr(e,t){const n={},{top:o,topWindowHeight:i}=us();if(t.node){const r=e.tagName.split("-")[1]||e.tagName;r&&(n.node=e.querySelector(r))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=bi(e)),t.rect||t.size){const r=e.getBoundingClientRect();t.rect&&(n.left=r.left,n.right=r.right,n.top=r.top-o-i,n.bottom=r.bottom-o-i),t.size&&(n.width=r.width,n.height=r.height)}if(K(t.properties)&&t.properties.forEach(r=>{r=r.replace(/-([a-z])/g,function(a,s){return s.toUpperCase()})}),t.scrollOffset)if(e.tagName==="UNI-SCROLL-VIEW"){const r=e.children[0].children[0];n.scrollLeft=r.scrollLeft,n.scrollTop=r.scrollTop,n.scrollHeight=r.scrollHeight,n.scrollWidth=r.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(K(t.computedStyle)){const r=getComputedStyle(e);t.computedStyle.forEach(a=>{n[a]=r[a]})}return t.context&&(n.contextInfo=nS(e)),n}function Ib(e,t){return e?e.$el:t.$el}function Fc(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(o){const i=this.parentElement.querySelectorAll(o);let r=i.length;for(;--r>=0&&i.item(r)!==this;);return r>-1}).call(e,t)}function Nb(e,t,n,o,i){const r=Ib(t,e),a=r.parentElement;if(!a)return o?null:[];const{nodeType:s}=r,l=s===3||s===8;if(o){const c=l?a.querySelector(n):Fc(r,n)?r:r.querySelector(n);return c?Vr(c,i):null}else{let c=[];const u=(l?a:r).querySelectorAll(n);return u&&u.length&&[].forEach.call(u,d=>{c.push(Vr(d,i))}),!l&&Fc(r,n)&&c.unshift(Vr(r,i)),c}}function xb(e,t,n){const o=[];t.forEach(({component:i,selector:r,single:a,fields:s})=>{i===null?o.push(kb(s)):o.push(Nb(e,i,r,a,s))}),n(o)}function Mb(e,{pageStyle:t,rootFontSize:n}){t&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",t),n&&document.documentElement.style.fontSize!==n&&(document.documentElement.style.fontSize=n)}function Pb({reqId:e,component:t,options:n,callback:o},i){const r=xc(t);(r.__io||(r.__io={}))[e]=rb(r,n,o)}function Ob({reqId:e,component:t},n){const o=xc(t),i=o.__io&&o.__io[e];i&&(i.disconnect(),delete o.__io[e])}let Cr={},Ar={};function Rb(e){const t=[],n=["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"];for(const i of n)i!=="orientation"&&e[i]&&+(e[i]>=0)&&t.push(`(${$c(i)}: ${Number(e[i])}px)`),i==="orientation"&&e[i]&&t.push(`(${$c(i)}: ${e[i]})`);return t.join(" and ")}function $c(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}function Lb({reqId:e,component:t,options:n,callback:o},i){const r=Cr[e]=window.matchMedia(Rb(n)),a=Ar[e]=s=>o(s.matches);a(r),r.addListener(a)}function Bb({reqId:e,component:t},n){const o=Ar[e],i=Cr[e];i&&(i.removeListener(o),delete Ar[e],delete Cr[e])}function Db(e,t,n){n(null,e)}const $t={};function Do(e,t){const n=$t[e];return n?Promise.resolve(n):/^data:[a-z-]+\/[a-z-]+;base64,/.test(e)?Promise.resolve(Ub(e)):t?Promise.reject(new Error("not find")):new Promise((o,i)=>{const r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="blob",r.onload=function(){o(this.response)},r.onerror=i,r.send()})}function Ub(e){const t=e.split(","),n=t[0].match(/:(.*?);/),o=n?n[1]:"",i=atob(t[1]);let r=i.length;const a=new Uint8Array(r);for(;r--;)a[r]=i.charCodeAt(r);return zc(a,o)}function Fb(e){const t=e.split("/")[1];return t?`.${t}`:""}function $b(e){e=e.split("#")[0].split("?")[0];const t=e.split("/");return t[t.length-1]}function zc(e,t){let n;if(e instanceof File)n=e;else{t=t||e.type||"";const o=`${Date.now()}${Fb(t)}`;try{n=new File([e],o,{type:t})}catch(i){e=e instanceof Blob?e:new Blob([e],{type:t}),n=e,n.name=n.name||o}}return n}function zt(e){for(const n in $t)if(le($t,n)&&$t[n]===e)return n;var t=(window.URL||window.webkitURL).createObjectURL(e);return $t[t]=e,t}function zb(e){const t=document.createElement("a");return t.href=e,t.origin===location.origin?Promise.resolve(e):Do(e).then(zt)}function Wc(e){(window.URL||window.webkitURL).revokeObjectURL(e),delete $t[e]}const Uo=ks(),Hc=ks();function qc(){return B({},Hc)}function Wb(){return B({},Uo)}function Hb({path:e,query:t}){return B(Uo,{path:e,query:t}),B(Hc,Uo),B({},Uo)}const Ge=X({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,{emit:t}){const n=Vue.ref(null),o=Gb(n),i=qb(n,t,o);return jb(n,e,i,o),()=>Vue.createVNode("uni-resize-sensor",{ref:n,onAnimationstartOnce:i},[Vue.createVNode("div",{onScroll:i},[Vue.createVNode("div",null,null)],40,["onScroll"]),Vue.createVNode("div",{onScroll:i},[Vue.createVNode("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});function qb(e,t,n){const o=Vue.reactive({width:-1,height:-1});return Vue.watch(()=>B({},o),i=>t("resize",i)),()=>{const i=e.value;i&&(o.width=i.offsetWidth,o.height=i.offsetHeight,n())}}function Gb(e){return()=>{const{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}function jb(e,t,n,o){Vue.onActivated(o),Vue.onMounted(()=>{t.initial&&Vue.nextTick(n);const i=e.value;i.offsetParent!==i.parentElement&&(i.parentElement.style.position="relative"),"AnimationEvent"in window||o()})}const Z=function(){if(navigator.userAgent.includes("jsdom"))return 1;const e=document.createElement("canvas");e.height=e.width=0;const t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function Gc(e,t=!0){const n=t?Z:1;e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").__hidpi__=t}let jc=!1;function Yb(){if(jc)return;jc=!0;const e=function(o,i){for(const r in o)le(o,r)&&i(o[r],r)},t={fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},n=CanvasRenderingContext2D.prototype;n.drawImageByCanvas=function(o){return function(i,r,a,s,l,c,u,d,f,h){if(!this.__hidpi__)return o.apply(this,arguments);r*=Z,a*=Z,s*=Z,l*=Z,c*=Z,u*=Z,d=h?d*Z:d,f=h?f*Z:f,o.call(this,i,r,a,s,l,c,u,d,f)}}(n.drawImage),Z!==1&&(e(t,function(o,i){n[i]=function(r){return function(){if(!this.__hidpi__)return r.apply(this,arguments);let a=Array.prototype.slice.call(arguments);if(o==="all")a=a.map(function(s){return s*Z});else if(Array.isArray(o))for(let s=0;s<o.length;s++)a[o[s]]*=Z;return r.apply(this,a)}}(n[i])}),n.stroke=function(o){return function(){if(!this.__hidpi__)return o.apply(this,arguments);this.lineWidth*=Z,o.apply(this,arguments),this.lineWidth/=Z}}(n.stroke),n.fillText=function(o){return function(){if(!this.__hidpi__)return o.apply(this,arguments);const i=Array.prototype.slice.call(arguments);i[1]*=Z,i[2]*=Z,i[3]&&typeof i[3]=="number"&&(i[3]*=Z);var r=this.font;this.font=r.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,function(a,s,l){return s*Z+l}),o.apply(this,i),this.font=r}}(n.fillText),n.strokeText=function(o){return function(){if(!this.__hidpi__)return o.apply(this,arguments);var i=Array.prototype.slice.call(arguments);i[1]*=Z,i[2]*=Z,i[3]&&typeof i[3]=="number"&&(i[3]*=Z);var r=this.font;this.font=r.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,function(a,s,l){return s*Z+l}),o.apply(this,i),this.font=r}}(n.strokeText),n.drawImage=function(o){return function(){if(!this.__hidpi__)return o.apply(this,arguments);this.scale(Z,Z),o.apply(this,arguments),this.scale(1/Z,1/Z)}}(n.drawImage))}const Xb=te(()=>Yb());function Yc(e){return e&&Q(e)}function Fo(e){return e=e.slice(0),e[3]=e[3]/255,"rgba("+e.join(",")+")"}function Xc(e,t){Array.from(t).forEach(n=>{n.x=n.clientX-e.left,n.y=n.clientY-e.top})}let An;function Jc(e=0,t=0){return An||(An=document.createElement("canvas")),An.width=e,An.height=t,An}const Jb=X({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,{emit:t,slots:n}){Xb();const o=Vue.ref(null),i=Vue.ref(null),r=Vue.ref(null),a=Vue.ref(!1),s=Km(t),{$attrs:l,$excludeAttrs:c,$listeners:u}=qo({excludeListeners:!0}),{_listeners:d}=Kb(e,u,s),{_handleSubscribe:f,_resize:h}=Qb(e,i,a);return qt(f,Xo(e.canvasId),!0),Vue.onMounted(()=>{h()}),()=>{const{canvasId:_,disableScroll:v}=e;return Vue.createVNode("uni-canvas",Vue.mergeProps({ref:o,"canvas-id":_,"disable-scroll":v},l.value,c.value,d.value),[Vue.createVNode("canvas",{ref:i,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),Vue.createVNode("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[n.default&&n.default()]),Vue.createVNode(Ge,{ref:r,onResize:h},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});function Kb(e,t,n){return{_listeners:Vue.computed(()=>{let i=["onTouchstart","onTouchmove","onTouchend"],r=t.value,a=B({},(()=>{let s={};for(const l in r)if(le(r,l)){const c=r[l];s[l]=c}return s})());return i.forEach(s=>{let l=a[s],c=[];l&&c.push(ne(u=>{const d=u.currentTarget.getBoundingClientRect();Xc(d,u.touches),Xc(d,u.changedTouches),n(s.replace("on","").toLocaleLowerCase(),u)})),e.disableScroll&&s==="onTouchmove"&&c.push(Xe),a[s]=c}),a})}}function Qb(e,t,n){let o=[],i={};const r=Vue.computed(()=>e.hidpi?Z:1);function a(v){let m=t.value;var T=!v||m.width!==Math.floor(v.width*r.value)||m.height!==Math.floor(v.height*r.value);if(T)if(m.width>0&&m.height>0){let b=m.getContext("2d"),g=b.getImageData(0,0,m.width,m.height);Gc(m,e.hidpi),b.putImageData(g,0,0)}else Gc(m,e.hidpi)}function s({actions:v,reserve:m},T){if(!v)return;if(n.value){o.push([v,m]);return}let b=t.value,g=b.getContext("2d");m||(g.fillStyle="#000000",g.strokeStyle="#000000",g.shadowColor="#000000",g.shadowBlur=0,g.shadowOffsetX=0,g.shadowOffsetY=0,g.setTransform(1,0,0,1,0,0),g.clearRect(0,0,b.width,b.height)),l(v);for(let p=0;p<v.length;p++){const V=v[p];let S=V.method;const w=V.data,A=w[0];if(/^set/.test(S)&&S!=="setTransform"){const x=S[3].toLowerCase()+S.slice(4);let E;if(x==="fillStyle"||x==="strokeStyle"){if(A==="normal")E=Fo(w[1]);else if(A==="linear"){const C=g.createLinearGradient(...w[1]);w[2].forEach(function(I){const M=I[0],F=Fo(I[1]);C.addColorStop(M,F)}),E=C}else if(A==="radial"){let C=w[1];const I=C[0],M=C[1],F=C[2],k=g.createRadialGradient(I,M,0,I,M,F);w[2].forEach(function(L){const z=L[0],N=Fo(L[1]);k.addColorStop(z,N)}),E=k}else if(A==="pattern"){if(!c(w[1],v.slice(p+1),T,function(I){I&&(g[x]=g.createPattern(I,w[2]))}))break;continue}g[x]=E}else if(x==="globalAlpha")g[x]=Number(A)/255;else if(x==="shadow"){let C=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];w.forEach(function(I,M){g[C[M]]=C[M]==="shadowColor"?Fo(I):I})}else if(x==="fontSize"){const C=g.__font__||g.font;g.__font__=g.font=C.replace(/\d+\.?\d*px/,A+"px")}else x==="lineDash"?(g.setLineDash(A),g.lineDashOffset=w[1]||0):x==="textBaseline"?(A==="normal"&&(w[0]="alphabetic"),g[x]=A):x==="font"?g.__font__=g.font=A:g[x]=A}else if(S==="fillPath"||S==="strokePath")S=S.replace(/Path/,""),g.beginPath(),w.forEach(function(x){g[x.method].apply(g,x.data)}),g[S]();else if(S==="fillText")g.fillText.apply(g,w);else if(S==="drawImage"){if(function(){let E=[...w],C=E[0],I=E.slice(1);if(i=i||{},!c(C,v.slice(p+1),T,function(M){M&&g.drawImage.apply(g,[M].concat([...I.slice(4,8)],[...I.slice(0,4)]))}))return"break"}()==="break")break}else S==="clip"?(w.forEach(function(x){g[x.method].apply(g,x.data)}),g.clip()):g[S].apply(g,w)}n.value||T({errMsg:"drawCanvas:ok"})}function l(v){v.forEach(function(m){let T=m.method,b=m.data,g="";T==="drawImage"?(g=b[0],g=Yc(g),b[0]=g):T==="setFillStyle"&&b[0]==="pattern"&&(g=b[1],g=Yc(g),b[1]=g),g&&!i[g]&&p();function p(){const V=i[g]=new Image;V.onload=function(){V.ready=!0},zb(g).then(S=>{V.src=S}).catch(()=>{V.src=g})}})}function c(v,m,T,b){let g=i[v];return g.ready?(b(g),!0):(o.unshift([m,!0]),n.value=!0,g.onload=function(){g.ready=!0,b(g),n.value=!1;let p=o.slice(0);o=[];for(let V=p.shift();V;)s({actions:V[0],reserve:V[1]},T),V=p.shift()},!1)}function u({x:v=0,y:m=0,width:T,height:b,destWidth:g,destHeight:p,hidpi:V=!0,dataType:S,quality:w=1,type:A="png"},x){const E=t.value;let C;const I=E.offsetWidth-v;T=T?Math.min(T,I):I;const M=E.offsetHeight-m;b=b?Math.min(b,M):M,V?(g=T,p=b):!g&&!p?(g=Math.round(T*r.value),p=Math.round(b*r.value)):g?p||(p=Math.round(b/T*g)):(p||(p=Math.round(b*r.value)),g=Math.round(T/b*p));const F=Jc(g,p),k=F.getContext("2d");(A==="jpeg"||A==="jpg")&&(A="jpeg",k.fillStyle="#fff",k.fillRect(0,0,g,p)),k.__hidpi__=!0,k.drawImageByCanvas(E,v,m,T,b,0,0,g,p,!1);let L;try{let z;if(S==="base64")C=F.toDataURL(`image/${A}`,w);else{const N=k.getImageData(0,0,g,p);C=Array.prototype.slice.call(N.data)}L={data:C,compressed:z,width:g,height:p}}catch(z){L={errMsg:`canvasGetImageData:fail ${z}`}}if(F.height=F.width=0,k.__hidpi__=!1,x)x(L);else return L}function d({data:v,x:m,y:T,width:b,height:g,compressed:p},V){try{g||(g=Math.round(v.length/4/b));const S=Jc(b,g);S.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(v),b,g),0,0),t.value.getContext("2d").drawImage(S,m,T,b,g),S.height=S.width=0}catch(S){V({errMsg:"canvasPutImageData:fail"});return}V({errMsg:"canvasPutImageData:ok"})}function f({x:v=0,y:m=0,width:T,height:b,destWidth:g,destHeight:p,fileType:V,quality:S,dirname:w},A){const x=u({x:v,y:m,width:T,height:b,destWidth:g,destHeight:p,hidpi:!1,dataType:"base64",type:V,quality:S});if(x.errMsg){A({errMsg:x.errMsg.replace("canvasPutImageData","toTempFilePath")});return}Db(x.data,w,(E,C)=>{let I=`toTempFilePath:${E?"fail":"ok"}`;E&&(I+=` ${E.message}`),A({errMsg:I,tempFilePath:C})})}const h={actionsChanged:s,getImageData:u,putImageData:d,toTempFilePath:f};function _(v,m,T){let b=h[v];v.indexOf("_")!==0&&G(b)&&b(m,T)}return B(h,{_resize:a,_handleSubscribe:_})}const Kc=dn("ucg"),Zb=X({name:"CheckboxGroup",props:{name:{type:String,default:""}},emits:["change"],setup(e,{emit:t,slots:n}){const o=Vue.ref(null),i=ve(o,t);return ew(e,i),()=>Vue.createVNode("uni-checkbox-group",{ref:o},[n.default&&n.default()],512)}});function ew(e,t){const n=[],o=()=>n.reduce((r,a)=>(a.value.checkboxChecked&&r.push(a.value.value),r),new Array);Vue.provide(Kc,{addField(r){n.push(r)},removeField(r){n.splice(n.indexOf(r),1)},checkboxChange(r){t("change",r,{value:o()})}});const i=Vue.inject(Fe,!1);return i&&i.addField({submit:()=>{let r=["",null];return e.name!==""&&(r[0]=e.name,r[1]=o()),r}}),o}const tw=X({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:""},foreColor:{type:String,default:""}},setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.ref(e.checked),i=Vue.computed(()=>o.value==="true"||o.value===!0),r=Vue.ref(e.value);function a(f){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};const h={};return f?(e.activeBorderColor&&(h.borderColor=e.activeBorderColor),e.activeBackgroundColor&&(h.backgroundColor=e.activeBackgroundColor)):(e.borderColor&&(h.borderColor=e.borderColor),e.backgroundColor&&(h.backgroundColor=e.backgroundColor)),h}const s=Vue.computed(()=>a(i.value));Vue.watch([()=>e.checked,()=>e.value],([f,h])=>{o.value=f,r.value=h});const l=()=>{o.value=!1},{uniCheckGroup:c,uniLabel:u}=nw(o,r,l),d=f=>{e.disabled||(o.value=!o.value,c&&c.checkboxChange(f),f.stopPropagation())};return u&&(u.addHandler(d),Vue.onBeforeUnmount(()=>{u.removeHandler(d)})),co(e,{"label-click":d}),()=>{const f=ht(e,"disabled");let h;return h=o.value,Vue.createVNode("uni-checkbox",Vue.mergeProps(f,{id:e.id,onClick:d,ref:n}),[Vue.createVNode("div",{class:"uni-checkbox-wrapper",style:{"--HOVER-BD-COLOR":e.activeBorderColor}},[Vue.createVNode("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}],style:s.value},[h?ce(fn,e.disabled?"#ADADAD":e.foreColor||e.iconColor||e.color,22):""],6),t.default&&t.default()],4)],16,["id","onClick"])}}});function nw(e,t,n){const o=Vue.computed(()=>({checkboxChecked:!!e.value,value:t.value})),i={reset:n},r=Vue.inject(Kc,!1);r&&r.addField(o);const a=Vue.inject(Fe,!1);a&&a.addField(i);const s=Vue.inject(pn,!1);return Vue.onBeforeUnmount(()=>{r&&r.removeField(o),a&&a.removeField(i)}),{uniCheckGroup:r,uniForm:a,uniLabel:s}}let ow;function Qc(){}const Zc={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}},eu=["keyboardheightchange"];function tu(e,t,n){function o(i){const r=Vue.computed(()=>String(navigator.vendor).indexOf("Apple")===0);i.addEventListener("focus",()=>{clearTimeout(ow),document.addEventListener("click",Qc,!1)});const a=()=>{document.removeEventListener("click",Qc,!1),r.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)};i.addEventListener("blur",()=>{r.value&&i.blur(),a()})}Vue.watch(()=>t.value,i=>i&&o(i))}var nu=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,ou=/^<\/([-A-Za-z0-9_]+)[^>]*>/,iw=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,rw=Wt("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),aw=Wt("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),sw=Wt("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),lw=Wt("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),cw=Wt("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),uw=Wt("script,style");function iu(e,t){var n,o,i,r=[],a=e;for(r.last=function(){return this[this.length-1]};e;){if(o=!0,!r.last()||!uw[r.last()]){if(e.indexOf("<!--")==0?(n=e.indexOf("-->"),n>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),o=!1)):e.indexOf("</")==0?(i=e.match(ou),i&&(e=e.substring(i[0].length),i[0].replace(ou,c),o=!1)):e.indexOf("<")==0&&(i=e.match(nu),i&&(e=e.substring(i[0].length),i[0].replace(nu,l),o=!1)),o){n=e.indexOf("<");var s=n<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}}else e=e.replace(new RegExp("([\\s\\S]*?)</"+r.last()+"[^>]*>"),function(u,d){return d=d.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(d),""}),c("",r.last());if(e==a)throw"Parse Error: "+e;a=e}c();function l(u,d,f,h){if(d=d.toLowerCase(),aw[d])for(;r.last()&&sw[r.last()];)c("",r.last());if(lw[d]&&r.last()==d&&c("",d),h=rw[d]||!!h,h||r.push(d),t.start){var _=[];f.replace(iw,function(v,m){var T=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:cw[m]?m:"";_.push({name:m,value:T,escaped:T.replace(/(^|[^\\])"/g,'$1\\"')})}),t.start&&t.start(d,_,h)}}function c(u,d){if(d)for(var f=r.length-1;f>=0&&r[f]!=d;f--);else var f=0;if(f>=0){for(var h=r.length-1;h>=f;h--)t.end&&t.end(r[h]);r.length=f}}}function Wt(e){for(var t={},n=e.split(","),o=0;o<n.length;o++)t[n[o]]=!0;return t}const Er={};function ru(e,t,n){if(J(e)?window[e]:e){n();return}let i=Er[t];if(!i){i=Er[t]=[];const r=document.createElement("script");r.src=t,document.body.appendChild(r),r.onload=function(){i.forEach(a=>a()),delete Er[t]}}i.push(n)}function dw(e){const t=e.import("blots/block/embed");class n extends t{}return n.blotName="divider",n.tagName="HR",{"formats/divider":n}}function fw(e){const t=e.import("blots/inline");class n extends t{}return n.blotName="ins",n.tagName="INS",{"formats/ins":n}}function hw(e){const{Scope:t,Attributor:n}=e.import("parchment"),o={scope:t.BLOCK,whitelist:["left","right","center","justify"]};return{"formats/align":new n.Style("align","text-align",o)}}function gw(e){const{Scope:t,Attributor:n}=e.import("parchment"),o={scope:t.BLOCK,whitelist:["rtl"]};return{"formats/direction":new n.Style("direction","direction",o)}}function pw(e){const t=e.import("parchment"),n=e.import("blots/container"),o=e.import("formats/list/item");class i extends n{static create(a){const s=a==="ordered"?"OL":"UL",l=super.create(s);return(a==="checked"||a==="unchecked")&&l.setAttribute("data-checked",a==="checked"),l}static formats(a){if(a.tagName==="OL")return"ordered";if(a.tagName==="UL")return a.hasAttribute("data-checked")?a.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}constructor(a){super(a);const s=l=>{if(l.target.parentNode!==a)return;const c=this.statics.formats(a),u=t.find(l.target);c==="checked"?u.format("list","unchecked"):c==="unchecked"&&u.format("list","checked")};a.addEventListener("click",s)}format(a,s){this.children.length>0&&this.children.tail.format(a,s)}formats(){return{[this.statics.blotName]:this.statics.formats(this.domNode)}}insertBefore(a,s){if(a instanceof o)super.insertBefore(a,s);else{const l=s==null?this.length():s.offset(this),c=this.split(l);c.parent.insertBefore(a,c)}}optimize(a){super.optimize(a);const s=this.next;s!=null&&s.prev===this&&s.statics.blotName===this.statics.blotName&&s.domNode.tagName===this.domNode.tagName&&s.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(s.moveChildren(this),s.remove())}replace(a){if(a.statics.blotName!==this.statics.blotName){const s=t.create(this.statics.defaultChild);a.moveChildren(s),this.appendChild(s)}super.replace(a)}}return i.blotName="list",i.scope=t.Scope.BLOCK_BLOT,i.tagName=["OL","UL"],i.defaultChild="list-item",i.allowedChildren=[o],{"formats/list":i}}function mw(e){const{Scope:t}=e.import("parchment"),n=e.import("formats/background");return{"formats/backgroundColor":new n.constructor("backgroundColor","background-color",{scope:t.INLINE})}}function vw(e){const{Scope:t,Attributor:n}=e.import("parchment"),o={scope:t.BLOCK},i=["margin","marginTop","marginBottom","marginLeft","marginRight"],r=["padding","paddingTop","paddingBottom","paddingLeft","paddingRight"],a={};return i.concat(r).forEach(s=>{a[`formats/${s}`]=new n.Style(s,Ct(s),o)}),a}function _w(e){const{Scope:t,Attributor:n}=e.import("parchment"),o={scope:t.INLINE},i=["font","fontSize","fontStyle","fontVariant","fontWeight","fontFamily"],r={};return i.forEach(a=>{r[`formats/${a}`]=new n.Style(a,Ct(a),o)}),r}function yw(e){const{Scope:t,Attributor:n}=e.import("parchment"),o=[{name:"lineHeight",scope:t.BLOCK},{name:"letterSpacing",scope:t.INLINE},{name:"textDecoration",scope:t.INLINE},{name:"textIndent",scope:t.BLOCK}],i={};return o.forEach(({name:r,scope:a})=>{i[`formats/${r}`]=new n.Style(r,Ct(r),{scope:a})}),i}function bw(e){const t=e.import("formats/image"),n=["alt","height","width","data-custom","class","data-local"];t.sanitize=i=>i&&Q(i),t.formats=function(r){return n.reduce(function(a,s){return r.hasAttribute(s)&&(a[s]=r.getAttribute(s)),a},{})};const o=t.prototype.format;t.prototype.format=function(i,r){n.indexOf(i)>-1?r?this.domNode.setAttribute(i,r):this.domNode.removeAttribute(i):o.call(this,i,r)}}function ww(e){const t=e.import("formats/link");t.sanitize=n=>{const o=document.createElement("a");o.href=n;const i=o.href.slice(0,o.href.indexOf(":"));return t.PROTOCOL_WHITELIST.concat("file").indexOf(i)>-1?n:t.SANITIZED_URL}}function Sw(e){const t={divider:dw,ins:fw,align:hw,direction:gw,list:pw,background:mw,box:vw,font:_w,text:yw,image:bw,link:ww},n={};Object.values(t).forEach(o=>B(n,o(e))),e.register(n,!0)}function Tw(e,t,n){let o,i,r;Vue.watch(()=>e.readOnly,_=>{o&&(r.enable(!_),_||r.blur())}),Vue.watch(()=>e.placeholder,_=>{o&&l(_)});function a(_){const v=["span","strong","b","ins","em","i","u","a","del","s","sub","sup","img","div","p","h1","h2","h3","h4","h5","h6","hr","ol","ul","li","br"];let m="",T;iu(_,{start:function(g,p,V){if(!v.includes(g)){T=!V;return}T=!1;const S=p.map(({name:A,value:x})=>`${A}="${x}"`).join(" "),w=`<${g} ${S} ${V?"/":""}>`;m+=w},end:function(g){T||(m+=`</${g}>`)},chars:function(g){T||(m+=g)}}),i=!0;const b=r.clipboard.convert(m);return i=!1,b}function s(){const _=r.root.innerHTML,v=r.getText(),m=r.getContents();return{html:_,text:v,delta:m}}function l(_){const v="data-placeholder",m=r.root;m.getAttribute(v)!==_&&m.setAttribute(v,_)}let c={};function u(_){const v=_?r.getFormat(_):{},m=Object.keys(v);(m.length!==Object.keys(c).length||m.find(T=>v[T]!==c[T]))&&(c=v,n("statuschange",{},v))}function d(){n("input",{},s())}function f(_){const v=window.Quill;Sw(v);const m={toolbar:!1,readOnly:e.readOnly,placeholder:e.placeholder};_.length&&(v.register("modules/ImageResize",window.ImageResize.default),m.modules={ImageResize:{modules:_}});const T=t.value;r=new v(T,m);const b=r.root;["focus","blur","input"].forEach(p=>{b.addEventListener(p,V=>{const S=s();if(p==="input"){if(Uc().platform==="ios"){const w=(S.html.match(/<span [\s\S]*>([\s\S]*)<\/span>/)||[])[1],A=w&&w.replace(/\s/g,"")?"":e.placeholder;l(A)}V.stopPropagation()}else n(p,V,S)})}),r.on("text-change",d),r.on("selection-change",u),r.on("scroll-optimize",()=>{const p=r.selection.getRange()[0];u(p)}),r.clipboard.addMatcher(Node.ELEMENT_NODE,(p,V)=>(i||V.ops&&(V.ops=V.ops.filter(({insert:S})=>J(S)).map(({insert:S})=>({insert:S}))),V)),o=!0,n("ready",{},{})}const h=Xo();qt((_,v,m)=>{const{options:T,callbackId:b}=v;let g,p,V;if(o){const S=window.Quill;switch(_){case"format":{let{name:w="",value:A=!1}=T;p=r.getSelection(!0);let x=r.getFormat(p)[w]||!1;if(["bold","italic","underline","strike","ins"].includes(w))A=!x;else if(w==="direction"){A=A==="rtl"&&x?!1:A;const E=r.getFormat(p).align;A==="rtl"&&!E?r.format("align","right","user"):!A&&E==="right"&&r.format("align",!1,"user")}else if(w==="indent"){const E=r.getFormat(p).direction==="rtl";A=A==="+1",E&&(A=!A),A=A?"+1":"-1"}else w==="list"&&(A=A==="check"?"unchecked":A,x=x==="checked"?"unchecked":x),A=x&&x!==(A||!1)||!x&&A?A:!x;r.format(w,A,"user")}break;case"insertDivider":p=r.getSelection(!0),r.insertText(p.index,Et,"user"),r.insertEmbed(p.index+1,"divider",!0,"user"),r.setSelection(p.index+2,0,"silent");break;case"insertImage":{p=r.getSelection(!0);const{src:w="",alt:A="",width:x="",height:E="",extClass:C="",data:I={}}=T,M=Q(w);r.insertEmbed(p.index,"image",M,"silent");const F=/^(file|blob):/.test(M)?M:!1;r.formatText(p.index,1,"data-local",F,"silent"),r.formatText(p.index,1,"alt",A,"silent"),r.formatText(p.index,1,"width",x,"silent"),r.formatText(p.index,1,"height",E,"silent"),r.formatText(p.index,1,"class",C,"silent"),r.formatText(p.index,1,"data-custom",Object.keys(I).map(k=>`${k}=${I[k]}`).join("&"),"silent"),r.setSelection(p.index+1,0,"silent"),r.scrollIntoView(),setTimeout(()=>{d()},1e3)}break;case"insertText":{p=r.getSelection(!0);const{text:w=""}=T;r.insertText(p.index,w,"user"),r.setSelection(p.index+w.length,0,"silent")}break;case"setContents":{const{delta:w,html:A}=T;typeof w=="object"?r.setContents(w,"silent"):J(A)?r.setContents(a(A),"silent"):V="contents is missing"}break;case"getContents":g=s();break;case"clear":r.setText("");break;case"removeFormat":{p=r.getSelection(!0);const w=S.import("parchment");p.length?r.removeFormat(p.index,p.length,"user"):Object.keys(r.getFormat(p)).forEach(A=>{w.query(A,w.Scope.INLINE)&&r.format(A,!1)})}break;case"undo":r.history.undo();break;case"redo":r.history.redo();break;case"blur":r.blur();break;case"getSelectionText":p=r.selection.savedRange,g={text:""},p&&p.length!==0&&(g.text=r.getText(p.index,p.length));break;case"scrollIntoView":r.scrollIntoView();break}u(p)}else V="not ready";b&&m({callbackId:b,data:B({},g,{errMsg:`${_}:${V?"fail "+V:"ok"}`})})},h,!0),Vue.onMounted(()=>{const _=[];e.showImgSize&&_.push("DisplaySize"),e.showImgToolbar&&_.push("Toolbar"),e.showImgResize&&_.push("Resize"),ru(window.Quill,"https://unpkg.com/quill@1.3.7/dist/quill.min.js",()=>{_.length?ru(window.ImageResize,"https://unpkg.com/quill-image-resize-mp@3.0.1/image-resize.min.js",()=>{f(_)}):f(_)})})}const Vw=X({name:"Editor",props:B({},Zc,{id:{type:String,default:""},readOnly:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},showImgSize:{type:[Boolean,String],default:!1},showImgToolbar:{type:[Boolean,String],default:!1},showImgResize:{type:[Boolean,String],default:!1}}),emit:["ready","focus","blur","input","statuschange",...eu],setup(e,{emit:t}){const n=Vue.ref(null),o=ve(n,t);return Tw(e,n,o),tu(e,n),()=>Vue.createVNode("uni-editor",{ref:n,id:e.id,class:"ql-container"},null,8,["id"])}}),au="#10aeff",Cw="#f76260",su="#b2b2b2",Aw={success:{d:em,c:nn},success_no_circle:{d:fn,c:nn},info:{d:Zp,c:au},warn:{d:ps,c:Cw},waiting:{d:tm,c:au},cancel:{d:Jp,c:"#f43530"},download:{d:Qp,c:nn},search:{d:gs,c:su},clear:{d:Kp,c:su}},Ew=X({name:"Icon",props:{type:{type:String,required:!0,default:""},size:{type:[String,Number],default:23},color:{type:String,default:""}},setup(e){const t=Vue.ref(null),n=Vue.computed(()=>Aw[e.type]);return()=>{const{value:o}=n;return Vue.createVNode("uni-icon",{ref:t},[o&&o.d&&ce(o.d,e.color||o.c,Je(e.size))],512)}}}),kw={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},$o={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},Iw={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]},Nw=X({name:"Image",props:kw,setup(e,{emit:t}){const n=Vue.ref(null),o=xw(n,e),i=ve(n,t),{fixSize:r}=Rw(n,e,o);return Mw(o,e,n,r,i),()=>Vue.createVNode("uni-image",{ref:n},[Vue.createVNode("div",{style:o.modeStyle},null,4),$o[e.mode]?Vue.createVNode(Ge,{onResize:r},null,8,["onResize"]):Vue.createVNode("span",null,null)],512)}});function xw(e,t){const n=Vue.ref(""),o=Vue.computed(()=>{let r="auto",a="";const s=Iw[t.mode];return s?(s[0]&&(a=s[0]),s[1]&&(r=s[1])):(a="0% 0%",r="100% 100%"),`background-image:${n.value?'url("'+n.value+'")':"none"};background-position:${a};background-size:${r};`}),i=Vue.reactive({rootEl:e,src:Vue.computed(()=>t.src?Q(t.src):""),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:o,imgSrc:n});return Vue.onMounted(()=>{const r=e.value;i.origWidth=r.clientWidth||0,i.origHeight=r.clientHeight||0}),i}function Mw(e,t,n,o,i){let r,a;const s=(u=0,d=0,f="")=>{e.origWidth=u,e.origHeight=d,e.imgSrc=f},l=u=>{if(!u){c(),s();return}r=r||new Image,r.onload=d=>{const{width:f,height:h}=r;s(f,h,u),Vue.nextTick(()=>{o()}),r.draggable=t.draggable,a&&a.remove(),a=r,n.value.appendChild(r),c(),i("load",d,{width:f,height:h})},r.onerror=d=>{s(),c(),i("error",d,{errMsg:`GET ${e.src} 404 (Not Found)`})},r.src=u},c=()=>{r&&(r.onload=null,r.onerror=null,r=null)};Vue.watch(()=>e.src,u=>l(u)),Vue.watch(()=>e.imgSrc,u=>{!u&&a&&(a.remove(),a=null)}),Vue.onMounted(()=>l(e.src)),Vue.onBeforeUnmount(()=>c())}const Pw=navigator.vendor==="Google Inc.";function Ow(e){return Pw&&e>10&&(e=Math.round(e/2)*2),e}function Rw(e,t,n){const o=()=>{const{mode:r}=t,a=$o[r];if(!a)return;const{origWidth:s,origHeight:l}=n,c=s&&l?s/l:0;if(!c)return;const u=e.value,d=u[a[0]];d&&(u.style[a[1]]=Ow(a[2](d,c))+"px")},i=()=>{const{style:r}=e.value,{origStyle:{width:a,height:s}}=n;r.width=a,r.height=s};return Vue.watch(()=>t.mode,(r,a)=>{$o[a]&&i(),$o[r]&&o()}),{fixSize:o,resetSize:i}}function Lw(e,t){let n=0,o,i;const r=function(...a){const s=Date.now();if(clearTimeout(o),i=()=>{i=null,n=s,e.apply(this,a)},s-n<t){o=setTimeout(i,t-(s-n));return}i()};return r.cancel=function(){clearTimeout(o),i=null},r.flush=function(){clearTimeout(o),i&&i()},r}const Bw=kt(!0),zo=[];let Wo=0,lu=!1;const cu=e=>zo.forEach(t=>t.userAction=e);function uu(e={userAction:!1}){lu||(["touchstart","touchmove","touchend","mousedown","mouseup"].forEach(n=>{document.addEventListener(n,function(){!Wo&&cu(!0),Wo++,setTimeout(()=>{!--Wo&&cu(!1)},0)},Bw)}),lu=!0),zo.push(e)}function Dw(e){const t=zo.indexOf(e);t>=0&&zo.splice(t,1)}const kr=()=>!!Wo;function du(){const e=Vue.reactive({userAction:!1});return Vue.onMounted(()=>{uu(e)}),Vue.onBeforeUnmount(()=>{Dw(e)}),{state:e}}function fu(){const e=Vue.reactive({attrs:{}});return Vue.onMounted(()=>{let t=Vue.getCurrentInstance();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&t.proxy.$mpType==="page"?null:t.parent}}),{state:e}}function Uw(e,t){const n=Vue.inject(Fe,!1);if(!n)return;const o=Vue.getCurrentInstance(),i={submit(){const r=o.proxy;return[r[e],J(t)?r[t]:t.value]},reset(){J(t)?o.proxy[t]="":t.value=""}};n.addField(i),Vue.onBeforeUnmount(()=>{n.removeField(i)})}function Fw(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}const $w=function(){os(ft(),"getSelectedTextRange",Fw)};function Ir(e,t,n){return t==="number"&&isNaN(Number(e))&&(e=""),e==null?"":String(e)}const zw=["none","text","decimal","numeric","tel","search","email","url"],hu=B({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~zw.indexOf(e)},cursorColor:{type:String,default:""}},Zc),gu=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend",...eu];function Ww(e,t,n){const o=Vue.ref(null),i=ve(t,n),r=Vue.computed(()=>{const d=Number(e.selectionStart);return isNaN(d)?-1:d}),a=Vue.computed(()=>{const d=Number(e.selectionEnd);return isNaN(d)?-1:d}),s=Vue.computed(()=>{const d=Number(e.cursor);return isNaN(d)?-1:d}),l=Vue.computed(()=>{var d=Number(e.maxlength);return isNaN(d)?140:d});let c="";c=Ir(e.modelValue,e.type)||Ir(e.value,e.type);const u=Vue.reactive({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:r,selectionEnd:a,cursor:s});return Vue.watch(()=>u.focus,d=>n("update:focus",d)),Vue.watch(()=>u.maxlength,d=>u.value=u.value.slice(0,d),{immediate:!1}),{fieldRef:o,state:u,trigger:i}}function Hw(e,t,n,o){let i=null;i=Ti(s=>{t.value=Ir(s,e.type)},100,{setTimeout,clearTimeout}),Vue.watch(()=>e.modelValue,i),Vue.watch(()=>e.value,i);const r=Lw((s,l)=>{i.cancel(),n("update:modelValue",l.value),n("update:value",l.value),o("input",s,l)},100),a=(s,l,c)=>{i.cancel(),r(s,l),c&&r.flush()};return Vue.onBeforeMount(()=>{i.cancel(),r.cancel()}),{trigger:o,triggerInput:a}}function qw(e,t){du();const n=Vue.computed(()=>e.autoFocus||e.focus);function o(){if(!n.value)return;const r=t.value;if(!r){setTimeout(o,100);return}r.focus()}function i(){const r=t.value;r&&r.blur()}Vue.watch(()=>e.focus,r=>{r?o():i()}),Vue.onMounted(()=>{n.value&&Vue.nextTick(o)})}function Gw(e,t,n,o,i,r){function a(){const u=e.value;u&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&u.type!=="number"&&(u.selectionStart=t.selectionStart,u.selectionEnd=t.selectionEnd)}function s(){const u=e.value;u&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&u.type!=="number"&&(u.selectionEnd=u.selectionStart=t.cursor)}function l(u){return u.type==="number"?null:u.selectionEnd}function c(){const u=e.value;if(!u)return;const d=function(v){t.focus=!0,o("focus",v,{value:t.value}),a(),s()},f=function(v,m){v.stopPropagation(),!(G(r)&&r(v,t)===!1)&&(t.value=u.value,(!t.composing||!n.ignoreCompositionEvent)&&i(v,{value:u.value,cursor:l(u)},m))},h=function(v){t.composing&&(t.composing=!1,f(v,!0)),t.focus=!1,o("blur",v,{value:t.value,cursor:l(v.target)})};u.addEventListener("change",v=>v.stopPropagation()),u.addEventListener("focus",d),u.addEventListener("blur",h),u.addEventListener("input",f),u.addEventListener("compositionstart",v=>{v.stopPropagation(),t.composing=!0,_(v)}),u.addEventListener("compositionend",v=>{v.stopPropagation(),t.composing&&(t.composing=!1,f(v)),_(v)}),u.addEventListener("compositionupdate",_);function _(v){n.ignoreCompositionEvent||o(v.type,v,{value:v.data})}}Vue.watch([()=>t.selectionStart,()=>t.selectionEnd],a),Vue.watch(()=>t.cursor,s),Vue.watch(()=>e.value,c)}function pu(e,t,n,o){$w();const{fieldRef:i,state:r,trigger:a}=Ww(e,t,n),{triggerInput:s}=Hw(e,r,n,a);qw(e,i),tu(e,i);const{state:l}=fu();Uw("name",r),Gw(i,r,e,a,s,o);const c=String(navigator.vendor).indexOf("Apple")===0&&CSS.supports("image-orientation:from-image");return{fieldRef:i,state:r,scopedAttrsState:l,fixDisabledColor:c,trigger:a}}const jw=B({},hu,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),Yw=te(()=>{{const e=navigator.userAgent;let t="";const n=e.match(/OS\s([\w_]+)\slike/);if(n)t=n[1].replace(/_/g,".");else if(/Macintosh|Mac/i.test(e)&&navigator.maxTouchPoints>0){const o=e.match(/Version\/(\S*)\b/);o&&(t=o[1])}return!!t&&parseInt(t)>=16&&parseFloat(t)<17.2}});function mu(e,t,n,o,i){if(t.value){if(e.data==="."){if(t.value.slice(-1)===".")return n.value=o.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",i&&(i.fn=()=>{n.value=o.value=t.value=t.value.slice(0,-1),o.removeEventListener("blur",i.fn)},o.addEventListener("blur",i.fn)),!1}else if(e.inputType==="deleteContentBackward"&&Yw()&&t.value.slice(-2,-1)===".")return t.value=n.value=o.value=t.value.slice(0,-2),!0}}function Xw(e,t){if(t.value==="number"){const n=typeof e.modelValue=="undefined"?e.value:e.modelValue,o=Vue.ref(typeof n!="undefined"&&n!==null?n.toLocaleString():"");return Vue.watch(()=>e.modelValue,i=>{o.value=typeof i!="undefined"&&i!==null?i.toLocaleString():""}),Vue.watch(()=>e.value,i=>{o.value=typeof i!="undefined"&&i!==null?i.toLocaleString():""}),o}else return Vue.ref("")}const Ho=X({name:"Input",props:jw,emits:["confirm",...gu],setup(e,{emit:t,expose:n}){const o=["text","number","idcard","digit","password","tel"],i=["off","one-time-code"],r=Vue.computed(()=>{let b="";switch(e.type){case"text":b="text",e.confirmType==="search"&&(b="search");break;case"idcard":b="text";break;case"digit":b="number";break;default:b=o.includes(e.type)?e.type:"text";break}return e.password?"password":b}),a=Vue.computed(()=>{const b=i.indexOf(e.textContentType),g=i.indexOf(Ct(e.textContentType));return i[b!==-1?b:g!==-1?g:0]});let s=Xw(e,r),l={fn:null};const c=Vue.ref(null),{fieldRef:u,state:d,scopedAttrsState:f,fixDisabledColor:h,trigger:_}=pu(e,c,t,(b,g)=>{const p=b.target;if(r.value==="number"){if(l.fn&&(p.removeEventListener("blur",l.fn),l.fn=null),p.validity&&!p.validity.valid){if((!s.value||!p.value)&&b.data==="-"||s.value[0]==="-"&&b.inputType==="deleteContentBackward")return s.value="-",g.value="",l.fn=()=>{s.value=p.value=""},p.addEventListener("blur",l.fn),!1;const S=mu(b,s,g,p,l);return typeof S=="boolean"?S:(s.value=g.value=p.value=s.value==="-"?"":s.value,!1)}else{const S=mu(b,s,g,p,l);if(typeof S=="boolean")return S;s.value=p.value}const V=g.maxlength;if(V>0&&p.value.length>V)return p.value=p.value.slice(0,V),g.value=p.value,(e.modelValue!==void 0&&e.modelValue!==null?e.modelValue.toString():"")!==p.value}});Vue.watch(()=>d.value,b=>{e.type==="number"&&!(s.value==="-"&&b==="")&&(s.value=b.toString())});const v=["number","digit"],m=Vue.computed(()=>v.includes(e.type)?e.step:"");function T(b){if(b.key!=="Enter")return;const g=b.target;b.stopPropagation(),_("confirm",b,{value:g.value}),!e.confirmHold&&g.blur()}return n({$triggerInput:b=>{t("update:modelValue",b.value),t("update:value",b.value),d.value=b.value}}),()=>{let b=e.disabled&&h?Vue.createVNode("input",{key:"disabled-input",ref:u,value:d.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:d.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:g=>g.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):Vue.createVNode("input",{key:"input",ref:u,value:d.value,onInput:g=>{d.value=g.target.value.toString()},disabled:!!e.disabled,type:r.value,maxlength:d.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:e.type==="number"?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:a.value,onKeyup:T,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return Vue.createVNode("uni-input",{ref:c},[Vue.createVNode("div",{class:"uni-input-wrapper"},[Vue.withDirectives(Vue.createVNode("div",Vue.mergeProps(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Vue.vShow,!(d.value.length||s.value==="-"||s.value.includes("."))]]),e.confirmType==="search"?Vue.createVNode("form",{action:"",onSubmit:g=>g.preventDefault(),class:"uni-input-form"},[b],40,["onSubmit"]):b])],512)}}});function Jw(e){return Object.keys(e).map(t=>[t,e[t]])}const Kw=["class","style"],Qw=/^on[A-Z]+/,qo=(e={})=>{const{excludeListeners:t=!1,excludeKeys:n=[]}=e,o=Vue.getCurrentInstance(),i=Vue.shallowRef({}),r=Vue.shallowRef({}),a=Vue.shallowRef({}),s=n.concat(Kw);return o.attrs=Vue.reactive(o.attrs),Vue.watchEffect(()=>{const l=Jw(o.attrs).reduce((c,[u,d])=>(s.includes(u)?c.exclude[u]=d:Qw.test(u)?(t||(c.attrs[u]=d),c.listeners[u]=d):c.attrs[u]=d,c),{exclude:{},attrs:{},listeners:{}});i.value=l.attrs,r.value=l.listeners,a.value=l.exclude}),{$attrs:i,$listeners:r,$excludeAttrs:a}};function Ht(e){const t=[];return K(e)&&e.forEach(n=>{Vue.isVNode(n)?n.type===Vue.Fragment?t.push(...Ht(n.children)):t.push(n):K(n)&&t.push(...Ht(n))}),t}const vu=X({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.ref(!1);let{setContexts:i,events:r}=Zw(e,n);const{$listeners:a,$attrs:s,$excludeAttrs:l}=qo(),c=a.value;["onTouchstart","onTouchmove","onTouchend"].forEach(m=>{let T=c[m],b=r[`_${m}`];c[m]=T?[].concat(T,b):b}),Vue.onMounted(()=>{r._resize(),o.value=!0});let d=[];const f=[];function h(){const m=[];for(let T=0;T<d.length;T++){let b=d[T];b=b.el;const g=f.find(p=>b===p.rootRef.value);g&&m.push(Vue.markRaw(g))}i(m)}const _=m=>{f.push(m),h()},v=m=>{const T=f.indexOf(m);T>=0&&(f.splice(T,1),h())};return Vue.provide("_isMounted",o),Vue.provide("movableAreaRootRef",n),Vue.provide("addMovableViewContext",_),Vue.provide("removeMovableViewContext",v),()=>{const m=t.default&&t.default();return d=Ht(m),Vue.createVNode("uni-movable-area",Vue.mergeProps({ref:n},s.value,l.value,c),[Vue.createVNode(Ge,{onResize:r._resize},null,8,["onResize"]),d],16)}}});function _u(e){return Math.sqrt(e.x*e.x+e.y*e.y)}function Zw(e,t){const n=Vue.ref(0),o=Vue.ref(0),i=Vue.reactive({x:null,y:null}),r=Vue.ref(null);let a=null,s=[];function l(v){v&&v!==1&&(e.scaleArea?s.forEach(function(m){m._setScale(v)}):a&&a._setScale(v))}function c(v,m=s){let T=t.value;function b(g){for(let p=0;p<m.length;p++){const V=m[p];if(g===V.rootRef.value)return V}return g===T||g===document.body||g===document?null:b(g.parentNode)}return b(v)}const u=ne(v=>{let m=v.touches;if(m&&m.length>1){let T={x:m[1].pageX-m[0].pageX,y:m[1].pageY-m[0].pageY};if(r.value=_u(T),i.x=T.x,i.y=T.y,!e.scaleArea){let b=c(m[0].target),g=c(m[1].target);a=b&&b===g?b:null}}}),d=ne(v=>{let m=v.touches;if(m&&m.length>1){v.preventDefault();let T={x:m[1].pageX-m[0].pageX,y:m[1].pageY-m[0].pageY};if(i.x!==null&&r.value&&r.value>0){let b=_u(T)/r.value;l(b)}i.x=T.x,i.y=T.y}}),f=ne(v=>{let m=v.touches;m&&m.length||v.changedTouches&&(i.x=0,i.y=0,r.value=null,e.scaleArea?s.forEach(function(T){T._endScale()}):a&&a._endScale())});function h(){_(),s.forEach(function(v,m){v.setParent()})}function _(){let v=window.getComputedStyle(t.value),m=t.value.getBoundingClientRect();n.value=m.width-["Left","Right"].reduce(function(T,b){const g="border"+b+"Width",p="padding"+b;return T+parseFloat(v[g])+parseFloat(v[p])},0),o.value=m.height-["Top","Bottom"].reduce(function(T,b){const g="border"+b+"Width",p="padding"+b;return T+parseFloat(v[g])+parseFloat(v[p])},0)}return Vue.provide("movableAreaWidth",n),Vue.provide("movableAreaHeight",o),{setContexts(v){s=v},events:{_onTouchstart:u,_onTouchmove:d,_onTouchend:f,_resize:h}}}const En=function(e,t,n,o){e.addEventListener(t,i=>{G(n)&&n(i)===!1&&((typeof i.cancelable=="undefined"||i.cancelable)&&i.preventDefault(),i.stopPropagation())},{passive:!1})};let yu,bu;function kn(e,t,n){Vue.onBeforeUnmount(()=>{document.removeEventListener("mousemove",yu),document.removeEventListener("mouseup",bu)});let o=0,i=0,r=0,a=0;const s=function(h,_,v,m){if(t({cancelable:h.cancelable,target:h.target,currentTarget:h.currentTarget,preventDefault:h.preventDefault.bind(h),stopPropagation:h.stopPropagation.bind(h),touches:h.touches,changedTouches:h.changedTouches,detail:{state:_,x:v,y:m,dx:v-o,dy:m-i,ddx:v-r,ddy:m-a,timeStamp:h.timeStamp}})===!1)return!1};let l=null,c,u;En(e,"touchstart",function(h){if(c=!0,h.touches.length===1&&!l)return l=h,o=r=h.touches[0].pageX,i=a=h.touches[0].pageY,s(h,"start",o,i)}),En(e,"mousedown",function(h){if(u=!0,!c&&!l)return l=h,o=r=h.pageX,i=a=h.pageY,s(h,"start",o,i)}),En(e,"touchmove",function(h){if(h.touches.length===1&&l){const _=s(h,"move",h.touches[0].pageX,h.touches[0].pageY);return r=h.touches[0].pageX,a=h.touches[0].pageY,_}});const d=yu=function(h){if(!c&&u&&l){const _=s(h,"move",h.pageX,h.pageY);return r=h.pageX,a=h.pageY,_}};document.addEventListener("mousemove",d),En(e,"touchend",function(h){if(h.touches.length===0&&l)return c=!1,l=null,s(h,"end",h.changedTouches[0].pageX,h.changedTouches[0].pageY)});const f=bu=function(h){if(u=!1,!c&&l)return l=null,s(h,"end",h.pageX,h.pageY)};document.addEventListener("mouseup",f),En(e,"touchcancel",function(h){if(l){c=!1;const _=l;return l=null,s(h,n?"cancel":"end",_.touches[0].pageX,_.touches[0].pageY)}})}function Go(e,t,n){return e>t-n&&e<t+n}function mt(e,t){return Go(e,0,t)}function Nr(){}Nr.prototype.x=function(e){return Math.sqrt(e)};function We(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}We.prototype.setV=function(e,t){const n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=new Date().getTime()},We.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},We.prototype.s=function(e){e===void 0&&(e=(new Date().getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);let t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},We.prototype.ds=function(e){return e===void 0&&(e=(new Date().getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},We.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},We.prototype.dt=function(){return-this._x_v/this._x_a},We.prototype.done=function(){const e=Go(this.s().x,this._endPositionX)||Go(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},We.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},We.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t};function Ie(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}Ie.prototype._solve=function(e,t){const n=this._c,o=this._m,i=this._k,r=n*n-4*o*i;if(r===0){const u=-n/(2*o),d=e,f=t/(u*e);return{x:function(h){return(d+f*h)*Math.pow(Math.E,u*h)},dx:function(h){const _=Math.pow(Math.E,u*h);return u*(d+f*h)*_+f*_}}}if(r>0){const u=(-n-Math.sqrt(r))/(2*o),d=(-n+Math.sqrt(r))/(2*o),f=(t-u*e)/(d-u),h=e-f;return{x:function(_){let v,m;return _===this._t&&(v=this._powER1T,m=this._powER2T),this._t=_,v||(v=this._powER1T=Math.pow(Math.E,u*_)),m||(m=this._powER2T=Math.pow(Math.E,d*_)),h*v+f*m},dx:function(_){let v,m;return _===this._t&&(v=this._powER1T,m=this._powER2T),this._t=_,v||(v=this._powER1T=Math.pow(Math.E,u*_)),m||(m=this._powER2T=Math.pow(Math.E,d*_)),h*u*v+f*d*m}}}const a=Math.sqrt(4*o*i-n*n)/(2*o),s=-n/2*o,l=e,c=(t-s*e)/a;return{x:function(u){return Math.pow(Math.E,s*u)*(l*Math.cos(a*u)+c*Math.sin(a*u))},dx:function(u){const d=Math.pow(Math.E,s*u),f=Math.cos(a*u),h=Math.sin(a*u);return d*(c*a*f-l*a*h)+s*d*(c*h+l*f)}}},Ie.prototype.x=function(e){return e===void 0&&(e=(new Date().getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Ie.prototype.dx=function(e){return e===void 0&&(e=(new Date().getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Ie.prototype.setEnd=function(e,t,n){if(n||(n=new Date().getTime()),e!==this._endPosition||!mt(t,.1)){t=t||0;let o=this._endPosition;this._solution&&(mt(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),o=this._solution.x((n-this._startTime)/1e3),mt(t,.1)&&(t=0),mt(o,.1)&&(o=0),o+=this._endPosition),this._solution&&mt(o-e,.1)&&mt(t,.1)||(this._endPosition=e,this._solution=this._solve(o-this._endPosition,t),this._startTime=n)}},Ie.prototype.snap=function(e){this._startTime=new Date().getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Ie.prototype.done=function(e){return e||(e=new Date().getTime()),Go(this.x(),this._endPosition,.1)&&mt(this.dx(),.1)},Ie.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=new Date().getTime())},Ie.prototype.springConstant=function(){return this._k},Ie.prototype.damping=function(){return this._c},Ie.prototype.configuration=function(){function e(n,o){n.reconfigure(1,o,n.damping())}function t(n,o){n.reconfigure(1,n.springConstant(),o)}return[{label:"Spring Constant",read:this.springConstant.bind(this),write:e.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:t.bind(this,this),min:1,max:500}]};function In(e,t,n){this._springX=new Ie(e,t,n),this._springY=new Ie(e,t,n),this._springScale=new Ie(e,t,n),this._startTime=0}In.prototype.setEnd=function(e,t,n,o){const i=new Date().getTime();this._springX.setEnd(e,o,i),this._springY.setEnd(t,o,i),this._springScale.setEnd(n,o,i),this._startTime=i},In.prototype.x=function(){const e=(new Date().getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},In.prototype.done=function(){const e=new Date().getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},In.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};const e1={direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.1},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}};function wu(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}const Su=X({name:"MovableView",props:e1,emits:["change","scale"],setup(e,{slots:t,emit:n}){const o=Vue.ref(null),i=ve(o,n),{setParent:r}=i1(e,i,o);return()=>Vue.createVNode("uni-movable-view",{ref:o},[Vue.createVNode(Ge,{onResize:r},null,8,["onResize"]),t.default&&t.default()],512)}});let xr=!1;function Tu(e){xr||(xr=!0,requestAnimationFrame(function(){e(),xr=!1}))}function Vu(e,t){if(e===t)return 0;let n=e.offsetLeft;return e.offsetParent?n+=Vu(e.offsetParent,t):0}function Cu(e,t){if(e===t)return 0;let n=e.offsetTop;return e.offsetParent?n+=Cu(e.offsetParent,t):0}function Au(e,t,n){let o={id:0,cancelled:!1},i=function(a){a&&a.id&&cancelAnimationFrame(a.id),a&&(a.cancelled=!0)};function r(a,s,l,c){if(!a||!a.cancelled){l(s);let u=s.done();u||a.cancelled||(a.id=requestAnimationFrame(r.bind(null,a,s,l,c))),u&&c&&c(s)}}return r(o,e,t,n),{cancel:i.bind(null,o),model:e}}function jo(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}function t1(e,t,n){const o=Vue.inject("movableAreaWidth",Vue.ref(0)),i=Vue.inject("movableAreaHeight",Vue.ref(0)),r=Vue.inject("movableAreaRootRef"),a={x:0,y:0},s={x:0,y:0},l=Vue.ref(0),c=Vue.ref(0),u=Vue.ref(0),d=Vue.ref(0),f=Vue.ref(0),h=Vue.ref(0);function _(){let T=0-a.x+s.x,b=o.value-l.value-a.x-s.x;u.value=Math.min(T,b),f.value=Math.max(T,b);let g=0-a.y+s.y,p=i.value-c.value-a.y-s.y;d.value=Math.min(g,p),h.value=Math.max(g,p)}function v(){a.x=Vu(e.value,r.value),a.y=Cu(e.value,r.value)}function m(T){T=T||t.value,T=n(T);let b=e.value.getBoundingClientRect();c.value=b.height/t.value,l.value=b.width/t.value;let g=c.value*T,p=l.value*T;s.x=(p-l.value)/2,s.y=(g-c.value)/2}return{_updateBoundary:_,_updateOffset:v,_updateWH:m,_scaleOffset:s,minX:u,minY:d,maxX:f,maxY:h}}function n1(e,t,n,o,i,r,a,s,l,c,u,d,f,h){const _=Vue.computed(()=>{let C=Number(t.damping);return isNaN(C)?20:C}),v=Vue.computed(()=>t.direction==="all"||t.direction==="horizontal"),m=Vue.computed(()=>t.direction==="all"||t.direction==="vertical"),T=Vue.ref(jo(t.x)),b=Vue.ref(jo(t.y));Vue.watch(()=>t.x,C=>{T.value=jo(C)}),Vue.watch(()=>t.y,C=>{b.value=jo(C)}),Vue.watch(T,C=>{x(C)}),Vue.watch(b,C=>{E(C)});const g=new In(1,9*Math.pow(_.value,2)/40,_.value);function p(C,I){let M=!1;return C>i.value?(C=i.value,M=!0):C<a.value&&(C=a.value,M=!0),I>r.value?(I=r.value,M=!0):I<s.value&&(I=s.value,M=!0),{x:C,y:I,outOfBounds:M}}function V(){d&&d.cancel(),u&&u.cancel()}function S(C,I,M,F,k,L){V(),v.value||(C=l.value),m.value||(I=c.value),t.scale||(M=o.value);let z=p(C,I);if(C=z.x,I=z.y,!t.animation){w(C,I,M,F,k,L);return}g._springX._solution=null,g._springY._solution=null,g._springScale._solution=null,g._springX._endPosition=l.value,g._springY._endPosition=c.value,g._springScale._endPosition=o.value,g.setEnd(C,I,M,1),u=Au(g,function(){let N=g.x(),P=N.x,O=N.y,$=N.scale;w(P,O,$,F,k,L)},function(){u.cancel()})}function w(C,I,M,F="",k,L){C!==null&&C.toString()!=="NaN"&&typeof C=="number"||(C=l.value||0),I!==null&&I.toString()!=="NaN"&&typeof I=="number"||(I=c.value||0),C=Number(C.toFixed(1)),I=Number(I.toFixed(1)),M=Number(M.toFixed(1)),l.value===C&&c.value===I||k||h("change",{},{x:wu(C,n.x),y:wu(I,n.y),source:F}),t.scale||(M=o.value),M=f(M),M=+M.toFixed(3),L&&M!==o.value&&h("scale",{},{x:C,y:I,scale:M});let z="translateX("+C+"px) translateY("+I+"px) translateZ(0px) scale("+M+")";e.value&&(e.value.style.transform=z,e.value.style.webkitTransform=z,l.value=C,c.value=I,o.value=M)}function A(C){let I=p(l.value,c.value),M=I.x,F=I.y,k=I.outOfBounds;return k&&S(M,F,o.value,C),k}function x(C){if(v.value){if(C+n.x===l.value)return l;u&&u.cancel(),S(C+n.x,b.value+n.y,o.value)}return C}function E(C){if(m.value){if(C+n.y===c.value)return c;u&&u.cancel(),S(T.value+n.x,C+n.y,o.value)}return C}return{FAandSFACancel:V,_getLimitXY:p,_animationTo:S,_setTransform:w,_revise:A,dampingNumber:_,xMove:v,yMove:m,xSync:T,ySync:b,_STD:g}}function o1(e,t,n,o,i,r,a,s,l,c){const u=Vue.computed(()=>{let U=Number(e.scaleMin);return isNaN(U)?.1:U}),d=Vue.computed(()=>{let U=Number(e.scaleMax);return isNaN(U)?10:U}),f=Vue.ref(Number(e.scaleValue)||1);Vue.watch(f,U=>{$(U)}),Vue.watch(u,()=>{O()}),Vue.watch(d,()=>{O()}),Vue.watch(()=>e.scaleValue,U=>{f.value=Number(U)||0});const{_updateBoundary:h,_updateOffset:_,_updateWH:v,_scaleOffset:m,minX:T,minY:b,maxX:g,maxY:p}=t1(t,o,P),{FAandSFACancel:V,_getLimitXY:S,_animationTo:w,_setTransform:A,_revise:x,dampingNumber:E,xMove:C,yMove:I,xSync:M,ySync:F,_STD:k}=n1(t,e,m,o,g,p,T,b,a,s,l,c,P,n);function L(U,re){if(e.scale){U=P(U),v(U),h();const ae=S(a.value,s.value),Me=ae.x,De=ae.y;re?w(Me,De,U,"",!0,!0):Tu(function(){A(Me,De,U,"",!0,!0)})}}function z(){r.value=!0}function N(U){i.value=U}function P(U){return U=Math.max(.1,u.value,U),U=Math.min(10,d.value,U),U}function O(){if(!e.scale)return!1;L(o.value,!0),N(o.value)}function $(U){return e.scale?(U=P(U),L(U,!0),N(U),U):!1}function H(){r.value=!1,N(o.value)}function q(U){U&&(U=i.value*U,z(),L(U))}return{_updateOldScale:N,_endScale:H,_setScale:q,scaleValueSync:f,_updateBoundary:h,_updateOffset:_,_updateWH:v,_scaleOffset:m,minX:T,minY:b,maxX:g,maxY:p,FAandSFACancel:V,_getLimitXY:S,_animationTo:w,_setTransform:A,_revise:x,dampingNumber:E,xMove:C,yMove:I,xSync:M,ySync:F,_STD:k}}function i1(e,t,n){const o=Vue.inject("_isMounted",Vue.ref(!1)),i=Vue.inject("addMovableViewContext",()=>{}),r=Vue.inject("removeMovableViewContext",()=>{});let a=Vue.ref(1),s=Vue.ref(1),l=Vue.ref(!1),c=Vue.ref(0),u=Vue.ref(0),d=null,f=null,h=!1,_,v,m=null,T=null;const b=new Nr,g=new Nr,p={historyX:[0,0],historyY:[0,0],historyT:[0,0]},V=Vue.computed(()=>{let ee=Number(e.friction);return isNaN(ee)||ee<=0?2:ee}),S=new We(1,V.value);Vue.watch(()=>e.disabled,()=>{st()});const{_updateOldScale:w,_endScale:A,_setScale:x,scaleValueSync:E,_updateBoundary:C,_updateOffset:I,_updateWH:M,_scaleOffset:F,minX:k,minY:L,maxX:z,maxY:N,FAandSFACancel:P,_getLimitXY:O,_setTransform:$,_revise:H,dampingNumber:q,xMove:U,yMove:re,xSync:ae,ySync:Me,_STD:De}=o1(e,n,t,a,s,l,c,u,d,f);function st(){l.value||e.disabled||(P(),p.historyX=[0,0],p.historyY=[0,0],p.historyT=[0,0],U.value&&(_=c.value),re.value&&(v=u.value),n.value.style.willChange="transform",m=null,T=null,h=!0)}function Hn(ee){if(!l.value&&!e.disabled&&h){let de=c.value,se=u.value;if(T===null&&(T=Math.abs(ee.detail.dx/ee.detail.dy)>1?"htouchmove":"vtouchmove"),U.value&&(de=ee.detail.dx+_,p.historyX.shift(),p.historyX.push(de),!re.value&&m===null&&(m=Math.abs(ee.detail.dx/ee.detail.dy)<1)),re.value&&(se=ee.detail.dy+v,p.historyY.shift(),p.historyY.push(se),!U.value&&m===null&&(m=Math.abs(ee.detail.dy/ee.detail.dx)<1)),p.historyT.shift(),p.historyT.push(ee.detail.timeStamp),!m){ee.preventDefault();let Ve="touch";de<k.value?e.outOfBounds?(Ve="touch-out-of-bounds",de=k.value-b.x(k.value-de)):de=k.value:de>z.value&&(e.outOfBounds?(Ve="touch-out-of-bounds",de=z.value+b.x(de-z.value)):de=z.value),se<L.value?e.outOfBounds?(Ve="touch-out-of-bounds",se=L.value-g.x(L.value-se)):se=L.value:se>N.value&&(e.outOfBounds?(Ve="touch-out-of-bounds",se=N.value+g.x(se-N.value)):se=N.value),Tu(function(){$(de,se,a.value,Ve)})}}}function _e(){if(!l.value&&!e.disabled&&h&&(n.value.style.willChange="auto",h=!1,!m&&!H("out-of-bounds")&&e.inertia)){const ee=1e3*(p.historyX[1]-p.historyX[0])/(p.historyT[1]-p.historyT[0]),de=1e3*(p.historyY[1]-p.historyY[0])/(p.historyT[1]-p.historyT[0]),se=c.value,Ve=u.value;S.setV(ee,de),S.setS(se,Ve);const qn=S.delta().x,Gn=S.delta().y;let Tt=qn+se,Vt=Gn+Ve;Tt<k.value?(Tt=k.value,Vt=Ve+(k.value-se)*Gn/qn):Tt>z.value&&(Tt=z.value,Vt=Ve+(z.value-se)*Gn/qn),Vt<L.value?(Vt=L.value,Tt=se+(L.value-Ve)*qn/Gn):Vt>N.value&&(Vt=N.value,Tt=se+(N.value-Ve)*qn/Gn),S.setEnd(Tt,Vt),f=Au(S,function(){let ng=S.s(),DC=ng.x,UC=ng.y;$(DC,UC,a.value,"friction")},function(){f.cancel()})}!e.outOfBounds&&!e.inertia&&P()}function ya(){if(!o.value)return;P();let ee=e.scale?E.value:1;I(),M(ee),C();let de=O(ae.value+F.x,Me.value+F.y),se=de.x,Ve=de.y;$(se,Ve,ee,"",!0),w(ee)}return Vue.onMounted(()=>{kn(n.value,de=>{switch(de.detail.state){case"start":st();break;case"move":Hn(de);break;case"end":_e()}}),ya(),S.reconfigure(1,V.value),De.reconfigure(1,9*Math.pow(q.value,2)/40,q.value),n.value.style.transformOrigin="center";const ee={rootRef:n,setParent:ya,_endScale:A,_setScale:x};i(ee),Vue.onUnmounted(()=>{r(ee)})}),Vue.onUnmounted(()=>{P()}),{setParent:ya}}const r1=["navigate","redirect","switchTab","reLaunch","navigateBack"],a1=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],s1=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],l1={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator(e){return!!~r1.indexOf(e)}},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator(e){return!e||a1.concat(s1).includes(e)}},animationDuration:{type:[String,Number],default:300}};function c1(e){return()=>{if(e.openType!=="navigateBack"&&!e.url){console.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab");return}const t=parseInt(e.animationDuration);switch(e.openType){case"navigate":uni.navigateTo({url:e.url,animationType:e.animationType||"pop-in",animationDuration:t});break;case"redirect":uni.redirectTo({url:e.url,exists:e.exists});break;case"switchTab":uni.switchTab({url:e.url});break;case"reLaunch":uni.reLaunch({url:e.url});break;case"navigateBack":uni.navigateBack({delta:e.delta,animationType:e.animationType||"pop-out",animationDuration:t});break}}}const u1=X({name:"Navigator",inheritAttrs:!1,compatConfig:{MODE:3},props:B({},l1,{renderLink:{type:Boolean,default:!0}}),setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.getCurrentInstance(),i=o&&o.vnode.scopeId||"",{hovering:r,binding:a}=Li(e),s=c1(e);return()=>{const{hoverClass:l,url:c}=e,u=e.hoverClass&&e.hoverClass!=="none",d=e.renderLink?Vue.createVNode("a",{class:"navigator-wrap",href:c,onClick:Xe,onMousedown:Xe},[t.default&&t.default()],40,["href","onClick","onMousedown"]):t.default&&t.default();return Vue.createVNode("uni-navigator",Vue.mergeProps({class:u&&r.value?l:"",ref:n},u&&a,o?o.attrs:{},{[i]:""},{onClick:s}),[d],16,["onClick"])}}}),d1={value:{type:Array,default(){return[]},validator:function(e){return K(e)&&e.filter(t=>typeof t=="number").length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}};function f1(e){const t=Vue.reactive([...e.value]),n=Vue.reactive({value:t,height:34});return Vue.watch(()=>e.value,(o,i)=>{n.value.length=o.length,o.forEach((r,a)=>{r!==n.value[a]&&n.value.splice(a,1,r)})}),n}const Eu=X({name:"PickerView",props:d1,emits:["change","pickstart","pickend","update:value"],setup(e,{slots:t,emit:n}){const o=Vue.ref(null),i=Vue.ref(null),r=ve(o,n),a=f1(e),s=Vue.ref(null),l=()=>{const h=s.value;h&&(a.height=h.$el.offsetHeight)};Vue.onMounted(l);let c=Vue.ref([]),u=Vue.ref([]);function d(h){let _=u.value;_=_.filter(m=>m.type!==Vue.Comment);let v=_.indexOf(h);return v!==-1?v:c.value.indexOf(h)}const f=function(h){return Vue.computed({get(){const v=d(h.vnode);return a.value[v]||0},set(v){const m=d(h.vnode);if(m<0)return;if(a.value[m]!==v){a.value[m]=v;const b=a.value.map(g=>g);n("update:value",b),r("change",{},{value:b})}}})};return Vue.provide("getPickerViewColumn",f),Vue.provide("pickerViewProps",e),Vue.provide("pickerViewState",a),()=>{const h=t.default&&t.default();{const _=Ht(h);c.value=_,Vue.nextTick(()=>{u.value=_})}return Vue.createVNode("uni-picker-view",{ref:o},[Vue.createVNode(Ge,{ref:s,onResize:({height:_})=>a.height=_},null,8,["onResize"]),Vue.createVNode("div",{ref:i,class:"uni-picker-view-wrapper"},[h],512)],512)}}});class Mr{constructor(t){this._drag=t,this._dragLog=Math.log(t),this._x=0,this._v=0,this._startTime=0}set(t,n){this._x=t,this._v=n,this._startTime=new Date().getTime()}setVelocityByEnd(t){this._v=(t-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(t){t===void 0&&(t=(new Date().getTime()-this._startTime)/1e3);const n=t===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,t);return this._dt=t,this._x+this._v*n/this._dragLog-this._v/this._dragLog}dx(t){t===void 0&&(t=(new Date().getTime()-this._startTime)/1e3);const n=t===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,t);return this._dt=t,this._v*n}done(){return Math.abs(this.dx())<3}reconfigure(t){const n=this.x(),o=this.dx();this._drag=t,this._dragLog=Math.log(t),this.set(n,o)}configuration(){const t=this;return[{label:"Friction",read:function(){return t._drag},write:function(n){t.reconfigure(n)},min:.001,max:.1,step:.001}]}}function ku(e,t,n){return e>t-n&&e<t+n}function vt(e,t){return ku(e,0,t)}class Pr{constructor(t,n,o){this._m=t,this._k=n,this._c=o,this._solution=null,this._endPosition=0,this._startTime=0}_solve(t,n){const o=this._c,i=this._m,r=this._k,a=o*o-4*i*r;if(a===0){const d=-o/(2*i),f=t,h=n/(d*t);return{x:function(_){return(f+h*_)*Math.pow(Math.E,d*_)},dx:function(_){const v=Math.pow(Math.E,d*_);return d*(f+h*_)*v+h*v}}}if(a>0){const d=(-o-Math.sqrt(a))/(2*i),f=(-o+Math.sqrt(a))/(2*i),h=(n-d*t)/(f-d),_=t-h;return{x:function(v){let m,T;return v===this._t&&(m=this._powER1T,T=this._powER2T),this._t=v,m||(m=this._powER1T=Math.pow(Math.E,d*v)),T||(T=this._powER2T=Math.pow(Math.E,f*v)),_*m+h*T},dx:function(v){let m,T;return v===this._t&&(m=this._powER1T,T=this._powER2T),this._t=v,m||(m=this._powER1T=Math.pow(Math.E,d*v)),T||(T=this._powER2T=Math.pow(Math.E,f*v)),_*d*m+h*f*T}}}const s=Math.sqrt(4*i*r-o*o)/(2*i),l=-o/2*i,c=t,u=(n-l*t)/s;return{x:function(d){return Math.pow(Math.E,l*d)*(c*Math.cos(s*d)+u*Math.sin(s*d))},dx:function(d){const f=Math.pow(Math.E,l*d),h=Math.cos(s*d),_=Math.sin(s*d);return f*(u*s*h-c*s*_)+l*f*(u*_+c*h)}}}x(t){return t===void 0&&(t=(new Date().getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(t):0}dx(t){return t===void 0&&(t=(new Date().getTime()-this._startTime)/1e3),this._solution?this._solution.dx(t):0}setEnd(t,n,o){if(o||(o=new Date().getTime()),t!==this._endPosition||!vt(n,.4)){n=n||0;let i=this._endPosition;this._solution&&(vt(n,.4)&&(n=this._solution.dx((o-this._startTime)/1e3)),i=this._solution.x((o-this._startTime)/1e3),vt(n,.4)&&(n=0),vt(i,.4)&&(i=0),i+=this._endPosition),this._solution&&vt(i-t,.4)&&vt(n,.4)||(this._endPosition=t,this._solution=this._solve(i-this._endPosition,n),this._startTime=o)}}snap(t){this._startTime=new Date().getTime(),this._endPosition=t,this._solution={x:function(){return 0},dx:function(){return 0}}}done(t){return t||(t=new Date().getTime()),ku(this.x(),this._endPosition,.4)&&vt(this.dx(),.4)}reconfigure(t,n,o){this._m=t,this._k=n,this._c=o,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=new Date().getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){function t(o,i){o.reconfigure(1,i,o.damping())}function n(o,i){o.reconfigure(1,o.springConstant(),i)}return[{label:"Spring Constant",read:this.springConstant.bind(this),write:t.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:n.bind(this,this),min:1,max:500}]}}class h1{constructor(t,n,o){this._extent=t,this._friction=n||new Mr(.01),this._spring=o||new Pr(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(t,n){this._springOffset=0,this._springing=!0,this._spring.snap(t),this._spring.setEnd(n)}set(t,n){this._friction.set(t,n),t>0&&n>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(t),this._spring.setEnd(0)):t<-this._extent&&n<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(t),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=new Date().getTime()}x(t){if(!this._startTime)return 0;if(t||(t=(new Date().getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;let n=this._friction.x(t),o=this.dx(t);return(n>0&&o>=0||n<-this._extent&&o<=0)&&(this._springing=!0,this._spring.setEnd(0,o),n<-this._extent?this._springOffset=-this._extent:this._springOffset=0,n=this._spring.x()+this._springOffset),n}dx(t){let n;return this._lastTime===t?n=this._lastDx:n=this._springing?this._spring.dx(t):this._friction.dx(t),this._lastTime=t,this._lastDx=n,n}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(t){this._friction.setVelocityByEnd(t)}configuration(){const t=this._friction.configuration();return t.push.apply(t,this._spring.configuration()),t}}function g1(e,t,n){const o={id:0,cancelled:!1};function i(a,s,l,c){if(!a||!a.cancelled){l(s);const u=s.done();u||a.cancelled||(a.id=requestAnimationFrame(i.bind(null,a,s,l,c))),u&&c&&c(s)}}function r(a){a&&a.id&&cancelAnimationFrame(a.id),a&&(a.cancelled=!0)}return i(o,e,t,n),{cancel:r.bind(null,o),model:e}}class p1{constructor(t,n){n=n||{},this._element=t,this._options=n,this._enableSnap=n.enableSnap||!1,this._itemSize=n.itemSize||0,this._enableX=n.enableX||!1,this._enableY=n.enableY||!1,this._shouldDispatchScrollEvent=!!n.onScroll,this._enableX?(this._extent=(n.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=n.scrollWidth):(this._extent=(n.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=n.scrollHeight),this._position=0,this._scroll=new h1(this._extent,n.friction,n.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(t,n){let o=this._startPosition;this._enableX?o+=t:this._enableY&&(o+=n),o>0?o*=.5:o<-this._extent&&(o=.5*(o+this._extent)-this._extent),this._position=o,this.updatePosition(),this.dispatchScroll()}onTouchEnd(t,n,o){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(n)<this._itemSize&&Math.abs(o.y)<300||Math.abs(o.y)<150)){this.snap();return}if(this._enableX&&(Math.abs(t)<this._itemSize&&Math.abs(o.x)<300||Math.abs(o.x)<150)){this.snap();return}}this._enableX?this._scroll.set(this._position,o.x):this._enableY&&this._scroll.set(this._position,o.y);let i;if(this._enableSnap){const r=this._scroll._friction.x(100),a=r%this._itemSize;i=Math.abs(a)>this._itemSize/2?r-(this._itemSize-Math.abs(a)):r-a,i<=0&&i>=-this._extent&&this._scroll.setVelocityByEnd(i)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=g1(this._scroll,()=>{const r=Date.now(),a=(r-this._scroll._startTime)/1e3,s=this._scroll.x(a);this._position=s,this.updatePosition();const l=this._scroll.dx(a);this._shouldDispatchScrollEvent&&r-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/l),this._lastTime=r)},()=>{this._enableSnap&&(i<=0&&i>=-this._extent&&(this._position=i,this.updatePosition()),G(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1})}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){const t=this._itemSize,n=this._position%t,o=Math.abs(n)>this._itemSize/2?this._position-(t-Math.abs(n)):this._position-n;this._position!==o&&(this._snapping=!0,this.scrollTo(-o),G(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(t,n){this._animation&&(this._animation.cancel(),this._scrolling=!1),typeof t=="number"&&(this._position=-t),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);const o="transform "+(n||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+o,this._element.style.transition=o,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(G(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;const t={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(t)}}update(t,n,o){let i=0;const r=this._position;this._enableX?(i=this._element.childNodes.length?(n||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=n):(i=this._element.childNodes.length?(n||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=n),typeof t=="number"&&(this._position=-t),this._position<-i?this._position=-i:this._position>0&&(this._position=0),this._itemSize=o||this._itemSize,this.updatePosition(),r!==this._position&&(this.dispatchScroll(),G(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=i,this._scroll._extent=i}updatePosition(){let t="";this._enableX?t="translateX("+this._position+"px) translateZ(0)":this._enableY&&(t="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=t,this._element.style.transform=t}isScrolling(){return this._scrolling||this._snapping}}function Iu(e,t){const n={trackingID:-1,maxDy:0,maxDx:0},o=new p1(e,t);function i(l){const c=l,u=l;return c.detail.state==="move"||c.detail.state==="end"?{x:c.detail.dx,y:c.detail.dy}:{x:u.screenX-n.x,y:u.screenY-n.y}}function r(l){const c=l,u=l;c.detail.state==="start"?(n.trackingID="touch",n.x=c.detail.x,n.y=c.detail.y):(n.trackingID="mouse",n.x=u.screenX,n.y=u.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[c.detail.timeStamp||u.timeStamp],n.listener=o,o.onTouchStart&&o.onTouchStart(),(typeof l.cancelable!="boolean"||l.cancelable)&&l.preventDefault()}function a(l){const c=l,u=l;if(n.trackingID!==-1){(typeof l.cancelable!="boolean"||l.cancelable)&&l.preventDefault();const d=i(l);if(d){for(n.maxDy=Math.max(n.maxDy,Math.abs(d.y)),n.maxDx=Math.max(n.maxDx,Math.abs(d.x)),n.historyX.push(d.x),n.historyY.push(d.y),n.historyTime.push(c.detail.timeStamp||u.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(d.x,d.y)}}}function s(l){if(n.trackingID!==-1){l.preventDefault();const c=i(l);if(c){const u=n.listener;n.trackingID=-1,n.listener=null;const d=n.historyTime.length,f={x:0,y:0};if(d>2)for(let h=n.historyTime.length-1,_=n.historyTime[h],v=n.historyX[h],m=n.historyY[h];h>0;){h--;const T=n.historyTime[h],b=_-T;if(b>30&&b<50){f.x=(v-n.historyX[h])/(b/1e3),f.y=(m-n.historyY[h])/(b/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],u&&u.onTouchEnd&&u.onTouchEnd(c.x,c.y,f)}}}return{scroller:o,handleTouchStart:r,handleTouchMove:a,handleTouchEnd:s}}function m1(e){let n=0,o=0;e.addEventListener("touchstart",i=>{const r=i.changedTouches[0];n=r.clientX,o=r.clientY}),e.addEventListener("touchend",i=>{const r=i.changedTouches[0];if(Math.abs(r.clientX-n)<20&&Math.abs(r.clientY-o)<20){const a={bubbles:!0,cancelable:!0,target:i.target,currentTarget:i.currentTarget},s=new CustomEvent("click",a);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach(c=>{s[c]=r[c]}),i.target.dispatchEvent(s)}})}const Nu=X({name:"PickerViewColumn",setup(e,{slots:t,emit:n}){const o=Vue.ref(null),i=Vue.ref(null),r=Vue.inject("getPickerViewColumn"),a=Vue.getCurrentInstance(),s=r?r(a):Vue.ref(0),l=Vue.inject("pickerViewProps"),c=Vue.inject("pickerViewState"),u=Vue.ref(34),d=Vue.ref(null),f=()=>{const w=d.value;u.value=w.$el.offsetHeight};Vue.onMounted(f);const h=Vue.computed(()=>(c.height-u.value)/2),{state:_}=fu();let v;const m=Vue.reactive({current:s.value,length:0});let T;function b(){v&&!T&&(T=!0,Vue.nextTick(()=>{T=!1;let w=Math.min(m.current,m.length-1);w=Math.max(w,0),v.update(w*u.value,void 0,u.value)}))}Vue.watch(()=>s.value,w=>{w!==m.current&&(m.current=w,b())}),Vue.watch(()=>m.current,w=>s.value=w),Vue.watch([()=>u.value,()=>m.length,()=>c.height],b);let g=0;function p(w){const A=g+w.deltaY;if(Math.abs(A)>10){g=0;let x=Math.min(m.current+(A<0?-1:1),m.length-1);m.current=x=Math.max(x,0),v.scrollTo(x*u.value)}else g=A;w.preventDefault()}function V({clientY:w}){const A=o.value;if(!v.isScrolling()){const x=A.getBoundingClientRect(),E=w-x.top-c.height/2,C=u.value/2;if(!(Math.abs(E)<=C)){const I=Math.ceil((Math.abs(E)-C)/u.value),M=E<0?-I:I;let F=Math.min(m.current+M,m.length-1);m.current=F=Math.max(F,0),v.scrollTo(F*u.value)}}}const S=()=>{const w=o.value,A=i.value,{scroller:x,handleTouchStart:E,handleTouchMove:C,handleTouchEnd:I}=Iu(A,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:u.value,friction:new Mr(1e-4),spring:new Pr(2,90,20),onSnap:M=>{!isNaN(M)&&M!==m.current&&(m.current=M)}});v=x,kn(w,M=>{switch(M.detail.state){case"start":E(M);break;case"move":C(M),M.stopPropagation();break;case"end":case"cancel":I(M)}},!0),m1(w),b()};return Vue.onMounted(S),()=>{const w=t.default&&t.default();m.length=Ht(w).length;const A=`${h.value}px 0`;return Vue.createVNode("uni-picker-view-column",{ref:o},[Vue.createVNode("div",{onWheel:p,onClick:V,class:"uni-picker-view-group"},[Vue.createVNode("div",Vue.mergeProps(_.attrs,{class:["uni-picker-view-mask",l.maskClass],style:`background-size: 100% ${h.value}px;${l.maskStyle}`}),null,16),Vue.createVNode("div",Vue.mergeProps(_.attrs,{class:["uni-picker-view-indicator",l.indicatorClass],style:l.indicatorStyle}),[Vue.createVNode(Ge,{ref:d,onResize:({height:x})=>u.value=x},null,8,["onResize"])],16),Vue.createVNode("div",{ref:i,class:["uni-picker-view-content"],style:{padding:A,"--picker-view-column-indicator-height":`${u.value}px`}},[w],4)],40,["onWheel","onClick"])],512)}}}),v1=16,_t={activeColor:nn,backgroundColor:"#EBEBEB",activeMode:"backwards"},_1={percent:{type:[Number,String],default:0,validator(e){return!isNaN(parseFloat(e))}},fontSize:{type:[String,Number],default:v1},showInfo:{type:[Boolean,String],default:!1},strokeWidth:{type:[Number,String],default:6,validator(e){return!isNaN(parseFloat(e))}},color:{type:String,default:_t.activeColor},activeColor:{type:String,default:_t.activeColor},backgroundColor:{type:String,default:_t.backgroundColor},active:{type:[Boolean,String],default:!1},activeMode:{type:String,default:_t.activeMode},duration:{type:[Number,String],default:30,validator(e){return!isNaN(parseFloat(e))}},borderRadius:{type:[Number,String],default:0}},y1=X({name:"Progress",props:_1,setup(e){const t=Vue.ref(null),n=b1(e);return xu(n,e),Vue.watch(()=>n.realPercent,(o,i)=>{n.strokeTimer&&clearInterval(n.strokeTimer),n.lastPercent=i||0,xu(n,e)}),()=>{const{showInfo:o}=e,{outerBarStyle:i,innerBarStyle:r,currentPercent:a}=n;return Vue.createVNode("uni-progress",{class:"uni-progress",ref:t},[Vue.createVNode("div",{style:i,class:"uni-progress-bar"},[Vue.createVNode("div",{style:r,class:"uni-progress-inner-bar"},null,4)],4),o?Vue.createVNode("p",{class:"uni-progress-info"},[a+"%"]):""],512)}}});function b1(e){const t=Vue.ref(0),n=Vue.computed(()=>`background-color: ${e.backgroundColor}; height: ${e.strokeWidth}px;`),o=Vue.computed(()=>{const a=e.color!==_t.activeColor&&e.activeColor===_t.activeColor?e.color:e.activeColor;return`width: ${t.value}%;background-color: ${a}`}),i=Vue.computed(()=>{if(typeof e.percent=="string"&&!/^-?\d*\.?\d*$/.test(e.percent))return 0;let a=parseFloat(e.percent);return Number.isNaN(a)||a<0?a=0:a>100&&(a=100),a});return Vue.reactive({outerBarStyle:n,innerBarStyle:o,realPercent:i,currentPercent:t,strokeTimer:0,lastPercent:0})}function xu(e,t){t.active?(e.currentPercent=t.activeMode===_t.activeMode?0:e.lastPercent,e.strokeTimer=setInterval(()=>{e.currentPercent+1>e.realPercent?(e.currentPercent=e.realPercent,e.strokeTimer&&clearInterval(e.strokeTimer)):e.currentPercent+=1},parseFloat(t.duration))):e.currentPercent=e.realPercent}const Mu=dn("ucg"),w1=X({name:"RadioGroup",props:{name:{type:String,default:""}},setup(e,{emit:t,slots:n}){const o=Vue.ref(null),i=ve(o,t);return S1(e,i),()=>Vue.createVNode("uni-radio-group",{ref:o},[n.default&&n.default()],512)}});function S1(e,t){const n=[];Vue.onMounted(()=>{s(n.length-1)});const o=()=>{var l;return(l=n.find(c=>c.value.radioChecked))==null?void 0:l.value.value};Vue.provide(Mu,{addField(l){n.push(l)},removeField(l){n.splice(n.indexOf(l),1)},radioChange(l,c){const u=n.indexOf(c);s(u,!0),t("change",l,{value:o()})}});const i=Vue.inject(Fe,!1),r={submit:()=>{let l=["",null];return e.name!==""&&(l[0]=e.name,l[1]=o()),l}};i&&(i.addField(r),Vue.onBeforeUnmount(()=>{i.removeField(r)}));function a(l,c){l.value={radioChecked:c,value:l.value.value}}function s(l,c){n.forEach((u,d)=>{d!==l&&(c?a(n[d],!1):n.forEach((f,h)=>{d>=h||n[h].value.radioChecked&&a(n[d],!1)}))})}return n}const T1=X({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:"#ffffff"}},setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.ref(e.checked),i=Vue.ref(e.value);function r(f){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};const h={};return o.value?(h.backgroundColor=e.activeBackgroundColor||e.color,h.borderColor=e.activeBorderColor||h.backgroundColor):(e.borderColor&&(h.borderColor=e.borderColor),e.backgroundColor&&(h.backgroundColor=e.backgroundColor)),h}const a=Vue.computed(()=>r(o.value));Vue.watch([()=>e.checked,()=>e.value],([f,h])=>{o.value=f,i.value=h});const s=()=>{o.value=!1},{uniCheckGroup:l,uniLabel:c,field:u}=V1(o,i,s),d=f=>{e.disabled||o.value||(o.value=!0,l&&l.radioChange(f,u),f.stopPropagation())};return c&&(c.addHandler(d),Vue.onBeforeUnmount(()=>{c.removeHandler(d)})),co(e,{"label-click":d}),()=>{const f=ht(e,"disabled");let h;return h=o.value,Vue.createVNode("uni-radio",Vue.mergeProps(f,{id:e.id,onClick:d,ref:n}),[Vue.createVNode("div",{class:"uni-radio-wrapper",style:{"--HOVER-BD-COLOR":o.value?a.value.borderColor:e.activeBorderColor}},[Vue.createVNode("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:a.value},[h?ce(fn,e.disabled?"#ADADAD":e.iconColor,18):""],6),t.default&&t.default()],4)],16,["id","onClick"])}}});function V1(e,t,n){const o=Vue.computed({get:()=>({radioChecked:!!e.value,value:t.value}),set:({radioChecked:l})=>{e.value=l}}),i={reset:n},r=Vue.inject(Mu,!1);r&&r.addField(o);const a=Vue.inject(Fe,!1);a&&a.addField(i);const s=Vue.inject(pn,!1);return Vue.onBeforeUnmount(()=>{r&&r.removeField(o),a&&a.removeField(i)}),{uniCheckGroup:r,uniForm:a,uniLabel:s,field:o}}const C1={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},Or={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};function A1(e){return e.replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,function(t,n){return le(Or,n)&&Or[n]?Or[n]:/^#[0-9]{1,4}$/.test(n)?String.fromCharCode(n.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(n)?String.fromCharCode(0+n.slice(1)):t})}function E1(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}function k1(e,t){if(fe(t)){for(const n in t)if(le(t,n)){const o=t[n];e==="img"&&n==="src"&&(t[n]=Q(o))}}}const Pu=(e,t,n)=>!n||K(n)&&!n.length?[]:n.map(o=>{var i;if(fe(o)){if(!le(o,"type")||o.type==="node"){let r={[e]:""};const a=(i=o.name)==null?void 0:i.toLowerCase();return le(C1,a)?(k1(a,o.attrs),r=B(r,E1(o,t),o.attrs),Vue.h(o.name,r,Pu(e,t,o.children))):void 0}if(o.type==="text"&&J(o.text)&&o.text!=="")return Vue.createTextVNode(A1(o.text||""))}});function I1(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}function N1(e){return e.reduce(function(t,n){let o=n.value;const i=n.name;return o.match(/ /)&&["style","src"].indexOf(i)===-1&&(o=o.split(" ")),t[i]?Array.isArray(t[i])?t[i].push(o):t[i]=[t[i],o]:t[i]=o,t},{})}function x1(e){e=I1(e);const t=[],n={children:[]};return iu(e,{start:function(o,i,r){const a={name:o};if(i.length!==0&&(a.attrs=N1(i)),r){const s=t[0]||n;s.children||(s.children=[]),s.children.push(a)}else t.unshift(a)},end:function(o){const i=t.shift();if(i.name!==o&&console.error("invalid state: mismatch end tag"),t.length===0)n.children.push(i);else{const r=t[0];r.children||(r.children=[]),r.children.push(i)}},chars:function(o){const i={type:"text",text:o};if(t.length===0)n.children.push(i);else{const r=t[0];r.children||(r.children=[]),r.children.push(i)}},comment:function(o){const i={node:"comment",text:o},r=t[0];r&&(r.children||(r.children=[]),r.children.push(i))}}),n.children}const M1=X({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["itemclick"],setup(e,{emit:t}){const n=Vue.getCurrentInstance(),o=n&&n.vnode.scopeId||"",i=Vue.ref(null),r=Vue.ref([]),a=ve(i,t);function s(c,u={}){a("itemclick",c,u)}function l(){let c=e.nodes;J(c)&&(c=x1(e.nodes)),r.value=Pu(o,s,c)}return Vue.watch(()=>e.nodes,l,{immediate:!0}),()=>Vue.h("uni-rich-text",{ref:i},Vue.h("div",{},r.value))}}),P1=X({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.computed(()=>{const r={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":r.height=e.refresherHeight+"px";break;case"refreshing":r.height=e.refresherThreshold+"px",r.transition="height 0.3s";break;case"":case"refresherabort":case"restore":r.height="0px",r.transition="height 0.3s";break}return r}),i=Vue.computed(()=>{const r=e.refresherHeight/e.refresherThreshold;return(r>1?1:r)*360});return()=>{const{refreshState:r,refresherDefaultStyle:a,refresherThreshold:s}=e;return Vue.createVNode("div",{ref:n,style:o.value,class:"uni-scroll-view-refresher"},[a!=="none"?Vue.createVNode("div",{class:"uni-scroll-view-refresh"},[Vue.createVNode("div",{class:"uni-scroll-view-refresh-inner"},[r=="pulling"?Vue.createVNode("svg",{key:"refresh__icon",style:{transform:"rotate("+i.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[Vue.createVNode("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),Vue.createVNode("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,r=="refreshing"?Vue.createVNode("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[Vue.createVNode("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,a==="none"?Vue.createVNode("div",{class:"uni-scroll-view-refresher-container",style:{height:`${s}px`}},[t.default&&t.default()]):null],4)}}}),Ou=kt(!0),Ru=X({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,{emit:t,slots:n,expose:o}){const i=Vue.ref(null),r=Vue.ref(null),a=Vue.ref(null),s=Vue.ref(null),l=ve(i,t),{state:c,scrollTopNumber:u,scrollLeftNumber:d}=O1(e),{realScrollX:f,realScrollY:h}=R1(e,c,u,d,l,i,r,s,t),_=Vue.computed(()=>{let m="";return f.value?m+="overflow-x:auto;":m+="overflow-x:hidden;",h.value?m+="overflow-y:auto;":m+="overflow-y:hidden;",m}),v=Vue.computed(()=>{let m="uni-scroll-view";return e.showScrollbar===!1&&(m+=" uni-scroll-view-scrollbar-hidden"),m});return o({$getMain(){return r.value}}),()=>{const{refresherEnabled:m,refresherBackground:T,refresherDefaultStyle:b,refresherThreshold:g}=e,{refresherHeight:p,refreshState:V}=c;return Vue.createVNode("uni-scroll-view",{ref:i},[Vue.createVNode("div",{ref:a,class:"uni-scroll-view"},[Vue.createVNode("div",{ref:r,style:_.value,class:v.value},[m?Vue.createVNode(P1,{refreshState:V,refresherHeight:p,refresherThreshold:g,refresherDefaultStyle:b,refresherBackground:T},{default:()=>[b=="none"?n.refresher&&n.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,Vue.createVNode("div",{ref:s,class:"uni-scroll-view-content"},[n.default&&n.default()],512)],6)],512)],512)}}});function O1(e){const t=Vue.computed(()=>Number(e.scrollTop)||0),n=Vue.computed(()=>Number(e.scrollLeft)||0);return{state:Vue.reactive({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""}),scrollTopNumber:t,scrollLeftNumber:n}}function R1(e,t,n,o,i,r,a,s,l){let c=!1,u=0,d=!1,f=()=>{};const h=Vue.computed(()=>e.scrollX),_=Vue.computed(()=>e.scrollY),v=Vue.computed(()=>{let E=Number(e.upperThreshold);return isNaN(E)?50:E}),m=Vue.computed(()=>{let E=Number(e.lowerThreshold);return isNaN(E)?50:E});function T(E,C){const I=a.value;let M=0,F="";if(E<0?E=0:C==="x"&&E>I.scrollWidth-I.offsetWidth?E=I.scrollWidth-I.offsetWidth:C==="y"&&E>I.scrollHeight-I.offsetHeight&&(E=I.scrollHeight-I.offsetHeight),C==="x"?M=I.scrollLeft-E:C==="y"&&(M=I.scrollTop-E),M===0)return;let k=s.value;k.style.transition="transform .3s ease-out",k.style.webkitTransition="-webkit-transform .3s ease-out",C==="x"?F="translateX("+M+"px) translateZ(0)":C==="y"&&(F="translateY("+M+"px) translateZ(0)"),k.removeEventListener("transitionend",f),k.removeEventListener("webkitTransitionEnd",f),f=()=>S(E,C),k.addEventListener("transitionend",f),k.addEventListener("webkitTransitionEnd",f),C==="x"?I.style.overflowX="hidden":C==="y"&&(I.style.overflowY="hidden"),k.style.transform=F,k.style.webkitTransform=F}function b(E){const C=E.target;i("scroll",E,{scrollLeft:C.scrollLeft,scrollTop:C.scrollTop,scrollHeight:C.scrollHeight,scrollWidth:C.scrollWidth,deltaX:t.lastScrollLeft-C.scrollLeft,deltaY:t.lastScrollTop-C.scrollTop}),_.value&&(C.scrollTop<=v.value&&t.lastScrollTop-C.scrollTop>0&&E.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",E,{direction:"top"}),t.lastScrollToUpperTime=E.timeStamp),C.scrollTop+C.offsetHeight+m.value>=C.scrollHeight&&t.lastScrollTop-C.scrollTop<0&&E.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",E,{direction:"bottom"}),t.lastScrollToLowerTime=E.timeStamp)),h.value&&(C.scrollLeft<=v.value&&t.lastScrollLeft-C.scrollLeft>0&&E.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",E,{direction:"left"}),t.lastScrollToUpperTime=E.timeStamp),C.scrollLeft+C.offsetWidth+m.value>=C.scrollWidth&&t.lastScrollLeft-C.scrollLeft<0&&E.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",E,{direction:"right"}),t.lastScrollToLowerTime=E.timeStamp)),t.lastScrollTop=C.scrollTop,t.lastScrollLeft=C.scrollLeft}function g(E){_.value&&(e.scrollWithAnimation?T(E,"y"):a.value.scrollTop=E)}function p(E){h.value&&(e.scrollWithAnimation?T(E,"x"):a.value.scrollLeft=E)}function V(E){if(E){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(E)){console.error(`id error: scroll-into-view=${E}`);return}let C=r.value.querySelector("#"+E);if(C){let I=a.value.getBoundingClientRect(),M=C.getBoundingClientRect();if(h.value){let F=M.left-I.left,L=a.value.scrollLeft+F;e.scrollWithAnimation?T(L,"x"):a.value.scrollLeft=L}if(_.value){let F=M.top-I.top,L=a.value.scrollTop+F;e.scrollWithAnimation?T(L,"y"):a.value.scrollTop=L}}}}function S(E,C){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";let I=a.value;C==="x"?(I.style.overflowX=h.value?"auto":"hidden",I.scrollLeft=E):C==="y"&&(I.style.overflowY=_.value?"auto":"hidden",I.scrollTop=E),s.value.removeEventListener("transitionend",f),s.value.removeEventListener("webkitTransitionEnd",f)}function w(E){if(e.refresherEnabled){switch(E){case"refreshing":t.refresherHeight=e.refresherThreshold,c||(c=!0,i("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),i("refresherrefresh",{},{dy:x.y-A.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":c=!1,t.refresherHeight=u=0,E==="restore"&&(d=!1,i("refresherrestore",{},{dy:x.y-A.y})),E==="refresherabort"&&d&&(d=!1,i("refresherabort",{},{dy:x.y-A.y}));break}t.refreshState=E}}let A={x:0,y:0},x={y:e.refresherThreshold};return Vue.onMounted(()=>{Vue.nextTick(()=>{g(n.value),p(o.value)}),V(e.scrollIntoView);let E=function(k){k.preventDefault(),k.stopPropagation(),b(k)},C=null,I=function(k){if(A===null)return;let L=k.touches[0].pageX,z=k.touches[0].pageY,N=a.value;if(Math.abs(L-A.x)>Math.abs(z-A.y))if(h.value){if(N.scrollLeft===0&&L>A.x){C=!1;return}else if(N.scrollWidth===N.offsetWidth+N.scrollLeft&&L<A.x){C=!1;return}C=!0}else C=!1;else if(_.value)if(N.scrollTop===0&&z>A.y)C=!1,e.refresherEnabled&&k.cancelable!==!1&&k.preventDefault();else if(N.scrollHeight===N.offsetHeight+N.scrollTop&&z<A.y){C=!1;return}else C=!0;else C=!1;if(C&&k.stopPropagation(),N.scrollTop===0&&k.touches.length===1&&w("pulling"),e.refresherEnabled&&t.refreshState==="pulling"){const P=z-A.y;u===0&&(u=z),c?(t.refresherHeight=P+e.refresherThreshold,d=!1):(t.refresherHeight=z-u,t.refresherHeight>0&&(d=!0,i("refresherpulling",k,{deltaY:P,dy:P})))}},M=function(k){k.touches.length===1&&(A={x:k.touches[0].pageX,y:k.touches[0].pageY})},F=function(k){x={x:k.changedTouches[0].pageX,y:k.changedTouches[0].pageY},t.refresherHeight>=e.refresherThreshold?w("refreshing"):w("refresherabort"),A={x:0,y:0},x={x:0,y:e.refresherThreshold}};a.value.addEventListener("touchstart",M,Ou),a.value.addEventListener("touchmove",I,kt(!1)),a.value.addEventListener("scroll",E,kt(!1)),a.value.addEventListener("touchend",F,Ou),Vue.onBeforeUnmount(()=>{a.value.removeEventListener("touchstart",M),a.value.removeEventListener("touchmove",I),a.value.removeEventListener("scroll",E),a.value.removeEventListener("touchend",F)})}),Vue.onActivated(()=>{_.value&&(a.value.scrollTop=t.lastScrollTop),h.value&&(a.value.scrollLeft=t.lastScrollLeft)}),Vue.watch(n,E=>{g(E)}),Vue.watch(o,E=>{p(E)}),Vue.watch(()=>e.scrollIntoView,E=>{V(E)}),Vue.watch(()=>e.refresherTriggered,E=>{E===!0?w("refreshing"):E===!1&&w("restore")}),{realScrollX:h,realScrollY:_,_scrollTopChanged:g,_scrollLeftChanged:p}}const L1=X({name:"Slider",props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},emits:["changing","change"],setup(e,{emit:t}){const n=Vue.ref(null),o=Vue.ref(null),i=Vue.ref(null),r=Vue.ref(Number(e.value));Vue.watch(()=>e.value,u=>{r.value=Number(u)});const a=ve(n,t),s=D1(e,r),{_onClick:l,_onTrack:c}=U1(e,r,n,o,a);return Vue.onMounted(()=>{kn(i.value,c)}),()=>{const{setBgColor:u,setBlockBg:d,setActiveColor:f,setBlockStyle:h}=s;return Vue.createVNode("uni-slider",{ref:n,onClick:ne(l)},[Vue.createVNode("div",{class:"uni-slider-wrapper"},[Vue.createVNode("div",{class:"uni-slider-tap-area"},[Vue.createVNode("div",{style:u.value,class:"uni-slider-handle-wrapper"},[Vue.createVNode("div",{ref:i,style:d.value,class:"uni-slider-handle"},null,4),Vue.createVNode("div",{style:h.value,class:"uni-slider-thumb"},null,4),Vue.createVNode("div",{style:f.value,class:"uni-slider-track"},null,4)],4)]),Vue.withDirectives(Vue.createVNode("span",{ref:o,class:"uni-slider-value"},[r.value],512),[[Vue.vShow,e.showValue]])]),Vue.createVNode("slot",null,null)],8,["onClick"])}}}),B1=(e,t,n)=>(n=Number(n),t=Number(t),100*(e-t)/(n-t)+"%");function D1(e,t){const n=()=>B1(t.value,e.min,e.max),o=()=>e.backgroundColor!=="#e9e9e9"?e.backgroundColor:e.color!=="#007aff"?e.color:"#007aff",i=()=>e.activeColor!=="#007aff"?e.activeColor:e.selectedColor!=="#e9e9e9"?e.selectedColor:"#e9e9e9";return{setBgColor:Vue.computed(()=>({backgroundColor:o()})),setBlockBg:Vue.computed(()=>({left:n()})),setActiveColor:Vue.computed(()=>({backgroundColor:i(),width:n()})),setBlockStyle:Vue.computed(()=>({width:e.blockSize+"px",height:e.blockSize+"px",marginLeft:-e.blockSize/2+"px",marginTop:-e.blockSize/2+"px",left:n(),backgroundColor:e.blockColor}))}}function U1(e,t,n,o,i){const r=u=>{e.disabled||(s(u),i("change",u,{value:t.value}))},a=u=>{const d=Number(e.max),f=Number(e.min),h=Number(e.step);return u<f?f:u>d?d:F1.mul.call(Math.round((u-f)/h),h)+f},s=u=>{const d=Number(e.max),f=Number(e.min),h=o.value,_=getComputedStyle(h,null).marginLeft;let v=h.offsetWidth;v=v+parseInt(_);const m=n.value,T=m.offsetWidth-(e.showValue?v:0),b=m.getBoundingClientRect().left,g=(u.x-b)*(d-f)/T+f;t.value=a(g)},l=u=>{if(!e.disabled)return u.detail.state==="move"?(s({x:u.detail.x}),i("changing",u,{value:t.value}),!1):u.detail.state==="end"&&i("change",u,{value:t.value})},c=Vue.inject(Fe,!1);if(c){const u={reset:()=>t.value=Number(e.min),submit:()=>{const d=["",null];return e.name!==""&&(d[0]=e.name,d[1]=t.value),d}};c.addField(u),Vue.onBeforeUnmount(()=>{c.removeField(u)})}return{_onClick:r,_onTrack:l}}var F1={mul:function(e){let t=0,n=this.toString(),o=e.toString();try{t+=n.split(".")[1].length}catch(i){}try{t+=o.split(".")[1].length}catch(i){}return Number(n.replace(".",""))*Number(o.replace(".",""))/Math.pow(10,t)}};const $1={indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}};function z1(e){const t=Vue.computed(()=>{const r=Number(e.interval);return isNaN(r)?5e3:r}),n=Vue.computed(()=>{const r=Number(e.duration);return isNaN(r)?500:r}),o=Vue.computed(()=>{const r=Math.round(e.displayMultipleItems);return isNaN(r)?1:r});return Vue.reactive({interval:t,duration:n,displayMultipleItems:o,current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}function W1(e,t,n,o,i,r){function a(){s&&(clearTimeout(s),s=null)}let s=null,l=!0,c=0,u=1,d=null,f=!1,h=0,_,v="",m;const T=Vue.computed(()=>n.value.length>t.displayMultipleItems),b=Vue.computed(()=>e.circular&&T.value);function g(k){if(!l)for(let L=n.value,z=L.length,N=k+t.displayMultipleItems,P=0;P<z;P++){const O=L[P],$=Math.floor(k/z)*z+P,H=$+z,q=$-z,U=Math.max(k-($+1),$-N,0),re=Math.max(k-(H+1),H-N,0),ae=Math.max(k-(q+1),q-N,0),Me=Math.min(U,re,ae),De=[$,H,q][[U,re,ae].indexOf(Me)];O.updatePosition(De,e.vertical)}}function p(k){Math.floor(2*c)===Math.floor(2*k)&&Math.ceil(2*c)===Math.ceil(2*k)||b.value&&g(k);const L=e.vertical?"0":100*-k*u+"%",z=e.vertical?100*-k*u+"%":"0",N="translate("+L+", "+z+") translateZ(0)",P=o.value;if(P&&(P.style.webkitTransform=N,P.style.transform=N),c=k,!_){if(k%1===0)return;_=k}k-=Math.floor(_);const O=n.value;k<=-(O.length-1)?k+=O.length:k>=O.length&&(k-=O.length),k=_%1>.5||_<0?k-1:k,r("transition",{},{dx:e.vertical?0:k*P.offsetWidth,dy:e.vertical?k*P.offsetHeight:0})}function V(){d&&(p(d.toPos),d=null)}function S(k){const L=n.value.length;if(!L)return-1;const z=(Math.round(k)%L+L)%L;if(b.value){if(L<=t.displayMultipleItems)return 0}else if(z>L-t.displayMultipleItems)return L-t.displayMultipleItems;return z}function w(){d=null}function A(){if(!d){f=!1;return}const k=d,L=k.toPos,z=k.acc,N=k.endTime,P=k.source,O=N-Date.now();if(O<=0){p(L),d=null,f=!1,_=null;const q=n.value[t.current];if(q){const U=q.getItemId();r("animationfinish",{},{current:t.current,currentItemId:U,source:P})}return}const $=z*O*O/2,H=L+$;p(H),m=requestAnimationFrame(A)}function x(k,L,z){w();const N=t.duration,P=n.value.length;let O=c;if(b.value)if(z<0){for(;O<k;)O+=P;for(;O-P>k;)O-=P}else if(z>0){for(;O>k;)O-=P;for(;O+P<k;)O+=P;O+P-k<k-O&&(O+=P)}else{for(;O+P<k;)O+=P;for(;O-P>k;)O-=P;O+P-k<k-O&&(O+=P)}else L==="click"&&(k=k+t.displayMultipleItems-1<P?k:0);d={toPos:k,acc:2*(O-k)/(N*N),endTime:Date.now()+N,source:L},f||(f=!0,m=requestAnimationFrame(A))}function E(){a();const k=n.value,L=function(){s=null,v="autoplay",b.value?t.current=S(t.current+1):t.current=t.current+t.displayMultipleItems<k.length?t.current+1:0,x(t.current,"autoplay",b.value?1:0),s=setTimeout(L,t.interval)};l||k.length<=t.displayMultipleItems||(s=setTimeout(L,t.interval))}function C(){a(),V();const k=n.value;for(let P=0;P<k.length;P++)k[P].updatePosition(P,e.vertical);u=1;const L=o.value;if(t.displayMultipleItems===1&&k.length){const P=k[0].getBoundingClientRect(),O=L.getBoundingClientRect();u=P.width/O.width,u>0&&u<1||(u=1)}const z=c;c=-2;const N=t.current;N>=0?(l=!1,t.userTracking?(p(z+N-h),h=N):(p(N),e.autoplay&&E())):(l=!0,p(-t.displayMultipleItems-1))}Vue.watch([()=>e.current,()=>e.currentItemId,()=>[...n.value]],()=>{let k=-1;if(e.currentItemId){for(let L=0,z=n.value;L<z.length;L++)if(z[L].getItemId()===e.currentItemId){k=L;break}}k<0&&(k=Math.round(e.current)||0),k=k<0?0:k,t.current!==k&&(v="",t.current=k)}),Vue.watch([()=>e.vertical,()=>b.value,()=>t.displayMultipleItems,()=>[...n.value]],C),Vue.watch(()=>t.interval,()=>{s&&(a(),E())});function I(k,L){const z=v;v="";const N=n.value;if(!z){const O=N.length;x(k,"",b.value&&L+(O-k)%O>O/2?1:0)}const P=N[k];if(P){const O=t.currentItemId=P.getItemId();r("change",{},{current:t.current,currentItemId:O,source:z})}}Vue.watch(()=>t.current,(k,L)=>{I(k,L),i("update:current",k)}),Vue.watch(()=>t.currentItemId,k=>{i("update:currentItemId",k)});function M(k){k?E():a()}Vue.watch(()=>e.autoplay&&!t.userTracking,M),M(e.autoplay&&!t.userTracking),Vue.onMounted(()=>{let k=!1,L=0,z=0;function N(){a(),h=c,L=0,z=Date.now(),w()}function P($){const H=z;z=Date.now();const U=n.value.length-t.displayMultipleItems;function re(st){return .5-.25/(st+.5)}function ae(st,Hn){let _e=h+st;L=.6*L+.4*Hn,b.value||(_e<0||_e>U)&&(_e<0?_e=-re(-_e):_e>U&&(_e=U+re(_e-U)),L=0),p(_e)}const Me=z-H||1,De=o.value;e.vertical?ae(-$.dy/De.offsetHeight,-$.ddy/Me):ae(-$.dx/De.offsetWidth,-$.ddx/Me)}function O($){t.userTracking=!1;const H=L/Math.abs(L);let q=0;!$&&Math.abs(L)>.2&&(q=.5*H);const U=S(c+q);$?p(h):(v="touch",t.current=U,x(U,"touch",q!==0?q:U===0&&b.value&&c>=1?1:0))}kn(o.value,$=>{if(!e.disableTouch&&!l){if($.detail.state==="start")return t.userTracking=!0,k=!1,N();if($.detail.state==="end")return O(!1);if($.detail.state==="cancel")return O(!0);if(t.userTracking){if(!k){k=!0;const H=Math.abs($.detail.dx),q=Math.abs($.detail.dy);if((H>=q&&e.vertical||H<=q&&!e.vertical)&&(t.userTracking=!1),!t.userTracking){e.autoplay&&E();return}}return P($.detail),!1}}})}),Vue.onUnmounted(()=>{a(),cancelAnimationFrame(m)});function F(k){x(t.current=k,v="click",b.value?1:0)}return{onSwiperDotClick:F,circularEnabled:b,swiperEnabled:T}}const Lu=X({name:"Swiper",props:$1,emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,{slots:t,emit:n}){const o=Vue.ref(null),i=ve(o,n),r=Vue.ref(null),a=Vue.ref(null),s=z1(e),l=Vue.computed(()=>{let p={};return(e.nextMargin||e.previousMargin)&&(p=e.vertical?{left:0,right:0,top:Je(e.previousMargin,!0),bottom:Je(e.nextMargin,!0)}:{top:0,bottom:0,left:Je(e.previousMargin,!0),right:Je(e.nextMargin,!0)}),p}),c=Vue.computed(()=>{const p=Math.abs(100/s.displayMultipleItems)+"%";return{width:e.vertical?"100%":p,height:e.vertical?p:"100%"}});let u=[];const d=[],f=Vue.ref([]);function h(){const p=[];for(let V=0;V<u.length;V++){let S=u[V];S instanceof Element||(S=S.el);const w=d.find(A=>S===A.rootRef.value);w&&p.push(Vue.markRaw(w))}f.value=p}const _=function(p){d.push(p),h()};Vue.provide("addSwiperContext",_);const v=function(p){const V=d.indexOf(p);V>=0&&(d.splice(V,1),h())};Vue.provide("removeSwiperContext",v);const{onSwiperDotClick:m,circularEnabled:T,swiperEnabled:b}=W1(e,s,f,a,n,i);let g=()=>null;return g=H1(o,e,s,m,f,T,b),()=>{const p=t.default&&t.default();return u=Ht(p),Vue.createVNode("uni-swiper",{ref:o},[Vue.createVNode("div",{ref:r,class:"uni-swiper-wrapper"},[Vue.createVNode("div",{class:"uni-swiper-slides",style:l.value},[Vue.createVNode("div",{ref:a,class:"uni-swiper-slide-frame",style:c.value},[p],4)],4),e.indicatorDots&&Vue.createVNode("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map((V,S,w)=>Vue.createVNode("div",{onClick:()=>m(S),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":S<s.current+s.displayMultipleItems&&S>=s.current||S<s.current+s.displayMultipleItems-w.length},style:{background:S===s.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"]))],2),g()],512)],512)}}}),H1=(e,t,n,o,i,r,a)=>{let s=!1,l=!1,c=!1,u=Vue.ref(!1);Vue.watchEffect(()=>{s=t.navigation==="auto",u.value=t.navigation!==!0||s,b()}),Vue.watchEffect(()=>{const p=i.value.length,V=!r.value;l=n.current===0&&V,c=n.current===p-1&&V||V&&n.current+n.displayMultipleItems>=p,a.value||(l=!0,c=!0,s&&(u.value=!0))});function d(p,V){const S=p.currentTarget;S&&(S.style.backgroundColor=V==="over"?t.navigationActiveColor:"")}const f={onMouseover:p=>d(p,"over"),onMouseout:p=>d(p,"out")};function h(p,V,S){if(p.stopPropagation(),S)return;const w=i.value.length;let A=n.current;switch(V){case"prev":A--,A<0&&r.value&&(A=w-1);break;case"next":A++,A>=w&&r.value&&(A=0);break}o(A)}const _=()=>ce(hn,t.navigationColor,26);let v;const m=p=>{clearTimeout(v);const{clientX:V,clientY:S}=p,{left:w,right:A,top:x,bottom:E,width:C,height:I}=e.value.getBoundingClientRect();let M=!1;if(t.vertical?M=!(S-x<I/3||E-S<I/3):M=!(V-w<C/3||A-V<C/3),M)return v=setTimeout(()=>{u.value=M},300);u.value=M},T=()=>{u.value=!0};function b(){e.value&&(e.value.removeEventListener("mousemove",m),e.value.removeEventListener("mouseleave",T),s&&(e.value.addEventListener("mousemove",m),e.value.addEventListener("mouseleave",T)))}Vue.onMounted(b);function g(){const p={"uni-swiper-navigation-hide":u.value,"uni-swiper-navigation-vertical":t.vertical};return t.navigation?Vue.createVNode(Vue.Fragment,null,[Vue.createVNode("div",Vue.mergeProps({class:["uni-swiper-navigation uni-swiper-navigation-prev",B({"uni-swiper-navigation-disabled":l},p)],onClick:V=>h(V,"prev",l)},f),[_()],16,["onClick"]),Vue.createVNode("div",Vue.mergeProps({class:["uni-swiper-navigation uni-swiper-navigation-next",B({"uni-swiper-navigation-disabled":c},p)],onClick:V=>h(V,"next",c)},f),[_()],16,["onClick"])]):null}return g},Bu=X({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,{slots:t}){const n=Vue.ref(null),o={rootRef:n,getItemId(){return e.itemId},getBoundingClientRect(){return n.value.getBoundingClientRect()},updatePosition(i,r){const a=r?"0":100*i+"%",s=r?100*i+"%":"0",l=n.value,c=`translate(${a},${s}) translateZ(0)`;l&&(l.style.webkitTransform=c,l.style.transform=c)}};return Vue.onMounted(()=>{const i=Vue.inject("addSwiperContext");i&&i(o)}),Vue.onUnmounted(()=>{const i=Vue.inject("removeSwiperContext");i&&i(o)}),()=>Vue.createVNode("uni-swiper-item",{ref:n,style:{position:"absolute",width:"100%",height:"100%"}},[t.default&&t.default()],512)}}),q1=X({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=Vue.ref(null),o=Vue.ref(e.checked),i=G1(e,o),r=ve(n,t);Vue.watch(()=>e.checked,s=>{o.value=s});const a=s=>{e.disabled||(o.value=!o.value,r("change",s,{value:o.value}))};return i&&(i.addHandler(a),Vue.onBeforeUnmount(()=>{i.removeHandler(a)})),co(e,{"label-click":a}),()=>{const{color:s,type:l}=e,c=ht(e,"disabled"),u={};s&&o.value&&(u.backgroundColor=s,u.borderColor=s);let d;return d=o.value,Vue.createVNode("uni-switch",Vue.mergeProps({id:e.id,ref:n},c,{onClick:a}),[Vue.createVNode("div",{class:"uni-switch-wrapper"},[Vue.withDirectives(Vue.createVNode("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:u},null,6),[[Vue.vShow,l==="switch"]]),Vue.withDirectives(Vue.createVNode("div",{class:"uni-checkbox-input"},[d?ce(fn,e.color,22):""],512),[[Vue.vShow,l==="checkbox"]])])],16,["id","onClick"])}}});function G1(e,t){const n=Vue.inject(Fe,!1),o=Vue.inject(pn,!1),i={submit:()=>{const r=["",null];return e.name&&(r[0]=e.name,r[1]=t.value),r},reset:()=>{t.value=!1}};return n&&(n.addField(i),Vue.onUnmounted(()=>{n.removeField(i)})),o}const Nn={ensp:" ",emsp:" ",nbsp:" "};function j1(e,{space:t,decode:n}){let o="",i=!1;for(let r of e)t&&Nn[t]&&r===" "&&(r=Nn[t]),i?(r==="n"?o+=Et:r==="\\"?o+="\\":o+="\\"+r,i=!1):r==="\\"?i=!0:o+=r;return n?o.replace(/&nbsp;/g,Nn.nbsp).replace(/&ensp;/g,Nn.ensp).replace(/&emsp;/g,Nn.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):o}function Y1(e,t){return j1(e,t).split(Et)}const X1=X({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=Vue.ref(null);return()=>{const o=[];return t.default&&t.default().forEach(i=>{if(i.shapeFlag&8&&i.type!==Vue.Comment){const r=Y1(i.children,{space:e.space,decode:e.decode}),a=r.length-1;r.forEach((s,l)=>{l===0&&!s||o.push(Vue.createTextVNode(s)),l!==a&&o.push(Vue.createVNode("br"))})}else o.push(i)}),Vue.createVNode("uni-text",{ref:n,selectable:e.selectable?!0:null},[Vue.createVNode("span",null,o)],8,["selectable"])}}}),J1=B({},hu,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator(e){return Du.concat("return").includes(e)}}});let Rr=!1;const Du=["done","go","next","search","send"];function K1(){const e="(prefers-color-scheme: dark)";Rr=String(navigator.platform).indexOf("iP")===0&&String(navigator.vendor).indexOf("Apple")===0&&window.matchMedia(e).media!==e}const Q1=X({name:"Textarea",props:J1,emits:["confirm","linechange",...gu],setup(e,{emit:t,expose:n}){const o=Vue.ref(null),i=Vue.ref(null),{fieldRef:r,state:a,scopedAttrsState:s,fixDisabledColor:l,trigger:c}=pu(e,o,t),u=Vue.computed(()=>a.value.split(Et)),d=Vue.computed(()=>Du.includes(e.confirmType)),f=Vue.ref(0),h=Vue.ref(null);Vue.watch(()=>f.value,b=>{const g=o.value,p=h.value,V=i.value;let S=parseFloat(getComputedStyle(g).lineHeight);isNaN(S)&&(S=p.offsetHeight);var w=Math.round(b/S);c("linechange",{},{height:b,heightRpx:750/window.innerWidth*b,lineCount:w}),e.autoHeight&&(g.style.height="auto",V.style.height=b+"px")});function _({height:b}){f.value=b}function v(b){c("confirm",b,{value:a.value})}function m(b){b.key==="Enter"&&d.value&&b.preventDefault()}function T(b){if(b.key==="Enter"&&d.value){v(b);const g=b.target;!e.confirmHold&&g.blur()}}return K1(),n({$triggerInput:b=>{t("update:modelValue",b.value),t("update:value",b.value),a.value=b.value}}),()=>{let b=e.disabled&&l?Vue.createVNode("textarea",{key:"disabled-textarea",ref:r,value:a.value,tabindex:"-1",readonly:!!e.disabled,maxlength:a.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Rr},style:ba({overflowY:e.autoHeight?"hidden":"auto"},e.cursorColor&&{caretColor:e.cursorColor}),onFocus:g=>g.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):Vue.createVNode("textarea",{key:"textarea",ref:r,value:a.value,disabled:!!e.disabled,maxlength:a.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Rr},style:ba({overflowY:e.autoHeight?"hidden":"auto"},e.cursorColor&&{caretColor:e.cursorColor}),onKeydown:m,onKeyup:T},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return Vue.createVNode("uni-textarea",{ref:o},[Vue.createVNode("div",{ref:i,class:"uni-textarea-wrapper"},[Vue.withDirectives(Vue.createVNode("div",Vue.mergeProps(s.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Vue.vShow,!a.value.length]]),Vue.createVNode("div",{ref:h,class:"uni-textarea-line"},[" "],512),Vue.createVNode("div",{class:"uni-textarea-compute"},[u.value.map(g=>Vue.createVNode("div",null,[g.trim()?g:"."])),Vue.createVNode(Ge,{initial:!0,onResize:_},null,8,["initial","onResize"])]),e.confirmType==="search"?Vue.createVNode("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[b],40,["onSubmit"]):b],512)],512)}}}),Z1=X({name:"View",props:B({},Zm),setup(e,{slots:t}){const n=Vue.ref(null),{hovering:o,binding:i}=Li(e);return()=>{const r=e.hoverClass;return r&&r!=="none"?Vue.createVNode("uni-view",Vue.mergeProps({class:o.value?r:"",ref:n},i),[Vue.renderSlot(t,"default")],16):Vue.createVNode("uni-view",{ref:n},[Vue.renderSlot(t,"default")],512)}}});function Yo(e,t){if(t||(t=e.id),!!t)return e.$options.name.toLowerCase()+"."+t}function Uu(e,t,n){e&&os(n||ft(),e,({type:o,data:i},r)=>{t(o,i,r)})}function Fu(e,t){e&&Mp(t||ft(),e)}function qt(e,t,n,o){const r=Vue.getCurrentInstance().proxy;Vue.onMounted(()=>{Uu(t||Yo(r),e,o),(n||!t)&&Vue.watch(()=>r.id,(a,s)=>{Uu(Yo(r,a),e,o),Fu(s&&Yo(r,s))})}),Vue.onBeforeUnmount(()=>{Fu(t||Yo(r),o)})}function eS(e,t){Vue.onMounted(()=>UniViewJSBridge.on(e,t)),Vue.onBeforeUnmount(()=>UniViewJSBridge.off(e))}let tS=0;function Xo(e){const t=so(),o=Vue.getCurrentInstance().proxy,i=o.$options.name.toLowerCase(),r=e||o.id||`context${tS++}`;return Vue.onMounted(()=>{const a=o.$el;a.__uniContextInfo={id:r,type:i,page:t}}),`${i}.${r}`}function nS(e){return e.__uniContextInfo}function $u(e,t,n,o){G(t)&&Vue.injectHook(e,t.bind(n),o)}function oS(e,t,n){const o=e.mpType||n.$mpType;if(!(!o||o==="component")&&(Object.keys(e).forEach(i=>{if(jg(i,e[i],!1)){const r=e[i];K(r)?r.forEach(a=>$u(i,a,n,t)):$u(i,r,n,t)}}),o==="page")){t.__isVisible=!0;try{let i=t.attrs.__pageQuery;ie(n,Va,i),delete t.attrs.__pageQuery;const r=n.$page;(r==null?void 0:r.openType)!=="preloadPage"&&ie(n,lt)}catch(i){console.error(i.message+Et+i.stack)}}}function iS(e,t,n){oS(e,t,n)}function rS(e,t,n){return e[t]=n}function aS(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function sS(e){const t=e.config.errorHandler;return function(o,i,r){t&&t(o,i,r);const a=e._instance;if(!a||!a.proxy)throw o;a[ut]?ie(a.proxy,ut,o):Vue.logError(o,r,i?i.$.vnode:null,!1)}}function lS(e,t){return e?[...new Set([].concat(e,t))]:t}function cS(e){Ha.forEach(t=>{e[t]=lS})}let Lr;const Jo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",uS=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;typeof atob!="function"?Lr=function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!uS.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");e+="==".slice(2-(e.length&3));for(var t,n="",o,i,r=0;r<e.length;)t=Jo.indexOf(e.charAt(r++))<<18|Jo.indexOf(e.charAt(r++))<<12|(o=Jo.indexOf(e.charAt(r++)))<<6|(i=Jo.indexOf(e.charAt(r++))),n+=o===64?String.fromCharCode(t>>16&255):i===64?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,t&255);return n}:Lr=atob;function dS(e){return decodeURIComponent(Lr(e).split("").map(function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)}).join(""))}function Br(){const e=uni.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||t.length!==3)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse(dS(t[1]))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}return n.tokenExpired=n.exp*1e3,delete n.exp,delete n.iat,n}function fS(e){e.uniIDHasRole=function(t){const{role:n}=Br();return n.indexOf(t)>-1},e.uniIDHasPermission=function(t){const{permission:n}=Br();return this.uniIDHasRole("admin")||n.indexOf(t)>-1},e.uniIDTokenValid=function(){const{tokenExpired:t}=Br();return t>Date.now()}}function hS(e){const t=e.config;t.errorHandler=Xg(e,sS),cS(t.optionMergeStrategies);const n=t.globalProperties;__UNI_FEATURE_UNI_CLOUD__&&fS(n),n.$set=rS,n.$applyOptions=iS,n.$callMethod=aS,Yg(e)}function gS(e){const t=VueRouter.createRouter(_S());t.beforeEach((n,o)=>{n&&o&&n.meta.isTabBar&&o.meta.isTabBar&&mS(o.meta.tabBarIndex)}),e.router=t,e.use(t)}let zu=Object.create(null);function pS(e){return zu[e]}function mS(e){typeof window!="undefined"&&(zu[e]={left:window.pageXOffset,top:window.pageYOffset})}const vS=(e,t,n)=>{if(n)return n;if(e&&t&&e.meta.isTabBar&&t.meta.isTabBar){const o=pS(e.meta.tabBarIndex);if(o)return o}return{left:0,top:0}};function _S(){return{history:bS(),strict:!!__uniConfig.router.strict,routes:__uniRoutes,scrollBehavior:vS}}function yS(e=1){const t=Ft(),n=t.length-1,o=n-e;for(let i=n;i>o;i--){const r=nt(t[i]);Ro(Lo(r.path,r.id),!1)}}function bS(){let{routerBase:e}=__uniConfig.router;e==="/"&&(e="");const t=__UNI_FEATURE_ROUTER_MODE__==="history"?VueRouter.createWebHistory(e):VueRouter.createWebHashHistory(e);return t.listen((n,o,i)=>{i.direction==="back"&&yS(Math.abs(i.delta))}),t}const Wu={install(e){hS(e),Tm(e),Gm(e),e.config.warnHandler||(e.config.warnHandler=wS),__UNI_FEATURE_PAGES__&&gS(e)}};function wS(e,t,n){if(t){if(t.$.type.name==="PageMetaHead")return;const r=t.$.parent;if(r&&r.type.name==="PageMeta")return}const o=[`[Vue warn]: ${e}`];n.length&&o.push(`
`,n),console.warn(...o)}const SS={class:"uni-async-loading"},TS=Vue.createVNode("i",{class:"uni-loading"},null,-1),Ko=ge({name:"AsyncLoading",render(){return Vue.openBlock(),Vue.createBlock("div",SS,[TS])}});function VS(){window.location.reload()}const Qo=ge({name:"AsyncError",setup(){mp();const{t:e}=D();return()=>Vue.createVNode("div",{class:"uni-async-error",onClick:VS},[e("uni.async.error")],8,["onClick"])}});let Gt;function CS(){return Gt}function AS(e){Gt=e,Object.defineProperty(Gt.$.ctx,"$children",{get(){return Ft().map(n=>n.$vm)}});const t=Gt.$.appContext.app;t.component(Ko.name)||t.component(Ko.name,Ko),t.component(Qo.name)||t.component(Qo.name,Qo),Om(Gt),jm(Gt),Pm(),cm()}function Hu(e,{clone:t,init:n,setup:o,before:i}){t&&(e=B({},e)),i&&i(e);const r=e.setup;return e.setup=(a,s)=>{const l=Vue.getCurrentInstance();if(n(l.proxy),o(l),r)return r(a,s)},e}function Dr(e,t){return e&&(e.__esModule||e[Symbol.toStringTag]==="Module")?Hu(e.default,t):Hu(e,t)}function ES(e,t){return Dr(e,{init:n=>{n.$page={id:t}},setup(n){n.$pageInstance=n}})}function kS(e){return Dr(e,{clone:!0,init:pb,setup(t){t.$pageInstance=t;const n=mn(),o=$a(n.query);t.attrs.__pageQuery=o,nt(t.proxy).options=o,t.proxy.options=o;const i=Pt();return t.onReachBottom=Vue.reactive([]),t.onPageScroll=Vue.reactive([]),Vue.watch([t.onReachBottom,t.onPageScroll],()=>{const r=dt();t.proxy===r&&Nc(t,i)},{once:!0}),Vue.onBeforeMount(()=>{kc(t,i)}),Vue.onMounted(()=>{bb(t);const{onReady:r}=t;r&&At(r),qu(n)}),Vue.onBeforeActivate(()=>{if(!t.__isVisible){kc(t,i),t.__isVisible=!0;const{onShow:r}=t;r&&At(r),Vue.nextTick(()=>{qu(n)})}}),Vue.onBeforeDeactivate(()=>{if(t.__isVisible&&!t.__isUnload){t.__isVisible=!1;{const{onHide:r}=t;r&&At(r)}}}),Np(i.id),Vue.onBeforeUnmount(()=>{xp(i.id)}),o}})}function IS(e){return Dr(e,{init:AS,setup(t){const n=mn(),o=()=>{Dv(t);const{onLaunch:i,onShow:r,onPageNotFound:a}=t,s=n.path.slice(1),l=Hb({path:s||__uniRoutes[0].meta.route,query:$a(n.query)});if(i&&At(i,l),r&&At(r,l),__UNI_FEATURE_PAGES__&&!n.matched.length){const c={notFound:!0,openType:"appLaunch",path:n.path,query:{},scene:1001};Vc(),a&&At(a,c)}};return __UNI_FEATURE_PAGES__?VueRouter.useRouter().isReady().then(o):Vue.onBeforeMount(o),Vue.onMounted(()=>{window.addEventListener("resize",Ti(NS,50,{setTimeout,clearTimeout})),window.addEventListener("message",xS),document.addEventListener("visibilitychange",MS),PS()}),n.query},before(t){t.mpType="app";const{setup:n}=t,o=()=>(Vue.openBlock(),Vue.createBlock(qf));t.setup=(i,r)=>{const a=n&&n(i,r);return G(a)?o:a},t.render=o}})}function NS(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=uni.getSystemInfoSync(),r=Math.abs(Number(window.orientation))===90?"landscape":"portrait";UniServiceJSBridge.emit(Xn,{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function xS(e){fe(e.data)&&e.data.type===Cg&&UniServiceJSBridge.emit(Oa,e.data.data,e.data.pageId)}function MS(){const{emit:e}=UniServiceJSBridge;document.visibilityState==="visible"?e(Ma,qc()):e(Pa)}function PS(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=n=>{UniServiceJSBridge.emit(Ye,{theme:n.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}function qu(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&ie("onTabItemTap",{index:n,text:t,pagePath:o})}function xn(e){e=e>0&&e<1/0?e:0;const t=Math.floor(e/3600),n=Math.floor(e%3600/60),o=Math.floor(e%3600%60),i=(t<10?"0":"")+t,r=(n<10?"0":"")+n,a=(o<10?"0":"")+o;let s=r+":"+a;return i!=="00"&&(s=i+":"+s),s}function OS(e,t,n){const o=Vue.reactive({gestureType:"none",volumeOld:0,volumeNew:0,currentTimeOld:0,currentTimeNew:0}),i={x:0,y:0};function r(u){const d=u.targetTouches[0];i.x=d.pageX,i.y=d.pageY,o.gestureType="none",o.volumeOld=0,o.currentTimeOld=o.currentTimeNew=0}function a(u){function d(){u.stopPropagation(),u.preventDefault()}n.fullscreen&&d();const f=o.gestureType;if(f==="stop")return;const h=u.targetTouches[0],_=h.pageX,v=h.pageY,m=i,T=t.value;if(f==="progress"?l(_-m.x):f==="volume"&&c(v-m.y),f==="none")if(Math.abs(_-m.x)>Math.abs(v-m.y)){if(!e.enableProgressGesture){o.gestureType="stop";return}o.gestureType="progress",o.currentTimeOld=o.currentTimeNew=T.currentTime,n.fullscreen||d()}else{if(!e.pageGesture){o.gestureType="stop";return}o.gestureType="volume",o.volumeOld=T.volume,n.fullscreen||d()}}function s(u){const d=t.value;o.gestureType!=="none"&&o.gestureType!=="stop"&&(u.stopPropagation(),u.preventDefault()),o.gestureType==="progress"&&o.currentTimeOld!==o.currentTimeNew&&(d.currentTime=o.currentTimeNew),o.gestureType="none"}function l(u){const f=t.value.duration;let h=u/600*f+o.currentTimeOld;h<0?h=0:h>f&&(h=f),o.currentTimeNew=h}function c(u){const d=t.value,f=o.volumeOld;let h;typeof f=="number"&&(h=f-u/200,h<0?h=0:h>1&&(h=1),d.volume=h,o.volumeNew=h)}return{state:o,onTouchstart:r,onTouchmove:a,onTouchend:s}}function RS(e,t,n,o,i){const r=Vue.reactive({fullscreen:!1}),a=/^Apple/.test(navigator.vendor);function s(f,h){h&&document.fullscreenEnabled||l(!!(document.fullscreenElement||document.webkitFullscreenElement))}function l(f){r.fullscreen=f,e("fullscreenchange",{},{fullScreen:f,direction:"vertical"})}function c(f){const h=i.value,_=t.value,v=n.value;let m;f?(document.fullscreenEnabled||document.webkitFullscreenEnabled)&&(!a||o.userAction)?_[document.fullscreenEnabled?"requestFullscreen":"webkitRequestFullscreen"]():v.webkitEnterFullScreen?v.webkitEnterFullScreen():(m=!0,_.remove(),_.classList.add("uni-video-type-fullscreen"),document.body.appendChild(_)):document.fullscreenEnabled||document.webkitFullscreenEnabled?document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen():v.webkitExitFullScreen?v.webkitExitFullScreen():(m=!0,_.remove(),_.classList.remove("uni-video-type-fullscreen"),h.appendChild(_)),m&&l(f)}function u(){c(!0)}function d(){c(!1)}return Vue.onBeforeUnmount(d),{state:r,onFullscreenChange:s,emitFullscreenChange:l,toggleFullscreen:c,requestFullScreen:u,exitFullScreen:d}}function LS(e,t,n){const o=Vue.ref(null),i=Vue.computed(()=>Q(e.src)),r=Vue.computed(()=>e.muted==="true"||e.muted===!0),a=Vue.reactive({start:!1,src:i,playing:!1,currentTime:0,duration:0,progress:0,buffered:0,muted:r});Vue.watch(()=>i.value,()=>{a.playing=!1,a.currentTime=0}),Vue.watch(()=>a.buffered,S=>{n("progress",{},{buffered:S})}),Vue.watch(()=>r.value,S=>{const w=o.value;w.muted=S});function s({target:S}){a.duration=S.duration}function l(S){const w=Number(e.initialTime)||0,A=S.target;w>0&&(A.currentTime=w),n("loadedmetadata",S,{width:A.videoWidth,height:A.videoHeight,duration:A.duration}),c(S)}function c(S){const w=S.target,A=w.buffered;A.length&&(a.buffered=A.end(A.length-1)/w.duration*100)}function u(S){n("waiting",S,{})}function d(S){a.playing=!1,n("error",S,{})}function f(S){a.start=!0,a.playing=!0,n("play",S,{})}function h(S){a.playing=!1,n("pause",S,{})}function _(S){a.playing=!1,n("ended",S,{})}function v(S){const w=S.target,A=a.currentTime=w.currentTime;n("timeupdate",S,{currentTime:A,duration:w.duration})}function m(){const S=o.value;a.playing?S.pause():S.play()}function T(){const S=o.value;a.start=!0,S.play()}function b(){o.value.pause()}function g(S){const w=o.value;S=Number(S),typeof S=="number"&&!isNaN(S)&&(w.currentTime=S)}function p(){g(0),b()}function V(S){const w=o.value;w.playbackRate=S}return{videoRef:o,state:a,play:T,pause:b,stop:p,seek:g,playbackRate:V,toggle:m,onDurationChange:s,onLoadedMetadata:l,onProgress:c,onWaiting:u,onVideoError:d,onPlay:f,onPause:h,onEnded:_,onTimeUpdate:v}}function BS(e,t,n){const o=Vue.ref(null),i=Vue.ref(null),r=Vue.computed(()=>e.showCenterPlayBtn&&!t.start),a=Vue.ref(!0),s=Vue.computed(()=>!r.value&&e.controls&&a.value),l=Vue.reactive({touching:!1,controlsTouching:!1,centerPlayBtnShow:r,controlsShow:s,controlsVisible:a});function c(_){const v=o.value;let m=_.target,T=_.offsetX;for(;m&&m!==v;)T+=m.offsetLeft,m=m.parentNode;const b=v.offsetWidth;let g=0;T>=0&&T<=b&&(g=T/b,n(t.duration*g))}function u(){l.controlsVisible=!l.controlsVisible}let d;function f(){d=setTimeout(()=>{l.controlsVisible=!1},3e3)}function h(){d&&(clearTimeout(d),d=null)}return Vue.onBeforeUnmount(()=>{d&&clearTimeout(d)}),Vue.watch(()=>l.controlsShow&&t.playing&&!l.controlsTouching,_=>{_?f():h()}),Vue.watch([()=>t.currentTime,()=>{e.duration}],function(){l.touching||(t.progress=t.currentTime/t.duration*100)}),Vue.onMounted(()=>{const _=kt(!1);let v,m,T=!0,b;const g=i.value;function p(S){const w=S.targetTouches[0],A=w.pageX,x=w.pageY;if(T&&Math.abs(A-v)<Math.abs(x-m)){V(S);return}T=!1;const C=o.value.offsetWidth;let I=b+(A-v)/C*100;I<0?I=0:I>100&&(I=100),t.progress=I,S.preventDefault(),S.stopPropagation()}function V(S){l.controlsTouching=!1,l.touching&&(g.removeEventListener("touchmove",p,_),T||(S.preventDefault(),S.stopPropagation(),n(t.duration*t.progress/100)),l.touching=!1)}g.addEventListener("touchstart",S=>{l.controlsTouching=!0;const w=S.targetTouches[0];v=w.pageX,m=w.pageY,b=t.progress,T=!0,l.touching=!0,g.addEventListener("touchmove",p,_)}),g.addEventListener("touchend",V),g.addEventListener("touchcancel",V)}),{state:l,progressRef:o,ballRef:i,clickProgress:c,toggleControls:u,autoHideStart:f,autoHideEnd:h}}function DS(e,t){const n=Vue.ref(null),o=Vue.reactive({enable:!!e.enableDanmu});let i={time:0,index:-1};const r=K(e.danmuList)?JSON.parse(JSON.stringify(e.danmuList)):[];r.sort(function(u,d){return(u.time||0)-(d.time||0)});function a(){o.enable=!o.enable}function s(u){const f=u.target.currentTime,h=i,_={time:f,index:h.index};if(f>h.time)for(let v=h.index+1;v<r.length;v++){const m=r[v];if(f>=(m.time||0))_.index=v,t.playing&&o.enable&&l(m);else break}else if(f<h.time)for(let v=h.index-1;v>-1;v--){const m=r[v];if(f<=(m.time||0))_.index=v-1;else break}i=_}function l(u){const d=document.createElement("p");d.className="uni-video-danmu-item",d.innerText=u.text;let f=`bottom: ${Math.random()*100}%;color: ${u.color};`;d.setAttribute("style",f),n.value.appendChild(d),setTimeout(function(){f+="left: 0;-webkit-transform: translateX(-100%);transform: translateX(-100%);",d.setAttribute("style",f),setTimeout(function(){d.remove()},4e3)},17)}function c(u){r.splice(i.index+1,0,{text:String(u.text),color:u.color,time:t.currentTime||0})}return{state:o,danmuRef:n,updateDanmu:s,toggleDanmu:a,sendDanmu:c}}function US(e,t,n,o,i,r,a,s){const l={play:e,stop:n,pause:t,seek:o,sendDanmu:i,playbackRate:r,requestFullScreen:a,exitFullScreen:s},c=Xo();qt((u,d)=>{let f;switch(u){case"seek":f=d.position;break;case"sendDanmu":f=d;break;case"playbackRate":f=d.rate;break}u in l&&l[u](f)},c,!0)}const FS=X({name:"Video",props:{id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default(){return[]}},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},showPlayBtn:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0}},emits:["fullscreenchange","progress","loadedmetadata","waiting","error","play","pause","ended","timeupdate"],setup(e,{emit:t,attrs:n,slots:o}){const i=Vue.ref(null),r=Vue.ref(null),a=ve(i,t),{state:s}=du(),{$attrs:l}=qo({excludeListeners:!0}),{t:c}=D();Vp();const{videoRef:u,state:d,play:f,pause:h,stop:_,seek:v,playbackRate:m,toggle:T,onDurationChange:b,onLoadedMetadata:g,onProgress:p,onWaiting:V,onVideoError:S,onPlay:w,onPause:A,onEnded:x,onTimeUpdate:E}=LS(e,n,a),{state:C,danmuRef:I,updateDanmu:M,toggleDanmu:F,sendDanmu:k}=DS(e,d),{state:L,onFullscreenChange:z,emitFullscreenChange:N,toggleFullscreen:P,requestFullScreen:O,exitFullScreen:$}=RS(a,r,u,s,i),{state:H,onTouchstart:q,onTouchend:U,onTouchmove:re}=OS(e,u,L),{state:ae,progressRef:Me,ballRef:De,clickProgress:st,toggleControls:Hn}=BS(e,d,v);return US(f,h,_,v,k,m,O,$),()=>Vue.createVNode("uni-video",{ref:i,id:e.id,onClick:Hn},[Vue.createVNode("div",{ref:r,class:"uni-video-container",onTouchstart:q,onTouchend:U,onTouchmove:re,onFullscreenchange:Vue.withModifiers(z,["stop"]),onWebkitfullscreenchange:Vue.withModifiers(_e=>z(_e,!0),["stop"])},[Vue.createVNode("video",Vue.mergeProps({ref:u,style:{"object-fit":e.objectFit},muted:!!e.muted,loop:!!e.loop,src:d.src,poster:e.poster,autoplay:!!e.autoplay},l.value,{class:"uni-video-video","webkit-playsinline":!0,playsinline:!0,onDurationchange:b,onLoadedmetadata:g,onProgress:p,onWaiting:V,onError:S,onPlay:w,onPause:A,onEnded:x,onTimeupdate:_e=>{E(_e),M(_e)},onWebkitbeginfullscreen:()=>N(!0),onX5videoenterfullscreen:()=>N(!0),onWebkitendfullscreen:()=>N(!1),onX5videoexitfullscreen:()=>N(!1)}),null,16,["muted","loop","src","poster","autoplay","webkit-playsinline","playsinline","onDurationchange","onLoadedmetadata","onProgress","onWaiting","onError","onPlay","onPause","onEnded","onTimeupdate","onWebkitbeginfullscreen","onX5videoenterfullscreen","onWebkitendfullscreen","onX5videoexitfullscreen"]),Vue.withDirectives(Vue.createVNode("div",{class:"uni-video-bar uni-video-bar-full",onClick:Vue.withModifiers(()=>{},["stop"])},[Vue.createVNode("div",{class:"uni-video-controls"},[Vue.withDirectives(Vue.createVNode("div",{class:{"uni-video-control-button":!0,"uni-video-control-button-play":!d.playing,"uni-video-control-button-pause":d.playing},onClick:Vue.withModifiers(T,["stop"])},null,10,["onClick"]),[[Vue.vShow,e.showPlayBtn]]),Vue.withDirectives(Vue.createVNode("div",{class:"uni-video-current-time"},[xn(d.currentTime)],512),[[Vue.vShow,e.showProgress]]),Vue.withDirectives(Vue.createVNode("div",{ref:Me,class:"uni-video-progress-container",onClick:Vue.withModifiers(st,["stop"])},[Vue.createVNode("div",{class:"uni-video-progress"},[Vue.createVNode("div",{style:{width:d.buffered+"%"},class:"uni-video-progress-buffered"},null,4),Vue.createVNode("div",{ref:De,style:{left:d.progress+"%"},class:"uni-video-ball"},[Vue.createVNode("div",{class:"uni-video-inner"},null)],4)])],8,["onClick"]),[[Vue.vShow,e.showProgress]]),Vue.withDirectives(Vue.createVNode("div",{class:"uni-video-duration"},[xn(Number(e.duration)||d.duration)],512),[[Vue.vShow,e.showProgress]])]),Vue.withDirectives(Vue.createVNode("div",{class:{"uni-video-danmu-button":!0,"uni-video-danmu-button-active":C.enable},onClick:Vue.withModifiers(F,["stop"])},[c("uni.video.danmu")],10,["onClick"]),[[Vue.vShow,e.danmuBtn]]),Vue.withDirectives(Vue.createVNode("div",{class:{"uni-video-fullscreen":!0,"uni-video-type-fullscreen":L.fullscreen},onClick:Vue.withModifiers(()=>P(!L.fullscreen),["stop"])},null,10,["onClick"]),[[Vue.vShow,e.showFullscreenBtn]])],8,["onClick"]),[[Vue.vShow,ae.controlsShow]]),Vue.withDirectives(Vue.createVNode("div",{ref:I,style:"z-index: 0;",class:"uni-video-danmu"},null,512),[[Vue.vShow,d.start&&C.enable]]),ae.centerPlayBtnShow&&Vue.createVNode("div",{class:"uni-video-cover",onClick:Vue.withModifiers(()=>{},["stop"])},[Vue.createVNode("div",{class:"uni-video-cover-play-button",onClick:Vue.withModifiers(f,["stop"])},null,8,["onClick"]),Vue.createVNode("p",{class:"uni-video-cover-duration"},[xn(Number(e.duration)||d.duration)])],8,["onClick"]),Vue.createVNode("div",{class:{"uni-video-toast":!0,"uni-video-toast-volume":H.gestureType==="volume"}},[Vue.createVNode("div",{class:"uni-video-toast-title"},[c("uni.video.volume")]),Vue.createVNode("svg",{class:"uni-video-toast-icon",width:"200px",height:"200px",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},[Vue.createVNode("path",{d:"M475.400704 201.19552l0 621.674496q0 14.856192-10.856448 25.71264t-25.71264 10.856448-25.71264-10.856448l-190.273536-190.273536-149.704704 0q-14.856192 0-25.71264-10.856448t-10.856448-25.71264l0-219.414528q0-14.856192 10.856448-25.71264t25.71264-10.856448l149.704704 0 190.273536-190.273536q10.856448-10.856448 25.71264-10.856448t25.71264 10.856448 10.856448 25.71264zm219.414528 310.837248q0 43.425792-24.28416 80.851968t-64.2816 53.425152q-5.71392 2.85696-14.2848 2.85696-14.856192 0-25.71264-10.570752t-10.856448-25.998336q0-11.999232 6.856704-20.284416t16.570368-14.2848 19.427328-13.142016 16.570368-20.284416 6.856704-32.569344-6.856704-32.569344-16.570368-20.284416-19.427328-13.142016-16.570368-14.2848-6.856704-20.284416q0-15.427584 10.856448-25.998336t25.71264-10.570752q8.57088 0 14.2848 2.85696 39.99744 15.427584 64.2816 53.139456t24.28416 81.137664zm146.276352 0q0 87.422976-48.56832 161.41824t-128.5632 107.707392q-7.428096 2.85696-14.2848 2.85696-15.427584 0-26.284032-10.856448t-10.856448-25.71264q0-22.284288 22.284288-33.712128 31.997952-16.570368 43.425792-25.141248 42.283008-30.855168 65.995776-77.423616t23.712768-99.136512-23.712768-99.136512-65.995776-77.423616q-11.42784-8.57088-43.425792-25.141248-22.284288-11.42784-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 79.99488 33.712128 128.5632 107.707392t48.56832 161.41824zm146.276352 0q0 131.42016-72.566784 241.41312t-193.130496 161.989632q-7.428096 2.85696-14.856192 2.85696-14.856192 0-25.71264-10.856448t-10.856448-25.71264q0-20.570112 22.284288-33.712128 3.999744-2.285568 12.85632-5.999616t12.85632-5.999616q26.284032-14.2848 46.854144-29.140992 70.281216-51.996672 109.707264-129.705984t39.426048-165.132288-39.426048-165.132288-109.707264-129.705984q-20.570112-14.856192-46.854144-29.140992-3.999744-2.285568-12.85632-5.999616t-12.85632-5.999616q-22.284288-13.142016-22.284288-33.712128 0-14.856192 10.856448-25.71264t25.71264-10.856448q7.428096 0 14.856192 2.85696 120.563712 51.996672 193.130496 161.989632t72.566784 241.41312z"},null)]),Vue.createVNode("div",{class:"uni-video-toast-value"},[Vue.createVNode("div",{style:{width:H.volumeNew*100+"%"},class:"uni-video-toast-value-content"},[Vue.createVNode("div",{class:"uni-video-toast-volume-grids"},[Vue.renderList(10,()=>Vue.createVNode("div",{class:"uni-video-toast-volume-grids-item"},null))])],4)])],2),Vue.createVNode("div",{class:{"uni-video-toast":!0,"uni-video-toast-progress":H.gestureType==="progress"}},[Vue.createVNode("div",{class:"uni-video-toast-title"},[xn(H.currentTimeNew)," / ",xn(d.duration)])],2),Vue.createVNode("div",{class:"uni-video-slots"},[o.default&&o.default()])],40,["onTouchstart","onTouchend","onTouchmove","onFullscreenchange","onWebkitfullscreenchange"])],8,["id","onClick"])}}),$S=({name:e,arg:t})=>{e==="postMessage"||uni[e](t)},zS=te(()=>UniServiceJSBridge.on(Oa,$S)),WS=X({inheritAttrs:!1,name:"WebView",props:{src:{type:String,default:""},fullscreen:{type:Boolean,default:!0}},setup(e){zS();const t=Vue.ref(null),n=Vue.ref(null),{$attrs:o,$excludeAttrs:i,$listeners:r}=qo({excludeListeners:!0});let a;return(()=>{const l=document.createElement("iframe");Vue.watchEffect(()=>{for(const c in o.value)if(le(o.value,c)){const u=o.value[c];l[c]=u}}),Vue.watchEffect(()=>{l.src=Q(e.src)}),n.value=l,a=HS(t,n,e.fullscreen),e.fullscreen&&document.body.appendChild(l)})(),Vue.onMounted(()=>{var l;a(),!e.fullscreen&&((l=t.value)==null||l.appendChild(n.value))}),Vue.onActivated(()=>{e.fullscreen&&(n.value.style.display="block")}),Vue.onDeactivated(()=>{e.fullscreen&&(n.value.style.display="none")}),Vue.onBeforeUnmount(()=>{e.fullscreen&&document.body.removeChild(n.value)}),()=>Vue.createVNode(Vue.Fragment,null,[Vue.createVNode("uni-web-view",Vue.mergeProps({class:e.fullscreen?"uni-webview--fullscreen":""},r.value,i.value,{ref:t}),[Vue.createVNode(Ge,{onResize:a},null,8,["onResize"])],16)])}});function HS(e,t,n){return()=>{var i,r;if(n){const{top:a,left:s,width:l,height:c}=e.value.getBoundingClientRect();yi(t.value,{position:"absolute",display:"block",border:"0",top:a+"px",left:s+"px",width:l+"px",height:c+"px"})}else yi(t.value,{width:((i=e.value)==null?void 0:i.style.width)||"300px",height:((r=e.value)==null?void 0:r.style.height)||"150px"})}}let qS=0;function Ur(e,t,n,o){var i=document.createElement("script"),r=t.callback||"callback",a="__uni_jsonp_callback_"+qS++,s=t.timeout||3e4,l;function c(){clearTimeout(l),delete window[a],i.remove()}window[a]=u=>{G(n)&&n(u),c()},i.onerror=()=>{G(o)&&o(),c()},l=setTimeout(function(){G(o)&&o(),c()},s),i.src=e+(e.indexOf("?")>=0?"&":"?")+r+"="+a,document.body.appendChild(i)}function Gu(e){function t(){const l=this.div;this.getPanes().floatPane.appendChild(l)}function n(){const l=this.div.parentNode;l&&l.removeChild(this.div)}function o(){const l=this.option;this.Text=new e.Text({text:l.content,anchor:"bottom-center",offset:new e.Pixel(0,l.offsetY-16),style:{padding:(l.padding||8)+"px","line-height":(l.fontSize||14)+"px","border-radius":(l.borderRadius||0)+"px","border-color":`${l.bgColor||"#fff"} transparent transparent`,"background-color":l.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(l.fontSize||14)+"px",color:l.color||"#000"},position:l.position}),(e.event||e.Event).addListener(this.Text,"click",()=>{this.callback()}),this.Text.setMap(l.map)}function i(){}function r(){this.Text&&this.option.map.remove(this.Text)}function a(){this.Text&&this.option.map.remove(this.Text)}class s{constructor(c={},u){this.createAMapText=o,this.removeAMapText=r,this.createBMapText=i,this.removeBMapText=a,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=c||{};const d=this.visible=this.alwaysVisible=c.display==="ALWAYS";if(ue())this.callback=u,this.visible&&this.createAMapText();else if(oe())this.visible&&this.createBMapText();else{const f=c.map;this.position=c.position,this.index=1;const h=this.div=document.createElement("div"),_=h.style;_.position="absolute",_.whiteSpace="nowrap",_.transform="translateX(-50%) translateY(-100%)",_.zIndex="1",_.boxShadow=c.boxShadow||"none",_.display=d?"block":"none";const v=this.triangle=document.createElement("div");v.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(c),h.appendChild(v),f&&this.setMap(f)}}set onclick(c){this.div.onclick=c}get onclick(){return this.div.onclick}setOption(c){this.option=c,c.display==="ALWAYS"?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,ue()?this.visible&&this.createAMapText():oe()?this.visible&&this.createBMapText():(this.setPosition(c.position),this.setStyle(c))}setStyle(c){const u=this.div,d=u.style;u.innerText=c.content||"",d.lineHeight=(c.fontSize||14)+"px",d.fontSize=(c.fontSize||14)+"px",d.padding=(c.padding||8)+"px",d.color=c.color||"#000",d.borderRadius=(c.borderRadius||0)+"px",d.backgroundColor=c.bgColor||"#fff",d.marginTop="-"+((c.top||0)+5)+"px",this.triangle.style.borderColor=`${c.bgColor||"#fff"} transparent transparent`}setPosition(c){this.position=c,this.draw()}draw(){const c=this.getProjection();if(!this.position||!this.div||!c)return;const u=c.fromLatLngToDivPixel(this.position),d=this.div.style;d.left=u.x+"px",d.top=u.y+"px"}changed(){const c=this.div.style;c.display=this.visible?"block":"none"}}if(!ue()&&!oe()){const l=new(e.OverlayView||e.Overlay);s.prototype.setMap=l.setMap,s.prototype.getMap=l.getMap,s.prototype.getPanes=l.getPanes,s.prototype.getProjection=l.getProjection,s.prototype.map_changed=l.map_changed,s.prototype.set=l.set,s.prototype.get=l.get,s.prototype.setOptions=l.setValues,s.prototype.bindTo=l.bindTo,s.prototype.bindsTo=l.bindsTo,s.prototype.notify=l.notify,s.prototype.setValues=l.setValues,s.prototype.unbind=l.unbind,s.prototype.unbindAll=l.unbindAll,s.prototype.addListener=l.addListener}return s}let Be;const ju={},GS="__map_callback__";function Fr(e,t){const n=ot();if(!n.key){console.error("Map key not configured.");return}const o=ju[n.type]=ju[n.type]||[];if(Be)t(Be);else if(window[n.type]&&window[n.type].maps)Be=ue()||oe()?window[n.type]:window[n.type].maps,Be.Callout=Be.Callout||Gu(Be),t(Be);else if(o.length)o.push(t);else{o.push(t);const i=window,r=GS+n.type;i[r]=function(){delete i[r],Be=ue()||oe()?window[n.type]:window[n.type].maps,Be.Callout=Gu(Be),o.forEach(l=>l(Be)),o.length=0},ue()&&YS(n);const a=document.createElement("script");let s=jS(n.type);n.type===Ne.QQ&&e.push("geometry"),e.length&&(s+=`libraries=${e.join("%2C")}&`),n.type===Ne.BMAP?a.src=`${s}ak=${n.key}&callback=${r}`:a.src=`${s}key=${n.key}&callback=${r}`,a.onerror=function(){console.error("Map load failed.")},document.body.appendChild(a)}}const jS=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"})[e];function YS(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}const Yu="M13.3334375 16 q0.033125 1.1334375 0.783125 1.8834375 q0.75 0.75 1.8834375 0.75 q1.1334375 0 1.8834375 -0.75 q0.75 -0.75 0.75 -1.8834375 q0 -1.1334375 -0.75 -1.8834375 q-0.75 -0.75 -1.8834375 -0.75 q-1.1334375 0 -1.8834375 0.75 q-0.75 0.75 -0.783125 1.8834375 ZM30.9334375 14.9334375 l-1.1334375 0 q-0.5 -5.2 -4.0165625 -8.716875 q-3.516875 -3.5165625 -8.716875 -4.0165625 l0 -1.1334375 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 1.1334375 q-5.2 0.5 -8.716875 4.0165625 q-3.5165625 3.516875 -4.0165625 8.716875 l-1.1334375 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l1.1334375 0 q0.5 5.2 4.0165625 8.716875 q3.516875 3.5165625 8.716875 4.0165625 l0 1.1334375 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -1.1334375 q5.2 -0.5 8.716875 -4.0165625 q3.5165625 -3.516875 4.0165625 -8.716875 l1.1334375 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 ZM17.0665625 27.6665625 l0 -2.0665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 2.0665625 q-4.3 -0.4665625 -7.216875 -3.383125 q-2.916875 -2.916875 -3.3834375 -7.216875 l2.0665625 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 l-2.0665625 0 q0.4665625 -4.3 3.3834375 -7.216875 q2.9165625 -2.916875 7.216875 -3.3834375 l0 2.0665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -2.0665625 q4.3 0.4665625 7.216875 3.3834375 q2.9165625 2.9165625 3.383125 7.216875 l-2.0665625 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l2.0665625 0 q-0.4665625 4.3 -3.383125 7.216875 q-2.916875 2.9165625 -7.216875 3.383125 Z",Xu="data:image/png;base64,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",Ju="data:image/png;base64,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";var Ne=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(Ne||{});function ot(){return __uniConfig.bMapKey?{type:"BMapGL",key:__uniConfig.bMapKey}:__uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:__uniConfig.googleMapKey?{type:"google",key:__uniConfig.googleMapKey}:__uniConfig.aMapKey?{type:"AMap",key:__uniConfig.aMapKey,securityJsCode:__uniConfig.aMapSecurityJsCode,serviceHost:__uniConfig.aMapServiceHost}:{type:"",key:""}}let Ku=!1,Qu=!1;const ue=()=>Qu?Ku:(Qu=!0,Ku=ot().type==="AMap"),oe=()=>ot().type==="BMapGL";function Zu(e,t,n){const o=ot(),i=["google"];return e&&e.toUpperCase()==="WGS84"||i.includes(o.type)||n?Promise.resolve(t):o.type==="qq"?new Promise(r=>{Ur(`https://apis.map.qq.com/ws/coord/v1/translate?type=1&locations=${t.latitude},${t.longitude}&key=${o.key}&output=jsonp`,{callback:"callback"},a=>{if("locations"in a&&a.locations.length){const{lng:s,lat:l}=a.locations[0];r({longitude:s,latitude:l,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else r(t)},()=>r(t))}):o.type==="AMap"?new Promise(r=>{Fr([],()=>{window.AMap.convertFrom([t.longitude,t.latitude],"gps",(a,s)=>{if(s.info==="ok"&&s.locations.length){const{lat:l,lng:c}=s.locations[0];r({longitude:c,latitude:l,altitude:t.altitude,accuracy:t.accuracy,altitudeAccuracy:t.altitudeAccuracy,heading:t.heading,speed:t.speed})}else r(t)})})}):Promise.reject(new Error("translate coordinate system faild"))}const XS={id:{type:[Number,String],default:""},latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},title:{type:String,default:""},iconPath:{type:String,require:!0},rotate:{type:[Number,String],default:0},alpha:{type:[Number,String],default:1},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""},callout:{type:Object,default:null},label:{type:Object,default:null},anchor:{type:Object,default:null},clusterId:{type:[Number,String],default:""},customCallout:{type:Object,default:null},ariaLabel:{type:String,default:""}};function JS(e){const t="uni-map-marker-label-"+e,n=document.createElement("style");return n.id=t,document.head.appendChild(n),Vue.onUnmounted(()=>{n.remove()}),function(i){const r=Object.assign({},i,{position:"absolute",top:"70px",borderStyle:"solid"}),a=document.createElement("div");return Object.keys(r).forEach(s=>{a.style[s]=r[s]||""}),n.innerText=`.${t}{${a.getAttribute("style")}}`,t}}const ed=ge({name:"MapMarker",props:XS,setup(e){const t=String(isNaN(Number(e.id))?"":e.id),n=Vue.inject("onMapReady"),o=JS(t);let i;function r(){i&&(i.label&&"setMap"in i.label&&i.label.setMap(null),i.callout&&a(i.callout),i.setMap(null))}function a(s){ue()?s.removeAMapText():s.setMap(null)}if(n((s,l,c)=>{function u(f){const h=f.title;let _;ue()?_=new l.LngLat(f.longitude,f.latitude):oe()?_=new l.Point(f.longitude,f.latitude):_=new l.LatLng(f.latitude,f.longitude);const v=new Image;let m=0;v.onload=()=>{const T=f.anchor||{};let b,g,p,V,S=typeof T.x=="number"?T.x:.5,w=typeof T.y=="number"?T.y:1;f.iconPath&&(f.width||f.height)?(g=f.width||v.width/v.height*f.height,p=f.height||v.height/v.width*f.width):(g=v.width/2,p=v.height/2),m=p,V=p-(p-w*p),"MarkerImage"in l?b=new l.MarkerImage(v.src,null,null,new l.Point(S*g,w*p),new l.Size(g,p)):"Icon"in l?b=new l.Icon({image:v.src,size:new l.Size(g,p),imageSize:new l.Size(g,p),imageOffset:new l.Pixel(S*g,w*p)}):b={url:v.src,anchor:new l.Point(S,w),size:new l.Size(g,p)},oe()?(i=new l.Marker(new l.Point(_.lng,_.lat)),s.addOverlay(i)):(i.setPosition(_),i.setIcon(b)),"setRotation"in i&&i.setRotation(f.rotate||0);const A=f.label||{};"label"in i&&(i.label.setMap(null),delete i.label);let x;if(A.content){const M={borderColor:A.borderColor,borderWidth:(Number(A.borderWidth)||0)+"px",padding:(Number(A.padding)||0)+"px",borderRadius:(Number(A.borderRadius)||0)+"px",backgroundColor:A.bgColor,color:A.color,fontSize:(A.fontSize||14)+"px",lineHeight:(A.fontSize||14)+"px",marginLeft:(Number(A.anchorX||A.x)||0)+"px",marginTop:(Number(A.anchorY||A.y)||0)+"px"};if("Label"in l)x=new l.Label({position:_,map:s,clickable:!1,content:A.content,style:M}),i.label=x;else if("setLabel"in i)if(ue()){const F=`<div style="
                  margin-left:${M.marginLeft};
                  margin-top:${M.marginTop};
                  padding:${M.padding};
                  background-color:${M.backgroundColor};
                  border-radius:${M.borderRadius};
                  line-height:${M.lineHeight};
                  color:${M.color};
                  font-size:${M.fontSize};

                  ">
                  ${A.content}
                <div>`;i.setLabel({content:F,direction:"bottom-right"})}else{const F=o(M);i.setLabel({text:A.content,color:M.color,fontSize:M.fontSize,className:F})}}const E=f.callout||{};let C=i.callout,I;if(E.content||h){ue()&&E.content&&(E.content=E.content.replaceAll(`
`,"<br/>"));const M="0px 0px 3px 1px rgba(0,0,0,0.5)";let F=-m/2;if((f.width||f.height)&&(F+=14-m/2),I=E.content?{position:_,map:s,top:V,offsetY:F,content:E.content,color:E.color,fontSize:E.fontSize,borderRadius:E.borderRadius,bgColor:E.bgColor,padding:E.padding,boxShadow:E.boxShadow||M,display:E.display}:{position:_,map:s,top:V,offsetY:F,content:h,boxShadow:M},C)C.setOption(I);else if(ue()){const k=()=>{t!==""&&c("callouttap",{},{markerId:Number(t)})};C=i.callout=new l.Callout(I,k)}else C=i.callout=new l.Callout(I),C.div.onclick=function(k){t!==""&&c("callouttap",k,{markerId:Number(t)}),k.stopPropagation(),k.preventDefault()},ot().type===Ne.GOOGLE&&(C.div.ontouchstart=function(k){k.stopPropagation()},C.div.onpointerdown=function(k){k.stopPropagation()})}else C&&(a(C),delete i.callout)},f.iconPath?v.src=Q(f.iconPath):console.error("Marker.iconPath is required.")}function d(f){oe()||(i=new l.Marker({map:s,flat:!0,autoRotation:!1})),u(f);const h=l.event||l.Event;oe()||h.addListener(i,"click",()=>{const _=i.callout;if(_&&!_.alwaysVisible){if(ue())_.visible=!_.visible,_.visible?i.callout.createAMapText():i.callout.removeAMapText();else if(_.set("visible",!_.visible),_.visible){const v=_.div,m=v.parentNode;m.removeChild(v),m.appendChild(v)}}t&&c("markertap",{},{markerId:Number(t),latitude:f.latitude,longitude:f.longitude})})}d(e),Vue.watch(e,u)}),t){const s=Vue.inject("addMapChidlContext"),l=Vue.inject("removeMapChidlContext"),c={id:t,translate(u){n((d,f,h)=>{const _=u.destination,v=u.duration,m=!!u.autoRotate;let T=Number(u.rotate)||0,b=0;"getRotation"in i&&(b=i.getRotation());const g=i.getPosition(),p=new f.LatLng(_.latitude,_.longitude),V=f.geometry.spherical.computeDistanceBetween(g,p)/1e3,S=(typeof v=="number"?v:1e3)/(1e3*60*60),w=V/S,A=f.event||f.Event,x=A.addListener(i,"moving",I=>{const M=I.latLng,F=i.label;F&&F.setPosition(M);const k=i.callout;k&&k.setPosition(M)}),E=A.addListener(i,"moveend",()=>{E.remove(),x.remove(),i.lastPosition=g,i.setPosition(p);const I=i.label;I&&I.setPosition(p);const M=i.callout;M&&M.setPosition(p);const F=u.animationEnd;G(F)&&F()});let C=0;m&&(i.lastPosition&&(C=f.geometry.spherical.computeHeading(i.lastPosition,g)),T=f.geometry.spherical.computeHeading(g,p)-C),"setRotation"in i&&i.setRotation(b+T),"moveTo"in i?i.moveTo(p,w):(i.setPosition(p),A.trigger(i,"moveend",{}))})}};s(c),Vue.onUnmounted(()=>l(c))}return Vue.onUnmounted(r),()=>null}});function it(e){if(!e)return{r:0,g:0,b:0,a:0};let t=e.slice(1);const n=t.length;if(![3,4,6,8].includes(n))return{r:0,g:0,b:0,a:0};(n===3||n===4)&&(t=t.replace(/(\w{1})/g,"$1$1"));let[o,i,r,a]=t.match(/(\w{2})/g);const s=parseInt(o,16),l=parseInt(i,16),c=parseInt(r,16);return a?{r:s,g:l,b:c,a:(`0x100${a}`-65536)/255}:{r:s,g:l,b:c,a:1}}const KS=ge({name:"MapPolyline",props:{points:{type:Array,require:!0},color:{type:String,default:"#000000"},width:{type:[Number,String],default:""},dottedLine:{type:[Boolean,String],default:!1},arrowLine:{type:[Boolean,String],default:!1},arrowIconPath:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderWidth:{type:[Number,String],default:""},colorList:{type:Array,default(){return[]}},level:{type:String,default:""}},setup(e){const t=Vue.inject("onMapReady");let n,o;function i(){n&&n.setMap(null),o&&o.setMap(null)}return t((r,a)=>{function s(c){i(),l(c)}function l(c){const u=[];c.points.forEach(w=>{let A;ue()?A=[w.longitude,w.latitude]:oe()?A=new a.Point(w.longitude,w.latitude):A=new a.LatLng(w.latitude,w.longitude),u.push(A)});const d=Number(c.width)||1,{r:f,g:h,b:_,a:v}=it(c.color),{r:m,g:T,b,a:g}=it(c.borderColor),p={map:r,clickable:!1,path:u,strokeWeight:d,strokeColor:c.color||void 0,strokeDashStyle:c.dottedLine?"dash":"solid"},V=Number(c.borderWidth)||0,S={map:r,clickable:!1,path:u,strokeWeight:d+V*2,strokeColor:c.borderColor||void 0,strokeDashStyle:c.dottedLine?"dash":"solid"};"Color"in a?(p.strokeColor=new a.Color(f,h,_,v),S.strokeColor=new a.Color(m,T,b,g)):(p.strokeColor=`rgb(${f}, ${h}, ${_})`,p.strokeOpacity=v,S.strokeColor=`rgb(${m}, ${T}, ${b})`,S.strokeOpacity=g),V&&(o=new a.Polyline(S)),oe()?(n=new a.Polyline(p.path,p),r.addOverlay(n)):n=new a.Polyline(p)}l(e),Vue.watch(e,s)}),Vue.onUnmounted(i),()=>null}}),QS=ge({name:"MapCircle",props:{latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},color:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},radius:{type:[Number,String],require:!0},strokeWidth:{type:[Number,String],default:""},level:{type:String,default:""}},setup(e){const t=Vue.inject("onMapReady");let n;function o(){n&&n.setMap(null)}return t((i,r)=>{function a(l){o(),s(l)}function s(l){const c=ue()||oe()?[l.longitude,l.latitude]:new r.LatLng(l.latitude,l.longitude),u={map:i,center:c,clickable:!1,radius:l.radius,strokeWeight:Number(l.strokeWidth)||1,strokeDashStyle:"solid"};if(oe())u.strokeColor=l.color,u.fillColor=l.fillColor||"#000",u.fillOpacity=1;else{const{r:d,g:f,b:h,a:_}=it(l.fillColor),{r:v,g:m,b:T,a:b}=it(l.color);"Color"in r?(u.fillColor=new r.Color(d,f,h,_),u.strokeColor=new r.Color(v,m,T,b)):(u.fillColor=`rgb(${d}, ${f}, ${h})`,u.fillOpacity=_,u.strokeColor=`rgb(${v}, ${m}, ${T})`,u.strokeOpacity=b)}if(oe()){let d=new r.Point(u.center[0],u.center[1]);n=new r.Circle(d,u.radius,u),i.addOverlay(n)}else n=new r.Circle(u),ue()&&i.add(n)}s(e),Vue.watch(e,a)}),Vue.onUnmounted(o),()=>null}}),ZS=ge({name:"MapControl",props:{id:{type:[Number,String],default:""},position:{type:Object,required:!0},iconPath:{type:String,required:!0},clickable:{type:[Boolean,String],default:""},trigger:{type:Function,required:!0}},setup(e){const t=Vue.computed(()=>Q(e.iconPath)),n=Vue.computed(()=>{let i=`top:${e.position.top||0}px;left:${e.position.left||0}px;`;return e.position.width&&(i+=`width:${e.position.width}px;`),e.position.height&&(i+=`height:${e.position.height}px;`),i}),o=i=>{e.clickable&&e.trigger("controltap",i,{controlId:e.id})};return()=>Vue.createVNode("div",{class:"uni-map-control"},[Vue.createVNode("img",{src:t.value,style:n.value,class:"uni-map-control-icon",onClick:o},null,12,["src","onClick"])])}}),eT=te(()=>{Cl.forEach(e=>{$r.prototype[e]=function(t){G(t)&&this._events[e].push(t)}}),yv.forEach(e=>{$r.prototype[e]=function(t){var n=this._events[e.replace("off","on")],o=n.indexOf(t);o>=0&&n.splice(o,1)}})});class $r{constructor(){this._src="";var t=this._audio=new Audio;this._stoping=!1,["src","autoplay","loop","duration","currentTime","paused","volume"].forEach(r=>{Object.defineProperty(this,r,{set:r==="src"?a=>(t.src=Q(a),this._src=a,a):a=>(t[r]=a,a),get:r==="src"?()=>this._src:()=>t[r]})}),this.startTime=0,Object.defineProperty(this,"obeyMuteSwitch",{set:()=>!1,get:()=>!1}),Object.defineProperty(this,"buffered",{get(){var r=t.buffered;return r.length?r.end(r.length-1):0}}),this._events={},Cl.forEach(r=>{this._events[r]=[]}),t.addEventListener("loadedmetadata",()=>{var r=Number(this.startTime)||0;r>0&&(t.currentTime=r)});var o=["canplay","pause","seeking","seeked","timeUpdate"],i=o.concat(["play","ended","error","waiting"]);i.forEach(r=>{t.addEventListener(r.toLowerCase(),()=>{if(this._stoping&&o.indexOf(r)>=0)return;const a=`on${r.slice(0,1).toUpperCase()}${r.slice(1)}`;this._events[a].forEach(s=>{s()})},!1)}),eT()}play(){this._stoping=!1,this._audio.play()}pause(){this._audio.pause()}stop(){this._stoping=!0,this._audio.pause(),this._audio.currentTime=0,this._events.onStop.forEach(t=>{t()})}seek(t){this._stoping=!1,t=Number(t),typeof t=="number"&&!isNaN(t)&&(this._audio.currentTime=t)}destroy(){this.stop()}}const td=j(tv,()=>new $r),nd=R(Fv,({phoneNumber:e},{resolve:t})=>(window.location.href=`tel:${e}`,t())),od="__DC_STAT_UUID",id=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let jt;function tT(){if(jt=jt||id[od],!jt){jt=Date.now()+""+Math.floor(Math.random()*1e7);try{id[od]=jt}catch(e){}}return jt}function nT(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const r=parseFloat(RegExp.$1);return r>6?r:6}else return n?-1:o?11:-1}function Mn(){if(__uniConfig.darkmode!==!0)return J(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function oT(){let e,t="0",n="",o="phone";const i=navigator.language;if(Pc){e="iOS";const d=we.match(/OS\s([\w_]+)\slike/);d&&(t=d[1].replace(/_/g,"."));const f=we.match(/\(([a-zA-Z]+);/);f&&(n=f[1])}else if(Vb){e="Android";const d=we.match(/Android[\s/]([\w\.]+)[;\s]/);d&&(t=d[1]);const f=we.match(/\((.+?)\)/),h=f?f[1].split(";"):we.split(" "),_=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let v=0;v<h.length;v++){const m=h[v];if(m.indexOf("Build")>0){n=m.split("Build")[0].trim();break}let T;for(let b=0;b<_.length;b++)if(_[b].test(m)){T=!0;break}if(!T){n=m.trim();break}}}else if(Cb){if(n="iPad",e="iOS",o="pad",t=G(window.BigInt)?"14.0":"13.0",parseInt(t)===14){const d=we.match(/Version\/(\S*)\b/);d&&(t=d[1])}}else if(Sr||Tr||Oc){n="PC",e="PC",o="pc",t="0";let d=we.match(/\((.+?)\)/)[1];if(Sr){switch(e="Windows",Sr[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10";break}const f=d&&d.match(/[Win|WOW]([\d]+)/);f&&(t+=` x${f[1]}`)}else if(Tr){e="macOS";const f=d&&d.match(/Mac OS X (.+)/)||"";t&&(t=f[1].replace(/_/g,"."),t.indexOf(";")!==-1&&(t=t.split(";")[0]))}else if(Oc){e="Linux";const f=d&&d.match(/Linux (.*)/)||"";f&&(t=f[1],t.indexOf(";")!==-1&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const r=`${e} ${t}`,a=e.toLocaleLowerCase();let s="",l=String(nT());if(l!=="-1")s="IE";else{const d=["Version","Firefox","Chrome","Edge{0,1}"],f=["Safari","Firefox","Chrome","Edge"];for(let h=0;h<d.length;h++){const _=d[h],v=new RegExp(`(${_})/(\\S*)\\b`);v.test(we)&&(s=f[h],l=we.match(v)[2])}}let c="portrait";const u=typeof window.screen.orientation=="undefined"?window.orientation:window.screen.orientation.angle;return c=Math.abs(u)===90?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:r,platform:a,browserName:s.toLocaleLowerCase(),browserVersion:l,language:i,deviceType:o,ua:we,osname:e,osversion:t,theme:Mn()}}const zr=j("getWindowInfo",()=>{const e=window.devicePixelRatio,t=Rc(),n=Lc(t),o=Bc(t,n),i=Ab(t,n),r=Dc(o);let a=window.innerHeight;const s=he.top,l={left:he.left,right:r-he.right,top:he.top,bottom:a-he.bottom,width:r-he.left-he.right,height:a-he.top-he.bottom},{top:c,bottom:u}=us();return a-=c,a-=u,{windowTop:c,windowBottom:u,windowWidth:r,windowHeight:a,pixelRatio:e,screenWidth:o,screenHeight:i,statusBarHeight:s,safeArea:l,safeAreaInsets:{top:he.top,right:he.right,bottom:he.bottom,left:he.left},screenTop:i-a}});let Zo,ei=!0;function Wr(){ei&&(Zo=oT())}const Hr=j("getDeviceInfo",()=>{Wr();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:i,system:r,deviceOrientation:a,deviceType:s,osname:l,osversion:c}=Zo;return B({brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:tT(),deviceOrientation:a,deviceType:s,model:o,platform:i,system:r,osName:l?l.toLocaleLowerCase():void 0,osVersion:c})}),qr=j("getAppBaseInfo",()=>{Wr();const{theme:e,language:t,browserName:n,browserVersion:o}=Zo;return B({appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:bo?bo():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:"",uniPlatform:"web",isUniAppX:!1,uniCompileVersion:__uniConfig.compilerVersion,uniCompilerVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion},{})}),Gr=j("getSystemInfoSync",()=>{ei=!0,Wr(),ei=!1;const e=zr(),t=Hr(),n=qr();ei=!0;const{ua:o,browserName:i,browserVersion:r,osname:a,osversion:s}=Zo,l=B(e,t,n,{ua:o,browserName:i,browserVersion:r,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:a.toLocaleLowerCase(),osVersion:s,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,Fg(l)}),rd=R("getSystemInfo",(e,{resolve:t})=>t(Gr())),ad="onNetworkStatusChange";function Yt(){Yr().then(({networkType:e})=>{UniServiceJSBridge.invokeOnCallback(ad,{isConnected:e!=="none",networkType:e})})}function jr(){return navigator.connection||navigator.webkitConnection||navigator.mozConnection}const sd=ke(ad,()=>{const e=jr();e?e.addEventListener("change",Yt):(window.addEventListener("offline",Yt),window.addEventListener("online",Yt))}),ld=gt("offNetworkStatusChange",()=>{const e=jr();e?e.removeEventListener("change",Yt):(window.removeEventListener("offline",Yt),window.removeEventListener("online",Yt))}),Yr=R("getNetworkType",(e,{resolve:t})=>{const n=jr();let o="unknown";return n?(o=n.type,o==="cellular"&&n.effectiveType?o=n.effectiveType.replace("slow-",""):!o&&n.effectiveType?o=n.effectiveType:["none","wifi"].includes(o)||(o="unknown")):navigator.onLine===!1&&(o="none"),t({networkType:o})});let Xt=null;const cd=ke(cc,()=>{Xr()}),ud=gt(qv,()=>{Jr()}),Xr=R(Gv,(e,{resolve:t,reject:n})=>{if(!window.DeviceMotionEvent){n();return}function o(){Xt=function(i){const r=i.acceleration||i.accelerationIncludingGravity;UniServiceJSBridge.invokeOnCallback(cc,{x:r&&r.x||0,y:r&&r.y||0,z:r&&r.z||0})},window.addEventListener("devicemotion",Xt,!1)}if(!Xt){if(DeviceMotionEvent.requestPermission){DeviceMotionEvent.requestPermission().then(i=>{i==="granted"?(o(),t()):n(`${i}`)}).catch(i=>{n(`${i}`)});return}o()}t()}),Jr=R(jv,(e,{resolve:t})=>{Xt&&(window.removeEventListener("devicemotion",Xt,!1),Xt=null),t()});let Jt=null;const Kr=ke(uc,()=>{Zr()}),Qr=gt(Yv,()=>{ea()}),Zr=R(Xv,(e,{resolve:t,reject:n})=>{if(!window.DeviceOrientationEvent){n();return}function o(){Jt=function(i){const r=360-(i.alpha!==null?i.alpha:360);UniServiceJSBridge.invokeOnCallback(uc,{direction:r})},window.addEventListener("deviceorientation",Jt,!1)}if(!Jt){if(DeviceOrientationEvent.requestPermission){DeviceOrientationEvent.requestPermission().then(i=>{i==="granted"?(o(),t()):n(`${i}`)}).catch(i=>{n(`${i}`)});return}o()}t()}),ea=R(Jv,(e,{resolve:t})=>{Jt&&(window.removeEventListener("deviceorientation",Jt,!1),Jt=null),t()}),dd=!!window.navigator.vibrate,fd=R(Kv,(e,{resolve:t,reject:n})=>{dd&&window.navigator.vibrate(15)?t():n("vibrateLong:fail")}),hd=R(Qv,(e,{resolve:t,reject:n})=>{dd&&window.navigator.vibrate(400)?t():n("vibrateLong:fail")});var gd=(e,t,n)=>new Promise((o,i)=>{var r=l=>{try{s(n.next(l))}catch(c){i(c)}},a=l=>{try{s(n.throw(l))}catch(c){i(c)}},s=l=>l.done?o(l.value):Promise.resolve(l.value).then(r,a);s((n=n.apply(e,t)).next())});const pd=R($v,(e,t)=>gd(void 0,[e,t],function*(n,{resolve:o,reject:i}){Sp();const{t:r}=D();try{const a=yield navigator.clipboard.readText();o({data:a})}catch(a){iT(o,()=>{i(`${a} ${r("uni.getClipboardData.fail")}`)})}})),md=R(zv,(e,t)=>gd(void 0,[e,t],function*({data:n},{resolve:o,reject:i}){try{yield navigator.clipboard.writeText(n),o()}catch(r){rT(n,o,i)}}),Hv,Wv);function iT(e,t){const n=document.getElementById("#clipboard"),o=n?n.value:void 0;o?e({data:o}):t()}function rT(e,t,n){const o=document.getElementById("#clipboard");o&&o.remove();const i=document.createElement("textarea");i.setAttribute("inputmode","none"),i.id="#clipboard",i.style.position="fixed",i.style.top="-9999px",i.style.zIndex="-9999",document.body.appendChild(i),i.value=e,i.select(),i.setSelectionRange(0,i.value.length);const r=document.execCommand("Copy",!1);i.blur(),r?t():n()}const vd=e=>{UniServiceJSBridge.invokeOnCallback(Ye,e)},_d=ke(Ye,()=>{UniServiceJSBridge.on(Ye,vd)}),yd=gt(Eg,()=>{UniServiceJSBridge.off(Ye,vd)}),aT="uni-storage-keys";function sT(e){const t=["object","string","number","boolean","undefined"];try{const n=J(e)?JSON.parse(e):e,o=n.type;if(t.indexOf(o)>=0){const i=Object.keys(n);if(i.length===2&&"data"in n){if(typeof n.data===o)return n.data;if(o==="object"&&/^\d{4}-\d{2}-\d{2}T\d{2}\:\d{2}\:\d{2}\.\d{3}Z$/.test(n.data))return new Date(n.data)}else if(i.length===1)return""}}catch(n){}}const ta=j(n_,(e,t)=>{const n=typeof t,o=n==="string"?t:JSON.stringify({type:n,data:t});localStorage.setItem(e,o)}),bd=R(t_,({key:e,data:t},{resolve:n,reject:o})=>{try{ta(e,t),n()}catch(i){o(i.message)}});function wd(e){const t=localStorage&&localStorage.getItem(e);if(!J(t))throw new Error("data not found");let n=t;try{const o=JSON.parse(t),i=sT(o);i!==void 0&&(n=i)}catch(o){}return n}const Sd=j(e_,e=>{try{return wd(e)}catch(t){return""}}),Td=R(Zv,({key:e},{resolve:t,reject:n})=>{try{const o=wd(e);t({data:o})}catch(o){n(o.message)}}),na=j(dc,e=>{localStorage&&localStorage.removeItem(e)}),Vd=R(dc,({key:e},{resolve:t})=>{na(e),t()}),oa=j("clearStorageSync",()=>{localStorage&&localStorage.clear()}),Cd=R("clearStorage",(e,{resolve:t})=>{oa(),t()}),ia=j("getStorageInfoSync",()=>{const e=localStorage&&localStorage.length||0,t=[];let n=0;for(let o=0;o<e;o++){const i=localStorage.key(o),r=localStorage.getItem(i)||"";n+=i.length+r.length,i!==aT&&t.push(i)}return{keys:t,currentSize:Math.ceil(n*2/1024),limitSize:Number.MAX_VALUE}}),Ad=R("getStorageInfo",(e,{resolve:t})=>{t(ia())}),Ed=R(o_,({filePath:e},{resolve:t,reject:n})=>{Do(e).then(o=>{t({size:o.size})}).catch(o=>{n(String(o))})},r_,i_),kd=R(a_,({filePath:e},{resolve:t})=>(window.open(e),t()),l_,s_),Id=R(c_,(e,{resolve:t,reject:n})=>{const o=document.activeElement;o&&(o.tagName==="TEXTAREA"||o.tagName==="INPUT")&&(o.blur(),t())});function lT(){return window.location.protocol+"//"+window.location.host}const Nd=R(E_,({src:e},{resolve:t,reject:n})=>{const o=new Image;o.onload=function(){t({width:o.naturalWidth,height:o.naturalHeight,path:e.indexOf("/")===0?lT()+e:e})},o.onerror=function(){n()},o.src=e},I_,k_),xd=R(O_,({src:e},{resolve:t,reject:n})=>{Do(e,!0).then(o=>o).catch(()=>null).then(o=>{const i=document.createElement("video");if(i.onloadedmetadata!==void 0){const r=setTimeout(()=>{i.onloadedmetadata=null,i.onerror=null,n()},e.startsWith("data:")||e.startsWith("blob:")?300:3e3);i.onloadedmetadata=function(){clearTimeout(r),i.onerror=null,t({size:Math.ceil((o?o.size:0)/1024),duration:i.duration||0,width:i.videoWidth||0,height:i.videoHeight||0})},i.onerror=function(){clearTimeout(r),i.onloadedmetadata=null,n()},i.src=e}else n()})},L_,R_),cT={image:{jpg:"jpeg",jpe:"jpeg",pbm:"x-portable-bitmap",pgm:"x-portable-graymap",pnm:"x-portable-anymap",ppm:"x-portable-pixmap",psd:"vnd.adobe.photoshop",pic:"x-pict",rgb:"x-rgb",svg:"svg+xml",svgz:"svg+xml",tif:"tiff",xif:"vnd.xiff",wbmp:"vnd.wap.wbmp",wdp:"vnd.ms-photo",xbm:"x-xbitmap",ico:"x-icon"},video:{"3g2":"3gpp2","3gp":"3gpp",avi:"x-msvideo",f4v:"x-f4v",flv:"x-flv",jpgm:"jpm",jpgv:"jpeg",m1v:"mpeg",m2v:"mpeg",mpe:"mpeg",mpg:"mpeg",mpg4:"mpeg",m4v:"x-m4v",mkv:"x-matroska",mov:"quicktime",qt:"quicktime",movie:"x-sgi-movie",mp4v:"mp4",ogv:"ogg",smv:"x-smv",wm:"x-ms-wm",wmv:"x-ms-wmv",wmx:"x-ms-wmx",wvx:"x-ms-wvx"}},Md="all";function uT(){const t=window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i);return!!(t&&t[0]==="micromessenger")}function ra({count:e,sourceType:t,type:n,extension:o}){uu();const i=document.createElement("input");return i.type="file",yi(i,{position:"absolute",visibility:"hidden",zIndex:"-999",width:"0",height:"0",top:"0",left:"0"}),i.accept=o.map(r=>{if(n!==Md){const a=r.replace(".","");return`${n}/${cT[n][a]||a}`}else return uT()?".":r.indexOf(".")===0?r:`.${r}`}).join(","),e&&e>1&&(i.multiple=!0),n!==Md&&t instanceof Array&&t.length===1&&t[0]==="camera"&&i.setAttribute("capture","camera"),i}let yt=null;const Pd=R(T_,({count:e,sourceType:t,type:n,extension:o},{resolve:i,reject:r})=>{Ei();const{t:a}=D();yt&&(document.body.removeChild(yt),yt=null),yt=ra({count:e,sourceType:t,type:n,extension:o}),document.body.appendChild(yt),yt.addEventListener("change",function(s){const l=s.target,c=[];if(l&&l.files){const d=l.files.length;for(let f=0;f<d;f++){const h=l.files[f];let _;Object.defineProperty(h,"path",{get(){return _=_||zt(h),_}}),f<e&&c.push(h)}}i({get tempFilePaths(){return c.map(({path:d})=>d)},tempFiles:c})}),yt.click(),kr()||console.warn(a("uni.chooseFile.notUserActivation"))},A_,C_);let bt=null;const Od=R(v_,({count:e,sourceType:t,extension:n},{resolve:o,reject:i})=>{Ei();const{t:r}=D();bt&&(document.body.removeChild(bt),bt=null),bt=ra({count:e,sourceType:t,extension:n,type:"image"}),document.body.appendChild(bt),bt.addEventListener("change",function(a){const s=a.target,l=[];if(s&&s.files){const u=s.files.length;for(let d=0;d<u;d++){const f=s.files[d];let h;Object.defineProperty(f,"path",{get(){return h=h||zt(f),h}}),d<e&&l.push(f)}}o({get tempFilePaths(){return l.map(({path:u})=>u)},tempFiles:l})}),bt.click(),kr()||console.warn(r("uni.chooseFile.notUserActivation"))},y_,__),Rd={esc:["Esc","Escape"],enter:["Enter"]},dT=Object.keys(Rd);function aa(){const e=Vue.ref(""),t=Vue.ref(!1),n=o=>{if(t.value)return;const i=dT.find(r=>Rd[r].indexOf(o.key)!==-1);i&&(e.value=i),Vue.nextTick(()=>e.value="")};return Vue.onMounted(()=>{document.addEventListener("keyup",n)}),Vue.onBeforeUnmount(()=>{document.removeEventListener("keyup",n)}),{key:e,disable:t}}const fT=Vue.createVNode("div",{class:"uni-mask"},null,-1);function Kt(e,t,n){return t.onClose=(...o)=>(t.visible=!1,n.apply(null,o)),Vue.createApp(Vue.defineComponent({setup(){return()=>(Vue.openBlock(),Vue.createBlock(e,t,null,16))}}))}function Qt(e){let t=document.getElementById(e);return t||(t=document.createElement("div"),t.id=e,document.body.append(t)),t}function Ld(e,{onEsc:t,onEnter:n}){const o=Vue.ref(e.visible),{key:i,disable:r}=aa();return Vue.watch(()=>e.visible,a=>o.value=a),Vue.watch(()=>o.value,a=>r.value=!a),Vue.watchEffect(()=>{const{value:a}=i;a==="esc"?t&&t():a==="enter"&&n&&n()}),o}let Pn=0,sa="";function Bd(e){let t=Pn;Pn+=e?1:-1,Pn=Math.max(0,Pn),Pn>0?t===0&&(sa=document.body.style.overflow,document.body.style.overflow="hidden"):(document.body.style.overflow=sa,sa="")}function la(){Vue.onMounted(()=>Bd(!0)),Vue.onUnmounted(()=>Bd(!1))}const hT=ge({name:"ImageView",props:{src:{type:String,default:""}},setup(e){const t=Vue.reactive({direction:"none"});let n=1,o=0,i=0,r=0,a=0;function s({detail:f}){n=f.scale}function l(f){const _=f.target.getBoundingClientRect();o=_.width,i=_.height}function c(f){const _=f.target.getBoundingClientRect();r=_.width,a=_.height,d(f)}function u(f){const h=n*o>r,_=n*i>a;h&&_?t.direction="all":h?t.direction="horizontal":_?t.direction="vertical":t.direction="none",d(f)}function d(f){(t.direction==="all"||t.direction==="horizontal")&&f.stopPropagation()}return()=>{const f={position:"absolute",left:"0",top:"0",width:"100%",height:"100%"};return Vue.createVNode(vu,{style:f,onTouchstart:ne(c),onTouchmove:ne(d),onTouchend:ne(u)},{default:()=>[Vue.createVNode(Su,{style:f,direction:t.direction,inertia:!0,scale:!0,"scale-min":"1","scale-max":"4",onScale:s},{default:()=>[Vue.createVNode("img",{src:e.src,style:{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",maxHeight:"100%",maxWidth:"100%"},onLoad:l},null,40,["src","onLoad"])]},8,["style","direction","inertia","scale","onScale"])]},8,["style","onTouchstart","onTouchmove","onTouchend"])}}});function gT(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Vue.isVNode(e)}const pT={urls:{type:Array,default(){return[]}},current:{type:[Number,String],default:0}};function Dd(e){let t=typeof e.current=="number"?e.current:e.urls.indexOf(e.current);return t=t<0?0:t,t}const mT=ge({name:"ImagePreview",props:pT,emits:["close"],setup(e,{emit:t}){la();const n=Vue.ref(null),o=Vue.ref(Dd(e));Vue.watch(()=>e.current,()=>o.value=Dd(e));let i;Vue.onMounted(()=>{const l=n.value,c=20;let u=0,d=0;l.addEventListener("mousedown",f=>{i=!1,u=f.clientX,d=f.clientY}),l.addEventListener("mouseup",f=>{(Math.abs(f.clientX-u)>c||Math.abs(f.clientY-d)>c)&&(i=!0)})});function r(){i||Vue.nextTick(()=>{t("close")})}function a(l){o.value=l.detail.current}const s={position:"absolute","box-sizing":"border-box",top:"0",right:"0",width:"60px",height:"44px",padding:"6px","line-height":"32px","font-size":"26px",color:"white","text-align":"center",cursor:"pointer"};return()=>{let l;return Vue.createVNode("div",{ref:n,style:{display:"block",position:"fixed",left:"0",top:"0",width:"100%",height:"100%",zIndex:999,background:"rgba(0,0,0,0.8)"},onClick:r},[Vue.createVNode(Lu,{navigation:"auto",current:o.value,onChange:a,"indicator-dots":!1,autoplay:!1,style:{position:"absolute",left:"0",top:"0",width:"100%",height:"100%"}},gT(l=e.urls.map(c=>Vue.createVNode(Bu,null,{default:()=>[Vue.createVNode(hT,{src:c},null,8,["src"])]})))?l:{default:()=>[l],_:1},8,["current","onChange"]),Vue.createVNode("div",{style:s},[ce(xi,"#ffffff",26)],4)],8,["onClick"])}}});let On=null,Zt;const Ud=()=>{On=null,Vue.nextTick(()=>{Zt==null||Zt.unmount(),Zt=null})},Fd=R(N_,(e,{resolve:t})=>{On?B(On,e):(On=Vue.reactive(e),Vue.nextTick(()=>{Zt=Kt(mT,On,Ud),Zt.mount(Qt("u-a-p"))})),t()},M_,x_),$d=R(P_,(e,{resolve:t,reject:n})=>{Zt?(Ud(),t()):n()});let wt=null;const zd=R(b_,({sourceType:e,extension:t},{resolve:n,reject:o})=>{Ei();const{t:i}=D();wt&&(document.body.removeChild(wt),wt=null),wt=ra({sourceType:e,extension:t,type:"video"}),document.body.appendChild(wt),wt.addEventListener("change",function(r){const s=r.target.files[0];let l="";const c={tempFilePath:l,tempFile:s,size:s.size,duration:0,width:0,height:0,name:s.name};Object.defineProperty(c,"tempFilePath",{get(){return l=l||zt(this.tempFile),l}});const u=document.createElement("video");if(u.onloadedmetadata!==void 0){const d=zt(s);u.onloadedmetadata=function(){Wc(d),n(B(c,{duration:u.duration||0,width:u.videoWidth||0,height:u.videoHeight||0}))},setTimeout(()=>{u.onloadedmetadata=null,Wc(d),n(c)},300),u.src=d}else n(c)}),wt.click(),kr()||console.warn(i("uni.chooseFile.notUserActivation"))},S_,w_),ca=mo(B_,({url:e,data:t,header:n={},method:o,dataType:i,responseType:r,withCredentials:a,timeout:s=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=vT(n);if(o!=="GET")if(J(t)||t instanceof ArrayBuffer)u=t;else if(d==="json")try{u=JSON.stringify(t)}catch(v){u=t.toString()}else if(d==="urlencoded"){const v=[];for(const m in t)le(t,m)&&v.push(encodeURIComponent(m)+"="+encodeURIComponent(t[m]));u=v.join("&")}else u=t.toString();const f=new XMLHttpRequest,h=new _T(f);f.open(o,e);for(const v in n)le(n,v)&&f.setRequestHeader(v,n[v]);const _=setTimeout(function(){f.onload=f.onabort=f.onerror=null,h.abort(),c("timeout",{errCode:5})},s);return f.responseType=r,f.onload=function(){clearTimeout(_);const v=f.status;let m=r==="text"?f.responseText:f.response;if(r==="text"&&i==="json")try{m=JSON.parse(m)}catch(T){}l({data:m,statusCode:v,header:yT(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(_),c("abort",{errCode:600003})},f.onerror=function(){clearTimeout(_),c(void 0,{errCode:5})},f.withCredentials=a,f.send(u),h},z_,W_);function vT(e){const t=Object.keys(e).find(o=>o.toLowerCase()==="content-type");if(!t)return;const n=e[t];return n.indexOf("application/json")===0?"json":n.indexOf("application/x-www-form-urlencoded")===0?"urlencoded":"string"}class _T{constructor(t){this._xhr=t}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(t){throw new Error("Method not implemented.")}offHeadersReceived(t){throw new Error("Method not implemented.")}}function yT(e){const t={};return e.split(Et).forEach(n=>{const o=n.match(/(\S+\s*):\s*(.*)/);!o||o.length!==3||(t[o[1]]=o[2])}),t}class bT{constructor(t){this._callbacks=[],this._xhr=t}onProgressUpdate(t){G(t)&&this._callbacks.push(t)}offProgressUpdate(t){const n=this._callbacks.indexOf(t);n>=0&&this._callbacks.splice(n,1)}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(t){throw new Error("Method not implemented.")}offHeadersReceived(t){throw new Error("Method not implemented.")}}const Wd=mo(H_,({url:e,header:t={},timeout:n=__uniConfig.networkTimeout.downloadFile},{resolve:o,reject:i})=>{var r,a=new XMLHttpRequest,s=new bT(a);return a.open("GET",e,!0),Object.keys(t).forEach(l=>{a.setRequestHeader(l,t[l])}),a.responseType="blob",a.onload=function(){clearTimeout(r);const l=a.status,c=this.response;let u;const d=a.getResponseHeader("content-disposition");if(d){const f=d.match(/filename="?(\S+)"?\b/);f&&(u=f[1])}c.name=u||$b(e),o({statusCode:l,tempFilePath:zt(c)})},a.onabort=function(){clearTimeout(r),i("abort",{errCode:600003})},a.onerror=function(){clearTimeout(r),i("",{errCode:602001})},a.onprogress=function(l){s._callbacks.forEach(c=>{var u=l.loaded,d=l.total,f=Math.round(u/d*100);c({progress:f,totalBytesWritten:u,totalBytesExpectedToWrite:d})})},a.send(),r=setTimeout(function(){a.onprogress=a.onload=a.onabort=a.onerror=null,s.abort(),i("timeout",{errCode:5})},n),s},G_,q_);class wT{constructor(t){this._callbacks=[],this._xhr=t}onProgressUpdate(t){G(t)&&this._callbacks.push(t)}offProgressUpdate(t){const n=this._callbacks.indexOf(t);n>=0&&this._callbacks.splice(n,1)}abort(){this._isAbort=!0,this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(t){throw new Error("Method not implemented.")}offHeadersReceived(t){throw new Error("Method not implemented.")}}const Hd=mo(j_,({url:e,file:t,filePath:n,name:o,files:i,header:r={},formData:a={},timeout:s=__uniConfig.networkTimeout.uploadFile},{resolve:l,reject:c})=>{var u=new wT;(!K(i)||!i.length)&&(i=[{name:o,file:t,uri:n}]);function d(f){var h=new XMLHttpRequest,_=new FormData,v;Object.keys(a).forEach(m=>{_.append(m,a[m])}),Object.values(i).forEach(({name:m},T)=>{const b=f[T];_.append(m||"file",b,b.name||`file-${Date.now()}`)}),h.open("POST",e),Object.keys(r).forEach(m=>{h.setRequestHeader(m,r[m])}),h.upload.onprogress=function(m){u._callbacks.forEach(T=>{var b=m.loaded,g=m.total,p=Math.round(b/g*100);T({progress:p,totalBytesSent:b,totalBytesExpectedToSend:g})})},h.onerror=function(){clearTimeout(v),c("",{errCode:602001})},h.onabort=function(){clearTimeout(v),c("abort",{errCode:600003})},h.onload=function(){clearTimeout(v);const m=h.status;l({statusCode:m,data:h.responseText||h.response})},u._isAbort?c("abort",{errCode:600003}):(v=setTimeout(function(){h.upload.onprogress=h.onload=h.onabort=h.onerror=null,u.abort(),c("timeout",{errCode:5})},s),h.send(_),u._xhr=h)}return Promise.all(i.map(({file:f,uri:h})=>f instanceof Blob?Promise.resolve(zc(f)):Do(h))).then(d).catch(()=>{setTimeout(()=>{c("file error")},0)}),u},X_,Y_),en=[],ua={open:"",close:"",error:"",message:""};class ST{constructor(t,n,o){this._callbacks={open:[],close:[],error:[],message:[]};let i;try{const r=this._webSocket=new WebSocket(t,n);r.binaryType="arraybuffer",["open","close","error","message"].forEach(l=>{this._callbacks[l]=[],r.addEventListener(l,c=>{const{data:u,code:d,reason:f}=c,h=l==="message"?{data:u}:l==="close"?{code:d,reason:f}:{};if(this._callbacks[l].forEach(_=>{try{_(h)}catch(v){console.error(`thirdScriptError
${v};at socketTask.on${gi(l)} callback function
`,v)}}),this===en[0]&&ua[l]&&UniServiceJSBridge.invokeOnCallback(ua[l],h),l==="error"||l==="close"){const _=en.indexOf(this);_>=0&&en.splice(_,1)}})}),["CLOSED","CLOSING","CONNECTING","OPEN","readyState"].forEach(l=>{Object.defineProperty(this,l,{get(){return r[l]}})})}catch(r){i=r}o&&o(i,this)}send(t){const n=(t||{}).data,o=this._webSocket;try{if(o.readyState!==o.OPEN)throw ye(t,{errMsg:"sendSocketMessage:fail SocketTask.readyState is not OPEN",errCode:10002}),new Error("SocketTask.readyState is not OPEN");o.send(n),ye(t,"sendSocketMessage:ok")}catch(i){ye(t,{errMsg:`sendSocketMessage:fail ${i}`,errCode:602001})}}close(t={}){const n=this._webSocket;try{const o=t.code||1e3,i=t.reason;J(i)?n.close(o,i):n.close(o),ye(t,"closeSocket:ok")}catch(o){ye(t,`closeSocket:fail ${o}`)}}onOpen(t){this._callbacks.open.push(t)}onMessage(t){this._callbacks.message.push(t)}onError(t){this._callbacks.error.push(t)}onClose(t){this._callbacks.close.push(t)}}const qd=mo(J_,({url:e,protocols:t},{resolve:n,reject:o})=>new ST(e,t,(i,r)=>{if(i){o(i.toString(),{errCode:600009});return}en.push(r),n()}),Q_,K_);function Gd(e,t,n,o,i){const r=e[t];G(r)&&r.call(e,B({},n,{success(){o()},fail({errMsg:a}){i(a.replace("sendSocketMessage:fail ",""))},complete:void 0}))}const jd=R(Z_,(e,{resolve:t,reject:n})=>{const o=en[0];o&&o.readyState===o.OPEN?Gd(o,"send",e,t,n):n("WebSocket is not connected")}),Yd=R(ey,(e,{resolve:t,reject:n})=>{const o=en[0];o?Gd(o,"close",e,t,n):n("WebSocket is not connected")});function ti(e){const t=`onSocket${gi(e)}`;return ke(t,()=>{ua[e]=t})}const Xd=ti("open"),Jd=ti("error"),Kd=ti("message"),Qd=ti("close"),Rn=R(d_,({type:e,altitude:t,highAccuracyExpireTime:n,isHighAccuracy:o},{resolve:i,reject:r})=>{const a=ot();new Promise((s,l)=>{navigator.geolocation?navigator.geolocation.getCurrentPosition(c=>s({coords:c.coords}),l,{enableHighAccuracy:o||t,timeout:n||1e3*100}):l(new Error("device nonsupport geolocation"))}).catch(s=>new Promise((l,c)=>{a.type===Ne.QQ?Ur(`https://apis.map.qq.com/ws/location/v1/ip?output=jsonp&key=${a.key}`,{callback:"callback"},u=>{if("result"in u&&u.result.location){const d=u.result.location;l({coords:{latitude:d.lat,longitude:d.lng},skip:!0})}else c(new Error(u.message||JSON.stringify(u)))},()=>c(new Error("network error"))):a.type===Ne.GOOGLE?ca({method:"POST",url:`https://www.googleapis.com/geolocation/v1/geolocate?key=${a.key}`,success(u){const d=u.data;"location"in d?l({coords:{latitude:d.location.lat,longitude:d.location.lng,accuracy:d.accuracy},skip:!0}):c(new Error(d.error&&d.error.message||JSON.stringify(u)))},fail(){c(new Error("network error"))}}):a.type===Ne.AMAP?Fr([],()=>{window.AMap.plugin("AMap.Geolocation",()=>{new window.AMap.Geolocation({enableHighAccuracy:!0,timeout:1e4}).getCurrentPosition((d,f)=>{d==="complete"?l({coords:{latitude:f.position.lat,longitude:f.position.lng,accuracy:f.accuracy},skip:!0}):c(new Error(f.message))})})}):c(s)})).then(({coords:s,skip:l})=>{Zu(e,s,l).then(c=>{i({latitude:c.latitude,longitude:c.longitude,accuracy:c.accuracy,speed:c.altitude||0,altitude:c.altitude||0,verticalAccuracy:c.altitudeAccuracy||0,horizontalAccuracy:c.accuracy||0})}).catch(c=>{r(c.message)})}).catch(s=>{r(s.message||JSON.stringify(s))})},h_,f_),TT="M28 17c-6.49396875 0-12.13721875 2.57040625-15 6.34840625V5.4105l6.29859375 6.29859375c0.387875 0.387875 1.02259375 0.387875 1.4105 0 0.387875-0.387875 0.387875-1.02259375 0-1.4105L12.77853125 2.36803125a0.9978125 0.9978125 0 0 0-0.0694375-0.077125c-0.1944375-0.1944375-0.45090625-0.291375-0.70721875-0.290875l-0.00184375-0.0000625-0.00184375 0.0000625c-0.2563125-0.0005-0.51278125 0.09640625-0.70721875 0.290875a0.9978125 0.9978125 0 0 0-0.0694375 0.077125l-7.930625 7.9305625c-0.387875 0.387875-0.387875 1.02259375 0 1.4105 0.387875 0.387875 1.02259375 0.387875 1.4105 0L11 5.4105V29c0 0.55 0.45 1 1 1s1-0.45 1-1c0-5.52284375 6.71571875-10 15-10 0.55228125 0 1-0.44771875 1-1 0-0.55228125-0.44771875-1-1-1z",VT={latitude:{type:Number},longitude:{type:Number},scale:{type:Number,default:18},name:{type:String,default:""},address:{type:String,default:""}};function CT(e){const t=Vue.reactive({center:{latitude:0,longitude:0},marker:{id:1,latitude:0,longitude:0,iconPath:Ju,width:32,height:52},location:{id:2,latitude:0,longitude:0,iconPath:Xu,width:44,height:44}});function n(){e.latitude&&e.longitude&&(t.center.latitude=e.latitude,t.center.longitude=e.longitude,t.marker.latitude=e.latitude,t.marker.longitude=e.longitude)}return Vue.watch([()=>e.latitude,()=>e.longitude],n),n(),t}const AT=ge({name:"LocationView",props:VT,emits:["close"],setup(e,{emit:t}){const n=CT(e);la(),Rn({type:"gcj02",success:({latitude:s,longitude:l})=>{n.location.latitude=s,n.location.longitude=l}});function o(s){const l=s.detail.centerLocation;l&&(n.center.latitude=l.latitude,n.center.longitude=l.longitude)}function i(){const s=ot();let l="";s.type===Ne.GOOGLE?l=`https://www.google.com/maps/dir/?api=1${n.location.latitude?`&origin=${n.location.latitude}%2C${n.location.longitude}`:""}&destination=${e.latitude}%2C${e.longitude}`:s.type===Ne.QQ?l=`https://apis.map.qq.com/uri/v1/routeplan?type=drive${n.location.latitude?`&fromcoord=${n.location.latitude}%2C${n.location.longitude}&from=${encodeURIComponent("我的位置")}`:""}&tocoord=${e.latitude}%2C${e.longitude}&to=${encodeURIComponent(e.name||"目的地")}&ref=${s.key}`:s.type===Ne.AMAP&&(l=`https://uri.amap.com/navigation?${n.location.latitude?`from=${n.location.longitude},${n.location.latitude},${encodeURIComponent("我的位置")}&`:""}to=${e.longitude},${e.latitude},${encodeURIComponent(e.name||"目的地")}`),window.open(l)}function r(){t("close")}function a({latitude:s,longitude:l}){n.center.latitude=s,n.center.longitude=l}return()=>Vue.createVNode("div",{class:"uni-system-open-location"},[Vue.createVNode(_a,{latitude:n.center.latitude,longitude:n.center.longitude,class:"map",markers:[n.marker,n.location],onRegionchange:o},{default:()=>[Vue.createVNode("div",{class:"map-move",onClick:()=>a(n.location)},[ce(Yu,"#000000",24)],8,["onClick"])]},8,["latitude","longitude","markers","onRegionchange"]),Vue.createVNode("div",{class:"info"},[Vue.createVNode("div",{class:"name",onClick:()=>a(n.marker)},[e.name],8,["onClick"]),Vue.createVNode("div",{class:"address",onClick:()=>a(n.marker)},[e.address],8,["onClick"]),Vue.createVNode("div",{class:"nav",onClick:i},[ce(TT,"#ffffff",26)],8,["onClick"])]),Vue.createVNode("div",{class:"nav-btn-back",onClick:r},[ce(hn,"#ffffff",26)],8,["onClick"])])}});let Ln=null;const Zd=R(g_,(e,{resolve:t})=>{Ln?B(Ln,e):(Ln=Vue.reactive(e),Vue.nextTick(()=>{const n=Kt(AT,Ln,()=>{Ln=null,Vue.nextTick(()=>{n.unmount()})});n.mount(Qt("u-a-o"))})),t()},m_,p_);function ET(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Vue.isVNode(e)}const kT={latitude:{type:Number},longitude:{type:Number}};function IT(e){return e>100?`${e>1e3?(e/1e3).toFixed(1)+"k":e.toFixed(0)}m | `:e>0?"<100m | ":""}function NT(e){const t=Vue.reactive({latitude:0,longitude:0,keyword:"",searching:!1});function n(){e.latitude&&e.longitude&&(t.latitude=e.latitude,t.longitude=e.longitude)}return Vue.watch([()=>e.latitude,()=>e.longitude],n),n(),t}function xT(e){const t=__uniConfig.qqMapKey,n=Vue.reactive([]),o=Vue.ref(-1),i=Vue.computed(()=>n[o.value]),r=Vue.reactive({loading:!0,pageSize:20,pageIndex:1,hasNextPage:!0,nextPage:null,selectedIndex:o,selected:i}),a=Vue.ref(""),s=Vue.computed(()=>a.value?`region(${a.value},1,${e.latitude},${e.longitude})`:`nearby(${e.latitude},${e.longitude},5000)`);function l(f){f.forEach(h=>{n.push({name:h.title||h.name,address:h.address,distance:h._distance||h.distance,latitude:h.location.lat,longitude:h.location.lng})})}function c(){r.loading=!0;const f=ot();if(f.type===Ne.GOOGLE){if(r.pageIndex>1&&r.nextPage){r.nextPage();return}new google.maps.places.PlacesService(document.createElement("div"))[e.searching?"textSearch":"nearbySearch"]({location:{lat:e.latitude,lng:e.longitude},query:e.keyword,radius:5e3},(_,v,m)=>{r.loading=!1,_&&_.length&&_.forEach(T=>{n.push({name:T.name||"",address:T.vicinity||T.formatted_address||"",distance:0,latitude:T.geometry.location.lat(),longitude:T.geometry.location.lng()})}),m&&(m.hasNextPage?r.nextPage=()=>{m.nextPage()}:r.hasNextPage=!1)})}else if(f.type===Ne.QQ){const h=e.searching?`https://apis.map.qq.com/ws/place/v1/search?output=jsonp&key=${t}&boundary=${s.value}&keyword=${e.keyword}&page_size=${r.pageSize}&page_index=${r.pageIndex}`:`https://apis.map.qq.com/ws/geocoder/v1/?output=jsonp&key=${t}&location=${e.latitude},${e.longitude}&get_poi=1&poi_options=page_size=${r.pageSize};page_index=${r.pageIndex}`;Ur(h,{callback:"callback"},_=>{if(r.loading=!1,e.searching&&"data"in _&&_.data.length)l(_.data);else if("result"in _){const v=_.result;a.value=v.ad_info?v.ad_info.adcode:"",v.pois&&l(v.pois)}n.length===r.pageSize*r.pageIndex&&(r.hasNextPage=!1)},()=>{r.loading=!1})}else f.type===Ne.AMAP&&window.AMap.plugin("AMap.PlaceSearch",function(){const h=new window.AMap.PlaceSearch({city:"全国",pageSize:10,pageIndex:r.pageIndex}),_=e.searching?e.keyword:"",v=e.searching?5e4:5e3;h.searchNearBy(_,[e.longitude,e.latitude],v,function(m,T){m==="error"?console.error(T):m==="no_data"?r.hasNextPage=!1:l(T.poiList.pois)}),r.loading=!1})}function u(){!r.loading&&r.hasNextPage&&(r.pageIndex++,c())}function d(){r.selectedIndex=-1,r.pageIndex=1,r.hasNextPage=!0,r.nextPage=null,n.splice(0,n.length)}return{listState:r,list:n,loadMore:u,reset:d,getList:c}}const MT=ge({name:"LoctaionPicker",props:kT,emits:["close"],setup(e,{emit:t}){la(),Cp();const{t:n}=D(),o=NT(e),{list:i,listState:r,loadMore:a,reset:s,getList:l}=xT(o),c=Ti(()=>{s(),o.keyword&&l()},1e3,{setTimeout,clearTimeout});Vue.watch(()=>o.searching,m=>{s(),m||l()});function u(m){o.keyword=m.detail.value,c()}function d(){t("close",B({},r.selected))}function f(){t("close")}function h(m){const T=m.detail.centerLocation;T&&v(T)}function _(){Rn({type:"gcj02",success:v,fail:()=>{}})}function v({latitude:m,longitude:T}){o.latitude=m,o.longitude=T,o.searching||(s(),l())}return(!o.latitude||!o.longitude)&&_(),()=>{const m=i.map((T,b)=>Vue.createVNode("div",{key:b,class:{"list-item":!0,selected:r.selectedIndex===b},onClick:()=>{r.selectedIndex=b,o.latitude=T.latitude,o.longitude=T.longitude}},[ce(ms,"#007aff",24),Vue.createVNode("div",{class:"list-item-title"},[T.name]),Vue.createVNode("div",{class:"list-item-detail"},[IT(T.distance),T.address])],10,["onClick"]));return r.loading&&m.unshift(Vue.createVNode("div",{class:"list-loading"},[Vue.createVNode("i",{class:"uni-loading"},null)])),Vue.createVNode("div",{class:"uni-system-choose-location"},[Vue.createVNode(_a,{latitude:o.latitude,longitude:o.longitude,class:"map","show-location":!0,libraries:["places"],onUpdated:l,onRegionchange:h},{default:()=>[Vue.createVNode("div",{class:"map-location",style:`background-image: url("${Ju}")`},null),Vue.createVNode("div",{class:"map-move",onClick:_},[ce(Yu,"#000000",24)],8,["onClick"])],_:1},8,["latitude","longitude","show-location","onUpdated","onRegionchange"]),Vue.createVNode("div",{class:"nav"},[Vue.createVNode("div",{class:"nav-btn back",onClick:f},[ce(xi,"#ffffff",26)],8,["onClick"]),Vue.createVNode("div",{class:{"nav-btn":!0,confirm:!0,disable:!r.selected},onClick:d},[ce(ms,"#ffffff",26)],10,["onClick"])]),Vue.createVNode("div",{class:"menu"},[Vue.createVNode("div",{class:"search"},[Vue.createVNode(Ho,{value:o.keyword,class:"search-input",placeholder:n("uni.chooseLocation.search"),onFocus:()=>o.searching=!0,onInput:u},null,8,["value","placeholder","onFocus","onInput"]),o.searching&&Vue.createVNode("div",{class:"search-btn",onClick:()=>{o.searching=!1,o.keyword=""}},[n("uni.chooseLocation.cancel")],8,["onClick"])]),Vue.createVNode(Ru,{"scroll-y":!0,class:"list",onScrolltolower:a},ET(m)?m:{default:()=>[m],_:2},8,["scroll-y","onScrolltolower"])])])}}});let ni=null;const ef=R(u_,(e,{resolve:t,reject:n})=>{ni?n("cancel"):(ni=Vue.reactive(e),Vue.nextTick(()=>{const o=Kt(MT,ni,i=>{ni=null,Vue.nextTick(()=>{o.unmount()}),i?t(i):n("cancel")});o.mount(Qt("u-a-c"))}))});let oi=!1,Bn=0;const tf=R(ty,(e,{resolve:t,reject:n})=>{if(!navigator.geolocation){n();return}Bn=Bn||navigator.geolocation.watchPosition(o=>{oi=!0,Zu(e==null?void 0:e.type,o.coords).then(i=>{UniServiceJSBridge.invokeOnCallback(vc,i),t()}).catch(i=>{UniServiceJSBridge.invokeOnCallback(Ji,{errMsg:`onLocationChange:fail ${i.message}`})})},o=>{oi||(n(o.message),oi=!0),UniServiceJSBridge.invokeOnCallback(Ji,{errMsg:`onLocationChange:fail ${o.message}`})}),setTimeout(t,100)},ry,ay),nf=R(ny,(e,{resolve:t})=>{Bn&&(navigator.geolocation.clearWatch(Bn),oi=!1,Bn=0),t()}),of=ke(vc,()=>{}),rf=gt(oy,()=>{}),af=ke(Ji,()=>{}),sf=gt(iy,()=>{}),lf=R(uy,(e,{resolve:t,reject:n})=>{let o=!0;return ie(mi,{from:e.from||"navigateBack"})===!0&&(o=!1),o?(getApp().$router.go(-e.delta),t()):n(mi)},hy,wy),cf=R(wn,({url:e,events:t,isAutomatedTesting:n},{resolve:o,reject:i})=>{if(!Ut.handledBeforeEntryPageRoutes){mr.push({args:{type:wn,url:e,events:t,isAutomatedTesting:n},resolve:o,reject:i});return}return et({type:wn,url:e,events:t,isAutomatedTesting:n}).then(o).catch(i)},fy,vy),uf=R(Ki,({url:e},{resolve:t,reject:n})=>{const o=e.split("?")[0],i=gn(o);if(!i){n(`${e}}`);return}i.loader&&i.loader().then(()=>{t({url:e,errMsg:"preloadPage:ok"})}).catch(r=>{n(`${e} ${String(r)}`)})});function ii(e){__uniConfig.darkmode&&UniServiceJSBridge.on(Ye,e)}function da(e){UniServiceJSBridge.off(Ye,e)}function Dn(e){let t={};return __uniConfig.darkmode&&(t=Zn(e,__uniConfig.themeConfig,Mn())),__uniConfig.darkmode?t:e}function df(e,t){const n=Vue.isReactive(e),o=n?Vue.reactive(Dn(e)):Dn(e);return __uniConfig.darkmode&&n&&Vue.watch(e,i=>{const r=Dn(i);for(const a in r)o[a]=r[a]}),t&&ii(t),o}const PT={light:{cancelColor:"#000000"},dark:{cancelColor:"rgb(170, 170, 170)"}},OT=(e,t)=>t.value=PT[e].cancelColor,RT={title:{type:String,default:""},content:{type:String,default:""},showCancel:{type:Boolean,default:!0},cancelText:{type:String,default:"Cancel"},cancelColor:{type:String,default:"#000000"},confirmText:{type:String,default:"OK"},confirmColor:{type:String,default:"#007aff"},visible:{type:Boolean},editable:{type:Boolean,default:!1},placeholderText:{type:String,default:""}},LT=Vue.defineComponent({props:RT,setup(e,{emit:t}){const n=Vue.ref(""),o=()=>a.value=!1,i=()=>(o(),t("close","cancel")),r=()=>(o(),t("close","confirm",n.value)),a=Ld(e,{onEsc:i,onEnter:()=>{!e.editable&&r()}}),s=BT(e);return()=>{const{title:l,content:c,showCancel:u,confirmText:d,confirmColor:f,editable:h,placeholderText:_}=e;return n.value=c,Vue.createVNode(Vue.Transition,{name:"uni-fade"},{default:()=>[Vue.withDirectives(Vue.createVNode("uni-modal",{onTouchmove:Xe},[fT,Vue.createVNode("div",{class:"uni-modal"},[l?Vue.createVNode("div",{class:"uni-modal__hd"},[Vue.createVNode("strong",{class:"uni-modal__title",textContent:l||""},null,8,["textContent"])]):null,h?Vue.createVNode("textarea",{class:"uni-modal__textarea",rows:"1",placeholder:_,value:c,onInput:v=>n.value=v.target.value},null,40,["placeholder","value","onInput"]):Vue.createVNode("div",{class:"uni-modal__bd",onTouchmovePassive:cn,textContent:c},null,40,["onTouchmovePassive","textContent"]),Vue.createVNode("div",{class:"uni-modal__ft"},[u&&Vue.createVNode("div",{style:{color:s.value},class:"uni-modal__btn uni-modal__btn_default",onClick:i},[e.cancelText],12,["onClick"]),Vue.createVNode("div",{style:{color:f},class:"uni-modal__btn uni-modal__btn_primary",onClick:r},[d],12,["onClick"])])])],40,["onTouchmove"]),[[Vue.vShow,a.value]])]})}}});function BT(e){const t=Vue.ref(e.cancelColor),n=({theme:o})=>{OT(o,t)};return Vue.watchEffect(()=>{e.visible?(t.value=e.cancelColor,e.cancelColor==="#000"&&(Mn()==="dark"&&n({theme:"dark"}),ii(n))):da(n)}),t}let He;const DT=te(()=>{UniServiceJSBridge.on("onHidePopup",()=>He.visible=!1)});let fa;function UT(e,t){const n=e==="confirm",o={confirm:n,cancel:e==="cancel"};n&&He.editable&&(o.content=t),fa&&fa(o)}const ff=()=>{He&&(He.visible=!1)},hf=R(Dy,(e,{resolve:t})=>{DT(),fa=t,He?(B(He,e),He.visible=!0):(He=Vue.reactive(e),Vue.nextTick(()=>(Kt(LT,He,UT).mount(Qt("u-a-m")),Vue.nextTick(()=>He.visible=!0))))},Uy,Fy),FT={title:{type:String,default:""},icon:{default:"success",validator(e){return bc.indexOf(e)!==-1}},image:{type:String,default:""},duration:{type:Number,default:1500},mask:{type:Boolean,default:!1},visible:{type:Boolean}},ri="uni-toast__icon",$T={light:"#fff",dark:"rgba(255,255,255,0.9)"},gf=e=>$T[e],zT=Vue.defineComponent({name:"Toast",props:FT,setup(e){_p(),yp();const{Icon:t}=WT(e),n=Ld(e,{});return()=>{const{mask:o,duration:i,title:r,image:a}=e;return Vue.createVNode(Vue.Transition,{name:"uni-fade"},{default:()=>[Vue.withDirectives(Vue.createVNode("uni-toast",{"data-duration":i},[o?Vue.createVNode("div",{class:"uni-mask",style:"background: transparent;",onTouchmove:Xe},null,40,["onTouchmove"]):"",!a&&!t.value?Vue.createVNode("div",{class:"uni-sample-toast"},[Vue.createVNode("p",{class:"uni-simple-toast__text"},[r])]):Vue.createVNode("div",{class:"uni-toast"},[a?Vue.createVNode("img",{src:a,class:ri},null,10,["src"]):t.value,Vue.createVNode("p",{class:"uni-toast__content"},[r])])],8,["data-duration"]),[[Vue.vShow,n.value]])]})}}});function WT(e){const t=Vue.ref(gf(Mn())),n=({theme:i})=>t.value=gf(i);return Vue.watchEffect(()=>{e.visible?ii(n):da(n)}),{Icon:Vue.computed(()=>{switch(e.icon){case"success":return Vue.createVNode(ce(fn,t.value,38),{class:ri});case"error":return Vue.createVNode(ce(ps,t.value,38),{class:ri});case"loading":return Vue.createVNode("i",{class:[ri,"uni-loading"]},null,2);default:return null}})}}let rt,St="",Un;const HT=Vue.effectScope();function qT(){HT.run(()=>{Vue.watch([()=>rt.visible,()=>rt.duration],([e,t])=>{if(e){if(Un&&clearTimeout(Un),St==="onShowLoading")return;Un=setTimeout(()=>{ai("onHideToast")},t)}else Un&&clearTimeout(Un)})})}function pf(e){rt?B(rt,e):(rt=Vue.reactive(B(e,{visible:!1})),Vue.nextTick(()=>{qT(),UniServiceJSBridge.on("onHidePopup",()=>ai("onHidePopup")),Kt(zT,rt,()=>{}).mount(Qt("u-a-t"))})),setTimeout(()=>{rt.visible=!0},10)}const mf=R($y,(e,{resolve:t,reject:n})=>{pf(e),St="onShowToast",t()},zy,Wy),GT={icon:"loading",duration:1e8,image:""},vf=R(Ry,(e,{resolve:t,reject:n})=>{B(e,GT),pf(e),St="onShowLoading",t()},Ly,By),_f=R(Cy,(e,{resolve:t,reject:n})=>{ai("onHideToast"),t()}),yf=R(Vy,(e,{resolve:t,reject:n})=>{ai("onHideLoading"),t()});function ai(e){const{t}=D();if(!St)return;let n="";if(e==="onHideToast"&&St!=="onShowToast"?n=t("uni.showToast.unpaired"):e==="onHideLoading"&&St!=="onShowLoading"&&(n=t("uni.showLoading.unpaired")),n)return console.warn(n);St="",setTimeout(()=>{rt.visible=!1},10)}function bf(e){const t=Vue.ref(0),n=Vue.ref(0),o=Vue.computed(()=>t.value>=500&&n.value>=500),i=Vue.computed(()=>{const r={content:{transform:"",left:"",top:"",bottom:""},triangle:{left:"",top:"",bottom:"","border-width":"","border-color":""}},a=r.content,s=r.triangle,l=e.popover;function c(u){return Number(u)||0}if(o.value&&l){B(s,{position:"absolute",width:"0",height:"0","margin-left":"-6px","border-style":"solid"});const u=c(l.left),d=c(l.width),f=c(l.top),h=c(l.height),_=u+d/2;a.transform="none !important";const v=Math.max(0,_-300/2);a.left=`${v}px`;let m=Math.max(12,_-v);m=Math.min(288,m),s.left=`${m}px`;const T=n.value/2;f+h-T>T-f?(a.top="auto",a.bottom=`${n.value-f+6}px`,s.bottom="-6px",s["border-width"]="6px 6px 0 6px",s["border-color"]="#fcfcfd transparent transparent transparent"):(a.top=`${f+h+6}px`,s.top="-6px",s["border-width"]="0 6px 6px 6px",s["border-color"]="transparent transparent #fcfcfd transparent")}return r});return Vue.onMounted(()=>{const r=()=>{const{windowWidth:a,windowHeight:s,windowTop:l}=uni.getSystemInfoSync();t.value=a,n.value=s+(l||0)};window.addEventListener("resize",r),r(),Vue.onUnmounted(()=>{window.removeEventListener("resize",r)})}),{isDesktop:o,popupStyle:i}}const jT={light:{listItemColor:"#000000",cancelItemColor:"#000000"},dark:{listItemColor:"rgba(255, 255, 255, 0.8)",cancelItemColor:"rgba(255, 255, 255)"}};function YT(e,t){["listItemColor","cancelItemColor"].forEach(o=>{t[o]=jT[e][o]})}const XT={title:{type:String,default:""},itemList:{type:Array,default(){return[]}},itemColor:{type:String,default:"#000000"},popover:{type:Object,default:null},visible:{type:Boolean,default:!1}},JT=Vue.defineComponent({name:"ActionSheet",props:XT,emits:["close"],setup(e,{emit:t}){vp();const n=Vue.ref(260),o=Vue.ref(0),i=Vue.ref(0),r=Vue.ref(0),a=Vue.ref(0),s=Vue.ref(null),l=Vue.ref(null),{t:c}=D(),{_close:u}=KT(e,t),{popupStyle:d}=bf(e);let f;Vue.onMounted(()=>{const{scroller:v,handleTouchStart:m,handleTouchMove:T,handleTouchEnd:b}=Iu(s.value,{enableY:!0,friction:new Mr(1e-4),spring:new Pr(2,90,20),onScroll:g=>{a.value=g.target.scrollTop}});f=v,kn(s.value,g=>{if(v)switch(g.detail.state){case"start":m(g);break;case"move":T(g);break;case"end":case"cancel":b(g)}},!0)});function h(v){const m=r.value+v.deltaY;Math.abs(m)>10?(a.value+=m/3,a.value=a.value>=o.value?o.value:a.value<=0?0:a.value,f.scrollTo(a.value)):r.value=m,v.preventDefault()}Vue.watch(()=>e.visible,()=>{Vue.nextTick(()=>{e.title&&(i.value=document.querySelector(".uni-actionsheet__title").offsetHeight),f.update(),s.value&&(o.value=s.value.clientHeight-n.value),document.querySelectorAll(".uni-actionsheet__cell").forEach(v=>{QT(v)})})});const _=ZT(e);return()=>Vue.createVNode("uni-actionsheet",{onTouchmove:Xe},[Vue.createVNode(Vue.Transition,{name:"uni-fade"},{default:()=>[Vue.withDirectives(Vue.createVNode("div",{class:"uni-mask uni-actionsheet__mask",onClick:()=>u(-1)},null,8,["onClick"]),[[Vue.vShow,e.visible]])]}),Vue.createVNode("div",{class:["uni-actionsheet",{"uni-actionsheet_toggle":e.visible}],style:d.value.content},[Vue.createVNode("div",{ref:l,class:"uni-actionsheet__menu",onWheel:h},[e.title?Vue.createVNode(Vue.Fragment,null,[Vue.createVNode("div",{class:"uni-actionsheet__cell",style:{height:`${i.value}px`}},null),Vue.createVNode("div",{class:"uni-actionsheet__title"},[e.title])]):"",Vue.createVNode("div",{style:{maxHeight:`${n.value}px`,overflow:"hidden"}},[Vue.createVNode("div",{ref:s},[e.itemList.map((v,m)=>Vue.createVNode("div",{key:m,style:{color:_.listItemColor},class:"uni-actionsheet__cell",onClick:()=>u(m)},[v],12,["onClick"]))],512)])],40,["onWheel"]),Vue.createVNode("div",{class:"uni-actionsheet__action"},[Vue.createVNode("div",{style:{color:_.cancelItemColor},class:"uni-actionsheet__cell",onClick:()=>u(-1)},[c("uni.showActionSheet.cancel")],12,["onClick"])]),Vue.createVNode("div",{style:d.value.triangle},null,4)],6)],40,["onTouchmove"])}});function KT(e,t){function n(r){t("close",r)}const{key:o,disable:i}=aa();return Vue.watch(()=>e.visible,r=>i.value=!r),Vue.watchEffect(()=>{const{value:r}=o;r==="esc"&&n&&n(-1)}),{_close:n}}function QT(e){let n=0,o=0;e.addEventListener("touchstart",i=>{const r=i.changedTouches[0];n=r.clientX,o=r.clientY}),e.addEventListener("touchend",i=>{const r=i.changedTouches[0];if(Math.abs(r.clientX-n)<20&&Math.abs(r.clientY-o)<20){const a=i.target,s=i.currentTarget,l=new CustomEvent("click",{bubbles:!0,cancelable:!0,target:a,currentTarget:s});["screenX","screenY","clientX","clientY","pageX","pageY"].forEach(c=>{l[c]=r[c]}),i.target.dispatchEvent(l)}})}function ZT(e){const t=Vue.reactive({listItemColor:"#000",cancelItemColor:"#000"}),n=({theme:o})=>{YT(o,t)};return Vue.watchEffect(()=>{e.visible?(t.listItemColor=t.cancelItemColor=e.itemColor,e.itemColor==="#000"&&(n({theme:Mn()}),ii(n))):da(n)}),t}let ha,ga,je;const eV=te(()=>{UniServiceJSBridge.on("onHidePopup",()=>je.visible=!1)});function tV(e){e===-1?ga&&ga("cancel"):ha&&ha({tapIndex:e})}const wf=()=>{je&&(je.visible=!1)},Sf=R(My,(e,{resolve:t,reject:n})=>{eV(),ha=t,ga=n,je?(B(je,e),je.visible=!0):(je=Vue.reactive(e),Vue.nextTick(()=>(Kt(JT,je,tV).mount(Qt("u-s-a-s")),Vue.nextTick(()=>je.visible=!0))))},Py,Oy),pa=R(Ay,({family:e,source:t,desc:n},{resolve:o,reject:i})=>{t.startsWith('url("')||t.startsWith("url('")?t=`url('${Q(t.substring(5,t.length-2))}')`:t.startsWith("url(")?t=`url('${Q(t.substring(4,t.length-1))}')`:t=Q(t),Wg(e,t,n).then(()=>{o()}).catch(r=>{i(`loadFontFace:fail ${r}`)})});function nV(e){document.title=e,UniServiceJSBridge.emit(ka,{titleText:e})}function oV(e){function t(){nV(e.navigationBar.titleText)}Vue.watchEffect(t),Vue.onActivated(t)}function si(e,t,n,o,i){if(!e)return i("page not found");const{navigationBar:r}=e;switch(t){case Qi:const{frontColor:a,backgroundColor:s,animation:l}=n,{duration:c,timingFunc:u}=l;a&&(r.titleColor=a==="#000000"?"#000000":"#ffffff"),s&&(r.backgroundColor=s),r.duration=c+"ms",r.timingFunc=u;break;case er:r.loading=!0;break;case tr:r.loading=!1;break;case Zi:const{title:d}=n;r.titleText=d;break}o()}const Tf=R(Qi,(e,{resolve:t,reject:n})=>{si(xt(),Qi,e,t,n)},ky,Ey),Vf=R(er,(e,{resolve:t,reject:n})=>{si(xt(),er,e||{},t,n)}),Cf=R(tr,(e,{resolve:t,reject:n})=>{si(xt(),tr,e||{},t,n)}),Af=R(Zi,(e,{resolve:t,reject:n})=>{si(xt(),Zi,e,t,n)}),Ef=R(Iy,({scrollTop:e,selector:t,duration:n},{resolve:o})=>{Hg(t||e||0,n),o()},Ny,xy),kf=R(nr,(e,{resolve:t})=>{UniServiceJSBridge.invokeViewMethod(nr,{},ft()),t()}),If=R(or,(e,{resolve:t})=>{UniServiceJSBridge.invokeViewMethod(or,{},ft()),t()}),iV=["text","iconPath","iconfont","selectedIconPath","visible"],rV=["color","selectedColor","backgroundColor","borderStyle","borderColor","midButton"],ma=["badge","redDot"];function Fn(e,t,n){t.forEach(function(o){le(n,o)&&(e[o]=n[o])})}function at(e,t,n,o){var i;let r=!1;const a=Ft();if(a.length&&nt(a[a.length-1]).meta.isTabBar&&(r=!0),!r)return o("not TabBar page");const{index:s}=t;if(typeof s=="number"){const c=(i=__uniConfig==null?void 0:__uniConfig.tabBar)==null?void 0:i.list.length;if(!c||s>=c)return o("tabbar item not found")}const l=Vn();switch(e){case sr:l.shown=!0;break;case ar:l.shown=!1;break;case ir:const c=l.list[s],u=c.pagePath;Fn(c,iV,t);const{pagePath:d}=t;if(d){const f=qe(d);f!==u&&lm(s,u,f)}break;case rr:Fn(l,rV,t);break;case cr:Fn(l.list[s],ma,{badge:"",redDot:!0});break;case dr:Fn(l.list[s],ma,{badge:t.text,redDot:!0});break;case lr:case ur:Fn(l.list[s],ma,{badge:"",redDot:!1});break}n()}const Nf=R(ir,(e,{resolve:t,reject:n})=>{at(ir,e,t,n)},Hy,qy),xf=R(rr,(e,{resolve:t,reject:n})=>{at(rr,e,t,n)},Gy,Yy),Mf=R(ar,(e,{resolve:t,reject:n})=>{at(ar,e||{},t,n)}),Pf=R(sr,(e,{resolve:t,reject:n})=>{at(sr,e||{},t,n)}),Of=R(lr,(e,{resolve:t,reject:n})=>{at(lr,e,t,n)},Xy,Jy),Rf=R(cr,(e,{resolve:t,reject:n})=>{at(cr,e,t,n)},Ky,Qy),Lf=R(ur,(e,{resolve:t,reject:n})=>{at(ur,e,t,n)},Zy,eb),Bf=R(dr,(e,{resolve:t,reject:n})=>{at(dr,e,t,n)},tb,nb),aV="UniTabbarIconFont",sV={width:"50px",height:"50px",iconWidth:"24px"},lV=ge({name:"TabBar",setup(){const e=Vue.ref([]),t=Vn(),n=df(t,()=>{const s=Dn(t);n.backgroundColor=s.backgroundColor,n.borderStyle=s.borderStyle,n.color=s.color,n.selectedColor=s.selectedColor,n.blurEffect=s.blurEffect,n.midButton=s.midButton,s.list&&s.list.length&&s.list.forEach((l,c)=>{n.list[c].iconPath=l.iconPath,n.list[c].selectedIconPath=l.selectedIconPath})});uV(n,e),cV(n);const o=dV(VueRouter.useRoute(),n,e),{style:i,borderStyle:r,placeholderStyle:a}=gV(n);return Vue.onMounted(()=>{n.iconfontSrc&&pa({family:aV,source:`url("${n.iconfontSrc}")`})}),()=>{const s=pV(n,o,e);return Vue.createVNode("uni-tabbar",{class:"uni-tabbar-"+n.position},[Vue.createVNode("div",{class:"uni-tabbar",style:i.value},[Vue.createVNode("div",{class:"uni-tabbar-border",style:r.value},null,4),s],4),Vue.createVNode("div",{class:"uni-placeholder",style:a.value},null,4)],2)}}});function cV(e){Vue.watch(()=>e.shown,t=>{ds({"--window-bottom":Ec(t?parseInt(e.height):0)})})}function uV(e,t){const n=Vue.ref(B({type:"midButton"},e.midButton));function o(){let i=[];i=e.list.filter(r=>r.visible!==!1),__UNI_FEATURE_TABBAR_MIDBUTTON__&&e.midButton&&(n.value=B({},sV,n.value,e.midButton),i=i.filter(r=>!$f(r)),i.length%2===0&&i.splice(Math.floor(i.length/2),0,n.value)),t.value=i}Vue.watchEffect(o)}function dV(e,t,n){return Vue.watchEffect(()=>{const o=e.meta;if(o.isTabBar){const i=o.route,r=n.value.findIndex(a=>a.pagePath===i);t.selectedIndex=r}}),(o,i)=>{const{type:r}=o;return()=>{if(__UNI_FEATURE_TABBAR_MIDBUTTON__&&r==="midButton")return UniServiceJSBridge.invokeOnCallback(Rl);const{pagePath:a,text:s}=o;let l=qe(a);l===__uniRoutes[0].alias&&(l="/"),e.path!==l?uni.switchTab({from:"tabBar",url:l,tabBarText:s}):ie("onTabItemTap",{index:i,text:s,pagePath:a})}}}const fV="#f7f7fa",Df="rgb(0, 0, 0, 0.8)",Uf="rgb(250, 250, 250, 0.8)",hV={dark:Df,light:Uf,extralight:Uf},Ff={white:"rgba(255, 255, 255, 0.33)",black:"rgba(0, 0, 0, 0.33)"};function gV(e){const t=Vue.computed(()=>{let i=e.backgroundColor;const r=e.blurEffect;return i||Po&&r&&r!=="none"&&(i=hV[r]),{backgroundColor:i||fV,backdropFilter:r!=="none"?"blur(10px)":r}}),n=Vue.computed(()=>{const{borderStyle:i,borderColor:r}=e;return r&&J(r)?{backgroundColor:r}:{backgroundColor:Ff[i]||Ff.black}}),o=Vue.computed(()=>({height:e.height}));return{style:t,borderStyle:n,placeholderStyle:o}}function $f(e){return e.type==="midButton"}function pV(e,t,n){const{selectedIndex:o,selectedColor:i,color:r}=e;return n.value.map((a,s)=>{const l=o===s,c=l?i:r,u=l&&a.selectedIconPath||a.iconPath||"",d=a.iconfont?l&&a.iconfont.selectedText||a.iconfont.text:void 0,f=a.iconfont?l&&a.iconfont.selectedColor||a.iconfont.color:void 0;return __UNI_FEATURE_TABBAR_MIDBUTTON__&&$f(a)?bV(c,u,d,f,a,e,s,t):zf(c,u,d,f,a,e,s,t)})}function zf(e,t,n,o,i,r,a,s){return Vue.createVNode("div",{key:a,class:"uni-tabbar__item",onClick:s(i,a)},[Wf(e,t||"",n,o,i,r)],8,["onClick"])}function Wf(e,t,n,o,i,r){const{height:a}=r;return Vue.createVNode("div",{class:"uni-tabbar__bd",style:{height:a}},[n?vV(n,o||Df,i,r):t&&mV(t,i,r),i.text&&_V(e,i,r),i.redDot&&yV(i.badge)],4)}function mV(e,t,n){const{type:o,text:i}=t,{iconWidth:r}=n,a="uni-tabbar__icon"+(i?" uni-tabbar__icon__diff":""),s={width:r,height:r};return Vue.createVNode("div",{class:a,style:s},[o!=="midButton"&&Vue.createVNode("img",{src:Q(e)},null,8,["src"])],6)}function vV(e,t,n,o){var i;const{type:r,text:a}=n,{iconWidth:s}=o,l="uni-tabbar__icon"+(a?" uni-tabbar__icon__diff":""),c={width:s,height:s},u={fontSize:((i=n.iconfont)==null?void 0:i.fontSize)||s,color:t};return Vue.createVNode("div",{class:l,style:c},[r!=="midButton"&&Vue.createVNode("div",{class:"uni-tabbar__iconfont",style:u},[e],4)],6)}function _V(e,t,n){const{iconPath:o,text:i}=t,{fontSize:r,spacing:a}=n,s={color:e,fontSize:r,lineHeight:o?"normal":1.8,marginTop:o?a:"inherit"};return Vue.createVNode("div",{class:"uni-tabbar__label",style:s},[i],4)}function yV(e){const t="uni-tabbar__reddot"+(e?" uni-tabbar__badge":"");return Vue.createVNode("div",{class:t},[e],2)}function bV(e,t,n,o,i,r,a,s){const{width:l,height:c,backgroundImage:u,iconWidth:d}=i;return Vue.createVNode("div",{key:"midButton",class:"uni-tabbar__item",style:{flex:"0 0 "+l,position:"relative"},onClick:s(i,a)},[Vue.createVNode("div",{class:"uni-tabbar__mid",style:{width:l,height:c,backgroundImage:u?"url('"+Q(u)+"')":"none"}},[t&&Vue.createVNode("img",{style:{width:d,height:d},src:Q(t)},null,12,["src"])],4),Wf(e,t,n,o,i,r)],12,["onClick"])}const tn="0px";let Hf;function Le(){return Hf}const qf=ge({name:"Layout",setup(e,{emit:t}){const n=Vue.ref(null);SV();const o=__UNI_FEATURE_PAGES__&&mb(),{layoutState:i,windowState:r}=CV();VV(i,n);const a=__UNI_FEATURE_TOPWINDOW__&&xV(i),s=__UNI_FEATURE_LEFTWINDOW__&&MV(i),l=__UNI_FEATURE_RIGHTWINDOW__&&PV(i),c=__UNI_FEATURE_TABBAR__&&EV(),u=wV(c);return Hf=i,()=>{const d=AV(o,i,r,a,s,l),f=__UNI_FEATURE_TABBAR__&&kV(c);return Vue.createVNode("uni-app",{ref:n,class:u.value},[d,f],2)}}});function wV(e){const t=Vue.ref(!1);return Vue.computed(()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value}))}function SV(){Ue({"--status-bar-height":tn,"--top-window-height":tn,"--window-left":tn,"--window-right":tn,"--window-margin":tn,"--tab-bar-height":tn})}function TV(e,t){const n=window.matchMedia("(min-width: "+e+"px)");return n.addEventListener?n.addEventListener("change",t):n.addListener(t),n.matches}function VV(e,t){const n=mn();function o(){const i=document.body.clientWidth,r=Ft();let a={};if(r.length>0){const c=r[r.length-1];a=nt(c).meta}else{const c=gn(n.path,!0);c&&(a=c.meta)}const s=parseInt(String((le(a,"maxWidth")?a.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let l=!1;i>s?l=!0:l=!1,l&&s?(e.marginWidth=(i-s)/2,Vue.nextTick(()=>{const c=t.value;c&&c.setAttribute("style","max-width:"+s+"px;margin:0 auto;")})):(e.marginWidth=0,Vue.nextTick(()=>{const c=t.value;c&&c.removeAttribute("style")}))}Vue.watch([()=>n.path],o),Vue.onMounted(()=>{o(),window.addEventListener("resize",o)})}function CV(){const e=mn();if(!__UNI_FEATURE_RESPONSIVE__){const u=Vue.reactive({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Vue.watch(()=>u.marginWidth,d=>Ue({"--window-margin":d+"px"})),Vue.watch(()=>u.leftWindowWidth+u.marginWidth,d=>{Ue({"--window-left":d+"px"})}),Vue.watch(()=>u.rightWindowWidth+u.marginWidth,d=>{Ue({"--window-right":d+"px"})}),{layoutState:u,windowState:Vue.computed(()=>({}))}}const t=Vue.ref(!1),n=Vue.ref(!1),o=Vue.ref(!1),i=Vue.computed(()=>__UNI_FEATURE_TOPWINDOW__&&e.meta.topWindow!==!1&&t.value),r=Vue.computed(()=>__UNI_FEATURE_LEFTWINDOW__&&e.meta.leftWindow!==!1&&n.value),a=Vue.computed(()=>__UNI_FEATURE_RIGHTWINDOW__&&e.meta.rightWindow!==!1&&o.value),s=Vue.reactive({topWindowMediaQuery:t,showTopWindow:i,apiShowTopWindow:!1,leftWindowMediaQuery:n,showLeftWindow:r,apiShowLeftWindow:!1,rightWindowMediaQuery:o,showRightWindow:a,apiShowRightWindow:!1,topWindowHeight:0,marginWidth:0,leftWindowWidth:0,rightWindowWidth:0,navigationBarTitleText:"",topWindowStyle:{},leftWindowStyle:{},rightWindowStyle:{}});["topWindow","leftWindow","rightWindow"].forEach(u=>{var d;const f=(d=__uniConfig[u])==null?void 0:d.matchMedia;let h=Sg;if(f&&le(f,"minWidth")){const v=f.minWidth;h=c0(v)?v:h}const _=TV(h,v=>{s[`${u}MediaQuery`]=v.matches});s[`${u}MediaQuery`]=_}),Vue.watch(()=>s.topWindowHeight,u=>Ue({"--top-window-height":u+"px"})),Vue.watch(()=>s.marginWidth,u=>Ue({"--window-margin":u+"px"})),Vue.watch(()=>s.leftWindowWidth+s.marginWidth,u=>{Ue({"--window-left":u+"px"})}),Vue.watch(()=>s.rightWindowWidth+s.marginWidth,u=>{Ue({"--window-right":u+"px"})}),UniServiceJSBridge.on(ka,u=>{s.navigationBarTitleText=u.titleText});const c=Vue.computed(()=>({matchTopWindow:s.topWindowMediaQuery,showTopWindow:s.showTopWindow||s.apiShowTopWindow,matchLeftWindow:s.leftWindowMediaQuery,showLeftWindow:s.showLeftWindow||s.apiShowLeftWindow,matchRightWindow:s.rightWindowMediaQuery,showRightWindow:s.showRightWindow||s.apiShowRightWindow}));return{layoutState:s,windowState:c}}function AV(e,t,n,o,i,r){const a=__UNI_FEATURE_PAGES__?NV(e):IV();if(!__UNI_FEATURE_RESPONSIVE__)return a;const s=__UNI_FEATURE_TOPWINDOW__?OV(o,t,n.value):null,l=__UNI_FEATURE_LEFTWINDOW__?RV(i,t,n.value):null,c=__UNI_FEATURE_RIGHTWINDOW__?LV(r,t,n.value):null;return Vue.createVNode("uni-layout",{class:{"uni-app--showtopwindow":__UNI_FEATURE_TOPWINDOW__&&t.showTopWindow,"uni-app--showleftwindow":__UNI_FEATURE_LEFTWINDOW__&&t.showLeftWindow,"uni-app--showrightwindow":__UNI_FEATURE_RIGHTWINDOW__&&t.showRightWindow}},[s,Vue.createVNode("uni-content",null,[Vue.createVNode("uni-main",null,[a]),l,c])],2)}function EV(e){const t=mn(),n=Vn(),o=Vue.computed(()=>t.meta.isTabBar&&n.shown);return Ue({"--tab-bar-height":n.height}),o}function kV(e){return Vue.withDirectives(Vue.createVNode(lV,null,null,512),[[Vue.vShow,e.value]])}function IV(){return Vue.createVNode(__uniRoutes[0].component)}function NV({routeKey:e,isTabBar:t,routeCache:n}){return Vue.createVNode(VueRouter.RouterView,null,{default:Vue.withCtx(({Component:o})=>[(Vue.openBlock(),Vue.createBlock(Vue.KeepAlive,{matchBy:"key",cache:n},[(Vue.openBlock(),Vue.createBlock(Vue.resolveDynamicComponent(o),{type:t.value?"tabBar":"",key:e.value}))],1032,["cache"]))]),_:1})}function xV(e){const{component:t,style:n}=__uniConfig.topWindow,o=Vue.ref(null);function i(){const r=o.value,s=Qn(r.$).getBoundingClientRect().height;e.topWindowHeight=s}return Vue.onMounted(i),Vue.watch(()=>e.showTopWindow||e.apiShowTopWindow,()=>Vue.nextTick(i)),e.topWindowStyle=n,{component:t,windowRef:o}}function MV(e){const{component:t,style:n}=__uniConfig.leftWindow,o=Vue.ref(null);function i(){const r=o.value,s=Qn(r.$).getBoundingClientRect().width;e.leftWindowWidth=s}return Vue.onMounted(i),Vue.watch(()=>e.showLeftWindow||e.apiShowLeftWindow,()=>Vue.nextTick(i)),e.leftWindowStyle=n,{component:t,windowRef:o}}function PV(e){const{component:t,style:n}=__uniConfig.rightWindow,o=Vue.ref(null);function i(){const r=o.value,s=Qn(r.$).getBoundingClientRect().width;e.rightWindowWidth=s}return Vue.onMounted(i),Vue.watch(()=>e.showRightWindow||e.apiShowRightWindow,()=>Vue.nextTick(i)),e.rightWindowStyle=n,{component:t,windowRef:o}}function OV(e,t,n){if(e){const{component:o,windowRef:i}=e;return Vue.withDirectives(Vue.createVNode("uni-top-window",null,[Vue.createVNode("div",{class:"uni-top-window",style:t.topWindowStyle},[Vue.createVNode(o,Vue.mergeProps({ref:i,"navigation-bar-title-text":t.navigationBarTitleText},n),null,16,["navigation-bar-title-text"])],4),Vue.createVNode("div",{class:"uni-top-window--placeholder",style:{height:t.topWindowHeight+"px"}},null,4)],512),[[Vue.vShow,t.showTopWindow||t.apiShowTopWindow]])}}function RV(e,t,n){if(e){const{component:o,windowRef:i}=e;return Vue.withDirectives(Vue.createVNode("uni-left-window",{"data-show":t.apiShowLeftWindow||void 0,style:t.leftWindowStyle},[t.apiShowLeftWindow?Vue.createVNode("div",{class:"uni-mask",onClick:()=>t.apiShowLeftWindow=!1},null,8,["onClick"]):null,Vue.createVNode("div",{class:"uni-left-window"},[Vue.createVNode(o,Vue.mergeProps({ref:i},n),null,16)])],12,["data-show"]),[[Vue.vShow,t.showLeftWindow||t.apiShowLeftWindow]])}}function LV(e,t,n){if(e){const{component:o,windowRef:i}=e;return Vue.withDirectives(Vue.createVNode("uni-right-window",{"data-show":t.apiShowRightWindow||void 0,style:t.rightWindowStyle},[t.apiShowRightWindow?Vue.createVNode("div",{class:"uni-mask",onClick:()=>t.apiShowRightWindow=!1},null,8,["onClick"]):null,Vue.createVNode("div",{class:"uni-right-window"},[Vue.createVNode(o,Vue.mergeProps({ref:i},n),null,16)])],12,["data-show"]),[[Vue.vShow,t.showRightWindow||t.apiShowRightWindow]])}}const Gf=R("showTopWindow",(e,{resolve:t,reject:n})=>{const o=Le();if(!o){n();return}o.apiShowTopWindow=!0,Vue.nextTick(t)}),jf=R("hideTopWindow",(e,{resolve:t,reject:n})=>{const o=Le();if(!o){n();return}o.apiShowTopWindow=!1,Vue.nextTick(t)}),Yf=R("showLeftWindow",(e,{resolve:t,reject:n})=>{const o=Le();if(!o){n();return}o.apiShowLeftWindow=!0,Vue.nextTick(t)}),Xf=R("hideLeftWindow",(e,{resolve:t,reject:n})=>{const o=Le();if(!o){n();return}o.apiShowLeftWindow=!1,Vue.nextTick(t)}),Jf=R("showRightWindow",(e,{resolve:t,reject:n})=>{const o=Le();if(!o){n();return}o.apiShowRightWindow=!0,Vue.nextTick(t)}),Kf=R("hideRightWindow",(e,{resolve:t,reject:n})=>{const o=Le();if(!o){n();return}o.apiShowRightWindow=!1,Vue.nextTick(t)}),Qf=j("getTopWindowStyle",()=>{const e=Le();return B({},e&&e.topWindowStyle)}),Zf=j("setTopWindowStyle",e=>{const t=Le();t&&(t.topWindowStyle=e)}),eh=j("getLeftWindowStyle",()=>{const e=Le();return B({},e&&e.leftWindowStyle)}),th=j("setLeftWindowStyle",e=>{const t=Le();t&&(t.leftWindowStyle=e)}),nh=j("getRightWindowStyle",()=>{const e=Le();return B({},e&&e.rightWindowStyle)}),oh=j("setRightWindowStyle",e=>{const t=Le();t&&(t.rightWindowStyle=e)}),ih=j("getElementById",e=>{const t=document.querySelector("uni-page-body");return t?t.querySelector(`#${e}`):null}),rh=R(gc,be(gc)),ah="getRecorderManager",sh=j(ah,vo(ah)),lh=R(pc,be(pc)),ch="createCameraContext",uh=j(ch,vo(ch)),dh="createLivePlayerContext",fh=j(dh,vo(dh)),hh="saveFile",gh=R(hh,be(hh)),ph="getSavedFileList",mh=R(ph,be(ph)),vh="getSavedFileInfo",_h=R(vh,be(vh)),yh="removeSavedFile",bh=R(yh,be(yh)),wh="onMemoryWarning",Sh=ke(wh,Ui(wh)),Th="onGyroscopeChange",Vh=ke(Th,Ui(Th)),Ch="startGyroscope",Ah=R(Ch,be(Ch)),Eh="stopGyroscope",kh=R(Eh,be(Eh)),Ih="scanCode",Nh=R(Ih,be(Ih)),xh="setScreenBrightness",Mh=R(xh,be(xh)),Ph="getScreenBrightness",Oh=R(Ph,be(Ph)),Rh="setKeepScreenOn",Lh=R(Rh,be(Rh)),Bh="onUserCaptureScreen",Dh=ke(Bh,Ui(Bh)),Uh="addPhoneContact",Fh=R(Uh,be(Uh)),$h="login",zh=R($h,be($h)),Wh="getProvider",Hh=R(Wh,be(Wh)),BV=Object.defineProperty({__proto__:null,$emit:ul,$off:cl,$on:sl,$once:ll,__f__:dl,addInterceptor:il,addPhoneContact:Fh,arrayBufferToBase64:Js,base64ToArrayBuffer:Xs,canIUse:Ac,canvasGetImageData:Sl,canvasPutImageData:Tl,canvasToTempFilePath:Vl,chooseFile:Pd,chooseImage:Od,chooseLocation:ef,chooseVideo:zd,clearStorage:Cd,clearStorageSync:oa,closePreviewImage:$d,closeSocket:Yd,connectSocket:qd,createAnimation:Ol,createCameraContext:uh,createCanvasContext:wl,createInnerAudioContext:td,createIntersectionObserver:kl,createLivePlayerContext:fh,createMapContext:hl,createMediaQueryObserver:Nl,createSelectorQuery:Ml,createVideoContext:fl,cssBackdropFilter:Po,cssConstant:Mo,cssEnv:xo,cssVar:gr,downloadFile:Wd,getAppBaseInfo:qr,getClipboardData:pd,getDeviceInfo:Hr,getElementById:ih,getEnterOptionsSync:tc,getFileInfo:Ed,getImageInfo:Nd,getLaunchOptionsSync:nc,getLeftWindowStyle:eh,getLocale:bo,getLocation:Rn,getNetworkType:Yr,getProvider:Hh,getPushClientId:ac,getRecorderManager:sh,getRightWindowStyle:nh,getSavedFileInfo:_h,getSavedFileList:mh,getScreenBrightness:Oh,getSelectedTextRange:Hl,getStorage:Td,getStorageInfo:Ad,getStorageInfoSync:ia,getStorageSync:Sd,getSystemInfo:rd,getSystemInfoSync:Gr,getTabBarPageId:Eo,getTopWindowStyle:Qf,getVideoInfo:xd,getWindowInfo:zr,hideActionSheet:wf,hideKeyboard:Id,hideLeftWindow:Xf,hideLoading:yf,hideModal:ff,hideNavigationBarLoading:Cf,hideRightWindow:Kf,hideTabBar:Mf,hideTabBarRedDot:Of,hideToast:_f,hideTopWindow:jf,interceptors:al,invokePushCallback:ic,loadFontFace:pa,login:zh,makePhoneCall:nd,navigateBack:lf,navigateTo:cf,offAccelerometerChange:ud,offAppHide:ec,offAppShow:Ql,offCompassChange:Qr,offError:Jl,offLocationChange:rf,offLocationChangeError:sf,offNetworkStatusChange:ld,offPageNotFound:Yl,offPushMessage:lc,offThemeChange:yd,offUnhandledRejection:Gl,offWindowResize:Dl,onAccelerometerChange:cd,onAppHide:Zl,onAppShow:Kl,onCompassChange:Kr,onCreateVueApp:Ga,onError:Xl,onGyroscopeChange:Vh,onLocaleChange:Fl,onLocationChange:of,onLocationChangeError:af,onMemoryWarning:Sh,onNetworkStatusChange:sd,onPageNotFound:jl,onPushMessage:sc,onSocketClose:Qd,onSocketError:Jd,onSocketMessage:Kd,onSocketOpen:Xd,onTabBarMidButtonTap:Ll,onThemeChange:_d,onUnhandledRejection:ql,onUserCaptureScreen:Dh,onWindowResize:Bl,openDocument:kd,openLocation:Zd,pageScrollTo:Ef,preloadPage:uf,previewImage:Fd,reLaunch:Tc,redirectTo:Sc,removeAllPages:Io,removeInterceptor:rl,removeLastPage:ko,removeNonTabBarPages:Ao,removeSavedFile:bh,removeStorage:Vd,removeStorageSync:na,removeTabBarBadge:Lf,request:ca,rpx2px:_o,saveFile:gh,saveImageToPhotosAlbum:rh,saveVideoToPhotosAlbum:lh,scanCode:Nh,sendSocketMessage:jd,setClipboardData:md,setKeepScreenOn:Lh,setLeftWindowStyle:th,setLocale:$l,setNavigationBarColor:Tf,setNavigationBarTitle:Af,setPageMeta:zl,setRightWindowStyle:oh,setScreenBrightness:Mh,setStorage:bd,setStorageSync:ta,setTabBarBadge:Bf,setTabBarItem:Nf,setTabBarStyle:xf,setTopWindowStyle:Zf,showActionSheet:Sf,showLeftWindow:Yf,showLoading:vf,showModal:hf,showNavigationBarLoading:Vf,showRightWindow:Jf,showTabBar:Pf,showTabBarRedDot:Rf,showToast:mf,showTopWindow:Gf,startAccelerometer:Xr,startCompass:Zr,startGyroscope:Ah,startLocationUpdate:tf,startPullDownRefresh:kf,stopAccelerometer:Jr,stopCompass:ea,stopGyroscope:kh,stopLocationUpdate:nf,stopPullDownRefresh:If,switchTab:wc,uploadFile:Hd,upx2px:_o,vibrateLong:hd,vibrateShort:fd},Symbol.toStringTag,{value:"Module"}),qh="MAP_LOCATION",DV=ge({name:"MapLocation",setup(){const e=Vue.reactive({latitude:0,longitude:0,rotate:0});{let t=function(c){e.rotate=c.direction},n=function(){Rn({type:"gcj02",success:c=>{e.latitude=c.latitude,e.longitude=c.longitude},complete:()=>{r=setTimeout(n,3e4)}})},o=function(){r&&clearTimeout(r),Qr(t)};const i=Vue.inject("onMapReady");let r;Kr(t),i(n),Vue.onUnmounted(o);const a=Vue.inject("addMapChidlContext"),s=Vue.inject("removeMapChidlContext"),l={id:qh,state:e};a(l),Vue.onUnmounted(()=>s(l))}return()=>e.latitude?Vue.createVNode(ed,Vue.mergeProps({anchor:{x:.5,y:.5},width:"44",height:"44",iconPath:Xu},e),null,16,["iconPath"]):null}}),UV=ge({name:"MapPolygon",props:{dashArray:{type:Array,default:()=>[0,0]},points:{type:Array,required:!0},strokeWidth:{type:Number,default:1},strokeColor:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},zIndex:{type:Number,default:0}},setup(e){let t;return Vue.inject("onMapReady")((o,i,r)=>{function a(){const{points:s,strokeWidth:l,strokeColor:c,dashArray:u,fillColor:d,zIndex:f}=e,h=s.map(w=>{const{latitude:A,longitude:x}=w;return ue()?[x,A]:oe()?new i.Point(x,A):new i.LatLng(A,x)}),{r:_,g:v,b:m,a:T}=it(d),{r:b,g,b:p,a:V}=it(c),S={clickable:!0,cursor:"crosshair",editable:!1,map:o,fillColor:"",path:h,strokeColor:"",strokeDashStyle:u.some(w=>w>0)?"dash":"solid",strokeWeight:l,visible:!0,zIndex:f};if(i.Color?(S.fillColor=new i.Color(_,v,m,T),S.strokeColor=new i.Color(b,g,p,V)):(S.fillColor=`rgb(${_}, ${v}, ${m})`,S.fillOpacity=T,S.strokeColor=`rgb(${b}, ${g}, ${p})`,S.strokeOpacity=V),t){t.setOptions(S);return}oe()?(t=new i.Polygon(S.path,S),o.addOverlay(t)):t=new i.Polygon(S)}a(),Vue.watch(e,a)}),Vue.onUnmounted(()=>{t.setMap(null)}),()=>null}}),FV={id:{type:String,default:""},latitude:{type:[String,Number],default:0},longitude:{type:[String,Number],default:0},scale:{type:[String,Number],default:16},markers:{type:Array,default(){return[]}},includePoints:{type:Array,default(){return[]}},polyline:{type:Array,default(){return[]}},circles:{type:Array,default(){return[]}},controls:{type:Array,default(){return[]}},showLocation:{type:[Boolean,String],default:!1},libraries:{type:Array,default(){return[]}},polygons:{type:Array,default:()=>[]}};function va(e){const t=[];return K(e)&&e.forEach(n=>{n&&n.latitude&&n.longitude&&t.push({latitude:n.latitude,longitude:n.longitude})}),t}function $V(e,t,n){return new e.LngLat(n,t)}function zV(e,t,n){return new e.Point(n,t)}function WV(e,t,n){return new e.LatLng(t,n)}function li(e,t,n){return oe()?zV(e,t,n):ue()?$V(e,t,n):WV(e,t,n)}function $n(e){return"getLat"in e?e.getLat():oe()?e.lat:e.lat()}function zn(e){return"getLng"in e?e.getLng():oe()?e.lng:e.lng()}function HV(e,t,n){const o=ve(t,n),i=Vue.ref(null);let r,a;const s=Vue.reactive({latitude:Number(e.latitude),longitude:Number(e.longitude),includePoints:va(e.includePoints)}),l=[];let c;function u(w){c?w(a,r,o):l.push(w)}function d(){c=!0,l.forEach(w=>w(a,r,o)),l.length=0}let f;const h=[];function _(w){f?w():l.push(w)}const v={};function m(w){v[w.id]=w}function T(w){delete v[w.id]}Vue.watch([()=>e.latitude,()=>e.longitude],([w,A])=>{const x=Number(w),E=Number(A);if((x!==s.latitude||E!==s.longitude)&&(s.latitude=x,s.longitude=E,a)){const C=li(r,s.latitude,s.longitude);a.setCenter(C)}}),Vue.watch(()=>e.includePoints,w=>{s.includePoints=va(w),f&&V()},{deep:!0});function b(){f=!0,h.forEach(w=>w()),h.length=0}function g(){const w=a.getCenter();return{scale:a.getZoom(),centerLocation:{latitude:$n(w),longitude:zn(w)}}}function p(){const w=li(r,s.latitude,s.longitude);a.setCenter(w)}function V(){if(ue()){const w=[];s.includePoints.forEach(x=>{w.push([x.longitude,x.latitude])});const A=new r.Bounds(...w);a.setBounds(A)}else if(!oe()){const w=new r.LatLngBounds;s.includePoints.forEach(({latitude:A,longitude:x})=>{const E=new r.LatLng(A,x);w.extend(E)}),a.fitBounds(w)}}function S(){const w=i.value,A=li(r,s.latitude,s.longitude),x=r.event||r.Event,E=new r.Map(w,{center:A,zoom:Number(e.scale),disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,panControl:!1,fullscreenControl:!1,streetViewControl:!1,keyboardShortcuts:!1,minZoom:5,maxZoom:18,draggable:!0});if(oe()&&(E.centerAndZoom(A,Number(e.scale)),E.enableScrollWheelZoom(),E._printLog&&E._printLog("uniapp")),Vue.watch(()=>e.scale,C=>{E.setZoom(Number(C)||16)}),_(()=>{s.includePoints.length&&(V(),p())}),oe())E.addEventListener("click",()=>{o("tap",{},{}),o("click",{},{})}),E.addEventListener("dragstart",()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})}),E.addEventListener("dragend",()=>{o("regionchange",{},B({type:"end",causedBy:"drag"},g()))});else{const C=x.addListener(E,"bounds_changed",()=>{C.remove(),b()});x.addListener(E,"click",()=>{o("tap",{},{}),o("click",{},{})}),x.addListener(E,"dragstart",()=>{o("regionchange",{},{type:"begin",causedBy:"gesture"})}),x.addListener(E,"dragend",()=>{o("regionchange",{},B({type:"end",causedBy:"drag"},g()))});const I=()=>{n("update:scale",E.getZoom()),o("regionchange",{},B({type:"end",causedBy:"scale"},g()))};x.addListener(E,"zoom_changed",I),x.addListener(E,"zoomend",I),x.addListener(E,"center_changed",()=>{const M=E.getCenter(),F=$n(M),k=zn(M);n("update:latitude",F),n("update:longitude",k)})}return E}try{const w=Xo();qt((A,x={})=>{switch(A){case"getCenterLocation":u(()=>{const E=a.getCenter();ye(x,{latitude:$n(E),longitude:zn(E),errMsg:`${A}:ok`})});break;case"moveToLocation":{let E=Number(x.latitude),C=Number(x.longitude);if(!E||!C){const I=v[qh];I&&(E=I.state.latitude,C=I.state.longitude)}if(E&&C){if(s.latitude=E,s.longitude=C,a){const I=li(r,E,C);a.setCenter(I)}u(()=>{ye(x,`${A}:ok`)})}else ye(x,`${A}:fail`)}break;case"translateMarker":u(()=>{const E=v[x.markerId];if(E){try{E.translate(x)}catch(C){ye(x,`${A}:fail ${C.message}`)}ye(x,`${A}:ok`)}else ye(x,`${A}:fail not found`)});break;case"includePoints":s.includePoints=va(x.includePoints),(f||ue())&&V(),_(()=>{ye(x,`${A}:ok`)});break;case"getRegion":_(()=>{const E=a.getBounds(),C=E.getSouthWest(),I=E.getNorthEast();ye(x,{southwest:{latitude:$n(C),longitude:zn(C)},northeast:{latitude:$n(I),longitude:zn(I)},errMsg:`${A}:ok`})});break;case"getScale":u(()=>{ye(x,{scale:a.getZoom(),errMsg:`${A}:ok`})});break}},w,!0)}catch(w){}return Vue.onMounted(()=>{Fr(e.libraries,w=>{r=w,a=S(),d(),o("updated",{},{})})}),Vue.provide("onMapReady",u),Vue.provide("addMapChidlContext",m),Vue.provide("removeMapChidlContext",T),{state:s,mapRef:i,trigger:o}}const _a=X({name:"Map",props:FV,emits:["markertap","labeltap","callouttap","controltap","regionchange","tap","click","updated","update:scale","update:latitude","update:longitude"],setup(e,{emit:t,slots:n}){const o=Vue.ref(null),{mapRef:i,trigger:r}=HV(e,o,t);return()=>Vue.createVNode("uni-map",{ref:o,id:e.id},[Vue.createVNode("div",{ref:i,style:"width: 100%; height: 100%; position: relative; overflow: hidden"},null,512),e.markers.map(a=>Vue.createVNode(ed,Vue.mergeProps({key:a.id},a),null,16)),e.polyline.map(a=>Vue.createVNode(KS,a,null,16)),e.circles.map(a=>Vue.createVNode(QS,a,null,16)),e.controls.map(a=>Vue.createVNode(ZS,Vue.mergeProps(a,{trigger:r}),null,16,["trigger"])),e.showLocation&&Vue.createVNode(DV,null,null),e.polygons.map(a=>Vue.createVNode(UV,a,null,16)),Vue.createVNode("div",{style:"position: absolute;top: 0;width: 100%;height: 100%;overflow: hidden;pointer-events: none;"},[n.default&&n.default()])],8,["id"])}}),qV=X({name:"CoverView",compatConfig:{MODE:3},props:{scrollTop:{type:[String,Number],default:0}},setup(e,{slots:t}){const n=Vue.ref(null),o=Vue.ref(null);Vue.watch(()=>e.scrollTop,a=>{i(a)});function i(a){let s=o.value;getComputedStyle(s).overflowY==="scroll"&&(s.scrollTop=r(a))}function r(a){let s=String(a);return/\d+[ur]px$/i.test(s)&&s.replace(/\d+[ur]px$/i,l=>String(uni.upx2px(parseFloat(l)))),parseFloat(s)||0}return Vue.onMounted(()=>{i(e.scrollTop)}),()=>Vue.createVNode("uni-cover-view",{"scroll-top":e.scrollTop,ref:n},[Vue.createVNode("div",{ref:o,class:"uni-cover-view"},[t.default&&t.default()],512)],8,["scroll-top"])}}),GV=X({name:"CoverImage",compatConfig:{MODE:3},props:{src:{type:String,default:""}},emits:["load","error"],setup(e,{emit:t}){const n=Vue.ref(null),o=ve(n,t);function i(a){o("load",a)}function r(a){o("error",a)}return()=>{const{src:a}=e;return Vue.createVNode("uni-cover-image",{ref:n,src:a},[Vue.createVNode("div",{class:"uni-cover-image"},[a?Vue.createVNode("img",{src:Q(a),onLoad:i,onError:r},null,40,["src","onLoad","onError"]):null])],8,["src"])}}});function Gh(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Vue.isVNode(e)}function jh(e){if(e.mode===Y.TIME)return"00:00";if(e.mode===Y.DATE){const t=new Date().getFullYear()-150;switch(e.fields){case xe.YEAR:return t.toString();case xe.MONTH:return t+"-01";default:return t+"-01-01"}}return""}function Yh(e){if(e.mode===Y.TIME)return"23:59";if(e.mode===Y.DATE){const t=new Date().getFullYear()+150;switch(e.fields){case xe.YEAR:return t.toString();case xe.MONTH:return t+"-12";default:return t+"-12-31"}}return""}function Wn(e,t,n,o){const i=e.mode===Y.DATE?"-":":",r=e.mode===Y.DATE?t.dateArray:t.timeArray;let a;if(e.mode===Y.TIME)a=2;else switch(e.fields){case xe.YEAR:a=1;break;case xe.MONTH:a=2;break;default:a=3;break}const s=String(n).split(i);let l=[];for(let c=0;c<a;c++){const u=s[c];l.push(r[c].indexOf(u))}return l.indexOf(-1)>=0&&(l=o?Wn(e,t,o):l.map(()=>0)),l}const Y={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},xe={YEAR:"year",MONTH:"month",DAY:"day"},ci={PICKER:"picker",SELECT:"select"},jV={name:{type:String,default:""},range:{type:Array,default(){return[]}},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Y.SELECTOR,validator(e){return Object.values(Y).includes(e)}},fields:{type:String,default:""},start:{type:String,default:e=>jh(e)},end:{type:String,default:e=>Yh(e)},disabled:{type:[Boolean,String],default:!1},selectorType:{type:String,default:""}},YV=X({name:"Picker",compatConfig:{MODE:3},props:jV,emits:["change","cancel","columnchange"],setup(e,{emit:t,slots:n}){Tp();const{t:o}=D(),i=Vue.ref(null),r=Vue.ref(null),a=Vue.ref(null),s=Vue.ref(null),l=Vue.ref(!1),{state:c,rangeArray:u}=XV(e),d=ve(i,t),{system:f,selectorTypeComputed:h,_show:_,_l10nColumn:v,_l10nItem:m,_input:T,_fixInputPosition:b,_pickerViewChange:g,_cancel:p,_change:V,_resetFormData:S,_getFormData:w,_createTime:A,_createDate:x,_setValueSync:E}=eC(e,c,d,i,r,a,s);tC(c,p,V),nC(S,w),A(),x(),E();const C=bf(c);return Vue.watchEffect(()=>{c.isDesktop=C.isDesktop.value,c.popupStyle=C.popupStyle.value}),Vue.onBeforeUnmount(()=>{r.value&&r.value.remove()}),Vue.onMounted(()=>{l.value=!0}),()=>{let I;const{visible:M,contentVisible:F,valueArray:k,popupStyle:L,valueSync:z}=c,{rangeKey:N,mode:P,start:O,end:$}=e,H=ht(e,"disabled");return Vue.createVNode("uni-picker",Vue.mergeProps({ref:i},H,{onClick:ne(_)}),[l.value?Vue.createVNode("div",{ref:r,class:["uni-picker-container",`uni-${P}-${h.value}`],onWheel:Xe,onTouchmove:Xe},[Vue.createVNode(Vue.Transition,{name:"uni-fade"},{default:()=>[Vue.withDirectives(Vue.createVNode("div",{class:"uni-mask uni-picker-mask",onClick:ne(p),onMousemove:b},null,40,["onClick","onMousemove"]),[[Vue.vShow,M]])]}),f.value?null:Vue.createVNode("div",{class:[{"uni-picker-toggle":M},"uni-picker-custom"],style:L.content},[Vue.createVNode("div",{class:"uni-picker-header",onClick:cn},[Vue.createVNode("div",{class:"uni-picker-action uni-picker-action-cancel",onClick:ne(p)},[o("uni.picker.cancel")],8,["onClick"]),Vue.createVNode("div",{class:"uni-picker-action uni-picker-action-confirm",onClick:V},[o("uni.picker.done")],8,["onClick"])],8,["onClick"]),F?Vue.createVNode(Eu,{value:v(k),class:"uni-picker-content",onChange:g},Gh(I=Vue.renderList(v(u.value),(q,U)=>{let re;return Vue.createVNode(Nu,{key:U},Gh(re=Vue.renderList(q,(ae,Me)=>Vue.createVNode("div",{key:Me,class:"uni-picker-item"},[typeof ae=="object"?ae[N]||"":m(ae,U)])))?re:{default:()=>[re],_:1})}))?I:{default:()=>[I],_:1},8,["value","onChange"]):null,Vue.createVNode("div",{ref:a,class:"uni-picker-select",onWheel:cn,onTouchmove:cn},[Vue.renderList(u.value[0],(q,U)=>Vue.createVNode("div",{key:U,class:["uni-picker-item",{selected:k[0]===U}],onClick:()=>{k[0]=U,V()}},[typeof q=="object"?q[N]||"":q],10,["onClick"]))],40,["onWheel","onTouchmove"]),Vue.createVNode("div",{style:L.triangle},null,4)],6)],40,["onWheel","onTouchmove"]):null,Vue.createVNode("div",null,[n.default&&n.default()]),f.value?Vue.createVNode("div",{class:"uni-picker-system",onMousemove:ne(b)},[Vue.createVNode("input",{class:["uni-picker-system_input",f.value],ref:s,value:z,type:P,tabindex:"-1",min:O,max:$,onChange:q=>{T(q),cn(q)}},null,42,["value","type","min","max","onChange"])],40,["onMousemove"]):null],16,["onClick"])}}});function XV(e){const t=Vue.reactive({valueSync:void 0,visible:!1,contentVisible:!1,popover:null,valueChangeSource:"",timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],isDesktop:!1,popupStyle:{content:{},triangle:{}}}),n=Vue.computed(()=>{let o=e.range;switch(e.mode){case Y.SELECTOR:return[o];case Y.MULTISELECTOR:return o;case Y.TIME:return t.timeArray;case Y.DATE:{const i=t.dateArray;switch(e.fields){case xe.YEAR:return[i[0]];case xe.MONTH:return[i[0],i[1]];default:return[i[0],i[1],i[2]]}}}return[]});return{state:t,rangeArray:n}}const JV=()=>String(navigator.vendor).indexOf("Apple")===0&&navigator.maxTouchPoints>0;function KV(){const e=Vue.ref(!1);return e.value=JV(),e}const QV=()=>{if(/win|mac/i.test(navigator.platform)){if(navigator.vendor==="Google Inc.")return"chrome";if(/Firefox/.test(navigator.userAgent))return"firefox"}return""};function ZV(){const e=Vue.ref("");return e.value=QV(),e}let Xh;function eC(e,t,n,o,i,r,a){const s=KV(),l=ZV(),c=Vue.computed(()=>{const N=e.selectorType;return Object.values(ci).includes(N)?N:s.value?ci.PICKER:ci.SELECT}),u=Vue.computed(()=>e.mode===Y.DATE&&!Object.values(xe).includes(e.fields)&&t.isDesktop?l.value:""),d=Vue.computed(()=>Wn(e,t,e.start,jh(e))),f=Vue.computed(()=>Wn(e,t,e.end,Yh(e)));function h(N){if(e.disabled)return;t.valueChangeSource="";let P=i.value,O=N.currentTarget;P.remove(),(document.querySelector("uni-app")||document.body).appendChild(P),P.style.display="block";const $=O.getBoundingClientRect();t.popover={top:$.top,left:$.left,width:$.width,height:$.height},setTimeout(()=>{t.visible=!0},20)}function _(){return{value:t.valueSync,key:e.name}}function v(){switch(e.mode){case Y.SELECTOR:t.valueSync=0;break;case Y.MULTISELECTOR:t.valueSync=e.value.map(N=>0);break;case Y.DATE:case Y.TIME:t.valueSync="";break}}function m(){let N=[],P=[];for(let O=0;O<24;O++)N.push((O<10?"0":"")+O);for(let O=0;O<60;O++)P.push((O<10?"0":"")+O);t.timeArray.push(N,P)}function T(){let N=new Date().getFullYear(),P=N-150,O=N+150;if(e.start){const $=new Date(e.start).getFullYear();!isNaN($)&&$<P&&(P=$)}if(e.end){const $=new Date(e.end).getFullYear();!isNaN($)&&$>O&&(O=$)}return{start:P,end:O}}function b(){let N=[];const P=T();for(let H=P.start,q=P.end;H<=q;H++)N.push(String(H));let O=[];for(let H=1;H<=12;H++)O.push((H<10?"0":"")+H);let $=[];for(let H=1;H<=31;H++)$.push((H<10?"0":"")+H);t.dateArray.push(N,O,$)}function g(N){return N[0]*60+N[1]}function p(N){return N[0]*31*12+(N[1]||0)*31+(N[2]||0)}function V(N,P){for(let O=0;O<N.length&&O<P.length;O++)N[O]=P[O]}function S(){let N=e.value;switch(e.mode){case Y.MULTISELECTOR:{K(N)||(N=t.valueArray),K(t.valueSync)||(t.valueSync=[]);const P=t.valueSync.length=Math.max(N.length,e.range.length);for(let O=0;O<P;O++){const $=Number(N[O]),H=Number(t.valueSync[O]),q=isNaN($)?isNaN(H)?0:H:$,U=e.range[O]?e.range[O].length-1:0;t.valueSync.splice(O,1,q<0||q>U?0:q)}}break;case Y.TIME:case Y.DATE:t.valueSync=String(N);break;default:{const P=Number(N);t.valueSync=P<0?0:P;break}}}function w(){let N=t.valueSync,P;switch(e.mode){case Y.MULTISELECTOR:P=[...N];break;case Y.TIME:P=Wn(e,t,N,Ba({mode:Y.TIME}));break;case Y.DATE:P=Wn(e,t,N,Ba({mode:Y.DATE}));break;default:P=[N];break}t.oldValueArray=[...P],t.valueArray=[...P]}function A(){let N=t.valueArray;switch(e.mode){case Y.SELECTOR:return N[0];case Y.MULTISELECTOR:return N.map(P=>P);case Y.TIME:return t.valueArray.map((P,O)=>t.timeArray[O][P]).join(":");case Y.DATE:return t.valueArray.map((P,O)=>t.dateArray[O][P]).join("-")}}function x(){C(),t.valueChangeSource="click";const N=A();t.valueSync=K(N)?N.map(P=>P):N,n("change",{},{value:N})}function E(N){if(u.value==="firefox"&&N){const{top:P,left:O,width:$,height:H}=t.popover,{pageX:q,pageY:U}=N;if(q>O&&q<O+$&&U>P&&U<P+H)return}C(),n("cancel",{},{})}function C(){t.visible=!1,setTimeout(()=>{let N=i.value;N.remove(),o.value.prepend(N),N.style.display="none"},260)}function I(){e.mode===Y.SELECTOR&&c.value===ci.SELECT&&(r.value.scrollTop=t.valueArray[0]*34)}function M(N){const P=N.target;t.valueSync=P.value,Vue.nextTick(()=>{x()})}function F(N){if(u.value==="chrome"){const P=o.value.getBoundingClientRect(),O=32;a.value.style.left=`${N.clientX-P.left-O*1.5}px`,a.value.style.top=`${N.clientY-P.top-O*.5}px`}}function k(N){t.valueArray=L(N.detail.value,!0)}function L(N,P){const{getLocale:O}=D();if(e.mode===Y.DATE){const $=O();if(!$.startsWith("zh"))switch(e.fields){case xe.YEAR:return N;case xe.MONTH:return[N[1],N[0]];default:switch($){case"es":case"fr":return[N[2],N[1],N[0]];default:return P?[N[2],N[0],N[1]]:[N[1],N[2],N[0]]}}}return N}function z(N,P){const{getLocale:O}=D();if(e.mode===Y.DATE){const $=O();if($.startsWith("zh"))return N+["年","月","日"][P];if(e.fields!==xe.YEAR&&P===(e.fields!==xe.MONTH&&($==="es"||$==="fr")?1:0)){let H;switch($){case"es":H=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":H=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:H=["January","February","March","April","May","June","July","August","September","October","November","December"];break}return H[Number(N)-1]}}return N}return Vue.watch(()=>t.visible,N=>{N?(clearTimeout(Xh),t.contentVisible=N,I()):Xh=setTimeout(()=>{t.contentVisible=N},300)}),Vue.watch([()=>e.mode,()=>e.value,()=>e.range],S,{deep:!0}),Vue.watch(()=>t.valueSync,w,{deep:!0}),Vue.watch(()=>t.valueArray,N=>{if(e.mode===Y.TIME||e.mode===Y.DATE){const P=e.mode===Y.TIME?g:p,O=t.valueArray,$=d.value,H=f.value;if(e.mode===Y.DATE){const q=t.dateArray,U=q[2].length,re=Number(q[2][O[2]])||1,ae=new Date(`${q[0][O[0]]}/${q[1][O[1]]}/${re}`).getDate();ae<re&&(O[2]-=ae+U-re)}P(O)<P($)?V(O,$):P(O)>P(H)&&V(O,H)}N.forEach((P,O)=>{P!==t.oldValueArray[O]&&(t.oldValueArray[O]=P,e.mode===Y.MULTISELECTOR&&n("columnchange",{},{column:O,value:P}))})}),{selectorTypeComputed:c,system:u,_show:h,_cancel:E,_change:x,_l10nColumn:L,_l10nItem:z,_input:M,_resetFormData:v,_getFormData:_,_createTime:m,_createDate:b,_setValueSync:S,_fixInputPosition:F,_pickerViewChange:k}}function tC(e,t,n){const{key:o,disable:i}=aa();Vue.watchEffect(()=>{i.value=!e.visible}),Vue.watch(o,r=>{r==="esc"?t():r==="enter"&&n()})}function nC(e,t){const n=Vue.inject(Fe,!1);if(n){const o={reset:e,submit:()=>{const i=["",null],{key:r,value:a}=t();return r!==""&&(i[0]=r,i[1]=a),i}};n.addField(o),Vue.onBeforeUnmount(()=>{n.removeField(o)})}}const oC=Mt("ad"),iC=Mt("ad-content-page"),rC=Mt("ad-draw"),aC=Mt("camera"),sC=Mt("live-player"),lC=Mt("live-pusher"),cC=B(Op,{publishHandler(e,t,n){UniServiceJSBridge.subscribeHandler(e,t,n)}}),uC=BV,dC=B(Cm,{publishHandler(e,t,n){UniViewJSBridge.subscribeHandler(e,t,n)}});function fC(e){const{r:t,g:n,b:o}=it(e);return`rgba(${t},${n},${o},0)`}function hC(e,{id:t,navigationBar:{titleColor:n,coverage:o,backgroundColor:i}}){let r=0;const a=Vue.computed(()=>it(i)),s=parseInt(o);let l,c;const u=[],d=[],f=[];Vue.onMounted(()=>{const h=e.value;c=h.style,l=h.querySelector(".uni-page-head__title");const _=h.querySelectorAll(".uni-page-head-btn"),v=h.querySelectorAll("svg path");for(let m=0;m<v.length;m++)u.push(v[m]);for(let m=0;m<_.length;m++){const T=_[m];f.push(getComputedStyle(T).backgroundColor),d.push(T.style)}}),eS(t+".onPageScroll",({scrollTop:h})=>{const _=Math.min(h/s,1);if(_===1&&r===1)return;_>.5&&r<=.5?u.forEach(function(m){m.setAttribute("fill",n)}):_<=.5&&r>.5&&u.forEach(function(m){m.setAttribute("fill","#fff")}),r=_,l&&(l.style.opacity=_);const v=a.value;c.backgroundColor=`rgba(${v.r},${v.g},${v.b},${_})`,d.forEach(function(m,T){const g=f[T].match(/[\d+\.]+/g);g[3]=(1-_)*(g.length===4?g[3]:1),m.backgroundColor=`rgba(${g})`})})}const Jh={none:"",forward:"M11 7.844q-0.25-0.219-0.25-0.578t0.25-0.578q0.219-0.25 0.563-0.25t0.563 0.25l9.656 9.125q0.125 0.125 0.188 0.297t0.063 0.328q0 0.188-0.063 0.359t-0.188 0.297l-9.656 9.125q-0.219 0.25-0.563 0.25t-0.563-0.25q-0.25-0.219-0.25-0.578t0.25-0.609l9.063-8.594-9.063-8.594z",back:hn,select:hn,share:"M26.563 24.844q0 0.125-0.109 0.234t-0.234 0.109h-17.938q-0.125 0-0.219-0.109t-0.094-0.234v-13.25q0-0.156 0.094-0.25t0.219-0.094h5.5v-1.531h-6q-0.531 0-0.906 0.391t-0.375 0.922v14.375q0 0.531 0.375 0.922t0.906 0.391h18.969q0.531 0 0.891-0.391t0.359-0.953v-5.156h-1.438v4.625zM29.813 10.969l-5.125-5.375-1.031 1.094 3.438 3.594-3.719 0.031q-2.313 0.188-4.344 1.125t-3.578 2.422-2.5 3.453-1.109 4.188l-0.031 0.25h1.469v-0.219q0.156-1.875 1-3.594t2.25-3.063 3.234-2.125 3.828-0.906l0.188-0.031 3.313-0.031-3.438 3.625 1.031 1.063 5.125-5.375-0.031-0.063 0.031-0.063z",favorite:"M27.594 13.375q-0.063-0.188-0.219-0.313t-0.344-0.156l-7.094-0.969-3.219-6.406q-0.094-0.188-0.25-0.281t-0.375-0.094q-0.188 0-0.344 0.094t-0.25 0.281l-3.125 6.438-7.094 1.094q-0.188 0.031-0.344 0.156t-0.219 0.313q-0.031 0.188 0.016 0.375t0.172 0.313l5.156 4.969-1.156 7.063q-0.031 0.188 0.047 0.375t0.234 0.313q0.094 0.063 0.188 0.094t0.219 0.031q0.063 0 0.141-0.031t0.172-0.063l6.313-3.375 6.375 3.313q0.063 0.031 0.141 0.047t0.172 0.016q0.188 0 0.344-0.094t0.25-0.281q0.063-0.094 0.078-0.234t-0.016-0.234q0-0.031 0-0.063l-1.25-6.938 5.094-5.031q0.156-0.156 0.203-0.344t-0.016-0.375zM11.469 19.063q0.031-0.188-0.016-0.344t-0.172-0.281l-4.406-4.25 6.063-0.906q0.156-0.031 0.297-0.125t0.203-0.25l2.688-5.531 2.75 5.5q0.063 0.156 0.203 0.25t0.297 0.125l6.094 0.844-4.375 4.281q-0.125 0.125-0.172 0.297t-0.016 0.328l1.063 6.031-5.438-2.813q-0.156-0.094-0.328-0.078t-0.297 0.078l-5.438 2.875 1-6.031z",home:"M23.719 16.5q-0.313 0-0.531 0.219t-0.219 0.5v7.063q0 0.219-0.172 0.391t-0.391 0.172h-12.344q-0.25 0-0.422-0.172t-0.172-0.391v-7.063q0-0.281-0.219-0.5t-0.531-0.219q-0.281 0-0.516 0.219t-0.234 0.5v7.063q0.031 0.844 0.625 1.453t1.438 0.609h12.375q0.844 0 1.453-0.609t0.609-1.453v-7.063q0-0.125-0.063-0.266t-0.156-0.234q-0.094-0.125-0.234-0.172t-0.297-0.047zM26.5 14.875l-8.813-8.813q-0.313-0.313-0.688-0.453t-0.781-0.141-0.781 0.141-0.656 0.422l-8.813 8.844q-0.188 0.219-0.188 0.516t0.219 0.484q0.094 0.125 0.234 0.172t0.297 0.047q0.125 0 0.25-0.047t0.25-0.141l8.781-8.781q0.156-0.156 0.406-0.156t0.406 0.156l8.813 8.781q0.219 0.188 0.516 0.188t0.516-0.219q0.188-0.188 0.203-0.484t-0.172-0.516z",menu:"M8.938 18.313q0.875 0 1.484-0.609t0.609-1.453-0.609-1.453-1.484-0.609q-0.844 0-1.453 0.609t-0.609 1.453 0.609 1.453 1.453 0.609zM16.188 18.313q0.875 0 1.484-0.609t0.609-1.453-0.609-1.453-1.484-0.609q-0.844 0-1.453 0.609t-0.609 1.453 0.609 1.453 1.453 0.609zM23.469 18.313q0.844 0 1.453-0.609t0.609-1.453-0.609-1.453-1.453-0.609q-0.875 0-1.484 0.609t-0.609 1.453 0.609 1.453 1.484 0.609z",close:xi},gC=ge({name:"PageHead",setup(){const e=Vue.ref(null),t=Pt(),n=df(t.navigationBar,()=>{const s=Dn(t.navigationBar);n.backgroundColor=s.backgroundColor,n.titleColor=s.titleColor}),{clazz:o,style:i}=bC(n),r=__UNI_FEATURE_NAVIGATIONBAR_BUTTONS__&&wC(t),a=__UNI_FEATURE_NAVIGATIONBAR_SEARCHINPUT__&&n.searchInput&&TC(t);return __UNI_FEATURE_NAVIGATIONBAR_TRANSPARENT__&&n.type==="transparent"&&hC(e,t),()=>{const s=__UNI_FEATURE_PAGES__?pC(n,t.isQuit):null,l=__UNI_FEATURE_NAVIGATIONBAR_BUTTONS__?Kh(r.left):[],c=__UNI_FEATURE_NAVIGATIONBAR_BUTTONS__?Kh(r.right):[],u=n.type||"default",d=u!=="transparent"&&u!=="float"&&Vue.createVNode("div",{class:{"uni-placeholder":!0,"uni-placeholder-titlePenetrate":n.titlePenetrate}},null,2);return Vue.createVNode("uni-page-head",{"uni-page-head-type":u},[Vue.createVNode("div",{ref:e,class:o.value,style:i.value},[Vue.createVNode("div",{class:"uni-page-head-hd"},[s,...l]),mC(n,a),Vue.createVNode("div",{class:"uni-page-head-ft"},[...c])],6),d],8,["uni-page-head-type"])}}});function pC(e,t){if(!t)return Vue.createVNode("div",{class:"uni-page-head-btn",onClick:yC},[ce(hn,e.type==="transparent"?"#fff":e.titleColor,26)],8,["onClick"])}function Kh(e){return e.map(({onClick:t,btnClass:n,btnStyle:o,btnText:i,btnIconPath:r,badgeText:a,iconStyle:s,btnSelect:l},c)=>Vue.createVNode("div",{key:c,class:n,style:o,onClick:t,"badge-text":a},[r?ce(r,s.color,s.fontSize):l?Vue.createVNode("span",{style:s},[Vue.createVNode("i",{class:"uni-btn-icon",innerHTML:i},null,8,["innerHTML"]),ce(Jh.select,"#000",14)],4):Vue.createVNode("i",{class:"uni-btn-icon",style:s,innerHTML:i},null,12,["innerHTML"])],14,["onClick","badge-text"]))}function mC(e,t){return!__UNI_FEATURE_NAVIGATIONBAR_SEARCHINPUT__||!e.searchInput?vC(e):_C(e,t)}function vC({type:e,loading:t,titleSize:n,titleText:o,titleImage:i}){return Vue.createVNode("div",{class:"uni-page-head-bd"},[Vue.createVNode("div",{style:{fontSize:n,opacity:e==="transparent"?0:1},class:"uni-page-head__title"},[t?Vue.createVNode("i",{class:"uni-loading"},null):i?Vue.createVNode("img",{src:i,class:"uni-page-head__title_image"},null,8,["src"]):o],4)])}function _C(e,{text:t,focus:n,composing:o,onBlur:i,onFocus:r,onInput:a,onConfirm:s,onClick:l}){const{color:c,align:u,autoFocus:d,disabled:f,borderRadius:h,backgroundColor:_,placeholder:v,placeholderColor:m}=e.searchInput,T={borderRadius:h,backgroundColor:_},b=["uni-page-head-search-placeholder",`uni-page-head-search-placeholder-${n.value||t.value?"left":u}`];return Vue.createVNode("div",{class:"uni-page-head-search",style:T},[Vue.createVNode("div",{style:{color:m},class:b},[Vue.createVNode("div",{class:"uni-page-head-search-icon"},[ce(gs,m,20)]),t.value||o.value?"":v],6),f?Vue.createVNode(Ho,{disabled:!0,style:{color:c},"placeholder-style":"color: "+m,class:"uni-page-head-search-input","confirm-type":"search",onClick:l},null,8,["style","placeholder-style","onClick"]):Vue.createVNode(Ho,{focus:d,style:{color:c},"placeholder-style":"color: "+m,class:"uni-page-head-search-input","confirm-type":"search",onFocus:r,onBlur:i,onInput:a,onConfirm:s},null,8,["focus","style","placeholder-style","onFocus","onBlur","onInput","onConfirm"])],4)}function yC(){getCurrentPages().length===1?uni.reLaunch({url:"/"}):uni.navigateBack({from:"backbutton",success(){}})}function bC(e){const t=Vue.computed(()=>{const{type:o,titlePenetrate:i,shadowColorType:r}=e,a={"uni-page-head":!0,"uni-page-head-transparent":o==="transparent","uni-page-head-titlePenetrate":i==="YES","uni-page-head-shadow":!!r};return r&&(a[`uni-page-head-shadow-${r}`]=!0),a}),n=Vue.computed(()=>({backgroundColor:__UNI_FEATURE_NAVIGATIONBAR_TRANSPARENT__&&e.type==="transparent"?fC(e.backgroundColor):e.backgroundColor,color:e.titleColor,transitionDuration:e.duration,transitionTimingFunction:e.timingFunc}));return{clazz:t,style:n}}function wC({id:e,navigationBar:t}){const n=[],o=[],{buttons:i}=t;if(K(i)){const{type:r}=t,a=r==="transparent",s=Object.create(null);i.forEach((l,c)=>{if(l.fontSrc&&!l.fontFamily){const d=Q(l.fontSrc);let f=s[d];f||(f=`font${Date.now()}`,s[d]=f,Vue.onBeforeMount(()=>jp("uni-btn-"+f,`@font-face{font-family: "${f}";src: url("${d}") format("truetype")}`))),l.fontFamily=f}const u=SC(e,c,l,a);l.float==="left"?n.push(u):o.push(u)})}return{left:n,right:o}}function SC(e,t,n,o){const i={color:n.color,fontSize:n.fontSize,fontWeight:n.fontWeight};return n.fontFamily&&(i.fontFamily=n.fontFamily),new Proxy({btnClass:{"uni-page-head-btn":!0,"uni-page-head-btn-red-dot":!!(n.redDot||n.badgeText),"uni-page-head-btn-select":!!n.select},btnStyle:{backgroundColor:o?n.background:"transparent",width:n.width},btnText:"",btnIconPath:Jh[n.type],badgeText:n.badgeText,iconStyle:i,onClick(){ie(e,Ea,B({index:t},n))},btnSelect:n.select},{get(r,a,s){return["btnText"].includes(a)?n.fontSrc&&n.fontFamily?n.text.replace("\\u","&#x"):n.text:Reflect.get(r,a,s)}})}function TC({id:e,navigationBar:{searchInput:t}}){const n=Vue.ref(!1),o=Vue.ref(""),i=Vue.ref(!1),{disabled:r}=t;return r?{focus:n,text:o,composing:i,onClick:()=>{ie(e,Ia)}}:{focus:n,text:o,composing:i,onFocus:()=>{n.value=!0,ie(e,_i,{focus:!0})},onBlur:()=>{n.value=!1,ie(e,_i,{focus:!1})},onInput:u=>{o.value=u.detail.value,ie(e,Na,{text:o.value})},onConfirm:u=>{ie(e,xa,{text:o.value})}}}const VC={name:"PageRefresh",setup(){const{pullToRefresh:e}=Pt();return{offset:e.offset,color:e.color}}},CC=(e,t)=>{const n=e.__vccOpts||e;for(const[o,i]of t)n[o]=i;return n},AC={class:"uni-page-refresh-inner"},EC=["fill"],kC=[Vue.createElementVNode("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null,-1),Vue.createElementVNode("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1)],IC={class:"uni-page-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},NC=["stroke"];function xC(e,t,n,o,i,r){return Vue.openBlock(),Vue.createElementBlock("uni-page-refresh",null,[Vue.createElementVNode("div",{style:Vue.normalizeStyle({"margin-top":o.offset+"px"}),class:"uni-page-refresh"},[Vue.createElementVNode("div",AC,[(Vue.openBlock(),Vue.createElementBlock("svg",{fill:o.color,class:"uni-page-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},kC,8,EC)),(Vue.openBlock(),Vue.createElementBlock("svg",IC,[Vue.createElementVNode("circle",{stroke:o.color,class:"uni-page-refresh__path",cx:"50",cy:"50",r:"20",fill:"none","stroke-width":"4","stroke-miterlimit":"10"},null,8,NC)]))])],4)])}const MC=CC(VC,[["render",xC]]);function Qh(e,t,n){const o=Array.prototype.slice.call(e.changedTouches).filter(i=>i.identifier===t)[0];return o?(e.deltaY=o.pageY-n,!0):!1}const ui="pulling",di="reached",Zh="aborting",fi="refreshing",eg="restoring";function PC(e){const t=Pt(),{id:n,pullToRefresh:o}=t,{range:i,height:r}=o;let a,s,l,c;qt(()=>{t.enablePullDownRefresh&&(_||(_=fi,b(),setTimeout(()=>{x()},50)))},nr,!1,n),qt(()=>{t.enablePullDownRefresh&&_===fi&&(g(),_=eg,b(),E(()=>{g(),_=v=m=null}))},or,!1,n);function u(){a=e.value.$el,s=a.querySelector(".uni-page-refresh"),l=s.style,c=s.querySelector(".uni-page-refresh-inner").style}Vue.onMounted(()=>{u()});let d,f,h,_,v=null,m=null;function T(C){_&&a&&a.classList[C]("uni-page-refresh--"+_)}function b(){T("add")}function g(){T("remove")}function p(C){if(!s)return;let I=C/i;I>1?I=1:I=I*I*I;const M=Math.round(C/(i/r))||0;c.transform="rotate("+360*I+"deg)",l.clip="rect("+(45-M)+"px,45px,45px,-5px)",l.transform="translate3d(-50%, "+M+"px, 0)"}const V=ne(C=>{if(!t.enablePullDownRefresh)return;const I=C.changedTouches[0];d=I.identifier,f=I.pageY,[Zh,fi,eg].indexOf(_)>=0?h=!1:h=!0}),S=ne(C=>{if(!t.enablePullDownRefresh||!h||!Qh(C,d,f))return;let{deltaY:I}=C;if((document.documentElement.scrollTop||document.body.scrollTop)!==0){d=null;return}if(I<0&&!_)return;C.cancelable&&C.preventDefault(),v===null&&(m=I,_=ui,b()),I=I-m,I<0&&(I=0),v=I;const M=I>=i&&_!==di,F=I<i&&_!==ui;(M||F)&&(g(),_=_===di?ui:di,b()),p(I)}),w=ne(C=>{t.enablePullDownRefresh&&Qh(C,d,f)&&_!==null&&(_===ui?(g(),_=Zh,b(),A(()=>{g(),_=v=m=null})):_===di&&(g(),_=fi,b(),x()))});function A(C){if(s)if(l.transform){l.transition="-webkit-transform 0.3s",l.transform="translate3d(-50%, 0, 0)";const I=function(){M&&clearTimeout(M),s.removeEventListener("webkitTransitionEnd",I),l.transition="",C()};s.addEventListener("webkitTransitionEnd",I);const M=setTimeout(I,350)}else C()}function x(){s&&(l.transition="-webkit-transform 0.2s",l.transform="translate3d(-50%, "+r+"px, 0)",ie(n,Aa))}function E(C){if(!s)return;l.transition="-webkit-transform 0.3s",l.transform+=" scale(0.01)";const I=function(){M&&clearTimeout(M),s.removeEventListener("webkitTransitionEnd",I),l.transition="",l.transform="translate3d(-50%, 0, 0)",C()};s.addEventListener("webkitTransitionEnd",I);const M=setTimeout(I,350)}return{onTouchstartPassive:V,onTouchmove:S,onTouchend:w,onTouchcancel:w}}const OC=ge({name:"PageBody",setup(e,t){const n=__UNI_FEATURE_PULL_DOWN_REFRESH__&&Pt(),o=__UNI_FEATURE_PULL_DOWN_REFRESH__&&Vue.ref(null),i=Vue.ref(null),r=__UNI_FEATURE_PULL_DOWN_REFRESH__&&n.enablePullDownRefresh?PC(o):null,a=Vue.ref(null);return Vue.watch(()=>n.enablePullDownRefresh,()=>{a.value=n.enablePullDownRefresh?r:null},{immediate:!0}),()=>{const s=__UNI_FEATURE_PULL_DOWN_REFRESH__&&RC(o,n);return Vue.createVNode(Vue.Fragment,null,[s,Vue.createVNode("uni-page-wrapper",Vue.mergeProps({ref:i},a.value),[Vue.createVNode("uni-page-body",null,[Vue.renderSlot(t.slots,"default")]),null],16)])}}});function RC(e,t){return!__UNI_FEATURE_PULL_DOWN_REFRESH__||!t.enablePullDownRefresh?null:Vue.createVNode(MC,{ref:e},null,512)}const LC=ge({name:"Page",setup(e,t){let n=s0(Bi());const o=n.navigationBar,i={};return oV(n),Vue.getCurrentInstance(),()=>Vue.createVNode("uni-page",{"data-page":n.route,style:i},__UNI_FEATURE_NAVIGATIONBAR__&&o.style!=="custom"?[Vue.createVNode(gC),tg(t),null]:[tg(t),null])}});function tg(e){return Vue.openBlock(),Vue.createBlock(OC,{key:0},{default:Vue.withCtx(()=>[Vue.renderSlot(e.slots,"page")]),_:3})}function BC(e){e.use(Wu)}y.$emit=ul,y.$off=cl,y.$on=sl,y.$once=ll,y.Ad=oC,y.AdContentPage=iC,y.AdDraw=rC,y.AsyncErrorComponent=Qo,y.AsyncLoadingComponent=Ko,y.Button=r0,y.Camera=aC,y.Canvas=Jb,y.Checkbox=tw,y.CheckboxGroup=Zb,y.CoverImage=GV,y.CoverView=qV,y.Editor=Vw,y.Form=e0,y.Icon=Ew,y.Image=Nw,y.Input=Ho,y.Label=i0,y.LayoutComponent=qf,y.LivePlayer=sC,y.LivePusher=lC,y.Map=_a,y.MovableArea=vu,y.MovableView=Su,y.Navigator=u1,y.PageComponent=LC,y.Picker=YV,y.PickerView=Eu,y.PickerViewColumn=Nu,y.Progress=y1,y.Radio=T1,y.RadioGroup=w1,y.ResizeSensor=Ge,y.RichText=M1,y.ScrollView=Ru,y.Slider=L1,y.Swiper=Lu,y.SwiperItem=Bu,y.Switch=q1,y.Text=X1,y.Textarea=Q1,y.UniServiceJSBridge=dC,y.UniViewJSBridge=cC,y.Video=FS,y.View=Z1,y.WebView=WS,y.__f__=dl,y.addInterceptor=il,y.addPhoneContact=Fh,y.arrayBufferToBase64=Js,y.base64ToArrayBuffer=Xs,y.canIUse=Ac,y.canvasGetImageData=Sl,y.canvasPutImageData=Tl,y.canvasToTempFilePath=Vl,y.chooseFile=Pd,y.chooseImage=Od,y.chooseLocation=ef,y.chooseVideo=zd,y.clearStorage=Cd,y.clearStorageSync=oa,y.closePreviewImage=$d,y.closeSocket=Yd,y.connectSocket=qd,y.createAnimation=Ol,y.createCameraContext=uh,y.createCanvasContext=wl,y.createInnerAudioContext=td,y.createIntersectionObserver=kl,y.createLivePlayerContext=fh,y.createMapContext=hl,y.createMediaQueryObserver=Nl,y.createSelectorQuery=Ml,y.createVideoContext=fl,y.cssBackdropFilter=Po,y.cssConstant=Mo,y.cssEnv=xo,y.cssVar=gr,y.downloadFile=Wd,y.getApp=CS,y.getAppBaseInfo=qr,y.getClipboardData=pd,y.getCurrentPages=ub,y.getDeviceInfo=Hr,y.getElementById=ih,y.getEnterOptionsSync=tc,y.getFileInfo=Ed,y.getImageInfo=Nd,y.getLaunchOptionsSync=nc,y.getLeftWindowStyle=eh,y.getLocale=bo,y.getLocation=Rn,y.getNetworkType=Yr,y.getProvider=Hh,y.getPushClientId=ac,y.getRealPath=Q,y.getRecorderManager=sh,y.getRightWindowStyle=nh,y.getSavedFileInfo=_h,y.getSavedFileList=mh,y.getScreenBrightness=Oh,y.getSelectedTextRange=Hl,y.getStorage=Td,y.getStorageInfo=Ad,y.getStorageInfoSync=ia,y.getStorageSync=Sd,y.getSystemInfo=rd,y.getSystemInfoSync=Gr,y.getTabBarPageId=Eo,y.getTopWindowStyle=Qf,y.getVideoInfo=xd,y.getWindowInfo=zr,y.hideActionSheet=wf,y.hideKeyboard=Id,y.hideLeftWindow=Xf,y.hideLoading=yf,y.hideModal=ff,y.hideNavigationBarLoading=Cf,y.hideRightWindow=Kf,y.hideTabBar=Mf,y.hideTabBarRedDot=Of,y.hideToast=_f,y.hideTopWindow=jf,y.install=BC,y.interceptors=al,y.invokePushCallback=ic,y.loadFontFace=pa,y.login=zh,y.makePhoneCall=nd,y.navigateBack=lf,y.navigateTo=cf,y.offAccelerometerChange=ud,y.offAppHide=ec,y.offAppShow=Ql,y.offCompassChange=Qr,y.offError=Jl,y.offLocationChange=rf,y.offLocationChangeError=sf,y.offNetworkStatusChange=ld,y.offPageNotFound=Yl,y.offPushMessage=lc,y.offThemeChange=yd,y.offUnhandledRejection=Gl,y.offWindowResize=Dl,y.onAccelerometerChange=cd,y.onAppHide=Zl,y.onAppShow=Kl,y.onCompassChange=Kr,y.onCreateVueApp=Ga,y.onError=Xl,y.onGyroscopeChange=Vh,y.onLocaleChange=Fl,y.onLocationChange=of,y.onLocationChangeError=af,y.onMemoryWarning=Sh,y.onNetworkStatusChange=sd,y.onPageNotFound=jl,y.onPushMessage=sc,y.onSocketClose=Qd,y.onSocketError=Jd,y.onSocketMessage=Kd,y.onSocketOpen=Xd,y.onTabBarMidButtonTap=Ll,y.onThemeChange=_d,y.onUnhandledRejection=ql,y.onUserCaptureScreen=Dh,y.onWindowResize=Bl,y.openDocument=kd,y.openLocation=Zd,y.pageScrollTo=Ef,y.plugin=Wu,y.preloadPage=uf,y.previewImage=Fd,y.reLaunch=Tc,y.redirectTo=Sc,y.removeAllPages=Io,y.removeInterceptor=rl,y.removeLastPage=ko,y.removeNonTabBarPages=Ao,y.removeSavedFile=bh,y.removeStorage=Vd,y.removeStorageSync=na,y.removeTabBarBadge=Lf,y.request=ca,y.rpx2px=_o,y.saveFile=gh,y.saveImageToPhotosAlbum=rh,y.saveVideoToPhotosAlbum=lh,y.scanCode=Nh,y.sendSocketMessage=jd,y.setClipboardData=md,y.setKeepScreenOn=Lh,y.setLeftWindowStyle=th,y.setLocale=$l,y.setNavigationBarColor=Tf,y.setNavigationBarTitle=Af,y.setPageMeta=zl,y.setRightWindowStyle=oh,y.setScreenBrightness=Mh,y.setStorage=bd,y.setStorageSync=ta,y.setTabBarBadge=Bf,y.setTabBarItem=Nf,y.setTabBarStyle=xf,y.setTopWindowStyle=Zf,y.setupApp=IS,y.setupPage=kS,y.setupWindow=ES,y.showActionSheet=Sf,y.showLeftWindow=Yf,y.showLoading=vf,y.showModal=hf,y.showNavigationBarLoading=Vf,y.showRightWindow=Jf,y.showTabBar=Pf,y.showTabBarRedDot=Rf,y.showToast=mf,y.showTopWindow=Gf,y.startAccelerometer=Xr,y.startCompass=Zr,y.startGyroscope=Ah,y.startLocationUpdate=tf,y.startPullDownRefresh=kf,y.stopAccelerometer=Jr,y.stopCompass=ea,y.stopGyroscope=kh,y.stopLocationUpdate=nf,y.stopPullDownRefresh=If,y.switchTab=wc,y.uni=uC,y.uploadFile=Hd,y.upx2px=_o,y.useI18n=D,y.useTabBar=Vn,y.vibrateLong=hd,y.vibrateShort=fd,Object.defineProperty(y,Symbol.toStringTag,{value:"Module"})});
