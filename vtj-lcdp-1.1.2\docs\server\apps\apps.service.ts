import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import type { ProjectSchema } from '@vtj/core';
import { App } from './entities/app.entity';
import { AccessService } from '../access/access.service';
import { Access } from '../access/entities/access.entity';
import { AppDto } from './dto/app.dto';
import { QueryAppDto } from './dto/query-app.dto';
import { AccessType, pager } from '../shared';
import { SchemasService } from '../schemas/schemas.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class AppsService {
  constructor(
    @InjectRepository(App) private repo: Repository<App>,
    private access: AccessService,
    private schemas: SchemasService,
    private users: UsersService
  ) {}

  async save(dto: AppDto) {
    if (dto.name && !dto.id) {
      const row = await this.repo.findOneBy({ name: dto.name });
      if (row) {
        throw new BadRequestException('应用名称已被使用，请更换');
      }
    }
    const role = new App();
    const result = await this.repo.save(Object.assign(role, dto));
    const access =
      (await this.access.getAccessByCode(result.name)) || new Access();
    access.code = result.name;
    access.label = result.label;
    access.type = AccessType.App;
    await this.access.save(access);

    return result;
  }

  async find(dto: QueryAppDto) {
    const { page, limit, skip, take } = pager(dto);
    const { withDeleted } = dto;
    const [list, total] = await this.repo.findAndCount({
      withDeleted,
      skip,
      take,
      where: [
        {
          name: dto.keyword ? Like(`%${dto.keyword}%`) : undefined
        },
        {
          label: dto.keyword ? Like(`%${dto.keyword}%`) : undefined
        }
      ],
      order: {
        order: 'ASC',
        createdAt: 'ASC'
      }
    });
    const userIds = list.map((n) => n.userId).filter((v) => !!v);
    if (userIds.length > 0) {
      const users = await this.users.getUsersByIds(userIds);
      for (const item of list) {
        (item as any).userName = users.find((n) => n.id === item.userId)?.name;
      }
    }
    return {
      page,
      limit,
      total,
      list
    };
  }

  async findMyApps(userId: string, dto: QueryAppDto) {
    const { page, limit, skip, take } = pager(dto);
    const { withDeleted } = dto;
    const [list, total] = await this.repo.findAndCount({
      withDeleted,
      skip,
      take,
      where: [
        {
          name: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          userId
        },
        {
          label: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          userId
        }
      ],
      order: {
        order: 'ASC',
        createdAt: 'ASC'
      }
    });
    return {
      page,
      limit,
      total,
      list
    };
  }

  fineOne(id: string) {
    return this.repo.findOneBy({ id });
  }

  fineOneByName(name: string) {
    return this.repo.findOneBy({ name });
  }

  async remove(id: string) {
    const app = await this.fineOne(id);
    if (app) {
      await this.access.remove(app.name);
      await this.schemas.removeApp(app.name);
    }
    return await this.repo.delete(id);
  }

  async genSoucre(project: ProjectSchema) {
    console.log(project);
    return '功能正在开发中...';
  }
}
