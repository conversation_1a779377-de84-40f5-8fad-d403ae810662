!function(e,t){"function"==typeof define&&define.amd?define("dom-zindex",["exports"],t):"undefined"!=typeof exports?t(exports):(t(t={}),e.domZindex=t)}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:this,function(e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getCurrent=e.default=void 0,e.getNext=b,e.getSubCurrent=x,e.getSubNext=C,e.setSubCurrent=e.setCurrent=void 0;var t=null,n=null,r=null,u="z-index-manage",d=null,a="z-index-style",i={m:1e3,s:1e3};function o(){return t||"undefined"!=typeof document&&(t=document),t}function s(){return n=t&&!n?t.body||t.getElementsByTagName("body")[0]:n}function f(){var e,t;d||!(e=o())||(d=e.getElementById(a))||((d=e.createElement("style")).id=a,e.getElementsByTagName("head")[0].appendChild(d)),d&&(d.innerHTML=":root{"+(e="--dom-")+"main"+(t="-z-index")+":"+y()+";"+e+"sub"+t+":"+x()+"}")}function l(){var e,t;return r||(e=o())&&!(r=e.getElementById(u))&&(t=s())&&((r=e.createElement("div")).id=u,r.style.display="none",t.appendChild(r),g(i.m),v(i.s)),r}function m(n){return function(e){var t;return e&&(e=Number(e),i[n]=e,t=l())&&(t.dataset?t.dataset[n]=e+"":t.setAttribute("data-"+n,e+"")),f(),i[n]}}var g=e.setCurrent=m("m");function c(r,u){return function(e){var t=l(),n=(n=t&&(t=t.dataset?t.dataset[r]:t.getAttribute("data-"+r))?Number(t):n)||i[r];return e?Number(e)<n?u():e:n}}var y=e.getCurrent=c("m",b);function b(){return g(y()+1)}var v=e.setSubCurrent=m("s"),p=c("s",C);function x(){return y()+p()}function C(){return v(p()+1),x()}var N={setCurrent:g,getCurrent:y,getNext:b,setSubCurrent:v,getSubCurrent:x,getSubNext:C,getMax:function(){var e=0;if(o()){var t=s();if(t)for(var n=t.getElementsByTagName("*"),r=0;r<n.length;r++){var u=n[r];u&&u.style&&1===u.nodeType&&(u=u.style.zIndex)&&/^\d+$/.test(u)&&(e=Math.max(e,Number(u)))}}return e}};f(),e.default=N});