export interface StorageOptions {
    /**
     * 存储类型
     */
    type: 'cache' | 'local' | 'session';
    /**
     * 在多少毫秒后失效, 0为永不过期
     */
    expired: number;
    /**
     * key前缀
     */
    prefix: string;
}
export interface StorageTypes {
    local: any;
    session: any;
    cache: any;
}
export declare class Storage {
    options: StorageOptions;
    private caches;
    private types;
    constructor(options?: Partial<StorageOptions>);
    config(options?: Partial<StorageOptions>): void;
    save(key: string, value: any, opts?: Partial<StorageOptions>): void;
    get(key: string, opts?: Partial<StorageOptions>): any;
    remove(key: string, opts?: Partial<StorageOptions>): void;
    clear(opts?: Partial<StorageOptions>): void;
}
export declare const storage: Storage;
