import { Ref } from 'vue';
import { default as axios, AxiosInstance, CreateAxiosDefaults, AxiosResponse, AxiosRequestConfig, RawAxiosRequestHeaders, InternalAxiosRequestConfig, CancelTokenSource } from 'axios';
declare const LOCAL_REQUEST_ID = "Local-Request-Id";
export interface IRequestSkipWarn {
    executor: (resolve: (value: unknown) => void, reject: (reason?: any) => void) => void;
    code: string | number;
    name?: string;
    callback?: (res: AxiosResponse) => void;
    complete?: () => void;
}
export interface IRequestSkipWarnResponse extends AxiosResponse {
    promise: any;
}
export interface IResultWrapper<T = any> {
    code: number;
    data: T;
    msg: string;
    success: boolean;
}
export type RequestOriginResponse<R = any, D = any> = AxiosResponse<IResultWrapper<R>, D>;
export interface IRequestSettings {
    /**
     * 发送数据类型
     */
    type?: 'form' | 'json' | 'data';
    /**
     *  是否注入自定义的请求头
     */
    injectHeaders?: boolean;
    /**
     * 自定义请求头
     */
    headers?: RawAxiosRequestHeaders | ((id: string, config: AxiosRequestConfig, settings: IRequestSettings) => RawAxiosRequestHeaders);
    /**
     * 是否显示 loading
     */
    loading?: boolean;
    /**
     * 显示 loading
     */
    showLoading?: () => void;
    /**
     * 关闭 loading
     */
    hideLoading?: () => void;
    /**
     * 显示失败提示
     */
    failMessage?: boolean;
    /**
     * 自定义失败提示
     */
    showError?: (msg: string, e: any) => void;
    /**
     *  返回原始 axios 响应对象
     */
    originResponse?: boolean;
    /**
     * 校验响应成功
     */
    validSuccess?: boolean;
    /**
     * 自定义校验方法
     */
    validate?: (res: AxiosResponse) => boolean;
    /**
     * 请求响应警告执行程序插件
     */
    skipWarn?: IRequestSkipWarn;
    /**
     * 其他自定义扩展参数
     */
    [index: string]: any;
}
export interface IRequestOptions extends CreateAxiosDefaults {
    settings?: IRequestSettings;
}
export interface IRequestConfig<D = any> extends AxiosRequestConfig<D> {
    settings?: IRequestSettings;
    query?: Record<string, any>;
}
export interface IRequestRecord {
    settings: IRequestSettings;
    config: AxiosRequestConfig;
    source: CancelTokenSource;
}
export type IRequest<R = any, D = any> = (config?: IRequestConfig<D>) => Promise<R>;
export declare class Request {
    axios: AxiosInstance;
    settings: IRequestSettings;
    records: Record<string, IRequestRecord>;
    isLoading: boolean;
    private stopSkipWarn?;
    private showLoading;
    private showError;
    constructor(options?: IRequestOptions);
    setConfig(options?: IRequestOptions): void;
    cancel(id?: string, message?: string): void;
    private createHeaders;
    private isJsonType;
    private toFormData;
    private createSendData;
    private createUrl;
    private openLoading;
    private closeLoading;
    private _showError;
    private validResponse;
    private isSkipWarnResponse;
    send<R = any, D = any>(options?: IRequestConfig<D>, isSkipWarn?: boolean): Promise<R>;
    useResponse(onFulfilled?: ((value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>) | null, onRejected?: ((error: any) => any) | null): () => void;
    useRequest(onFulfilled?: ((value: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>) | null, onRejected?: ((error: any) => any) | null): () => void;
    private setupSkipWarn;
}
export interface IStaticRequest<R = any, D = any> extends Request {
    (options: IRequestConfig<D>): Promise<R>;
    instance: Request;
}
export declare function createRequest(options?: IRequestOptions): IStaticRequest;
export declare const request: IStaticRequest;
export declare function createApi<R = any, D = any>(config: string | IRequestConfig): (data?: D, opts?: IRequestConfig) => Promise<R>;
export interface IApiMap {
    [name: string]: string | IRequestConfig;
}
export declare function createApis(map: IApiMap): Record<string, (data?: unknown, opts?: IRequestConfig) => Promise<unknown>>;
export interface UseApiReturn<R = any> {
    data: Ref<R | null>;
    error: Ref<any>;
    loading: Ref<boolean>;
}
export declare function useApi<R = any>(api: Promise<R>, transform?: (res: any) => R): UseApiReturn<R>;
export { axios, LOCAL_REQUEST_ID, type AxiosRequestConfig, type AxiosResponse, type RawAxiosRequestHeaders };
