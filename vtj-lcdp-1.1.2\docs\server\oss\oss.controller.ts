import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Body,
  Get,
  Param,
  Query,
  Res
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Express, type Response } from 'express';
import { OssService } from './oss.service';
import { Public } from '../shared';

@Controller('oss')
export class OssController {
  constructor(private service: OssService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async upload(
    @UploadedFile() file: Express.Multer.File,
    @Body('type') type: string = ''
  ) {
    const ext = file.originalname.substring(file.originalname.lastIndexOf('.'));
    const { name, url } = await this.service.upload(file, type, ext);
    const signUrl = this.service.sign(name);
    return {
      name,
      url: signUrl,
      src: url
    };
  }

  @Public()
  @Get('file/:type/:name')
  async fileWithType(
    @Param('type') type: string,
    @Param('name') name: string,
    @Query('w') width: string,
    @Res() res: Response
  ) {
    const path = type + '/' + name;
    const signUrl = this.service.sign(path, 1800, width);
    res.redirect(signUrl);
    return signUrl;
  }

  @Public()
  @Get('file/:type/:date/:name')
  async fileWithDateType(
    @Param('type') type: string,
    @Param('date') date: string,
    @Param('name') name: string,
    @Query('w') width: string,
    @Res() res: Response
  ) {
    const path = type + '/' + date + '/' + name;
    const signUrl = this.service.sign(path, 1800, width);
    res.redirect(signUrl);
    return signUrl;
  }
}
