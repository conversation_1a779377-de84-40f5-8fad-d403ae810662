(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.ElementPlusMaterial=e())})(this,function(){"use strict";var ze=Object.defineProperty,Fe=Object.defineProperties;var Ae=Object.getOwnPropertyDescriptors;var n=Object.getOwnPropertySymbols;var Je=Object.prototype.hasOwnProperty,De=Object.prototype.propertyIsEnumerable;var o=(t,e,a)=>e in t?ze(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,u=(t,e)=>{for(var a in e||(e={}))Je.call(e,a)&&o(t,a,e[a]);if(n)for(var a of n(e))De.call(e,a)&&o(t,a,e[a]);return t},m=(t,e)=>Fe(t,Ae(e));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const t="0.12.70";function e(l="size"){return{name:l,defaultValue:"default",setters:"SelectSetter",options:["default","large","small"]}}function a(l="type"){return{name:l,defaultValue:"default",setters:"SelectSetter",options:["default","primary","success","warning","danger","info"]}}function d(l,Te){return l.map(Oe=>m(u({},Oe),{package:Te}))}function r(){return[{value:"guide",label:"Guide",children:[{value:"disciplines",label:"Disciplines",children:[{value:"consistency",label:"Consistency"},{value:"feedback",label:"Feedback"},{value:"efficiency",label:"Efficiency"},{value:"controllability",label:"Controllability"}]},{value:"navigation",label:"Navigation",children:[{value:"side nav",label:"Side Navigation"},{value:"top nav",label:"Top Navigation"}]}]},{value:"component",label:"Component",children:[{value:"basic",label:"Basic",children:[{value:"layout",label:"Layout"},{value:"color",label:"Color"},{value:"typography",label:"Typography"},{value:"icon",label:"Icon"},{value:"button",label:"Button"}]},{value:"form",label:"Form",children:[{value:"radio",label:"Radio"},{value:"checkbox",label:"Checkbox"},{value:"input",label:"Input"},{value:"input-number",label:"InputNumber"},{value:"select",label:"Select"},{value:"cascader",label:"Cascader"},{value:"switch",label:"Switch"},{value:"slider",label:"Slider"},{value:"time-picker",label:"TimePicker"},{value:"date-picker",label:"DatePicker"},{value:"datetime-picker",label:"DateTimePicker"},{value:"upload",label:"Upload"},{value:"rate",label:"Rate"},{value:"form",label:"Form"}]},{value:"data",label:"Data",children:[{value:"table",label:"Table"},{value:"tag",label:"Tag"},{value:"progress",label:"Progress"},{value:"tree",label:"Tree"},{value:"pagination",label:"Pagination"},{value:"badge",label:"Badge"}]},{value:"notice",label:"Notice",children:[{value:"alert",label:"Alert"},{value:"loading",label:"Loading"},{value:"message",label:"Message"},{value:"message-box",label:"MessageBox"},{value:"notification",label:"Notification"}]},{value:"navigation",label:"Navigation",children:[{value:"menu",label:"Menu"},{value:"tabs",label:"Tabs"},{value:"breadcrumb",label:"Breadcrumb"},{value:"dropdown",label:"Dropdown"},{value:"steps",label:"Steps"}]},{value:"others",label:"Others",children:[{value:"dialog",label:"Dialog"},{value:"tooltip",label:"Tooltip"},{value:"popover",label:"Popover"},{value:"card",label:"Card"},{value:"carousel",label:"Carousel"},{value:"collapse",label:"Collapse"}]}]},{value:"resource",label:"Resource",children:[{value:"axure",label:"Axure Components"},{value:"sketch",label:"Sketch Templates"},{value:"docs",label:"Design Documentation"}]}]}function i(){return[{date:"2016-05-03",name:"Tom",address:"No. 189, Grove St, Los Angeles"},{date:"2016-05-02",name:"Tom",address:"No. 189, Grove St, Los Angeles"},{date:"2016-05-04",name:"Tom",address:"No. 189, Grove St, Los Angeles"},{date:"2016-05-01",name:"Tom",address:"No. 189, Grove St, Los Angeles"}]}const p={name:"ElButton",label:"按钮",categoryId:"base",doc:"https://element-plus.org/zh-CN/component/button.html",props:[e("size"),a("type"),{name:"plain",defaultValue:!1,setters:"BooleanSetter"},{name:"text",defaultValue:!1,setters:"BooleanSetter"},{name:"bg",defaultValue:!1,setters:"BooleanSetter"},{name:"link",defaultValue:!1,setters:"BooleanSetter"},{name:"round",defaultValue:!1,setters:"BooleanSetter"},{name:"circle",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"loadingIcon",defaultValue:void 0,setters:"IconSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"icon",defaultValue:void 0,setters:"IconSetter"},{name:"autofocus",defaultValue:!1,setters:"BooleanSetter"},{name:"nativeType",defaultValue:"button",setters:"SelectSetter",options:["button ","submit","reset"]},{name:"autoInsertSpace",setters:"BooleanSetter"},{name:"color",setters:"StringSetter"},{name:"dark",defaultValue:!1,setters:"BooleanSetter"},{name:"tag",setters:"StringSetter",defaultValue:"button"}],events:["click"],slots:["default","loading","icon","tag"],snippet:{name:"ElButton",children:"按钮",props:{type:"primary"}}},f={name:"ElButtonGroup",childIncludes:["ElButton"],label:"按钮组",categoryId:"base",props:[e("size"),a("type")],slots:["default"],snippet:{name:"ElButtonGroup",children:[{name:"ElButton",children:"Button1"},{name:"ElButton",children:"Button2"},{name:"ElButton",children:"Button3"}]}},c=[p,f],S=[{name:"ElContainer",label:"布局容器",categoryId:"layout",doc:"https://element-plus.org/zh-CN/component/container.html",package:"element-plus",props:[{name:"direction",defaultValue:"",setters:"SelectSetter",options:["horizontal","vertical"]}],slots:["default"],snippet:{props:{style:{width:"100%",height:"100%"}}}},{name:"ElHeader",parentIncludes:["ElContainer"],label:"顶栏容器",categoryId:"layout",package:"element-plus",props:[{name:"height",defaultValue:"60px",setters:["InputSetter"]}]},{name:"ElAside",parentIncludes:["ElContainer"],label:"侧边栏容器",categoryId:"layout",package:"element-plus",props:[{name:"width",defaultValue:"300px",setters:["InputSetter"]}],slots:["default"]},{name:"ElMain",parentIncludes:["ElContainer"],label:"主要区域容器",categoryId:"layout",package:"element-plus"},{name:"ElFooter",parentIncludes:["ElContainer"],label:"底栏容器",categoryId:"layout",package:"element-plus",props:[{name:"height",defaultValue:"60px",setters:["InputSetter"]}],slots:["default"]}],V=[{name:"ElRow",label:"布局行",categoryId:"layout",doc:"https://element-plus.org/zh-CN/component/layout.html",package:"element-plus",props:[{name:"gutter",defaultValue:0,label:"栅格间隔",setters:"NumberSetter"},{name:"justify",defaultValue:"start",title:"flex 布局下的水平排列方式",options:["start","end","center","space-around","space-between","space-evenly"],setters:"SelectSetter"},{name:"align",defaultValue:"top",title:"flex 布局下的垂直排列方式",options:["top","middle","bottom"],setters:"SelectSetter"},{name:"tag",defaultValue:"div",title:"自定义元素标签",setters:"InputSetter"}],slots:["default"],snippet:{props:{gutter:10}}},{name:"ElCol",label:"布局列",categoryId:"layout",package:"element-plus",parentIncludes:["ElRow"],props:[{name:"span",defaultValue:24,setters:"NumberSetter"},{name:"offset",defaultValue:0,setters:"NumberSetter"},{name:"push",defaultValue:0,setters:"NumberSetter"},{name:"pull",defaultValue:0,setters:"NumberSetter"},{name:"xs",defaultValue:void 0,setters:["JSONSetter","NumberSetter"]},{name:"sm",defaultValue:void 0,setters:["JSONSetter","NumberSetter"]},{name:"md",defaultValue:void 0,setters:["JSONSetter","NumberSetter"]},{name:"lg",defaultValue:void 0,setters:["JSONSetter","NumberSetter"]},{name:"xl",defaultValue:void 0,setters:["JSONSetter","NumberSetter"]},{name:"tag",defaultValue:"div",title:"自定义元素标签",setters:"InputSetter"}],slots:["default"],snippet:{props:{span:6}}}],b={name:"ElLink",label:"链接",categoryId:"base",doc:"https://element-plus.org/zh-CN/component/link.html",props:[a("type"),{name:"underline",defaultValue:!0,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"href",defaultValue:"",setters:"InputSetter"},{name:"target",label:"target",title:"同原生 target 属性",setters:"SelectSetter",options:["_blank","_parent","_self","_top"],defaultValue:"_self"},{name:"icon",defaultValue:"",setters:"IconSetter"}],events:[],slots:[{name:"default"},{name:"icon"}],snippet:{children:"链接文本"}},g={name:"ElText",label:"文本",categoryId:"base",doc:"https://element-plus.org/zh-CN/component/text.html",props:[{name:"type",label:"type",title:"类型",setters:"SelectSetter",options:["primary","success","warning","danger","info"]},{name:"size",label:"size",title:"大小",setters:"SelectSetter",options:["large","default","small"],defaultValue:"default"},{name:"truncated",label:"truncated",title:"显示省略号",setters:"BooleanSetter",defaultValue:!1},{name:"lineClamp",label:"lineClamp",title:"最大行数",setters:["StringSetter","NumberSetter"]},{name:"tag",label:"tag",title:"自定义元素标签",setters:"StringSetter",defaultValue:"span"}],slots:["default"],snippet:{props:{type:"primary"},children:"这是一段Primary颜色的文本"}},h={name:"ElScrollbar",label:"滚动条",categoryId:"base",doc:"https://element-plus.org/zh-CN/component/scrollbar.html",package:"element-plus",props:[{name:"height",defaultValue:"",setters:["NumberSetter","InputSetter"]},{name:"maxHeight",defaultValue:"",setters:["NumberSetter","InputSetter"]},{name:"native",defaultValue:!1,setters:"BooleanSetter"},{name:"wrapStyle",defaultValue:"",setters:"InputSetter"},{name:"wrapClass",defaultValue:"",setters:"InputSetter"},{name:"viewStyle",defaultValue:"",setters:"InputSetter"},{name:"viewClass",defaultValue:"",setters:"InputSetter"},{name:"noresize",defaultValue:!1,setters:"BooleanSetter"},{name:"tag",defaultValue:"div",setters:"InputSetter"},{name:"always",defaultValue:!1,setters:"BooleanSetter"},{name:"minSize",defaultValue:20,setters:"NumberSetter"},{name:"id",label:"id",title:"视图id",setters:"StringSetter"},{name:"role",label:"role",title:"视图的角色",setters:"StringSetter"},{name:"ariaLabel",label:"ariaLabel",title:"视图的 aria-label",setters:"StringSetter"},{name:"ariaOrientation",label:"ariaOrientation",title:"视图的 aria-orientation",setters:"SelectSetter",options:["horizontal","vertical"]}],events:[{name:"scroll"}],snippet:{props:{style:{height:"300px"}},children:[{name:"component",props:{style:{height:"50px",margin:"10px",background:"#ecf5ff"}},directives:[{name:"vFor",value:{type:"JSExpression",value:"6"}}]}]}},I={name:"ElSpace",label:"间距",categoryId:"base",doc:"https://element-plus.org/zh-CN/component/space.html",package:"element-plus",props:[{name:"alignment",defaultValue:"center",title:"对齐的方式",options:["normal","stretch","center","start","end","flex-start","flex-end","baseline","first baseline","last baseline","safe center","unsafe center","inherit","initial","revert","revert-layer","unset"],setters:"SelectSetter"},{name:"class",title:"className",defaultValue:"",setters:["StringSetter","ObjectSetter","ArraySetter"]},{name:"direction",title:"排列的方向",defaultValue:"horizontal",options:["vertical","horizontal"],setters:"SelectSetter"},{name:"prefixCls",title:"给 space-items 的类名前缀",defaultValue:"",setters:"StringSetter"},{name:"style",title:"额外样式",defaultValue:"",setters:["StringSetter","JSONSetter"]},{name:"spacer",title:"间隔符",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"size",title:"间隔大小",defaultValue:"small",setters:["SelectSetter","NumberSetter","ArraySetter"],options:["large","default","small"]},{name:"wrap",title:"设置是否自动折行",defaultValue:!1,setters:"BooleanSetter"},{name:"fill",title:"子元素是否填充父容器",defaultValue:!1,setters:"BooleanSetter"},{name:"fillRatio",title:"填充父容器的比例",defaultValue:100,setters:"NumberSetter"}],slots:["default","spacer"],snippet:{props:{fill:!0,style:{width:"100%"}},children:[{name:"ElCard"},{name:"ElCard"}]}},v={name:"ElAutocomplete",label:"自动补全输入框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/autocomplete.html",package:"element-plus",props:[{name:"modelValue",defaultValue:"",title:"选中项绑定值",setters:"StringSetter"},{name:"placeholder",title:"占位文本",defaultValue:"",setters:"StringSetter"},{name:"clearable",title:"是否可清空",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"自动补全组件是否被禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"valueKey",title:"输入建议对象中用于显示的键名",defaultValue:"value",setters:"StringSetter"},{name:"debounce",defaultValue:300,title:"获取输入建议的防抖延时",setters:"NumberSetter"},{name:"placement",defaultValue:"bottom-start",title:"菜单弹出位置",options:["top ","top-start","top-end","top-end","bottom","bottom-start","bottom-end"],setters:"SelectSetter"},{name:"fetchSuggestions",defaultValue:"",title:"获取输入建议的方法",setters:["ArraySetter","FunctionSetter"]},{name:"triggerOnFocus",defaultValue:!0,title:"是否在输入框focus时显示建议列表",setters:"BooleanSetter"},{name:"selectWhenUnmatched",defaultValue:!1,title:"在输入没有任何匹配建议的情况下，按下回车是否触发select事件",setters:"BooleanSetter"},{name:"name",title:"等价于原生 input name 属性",defaultValue:"",setters:"StringSetter"},{name:"aria-label",defaultValue:"",title:"原生 aria-label属性",setters:"StringSetter"},{name:"hideLoading",title:"是否隐藏远程加载时的加载图标",defaultValue:!1,setters:"BooleanSetter"},{name:"popperClass",defaultValue:"",title:"下拉列表的类名",setters:"StringSetter"},{name:"teleported",title:"是否将下拉列表元素插入 append-to 指向的元素下",defaultValue:!0,setters:"BooleanSetter"},{name:"highlightFirstItem",defaultValue:!1,title:"是否默认突出显示远程搜索建议中的第一项",setters:"BooleanSetter"},{name:"fit-input-width",defaultValue:!1,title:"下拉框是否与输入框同宽",setters:"BooleanSetter"},{name:"popperAppendToBody",defaultValue:!1,title:"是否将下拉列表插入至body元素",setters:"BooleanSetter"}],slots:[{name:"default",params:["item"]},{name:"prefix"},{name:"suffix"},{name:"prepend"},{name:"append"},{name:"loading"}],events:[{name:"update:modelValue"},{name:"select"},{name:"change"}],snippet:{props:{fetchSuggestions:{type:"JSFunction",value:`(function (queryString, cb) {\r
\r
    const list = [\r
        { value: 'vue', link: 'https://github.com/vuejs/vue' },\r
        { value: 'element', link: 'https://github.com/ElemeFE/element' },\r
        { value: 'cooking', link: 'https://github.com/ElemeFE/cooking' },\r
        { value: 'mint-ui', link: 'https://github.com/ElemeFE/mint-ui' },\r
        { value: 'vuex', link: 'https://github.com/vuejs/vuex' },\r
        { value: 'vue-router', link: 'https://github.com/vuejs/vue-router' },\r
        { value: 'babel', link: 'https://github.com/babel/babel' }\r
    ];\r
\r
    const results = list.filter(n => n.value.startsWith(queryString));\r
\r
    cb(results);\r
\r
})`}}}},y=[{name:"ElCascader",label:"级联选择器",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/cascader.html",package:"element-plus",props:[{name:"modelValue",title:"选中项绑定值",setters:["StringSetter","NumberSetter","ObjectSetter"]},{name:"options",title:"选项的数据源， value 和 label 可以通过 CascaderProps 自定义",defaultValue:[],setters:["ArraySetter","JSONSetter"]},{name:"props",title:"配置选项",setters:["ObjectSetter","JSONSetter"]},{name:"size",defaultValue:"",title:"尺寸",options:["large","default","small"],setters:"SelectSetter"},{name:"placeholder",title:"输入框占位文本",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"clearable",title:"是否支持清空选项",defaultValue:!1,setters:"BooleanSetter"},{name:"showAllLevels",defaultValue:!0,title:"输入框中是否显示选中值的完整路径",setters:"BooleanSetter"},{name:"collapseTags",title:"多选模式下是否折叠Tag",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTagsTooltip",defaultValue:!1,title:"当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签。 要使用此属性，collapseTags属性必须设定为 true",setters:"BooleanSetter"},{name:"separator",title:"用于分隔选项的字符",defaultValue:"/",setters:"StringSetter"},{name:"filterable",title:"该选项是否可以被搜索",defaultValue:!1,setters:"BooleanSetter"},{name:"filterMethod",title:"自定义搜索逻辑，第一个参数是node，第二个参数是keyword，返回的布尔值表示是否保留该选项",defaultValue:"",setters:["FunctionSetter","ExpressionSetter"]},{name:"debounce",title:"搜索关键词正在输入时的去抖延迟，单位为毫秒",defaultValue:300,setters:"NumberSetter"},{name:"beforeFilter",title:"过滤函数调用前，所要调用的钩子函数，该函数接收要过滤的值作为参数。 如果该函数的返回值是 false 或者是一个被拒绝的 Promise，那么接下来的过滤逻辑便不会执行",defaultValue:"",setters:["FunctionSetter","ExpressionSetter"]},{name:"popperClass",title:"弹出内容的自定义类名",defaultValue:"",setters:"StringSetter"},{name:"teleported",title:"弹层是否使用 teleport",defaultValue:!0,setters:"BooleanSetter"},{name:"tagType",title:"标签类型",defaultValue:"info",options:["success","info","warning","danger"],setters:"SelectSetter"},{name:"tag-effect",title:"tag effect",defaultValue:"light",options:["light","dark","plain"],setters:"SelectSetter"},{name:"validateEvent",title:"输入时是否触发表单的校验",defaultValue:!0,setters:"BooleanSetter"},{name:"maxCollapseTags",title:"需要显示的 Tag 的最大数量 只有当 collapse-tags 设置为 true 时才会生效。",setters:"NumberSetter",defaultValue:1},{name:"empty-values",title:"组件的空值配置",setters:"ArraySetter"},{name:"value-on-clear",title:"清空选项的值",setters:["StringSetter","NumberSetter","BooleanSetter","ArraySetter"]},{name:"persistent",title:"当下拉框未被激活并且persistent设置为false，下拉框容器会被删除。",defaultValue:!0,setters:"BooleanSetter"},{name:"fallback-placements",title:"Tooltip 可用的 positions",setters:"ArraySetter"},{name:"placement",title:"下拉框出现的位置",defaultValue:"bottom-start",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],setters:"SelectSetter"},{name:"popperAppendToBody",defaultValue:!0,title:"是否将弹出的内容直接插入到 body 元素。 在弹出内容的边框定位出现问题时，可将该属性设置为 false",setters:"BooleanSetter"}],events:[{name:"change"},{name:"expand-change"},{name:"blur"},{name:"focus"},{name:"clear"},{name:"visible-change"},{name:"remove-tag"},{name:"update:modelValue"}],slots:[{name:"default",params:["node","data"]},{name:"empty"}],snippet:{props:{options:r(),props:{},modelValue:[]}}},{name:"ElCascaderPanel",label:"级联面板",categoryId:"form",package:"element-plus",props:[{name:"modelValue",title:"选中项绑定值",defaultValue:"",setters:["StringSetter","NumberSetter","JSONSetter"]},{name:"options",title:"选项的数据源",defaultValue:"",setters:"JSONSetter"},{name:"props",title:"配置选项",defaultValue:"",setters:"JSONSetter"}],events:[{name:"change"},{name:"expand-change"},{name:"close"}],slots:[{name:"default",params:["node","data"]},{name:"empty"}],snippet:{props:{options:r(),props:{},modelValue:[]}}}],B=[{name:"ElCheckbox",label:"多选框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/checkbox.html",package:"element-plus",props:[{name:"modelValue",title:"选中项绑定值",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter"]},{name:"label",title:"选中状态的值，只有在绑定对象类型为 array 时有效。 如果没有 value， label则作为value使用",defaultValue:"",setters:["InputSetter"]},{name:"value",title:"选中状态的值（只有在checkbox-group或者绑定对象类型为array时有效）",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter","JSONSetter"]},{name:"trueValue",title:"选中时的值",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"falseValue",title:"没有选中时的值",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"border",title:"是否显示边框",defaultValue:!1,setters:"BooleanSetter"},{name:"size",title:"Checkbox 的尺寸",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"checked",title:"当前是否勾选",defaultValue:!1,setters:"BooleanSetter"},{name:"indeterminate",defaultValue:!1,title:"设置 indeterminate 状态，只负责样式控制",setters:"BooleanSetter"},{name:"validateEvent",defaultValue:!0,title:"输入时是否触发表单的校验",setters:"BooleanSetter"},{name:"tabindex",label:"tabindex",title:"输入框的 tabindex",setters:["StringSetter","NumberSetter"]},{name:"id",label:"id",title:"input id",setters:"StringSetter"},{name:"aria-controls",title:"与 aria-control一致, 当 indeterminate为 true时生效",setters:"BooleanSetter"},{name:"trueLabel",title:"选中时的值(deprecated)",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"falseLabel",title:"没有选中时的值(deprecated)",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"controls(deprecated)",title:"与 aria-control一致, 当 indeterminate为 true时生效",setters:"BooleanSetter"}],events:[{name:"change"},{name:"update:modelValue"}],slots:["default"],snippet:{props:{label:"选项一",value:1}}},{name:"ElCheckboxGroup",label:"多选框组",childIncludes:["ElCheckbox","ElCheckboxButton"],categoryId:"form",package:"element-plus",props:[{name:"modelValue",title:"绑定值",defaultValue:[],setters:"JSONSetter"},{name:"size",title:"多选框组尺寸",defaultValue:"",options:["large","default","small"],setters:"SelectSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"min",title:"可被勾选的 checkbox 的最小数量",defaultValue:"",setters:"NumberSetter"},{name:"max",title:"可被勾选的 checkbox 的最大数量",defaultValue:"",setters:"NumberSetter"},{name:"aria-label",title:"原生 aria-label属性",defaultValue:"",setters:"InputSetter"},{name:"textColor",title:"当按钮为活跃状态时的字体颜色",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"fill",defaultValue:"#409EFF",title:"当按钮为活跃状态时的边框和背景颜色",setters:"ColorSetter"},{name:"tag",defaultValue:"div",title:"复选框组元素标签",setters:"StringSetter"},{name:"validateEvent",defaultValue:!0,title:"输入时是否触发表单的校验",setters:"BooleanSetter"},{name:"label",title:"原生 aria-label属性",defaultValue:"",setters:"StringSetter"}],events:["change","update:modelValue"],slots:["default"],snippet:{children:[{name:"ElCheckbox",props:{label:"选项一",value:"1"}},{name:"ElCheckbox",props:{label:"选项二",value:"2"}},{name:"ElCheckbox",props:{label:"选项三",value:"3"}}]}},{name:"ElCheckboxButton",label:"按钮样式的多选组合",categoryId:"form",package:"element-plus",props:[{name:"modelValue",title:"选中状态的值，只有在绑定对象类型为 array 时有效。",defaultValue:[],setters:["StringSetter","NumberSetter","BooleanSetter","JSONSetter"]},{name:"label",title:"选中状态的值，只有在绑定对象类型为 array 时有效。 如果没有 value， label则作为value使用",defaultValue:"",setters:["StringSetter","NumberSetter","BooleanSetter","JSONSetter"]},{name:"trueValue",title:"选中时的值",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"falseValue",title:"没有选中时的值",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"name",title:"原生 name 属性",defaultValue:"",setters:"StringSetter"},{name:"checked",defaultValue:!1,setters:"BooleanSetter"},{name:"trueLabel",title:"选中时的值",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"falseLabel",title:"没有选中时的值",defaultValue:"",setters:["StringSetter","NumberSetter"]}],slots:["default"],snippet:{props:{label:"按钮选项"}}}],N={name:"ElColorPicker",label:"取色器",categoryId:"form",childIncludes:!1,doc:"https://element-plus.org/zh-CN/component/color-picker.html",package:"element-plus",props:[{name:"modelValue",title:"选中项绑定值",defaultValue:"",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"size",defaultValue:"",options:["large","default","small"],setters:"SelectSetter"},{name:"showAlpha",title:"是否支持透明度选择",defaultValue:!1,setters:"BooleanSetter"},{name:"colorFormat",title:"写入 v-model 的颜色的格式",defaultValue:"",options:["hsl","hsv","hex","rgb"],setters:"SelectSetter"},{name:"popperClass",title:"ColorPicker 下拉框的类名",defaultValue:"",setters:"StringSetter"},{name:"predefine",title:"预定义颜色",defaultValue:"",setters:"JSONSetter"},{name:"validateEvent",title:"输入时是否触发表单的校验",defaultValue:!0,setters:"BooleanSetter"},{name:"tabindex",label:"tabindex",title:"ColorPicker 的 tabindex",setters:["StringSetter","NumberSetter"],defaultValue:0},{name:"aria-label",title:"ColorPicker 的 aria-label",setters:"StringSetter"},{name:"id",title:"ColorPicker 的 id",setters:"StringSetter"},{name:"teleported",title:"是否将 popover 的下拉列表渲染至 body 下",defaultValue:!0,setters:"BooleanSetter"},{name:"label",title:"ColorPicker 的 aria-label(deprecated)",setters:"StringSetter"}],events:[{name:"change"},{name:"active-change"},{name:"update:modelValue"},{name:"focus"},{name:"blur"}]},k={name:"ElDatePicker",label:"日期选择器",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/date-picker.html",package:"element-plus",props:[{name:"modelValue",title:"绑定值，如果它是数组，长度应该是 2",defaultValue:"",setters:["NumberSetter","StringSetter","ArraySetter","ExpressionSetter"]},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"editable",title:"文本框可输入",defaultValue:!0,setters:"BooleanSetter"},{name:"clearable",defaultValue:!0,setters:"BooleanSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"startPlaceholder",defaultValue:"",title:"范围选择时开始日期的占位内容",setters:"InputSetter"},{name:"endPlaceholder",defaultValue:"",title:"范围选择时结束日期的占位内容",setters:"InputSetter"},{name:"type",defaultValue:"date",title:"显示类型",options:["year","years","month","months","date","dates","datetime","week","datetimerange","daterange","monthrange","yearrange"],setters:"SelectSetter"},{name:"format",title:"显示在输入框中的格式",defaultValue:"YYYY-MM-DD",setters:"InputSetter"},{name:"popperClass",title:"DatePicker 下拉框的类名",defaultValue:"",setters:"InputSetter"},{name:"popper-options",title:"自定义 popper 选项",defaultValue:"",setters:["ObjectSetter","JSONSetter"]},{name:"rangeSeparator",defaultValue:"-",title:"选择范围时的分隔符",setters:"InputSetter"},{name:"defaultValue",title:"可选，选择器打开时默认显示的时间",defaultValue:"",setters:"ExpressionSetter"},{name:"defaultTime",title:"范围选择时选中日期所使用的当日内具体时刻",defaultValue:"",setters:"ExpressionSetter"},{name:"valueFormat",title:"可选，绑定值的格式。 不指定则绑定值为 Date 对象",defaultValue:"",setters:"InputSetter"},{name:"id",defaultValue:"",setters:["InputSetter"]},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"unlinkPanels",title:"在范围选择器里取消两个日期面板之间的联动",defaultValue:!1,setters:"BooleanSetter"},{name:"prefixIcon",defaultValue:"Date",title:"自定义前缀图标",setters:"InputSetter"},{name:"clearIcon",defaultValue:"CircleClose",title:"自定义清除图标",setters:"InputSetter"},{name:"validateEvent",defaultValue:!0,title:"输入时是否触发表单的校验",setters:"BooleanSetter"},{name:"disabledDate",title:"一个用来判断该日期是否被禁用的函数，接受一个 Date 对象作为参数。 应该返回一个 Boolean 值。",defaultValue:"",setters:"FunctionSetter"},{name:"shortcuts",defaultValue:"",title:"设置快捷选项，需要传入数组对象",setters:"JSONSetter"},{name:"cellClassName",defaultValue:"",title:"设置自定义类名",setters:"FunctionSetter"},{name:"teleported",defaultValue:!0,title:"是否将 date-picker 的下拉列表插入至 body 元素",setters:"BooleanSetter"},{name:"empty-values",title:"组件的空值配置",setters:"ArraySetter"},{name:"value-on-clear",title:"清空选项的值",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]},{name:"fallback-placements",title:"Tooltip 可用的 positions",setters:"ArraySetter"},{name:"placement",title:"下拉框出现的位置",defaultValue:"bottom",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"]}],events:[{name:"change"},{name:"blur"},{name:"focus"},{name:"clear"},{name:"calendar-change"},{name:"panel-change"},{name:"visible-change"},{name:"update:modelValue"}],slots:[{name:"default"},{name:"range-separator"},{name:"prev-month"},{name:"next-month"},{name:"prev-year"},{name:"next-year"}]},x={name:"ElDateTimePicker",label:"日期时间选择器",alias:"ElDatePicker",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/datetime-picker.html",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["NumberSetter","StringSetter","ExpressionSetter"]},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"editable",defaultValue:!0,setters:"BooleanSetter"},{name:"clearable",defaultValue:!0,setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"placeholder",defaultValue:"",setters:"StringSetter"},{name:"startPlaceholder",defaultValue:"",title:"范围选择时开始日期的占位内容",setters:"StringSetter"},{name:"endPlaceholder",defaultValue:"",title:"范围选择时结束日期的占位内容",setters:"InputSetter"},{name:"arrowControl",title:"是否使用箭头进行时间选择",setters:"BooleanSetter",defaultValue:!1},{name:"type",defaultValue:"date",title:"显示类型",options:["year","month","date","dates","datetime","week","datetimerange","daterange","monthrange"],setters:"SelectSetter"},{name:"format",title:"显示在输入框中的格式",defaultValue:"YYYY-MM-DD HH:mm:ss",setters:"StringSetter"},{name:"popperClass",title:"DateTimePicker 下拉框的类名",defaultValue:"",setters:"StringSetter"},{name:"rangeSeparator",defaultValue:"-",title:"选择范围时的分隔符",setters:"StringSetter"},{name:"defaultValue",title:"可选，选择器打开时默认显示的时间",defaultValue:"",setters:"ExpressionSetter"},{name:"defaultTime",title:"选择日期后的默认时间值。 如未指定则默认时间值为 00:00:00",defaultValue:"",setters:"ExpressionSetter"},{name:"valueFormat",title:"可选，绑定值的格式。 不指定则绑定值为 Date 对象",defaultValue:"",setters:"StringSetter"},{name:"dateFormat",defaultValue:"",setters:"StringSetter",title:"时间选择器下拉列表中显示的日期格式"},{name:"timeFormat",defaultValue:"",setters:"StringSetter",title:"时间选择器下拉列表中显示的时间格式"},{name:"id",defaultValue:"",setters:["StringSetter","ArraySetter","ExpressionSetter"]},{name:"name",defaultValue:"",setters:"StringSetter"},{name:"unlinkPanels",title:"在范围选择器里取消两个日期面板之间的联动",defaultValue:!1,setters:"BooleanSetter"},{name:"prefixIcon",defaultValue:"Date",title:"自定义前缀图标",setters:"InputSetter"},{name:"clearIcon",defaultValue:"CircleClose",title:"自定义清除图标",setters:"InputSetter"},{name:"shortcuts",defaultValue:"",title:"设置快捷选项，需要传入数组对象",setters:"JSONSetter"},{name:"disabledDate",defaultValue:"",setters:"FunctionSetter"},{name:"cellClassName",defaultValue:"",title:"设置自定义类名",setters:"FunctionSetter"},{name:"teleported",defaultValue:!0,title:"设置自定义类名",setters:"BooleanSetter"},{name:"emptyValues",title:"组件的空值配置",setters:"ArraySetter"},{name:"valueOnClear",title:"清空选项的值",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]},{name:"showNow",title:"是否显示 now 按钮",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"change"},{name:"blur"},{name:"focus"},{name:"clear"},{name:"calendar-change"},{name:"visible-change"},{name:"update:modelValue"}],slots:[{name:"default"},{name:"range-separator"},{name:"prev-month"},{name:"next-month"},{name:"prev-year"},{name:"next-year"}],snippet:{props:{type:"datetime",placeholder:"Select date and time"}}},E=[{name:"ElForm",label:"表单",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/form.html",package:"element-plus",props:[{name:"model",title:"表单数据对象",defaultValue:"",setters:"ExpressionSetter"},{name:"rules",defaultValue:"",setters:"ExpressionSetter"},{name:"inline",defaultValue:!1,setters:"BooleanSetter"},{name:"labelPosition",defaultValue:"right",options:["left","right","top"],setters:"SelectSetter"},{name:"labelWidth",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"labelSuffix",defaultValue:"",setters:"InputSetter"},{name:"hideRequiredAsterisk",defaultValue:!1,title:"是否显示必填字段的标签旁边的红色星号",setters:"BooleanSetter"},{name:"requireAsteriskPosition",defaultValue:"left",title:"星号的位置",options:["left","right"],setters:"SelectSetter"},{name:"showMessage",defaultValue:!0,title:"是否显示校验错误信息",setters:"BooleanSetter"},{name:"inlineMessage",defaultValue:!1,title:"是否以行内形式展示校验信息",setters:"BooleanSetter"},{name:"statusIcon",defaultValue:!1,title:"是否在输入框中显示校验结果反馈图标",setters:"BooleanSetter"},{name:"validateOnRuleChange",defaultValue:!0,title:"是否在 rules 属性改变后立即触发一次验证",setters:"BooleanSetter"},{name:"size",defaultValue:"",options:["large","default","small"],setters:"SelectSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollToError",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollIntoViewOptions",defaultValue:"",setters:["ExpressionSetter","BooleanSetter"]}],events:[{name:"validate"}],slots:["default"],snippet:{name:"ElForm",props:{labelWidth:"80px"},children:[{name:"ElFormItem",props:{label:"表单项"},children:[{name:"ElInput"}]},{name:"ElFormItem",props:{label:" "},children:[{name:"ElButton",props:{type:"primary"},children:"确定"}]}]}},{name:"ElFormItem",label:"表单项",categoryId:"form",package:"element-plus",props:[{name:"prop",defaultValue:"",setters:["InputSetter","ArraySetter"]},{name:"label",defaultValue:"",setters:"InputSetter"},{name:"labelPosition",defaultValue:"",setters:"SelectSetter",options:["left","right","top"]},{name:"labelWidth",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"required",defaultValue:!1,setters:"BooleanSetter"},{name:"rules",defaultValue:"",setters:"JSONSetter"},{name:"error",defaultValue:"",setters:"InputSetter"},{name:"showMessage",defaultValue:!0,title:"是否显示校验错误信息",label:"错误信息",setters:"BooleanSetter"},{name:"inlineMessage",defaultValue:!1,title:"是否在行内显示校验信息",label:"校验信息",setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"for",defaultValue:"",setters:"StringSetter"},{name:"validateStatus",title:"formitem 校验的状态",options:["","error","validating","success"],setters:"SelectSetter"}],slots:["default","label","error"],snippet:{props:{label:"表单项"},children:[{name:"ElInput"}]}}],C={name:"ElInput",label:"输入框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/input.html",props:[{name:"type",defaultValue:"text",options:["text","textarea"],setters:["SelectSetter","InputSetter"]},{name:"modelValue",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"maxlength",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"minlength",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"showWordLimit",defaultValue:!1,title:'是否显示输入字数统计，只在 type = "text" 或 type = "textarea" 时有效',label:"字数统计",setters:"BooleanSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"formatter",defaultValue:"",setters:"FunctionSetter"},{name:"parser",defaultValue:"",setters:"FunctionSetter"},{name:"showPassword",defaultValue:!1,title:"是否显示切换密码图标",label:"密码图标",setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},e("size"),{name:"prefix-icon",defaultValue:"",setters:"InputSetter"},{name:"suffix-icon",defaultValue:"",setters:"InputSetter"},{name:"rows",defaultValue:2,setters:"NumberSetter"},{name:"autosize",defaultValue:!1,setters:["BooleanSetter","JSONStter"]},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"max",defaultValue:"",setters:"InputSetter"},{name:"min",defaultValue:"",setters:"InputSetter"},{name:"step",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"resize",defaultValue:"",options:["none","both","horizontal","vertical"],setters:"InputSetter"},{name:"autofocus",defaultValue:!1,setters:"BooleanSetter"},{name:"form",defaultValue:"",setters:"InputSetter"},{name:"aria-label",defaultValue:"",setters:"InputSetter"},{name:"tabindex",defaultValue:"",setters:"InputSetter"},{name:"validateEvent",defaultValue:!0,title:"输入时是否触发表单的校验",label:"表单校验",setters:"BooleanSetter"},{name:"inputStyle",defaultValue:{},setters:["JSONSetter"]}],events:[{name:"blur"},{name:"focus"},{name:"change"},{name:"input"},{name:"clear"},{name:"update:modelValue"}],slots:["prefix","suffix","prepend","append"]},w={name:"ElInputNumber",label:"数字输入框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/input-number.html",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:"NumberSetter"},{name:"min",defaultValue:-1/0,setters:"NumberSetter"},{name:"max",defaultValue:1/0,setters:"NumberSetter"},{name:"step",defaultValue:1,setters:"NumberSetter"},{name:"stepStrictly",defaultValue:!1,setters:"BooleanSetter"},{name:"precision",defaultValue:"",setters:"NumberSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"controls",defaultValue:!0,setters:"BooleanSetter"},{name:"controlsPosition",defaultValue:"",title:"控制按钮位置",label:"按钮位置",options:["","right"],setters:"SelectSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"ariaLabel",defaultValue:"",setters:"InputSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"valueOnClear",defaultValue:"",options:["min","max"],setters:["InputSetter","NumberSetter"]},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"}],slots:[{name:"decrease-icon"},{name:"increase-icon"},{name:"prefix"},{name:"suffix"}],events:[{name:"change"},{name:"blur"},{name:"focus"},{name:"update:modelValue"}]},T={name:"ElInputTag",label:"标签输入框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/input-tag.html",props:[{name:"modelValue",setters:"ArraySetter"},{name:"max",title:"可添加标签的最大数量",setters:"NumberSetter"},{name:"tagType",title:"标签类型",defaultValue:"info",setters:"SelectSetter",options:["primary","success","info","warning","danger"]},{name:"tagEffect",title:"标签效果",defaultValue:"light",setters:"SelectSetter",options:["light","dark","plain"]},{name:"trigger",title:"触发输入标签的按键",defaultValue:"Enter",setters:"SelectSetter",options:["Enter","Space"]},{name:"draggable",title:"是否可以拖动标签",defaultValue:!1,setters:"BooleanSetter"},{name:"size",setters:"SelectSetter",defaultValue:"default",options:["large","default","small"]},{name:"clearable",title:"是否显示清除按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"validateEvent",title:"是否触发表单验证",defaultValue:!0,setters:"BooleanSetter"},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"autofocus",defaultValue:!1,setters:"BooleanSetter"},{name:"id",setters:"StringSetter"},{name:"tabindex",setters:["StringSetter","NumberSetter"]},{name:"maxlength",setters:["StringSetter","NumberSetter"]},{name:"minlength",setters:["StringSetter","NumberSetter"]},{name:"placeholder",setters:"StringSetter"},{name:"autocomplete",defaultValue:"off",setters:"StringSetter"},{name:"ariaLabel",setters:"StringSetter"}],events:[{name:"change"},{name:"input"},{name:"add-tag"},{name:"remove-tag"},{name:"focus"},{name:"blur"},{name:"clear"},{name:"update:modelValue"}],slots:[{name:"tag"},{name:"prefix"},{name:"suffix"}],snippet:{props:{modelValue:["1","2"]}}},O={name:"ElMention",label:"提及",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/mention.html#api",props:[{name:"options",title:"提及选项列表",defaultValue:[],setters:"ArraySetter"},{name:"prefix",title:"触发字段的前缀。 字符串长度必须且只能为 1",defaultValue:"@",setters:["StringSetter","ArraySetter"]},{name:"split",title:"用于拆分提及的字符。 字符串长度必须且只能为 1	",defaultValue:" ",setters:"StringSetter"},{name:"filterOption",title:"定制筛选器选项逻辑",setters:["BooleanSetter","FunctionSetter"]},{name:"placement",title:"设置弹出位置",defaultValue:"bottom",options:["top","bottom"],setters:"SelectSetter"},{name:"showArrow",title:"下拉菜单的内容是否有箭头",defaultValue:!1,setters:"BooleanSetter"},{name:"offset",title:"下拉面板偏移量",defaultValue:0,setters:"NumberSetter"},{name:"whole",title:"当退格键被按下做删除操作时，是否将提及部分作为整体删除",defaultValue:!1,setters:"BooleanSetter"},{name:"checkIsWhole",title:"当退格键被按下做删除操作时，检查是否将提及部分作为整体删除",setters:"FunctionSetter"},{name:"loading",title:"提及的下拉面板是否处于加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"modelValue",title:"输入值",setters:"StringSetter"},{name:"popperClass",title:"自定义浮层类名",setters:"StringSetter"},{name:"popperOptions",title:"参数",setters:["ObjectSetter","JSONSetter"]},{name:"type",defaultValue:"text",options:["text","textarea"],setters:["SelectSetter","InputSetter"]},{name:"maxlength",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"minlength",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"showWordLimit",defaultValue:!1,title:'是否显示输入字数统计，只在 type = "text" 或 type = "textarea" 时有效',label:"字数统计",setters:"BooleanSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"formatter",defaultValue:"",setters:"FunctionSetter"},{name:"parser",defaultValue:"",setters:"FunctionSetter"},{name:"showPassword",defaultValue:!1,title:"是否显示切换密码图标",label:"密码图标",setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},e("size"),{name:"prefix-icon",defaultValue:"",setters:"InputSetter"},{name:"suffix-icon",defaultValue:"",setters:"InputSetter"},{name:"rows",defaultValue:2,setters:"NumberSetter"},{name:"autosize",defaultValue:!1,setters:["BooleanSetter","JSONStter"]},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"max",defaultValue:"",setters:"InputSetter"},{name:"min",defaultValue:"",setters:"InputSetter"},{name:"step",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"resize",defaultValue:"",options:["none","both","horizontal","vertical"],setters:"InputSetter"},{name:"autofocus",defaultValue:!1,setters:"BooleanSetter"},{name:"form",defaultValue:"",setters:"InputSetter"},{name:"aria-label",defaultValue:"",setters:"InputSetter"},{name:"tabindex",defaultValue:"",setters:"InputSetter"},{name:"validateEvent",defaultValue:!0,title:"输入时是否触发表单的校验",label:"表单校验",setters:"BooleanSetter"},{name:"inputStyle",defaultValue:{},setters:["JSONSetter"]}],events:[{name:"update:modelValue"},{name:"search"},{name:"select"},{name:"input"},{name:"blur"},{name:"focus"},{name:"change"},{name:"clear"}],slots:["label","loading","header","footer","prefix","suffix","prepend","append"],snippet:{props:{options:[{label:"Fuphoenixes",value:"Fuphoenixes"},{label:"kooriookami",value:"kooriookami"},{label:"Jeremy",value:"Jeremy"},{label:"btea",value:"btea"}],modelValue:"@"}}},z=[{name:"ElRadio",label:"单选框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/radio.html",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter"]},{name:"value",title:"单选框的值",setters:["StringSetter","NumberSetter","BooleanSetter"]},{name:"label",defaultValue:"",title:"单选框的 label 如果value没有值， label则作为value使用",setters:["StringSetter","NumberSetter","BooleanSetter"]},{name:"disabled",title:"是否禁用单选框",defaultValue:!1,setters:"BooleanSetter"},{name:"border",title:"是否显示边框",defaultValue:!1,setters:"BooleanSetter"},{name:"size",title:"单选框的尺寸",defaultValue:"",options:["large","default","small"],setters:"SelectSetter"},{name:"name",title:"原始 name 属性",defaultValue:"",setters:"StringSetter"}],events:[{name:"change"},{name:"update:modelValue"}],slots:["default"],snippet:{props:{label:"选项一",value:"1"}}},{name:"ElRadioGroup",label:"单选框组",categoryId:"form",package:"element-plus",props:[{name:"modelValue",title:"绑定值",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter"]},{name:"size",title:"单选框按钮或边框按钮的大小",defaultValue:"",options:["large","default","small"],setters:"SelectSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"textColor",title:"按钮形式的 Radio 激活时的文本颜色",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"fill",title:"按钮形式的 Radio 激活时的填充色和边框色",defaultValue:"#409EFF",setters:"ColorSetter"},{name:"validateEvent",title:"输入时是否触发表单的校验",defaultValue:!0,setters:"BooleanSetter"},{name:"aria-label",title:"与 RadioGroup 中的 aria-label 属性相同",setters:"StringSetter"},{name:"name",label:"name",title:"原生 name 属性",setters:"StringSetter"},{name:"id",label:"id",title:"原生 id 属性",setters:"StringSetter"}],events:[{name:"change"},{name:"update:modelValue"}],slots:["default"],snippet:{name:"ElRadioGroup",children:[{name:"ElRadio",props:{label:"选项一",value:"1"}},{name:"ElRadio",props:{label:"选项二",value:"2"}},{name:"ElRadio",props:{label:"选项三",value:"3"}}]}},{name:"ElRadioButton",label:"单选框按钮",categoryId:"form",package:"element-plus",props:[{name:"label",title:"单选框的label,如果没有value,label则作为value使用",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"value",title:"单选框的值",defaultValue:"",setters:["StringSetter","NumberSetter","BooleanSetter"]},{name:"disabled",title:"是否禁用单选框",defaultValue:!1,setters:"BooleanSetter"},{name:"name",title:"原生 name 属性",defaultValue:"",setters:"StringSetter"}],slots:["default"],snippet:{children:"按钮选项"}}],F={name:"ElRate",label:"评分",childIncludes:!1,categoryId:"form",doc:"https://element-plus.org/zh-CN/component/rate.html",package:"element-plus",props:[{name:"modelValue",defaultValue:0,setters:"NumberSetter"},{name:"max",defaultValue:5,setters:"NumberSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"allowHalf",defaultValue:!1,setters:"BooleanSetter"},{name:"lowThreshold",defaultValue:2,title:"低分和中等分数的界限值， 值本身被划分在低分中",setters:"NumberSetter"},{name:"highThreshold",defaultValue:4,title:"高分和中等分数的界限值， 值本身被划分在高分中",setters:"NumberSetter"},{name:"colors",defaultValue:["#F7BA2A","#F7BA2A","#F7BA2A"],setters:["ArraySetter","JSONSetter"]},{name:"voidColor",defaultValue:"#C6D1DE",setters:"ColorSetter"},{name:"disabledVoidColor",defaultValue:"#EFF2F7",label:"disabledColor",setters:"ColorSetter"},{name:"icons",defaultValue:["StarFilled","StarFilled","StarFilled"],setters:["ArraySetter","JSONSetter"]},{name:"voidIcon",defaultValue:"Star",setters:"InputSetter"},{name:"disabledVoidIcon",defaultValue:"StarFilled",label:"disabledIcon",setters:"InputSetter"},{name:"showText",defaultValue:!1,setters:"BooleanSetter"},{name:"showScore",defaultValue:!1,setters:"BooleanSetter"},{name:"textColor",defaultValue:"#1F2D3D",setters:"ColorSetter"},{name:"texts",defaultValue:["Extremely bad","Disappointed","Fair","Satisfied","Surprise"],setters:["ArraySetter","JSONSetter"]},{name:"scoreTemplate",defaultValue:"",setters:"InputSetter"},{name:"clearable",label:"clearable",title:"是否可以重置值为 0",setters:"BooleanSetter",defaultValue:!1},{name:"id",label:"id",title:"原生 id 属性",setters:"StringSetter"},{name:"ariaLabel",title:"和 Rate 的 aria-label 属性保持一致",setters:"StringSetter"},{name:"label",label:"label",title:"和 Rate 的 aria-label 属性保持一致",setters:"StringSetter"}],events:["change","update:modelValue"]},A=[{name:"ElSelect",label:"选择器",doc:"https://element-plus.org/zh-CN/component/select.html",categoryId:"form",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["NumberSetter","InputSetter","BooleanSetter"]},{name:"multiple",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"valueKey",defaultValue:"value",setters:"InputSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTags",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTagsTooltip",title:"当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签。 要使用此属性，collapse-tags属性必须设定为 true",defaultValue:!1,setters:"BooleanSetter"},{name:"multipleLimit",defaultValue:0,setters:"NumberSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"light",options:["dark","light"],setters:"SelectSetter"},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"placeholder",defaultValue:"Select",setters:"InputSetter"},{name:"filterable",defaultValue:!1,setters:"BooleanSetter"},{name:"allowCreate",defaultValue:!1,setters:"BooleanSetter"},{name:"filterMethod",defaultValue:"",setters:"FunctionSetter"},{name:"remote",defaultValue:!1,setters:"BooleanSetter"},{name:"remoteMethod",defaultValue:"",setters:"FunctionSetter"},{name:"remoteShowSuffix",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"loadingText",defaultValue:"Loading",setters:"InputSetter"},{name:"noMatchText",defaultValue:"No matching data",setters:"InputSetter"},{name:"noDataText",defaultValue:"No data",setters:"InputSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"reserveKeyword",defaultValue:!0,setters:"BooleanSetter"},{name:"defaultFirstOption",defaultValue:!1,setters:"BooleanSetter"},{name:"teleported",defaultValue:!0,setters:"BooleanSetter"},{name:"appendTo",title:"下拉框挂载到哪个 DOM 元素",defaultValue:"",setters:"StringSetter"},{name:"persistent",defaultValue:!0,setters:"BooleanSetter"},{name:"automaticDropdown",defaultValue:!1,setters:"BooleanSetter"},{name:"clearIcon",defaultValue:"CircleClose",setters:"InputSetter"},{name:"fitInputWidth",defaultValue:!1,setters:"BooleanSetter"},{name:"suffixIcon",defaultValue:"ArrowUp",setters:"InputSetter"},{name:"tagType",defaultValue:"info",options:["success","info","warning","danger"],setters:"SelectSetter"},{name:"tagEffect",defaultValue:"light",options:["","light","dark","plain"],setters:"SelectSetter"},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"},{name:"offset",defaultValue:12,setters:"NumberSetter"},{name:"showArrow",defaultValue:!0,setters:"BooleanSetter"},{name:"placement",label:"placement",title:"下拉框出现的位置",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],defaultValue:"bottom-start"},{name:"fallbackPlacements",label:"fallbackPlacements",title:"dropdown 可用的 positions",setters:"ArraySetter",defaultValue:["bottom-start","top-start","right","left"]},{name:"maxCollapseTags",label:"maxCollapseTags",title:"需要显示的 Tag 的最大数量 只有当 collapse-tags 设置为 true 时才会生效。",setters:"NumberSetter",defaultValue:1},{name:"popperOptions",label:"popperOptions",title:"popper.js 参数",setters:"ObjectSetter",defaultValue:{}},{name:"ariaLabel",label:"ariaLabel",title:"等价于原生 input aria-label 属性",setters:"StringSetter"},{name:"emptyValues",title:"组件的空值配置",setters:"ArraySetter"},{name:"valueOnClear",title:"清空选项的值 ",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]},{name:"suffixTransition",title:"下拉菜单显示/消失时后缀图标的动画",defaultValue:!0,setters:"BooleanSetter"},{name:"tabindex",title:"input 的 tabindex",setters:["StringSetter","NumberSetter"]}],events:["change","visible-change","remove-tag","clear","blur","focus","update:modelValue"],slots:[{name:"default"},{name:"header"},{name:"footer"},{name:"prefix"},{name:"empty"},{name:"tag"},{name:"loading"},{name:"label"}],snippet:{children:[{name:"ElOption",props:{label:{type:"JSExpression",value:"`选项${this.context.item}`"}},directives:[{name:"vFor",value:{type:"JSExpression",value:"6"}}]}]}},{name:"ElOptionGroup",label:"选择器选项组",categoryId:"form",package:"element-plus",parentIncludes:["ElSelect"],props:[{name:"label",defaultValue:"",setters:"InputSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{props:{label:"分组"}}},{name:"ElOption",label:"选择器选项",categoryId:"form",package:"element-plus",parentIncludes:["ElSelect","ElOptionGroup"],props:[{name:"value",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter","JSONSetter"]},{name:"label",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{props:{label:"选项"}}}],J={name:"ElSelectV2",label:"虚拟列表选择器",doc:"https://element-plus.org/zh-CN/component/select-v2.html",categoryId:"data",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["InputSetter","NumberSetter","BooleanSetter","JSONSetter"]},{name:"options",label:"options",title:"选项的数据源， value 的 key 和 label 可以通过 props自定义.",setters:"ArraySetter"},{name:"props",label:"props",title:"配置选项，具体看下表",setters:"ObjectSetter"},{name:"multiple",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"valueKey",defaultValue:"value",setters:"InputSetter"},{name:"size",defaultValue:"",options:["","large","default","small"],setters:"SelectSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"clearIcon",label:"clearIcon",title:"自定义清除图标",setters:["StringSetter","ObjectSetter"],defaultValue:"CircleClose"},{name:"collapseTags",defaultValue:!1,setters:"BooleanSetter"},{name:"multipleLimit",label:"multipleLimit",title:"多选时可被选择的最大数目。 当被设置为0时，可被选择的数目不设限。",setters:"NumberSetter",defaultValue:0},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"light",options:["dark","light"],setters:["SelectSetter","StringSetter"]},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"placeholder",defaultValue:"Please select",setters:"InputSetter"},{name:"filterable",defaultValue:!1,setters:"BooleanSetter"},{name:"allowCreate",defaultValue:!1,setters:"BooleanSetter"},{name:"filterMethod",label:"filterMethod",title:"自定义筛选方法",setters:"FunctionSetter"},{name:"loading",label:"loading",title:"是否从远程加载数据",setters:"BooleanSetter",defaultValue:!1},{name:"loadingText",label:"loadingText",title:"从服务器加载数据时显示的文本，默认为“Loading”",setters:"StringSetter",defaultValue:"Loading"},{name:"reserveKeyword",label:"reserveKeyword",title:"筛选时，是否在选择选项后保留关键字",setters:"BooleanSetter",defaultValue:!0},{name:"noMatchText",label:"noMatchText",title:"搜索条件无匹配时显示的文字，也可以使用 empty 插槽设置，默认是 “No matching data“",setters:"StringSetter"},{name:"noDataText",label:"noDataText",title:"当在没有数据时显示的文字，你同时可以使用empty插槽进行设置。",setters:"StringSetter",defaultValue:"No Data"},{name:"popperClass",label:"popperClass",title:"选择器下拉菜单的自定义类名",setters:"StringSetter",defaultValue:""},{name:"teleported",label:"teleported",title:"是否将下拉列表元素插入 append-to 指向的元素下",setters:"BooleanSetter",defaultValue:!0},{name:"appendTo",title:"下拉框挂载到哪个 DOM 元素",setters:"StringSetter"},{name:"persistent",defaultValue:!0,setters:"BooleanSetter"},{name:"popperOptions",label:"popperOptions",setters:"ObjectSetter",defaultValue:{}},{name:"automaticDropdown",defaultValue:!1,setters:"BooleanSetter"},{name:"fitInputWidth",defaultValue:!0,setters:["BooleanSetter","NumberSetter"]},{name:"height",defaultValue:274,setters:"NumberSetter"},{name:"itemHeight",label:"itemHeight",title:"下拉项的高度",setters:"NumberSetter",defaultValue:34},{name:"scrollbarAlwaysOn",defaultValue:!1,setters:"BooleanSetter"},{name:"remote",defaultValue:!1,setters:"BooleanSetter"},{name:"remoteMethod",defaultValue:"",setters:"FunctionSetter"},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"},{name:"offset",title:"下拉面板偏移量",defaultValue:12,setters:"NumberSetter"},{name:"showArrow",title:"下拉菜单的内容是否有箭头",defaultValue:!0,setters:"BooleanSetter"},{name:"placement",label:"placement",title:"下拉框出现的位置",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],defaultValue:"bottom-start"},{name:"fallbackPlacements",setters:"ArraySetter",defaultValue:["bottom-start","top-start","right","left"]},{name:"collapseTagsTooltip",setters:"BooleanSetter",defaultValue:!1},{name:"maxCollapseTags",setters:"NumberSetter",defaultValue:1},{name:"tagType",label:"tagType",title:"标签类型",setters:"SelectSetter",options:["","success","info","warning","danger"],defaultValue:"info"},{name:"tagEffect",title:"标签效果",defaultValue:"light",options:["","light","dark","plain"],setters:"SelectSetter"},{name:"ariaLabel",label:"ariaLabel",title:"等价于原生 input aria-label 属性",setters:"StringSetter"},{name:"emptyValues",title:"组件的空值配置",setters:"ArraySetter"},{name:"valueOnClear",title:"清空选项的值 ",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]},{name:"popperAppendToBody",title:"是否将弹出框插入至 body 元素 当弹出框的位置出现问题时，你可以尝试将该属性设置为false。",setters:"BooleanSetter",defaultValue:!1},{name:"tabindex",title:"input 的 tabindex",setters:["StringSetter","NumberSetter"]}],events:["change","visible-change","remove-tag","clear","blur","focus","update:modelValue"],slots:[{name:"default",params:["item"]},{name:"header"},{name:"footer"},{name:"prefix"},{name:"empty"},{name:"tag"},{name:"loading"},{name:"label"}]},D={name:"ElSlider",label:"滑块",childIncludes:!1,doc:"https://element-plus.org/zh-CN/component/slider.html",categoryId:"form",package:"element-plus",props:[{name:"modelValue",title:"选中项绑定值",defaultValue:0,setters:"NumberSetter"},{name:"min",title:"最小值",defaultValue:0,setters:"NumberSetter"},{name:"max",title:"最大值",defaultValue:100,setters:"NumberSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"step",title:"步长",defaultValue:1,setters:"NumberSetter"},{name:"showInput",title:"是否显示输入框，仅在非范围选择时有效",defaultValue:!1,setters:"BooleanSetter"},{name:"showInputControls",title:"在显示输入框的情况下，是否显示输入框的控制按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"size",defaultValue:"default",title:"slider 包装器的大小，垂直模式下该属性不可用",options:["large","default","small"],setters:"SelectSetter"},{name:"inputSize",title:"输入框的大小，如果设置了 size 属性，默认值自动取 size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"showStops",defaultValue:!1,title:"是否显示间断点",setters:"BooleanSetter"},{name:"showTooltip",defaultValue:!0,title:"是否显示提示信息",setters:"BooleanSetter"},{name:"formatTooltip",title:"格式化提示信息",setters:"FunctionSetter"},{name:"range",title:"是否开启选择范围",defaultValue:!1,setters:"BooleanSetter"},{name:"vertical",defaultValue:!1,title:"垂直模式",setters:"BooleanSetter"},{name:"height",defaultValue:"",title:"滑块高度，垂直模式必填",setters:"InputSetter"},{name:"aria-label",title:"原生 aria-label属性",defaultValue:"",setters:"InputSetter"},{name:"rangeStartLabel",defaultValue:"",title:"当 range 为true时，屏幕阅读器标签开始的标记",setters:"InputSetter"},{name:"rangeEndLabel",defaultValue:"",title:"当 range 为true时，屏幕阅读器标签结尾的标记",setters:"InputSetter"},{name:"formatValueText",title:"显示屏幕阅读器的 aria-valuenow 属性的格式",defaultValue:"",setters:"FunctionSetter"},{name:"debounce",defaultValue:300,title:"输入时的去抖延迟，毫秒，仅在 show-input 等于 true 时有效",setters:"NumberSetter"},{name:"tooltipClass",title:"tooltip 的自定义类名",defaultValue:"",setters:"InputSetter"},{name:"placement",title:"Tooltip 出现的位置",defaultValue:"top",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],setters:"SelectSetter"},{name:"marks",title:"标记， key 的类型必须为 number 且取值在闭区间 [min, max] 内，每个标记可以单独设置样式",defaultValue:"",setters:"JSONSetter"},{name:"validateEvent",title:"输入时是否触发表单的校验",defaultValue:!0,setters:"BooleanSetter"}],events:["change","input","update:modelValue"]},P={name:"ElSwitch",label:"开关",doc:"https://element-plus.org/zh-CN/component/switch.html",categoryId:"form",package:"element-plus",props:[{name:"modelValue",defaultValue:!1,setters:["BooleanSetter","NumberSetter","InputSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"width",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"inlinePrompt",defaultValue:!1,setters:"BooleanSetter"},{name:"activeIcon",defaultValue:"",setters:"InputSetter"},{name:"inactiveIcon",defaultValue:"",setters:"InputSetter"},{name:"activeActionIcon",defaultValue:"",setters:"InputSetter"},{name:"inactiveActionIcon",defaultValue:"",setters:"InputSetter"},{name:"activeText",defaultValue:"",setters:"InputSetter"},{name:"inactiveText",defaultValue:"",setters:"InputSetter"},{name:"activeValue",defaultValue:!0,setters:["BooleanSetter","InputSetter","NumberSetter"]},{name:"inactiveValue",defaultValue:!1,setters:["BooleanSetter","InputSetter","NumberSetter"]},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"},{name:"beforeChange",setters:["BooleanSetter","FunctionSetter"]},{name:"id",defaultValue:"",setters:"StringSetter"},{name:"tabindex",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"ariaLabel",defaultValue:"",setters:"StringSetter"},{name:"activeColor",defaultValue:"",setters:"ColorSetter"},{name:"inactiveColor",defaultValue:"",setters:"ColorSetter"},{name:"borderColor",defaultValue:"",setters:"ColorSetter"},{name:"label",defaultValue:"",setters:"StringSetter"}],events:["change","update:modelValue"],slots:["active-action","inactive-action"]},L={name:"ElTimePicker",label:"时间选择器",childIncludes:!1,doc:"https://element-plus.org/zh-CN/component/time-picker.html",categoryId:"form",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["NumberSetter","StringSetter","ArraySetter","ExpressionSetter"]},{name:"readonly",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"editable",defaultValue:!0,setters:"BooleanSetter"},{name:"clearable",defaultValue:!0,setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"startPlaceholder",defaultValue:"",setters:"InputSetter"},{name:"endPlaceholder",defaultValue:"",setters:"InputSetter"},{name:"isRange",defaultValue:!1,setters:"BooleanSetter"},{name:"arrowControl",defaultValue:!1,setters:"BooleanSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"rangeSeparator",defaultValue:"",setters:"InputSetter"},{name:"format",defaultValue:"HH:mm:ss",setters:"InputSetter"},{name:"defaultValue",defaultValue:"",setters:["InputSetter","ExpressionSetter"]},{name:"valueFormat",title:"可选，绑定值的格式。 不指定则绑定值为 Date 对象",defaultValue:"",setters:"StringSetter"},{name:"id",defaultValue:"",setters:["InputSetter"]},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"ariaLabel",defaultValue:"",setters:"InputSetter"},{name:"prefixIcon",defaultValue:"Clock",setters:"InputSetter"},{name:"clearIcon",defaultValue:"CircleClose",setters:"InputSetter"},{name:"disabledHours",defaultValue:"",setters:"FunctionSetter"},{name:"disabledMinutes",defaultValue:"",setters:"FunctionSetter"},{name:"disabledSeconds",defaultValue:"",setters:"FunctionSetter"},{name:"teleported",defaultValue:!0,setters:"BooleanSetter"},{name:"tabindex",label:"tabindex",title:"输入框的 tabindex",setters:["StringSetter","NumberSetter"],defaultValue:0},{name:"emptyValues",title:"组件的空值配置",setters:"ArraySetter"},{name:"valueOnClear",title:"清空选项的值",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]}],events:["change","blur","focus","clear","visible-change","update:modelValue"]},j={name:"ElTimeSelect",label:"时间选择",childIncludes:!1,doc:"https://element-plus.org/zh-CN/component/time-select.html",categoryId:"form",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["StringSetter","ExpressionSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"editable",defaultValue:!0,setters:"BooleanSetter"},{name:"clearable",defaultValue:!0,setters:"BooleanSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"placeholder",defaultValue:"",setters:"InputSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"light",options:["dark","light"],setters:"SelectSetter"},{name:"prefixIcon",defaultValue:"Clock",setters:"InputSetter"},{name:"clearIcon",defaultValue:"CircleClose",setters:"InputSetter"},{name:"start",defaultValue:"09:00",setters:"InputSetter"},{name:"end",defaultValue:"18:00",setters:"InputSetter"},{name:"step",defaultValue:"00:30",setters:"InputSetter"},{name:"minTime",defaultValue:"00:00",setters:"InputSetter"},{name:"maxTime",defaultValue:"",setters:"InputSetter"},{name:"format",defaultValue:"HH:mm",setters:"InputSetter"},{name:"emptyValues",title:"组件的空值配置",setters:"ArraySetter"},{name:"valueOnClear",title:"清空选项的值",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]}],events:["change","blur","focus","clear","update:modelValue"]},M={name:"ElTransfer",label:"穿梭框",categoryId:"form",doc:"https://element-plus.org/zh-CN/component/transfer.html",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["ArraySetter","ExpressionSetter"]},{name:"data",defaultValue:[],setters:["ArraySetter","JSONSetter"]},{name:"filterable",defaultValue:!1,setters:"BooleanSetter"},{name:"filterPlaceholder",defaultValue:"Enter keyword",setters:"InputSetter"},{name:"filterMethod",setters:"FunctionSetter"},{name:"targetOrder",defaultValue:"original",options:["original","push","unshift"],setters:"SelectSetter"},{name:"titles",defaultValue:[],setters:["ArraySetter","JSONSetter"]},{name:"buttonTexts",defaultValue:[],setters:["ArraySetter","JSONSetter"]},{name:"renderContent",defaultValue:"",setters:"FunctionSetter"},{name:"format",defaultValue:"",setters:["ObjectSetter","JSONSetter"]},{name:"props",defaultValue:"",setters:["ObjectSetter","JSONSetter"]},{name:"leftDefaultChecked",defaultValue:[],title:"初始状态下左侧列表的已勾选项的 key 数组",setters:["ArraySetter","JSONSetter"]},{name:"rightDefaultChecked",defaultValue:[],title:"初始状态下右侧列表的已勾选项的 key 数组",setters:["ArraySetter","JSONSetter"]},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"}],slots:[{name:"default",params:["options"]},{name:"left-footer"},{name:"right-footer"},{name:"left-empty"},{name:"right-empty"}],events:[{name:"change"},{name:"left-check-change"},{name:"right-check-change"},{name:"update:modelValue"}]},R={name:"ElUpload",label:"上传",doc:"https://element-plus.org/zh-CN/component/upload.html",categoryId:"form",package:"element-plus",props:[{name:"action",defaultValue:"#",title:"请求 URL",setters:"InputSetter"},{name:"headers",defaultValue:"",setters:"JSONSetter"},{name:"method",defaultValue:"post",setters:"InputSetter"},{name:"multiple",defaultValue:!1,setters:"BooleanSetter"},{name:"data",defaultValue:"",setters:"ExpressionSetter"},{name:"name",defaultValue:"file",setters:"ExpressionSetter"},{name:"withCredentials",defaultValue:!1,title:"支持发送 cookie 凭证信息",setters:"BooleanSetter"},{name:"showFileList",defaultValue:!0,setters:"BooleanSetter"},{name:"drag",defaultValue:!1,setters:"BooleanSetter"},{name:"accept",defaultValue:"",setters:"InputSetter"},{name:"crossorigin",options:["","anonymous","use-credentials"],setters:"SelectSetter"},{name:"onPreview",setters:"FunctionSetter"},{name:"onRemove",setters:"FunctionSetter"},{name:"onSuccess",setters:"FunctionSetter"},{name:"onError",setters:["FunctionSetter","ExpressionSetter"]},{name:"onProgress",setters:["FunctionSetter","ExpressionSetter"]},{name:"onChange",setters:"FunctionSetter"},{name:"onExceed",setters:"FunctionSetter"},{name:"beforeUpload",setters:"FunctionSetter"},{name:"beforeRemove",setters:"FunctionSetter"},{name:"fileList",setters:["ArraySetter","JSONSetter"]},{name:"listType",defaultValue:"text",options:["text","picture","picture-card"],setters:"SelectSetter"},{name:"autoUpload",defaultValue:!0,setters:"BooleanSetter"},{name:"httpRequest",setters:"FunctionSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"limit",defaultValue:"",setters:"NumberSetter"}],slots:[{name:"default"},{name:"trigger"},{name:"tip"},{name:"file"}],events:[{name:"update:fileList"}],snippet:{props:{action:"https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15",multiple:!0,fileList:[{name:"element-plus-logo.svg",url:"https://element-plus.org/images/element-plus-logo.svg"}]},children:[{name:"ElButton",props:{type:"primary"},children:"选择文件"}]}},H={name:"ElAvatar",label:"头像",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/avatar.html",package:"element-plus",props:[{name:"icon",defaultValue:"",setters:"IconSetter"},{name:"size",setters:["SelectSetter","NumberSetter"],options:["large","default","small"],defaultValue:"default"},{name:"shape",defaultValue:"circle",options:["circle","square"],setters:"SelectSetter"},{name:"src",defaultValue:"",setters:"InputSetter"},{name:"srcSet",defaultValue:"",setters:"InputSetter"},{name:"alt",defaultValue:"",setters:"InputSetter"},{name:"fit",defaultValue:"cover",options:["fill","contain","cover","none","scale-down"],setters:"SelectSetter"}],events:["error"],slots:["default","icon"],snippet:{props:{src:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}}},W={name:"ElBadge",label:"徽章",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/badge.html",package:"element-plus",props:[{name:"value",defaultValue:"",title:"显示值",setters:["InputSetter","NumberSetter"]},{name:"max",defaultValue:99,setters:"NumberSetter"},{name:"isDot",defaultValue:!1,title:"是否显示小圆点。",setters:"BooleanSetter"},{name:"hidden",defaultValue:!1,title:"是否隐藏 Badge。",setters:"BooleanSetter"},{name:"type",defaultValue:"danger",title:"badge 类型。",options:["primary","success","warning","danger","info"],setters:"SelectSetter"},{name:"showZero",title:"值为零时是否显示 Badge  ",setters:"BooleanSetter",defaultValue:!0},{name:"color",title:"背景色",setters:"ColorSetter"},{name:"offset",title:"badge 的偏移量",setters:"ArraySetter"},{name:"badgeStyle",title:"自定义 badge 样式",setters:"ObjectSetter"},{name:"badgeClass",title:"自定义 badge 类名",setters:"StringSetter"}],slots:["default","content"],snippet:{props:{value:12},children:[{name:"ElButton",children:"评论"}]}},$={name:"ElCalendar",label:"日历",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/calendar.html",package:"element-plus",props:[{name:"modelValue",defaultValue:"",setters:["StringSetter","ExpressionSetter"]},{name:"range",defaultValue:"",setters:["ArraySetter","JSONSetter"]}],events:["update:modelValue"],slots:[{name:"date-cell"},{name:"header"}]},G={name:"ElCard",label:"卡片",categoryId:"data",package:"element-plus",doc:"https://element-plus.org/zh-CN/component/card.html",props:[{name:"header",defaultValue:"",setters:"InputSetter"},{name:"footer",defaultValue:"",setters:"InputSetter"},{name:"bodyStyle",defaultValue:void 0,setters:"JSONSetter"},{name:"bodyClass",setters:"StringSetter"},{name:"shadow",defaultValue:"always",options:["always","hover","never"],setters:"SelectSetter"}],slots:[{name:"default"},{name:"header"},{name:"footer"}],snippet:{props:{header:"标题"},children:"内容文本"}},K=[{name:"ElCarousel",label:"走马灯",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/carousel.html",childIncludes:["ElCarouselItem"],package:"element-plus",props:[{name:"height",defaultValue:"",setters:"InputSetter"},{name:"initialIndex",defaultValue:0,setters:"NumberSetter"},{name:"trigger",defaultValue:"hover",options:["hover","click"],setters:"SelectSetter"},{name:"autoplay",defaultValue:!0,setters:"BooleanSetter"},{name:"interval",defaultValue:3e3,setters:"NumberSetter"},{name:"indicatorPosition",defaultValue:"",options:["","outside","none"],label:"指示器",setters:"InputSetter"},{name:"arrow",defaultValue:"hover",options:["always","hover","never"],setters:"SelectSetter"},{name:"type",defaultValue:"",options:["","card"],setters:"SelectSetter"},{name:"cardScale",defaultValue:.83,setters:"NumberSetter"},{name:"loop",defaultValue:!0,setters:"BooleanSetter"},{name:"direction",defaultValue:"horizontal",options:["horizontal","vertical"],setters:"SelectSetter"},{name:"pauseOnHover",defaultValue:!0,setters:"BooleanSetter"},{name:"motionBlur",defaultValue:!1,setters:"BooleanSetter"}],events:["change"],slots:["default"],snippet:{props:{height:"300px",style:{width:"100%"}},children:[{name:"ElCarouselItem",props:{style:{width:"100%"}},children:[{name:"component",props:{is:"img",style:{width:"100%",height:"300px"},src:"https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"}}],directives:[{name:"vFor",value:{type:"JSExpression",value:"3"}}]}]}},{name:"ElCarouselItem",label:"走马灯子项",categoryId:"data",package:"element-plus",props:[{name:"name",defaultValue:"",setters:"InputSetter"},{name:"label",defaultValue:"",setters:"InputSetter"}],slots:["default"],snippet:{props:{style:{width:"100%"}},children:[{name:"component",props:{is:"img",style:{width:"100%",height:"300px"},src:"https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"}}]}}],U=[{name:"ElCollapse",label:"折叠面板",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/collapse.html",childIncludes:["ElCollapseItem"],package:"element-plus",props:[{name:"modelValue",defaultValue:"",title:"当前激活的面板(如果是手风琴模式，绑定值类型需要为string，否则为array)",setters:["InputSetter","ArraySetter","JSONSetter"]},{name:"accordion",defaultValue:!1,title:"是否手风琴模式",setters:"BooleanSetter"}],events:["change"],slots:["default"],snippet:{children:[{name:"ElCollapseItem",children:"面板内容",props:{title:"面板标题"},directives:[{name:"vFor",value:{type:"JSExpression",value:"3"}}]}]}},{name:"ElCollapseItem",label:"折叠面板子项",categoryId:"data",package:"element-plus",props:[{name:"name",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"title",defaultValue:"",setters:"InputSetter"},{name:"icon",defaultValue:"ArrowRight",setters:"StringSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],events:["change"],slots:[{name:"default"},{name:"title"},{name:"icon"}],snippet:{children:"面板内容",props:{title:"面板标题"}}}],q=[{name:"ElDescriptions",label:"描述列表",categoryId:"data",package:"element-plus",doc:"https://element-plus.org/zh-CN/component/descriptions.html",props:[{name:"border",defaultValue:!1,setters:"BooleanSetter"},{name:"column",defaultValue:3,setters:"NumberSetter"},{name:"direction",defaultValue:"horizontal",options:["vertical","horizontal"],setters:"SelectSetter"},{name:"size",defaultValue:"",options:["","large","default","small"],setters:"SelectSetter"},{name:"title",defaultValue:"",setters:"InputSetter"},{name:"extra",defaultValue:"",setters:"InputSetter"},{name:"labelWidth",defaultValue:"",setters:["StringSetter","NumberSetter"]}],slots:["default","title","extra"],snippet:{props:{border:!0},children:[{name:"ElDescriptionsItem",children:"kooriookami",props:{label:"Username"}},{name:"ElDescriptionsItem",children:"18100000000",props:{label:"Telephone"}},{name:"ElDescriptionsItem",children:"Suzhou",props:{label:"Place"}},{name:"ElDescriptionsItem",children:"No.1188, Wuzhong Avenue, Wuzhong District, Suzhou, Jiangs Province",props:{label:"Address"}}]}},{name:"ElDescriptionsItem",label:"描述列表子项",categoryId:"data",parentIncludes:["ElDescriptions"],package:"element-plus",props:[{name:"label",defaultValue:"",setters:"InputSetter"},{name:"span",defaultValue:1,setters:"NumberSetter"},{name:"rowspan",defaultValue:1,setters:"NumberSetter"},{name:"width",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"min-width",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"labelWidth",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"align",defaultValue:"left",options:["left","center","right"],setters:"SelectSetter"},{name:"label-align",defaultValue:"",options:["left","center","right"],setters:"SelectSetter"},{name:"class-name",defaultValue:"",setters:"InputSetter"},{name:"label-class-name",defaultValue:"",label:"标题类名",setters:"InputSetter"}],slots:["default","label"],snippet:{children:"内容",props:{label:"标题"}}}],Y={name:"ElEmpty",label:"空状态",doc:"https://element-plus.org/zh-CN/component/empty.html",categoryId:"data",package:"element-plus",props:[{name:"image",defaultValue:"",setters:"InputSetter"},{name:"imageSize",defaultValue:"",setters:"NumberSetter"},{name:"description",defaultValue:"",setters:"InputSetter"}],slots:[{name:"default"},{name:"image"},{name:"description"}]},_={name:"ElImage",label:"图片",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/image.html",package:"element-plus",props:[{name:"src",defaultValue:"",setters:"InputSetter"},{name:"fit",defaultValue:"",options:["","fill","contain","cover","none","scale-down"],setters:"SelectSetter"},{name:"hideOnClickModal",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:"",options:["eager","lazy"],setters:"SelectSetter"},{name:"lazy",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollContainer",defaultValue:"",setters:"InputSetter"},{name:"alt",defaultValue:"",setters:"InputSetter"},{name:"referrerPolicy",defaultValue:"",setters:"InputSetter"},{name:"crossorigin",label:"crossorigin",title:"原生属性 crossorigin",setters:"SelectSetter",options:["","anonymous","use-credentials"]},{name:"previewSrcList",defaultValue:"",setters:["ArraySetter","JSONSetter"]},{name:"zIndex",defaultValue:"",setters:"NumberSetter"},{name:"initialIndex",defaultValue:0,setters:"NumberSetter"},{name:"close-on-press-escape",label:"close-on-press-escape",defaultValue:!0,title:"是否可以通过按下 ESC 关闭 Image Viewer",setters:"BooleanSetter"},{name:"previewTeleported",defaultValue:!1,setters:"BooleanSetter"},{name:"infinite",defaultValue:!0,setters:"BooleanSetter",title:"是否可以无限循环预览"},{name:"zoomRate",defaultValue:1.2,setters:{name:"NumberSetter",props:{precision:1}},title:"图像查看器缩放事件的缩放速率"},{name:"minScale",defaultValue:.2,setters:{name:"NumberSetter",props:{precision:1}},title:"图像查看器缩放事件的最小缩放比例"},{name:"maxScale",defaultValue:7,setters:{name:"NumberSetter",props:{precision:1}},title:"图像查看器缩放事件的最大缩放比例"}],events:["load","error","switch","close","show"],slots:["placeholder","error","viewer"],snippet:{props:{style:{width:"100px",height:"100px"},src:"https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg",previewSrcList:["https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg","https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg","https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg","https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg","https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg","https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg","https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg"]}}},Q={name:"ElPagination",label:"分页",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/pagination.html",package:"element-plus",props:[{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"background",defaultValue:!1,setters:"BooleanSetter"},{name:"pageSize",defaultValue:10,setters:"NumberSetter"},{name:"defaultPageSize",label:"默认页大小",defaultValue:void 0,setters:"NumberSetter"},{name:"total",defaultValue:void 0,title:"总条目数",setters:"NumberSetter"},{name:"pageCount",defaultValue:void 0,setters:"NumberSetter"},{name:"pagerCount",defaultValue:7,setters:"NumberSetter"},{name:"currentPage",defaultValue:1,setters:"NumberSetter"},{name:"defaultCurrentPage",label:"默认当前页",defaultValue:void 0,setters:"NumberSetter"},{name:"layout",defaultValue:"prev, pager, next, jumper, ->, total",setters:"InputSetter"},{name:"pageSizes",defaultValue:[10,20,30,40,50,100],setters:["ArraySetter","JSONSetter"]},{name:"appendSizeTo",title:"下拉框挂载到哪个 DOM 元素",setters:"StringSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"prevText",defaultValue:"",setters:"InputSetter"},{name:"prevIcon",defaultValue:"ArrowLeft",setters:"InputSetter"},{name:"nextText",defaultValue:"",setters:"InputSetter"},{name:"nextIcon",defaultValue:"ArrowRight",setters:"InputSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"teleported ",title:"是否将下拉菜单teleport至 body",setters:"BooleanSetter",defaultValue:!0},{name:"hideOnSinglePage",defaultValue:!1,setters:"BooleanSetter"},{name:"small",defaultValue:!1,setters:"BooleanSetter"}],events:["size-change","current-change","change","prev-click","next-click","update:pageSize","update:currentPage"],slots:["default","prevIcon","nextIcon"],snippet:{props:{total:1e3,background:!0}}},Z={name:"ElProgress",label:"进度条",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/progress.html",package:"element-plus",props:[{name:"percentage",defaultValue:0,title:"百分比，必填",setters:{name:"NumberSetter",props:{min:0,max:100}}},{name:"type",defaultValue:"line",options:["line","circle","dashboard"],setters:"SelectSetter"},{name:"strokeWidth",defaultValue:6,setters:"NumberSetter"},{name:"textInside",defaultValue:!1,setters:"BooleanSetter"},{name:"status",defaultValue:"",options:["success","exception","warning"],setters:"SelectSetter"},{name:"indeterminate",defaultValue:!1,setters:"BooleanSetter"},{name:"duration",defaultValue:3,setters:"NumberSetter"},{name:"color",defaultValue:"",setters:["ColorSetter","FunctionSetter","ArraySetter","JSONSetter"]},{name:"width",defaultValue:126,setters:"NumberSetter"},{name:"showText",defaultValue:!0,setters:"BooleanSetter"},{name:"strokeLinecap",defaultValue:"round",options:["butt","round","square"],setters:"SelectSetter"},{name:"format",defaultValue:"",setters:"FunctionSetter"},{name:"striped",label:"striped",title:"在进度条上增加条纹",setters:"BooleanSetter",defaultValue:!1},{name:"stripedFlow",label:"stripedFlow",title:"让进度条上的条纹流动起来",setters:"BooleanSetter",defaultValue:!1}],slots:["default"],snippet:{name:"ElProgress",props:{percentage:50}}},X={name:"ElResult",label:"结果",doc:"https://element-plus.org/zh-CN/component/result.html",categoryId:"data",package:"element-plus",props:[{name:"title",defaultValue:"",setters:"InputSetter"},{name:"subTitle",defaultValue:"",setters:"InputSetter"},{name:"icon",defaultValue:"info",options:["success","warning","info","error"],setters:"SelectSetter"}],slots:["icon","title","sub-title","extra"],snippet:{props:{icon:"success",title:"Success Tip",subTitle:"Please follow the instructions"}}},ee=[{name:"ElSkeleton",label:"骨架屏",doc:"https://element-plus.org/zh-CN/component/skeleton.html",categoryId:"data",package:"element-plus",props:[{name:"animated",defaultValue:!1,setters:"BooleanSetter"},{name:"count",defaultValue:1,setters:"NumberSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"rows",defaultValue:3,setters:"NumberSetter"},{name:"throttle",defaultValue:0,setters:["NumberSetter","ObjectSetter"]}],slots:[{name:"default",params:["object"]},{name:"template",params:["object"]}]},{name:"ElSkeletonItem",label:"骨架项",doc:"https://element-plus.org/zh-CN/component/skeleton.html",categoryId:"data",package:"element-plus",props:[{name:"variant",setters:"SelectSetter",options:["p","text","h1","h3","caption","button","image","circle","rect"],defaultValue:"text"}],snippet:{props:{variant:"image",style:{width:"240px",height:"240px"}}}}],te=[{name:"ElTable",label:"表格",doc:"https://element-plus.org/zh-CN/component/table.html",categoryId:"data",package:"element-plus",props:[{name:"data",defaultValue:"",setters:["ArraySetter","JSONSetter"]},{name:"height",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"maxHeight",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"stripe",defaultValue:!1,setters:"BooleanSetter"},{name:"border",defaultValue:!1,setters:"BooleanSetter"},{name:"size",defaultValue:"",options:["","large","default","small"],setters:"SelectSetter"},{name:"fit",defaultValue:!0,title:"列的宽度是否自撑开",setters:"BooleanSetter"},{name:"showHeader",defaultValue:!0,setters:"BooleanSetter"},{name:"highlightCurrentRow",defaultValue:!1,label:"是否高亮",setters:"BooleanSetter"},{name:"currentRowKey",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"rowClassName",defaultValue:"",setters:["InputSetter","FunctionSetter"]},{name:"rowStyle",defaultValue:"",setters:["JSONSetter","FunctionSetter"]},{name:"cellClassName",defaultValue:"",setters:["InputSetter","FunctionSetter"]},{name:"cellStyle",defaultValue:"",setters:["JSONSetter","FunctionSetter"]},{name:"headerRowClassName",defaultValue:"",title:"表头行类名",label:"RowClassName",setters:["InputSetter","FunctionSetter"]},{name:"headerRowStyle",defaultValue:"",setters:["JSONSetter","FunctionSetter"]},{name:"headerCellClassName",defaultValue:"",title:"表头单元格类名",label:"CellClassName",setters:["InputSetter","FunctionSetter"]},{name:"headerCellStyle",defaultValue:"",setters:["JSONSetter","FunctionSetter"]},{name:"rowKey",defaultValue:"",setters:["InputSetter","FunctionSetter"]},{name:"emptyText",defaultValue:"",setters:"InputSetter"},{name:"defaultExpandAll",defaultValue:!1,label:"展开所有",setters:"BooleanSetter"},{name:"expand-row-keys",defaultValue:"",title:"可以通过该属性设置 Table 目前的展开行，需要设置 row-key 属性才能使用，该属性为展开行的 keys 数组。",label:"row-keys",setters:"JSONSetter"},{name:"default-sort",defaultValue:"",setters:"JSONSetter"},{name:"tooltip-effect",defaultValue:"dark",options:["dark","light"],setters:"SelectSetter"},{name:"tooltip-options",defaultValue:{enterable:!0,placement:"top",showArrow:!0,hideAfter:200,popperOptions:{strategy:"fixed"}},setters:"ObjectSetter"},{name:"show-summary",defaultValue:!1,label:"显示合计行",setters:"BooleanSetter"},{name:"sum-text",defaultValue:"合计",setters:"InputSetter"},{name:"summary-method",defaultValue:"",label:"合计计算方法",setters:"FunctionSetter"},{name:"span-method",defaultValue:"",setters:"FunctionSetter"},{name:"select-on-indeterminate",defaultValue:!0,title:"在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为。 若为 true，则选中所有行；若为 false，则取消选择所有行",label:"indeterminate",setters:"BooleanSetter"},{name:"indent",defaultValue:16,setters:"NumberSetter"},{name:"lazy",defaultValue:!1,setters:"BooleanSetter"},{name:"load",defaultValue:"",setters:"FunctionSetter"},{name:"tree-props",defaultValue:{hasChildren:"hasChildren",children:"children"},setters:"JSONSetter"},{name:"tableLayout",defaultValue:"fixed",title:"设置表格单元、行和列的布局方式",options:["fixed","auto"],setters:"SelectSetter"},{name:"scrollbar-always-on",defaultValue:!1,label:"显示滚动条",setters:"BooleanSetter"},{name:"show-overflow-tooltip",setters:["BooleanSetter","JSONSetter"]},{name:"flexible",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollbarTabindex",setters:["StringSetter","NumberSetter"]},{name:"allowDragLastColumn",defaultValue:!0,setters:"BooleanSetter"}],events:["select","select-all","selection-change","cell-mouse-enter","cell-mouse-leave","cell-click","cell-dblclick","cell-contextmenu","row-click","row-contextmenu","row-dblclick","header-click","header-contextmenu","sort-change","filter-change","current-change","header-dragend","expand-change","scroll"],slots:["default","append","empty"],snippet:{name:"ElTable",props:{data:i()},children:[{name:"ElTableColumn",props:{prop:"date",label:"Date"}},{name:"ElTableColumn",props:{prop:"name",label:"Name"}},{name:"ElTableColumn",props:{prop:"address",label:"Address"}}]}},{name:"ElTableColumn",label:"表头",categoryId:"data",package:"element-plus",props:[{name:"type",defaultValue:"default",options:["default","selection","index","expand"],setters:"SelectSetter"},{name:"index",defaultValue:0,title:"如果设置了 type=index，可以通过传递 index 属性来自定义索引",setters:["NumberSetter","FunctionSetter"]},{name:"label",defaultValue:"",setters:"InputSetter"},{name:"column-key",defaultValue:"",setters:"InputSetter"},{name:"prop",defaultValue:"",setters:"InputSetter"},{name:"width",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"min-width",defaultValue:"",title:"",setters:["InputSetter","NumberSetter"]},{name:"fixed",defaultValue:"",options:["left","right"],setters:["SelectSetter","BooleanSetter"]},{name:"render-header",defaultValue:"",setters:"FunctionSetter"},{name:"sortable",defaultValue:!1,options:["","custom"],setters:["BooleanSetter","SelectSetter"]},{name:"sort-method",defaultValue:"",setters:"FunctionSetter"},{name:"sort-by",defaultValue:"",setters:["InputSetter","FunctionSetter","JSONSetter"]},{name:"sort-orders",defaultValue:["ascending","descending",null],title:"数据在排序时所使用排序策略的轮转顺序，仅当 sortable 为 true 时有效。 需传入一个数组，随着用户点击表头，该列依次按照数组中元素的顺序进行排序",setters:"JSONSetter"},{name:"resizable",defaultValue:!0,setters:"BooleanSetter"},{name:"formatter",defaultValue:"",setters:"FunctionSetter"},{name:"show-overflow-tooltip",defaultValue:void 0,title:"当内容过长被隐藏时显示 tooltip",setters:["BooleanSetter","JSONSetter"]},{name:"align",defaultValue:"left",options:["left","center","right"],setters:"SelectSetter"},{name:"header-align",defaultValue:"left",options:["left","center","right"],setters:"SelectSetter"},{name:"class-name",defaultValue:"",setters:"InputSetter"},{name:"label-class-name",defaultValue:"",title:"当前列标题的自定义类名",setters:"InputSetter"},{name:"selectable",setters:"FunctionSetter"},{name:"reserve-selection",defaultValue:!1,title:"仅对  type=selection 的列有效， 请注意， 需指定 row-key 来让这个功能生效。",setters:"BooleanSetter"},{name:"filters",defaultValue:"",setters:"JSONSetter"},{name:"filter-placement",defaultValue:"",title:"过滤弹出框的定位,与 Tooltip 的 placement 属性相同",setters:"InputSetter"},{name:"filter-class-name",defaultValue:"",setters:"StringSetter"},{name:"filter-multiple",defaultValue:!0,setters:"BooleanSetter"},{name:"filter-method",defaultValue:"",setters:"FunctionSetter"},{name:"filtered-value",defaultValue:"",setters:"JSONSetter"}],slots:[{name:"default",params:["row","column","$index"]},{name:"header",params:["column","$index"]},{name:"filter-icon",params:["filterOpened"]}],snippet:{props:{label:"列名"}}}],ae={name:"ElTableV2",label:"虚拟化表格",categoryId:"data",doc:"https://element-plus.org/zh-CN/component/table-v2.html",package:"element-plus",props:[{name:"cache",defaultValue:2,setters:"NumberSetter"},{name:"estimated-row-height",defaultValue:"",title:"渲染动态的单元格的预估高度",label:"row-height",setters:"NumberSetter"},{name:"header-class",defaultValue:"",setters:["InputSetter","ExpressionSetter"]},{name:"header-props",defaultValue:"",setters:["JSONSetter","ExpressionSetter"]},{name:"header-cell-props",defaultValue:"",setters:["JSONSetter","ExpressionSetter"]},{name:"header-height",defaultValue:50,setters:["NumberSetter","ExpressionSetter"]},{name:"footer-height",defaultValue:0,setters:"NumberSetter"},{name:"row-class",defaultValue:"",setters:["InputSetter","ExpressionSetter"]},{name:"row-key",defaultValue:"id",setters:["InputSetter","NumberSetter"]},{name:"row-props",defaultValue:"",setters:["JSONSetter","ExpressionSetter"]},{name:"columns",defaultValue:"50",setters:"NumberSetter"},{name:"data",defaultValue:[],setters:"JSONSetter"},{name:"data-getter",defaultValue:"",setters:"ExpressionSetter"},{name:"fixed-data",defaultValue:"",setters:"JSONSetter"},{name:"expand-column-key",defaultValue:"",title:"列的 key 来标记哪个行可以被展开",label:"column-key",setters:"InputSetter"},{name:"expanded-row-keys",defaultValue:"",title:"列的 key 来标记哪个行可以被展开",label:"column-key",setters:"JSONSetter"},{name:"default-expanded-row-keys",defaultValue:"",title:"默认展开的行的 key 的数组, 这个数据不是响应式的",label:"默认展开行",setters:"JSONSetter"},{name:"class",defaultValue:"",setters:["JSONSetter","InputSetter"]},{name:"fixed",defaultValue:!1,setters:"BooleanSetter"},{name:"width",defaultValue:"",label:"表宽必填",setters:"NumberSetter"},{name:"height",defaultValue:"",label:"表高必填",setters:"NumberSetter"},{name:"max-height",defaultValue:"",setters:"NumberSetter"},{name:"h-scrollbar-size",defaultValue:6,label:"水平滚动条大小",setters:"NumberSetter"},{name:"h-scrollbar-size",defaultValue:6,label:"垂直滚动条大小",setters:"NumberSetter"},{name:"scrollbar-always-on",defaultValue:!1,title:"如果开启，滚动条将一直显示，反之只会在鼠标经过时显示。",label:"scrollbar",setters:"BooleanSetter"},{name:"sort-by",defaultValue:{},title:"排序方式",setters:"JSONSetter"},{name:"sort-state",defaultValue:void 0,title:"多个排序",setters:"JSONSetter"}],slots:["cell","header","header-cell","row","footer","empty","overlay"],events:["column-sort","expanded-rows-change","end-reached","scroll","rows-rendered","row-event-handlers"],snippet:{props:{width:700,height:400}}},le=[{name:"ElTag",label:"标签",doc:"https://element-plus.org/zh-CN/component/tag.html",categoryId:"data",package:"element-plus",props:[{name:"type",defaultValue:"primary",options:["primary","success","info","warning","danger"],setters:"SelectSetter"},{name:"closable",defaultValue:!1,setters:"BooleanSetter"},{name:"disable-transitions",defaultValue:!1,label:"渐变动画",setters:"BooleanSetter"},{name:"hit",defaultValue:!1,setters:"BooleanSetter"},{name:"color",defaultValue:"",setters:"ColorSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"effect",defaultValue:"light",options:["dark","light","plain"],setters:"SelectSetter"},{name:"round",defaultValue:!1,setters:"BooleanSetter"}],events:["click","close"],slots:["default"],snippet:{children:"标签一"}},{name:"ElCheckTag",label:"可选中的标签",categoryId:"data",package:"element-plus",props:[{name:"checked",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"type",label:"type",title:"CheckTag 类型",setters:"SelectSetter",options:["primary","success","info","warning","danger"],defaultValue:"primary"}],events:["change","update:checked"],slots:["default"],snippet:{children:"标签一"}}],re=[{name:"ElTimeline",label:"时间线",doc:"https://element-plus.org/zh-CN/component/timeline.html",categoryId:"form",package:"element-plus",slots:["default"],snippet:{name:"ElTimeline",children:[{name:"ElTimelineItem",children:"Event start",props:{timestamp:"2018-04-15",size:"large",type:"primary",icon:"MoreFilled"}},{name:"ElTimelineItem",children:"Approved",props:{timestamp:"2018-04-13",color:"#0bbd87"}},{name:"ElTimelineItem",children:"Success",props:{timestamp:"2018-04-11",hollow:!0,icon:"el-icon-more"}}]}},{name:"ElTimelineItem",label:"时间线子项",categoryId:"form",package:"element-plus",props:[{name:"timestamp",defaultValue:"",setters:"InputSetter"},{name:"hide-timestamp",defaultValue:!1,setters:"BooleanSetter"},{name:"center",defaultValue:!1,setters:"BooleanSetter"},{name:"placement",defaultValue:"bottom",options:["top","bottom"],setters:"SelectSetter"},{name:"type",defaultValue:"",options:["primary","success","warning","danger","info"],setters:"SelectSetter"},{name:"color",defaultValue:"",options:["hsl","hsv","hex","rgb"],setters:"SelectSetter"},{name:"size",defaultValue:"normal",options:["normal","large"],setters:"SelectSetter"},{name:"icon",defaultValue:"",setters:"IconSetter"},{name:"hollow",defaultValue:!1,setters:"BooleanSetter"}],slots:["default","dot"]}],se=[{name:"ElTour",label:"漫游式引导",doc:"https://element-plus.org/zh-CN/component/tour.html",categoryId:"data",package:"element-plus",props:[{name:"showArrow",label:"showArrow",title:"是否显示箭头",setters:"BooleanSetter",defaultValue:!0},{name:"placement",label:"placement",title:"引导卡片相对于目标元素的位置",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],defaultValue:"bottom"},{name:"contentStyle",label:"contentStyle",title:"为content自定义样式",setters:"ObjectSetter"},{name:"mask",label:"mask",title:"是否启用遮罩，通过自定义属性改变遮罩样式以及填充的颜色",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"type",label:"type",title:"类型，影响底色与文字颜色",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"modelValue",label:"modelValue",title:"打开引导",setters:"BooleanSetter",defaultValue:!1},{name:"current",label:"current",title:"当前值",setters:"NumberSetter"},{name:"scroll-into-view-options",label:"scroll-into-view-options",title:"是否支持当前元素滚动到视窗内，也可传入配置指定滚动视窗的相关参数",setters:["BooleanSetter","ObjectSetter"],defaultValue:{block:"center"}},{name:"z-index",label:"z-index",title:"Tour 的层级",setters:"NumberSetter",defaultValue:2001},{name:"show-close",label:"show-close",title:"是否显示关闭按钮",setters:"BooleanSetter",defaultValue:!0},{name:"close-icon",label:"close-icon",title:"自定义关闭图标",setters:"IconSetter",defaultValue:"Close"},{name:"close-on-press-escape",label:"close-on-press-escape",title:"是否可以通过按下 ESC 关闭引导",setters:"BooleanSetter",defaultValue:!0},{name:"target-area-clickable",label:"target-area-clickable",title:"启用蒙层时，target 元素区域是否可以点击。",setters:"BooleanSetter",defaultValue:!0}],slots:[{name:"default"},{name:"indicators",params:["current","total"]},{name:"close-icon"}],events:["close","finish","change","update:modelValue","update:current"],snippet:{props:{modelValue:!0},children:[{name:"ElTourStep",props:{title:"Center",description:"Displayed in the center of screen."}},{name:"ElTourStep",props:{title:"Center",description:"Displayed in the center of screen."}},{name:"ElTourStep",props:{title:"Center",description:"Displayed in the center of screen."}}]}},{name:"ElTourStep",label:"漫游式引导步",parentIncludes:["ElTour"],doc:"https://element-plus.org/zh-CN/component/tour.html",categoryId:"data",package:"element-plus",props:[{name:"target",label:"target",title:"获取引导卡片指向的元素， 为空时居中于屏幕。",setters:["StringSetter","FunctionSetter"]},{name:"showArrow",label:"showArrow",title:"是否显示箭头",setters:"BooleanSetter",defaultValue:!0},{name:"title",label:"title",title:"标题",setters:"StringSetter"},{name:"description",label:"description",title:"主要描述部分",setters:"StringSetter"},{name:"placement",label:"placement",title:"引导卡片相对于目标元素的位置",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],defaultValue:"bottom"},{name:"contentStyle",label:"contentStyle",title:"为content自定义样式",setters:"ObjectSetter"},{name:"mask",label:"mask",title:"是否启用蒙层，也可传入配置改变蒙层样式和填充色",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"type",label:"type",title:"类型，影响底色与文字颜色",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"nextButtonProps",label:"nextButtonProps",title:"“下一步”按钮的属性",setters:"ObjectSetter"},{name:"prev-button-props",label:"prev-button-props",title:"“上一步”按钮的属性",setters:"ObjectSetter"},{name:"scrollIntoViewOptions",label:"scrollIntoViewOptions",title:"是否支持当前元素滚动到视窗内，也可传入配置指定滚动视窗的相关参数",setters:["BooleanSetter","ObjectSetter"]},{name:"showClose",label:"showClose",title:"是否显示关闭按钮",setters:"BooleanSetter",defaultValue:!0},{name:"closeIcon",label:"closeIcon",title:"自定义关闭图标",setters:["IconSetter"]}],slots:[{name:"default"},{name:"header"},{name:"close-icon"}],events:["close"],snippet:{props:{title:"Center",description:"Displayed in the center of screen."}}}],ne={name:"ElTree",label:"树形控件",doc:"https://element-plus.org/zh-CN/component/tree.html",categoryId:"data",package:"element-plus",props:[{name:"data",defaultValue:"",setters:"JSONSetter"},{name:"empty-text",defaultValue:"",setters:"InputSetter"},{name:"node-key",defaultValue:"",setters:"InputSetter"},{name:"props",defaultValue:"",setters:"JSONSetter"},{name:"render-after-expand",defaultValue:!0,setters:"BooleanSetter"},{name:"load",defaultValue:"",setters:"FunctionSetter"},{name:"render-content",defaultValue:"",setters:"FunctionSetter"},{name:"highlight-current",defaultValue:!1,label:"高亮当前节点",setters:"BooleanSetter"},{name:"default-expand-all",defaultValue:!1,label:"展开所有节点",setters:"BooleanSetter"},{name:"expand-on-click-node",defaultValue:!0,title:"是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。",label:"点击展开",setters:"BooleanSetter"},{name:"check-on-click-node",defaultValue:!1,title:"是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点。",label:"点击选中",setters:"BooleanSetter"},{name:"auto-expand-parent",defaultValue:!0,title:"展开子节点的时候是否自动展开父节点",label:"自动展开",setters:"BooleanSetter"},{name:"default-expanded-keys",defaultValue:"",title:"默认展开的节点的 key 的数组",label:"默认展开",setters:"JSONSetter"},{name:"show-checkbox",defaultValue:!1,label:"显示checkbox",setters:"BooleanSetter"},{name:"check-strictly",defaultValue:!1,setters:"BooleanSetter"},{name:"default-checked-keys",defaultValue:"",title:"默认勾选的节点的 key 的数组",label:"默认勾选",setters:"JSONSetter"},{name:"current-node-key",defaultValue:"",label:"当前节点key",setters:["InputSetter","NumberSetter"]},{name:"filter-node-method",defaultValue:"",title:"filter-node-method 对树节点进行筛选时执行的方法， 返回 false 则表示这个节点会被隐藏",label:"筛选节点函数",setters:"FunctionSetter"},{name:"accordion",defaultValue:!1,setters:"BooleanSetter"},{name:"indent",defaultValue:16,setters:"NumberSetter"},{name:"icon",defaultValue:"",setters:"InputSetter"},{name:"lazy",defaultValue:!1,setters:"BooleanSetter"},{name:"draggable",defaultValue:!1,setters:"BooleanSetter"},{name:"allow-drag",defaultValue:"",title:"判断节点能否被拖拽 如果返回 false ，节点不能被拖动",setters:"FunctionSetter"},{name:"allow-drop",defaultValue:"",title:"拖拽时判定目标节点能否成为拖动目标位置。 如果返回 false ，拖动节点不能被拖放到目标节点。 type 参数有三种情况：prev、inner 和 next，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后",setters:"FunctionSetter"}],events:["node-click","node-contextmenu","check-change","check","current-change","node-expand","node-collapse","node-drag-start","node-drag-enter","node-drag-leave","node-drag-over","node-drop","node-drag-end"],slots:[{name:"default",params:["node","data"]},{name:"empty"}],snippet:{props:{data:r()}}},oe={name:"ElTreeSelect",label:"树形选择",doc:"https://element-plus.org/zh-CN/component/tree-select.html",categoryId:"data",package:"element-plus",props:[{name:"cacheData",label:"cacheData",title:"懒加载节点的缓存数据，结构与数据相同，用于获取未加载数据的标签",setters:"ArraySetter",defaultValue:[]},{name:"data",defaultValue:"",setters:"JSONSetter"},{name:"empty-text",defaultValue:"",setters:"InputSetter"},{name:"node-key",defaultValue:"",setters:"InputSetter"},{name:"props",defaultValue:"",setters:"JSONSetter"},{name:"renderAfterExpand",defaultValue:!0,setters:"BooleanSetter"},{name:"load",defaultValue:"",setters:"FunctionSetter"},{name:"renderContent",defaultValue:"",setters:"FunctionSetter"},{name:"highlightCurrent",defaultValue:!1,setters:"BooleanSetter"},{name:"defaultExpandAll",defaultValue:!1,setters:"BooleanSetter"},{name:"expandOnClickNode",defaultValue:!0,title:"是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。",setters:"BooleanSetter"},{name:"checkOnClickNode",defaultValue:!1,title:"是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点。",setters:"BooleanSetter"},{name:"autoExpandParent",defaultValue:!0,title:"展开子节点的时候是否自动展开父节点",setters:"BooleanSetter"},{name:"defaultExpandedKeys",defaultValue:"",title:"默认展开的节点的 key 的数组",label:"默认展开",setters:"JSONSetter"},{name:"showCheckbox",defaultValue:!1,label:"显示checkbox",setters:"BooleanSetter"},{name:"checkStrictly",defaultValue:!1,setters:"BooleanSetter"},{name:"defaultCheckedKeys",defaultValue:"",title:"默认勾选的节点的 key 的数组",label:"默认勾选",setters:"JSONSetter"},{name:"currentNodeKey",defaultValue:"",label:"当前节点key",setters:["InputSetter","NumberSetter"]},{name:"filterNodeMethod",defaultValue:"",title:"filter-node-method 对树节点进行筛选时执行的方法， 返回 false 则表示这个节点会被隐藏",label:"筛选节点函数",setters:"FunctionSetter"},{name:"accordion",defaultValue:!1,setters:"BooleanSetter"},{name:"indent",defaultValue:16,setters:"NumberSetter"},{name:"icon",defaultValue:"",setters:"InputSetter"},{name:"lazy",defaultValue:!1,setters:"BooleanSetter"},{name:"draggable",defaultValue:!1,setters:"BooleanSetter"},{name:"allowDrag",defaultValue:"",title:"判断节点能否被拖拽 如果返回 false ，节点不能被拖动",setters:"FunctionSetter"},{name:"allowDrop",defaultValue:"",title:"拖拽时判定目标节点能否成为拖动目标位置。 如果返回 false ，拖动节点不能被拖放到目标节点。 type 参数有三种情况：prev、inner 和 next，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后",setters:"FunctionSetter"},{name:"modelValue",defaultValue:"",setters:["StringSetter","NumberSetter","BooleanSetter","ObjectSetter","ArraySetter"]},{name:"multiple",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"valueKey",defaultValue:"value",setters:"InputSetter"},{name:"size",defaultValue:"default",options:["large","default","small"],setters:"SelectSetter"},{name:"clearable",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTags",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTagsTooltip",label:"Tooltip",title:"当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签。 要使用此属性，collapse-tags属性必须设定为 true",defaultValue:!1,setters:"BooleanSetter"},{name:"multipleLimit",defaultValue:0,setters:"NumberSetter"},{name:"name",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"light",options:["dark","light"],setters:"SelectSetter"},{name:"autocomplete",defaultValue:"off",setters:"InputSetter"},{name:"placeholder",defaultValue:"Select",setters:"InputSetter"},{name:"filterable",defaultValue:!1,setters:"BooleanSetter"},{name:"allowCreate",defaultValue:!1,setters:"BooleanSetter"},{name:"filterMethod",defaultValue:"",setters:"FunctionSetter"},{name:"remote",defaultValue:!1,setters:"BooleanSetter"},{name:"remoteMethod",defaultValue:"",setters:"FunctionSetter"},{name:"remoteShowSuffix",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",defaultValue:!1,setters:"BooleanSetter"},{name:"loadingText",defaultValue:"Loading",setters:"InputSetter"},{name:"noMatchText",defaultValue:"No matching data",setters:"InputSetter"},{name:"noDataText",defaultValue:"No data",setters:"InputSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"reserveKeyword",defaultValue:!0,setters:"BooleanSetter"},{name:"defaultFirstOption",defaultValue:!1,setters:"BooleanSetter"},{name:"popperAppendToBody",defaultValue:!0,setters:"BooleanSetter"},{name:"teleported",defaultValue:!0,setters:"BooleanSetter"},{name:"appendTo",setters:"StringSetter"},{name:"persistent",defaultValue:!0,setters:"BooleanSetter"},{name:"automaticDropdown",defaultValue:!1,setters:"BooleanSetter"},{name:"clearIcon",defaultValue:"CircleClose",setters:"InputSetter"},{name:"fitInputWidth",defaultValue:!1,setters:"BooleanSetter"},{name:"suffixIcon",defaultValue:"ArrowUp",setters:"InputSetter"},{name:"tagType",defaultValue:"info",options:["success","info","warning","danger"],setters:"SelectSetter"},{name:"tagEffect",defaultValue:"light",options:["","light","dark","plain"],setters:"SelectSetter"},{name:"validateEvent",defaultValue:!0,setters:"BooleanSetter"},{name:"offset",defaultValue:12,setters:"NumberSetter"},{name:"showArrow",defaultValue:!0,setters:"BooleanSetter"},{name:"placement",label:"placement",title:"下拉框出现的位置",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"],defaultValue:"bottom-start"},{name:"fallbackPlacements",label:"fallbackPlacements",title:"dropdown 可用的 positions",setters:"ArraySetter",defaultValue:["bottom-start","top-start","right","left"]},{name:"maxCollapseTags",label:"maxCollapseTags",title:"需要显示的 Tag 的最大数量 只有当 collapse-tags 设置为 true 时才会生效。",setters:"NumberSetter",defaultValue:1},{name:"popperOptions",label:"popperOptions",title:"popper.js 参数",setters:"ObjectSetter",defaultValue:{}},{name:"ariaLabel",label:"ariaLabel",title:"等价于原生 input aria-label 属性",setters:"StringSetter"},{name:"emptyValues",setters:"ArraySetter"},{name:"valueOnClear",setters:["StringSetter","NumberSetter","BooleanSetter","FunctionSetter"]},{name:"suffixTransition",title:"下拉菜单显示/消失时后缀图标的动画",defaultValue:!0,setters:"BooleanSetter"},{name:"tabindex",setters:["StringSetter","NumberSetter"]}],events:["node-click","node-contextmenu","check-change","check","current-change","node-expand","node-collapse","node-drag-start","node-drag-enter","node-drag-leave","node-drag-over","node-drop","node-drag-end","change","visible-change","remove-tag","clear","blur","focus","update:modelValue"],slots:[{name:"default",params:["node","data"]},{name:"empty"},{name:"header"},{name:"footer"},{name:"prefix"},{name:"tag"},{name:"loading"},{name:"label"}],snippet:{props:{data:[{value:"1",label:"Level one 1",children:[{value:"1-1",label:"Level two 1-1",children:[{value:"1-1-1",label:"Level three 1-1-1"}]}]},{value:"2",label:"Level one 2",children:[{value:"2-1",label:"Level two 2-1",children:[{value:"2-1-1",label:"Level three 2-1-1"}]},{value:"2-2",label:"Level two 2-2",children:[{value:"2-2-1",label:"Level three 2-2-1"}]}]},{value:"3",label:"Level one 3",children:[{value:"3-1",label:"Level two 3-1",children:[{value:"3-1-1",label:"Level three 3-1-1"}]},{value:"3-2",label:"Level two 3-2",children:[{value:"3-2-1",label:"Level three 3-2-1"}]}]}],renderAfterExpand:!1,style:{width:"240px"}}}},ue={name:"ElTreeV2",label:"虚拟化树形控件",doc:"https://element-plus.org/zh-CN/component/tree-v2.html",categoryId:"data",package:"element-plus",props:[{name:"data",defaultValue:"",setters:["ArraySetter","JSONSetter"]},{name:"empty-text",defaultValue:"",setters:"InputSetter"},{name:"props",defaultValue:"",setters:["ObjectSetter","JSONSetter"]},{name:"highlight-current",defaultValue:!1,label:"高亮选中节点",setters:"BooleanSetter"},{name:"expand-on-click-node",defaultValue:!0,title:"是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点",label:"expand-on",setters:"BooleanSetter"},{name:"check-on-click-node",defaultValue:!1,title:"是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点",label:"check-on",setters:"BooleanSetter"},{name:"default-expanded-keys",defaultValue:"",title:"默认展开的节点的 key 的数组",label:"check-on",setters:"JSONSetter"},{name:"show-checkbox",defaultValue:!1,title:"节点是否可被选择",label:"check-on",setters:"BooleanSetter"},{name:"check-strictly",defaultValue:!1,setters:"BooleanSetter"},{name:"default-checked-keys",defaultValue:"",title:"默认勾选的节点的 key 的数组",label:"checked-keys",setters:"JSONSetter"},{name:"current-node-key",defaultValue:"",label:"选中的节点",setters:["InputSetter","NumberSetter"]},{name:"filter-method",defaultValue:"",title:"对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示， 返回 false 则表示这个节点会被隐藏",setters:"JSONSetter"},{name:"indent",defaultValue:16,title:"相邻级节点间的水平缩进，单位为像素",setters:"NumberSetter"},{name:"icon",defaultValue:"",title:"相邻级节点间的水平缩进，单位为像素",setters:"InputSetter"},{name:"itemSize ",label:"itemSize ",title:"自定义树节点的高度",setters:"NumberSetter",defaultValue:26}],events:["node-click","node-drop","node-contextmenu","check-change","check","current-change","node-expand","node-collapse"],slots:[{name:"default",params:["node","data"]}]},me=[{name:"ElStatistic",label:"统计组件",doc:"https://element-plus.org/zh-CN/component/statistic.html",categoryId:"data",package:"element-plus",props:[{name:"value",label:"value",title:"数字内容",setters:"NumberSetter",defaultValue:0},{name:"decimalSeparator",label:"decimalSeparator",title:"设置小数点符号",setters:"StringSetter",defaultValue:"."},{name:"formatter",label:"formatter",title:"自定义数字格式化",setters:"FunctionSetter"},{name:"groupSeparator",label:"groupSeparator",title:"设置千分位标识符",setters:"StringSetter",defaultValue:","},{name:"precision",label:"precision",title:"数字精度",setters:"NumberSetter",defaultValue:0},{name:"prefix",label:"prefix",title:"设置数字的前缀",setters:"StringSetter"},{name:"suffix",label:"suffix",title:"设置数字的后缀",setters:"StringSetter"},{name:"title",label:"title",title:"数字标题",setters:"StringSetter"},{name:"valueStyle",label:"valueStyle",title:"数字样式",setters:["StringSetter","ObjectSetter","ArraySetter"]}],slots:["prefix","suffix","title","default"],snippet:{props:{title:"Daily active users",value:"268500"}}},{name:"ElCountdown",label:"倒计时",doc:"https://element-plus.org/zh-CN/component/statistic.html",categoryId:"data",package:"element-plus",props:[{name:"value",label:"value",title:"目标时间",setters:["NumberSetter"]},{name:"format",label:"format",title:"格式化倒计时",setters:"StringSetter",defaultValue:"HH:mm:ss"},{name:"prefix",label:"prefix",title:"设置倒计时前缀",setters:"StringSetter"},{name:"suffix",label:"suffix",title:"设置倒计时的后缀",setters:"StringSetter"},{name:"title",label:"title",title:"倒计时标题",setters:"StringSetter"},{name:"valueStyle",label:"valueStyle",title:"倒计时值的样式",setters:["StringSetter","ObjectSetter","ArraySetter"]}],events:["change","finish"],slots:["prefix","suffix","title"],snippet:{props:{title:"Start to grab",value:1792134444342}}}],de=[{name:"ElSegmented",label:"分段控制器",doc:"https://element-plus.org/zh-CN/component/segmented.html",categoryId:"data",package:"element-plus",props:[{name:"modelValue",title:"绑定值",setters:["StringSetter","NumberSetter","BooleanSetter"]},{name:"options",title:"选项的数据",defaultValue:[],setters:"ArraySetter"},{name:"size",defaultValue:"",options:["","large","default","small"],setters:"SelectSetter"},{name:"block",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"validate-event",defaultValue:!0,setters:"BooleanSetter"},{name:"name",setters:"StringSetter"},{name:"id",setters:"StringSetter"},{name:"ariaLabel",setters:"StringSetter"},{name:"direction",defaultValue:"horizontal",options:["horizontal","vertical"],setters:"SelectSetter"}],events:["change","update:modelValue"],slots:["default"],snippet:{props:{modelValue:"Mom",options:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]}}}],ie={name:"ElAffix",label:"固钉",categoryId:"nav",doc:"https://element-plus.org/zh-CN/component/affix.html",package:"element-plus",props:[{name:"offset",defaultValue:0,setters:"NumberSetter"},{name:"position",defaultValue:"top",setters:"SelectSetter",options:["top","bottom"]},{name:"target",defaultValue:"",setters:"InputSetter"},{name:"z-index",defaultValue:100,setters:"NumberSetter"}],events:[{name:"change"},{name:"scroll"}],slots:["default"],snippet:{name:"ElAffix",children:[{name:"ElButton",props:{type:"primary"},children:"Affix 固钉"}]}},pe=[{name:"ElAnchor",label:"锚点",doc:"https://element-plus.org/zh-CN/component/anchor.html",categoryId:"other",package:"element-plus",props:[{name:"container",label:"container",title:"滚动的容器",setters:"StringSetter"},{name:"offset",label:"offset",title:"设置锚点滚动的偏移量",setters:"NumberSetter",defaultValue:0},{name:"bound",label:"bound",title:"触发锚点的元素的位置偏移量",setters:"NumberSetter",defaultValue:15},{name:"duration",label:"duration",title:"设置容器滚动持续时间，单位为毫秒",setters:"NumberSetter",defaultValue:300},{name:"marker",label:"marker",title:"是否显示标记",setters:"BooleanSetter",defaultValue:!0},{name:"type",label:"type",title:"设置锚点类型",setters:"SelectSetter",options:["default","underline"],defaultValue:"default"},{name:"direction",label:"direction",title:"设置锚点方向",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"horizontal"},{name:"selectScrollTop",title:"滚动时，链接是否选中位于顶部",defaultValue:!1,setters:"BooleanSetter"}],events:["change","click"],slots:["default"],snippet:{props:{offset:"70"},children:[{name:"ElAnchorLink",props:{href:""},children:"基本用法"}]}},{name:"ElAnchorLink",label:"锚点链接",doc:"https://element-plus.org/zh-CN/component/anchor.html",categoryId:"other",package:"element-plus",props:[{name:"title",label:"title",title:"链接的文本内容",setters:"StringSetter"},{name:"href",label:"href",title:"链接的地址",setters:"StringSetter"}],slots:["default","sub-link"],snippet:{props:{href:""},children:"基本用法"}}],fe={name:"ElBacktop",label:"回到顶部",categoryId:"nav",doc:"https://element-plus.org/zh-CN/component/backtop.html",package:"element-plus",props:[{name:"target",defaultValue:"",setters:"InputSetter"},{name:"visibilityHeight",defaultValue:200,setters:"NumberSetter"},{name:"right",defaultValue:40,setters:"NumberSetter"},{name:"bottom",defaultValue:40,setters:"NumberSetter"}],events:[{name:"click"}],slots:["default"],snippet:{name:"ElBacktop",children:[{name:"component",props:{is:"div",style:{height:"100%",textAlign:"center",width:"100px",lineHeight:"40px",color:"#1989fa"}},children:"UP"}]}},ce=[{name:"ElBreadcrumb",childIncludes:["ElBreadcrumbItem"],label:"面包屑",categoryId:"nav",doc:"https://element-plus.org/zh-CN/component/breadcrumb.html",package:"element-plus",props:[{name:"separator",defaultValue:"/",setters:"InputSetter"},{name:"separatorIcon",defaultValue:"",setters:["InputSetter"]}],slots:["default","separatorIcon"],snippet:{children:[{name:"ElBreadcrumbItem",children:"主页"},{name:"ElBreadcrumbItem",children:"列表"},{name:"ElBreadcrumbItem",children:"详情"}]}},{name:"ElBreadcrumbItem",label:"面包屑项",categoryId:"nav",package:"element-plus",props:[{name:"to",defaultValue:"",setters:["InputSetter","JSONSetter"]},{name:"replace",defaultValue:"",setters:"InputSetter"}],slots:["default"],snippet:{name:"ElBreadcrumbItem",children:"BreadcrumbItem"}}],Se=[{name:"ElDropdown",label:"下拉菜单",categoryId:"nav",doc:"https://element-plus.org/zh-CN/component/dropdown.html",package:"element-plus",props:[{name:"type",defaultValue:"",options:["","default","primary","success","warning","info","danger","text"],setters:"SelectSetter"},{name:"size",defaultValue:"default",setters:"SelectSetter",options:["large","default","small"]},{name:"maxHeight",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"splitButton",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"placement",defaultValue:"bottom",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end"]},{name:"trigger",defaultValue:"hover",setters:"SelectSetter",options:["hover","click","contextmenu"]},{name:"triggerKeys",title:"指定键盘上哪些按键可以触发操作",defaultValue:["Enter","Space","ArrowDown","NumpadEnter"],setters:"ArraySetter"},{name:"hideOnClick",defaultValue:!0,setters:"BooleanSetter"},{name:"showTimeout",defaultValue:150,setters:"NumberSetter"},{name:"hideTimeout",defaultValue:150,setters:"NumberSetter"},{name:"role",defaultValue:"menu",setters:"InputSetter"},{name:"tabindex",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"popperOptions",defaultValue:{modifiers:[{name:"computeStyles",options:{gpuAcceleration:!1}}]},setters:"JSONSetter"},{name:"teleported",label:"teleported",title:"是否将下拉列表插入至 body 元素",setters:"BooleanSetter",defaultValue:!0}],slots:[{name:"default"},{name:"dropdown"}],events:[{name:"click"},{name:"command"},{name:"visible-change"}],snippet:{name:"ElDropdown",children:[{name:"ElButton",children:[{name:"component",props:{is:"span"},children:"下拉菜单"},{name:"component",props:{is:"span"},children:" V"}]},{name:"ElDropdownMenu",slot:"dropdown",children:[{name:"ElDropdownItem",children:"Action 1"},{name:"ElDropdownItem",children:"Action 2"},{name:"ElDropdownItem",children:"Action 3"}]}]}},{name:"ElDropdownMenu",label:"下拉菜单Menu",categoryId:"nav",package:"element-plus",slots:["default"]},{name:"ElDropdownItem",childIncludes:!0,label:"下拉菜单项",categoryId:"nav",package:"element-plus",props:[{name:"command",defaultValue:"",setters:["InputSetter","NumberSetter","JSONSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"divided",defaultValue:!1,setters:"BooleanSetter"},{name:"icon",defaultValue:"",setters:["InputSetter"]}],slots:["default","icon"],snippet:{name:"ElDropdownItem",children:"下拉选项"}}],Ve=[{name:"ElMenu",label:"导航菜单",doc:"https://element-plus.org/zh-CN/component/menu.html",categoryId:"nav",package:"element-plus",props:[{name:"mode",defaultValue:"vertical",setters:"SelectSetter",options:["horizontal","vertical"]},{name:"collapse",defaultValue:!1,setters:"BooleanSetter"},{name:"ellipsis",defaultValue:!0,setters:"BooleanSetter"},{name:"ellipsisIcon",label:"ellipsisIcon",title:"自定义省略图标 (仅在水平模式下可用)",setters:"StringSetter"},{name:"popperOffset",label:"popperOffset",title:"弹出层的偏移量(对所有子菜单有效)",setters:"NumberSetter",defaultValue:6},{name:"defaultActive",defaultValue:"",setters:"InputSetter"},{name:"defaultOpeneds",defaultValue:[],setters:"ArraySetter"},{name:"uniqueOpened",defaultValue:!1,setters:"BooleanSetter"},{name:"menuTrigger",defaultValue:"hover",setters:"SelectSetter",options:["hover","click"]},{name:"router",defaultValue:!1,setters:"BooleanSetter"},{name:"collapseTransition",defaultValue:!0,setters:"BooleanSetter"},{name:"popperEffect",label:"popperEffect",title:"Tooltip 主题，内置了 dark / light 两种主题",setters:"SelectSetter",options:["dark","light"],defaultValue:"dark"},{name:"closeOnClickOutside",label:"closeOnClickOutside",title:"可选，单击外部时是否折叠菜单",setters:"BooleanSetter",defaultValue:!1},{name:"popperClass",label:"popperClass",title:"为 popper 添加类名",setters:"StringSetter"},{name:"showTimeout",label:"showTimeout",title:"菜单出现前的延迟",setters:"NumberSetter",defaultValue:300},{name:"hideTimeout",label:"hideTimeout",title:"菜单消失前的延迟",setters:"NumberSetter",defaultValue:300},{name:"backgroundColor",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"textColor",defaultValue:"#303133",setters:"ColorSetter"},{name:"activeTextColor",defaultValue:"#409EFF",setters:"ColorSetter"}],events:[{name:"select"},{name:"open"},{name:"close"}],slots:["default","ellipsis-icon"],snippet:{props:{mode:"horizontal"},children:[{name:"ElMenuItem",children:"菜单项一",props:{index:"1"}},{name:"ElSubMenu",props:{index:"2"},children:[{name:"component",slot:"title",props:{is:"div"},children:"子菜单"},{name:"ElMenuItem",children:"子菜单项一",props:{index:"2-1"}},{name:"ElMenuItem",children:"子菜单项二",props:{index:"2-2"}}]},{name:"ElMenuItem",children:"菜单项三",props:{index:"3"}}]}},{name:"ElSubMenu",label:"导航子菜单",categoryId:"nav",package:"element-plus",props:[{name:"index",label:"index *",defaultValue:"",setters:"InputSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"showTimeout",setters:"NumberSetter"},{name:"hideTimeout",setters:"NumberSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"teleported",defaultValue:void 0,setters:"BooleanSetter"},{name:"popperOffset",defaultValue:6,setters:"NumberSetter"},{name:"expandCloseIcon",defaultValue:"",setters:["InputSetter"]},{name:"expandOpenIcon",defaultValue:"",setters:["InputSetter"]},{name:"collapseCloseIcon",defaultValue:"",setters:["InputSetter"]},{name:"collapseOpenIcon",defaultValue:"",setters:["InputSetter"]}],slots:[{name:"default"},{name:"title"}],snippet:{children:[{name:"component",slot:"title",props:{is:"div"},children:"子菜单"},{name:"ElMenuItem",children:"子菜单项一"}]}},{name:"ElMenuItem",label:"导航菜单项",categoryId:"nav",package:"element-plus",props:[{name:"index",defaultValue:null,setters:"InputSetter"},{name:"route",defaultValue:"",setters:["StringSetter","JSONSetter"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"click"}],slots:[{name:"default"},{name:"title"}],snippet:{children:"菜单项"}},{name:"ElMenuItemGroup",label:"导航菜单组",categoryId:"nav",package:"element-plus",props:[{name:"title",defaultValue:"",setters:"InputSetter"}],slots:[{name:"default"},{name:"title"}],snippet:{props:{title:"分组一"},children:[{name:"ElMenuItem",children:"子菜单项一"},{name:"ElMenuItem",children:"子菜单项一"}]}}],be={name:"ElPageHeader",label:"页头",doc:"https://element-plus.org/zh-CN/component/page-header.html",categoryId:"nav",package:"element-plus",props:[{name:"icon",defaultValue:"Back",setters:"InputSetter"},{name:"title",defaultValue:"",setters:"InputSetter"},{name:"content",defaultValue:"",setters:"InputSetter"}],events:[{name:"back"}],slots:[{name:"icon"},{name:"title"},{name:"content"},{name:"extra"},{name:"breadcrumb"},{name:"default"}],snippet:{name:"ElPageHeader",children:[{name:"component",slot:"content",props:{is:"span"},children:"Title"}]}},ge=[{name:"ElSteps",label:"步骤条",categoryId:"nav",doc:"https://element-plus.org/zh-CN/component/steps.html",package:"element-plus",props:[{name:"space",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"direction",defaultValue:"horizontal",setters:"SelectSetter",options:["vertical","horizontal"]},{name:"active",defaultValue:0,setters:"NumberSetter"},{name:"processStatus",defaultValue:"process",setters:"SelectSetter",options:["wait","process","finish","error","success"]},{name:"finishStatus",defaultValue:"finish",setters:"SelectSetter",options:["wait","process","finish","error","success"]},{name:"alignCenter",defaultValue:!1,setters:"BooleanSetter"},{name:"simple",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{name:"ElSteps",children:[{name:"ElStep",props:{title:"Step 1"}},{name:"ElStep",props:{title:"Step 2"}},{name:"ElStep",props:{title:"Step 3"}}],props:{active:0,finishStatus:"success"}}},{name:"ElStep",label:"步骤项",categoryId:"nav",package:"element-plus",props:[{name:"title",defaultValue:"",setters:"InputSetter"},{name:"description",defaultValue:"",setters:"InputSetter"},{name:"icon",defaultValue:"",setters:["InputSetter"]},{name:"status",defaultValue:"",setters:"SelectSetter",options:["wait","process","finish","error","success"]}],slots:[{name:"icon"},{name:"title"},{name:"description"}],snippet:{props:{title:"步骤"}}}],he=[{name:"ElTabs",label:"标签页",doc:"https://element-plus.org/zh-CN/component/tabs.html",categoryId:"nav",package:"element-plus",props:[{name:"modelValue",setters:["InputSetter","NumberSetter"]},{name:"type",setters:[{name:"SelectSetter",props:{closable:!0}}],defaultValue:"",options:["","card","border-card"]},{name:"closable",defaultValue:!1,setters:"BooleanSetter"},{name:"addable",defaultValue:!1,setters:"BooleanSetter"},{name:"editable",defaultValue:!1,setters:"BooleanSetter"},{name:"tabPosition",defaultValue:"top",setters:"SelectSetter",options:["top","right","bottom","left"]},{name:"stretch",defaultValue:!1,setters:"BooleanSetter"},{name:"beforeLeave",defaultValue:"",setters:"FunctionSetter"}],events:[{name:"tab-click"},{name:"tab-change"},{name:"tab-remove"},{name:"tab-add"},{name:"edit"},{name:"update:modelValue"}],slots:["default","addIcon","add-icon"],snippet:{props:{modelValue:"1"},children:[{name:"ElTabPane",children:"面板一内容",props:{label:"面板一",name:"1"}},{name:"ElTabPane",children:"面板二内容",props:{label:"面板二",name:"2"}},{name:"ElTabPane",children:"面板三内容",props:{label:"面板三",name:"3"}}]}},{name:"ElTabPane",label:"标签页面板",categoryId:"nav",package:"element-plus",props:[{name:"label",defaultValue:"",setters:"InputSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"name",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"closable",defaultValue:!1,setters:"BooleanSetter"},{name:"lazy",defaultValue:!1,setters:"BooleanSetter"}],slots:[{name:"default"},{name:"label"}],snippet:{props:{label:"面板标题"},children:"面板内容"}}],Ie={name:"ElAlert",childIncludes:!0,label:"提示",doc:"https://element-plus.org/zh-CN/component/alert.html",categoryId:"other",package:"element-plus",props:[{name:"title",defaultValue:"",setters:"InputSetter"},{name:"type",defaultValue:"info",setters:"SelectSetter",options:["success","warning","info","error"]},{name:"description",defaultValue:"",setters:"InputSetter"},{name:"closable",defaultValue:!0,setters:"BooleanSetter"},{name:"center",defaultValue:!1,setters:"BooleanSetter"},{name:"closeText",defaultValue:"",setters:"InputSetter"},{name:"showIcon",defaultValue:!1,setters:"BooleanSetter"},{name:"effect",defaultValue:"light",setters:"SelectSetter",options:["light","dark"]}],events:[{name:"close"}],slots:[{name:"default"},{name:"title"}],snippet:{props:{title:"success alert",type:"success"}}},ve={name:"ElDialog",label:"对话框",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/dialog.html",package:"element-plus",props:[{name:"modelValue",defaultValue:!1,setters:"BooleanSetter"},{name:"title",defaultValue:"",setters:"InputSetter"},{name:"width",defaultValue:"",setters:["InputSetter","NumberSetter"]},{name:"fullscreen",defaultValue:!1,setters:"BooleanSetter"},{name:"top",defaultValue:"",setters:"InputSetter"},{name:"modal",defaultValue:!0,setters:"BooleanSetter"},{name:"modalClass",label:"modalClass",title:"遮罩的自定义类名",setters:"StringSetter"},{name:"appendToBody",defaultValue:!1,setters:"BooleanSetter"},{name:"appendTo",label:"appendTo",title:"Dialog 挂载到哪个 DOM 元素 将覆盖 append-to-body",setters:"StringSetter",defaultValue:"body"},{name:"lockScroll",defaultValue:!0,setters:"BooleanSetter"},{name:"openDelay",defaultValue:0,setters:"NumberSetter"},{name:"closeDelay",defaultValue:0,setters:"NumberSetter"},{name:"closeOnClickModal",label:"点击关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"closeOnPressEscape",label:"ESC键关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"showClose",defaultValue:!0,setters:"BooleanSetter"},{name:"beforeClose",defaultValue:"",setters:"FunctionSetter"},{name:"draggable",defaultValue:!1,setters:"BooleanSetter"},{name:"overflow",label:"overflow",title:"拖动范围可以超出可视区",setters:"BooleanSetter",defaultValue:!1},{name:"center",defaultValue:!1,setters:"BooleanSetter"},{name:"alignCenter",defaultValue:!1,setters:"BooleanSetter"},{name:"destroyOnClose",defaultValue:!1,setters:"BooleanSetter"},{name:"closeIcon",label:"closeIcon",title:"自定义关闭图标",setters:["StringSetter","IconSetter"]},{name:"z-index",label:"z-index",title:"和原生的 CSS 的 z-index 相同，改变 z 轴的顺序",setters:"NumberSetter"},{name:"headerAriaLevel",label:"headerAriaLevel",title:"header 的 aria-level 属性",setters:"StringSetter",defaultValue:2},{name:"customClass",defaultValue:"",setters:"InputSetter"}],events:[{name:"open"},{name:"opened"},{name:"close"},{name:"closed"},{name:"open-auto-focus"},{name:"close-auto-focus"},{name:"update:modelValue"}],slots:[{name:"default"},{name:"header"},{name:"footer"},{name:"title"}],snippet:{name:"ElDialog",children:"对话框弹窗内容",props:{title:"标题",modelValue:!0}}},ye={name:"ElDrawer",label:"抽屉",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/drawer.html",package:"element-plus",props:[{name:"modelValue",defaultValue:!1,setters:"BooleanSetter"},{name:"appendToBody",defaultValue:!1,setters:"BooleanSetter"},{name:"appendTo",defaultValue:"body",setters:"StringSetter"},{name:"lockScroll",defaultValue:!0,setters:"BooleanSetter"},{name:"beforeClose",defaultValue:"",setters:"FunctionSetter"},{name:"closeOnClickModal",label:"点击蒙层关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"closOonPressEscape",label:"ESC键关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"openDelay",defaultValue:0,setters:"NumberSetter"},{name:"closeDelay",defaultValue:0,setters:"NumberSetter"},{name:"destroyOnClose",defaultValue:!1,setters:"BooleanSetter"},{name:"modal",defaultValue:!0,setters:"BooleanSetter"},{name:"direction",defaultValue:"rtl",setters:"SelectSetter",options:["rtl","ltr","ttb","btt"]},{name:"showClose",defaultValue:!0,setters:"BooleanSetter"},{name:"size",defaultValue:"30%",title:"Drawer 窗体的大小, 当使用 number 类型时, 以像素为单位, 当使用 string 类型时, 请传入 x%, 否则便会以 number 类型解释",setters:["InputSetter","NumberSetter"]},{name:"title",defaultValue:"",setters:"InputSetter"},{name:"withHeader",defaultValue:!0,setters:"BooleanSetter"},{name:"modalClass",defaultValue:"",setters:"InputSetter"},{name:"zIndex",defaultValue:0,setters:"NumberSetter"},{name:"headerAriaLevel",label:"headerAriaLevel",title:"header 的 aria-level 属性",setters:"StringSetter",defaultValue:2},{name:"customClass",defaultValue:"",setters:"InputSetter"}],events:[{name:"open"},{name:"opened"},{name:"close"},{name:"closed"},{name:"open-auto-focus"},{name:"close-auto-focus"},{name:"update:modelValue"}],slots:[{name:"default"},{name:"header"},{name:"footer"},{name:"title"}],snippet:{name:"ElDrawer",children:"抽屉内容",props:{title:"标题",modelValue:!0}}},Be={name:"ElPopconfirm",label:"气泡确认框",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/popconfirm.html",package:"element-plus",props:[{name:"title",defaultValue:"",setters:"InputSetter"},{name:"confirmButtonText",defaultValue:"",setters:"InputSetter"},{name:"cancelButtonText",defaultValue:"",setters:"InputSetter"},{name:"confirmButtonType",defaultValue:"primary",setters:"SelectSetter",options:["primary","success","warning","danger","info","text"]},{name:"confirmButtonType",defaultValue:"text",setters:"SelectSetter",options:["primary","success","warning","danger","info","text"]},{name:"icon",defaultValue:"QuestionFilled",setters:["InputSetter"]},{name:"iconColor",defaultValue:"#f90",setters:"ColorSetter"},{name:"hideIcon",defaultValue:!1,setters:"BooleanSetter"},{name:"hideAfter",defaultValue:200,setters:"NumberSetter"},{name:"teleported",defaultValue:!0,setters:"BooleanSetter"},{name:"persistent",defaultValue:!1,setters:"BooleanSetter"},{name:"width",defaultValue:"150",setters:["NumberSetter","InputSetter"]}],events:[{name:"confirm"},{name:"cancel"}],slots:[{name:"reference"},{name:"actions"}],snippet:{name:"ElPopconfirm",children:[{name:"ElButton",children:"气泡确认框",slot:"reference"}],props:{title:"标题内容"}}},Ne={name:"ElPopover",label:"气泡卡片",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/popover.html",package:"element-plus",props:[{name:"trigger",defaultValue:"primary",setters:"click",options:["click","focus","hover","contextmenu"]},{name:"title",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"light",options:["dark","light"],setters:"SelectSetter"},{name:"content",defaultValue:"",setters:"InputSetter"},{name:"width",defaultValue:150,setters:["NumberSetter","InputSetter"]},{name:"placement",defaultValue:"bottom",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"]},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"visible",defaultValue:!1,setters:"BooleanSetter"},{name:"offset",defaultValue:0,setters:"NumberSetter"},{name:"transition",defaultValue:"",setters:"InputSetter"},{name:"showArrow",defaultValue:!0,setters:"BooleanSetter"},{name:"popperOptions",defaultValue:void 0,setters:"JSONSetter"},{name:"popperClass",defaultValue:"",setters:"InputSetter"},{name:"popperStyle",setters:["InputSetter","ObjectSetter"]},{name:"showAfter",defaultValue:0,setters:"NumberSetter"},{name:"hideAfter",defaultValue:200,setters:"NumberSetter"},{name:"autoClose",defaultValue:0,setters:"NumberSetter"},{name:"tabindex",defaultValue:"",setters:"NumberSetter"},{name:"teleported",defaultValue:!0,setters:"BooleanSetter"},{name:"persistent",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"show"},{name:"before-enter"},{name:"after-enter"},{name:"hide"},{name:"before-leave"},{name:"after-leave"},{name:"update:visible"}],slots:[{name:"default"},{name:"reference"}],snippet:{name:"ElPopover",props:{placement:"bottom",title:"Title",width:"200",trigger:"hover",content:"这是content123！"},children:[{name:"ElButton",children:"气泡卡片",slot:"reference"}]}},ke={name:"ElTooltip",childIncludes:!0,label:"文字提示",doc:"https://element-plus.org/zh-CN/component/tooltip.html",categoryId:"other",package:"element-plus",props:[{name:"appendTo",defaultValue:"",setters:"InputSetter"},{name:"effect",defaultValue:"dark",setters:"InputSetter"},{name:"content",defaultValue:"",setters:"InputSetter"},{name:"rawContent",defaultValue:!1,setters:"BooleanSetter"},{name:"placement",defaultValue:"bottom",setters:"SelectSetter",options:["top","top-start","top-end","bottom","bottom-start","bottom-end","left","left-start","left-end","right","right-start","right-end"]},{name:"fallback-placements",setters:"ArraySetter"},{name:"visible",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",defaultValue:!1,setters:"BooleanSetter"},{name:"offset",defaultValue:0,setters:"NumberSetter"},{name:"transition",defaultValue:"el-fade-in-linear",setters:"InputSetter"},{name:"popperOptions",defaultValue:{boundariesElement:"body",gpuAcceleration:!1},setters:"JSONSetter"},{name:"showAfter",defaultValue:0,setters:"NumberSetter"},{name:"show-arrow",defaultValue:!1,setters:"BooleanSetter"},{name:"hide-after",defaultValue:200,setters:"NumberSetter"},{name:"auto-close",defaultValue:0,setters:"NumberSetter"},{name:"popper-class",defaultValue:"",setters:"InputSetter"},{name:"enterable",defaultValue:!0,setters:"BooleanSetter"},{name:"teleported",label:"teleported",title:"是否使用 teleport。设置成 true则会被追加到 append-to 的位置",setters:"BooleanSetter",defaultValue:!0},{name:"trigger",label:"trigger",title:"如何触发 Tooltip",setters:"SelectSetter",options:["hover","click","focus","contextmenu"],defaultValue:"hover"},{name:"virtual-triggering",defaultValue:!1,setters:"BooleanSetter"},{name:"virtual-ref",defaultValue:"",setters:"ExpressionSetter"},{name:"trigger-keys",defaultValue:["Enter","Space"],setters:"ExpressionSetter"},{name:"persistent",setters:"BooleanSetter"},{name:"ariaLabel",label:"ariaLabel",title:"和 aria-label 属性保持一致",setters:"StringSetter"}],events:[{name:"confirm"},{name:"cancel"},{name:"update:visible "}],slots:[{name:"default"},{name:"content"}],snippet:{name:"ElTooltip",children:"文字提示",props:{content:"自定义弹出框的内容"}}},xe={name:"ElDivider",label:"分割线",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/divider.html",package:"element-plus",props:[{name:"direction",defaultValue:"horizontal",setters:"SelectSetter",options:["horizontal","vertical"]},{name:"borderStyle",defaultValue:"solid",setters:"InputSetter"},{name:"contentPosition",defaultValue:"center",setters:"SelectSetter",options:["left","right","center"]}],slots:["default"],snippet:{name:"ElDivider",children:"分割线"}},Ee={name:"ElWatermark",label:"水印",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/watermark.html",package:"element-plus",props:[{name:"width",label:"width",title:"水印的宽度， content 的默认值是它自己的宽度",setters:"NumberSetter",defaultValue:120},{name:"height",label:"height",title:"水印的高度， content 的默认值是它自己的高度",setters:"NumberSetter",defaultValue:64},{name:"rotate",label:"rotate",title:"水印的旋转角度, 单位 °",setters:"NumberSetter",defaultValue:-22},{name:"zIndex",label:"zIndex",title:"水印元素的z-index值",setters:"NumberSetter",defaultValue:9},{name:"image",label:"image",title:"水印图片，建议使用 2x 或 3x 图像",setters:"StringSetter"},{name:"content",label:"content",title:"水印文本内容",setters:["StringSetter","ArraySetter"]},{name:"font",label:"font",title:"文字样式",setters:"ObjectSetter",defaultValue:{color:"rgba(0,0,0,.15)",fontSize:16,fontWeight:"normal",fontFamily:"sans-serif",fontStyle:"normal",textAlign:"center",textBaseline:"top"}},{name:"gap",label:"gap",title:"水印之间的间距",setters:"ArraySetter",defaultValue:[100,100]},{name:"offset",label:"offset",title:"水印从容器左上角的偏移 默认值为 gap/2",setters:"ArraySetter",defaultValue:[50,50]}],slots:["default"],snippet:{props:{font:{color:"rgba(0, 0, 0, .15)"}},children:[{name:"div",props:{style:{height:"500px"}}}]}},Ce={name:"ElConfigProvider",label:"全局配置",categoryId:"other",doc:"https://element-plus.org/zh-CN/component/config-provider.html",props:[{name:"locale",label:"locale",title:"翻译文本对象",setters:"ObjectSetter"},{name:"size",label:"size",title:"全局组件大小",setters:"SelectSetter",options:["large","default","small"],defaultValue:"default"},{name:"zIndex",label:"zIndex",title:"全局初始化 zIndex 的值",setters:"NumberSetter"},{name:"namespace",label:"namespace",title:"全局组件类名称前缀",setters:"StringSetter",defaultValue:"el"},{name:"button",label:"button",title:"按钮相关配置",setters:"ObjectSetter",defaultValue:{autoInsertSpace:!1}},{name:"message",label:"message",title:"消息相关配置",setters:"ObjectSetter"},{name:"experimentalFeatures",label:"experimentalFeatures",title:"将要添加的实验阶段的功能，所有功能都是默认设置为 false",setters:"ObjectSetter"}],slots:["default"]},s="element-plus",we=[ie,Ie,pe,v,H,fe,W,ce,c,$,G,K,y,B,U,N,Ce,S,k,x,q,ve,xe,ye,Se,Y,E,_,C,w,T,O,V,b,Ve,be,Q,Be,Ne,Z,z,F,X,h,A,J,ee,D,I,me,de,ge,P,te,he,le,g,re,L,j,ke,se,M,ne,oe,ue,R,ae,Ee].flat();return{name:s,version:t,label:"Element+",library:"ElementPlusMaterial",order:2,categories:[{id:"base",category:"基础组件"},{id:"layout",category:"排版布局"},{id:"form",category:"表单组件"},{id:"data",category:"数据展示"},{id:"nav",category:"导航"},{id:"other",category:"其他"}],components:d(we,s)}});
