"use strict";var pn=Object.defineProperty;var mn=(e,t,n)=>t in e?pn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var L=(e,t,n)=>mn(e,typeof t!="symbol"?t+"":t,n);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const O=require("@vtj/base");(function(){if(typeof window>"u"||typeof EventTarget>"u")return;const e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(t,n,r){typeof r!="boolean"&&(r=r||{},r.passive=!1),e.call(this,t,n,r)}})();/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/utils 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const gn="0.12.70";/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function wn(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const yn=Object.assign,bn=Object.prototype.hasOwnProperty,Ne=(e,t)=>bn.call(e,t),W=Array.isArray,ue=e=>Rt(e)==="[object Map]",Rn=e=>typeof e=="string",re=e=>typeof e=="symbol",we=e=>e!==null&&typeof e=="object",En=Object.prototype.toString,Rt=e=>En.call(e),Sn=e=>Rt(e).slice(8,-1),Ke=e=>Rn(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ee=(e,t)=>!Object.is(e,t);var _n={ENV_TYPE:"local",NODE_ENV:"production"};let On,Et=0,Te;function Ve(){Et++}function Xe(){if(--Et>0)return;let e;for(;Te;){let t=Te;for(Te=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}let pe=!0;const St=[];function Tn(){St.push(pe),pe=!1}function An(){const e=St.pop();pe=e===void 0?!0:e}class _t{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){}trigger(t){this.version++,this.notify(t)}notify(t){Ve();try{_n.NODE_ENV;for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Xe()}}}const Ue=new WeakMap,q=Symbol(""),Fe=Symbol(""),te=Symbol("");function C(e,t,n){if(pe&&On){let r=Ue.get(e);r||Ue.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new _t),s.map=r,s.key=n),s.track()}}function B(e,t,n,r,s,o){const i=Ue.get(e);if(!i)return;const a=u=>{u&&u.trigger()};if(Ve(),t==="clear")i.forEach(a);else{const u=W(e),c=u&&Ke(n);if(u&&n==="length"){const f=Number(r);i.forEach((d,m)=>{(m==="length"||m===te||!re(m)&&m>=f)&&a(d)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),c&&a(i.get(te)),t){case"add":u?c&&a(i.get("length")):(a(i.get(q)),ue(e)&&a(i.get(Fe)));break;case"delete":u||(a(i.get(q)),ue(e)&&a(i.get(Fe)));break;case"set":ue(e)&&a(i.get(q));break}}Xe()}function $(e){const t=R(e);return t===e?t:(C(t,"iterate",te),I(e)?t:t.map(x))}function Ye(e){return C(e=R(e),"iterate",te),e}const xn={__proto__:null,[Symbol.iterator](){return Ae(this,Symbol.iterator,x)},concat(...e){return $(this).concat(...e.map(t=>W(t)?$(t):t))},entries(){return Ae(this,"entries",e=>(e[1]=x(e[1]),e))},every(e,t){return F(this,"every",e,t,void 0,arguments)},filter(e,t){return F(this,"filter",e,t,n=>n.map(x),arguments)},find(e,t){return F(this,"find",e,t,x,arguments)},findIndex(e,t){return F(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return F(this,"findLast",e,t,x,arguments)},findLastIndex(e,t){return F(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return F(this,"forEach",e,t,void 0,arguments)},includes(...e){return xe(this,"includes",e)},indexOf(...e){return xe(this,"indexOf",e)},join(e){return $(this).join(e)},lastIndexOf(...e){return xe(this,"lastIndexOf",e)},map(e,t){return F(this,"map",e,t,void 0,arguments)},pop(){return G(this,"pop")},push(...e){return G(this,"push",e)},reduce(e,...t){return rt(this,"reduce",e,t)},reduceRight(e,...t){return rt(this,"reduceRight",e,t)},shift(){return G(this,"shift")},some(e,t){return F(this,"some",e,t,void 0,arguments)},splice(...e){return G(this,"splice",e)},toReversed(){return $(this).toReversed()},toSorted(e){return $(this).toSorted(e)},toSpliced(...e){return $(this).toSpliced(...e)},unshift(...e){return G(this,"unshift",e)},values(){return Ae(this,"values",x)}};function Ae(e,t,n){const r=Ye(e),s=r[t]();return r!==e&&!I(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Cn=Array.prototype;function F(e,t,n,r,s,o){const i=Ye(e),a=i!==e&&!I(e),u=i[t];if(u!==Cn[t]){const d=u.apply(e,o);return a?x(d):d}let c=n;i!==e&&(a?c=function(d,m){return n.call(this,x(d),m,e)}:n.length>2&&(c=function(d,m){return n.call(this,d,m,e)}));const f=u.call(i,c,r);return a&&s?s(f):f}function rt(e,t,n,r){const s=Ye(e);let o=n;return s!==e&&(I(e)?n.length>3&&(o=function(i,a,u){return n.call(this,i,a,u,e)}):o=function(i,a,u){return n.call(this,i,x(a),u,e)}),s[t](o,...r)}function xe(e,t,n){const r=R(e);C(r,"iterate",te);const s=r[t](...n);return(s===-1||s===!1)&&Jn(n[0])?(n[0]=R(n[0]),r[t](...n)):s}function G(e,t,n=[]){Tn(),Ve();const r=R(e)[t].apply(e,n);return Xe(),An(),r}const Pn=wn("__proto__,__v_isRef,__isVue"),Ot=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(re));function jn(e){re(e)||(e=String(e));const t=R(this);return C(t,"has",e),t.hasOwnProperty(e)}class Tt{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?kn:Ct:o?Mn:xt).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=W(t);if(!s){let u;if(i&&(u=xn[n]))return u;if(n==="hasOwnProperty")return jn}const a=Reflect.get(t,n,z(t)?t:r);return(re(n)?Ot.has(n):Pn(n))||(s||C(t,"get",n),o)?a:z(a)?i&&Ke(n)?a:a.value:we(a)?s?jt(a):Pt(a):a}}class Ln extends Tt{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const u=K(o);if(!I(r)&&!K(r)&&(o=R(o),r=R(r)),!W(t)&&z(o)&&!z(r))return u?!1:(o.value=r,!0)}const i=W(t)&&Ke(n)?Number(n)<t.length:Ne(t,n),a=Reflect.set(t,n,r,z(t)?t:s);return t===R(s)&&(i?ee(r,o)&&B(t,"set",n,r):B(t,"add",n,r)),a}deleteProperty(t,n){const r=Ne(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&B(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!re(n)||!Ot.has(n))&&C(t,"has",n),r}ownKeys(t){return C(t,"iterate",W(t)?"length":q),Reflect.ownKeys(t)}}class vn extends Tt{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Nn=new Ln,Un=new vn,De=e=>e,ae=e=>Reflect.getPrototypeOf(e);function Fn(e,t,n){return function(...r){const s=this.__v_raw,o=R(s),i=ue(o),a=e==="entries"||e===Symbol.iterator&&i,u=e==="keys"&&i,c=s[e](...r),f=n?De:t?Be:x;return!t&&C(o,"iterate",u?Fe:q),{next(){const{value:d,done:m}=c.next();return m?{value:d,done:m}:{value:a?[f(d[0]),f(d[1])]:f(d),done:m}},[Symbol.iterator](){return this}}}}function ce(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Dn(e,t){const n={get(s){const o=this.__v_raw,i=R(o),a=R(s);e||(ee(s,a)&&C(i,"get",s),C(i,"get",a));const{has:u}=ae(i),c=t?De:e?Be:x;if(u.call(i,s))return c(o.get(s));if(u.call(i,a))return c(o.get(a));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&C(R(s),"iterate",q),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=R(o),a=R(s);return e||(ee(s,a)&&C(i,"has",s),C(i,"has",a)),s===a?o.has(s):o.has(s)||o.has(a)},forEach(s,o){const i=this,a=i.__v_raw,u=R(a),c=t?De:e?Be:x;return!e&&C(u,"iterate",q),a.forEach((f,d)=>s.call(o,c(f),c(d),i))}};return yn(n,e?{add:ce("add"),set:ce("set"),delete:ce("delete"),clear:ce("clear")}:{add(s){!t&&!I(s)&&!K(s)&&(s=R(s));const o=R(this);return ae(o).has.call(o,s)||(o.add(s),B(o,"add",s,s)),this},set(s,o){!t&&!I(o)&&!K(o)&&(o=R(o));const i=R(this),{has:a,get:u}=ae(i);let c=a.call(i,s);c||(s=R(s),c=a.call(i,s));const f=u.call(i,s);return i.set(s,o),c?ee(o,f)&&B(i,"set",s,o):B(i,"add",s,o),this},delete(s){const o=R(this),{has:i,get:a}=ae(o);let u=i.call(o,s);u||(s=R(s),u=i.call(o,s)),a&&a.call(o,s);const c=o.delete(s);return u&&B(o,"delete",s,void 0),c},clear(){const s=R(this),o=s.size!==0,i=s.clear();return o&&B(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Fn(s,e,t)}),n}function At(e,t){const n=Dn(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(Ne(n,s)&&s in r?n:r,s,o)}const Bn={get:At(!1,!1)},In={get:At(!0,!1)},xt=new WeakMap,Mn=new WeakMap,Ct=new WeakMap,kn=new WeakMap;function qn(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Hn(e){return e.__v_skip||!Object.isExtensible(e)?0:qn(Sn(e))}function Pt(e){return K(e)?e:Lt(e,!1,Nn,Bn,xt)}function jt(e){return Lt(e,!0,Un,In,Ct)}function Lt(e,t,n,r,s){if(!we(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Hn(e);if(o===0)return e;const i=s.get(e);if(i)return i;const a=new Proxy(e,o===2?r:n);return s.set(e,a),a}function K(e){return!!(e&&e.__v_isReadonly)}function I(e){return!!(e&&e.__v_isShallow)}function Jn(e){return e?!!e.__v_raw:!1}function R(e){const t=e&&e.__v_raw;return t?R(t):e}const x=e=>we(e)?Pt(e):e,Be=e=>we(e)?jt(e):e;function z(e){return e?e.__v_isRef===!0:!1}function Ce(e){return $n(e,!1)}function $n(e,t){return z(e)?e:new Wn(e,t)}class Wn{constructor(t,n){this.dep=new _t,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:R(t),this._value=n?t:x(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||I(t)||K(t);t=r?t:R(t),ee(t,n)&&(this._rawValue=t,this._value=r?t:x(t),this.dep.trigger())}}function vt(e,t){return function(){return e.apply(t,arguments)}}const{toString:zn}=Object.prototype,{getPrototypeOf:Ge}=Object,{iterator:ye,toStringTag:Nt}=Symbol,be=(e=>t=>{const n=zn.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),N=e=>(e=e.toLowerCase(),t=>be(t)===e),Re=e=>t=>typeof t===e,{isArray:X}=Array,ne=Re("undefined");function Kn(e){return e!==null&&!ne(e)&&e.constructor!==null&&!ne(e.constructor)&&P(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ut=N("ArrayBuffer");function Vn(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ut(e.buffer),t}const Xn=Re("string"),P=Re("function"),Ft=Re("number"),Ee=e=>e!==null&&typeof e=="object",Yn=e=>e===!0||e===!1,fe=e=>{if(be(e)!=="object")return!1;const t=Ge(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Nt in e)&&!(ye in e)},Gn=N("Date"),Qn=N("File"),Zn=N("Blob"),er=N("FileList"),tr=e=>Ee(e)&&P(e.pipe),nr=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||P(e.append)&&((t=be(e))==="formdata"||t==="object"&&P(e.toString)&&e.toString()==="[object FormData]"))},rr=N("URLSearchParams"),[sr,or,ir,ar]=["ReadableStream","Request","Response","Headers"].map(N),cr=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function se(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),X(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function Dt(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const k=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Bt=e=>!ne(e)&&e!==k;function Ie(){const{caseless:e}=Bt(this)&&this||{},t={},n=(r,s)=>{const o=e&&Dt(t,s)||s;fe(t[o])&&fe(r)?t[o]=Ie(t[o],r):fe(r)?t[o]=Ie({},r):X(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&se(arguments[r],n);return t}const lr=(e,t,n,{allOwnKeys:r}={})=>(se(t,(s,o)=>{n&&P(s)?e[o]=vt(s,n):e[o]=s},{allOwnKeys:r}),e),ur=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),fr=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},dr=(e,t,n,r)=>{let s,o,i;const a={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&Ge(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},hr=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},pr=e=>{if(!e)return null;if(X(e))return e;let t=e.length;if(!Ft(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},mr=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ge(Uint8Array)),gr=(e,t)=>{const r=(e&&e[ye]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},wr=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},yr=N("HTMLFormElement"),br=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),st=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Rr=N("RegExp"),It=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};se(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Er=e=>{It(e,(t,n)=>{if(P(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(P(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Sr=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return X(e)?r(e):r(String(e).split(t)),n},_r=()=>{},Or=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Tr(e){return!!(e&&P(e.append)&&e[Nt]==="FormData"&&e[ye])}const Ar=e=>{const t=new Array(10),n=(r,s)=>{if(Ee(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=X(r)?[]:{};return se(r,(i,a)=>{const u=n(i,s+1);!ne(u)&&(o[a]=u)}),t[s]=void 0,o}}return r};return n(e,0)},xr=N("AsyncFunction"),Cr=e=>e&&(Ee(e)||P(e))&&P(e.then)&&P(e.catch),Mt=((e,t)=>e?setImmediate:t?((n,r)=>(k.addEventListener("message",({source:s,data:o})=>{s===k&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),k.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",P(k.postMessage)),Pr=typeof queueMicrotask<"u"?queueMicrotask.bind(k):typeof process<"u"&&process.nextTick||Mt,jr=e=>e!=null&&P(e[ye]),l={isArray:X,isArrayBuffer:Ut,isBuffer:Kn,isFormData:nr,isArrayBufferView:Vn,isString:Xn,isNumber:Ft,isBoolean:Yn,isObject:Ee,isPlainObject:fe,isReadableStream:sr,isRequest:or,isResponse:ir,isHeaders:ar,isUndefined:ne,isDate:Gn,isFile:Qn,isBlob:Zn,isRegExp:Rr,isFunction:P,isStream:tr,isURLSearchParams:rr,isTypedArray:mr,isFileList:er,forEach:se,merge:Ie,extend:lr,trim:cr,stripBOM:ur,inherits:fr,toFlatObject:dr,kindOf:be,kindOfTest:N,endsWith:hr,toArray:pr,forEachEntry:gr,matchAll:wr,isHTMLForm:yr,hasOwnProperty:st,hasOwnProp:st,reduceDescriptors:It,freezeMethods:Er,toObjectSet:Sr,toCamelCase:br,noop:_r,toFiniteNumber:Or,findKey:Dt,global:k,isContextDefined:Bt,isSpecCompliantForm:Tr,toJSONObject:Ar,isAsyncFn:xr,isThenable:Cr,setImmediate:Mt,asap:Pr,isIterable:jr};function w(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}l.inherits(w,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:l.toJSONObject(this.config),code:this.code,status:this.status}}});const kt=w.prototype,qt={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{qt[e]={value:e}});Object.defineProperties(w,qt);Object.defineProperty(kt,"isAxiosError",{value:!0});w.from=(e,t,n,r,s,o)=>{const i=Object.create(kt);return l.toFlatObject(e,i,function(u){return u!==Error.prototype},a=>a!=="isAxiosError"),w.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Lr=null;function Me(e){return l.isPlainObject(e)||l.isArray(e)}function Ht(e){return l.endsWith(e,"[]")?e.slice(0,-2):e}function ot(e,t,n){return e?e.concat(t).map(function(s,o){return s=Ht(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function vr(e){return l.isArray(e)&&!e.some(Me)}const Nr=l.toFlatObject(l,{},null,function(t){return/^is[A-Z]/.test(t)});function Se(e,t,n){if(!l.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=l.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,p){return!l.isUndefined(p[g])});const r=n.metaTokens,s=n.visitor||f,o=n.dots,i=n.indexes,u=(n.Blob||typeof Blob<"u"&&Blob)&&l.isSpecCompliantForm(t);if(!l.isFunction(s))throw new TypeError("visitor must be a function");function c(h){if(h===null)return"";if(l.isDate(h))return h.toISOString();if(l.isBoolean(h))return h.toString();if(!u&&l.isBlob(h))throw new w("Blob is not supported. Use a Buffer instead.");return l.isArrayBuffer(h)||l.isTypedArray(h)?u&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function f(h,g,p){let y=h;if(h&&!p&&typeof h=="object"){if(l.endsWith(g,"{}"))g=r?g:g.slice(0,-2),h=JSON.stringify(h);else if(l.isArray(h)&&vr(h)||(l.isFileList(h)||l.endsWith(g,"[]"))&&(y=l.toArray(h)))return g=Ht(g),y.forEach(function(b,v){!(l.isUndefined(b)||b===null)&&t.append(i===!0?ot([g],v,o):i===null?g:g+"[]",c(b))}),!1}return Me(h)?!0:(t.append(ot(p,g,o),c(h)),!1)}const d=[],m=Object.assign(Nr,{defaultVisitor:f,convertValue:c,isVisitable:Me});function E(h,g){if(!l.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+g.join("."));d.push(h),l.forEach(h,function(y,S){(!(l.isUndefined(y)||y===null)&&s.call(t,y,l.isString(S)?S.trim():S,g,m))===!0&&E(y,g?g.concat(S):[S])}),d.pop()}}if(!l.isObject(e))throw new TypeError("data must be an object");return E(e),t}function it(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Qe(e,t){this._pairs=[],e&&Se(e,this,t)}const Jt=Qe.prototype;Jt.append=function(t,n){this._pairs.push([t,n])};Jt.toString=function(t){const n=t?function(r){return t.call(this,r,it)}:it;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Ur(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $t(e,t,n){if(!t)return e;const r=n&&n.encode||Ur;l.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=l.isURLSearchParams(t)?t.toString():new Qe(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class at{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){l.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Wt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fr=typeof URLSearchParams<"u"?URLSearchParams:Qe,Dr=typeof FormData<"u"?FormData:null,Br=typeof Blob<"u"?Blob:null,Ir={isBrowser:!0,classes:{URLSearchParams:Fr,FormData:Dr,Blob:Br},protocols:["http","https","file","blob","url","data"]},Ze=typeof window<"u"&&typeof document<"u",ke=typeof navigator=="object"&&navigator||void 0,Mr=Ze&&(!ke||["ReactNative","NativeScript","NS"].indexOf(ke.product)<0),kr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",qr=Ze&&window.location.href||"http://localhost",Hr=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ze,hasStandardBrowserEnv:Mr,hasStandardBrowserWebWorkerEnv:kr,navigator:ke,origin:qr},Symbol.toStringTag,{value:"Module"})),T={...Hr,...Ir};function Jr(e,t){return Se(e,new T.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return T.isNode&&l.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function $r(e){return l.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Wr(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function zt(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),u=o>=n.length;return i=!i&&l.isArray(s)?s.length:i,u?(l.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!a):((!s[i]||!l.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&l.isArray(s[i])&&(s[i]=Wr(s[i])),!a)}if(l.isFormData(e)&&l.isFunction(e.entries)){const n={};return l.forEachEntry(e,(r,s)=>{t($r(r),s,n,0)}),n}return null}function zr(e,t,n){if(l.isString(e))try{return(t||JSON.parse)(e),l.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const oe={transitional:Wt,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=l.isObject(t);if(o&&l.isHTMLForm(t)&&(t=new FormData(t)),l.isFormData(t))return s?JSON.stringify(zt(t)):t;if(l.isArrayBuffer(t)||l.isBuffer(t)||l.isStream(t)||l.isFile(t)||l.isBlob(t)||l.isReadableStream(t))return t;if(l.isArrayBufferView(t))return t.buffer;if(l.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Jr(t,this.formSerializer).toString();if((a=l.isFileList(t))||r.indexOf("multipart/form-data")>-1){const u=this.env&&this.env.FormData;return Se(a?{"files[]":t}:t,u&&new u,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),zr(t)):t}],transformResponse:[function(t){const n=this.transitional||oe.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(l.isResponse(t)||l.isReadableStream(t))return t;if(t&&l.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?w.from(a,w.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};l.forEach(["delete","get","head","post","put","patch"],e=>{oe.headers[e]={}});const Kr=l.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Vr=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Kr[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},ct=Symbol("internals");function Q(e){return e&&String(e).trim().toLowerCase()}function de(e){return e===!1||e==null?e:l.isArray(e)?e.map(de):String(e)}function Xr(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Yr=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Pe(e,t,n,r,s){if(l.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!l.isString(t)){if(l.isString(r))return t.indexOf(r)!==-1;if(l.isRegExp(r))return r.test(t)}}function Gr(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Qr(e,t){const n=l.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let j=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(a,u,c){const f=Q(u);if(!f)throw new Error("header name must be a non-empty string");const d=l.findKey(s,f);(!d||s[d]===void 0||c===!0||c===void 0&&s[d]!==!1)&&(s[d||u]=de(a))}const i=(a,u)=>l.forEach(a,(c,f)=>o(c,f,u));if(l.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(l.isString(t)&&(t=t.trim())&&!Yr(t))i(Vr(t),n);else if(l.isObject(t)&&l.isIterable(t)){let a={},u,c;for(const f of t){if(!l.isArray(f))throw TypeError("Object iterator must return a key-value pair");a[c=f[0]]=(u=a[c])?l.isArray(u)?[...u,f[1]]:[u,f[1]]:f[1]}i(a,n)}else t!=null&&o(n,t,r);return this}get(t,n){if(t=Q(t),t){const r=l.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return Xr(s);if(l.isFunction(n))return n.call(this,s,r);if(l.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Q(t),t){const r=l.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Pe(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=Q(i),i){const a=l.findKey(r,i);a&&(!n||Pe(r,r[a],a,n))&&(delete r[a],s=!0)}}return l.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||Pe(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return l.forEach(this,(s,o)=>{const i=l.findKey(r,o);if(i){n[i]=de(s),delete n[o];return}const a=t?Gr(o):String(o).trim();a!==o&&delete n[o],n[a]=de(s),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return l.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&l.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[ct]=this[ct]={accessors:{}}).accessors,s=this.prototype;function o(i){const a=Q(i);r[a]||(Qr(s,i),r[a]=!0)}return l.isArray(t)?t.forEach(o):o(t),this}};j.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);l.reduceDescriptors(j.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});l.freezeMethods(j);function je(e,t){const n=this||oe,r=t||n,s=j.from(r.headers);let o=r.data;return l.forEach(e,function(a){o=a.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Kt(e){return!!(e&&e.__CANCEL__)}function Y(e,t,n){w.call(this,e??"canceled",w.ERR_CANCELED,t,n),this.name="CanceledError"}l.inherits(Y,w,{__CANCEL__:!0});function Vt(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new w("Request failed with status code "+n.status,[w.ERR_BAD_REQUEST,w.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Zr(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function es(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(u){const c=Date.now(),f=r[o];i||(i=c),n[s]=u,r[s]=c;let d=o,m=0;for(;d!==s;)m+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),c-i<t)return;const E=f&&c-f;return E?Math.round(m*1e3/E):void 0}}function ts(e,t){let n=0,r=1e3/t,s,o;const i=(c,f=Date.now())=>{n=f,s=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const f=Date.now(),d=f-n;d>=r?i(c,f):(s=c,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const me=(e,t,n=3)=>{let r=0;const s=es(50,250);return ts(o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,u=i-r,c=s(u),f=i<=a;r=i;const d={loaded:i,total:a,progress:a?i/a:void 0,bytes:u,rate:c||void 0,estimated:c&&a&&f?(a-i)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},n)},lt=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},ut=e=>(...t)=>l.asap(()=>e(...t)),ns=T.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,T.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,rs=T.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];l.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),l.isString(r)&&i.push("path="+r),l.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ss(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function os(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Xt(e,t,n){let r=!ss(t);return e&&(r||n==!1)?os(e,t):t}const ft=e=>e instanceof j?{...e}:e;function J(e,t){t=t||{};const n={};function r(c,f,d,m){return l.isPlainObject(c)&&l.isPlainObject(f)?l.merge.call({caseless:m},c,f):l.isPlainObject(f)?l.merge({},f):l.isArray(f)?f.slice():f}function s(c,f,d,m){if(l.isUndefined(f)){if(!l.isUndefined(c))return r(void 0,c,d,m)}else return r(c,f,d,m)}function o(c,f){if(!l.isUndefined(f))return r(void 0,f)}function i(c,f){if(l.isUndefined(f)){if(!l.isUndefined(c))return r(void 0,c)}else return r(void 0,f)}function a(c,f,d){if(d in t)return r(c,f);if(d in e)return r(void 0,c)}const u={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(c,f,d)=>s(ft(c),ft(f),d,!0)};return l.forEach(Object.keys(Object.assign({},e,t)),function(f){const d=u[f]||s,m=d(e[f],t[f],f);l.isUndefined(m)&&d!==a||(n[f]=m)}),n}const Yt=e=>{const t=J({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:a}=t;t.headers=i=j.from(i),t.url=$t(Xt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let u;if(l.isFormData(n)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((u=i.getContentType())!==!1){const[c,...f]=u?u.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([c||"multipart/form-data",...f].join("; "))}}if(T.hasStandardBrowserEnv&&(r&&l.isFunction(r)&&(r=r(t)),r||r!==!1&&ns(t.url))){const c=s&&o&&rs.read(o);c&&i.set(s,c)}return t},is=typeof XMLHttpRequest<"u",as=is&&function(e){return new Promise(function(n,r){const s=Yt(e);let o=s.data;const i=j.from(s.headers).normalize();let{responseType:a,onUploadProgress:u,onDownloadProgress:c}=s,f,d,m,E,h;function g(){E&&E(),h&&h(),s.cancelToken&&s.cancelToken.unsubscribe(f),s.signal&&s.signal.removeEventListener("abort",f)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function y(){if(!p)return;const b=j.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),A={data:!a||a==="text"||a==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:b,config:e,request:p};Vt(function(M){n(M),g()},function(M){r(M),g()},A),p=null}"onloadend"in p?p.onloadend=y:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(y)},p.onabort=function(){p&&(r(new w("Request aborted",w.ECONNABORTED,e,p)),p=null)},p.onerror=function(){r(new w("Network Error",w.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let v=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const A=s.transitional||Wt;s.timeoutErrorMessage&&(v=s.timeoutErrorMessage),r(new w(v,A.clarifyTimeoutError?w.ETIMEDOUT:w.ECONNABORTED,e,p)),p=null},o===void 0&&i.setContentType(null),"setRequestHeader"in p&&l.forEach(i.toJSON(),function(v,A){p.setRequestHeader(A,v)}),l.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),a&&a!=="json"&&(p.responseType=s.responseType),c&&([m,h]=me(c,!0),p.addEventListener("progress",m)),u&&p.upload&&([d,E]=me(u),p.upload.addEventListener("progress",d),p.upload.addEventListener("loadend",E)),(s.cancelToken||s.signal)&&(f=b=>{p&&(r(!b||b.type?new Y(null,e,p):b),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(f),s.signal&&(s.signal.aborted?f():s.signal.addEventListener("abort",f)));const S=Zr(s.url);if(S&&T.protocols.indexOf(S)===-1){r(new w("Unsupported protocol "+S+":",w.ERR_BAD_REQUEST,e));return}p.send(o||null)})},cs=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(c){if(!s){s=!0,a();const f=c instanceof Error?c:this.reason;r.abort(f instanceof w?f:new Y(f instanceof Error?f.message:f))}};let i=t&&setTimeout(()=>{i=null,o(new w(`timeout ${t} of ms exceeded`,w.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:u}=r;return u.unsubscribe=()=>l.asap(a),u}},ls=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},us=async function*(e,t){for await(const n of fs(e))yield*ls(n,t)},fs=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},dt=(e,t,n,r)=>{const s=us(e,t);let o=0,i,a=u=>{i||(i=!0,r&&r(u))};return new ReadableStream({async pull(u){try{const{done:c,value:f}=await s.next();if(c){a(),u.close();return}let d=f.byteLength;if(n){let m=o+=d;n(m)}u.enqueue(new Uint8Array(f))}catch(c){throw a(c),c}},cancel(u){return a(u),s.return()}},{highWaterMark:2})},_e=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Gt=_e&&typeof ReadableStream=="function",ds=_e&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Qt=(e,...t)=>{try{return!!e(...t)}catch{return!1}},hs=Gt&&Qt(()=>{let e=!1;const t=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ht=64*1024,qe=Gt&&Qt(()=>l.isReadableStream(new Response("").body)),ge={stream:qe&&(e=>e.body)};_e&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ge[t]&&(ge[t]=l.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new w(`Response type '${t}' is not supported`,w.ERR_NOT_SUPPORT,r)})})})(new Response);const ps=async e=>{if(e==null)return 0;if(l.isBlob(e))return e.size;if(l.isSpecCompliantForm(e))return(await new Request(T.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(l.isArrayBufferView(e)||l.isArrayBuffer(e))return e.byteLength;if(l.isURLSearchParams(e)&&(e=e+""),l.isString(e))return(await ds(e)).byteLength},ms=async(e,t)=>{const n=l.toFiniteNumber(e.getContentLength());return n??ps(t)},gs=_e&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:a,onUploadProgress:u,responseType:c,headers:f,withCredentials:d="same-origin",fetchOptions:m}=Yt(e);c=c?(c+"").toLowerCase():"text";let E=cs([s,o&&o.toAbortSignal()],i),h;const g=E&&E.unsubscribe&&(()=>{E.unsubscribe()});let p;try{if(u&&hs&&n!=="get"&&n!=="head"&&(p=await ms(f,r))!==0){let A=new Request(t,{method:"POST",body:r,duplex:"half"}),D;if(l.isFormData(r)&&(D=A.headers.get("content-type"))&&f.setContentType(D),A.body){const[M,ie]=lt(p,me(ut(u)));r=dt(A.body,ht,M,ie)}}l.isString(d)||(d=d?"include":"omit");const y="credentials"in Request.prototype;h=new Request(t,{...m,signal:E,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:r,duplex:"half",credentials:y?d:void 0});let S=await fetch(h,m);const b=qe&&(c==="stream"||c==="response");if(qe&&(a||b&&g)){const A={};["status","statusText","headers"].forEach(nt=>{A[nt]=S[nt]});const D=l.toFiniteNumber(S.headers.get("content-length")),[M,ie]=a&&lt(D,me(ut(a),!0))||[];S=new Response(dt(S.body,ht,M,()=>{ie&&ie(),g&&g()}),A)}c=c||"text";let v=await ge[l.findKey(ge,c)||"text"](S,e);return!b&&g&&g(),await new Promise((A,D)=>{Vt(A,D,{data:v,headers:j.from(S.headers),status:S.status,statusText:S.statusText,config:e,request:h})})}catch(y){throw g&&g(),y&&y.name==="TypeError"&&/Load failed|fetch/i.test(y.message)?Object.assign(new w("Network Error",w.ERR_NETWORK,e,h),{cause:y.cause||y}):w.from(y,y&&y.code,e,h)}}),He={http:Lr,xhr:as,fetch:gs};l.forEach(He,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const pt=e=>`- ${e}`,ws=e=>l.isFunction(e)||e===null||e===!1,Zt={getAdapter:e=>{e=l.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!ws(n)&&(r=He[(i=String(n)).toLowerCase()],r===void 0))throw new w(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([a,u])=>`adapter ${a} `+(u===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(pt).join(`
`):" "+pt(o[0]):"as no adapter specified";throw new w("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:He};function Le(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Y(null,e)}function mt(e){return Le(e),e.headers=j.from(e.headers),e.data=je.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Zt.getAdapter(e.adapter||oe.adapter)(e).then(function(r){return Le(e),r.data=je.call(e,e.transformResponse,r),r.headers=j.from(r.headers),r},function(r){return Kt(r)||(Le(e),r&&r.response&&(r.response.data=je.call(e,e.transformResponse,r.response),r.response.headers=j.from(r.response.headers))),Promise.reject(r)})}const en="1.10.0",Oe={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Oe[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const gt={};Oe.transitional=function(t,n,r){function s(o,i){return"[Axios v"+en+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,a)=>{if(t===!1)throw new w(s(i," has been removed"+(n?" in "+n:"")),w.ERR_DEPRECATED);return n&&!gt[i]&&(gt[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,a):!0}};Oe.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function ys(e,t,n){if(typeof e!="object")throw new w("options must be an object",w.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const a=e[o],u=a===void 0||i(a,o,e);if(u!==!0)throw new w("option "+o+" must be "+u,w.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new w("Unknown option "+o,w.ERR_BAD_OPTION)}}const he={assertOptions:ys,validators:Oe},U=he.validators;let H=class{constructor(t){this.defaults=t||{},this.interceptors={request:new at,response:new at}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=J(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&he.assertOptions(r,{silentJSONParsing:U.transitional(U.boolean),forcedJSONParsing:U.transitional(U.boolean),clarifyTimeoutError:U.transitional(U.boolean)},!1),s!=null&&(l.isFunction(s)?n.paramsSerializer={serialize:s}:he.assertOptions(s,{encode:U.function,serialize:U.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),he.assertOptions(n,{baseUrl:U.spelling("baseURL"),withXsrfToken:U.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&l.merge(o.common,o[n.method]);o&&l.forEach(["delete","get","head","post","put","patch","common"],h=>{delete o[h]}),n.headers=j.concat(i,o);const a=[];let u=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(u=u&&g.synchronous,a.unshift(g.fulfilled,g.rejected))});const c=[];this.interceptors.response.forEach(function(g){c.push(g.fulfilled,g.rejected)});let f,d=0,m;if(!u){const h=[mt.bind(this),void 0];for(h.unshift.apply(h,a),h.push.apply(h,c),m=h.length,f=Promise.resolve(n);d<m;)f=f.then(h[d++],h[d++]);return f}m=a.length;let E=n;for(d=0;d<m;){const h=a[d++],g=a[d++];try{E=h(E)}catch(p){g.call(this,p);break}}try{f=mt.call(this,E)}catch(h){return Promise.reject(h)}for(d=0,m=c.length;d<m;)f=f.then(c[d++],c[d++]);return f}getUri(t){t=J(this.defaults,t);const n=Xt(t.baseURL,t.url,t.allowAbsoluteUrls);return $t(n,t.params,t.paramsSerializer)}};l.forEach(["delete","get","head","options"],function(t){H.prototype[t]=function(n,r){return this.request(J(r||{},{method:t,url:n,data:(r||{}).data}))}});l.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,a){return this.request(J(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}H.prototype[t]=n(),H.prototype[t+"Form"]=n(!0)});let bs=class tn{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(a=>{r.subscribe(a),o=a}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,a){r.reason||(r.reason=new Y(o,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new tn(function(s){t=s}),cancel:t}}};function Rs(e){return function(n){return e.apply(null,n)}}function Es(e){return l.isObject(e)&&e.isAxiosError===!0}const Je={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Je).forEach(([e,t])=>{Je[t]=e});function nn(e){const t=new H(e),n=vt(H.prototype.request,t);return l.extend(n,H.prototype,t,{allOwnKeys:!0}),l.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return nn(J(e,s))},n}const _=nn(oe);_.Axios=H;_.CanceledError=Y;_.CancelToken=bs;_.isCancel=Kt;_.VERSION=en;_.toFormData=Se;_.AxiosError=w;_.Cancel=_.CanceledError;_.all=function(t){return Promise.all(t)};_.spread=Rs;_.isAxiosError=Es;_.mergeConfig=J;_.AxiosHeaders=j;_.formToJSON=e=>zt(l.isHTMLForm(e)?new FormData(e):e);_.getAdapter=Zt.getAdapter;_.HttpStatusCode=Je;_.default=_;const{Axios:uo,AxiosError:fo,CanceledError:ho,isCancel:po,CancelToken:mo,VERSION:go,all:wo,Cancel:yo,isAxiosError:bo,spread:Ro,toFormData:Eo,AxiosHeaders:So,HttpStatusCode:_o,formToJSON:Oo,getAdapter:To,mergeConfig:Ao}=_,Ss={form:"application/x-www-form-urlencoded",json:"application/json",data:"multipart/form-data"},_s=["put","post","patch"],$e="Local-Request-Id",Os=100,Ts=300;let rn=class{constructor(t={}){L(this,"axios");L(this,"settings");L(this,"records",{});L(this,"isLoading",!1);L(this,"stopSkipWarn");L(this,"showLoading");L(this,"showError");this.settings=Object.assign({type:"form"},t.settings||{});const n=O.omit(t,["settings","query"]);this.axios=_.create(O.merge({headers:{"Content-Type":"application/x-www-form-urlencoded"},timeout:120*1e3},n)),this.setupSkipWarn(this.settings),this.showLoading=O.debounce(this.openLoading.bind(this),Os),this.showError=O.throttle(this._showError.bind(this),Ts,{leading:!0,trailing:!1})}setConfig(t={}){this.settings=O.merge(this.settings,t.settings||{});const n=O.omit(t,["settings","query"]);this.axios.defaults=O.merge(this.axios.defaults,n),this.setupSkipWarn(this.settings)}cancel(t,n="请求已取消"){if(t){const r=this.records[t];if(!r)return;r.source.cancel(n)}else for(const r of Object.values(this.records))r.source.cancel(n)}createHeaders(t,n,r){const s=n.injectHeaders?typeof n.headers=="function"?n.headers(t,r,n):n.headers||{}:{},o={"Content-Type":Ss[n.type||"form"],...r.headers,...s};return n.skipWarn&&(o[$e]=t),o}isJsonType(t){return Object.entries(t).some(([n,r])=>n.toLowerCase()==="content-type"&&String(r).includes("application/json"))}toFormData(t,n="data"){if(t instanceof FormData||t instanceof URLSearchParams)return t;const r=n==="data"?new FormData:new URLSearchParams;return Object.entries(t).forEach(([s,o])=>{r.append(s,o)}),r}createSendData(t,n,r,s,o={}){const{type:i,skipWarn:a}=t,{name:u="skipWarn"}=a||{};let{data:c,params:f={},method:d="get"}=n;const m=s?{[u]:!0}:{};return _s.includes(d.toLowerCase())?(c=Object.assign(c||{},m),c=i!=="json"||!this.isJsonType(r)?this.toFormData(c,i):c,f={...o}):i==="form"?f={...c||{},...o,...m}:(c&&(i!=="json"||!this.isJsonType(r))&&(c=this.toFormData(c,i)),f={...o,...m}),{data:c,params:f}}createUrl(t){let{url:n,params:r}=t;if(n){let s=O.isUrl(n)?new URL(n).origin:"";const o=s?n.replace(s,""):n;try{const i=O.pathToRegexpCompile(o,{encode:encodeURIComponent});return s+i(r||{})}catch{console.warn("createUrl","pathToRegexpCompile error",n)}}return n}openLoading(t){const{loading:n,showLoading:r}=t;n&&r&&Object.keys(this.records).length>0&&(this.isLoading=!0,r())}closeLoading(t){const{loading:n,hideLoading:r}=t;if(!n)return;this.isLoading=!1;const s=Object.keys(this.records);r&&s.length===0&&(this.isLoading=!1,r())}_showError(t,n){const{failMessage:r,showError:s}=t;if(r&&s){const o=n?.response?.data,i=o?.message||o?.msg||n?.message||n?.msg||"未知错误";s(i,n)}}validResponse(t,n){const{validSuccess:r,validate:s}=t;return r&&s?!!s(n):!0}isSkipWarnResponse(t){return!!t.promise}send(t={},n=!1){const r=O.merge({},this.settings,t.settings||{}),s=t.query||{},o=O.omit(t,["settings","query"]),i=O.uuid(!1),a=_.CancelToken.source();this.records[i]={settings:r,config:o,source:a};const u=this.createUrl(o),c=this.createHeaders(i,r,o),{data:f,params:d}=this.createSendData(r,o,c,n,s);return this.showLoading(r),new Promise((m,E)=>{this.axios({cancelToken:a.token,...o,url:u,headers:c,data:f,params:d}).then(h=>this.isSkipWarnResponse(h)?m(h.promise):this.validResponse(r,h)?m(r.originResponse?h:h.data?.data):(this.showError(r,h.data),E(h.data))).catch(h=>(this.showError(r,h),E(h))).finally(()=>{delete this.records[i],this.closeLoading(r)})})}useResponse(t,n){const{response:r}=this.axios.interceptors,s=r.use(t,n);return()=>r.eject(s)}useRequest(t,n){const{request:r}=this.axios.interceptors,s=r.use(t,n);return()=>r.eject(s)}setupSkipWarn(t){if(this.stopSkipWarn&&(this.stopSkipWarn(),this.stopSkipWarn=void 0),!t.skipWarn)return;const{code:n,executor:r,callback:s,complete:o}=t.skipWarn;this.stopSkipWarn=this.useResponse(i=>{const u=(i.config.headers||{})[$e],c=this.records[u];if(!c)return i;const{data:f}=i;if(!f||typeof f!="object")return i;if(f?.code===n){s&&s(i);const d=new Promise(r).then(()=>this.send({...c.config,settings:c.settings},!0));d.catch(m=>m).finally(()=>{o&&o()}),i.promise=d}return i})}};function sn(e={}){const t=new rn(e),n=t.send.bind(t),r=t.cancel.bind(t),s=t.setConfig.bind(t),o=t.useRequest.bind(t),i=t.useResponse.bind(t);return Object.assign(n,{...t,instance:t,send:n,cancel:r,setConfig:s,useRequest:o,useResponse:i})}const on=sn({settings:{injectHeaders:!0,loading:!0,originResponse:!0}});function an(e){const t=typeof e=="string"?{url:e}:e;return(n,r)=>on.send(O.merge({},t,r||{},{data:n}))}function As(e){const t={};for(const[n,r]of Object.entries(e))t[n]=an(r);return t}function xs(e,t){const n=Ce(null),r=Ce(),s=Ce(!0);return e.then(o=>{n.value=t?t(o):o}).catch(o=>{r.value=o}).finally(()=>{s.value=!1}),{data:n,error:r,loading:s}}const V=typeof window<"u",Cs=e=>new Promise((t,n)=>{const r=new FileReader;r.readAsDataURL(e),r.onload=()=>{t(r.result)},r.onerror=s=>{n(s)}});function Ps(e){const t={};return e?(e.forEach((n,r)=>{t[r]=typeof n=="string"?decodeURIComponent(n):n}),t):{}}function js(e){const t=e.split(","),n=t[0].match(/:(.*?);/)?.[1],r=atob(t[1]);let s=r.length;const o=new Uint8Array(s);for(;s--;)o[s]=r.charCodeAt(s);return new Blob([o],{type:n})}function Ls(e,t){const n=e;return n.lastModified=Date.now(),n.lastModifiedDate=new Date,n.name=t,n}const vs=e=>V?window.requestAnimationFrame(e):setTimeout(e,16),Ns=e=>V?window.cancelAnimationFrame(e):clearTimeout(e);class cn{constructor(t={}){L(this,"options",{type:"cache",expired:0,prefix:"__VTJ_"});L(this,"caches",{});L(this,"types");this.types={local:V?window.localStorage:this.caches,session:V?window.sessionStorage:this.caches,cache:this.caches},this.config(t)}config(t={}){this.options=Object.assign(this.options,t)}save(t,n,r={}){const{type:s,expired:o,prefix:i}={...this.options,...r},a=Date.now(),u=i+t,c=this.types[s]||this.caches,f={value:n,timestamp:a,expired:o};c===this.caches?c[u]=f:c.setItem(u,JSON.stringify(f))}get(t,n={}){const{type:r,prefix:s}={...this.options,...n},o=s+t,i=this.types[r]||this.caches;let a;if(i===this.caches)a=i[o];else{const m=i.getItem(o);m&&(a=JSON.parse(m))}if(!a)return null;const{value:u,timestamp:c,expired:f}=a;return f>0&&c+f<Date.now()?(this.remove(t,n),null):u}remove(t,n={}){const{type:r,prefix:s}={...this.options,...n},o=this.types[r]||this.caches,i=s+t;o===this.caches?delete o[i]:o.removeItem(i)}clear(t={}){const{type:n}={...this.options,...t},r=this.types[n]||this.caches;r===this.caches?this.caches={}:r.clear()}}const Us=new cn;function ln(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Z={exports:{}},Fs=Z.exports,wt;function Ds(){return wt||(wt=1,function(e,t){(function(n,r){r(t,e)})(Fs,function(n,r){var s={timeout:5e3,jsonpCallback:"callback"};function o(){return"jsonp_"+Date.now()+"_"+Math.ceil(Math.random()*1e5)}function i(c){try{delete window[c]}catch{window[c]=void 0}}function a(c){var f=document.getElementById(c);f&&document.getElementsByTagName("head")[0].removeChild(f)}function u(c){var f=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],d=c,m=f.timeout||s.timeout,E=f.jsonpCallback||s.jsonpCallback,h=void 0;return new Promise(function(g,p){var y=f.jsonpCallbackFunction||o(),S=E+"_"+y;window[y]=function(v){g({ok:!0,json:function(){return Promise.resolve(v)}}),h&&clearTimeout(h),a(S),i(y)},d+=d.indexOf("?")===-1?"?":"&";var b=document.createElement("script");b.setAttribute("src",""+d+E+"="+y),f.charset&&b.setAttribute("charset",f.charset),f.nonce&&b.setAttribute("nonce",f.nonce),f.referrerPolicy&&b.setAttribute("referrerPolicy",f.referrerPolicy),f.crossorigin&&b.setAttribute("crossorigin","true"),b.id=S,document.getElementsByTagName("head")[0].appendChild(b),h=setTimeout(function(){p(new Error("JSONP request to "+c+" timed out")),i(y),a(S),window[y]=function(){i(y)}},m),b.onerror=function(){p(new Error("JSONP request to "+c+" failed")),i(y),a(S),h&&clearTimeout(h)}})}r.exports=u})}(Z,Z.exports)),Z.exports}var Bs=Ds();const Is=ln(Bs);function Ms(e){if(V){const{protocol:t,host:n,pathname:r}=location;return`${t}//${n}${e?r:""}`}else return null}function ks(e=""){const t=e.match(O.rURL);return t?t[0]:""}function un(e){const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push([n,encodeURIComponent(e[n])].join("="));return t.join("&")}function We(e,t,n){const r={};e=(e||location.search).replace(/^[^]*\?/,""),t=t||"&",n=n||"=";let s;const o=new RegExp("(?:^|\\"+t+")([^\\"+n+"\\"+t+"]+)(?:\\"+n+"([^\\"+t+"]*))?","g");for(;(s=o.exec(e))!==null;)s[1]!==e&&(r[decodeURIComponent(s[1])]=decodeURIComponent(s[2]||""));return r}function fn(e,t){t=typeof t=="string"?We(t):t;const n=e.split("?")[0],r=We(e),s=Object.assign({},r,t),o=un(s);return o?[n,o].join("?"):e}const qs=Object.freeze(Object.defineProperty({__proto__:null,append:fn,getCurrentHost:Ms,getHost:ks,parse:We,stringify:un},Symbol.toStringTag,{value:"Module"}));async function Hs(e,t={}){const{query:n={}}=t;e.includes("${")&&(e=O.template(e)(n||{}));const r=fn(e,n);return await(await Is(r,t)).json()}var ve,yt;function Js(){if(yt)return ve;yt=1,ve=function(s,o,i){var a=document.head||document.getElementsByTagName("head")[0],u=document.createElement("script");typeof o=="function"&&(i=o,o={}),o=o||{},i=i||function(){},u.type=o.type||"text/javascript",u.charset=o.charset||"utf8",u.async="async"in o?!!o.async:!0,u.src=s,o.attrs&&e(u,o.attrs),o.text&&(u.text=""+o.text);var c="onload"in u?t:n;c(u,i),u.onload||t(u,i),a.appendChild(u)};function e(r,s){for(var o in s)r.setAttribute(o,s[o])}function t(r,s){r.onload=function(){this.onerror=this.onload=null,s(null,r)},r.onerror=function(){this.onerror=this.onload=null,s(new Error("Failed to load "+this.src),r)}}function n(r,s){r.onreadystatechange=function(){this.readyState!="complete"&&this.readyState!="loaded"||(this.onreadystatechange=null,s(null,r))}}return ve}var $s=Js();const Ws=ln($s);function zs(e,t={}){return new Promise((n,r)=>{const{library:s}=t;Ws(e,t,(o,i)=>{o?r(o):n(s?window[s]:void 0)})})}const bt={debug:-1,log:0,info:0,warn:1,error:2},Ks=function(e,t,n,r){return function(...s){if(t&&bt[t]<=bt[e]&&console[e].apply&&(r==="*"||n.startsWith(r)))return console[e].apply(console,Vs(s,n))}};function Vs(e,t){return t!=="*"&&(typeof e[0]=="string"?e[0]=`[${t}] ${e[0]}`:e=["["+t+"]"].concat(e)),e}function Xs(e,t){if(!e)return{targetLevel:t.level,targetBizName:t.bizName};if(~e.indexOf(":")){const n=e.split(":");return{targetLevel:n[0],targetBizName:n[1]}}return{targetLevel:e,targetBizName:"*"}}const Ys={level:"warn",bizName:"*"};class dn{constructor(t){L(this,"config");L(this,"options");this.options={...Ys,...t};const n=typeof location<"u"?location:{},r=(/__(?:logConf|logLevel)__=([^#/&]*)/.exec(n.href)||[])[1];this.config=Xs(r,t)}_log(t){const{targetLevel:n,targetBizName:r}=this.config,{bizName:s}=this.options;return Ks(t,n,s,r)}debug(...t){return this._log("debug")(...t)}log(...t){return this._log("log")(...t)}info(...t){return this._log("info")(...t)}warn(...t){return this._log("warn")(...t)}error(...t){return this._log("error")(...t)}}function hn(e){return new dn(e)}const Gs=hn({level:"log",bizName:"VTJ"});/*! js-cookie v3.0.5 | MIT */function le(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)e[r]=n[r]}return e}var Qs={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function ze(e,t){function n(s,o,i){if(!(typeof document>"u")){i=le({},t,i),typeof i.expires=="number"&&(i.expires=new Date(Date.now()+i.expires*864e5)),i.expires&&(i.expires=i.expires.toUTCString()),s=encodeURIComponent(s).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var a="";for(var u in i)i[u]&&(a+="; "+u,i[u]!==!0&&(a+="="+i[u].split(";")[0]));return document.cookie=s+"="+e.write(o,s)+a}}function r(s){if(!(typeof document>"u"||arguments.length&&!s)){for(var o=document.cookie?document.cookie.split("; "):[],i={},a=0;a<o.length;a++){var u=o[a].split("="),c=u.slice(1).join("=");try{var f=decodeURIComponent(u[0]);if(i[f]=e.read(c,f),s===f)break}catch{}}return s?i[s]:i}}return Object.create({set:n,get:r,remove:function(s,o){n(s,"",le({},o,{expires:-1}))},withAttributes:function(s){return ze(this.converter,le({},this.attributes,s))},withConverter:function(s){return ze(le({},this.converter,s),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var et=ze(Qs,{path:"/"});function Zs(e,t,n){et.set(e,t,n)}function eo(e){return et.get(e)}function to(e,t){et.remove(e,t)}const no=Object.freeze(Object.defineProperty({__proto__:null,get:eo,remove:to,set:Zs},Symbol.toStringTag,{value:"Module"}));function ro(e,t=""){const n=document.createElement("a");n.download=t,n.href=e,n.target="_blank",n.click()}function tt(e,t="",n){const r=new Blob([e],{type:n}),s=document.createElement("a");s.download=t,s.style.display="none",s.href=URL.createObjectURL(r),s.click(),URL.revokeObjectURL(s.href)}async function so(e,t="",n){return fetch(e,{credentials:"include"}).then(async r=>{const s=await r.blob();return tt(s,t,n),s})}function oo(e,t=""){const n=JSON.stringify(e);tt(n,t,"application/json")}function io(){const e=navigator.userAgent;let t="Unknown",n="Unknown",r="Unknown",s="Unknown",o=!1;if(/Windows NT/i.test(e)){t="Windows";const a={"12.0":"12","11.0":"11","10.0":"10","6.3":"8.1","6.2":"8","6.1":"7","6.0":"Vista","5.2":"XP 64-bit","5.1":"XP"},u=e.match(/Windows NT (\d+\.\d+)/);u&&(n=a[u[1]]||u[1])}else if(/Mac OS X/i.test(e)){t="Mac OS";const a=e.match(/Mac OS X (\d+[._]\d+[._]?\d*)/);a&&(n=a[1].replace(/_/g,"."))}else if(/(iPhone|iPad|iPod)/i.test(e)){t="iOS";const a=e.match(/OS (\d+[_\.]\d+[_\.]?\d*)/);a&&(n=a[1].replace(/_/g,"."))}else if(/Android/i.test(e)){t="Android";const a=e.match(/Android (\d+\.\d+)/);a&&(n=a[1])}else/Linux/i.test(e)&&(t="Linux");const i=e.match(/(Edge|Edg|Edga|EdgA)\/(\d+)/i);if(i)r="Microsoft Edge",s=i[2];else{const a=e.match(/Firefox\/(\d+)/i);if(a)r="Firefox",s=a[1];else{const u=e.match(/(Opera|OPR)\/(\d+)/i);if(u)r="Opera",s=u[2];else{const c=e.match(/Chrome\/(\d+)/i);if(c)r="Chrome",s=c[1];else{const f=e.match(/Version\/(\d+\.\d+)/i);if(f&&/Safari/i.test(e))r="Safari",s=f[1];else{const d=e.match(/(MSIE |Trident.*rv:)(\d+)/i);d&&(r="Internet Explorer",s=d[2])}}}}}return o=/(iPhone|iPod|iPad|Android|Windows Phone|Mobile)/i.test(e)||["iOS","Android"].includes(t),/(iPad|Tablet|Android(?!.*Mobile))/i.test(e)&&(o=!0),{os:t,osVersion:n,browser:r,browserVersion:s,isMobile:o}}exports.LOCAL_REQUEST_ID=$e;exports.Logger=dn;exports.Request=rn;exports.Storage=cn;exports.VTJ_UTILS_VERSION=gn;exports.axios=_;exports.blobToFile=Ls;exports.cAF=Ns;exports.cookie=no;exports.createApi=an;exports.createApis=As;exports.createRequest=sn;exports.dataURLtoBlob=js;exports.downloadBlob=tt;exports.downloadJson=oo;exports.downloadRemoteFile=so;exports.downloadUrl=ro;exports.fileToBase64=Cs;exports.formDataToJson=Ps;exports.getClientInfo=io;exports.getLogger=hn;exports.isClient=V;exports.jsonp=Hs;exports.loadScript=zs;exports.logger=Gs;exports.rAF=vs;exports.request=on;exports.storage=Us;exports.url=qs;exports.useApi=xs;Object.keys(O).forEach(e=>{e!=="default"&&!Object.prototype.hasOwnProperty.call(exports,e)&&Object.defineProperty(exports,e,{enumerable:!0,get:()=>O[e]})});
