import {
  BadRequestException,
  Injectable,
  UnauthorizedException
} from '@nestjs/common';
import { arrayToKv } from '@vtj/node';
import { TemplatesService } from '../templates/templates.service';
import { UsersService } from '../users/users.service';
import { DictsService } from '../dicts/dicts.service';
import { OssService } from '../oss/oss.service';
import { ReportService } from '../report/report.service';
import { PublishTemplateDto } from './dto/publish-template.dto';
import { ReportDto } from '../report/dto/report.dto';
import { Template } from '../templates/entities/template.entity';
import { TemplateDsl } from '../templates/entities/template-dsl.entity';
import { PlatformType, compressSketchJson } from '../shared';
import { TopicsService } from '../topics/topics.service';
import { CreateChatDto } from '../topics/dto/create-chat.dto';
import { UserTopicDto } from '../topics/dto/user-topic.dto';
import {
  UserImageTopicDto,
  UserFileTopicDto
} from '../topics/dto/user-image-topic.dto';
import { UserChatDto } from '../topics/dto/user-chat.dto';
import { Chat } from '../topics/entities/chat.entify';
import { SettingsService } from '../settings/settings.service';
import { OrdersService } from '../orders/orders.service';
import { SettingMode } from '../settings/types';

@Injectable()
export class OpenService {
  constructor(
    private templates: TemplatesService,
    private users: UsersService,
    private dicts: DictsService,
    private oss: OssService,
    private report: ReportService,
    private topics: TopicsService,
    private settings: SettingsService,
    private orders: OrdersService
  ) {}

  jsonp(res: any, success: boolean, data: any) {
    const code = success ? 0 : 400;
    const result = { code, success, data };
    if (res.jsonp) {
      res.jsonp(result);
    }
    return result;
  }

  async loginBySign(code: string, res: any) {
    const result = await this.users.loginBySign(code);
    return this.jsonp(res, !!result, result);
  }

  async getUserByToken(token: string, res: any) {
    const user = await this.users.getLoginUserByToken(token).catch(() => null);
    return this.jsonp(res, !!user, user);
  }

  async getTemplates(res: any, platform: string, token?: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    const categories = await this.dicts.findByGroup('TemplateCategory');
    const map = arrayToKv(categories, 'name', 'label');
    const result = await this.templates.findByUserId(platform, user?.id);
    for (const item of result) {
      item.category = map[item.category] || item.category;
    }
    return this.jsonp(res, true, result);
  }

  async getTemplateById(token: string, templateId: string, res: any) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    let result = null;
    if (user) {
      result = await this.templates.findOneByUserId(user.id, templateId);
    }
    return this.jsonp(res, !!result, result);
  }

  async getLatestTemplateDsl(token: string, templateId: string, res: any) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    let result = null;
    if (user) {
      result = await this.templates.getLatest(templateId);
      if (result) {
        await this.templates.increaseInstalled(templateId);
      }
    }
    return this.jsonp(res, !!result, result?.content);
  }

  async removeOwnerTemplate(token: string, templateId: string, res: any) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    let result = null;
    if (user) {
      result = await this.templates.removeByUserId(user.id, templateId);
    }
    return this.jsonp(res, !!result, result);
  }

  async getDict(group: string, res: any) {
    const items = await this.dicts.findByGroup(group);
    const result = items.map((n) => {
      const { label, name, value } = n;
      return {
        name,
        label,
        value
      };
    });
    return this.jsonp(res, !!result, result);
  }

  async publishTemplate(dto: PublishTemplateDto, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (user) {
      const map: Record<string, PlatformType> = {
        web: PlatformType.Web,
        h5: PlatformType.H5,
        uniapp: PlatformType.UniApp
      };
      const { name } = await this.oss.upload(dto.cover as any, 'image');
      const template = new Template();
      template.id = dto.id;
      template.category = dto.category;
      template.name = dto.name;
      template.label = dto.label;
      template.vip = false;
      template.share =
        typeof dto.share === 'string' ? dto.share === 'true' : dto.share;
      template.cover = name;
      template.author = user.name;
      template.userId = user.id;
      template.latest = dto.version || '';
      template.platform = map[dto.platform.toLowerCase()] || PlatformType.Web;
      const result = await this.templates.save(template, user);

      const dsl = new TemplateDsl();
      dsl.changelog = dto.changelog || '';
      dsl.content = dto.dsl as any;
      dsl.label = dto.version || '';
      dsl.templateId = result.id;
      return await this.templates.saveDsl(dsl);
    }
    return null;
  }

  reportData(dto: ReportDto) {
    return this.report.save(dto);
  }

  async reportDataJsop(dto: ReportDto, res: any) {
    const result = await this.report.save(dto);
    return this.jsonp(res, !!result, !!result);
  }

  async createTopic(dto: UserTopicDto, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }

    const topic: UserTopicDto = Object.assign({}, dto, {
      userId: user.id,
      userName: user.name
    });

    return await this.topics.createTopic(topic);
  }

  async getTopics(fileId: string, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.topics.findTopicsByFileId(fileId);
  }

  async getHotTopics() {
    return this.topics.findHotTopics();
  }

  async createChat(dto: UserChatDto, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    const chat: CreateChatDto = {
      ...dto,
      userId: user.id,
      userName: user.name
    };
    return this.topics.createChat(chat);
  }

  async getChats(topicId: string, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.topics.findChatsByTopicId(topicId);
  }

  async removeTopic(topicId: string, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.topics.removeTopicsByUserId([topicId], user.id);
  }

  async completion(topicId: string, chatId: string, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.topics.completion(topicId, chatId);
  }

  async cancelCompletions(token: string, chatId: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.topics.cancelCompletions(chatId);
  }

  async saveChat(chat: Chat, token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.topics.saveChat(chat);
  }

  async getSettings(token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    const settings = await this.settings.get();
    const orders = await this.orders.getUserNowValidOrders(user.id);
    let free = false;
    if (settings?.mode === SettingMode.Pay) {
      const count = await this.topics.getUserChatCount(user.id);
      free = settings.limit > count;
    }
    const result = {
      ...settings,
      invited: settings?.mode === SettingMode.Invite && !!orders.length,
      paid: settings?.mode === SettingMode.Pay && !!orders.length,
      free
    };
    delete result.promptTemplate;
    delete result.imagePromptTemplate;
    delete result.imageApiKey;
    delete result.mailPass;
    delete result.jsonApiKey;
    delete result.jsonPromptTemplate;

    return result;
  }

  async createOrder(token: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.orders.createUserOrder(user.id, user.name);
  }

  async cancelOrder(token: string, orderId: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.orders.cancelUserOrder(orderId, user.id);
  }

  async getOrder(token: string, orderId: string) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    return this.orders.findOne(orderId);
  }

  async upload(token: string, file: any) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    const { name } = await this.oss.upload(file, 'image');
    return name;
  }

  async postImageTopic(token: string, file: any, data: UserImageTopicDto) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    const { name } = await this.oss.upload(file, 'image');
    const buffer = file.buffer || file;
    const base64String = buffer.toString('base64');

    const dto: UserImageTopicDto = Object.assign({}, data, {
      type: 'image',
      userId: user.id,
      userName: user.name
    });

    return this.topics.createImageTopic(name, base64String, dto);
  }

  async postJsonTopic(token: string, file: any, data: UserFileTopicDto) {
    const user = await this.users
      .getLoginUserByToken(token, false)
      .catch(() => null);
    if (!user) {
      return new UnauthorizedException('登录已经失效，请重新登录');
    }
    const buffer = file.buffer || file;
    const jsonStr = buffer.toString();
    try {
      const json = JSON.parse(jsonStr);
      const type = (json.type || 'unknown').toLowerCase();
      const { name } = await this.oss.upload(file, type);
      compressSketchJson(json);
      compressSketchJson(json);
      const dto: UserFileTopicDto = Object.assign({}, data, {
        type: 'json',
        dataType: type,
        userId: user.id,
        userName: user.name
      });
      return this.topics.createJsonTopic(name, json, dto);
    } catch (e) {
      return new BadRequestException(e);
    }
  }
}
