const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-ChHpEROZ.js","./vue-router-CezURGfh.js","./vue-ipWmmxHk.js","./@vueuse-WO_0ftym.js","./Editor-Dgw5r9tb-C0tzAbRO.js","./monaco-editor-B8sWZqMY.js","./monaco-editor-Cpw7ccEV.css","./element-plus-COProxbp.js","./lodash-es-BL-d_OSa.js","./@element-plus-icons-vue-0BR09xN9.js","./shared-Bnc4f-Fv.js","./dayjs-DO-COJPZ.js","./element-plus-CxJJf0IB.css","./vxe-Bet8YtVU.js","./vxe-BAb1d5vW.css","./html2canvas-CIV5arX1.js","./mockjs-DG3OW7C0.js","./marked-BQZxLJfc.js","./Editor-Dgw5r9tb-Dbitusvr.css","./utils-Bopp5ewb.js","./_plugin-vue_export-helper-DlAUqK2U.js","./index-0hrNT7Sv.css","./preview-uJKSpiqZ.js","./page-CKVd_gLV.js","./uni-page-7rl4CfXh.js","./uni-page-BpV4RJKo.css","./auth-BlURkzx9.js"])))=>i.map(i=>d[i]);
import{d as _,K as p,u as s,an as d,o as m,L as u,aB as f,T as h,aw as v}from"./vue-ipWmmxHk.js";import{z as g,d as y}from"./element-plus-COProxbp.js";import{_ as r}from"./monaco-editor-B8sWZqMY.js";import{createRouter as E,createWebHashHistory as L}from"./vue-router-CezURGfh.js";import"./lodash-es-BL-d_OSa.js";import"./@vueuse-WO_0ftym.js";import"./@element-plus-icons-vue-0BR09xN9.js";import"./shared-Bnc4f-Fv.js";import"./dayjs-DO-COJPZ.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))i(e);new MutationObserver(e=>{for(const t of e)if(t.type==="childList")for(const n of t.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&i(n)}).observe(document,{childList:!0,subtree:!0});function a(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?t.credentials="include":e.crossOrigin==="anonymous"?t.credentials="omit":t.credentials="same-origin",t}function i(e){if(e.ep)return;e.ep=!0;const t=a(e);fetch(e.href,t)}})();const O=_({__name:"app",setup(l){return(o,a)=>{const i=d("router-view");return m(),p(s(y),{locale:s(g)},{default:u(()=>[(m(),p(f,null,{default:u(()=>[h(i)]),_:1}))]),_:1},8,["locale"])}}}),P=[{path:"/",name:"home",component:()=>r(()=>import("./index-ChHpEROZ.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]),import.meta.url)},{path:"/preview/:id",name:"preview",component:()=>r(()=>import("./preview-uJKSpiqZ.js"),__vite__mapDeps([22,5,6,1,2,7,8,3,9,10,11,12,13,14,4,15,16,17,18,19]),import.meta.url)},{path:"/page/:id",name:"page",component:()=>r(()=>import("./page-CKVd_gLV.js"),__vite__mapDeps([23,5,6,1,2,7,8,3,9,10,11,12,13,14,4,15,16,17,18,19]),import.meta.url),meta:{pure:!0}},{path:"/pages/:id",name:"pages",component:()=>r(()=>import("./uni-page-7rl4CfXh.js"),__vite__mapDeps([24,1,2,20,25]),import.meta.url),meta:{pure:!0}},{path:"/auth",name:"auth",component:()=>r(()=>import("./auth-BlURkzx9.js"),__vite__mapDeps([26,7,2,8,3,9,10,11,12,13,14,4,5,6,1,15,16,17,18,20]),import.meta.url)}],w=E({history:L(),routes:P}),c=v(O);c.use(w);c.mount("#app");
