import { MenuDataItem, SubMenuProps } from './types';
import { DefineComponent, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
export interface Props {
    item: MenuDataItem;
    subMenu?: SubMenuProps;
    defaultIcon?: DefineComponent<any, any, any, any>;
}
declare const _default: DefineComponent<Props, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<Props> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {}, any>;
export default _default;
