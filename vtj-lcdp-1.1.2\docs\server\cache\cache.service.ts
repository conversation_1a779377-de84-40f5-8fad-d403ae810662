import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>ron } from '@nestjs/schedule';
import { Repository, Like, Between } from 'typeorm';
import { CreateCacheDto } from './dto/create-cache.dto';
import { QueryCacheDto } from './dto/query-cache.dto';
import { Cache } from './entities/cache.entity';
import { MIN_DATE, MAX_DATE, pager } from '../shared';

@Injectable()
export class CacheService {
  private readonly isLeader: boolean;
  private caches: Map<string, Cache> = new Map();
  private logger = new Logger('CacheService', { timestamp: true });
  constructor(@InjectRepository(Cache) private repo: Repository<Cache>) {
    // 仅当实例索引为 0 时执行任务
    this.isLeader = process.env.NODE_APP_INSTANCE === '0';
    this.handleCron();
  }

  save(dto: CreateCacheDto) {
    const cache = new Cache();
    Object.assign(cache, dto);
    return this.repo.save(cache);
  }

  findOne(id: number) {
    return this.repo.findOneBy({ id });
  }

  /**
   * 清空内存缓存数据
   * @param keys
   * @returns
   */
  clear(keys?: string[]) {
    if (keys) {
      for (const key of keys) {
        if (this.caches.has(key)) {
          this.caches.delete(key);
        }
      }
    } else {
      this.caches.clear();
    }
    return true;
  }

  /**
   * 写入缓存
   * @param key
   * @param value
   * @param expired
   * @returns
   */
  async set(key: string, value: any, expired: number = 0) {
    const dto = new Cache();
    dto.key = key;
    dto.value = value;
    dto.expired = expired;
    const cache = await this.save(dto);
    if (cache) {
      this.caches.set(key, cache);
    }
    return cache;
  }

  async get(key: string): Promise<Cache | null> {
    let cache = this.caches.get(key);
    if (cache) {
      let isExpired = false;
      if (cache.expired > 0) {
        const now = Date.now();
        isExpired = now - new Date(cache.createdAt).getTime() > cache.expired;
      }
      if (isExpired) {
        await this.repo.delete(cache.id);
        this.caches.delete(key);
        return null;
      }
      ++cache.hits;
      return cache;
    }
    cache = await this.repo.findOne({ where: { key } });
    if (cache) {
      this.caches.set(key, cache);
      return await this.get(key);
    }
    return null;
  }

  async remove(id: number | number[]) {
    const where = [].concat(id).map((n) => ({ id: n }));
    const rows = await this.repo.find({
      where
    });
    const keys = rows.map((n) => n.key);
    this.clear(keys);
    return this.repo.remove(rows);
  }

  async removeByKey(key: string | string[]) {
    const where = [].concat(key).map((n) => ({ key: n }));
    const rows = await this.repo.find({
      where
    });
    const keys = rows.map((n) => n.key);
    this.clear(keys);
    return this.repo.remove(rows);
  }

  removeAll() {
    this.clear();
    return this.repo.createQueryBuilder().delete().execute();
  }

  async find(dto: QueryCacheDto) {
    const { order, withDeleted } = dto;
    const { skip, take, page, limit } = pager(dto);
    const createdAt = Between(
      new Date(dto.startTime || MIN_DATE),
      new Date(dto.endTime || MAX_DATE)
    );
    const [list, total] = await this.repo.findAndCount({
      withDeleted,
      skip,
      take,
      order,
      where: [
        {
          key: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          createdAt
        },
        {
          value: dto.keyword ? Like(`%${dto.keyword}%`) : undefined,
          createdAt
        }
      ]
    });
    list.forEach((item) => {
      item.hits = this.caches.get(item.key)?.hits || 0;
    });
    return {
      page,
      limit,
      total,
      list
    };
  }
  @Cron('0 0 4 * * *')
  async handleCron() {
    if (!this.isLeader) return; // 非主节点直接退出
    const caches = await this.repo.findBy({});
    for (const item of caches) {
      await this.get(item.key);
    }
    this.logger.log('已清除过期缓存');
  }
}
