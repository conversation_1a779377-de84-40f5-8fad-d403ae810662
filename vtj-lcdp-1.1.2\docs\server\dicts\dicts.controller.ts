import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  ParseArrayPipe
} from '@nestjs/common';
import { DictsService } from './dicts.service';
import { DictDto } from './dto/dict.dto';
import { QueryDictDto } from './dto/query-dict.dto';

@Controller('dicts')
export class DictsController {
  constructor(private readonly service: DictsService) {}

  @Post()
  save(@Body() dto: DictDto) {
    return this.service.save(dto);
  }

  @Get()
  find(@Query() dto: QueryDictDto) {
    return this.service.findForRoot(dto);
  }

  @Get('items')
  getItemsByGroups(@Query('group', ParseArrayPipe) groups: string[]) {
    return this.service.findByGroups(groups);
  }

  @Get('items/:group')
  getItemsByGroup(@Param('group') group: string) {
    return this.service.findByGroup(group);
  }

  @Delete()
  removeGroups(@Body() groups: string[]) {
    return this.service.removeGroups(groups);
  }

  @Post(':group')
  saveItems(
    @Param('group') group: string,
    @Body('records') records: DictDto[],
    @Body('removed') removed: string[]
  ) {
    return this.service.updateGroupItems(group, records, removed);
  }
}
