import { ElNotification, ElLoading, ElMessageBox } from 'element-plus';
import type { PlatformType, ProjectModel } from '@vtj/core';
import type { Access } from '@vtj/renderer';
import { loginBySign, getLoginUser } from '@/apis';
import { AUTH_CODE, BASE_PATH } from '@/shared';

/**
 * 全屏 loading
 * @returns
 */
export function loading() {
  return ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  });
}

/**
 * 系统信息通知
 * @param message
 * @param title
 * @param type
 */
export function notify(
  message: string,
  title: string = '提示',
  type: 'success' | 'warning' | 'error' | 'info' = 'warning'
) {
  ElNotification({
    title,
    message,
    type
  });
}

/**
 * 消息提示
 * @param message
 * @param options
 * @returns
 */
export function alert(message: string, options?: Record<string, any>) {
  return ElMessageBox.alert(message, options);
}

/**
 * 构建应用预览页面路由
 * @param app
 * @param platform
 * @param fileId
 * @returns
 */
export function createPreviewPath(
  app: string,
  platform: PlatformType,
  fileId?: string
) {
  if (platform === 'uniapp') {
    return `${BASE_PATH}${platform}/#/pages/${app}/${fileId || ''}`;
  } else {
    const path = `${BASE_PATH}${platform}/#/${app}/`;
    return fileId ? `${path}page/${fileId}` : path;
  }
}

/**
 * 构建设计器当前编辑的页面预览路由
 * @param project
 * @returns
 */
export function createCurrrentPagePath(project: ProjectModel) {
  const file = project.currentFile;
  let url = `${BASE_PATH}${project.platform}/#/${project.platform === 'uniapp' ? 'pages' : project.id}`;
  if (file && file.type === 'page' && project.homepage !== file.id) {
    if (project.platform === 'uniapp') {
      url += `/${project.id}/${file.id}`;
    } else {
      url += `/page/${file.id}`;
    }
  } else {
    url += project.platform === 'uniapp' ? `/${project.id}` : '';
  }
  return url;
}

/**
 * 适配各个平台的页面路由配置
 * @param platform
 * @param appId
 * @returns
 */
export function createPageRoute(platform: PlatformType, appId: string) {
  const pageBasePath = platform === 'uniapp' ? '/pages' : `/${appId}`;
  const pageRouteName = platform === 'uniapp' ? appId : 'page';

  return {
    pageBasePath,
    pageRouteName
  };
}

/**
 * 自动登录 lcdp.vtj.pro
 * @param access
 */
export async function autoAuth(access: Access) {
  const token = access.getToken();
  const login = async () => {
    if (AUTH_CODE) {
      const info = await loginBySign(AUTH_CODE).catch(() => null);
      if (info) {
        access.login(info);
      }
    }
  };

  if (token) {
    const res = await getLoginUser(token).catch(() => null);
    if (!res) {
      await login();
    }
  } else {
    await login();
  }
}
