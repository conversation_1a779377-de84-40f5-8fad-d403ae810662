import {
  Controller,
  Post,
  Body,
  Delete,
  Get,
  Query,
  Param
} from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { TemplateDto } from './dto/template.dto';
import { LoginUserDto } from '../users/dto/login-user.dto';
import { QueryTemplateDto } from './dto/query-template.dto';
import { DslDto } from './dto/dsl.dto';
import { TemplateDsl } from './entities/template-dsl.entity';

import { User } from '../shared';

@Controller('templates')
export class TemplatesController {
  constructor(private readonly service: TemplatesService) {}

  @Post()
  create(@Body() dto: TemplateDto, @User() user: LoginUserDto) {
    return this.service.save(dto, user);
  }

  @Delete()
  remove(@Body() ids: string[]) {
    return this.service.remove(ids);
  }

  @Get()
  find(@Query() dto: QueryTemplateDto) {
    return this.service.find(dto);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }

  @Post('dsl')
  saveDsl(@Body() dto: DslDto) {
    const dsl = new TemplateDsl();
    return this.service.saveDsl(Object.assign(dsl, dto));
  }

  @Get('dsl/versions/:templateId')
  findDsl(
    @Param('templateId') templateId: string,
    @Query('page') page: number,
    @Query('limit') limit: number = 10
  ) {
    return this.service.findDsl(templateId, page, limit);
  }

  @Get('dsl/latest/:templateId')
  findlatestDsl(@Param('templateId') templateId: string) {
    return this.service.getLatest(templateId);
  }
}
