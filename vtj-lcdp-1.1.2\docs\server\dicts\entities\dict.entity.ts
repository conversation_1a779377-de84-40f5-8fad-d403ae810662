import { Entity, Column } from 'typeorm';
import { BaseEntity } from '../../shared';

@Entity({
  name: 'dicts',
  comment: '数据字典',
  orderBy: {
    order: 'ASC'
  }
})
export class Dict extends BaseEntity {
  @Column({ comment: '字典名称编码', unique: true })
  name: string;

  @Column({ comment: '字典名称描述' })
  label: string;

  @Column({ comment: '字典值', default: null, nullable: true })
  value: string;

  @Column({ comment: '排序', default: 0 })
  order: number;

  @Column({ comment: '备注', default: null, nullable: true })
  notes: string;

  @Column({ comment: '分组', default: null, nullable: true })
  group: string;
}
