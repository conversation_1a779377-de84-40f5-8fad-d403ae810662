{"arrowParens": "always", "bracketSpacing": true, "bracketSameLine": true, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": true, "jsxSingleQuote": true, "printWidth": 80, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false, "vueIndentScriptAndStyle": true}