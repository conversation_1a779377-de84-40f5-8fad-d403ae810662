import {
  Controller,
  Post,
  Body,
  Query,
  Get,
  Delete,
  Param
} from '@nestjs/common';
import type { ProjectSchema } from '@vtj/core';
import { AppsService } from './apps.service';
import { AppDto } from './dto/app.dto';
import { QueryAppDto } from './dto/query-app.dto';
import { LoginUserDto } from '../users/dto/login-user.dto';
import { User, Public } from '../shared';

@Controller('apps')
export class AppsController {
  constructor(private readonly service: AppsService) {}

  @Post()
  save(@Body() dto: AppDto, @User() user: LoginUserDto) {
    dto.userId = user.id;
    return this.service.save(dto);
  }

  @Get()
  find(@Query() dto: QueryAppDto) {
    return this.service.find(dto);
  }

  @Get('action/find-my-apps')
  findMyApps(@User() user: LoginUserDto, @Query() dto: QueryAppDto) {
    return this.service.findMyApps(user.id, dto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.service.remove(id);
  }

  @Public()
  @Get(':name')
  findApp(@Param('name') name: string) {
    return this.service.fineOneByName(name);
  }

  @Post('genSoucre')
  async genSoucre(@Body() project: ProjectSchema) {
    return this.service.genSoucre(project);
  }
}
