import{c as f,M as T,g as O,O as a,P as _}from"./vue-BK7aLblh.js";const N="onShow",E="onHide",S="onLoad",d="onUnload",I="onInit",R="onSaveExitState",h="onBackPress",g="onPageScroll",C="onTabItemTap",m="onReachBottom",H="onPullDownRefresh",P="onShareTimeline",p="onShareChat",B="onAddToFavorites",U="onShareAppMessage",b="onNavigationBarButtonTap",G="onNavigationBarSearchInputClicked",y="onNavigationBarSearchInputChanged",M="onNavigationBarSearchInputConfirmed",D="onNavigationBarSearchInputFocusChanged";function w(){if(typeof globalThis!="undefined")return globalThis;if(typeof self!="undefined")return self;if(typeof window!="undefined")return window;function n(){return this}return typeof n()!="undefined"?n():function(){return new Function("return this")()}()}let c;function A(){return c||(c=w(),c)}function l(n){const e=A();if(e&&e.UTSJSONObject&&n instanceof e.UTSJSONObject){const t={};return e.UTSJSONObject.keys(n).forEach(o=>{t[o]=n[o]}),a(t)}else if(n instanceof Map){const t={};return n.forEach((o,i)=>{t[i]=o}),a(t)}else{if(O(n))return _(n);if(f(n)){const t={};for(let o=0;o<n.length;o++){const i=n[o],r=O(i)?_(i):l(i);if(r)for(const s in r)t[s]=r[s]}return t}else return a(n)}}function u(n){let e="";const t=A();if(t&&t.UTSJSONObject&&n instanceof t.UTSJSONObject)t.UTSJSONObject.keys(n).forEach(o=>{n[o]&&(e+=o+" ")});else if(n instanceof Map)n.forEach((o,i)=>{o&&(e+=i+" ")});else if(f(n))for(let o=0;o<n.length;o++){const i=u(n[o]);i&&(e+=i+" ")}else e=T(n);return e.trim()}function z(n){if(!n)return null;let{class:e,style:t}=n;return e&&!O(e)&&(n.class=u(e)),t&&(n.style=l(t)),n}const x=new RegExp(`"[^"]+"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px`,"g");function F(n,e){const t=Math.pow(10,e+1),o=Math.floor(n*t);return Math.round(o/10)*10/t}const V={unit:"rem",unitRatio:10/320,unitPrecision:5};function k(n,e,t){return o=>o.replace(x,(i,r)=>{if(!r)return i;const s=F(parseFloat(r)*e,t);return s===0?"0":`${s}${n}`})}const L=[I,S,N,E,d,h,g,C,m,H,P,U,p,B,R,b,G,y,M,D];function J(n){return[S,N].indexOf(n)>-1}function K(n){return L.indexOf(n)>-1}export{S as O,l as a,J as b,k as c,V as d,z as e,K as i,u as n};
