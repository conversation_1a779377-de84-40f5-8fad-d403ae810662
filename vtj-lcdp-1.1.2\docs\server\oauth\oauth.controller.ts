import { Controller, Get, Req, Res, Post, Body, Query } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OAuthService } from './oauth.service';
import type { Request, Response } from 'express';
import { Gitte, Public } from '../shared';

@Controller('oauth')
export class OAuthController {
  constructor(
    private oauth: OAuthService,
    private config: ConfigService
  ) {}

  @Public()
  @Get('gitee')
  gitee(@Req() req: Request, @Res() res: Response) {
    const { clientId } = this.config.get<Gitte>('gitee');
    const redirectUri =
      req.query.redirect_uri || this.oauth.getGiteeRediectUri(req);
    const url = `https://gitee.com/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(
      redirectUri as string
    )}&response_type=code`;
    res.redirect(url);
  }

  @Public()
  @Post('gitee/login')
  async giteeLogin(
    @Body('code') code: string,
    @Body('redirect_uri') redirect_uri: string
  ) {
    return await this.oauth.giteeLogin(code, redirect_uri);
  }

  @Public()
  @Get('gitee/starred')
  async giteeStarred(@Query('accessToken') accessToken: string) {
    return await this.oauth.giteeIsStarred(accessToken);
  }
}
