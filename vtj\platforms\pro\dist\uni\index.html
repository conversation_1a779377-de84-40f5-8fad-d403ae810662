<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="../favicon.ico" type="image/x-icon" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title></title>
    <!--preload-links-->
    <!--app-context-->
    <script type="module" crossorigin src="./assets/index-DD-VkZ2D.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/vue-BK7aLblh.js">
    <link rel="modulepreload" crossorigin href="./assets/@dcloudio-uni-shared-VStmjWbh.js">
    <link rel="modulepreload" crossorigin href="./assets/@dcloudio-uni-h5-vue-BEVv3z-t.js">
    <link rel="modulepreload" crossorigin href="./assets/vue-router-5EIDvL-H.js">
  </head>
  <body>
    <div id="app"><!--app-html--></div>

  </body>
</html>
