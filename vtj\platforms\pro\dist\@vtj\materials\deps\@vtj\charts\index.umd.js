(function(r,h){typeof exports=="object"&&typeof module<"u"?h(exports):typeof define=="function"&&define.amd?define(["exports"],h):(r=typeof globalThis<"u"?globalThis:r||self,h(r.VtjCharts={}))})(this,function(r){"use strict";/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/charts 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const h="0.12.70";function m(t){const s=Vue.reactive({});for(const[l,e]of Object.entries(t)){const[u,...i]=VtjUtils.kebabCase(l).split("-");if(u==="on"&&typeof e=="function"){const n=VtjUtils.camelCase(i.join("-"));s[n]=e}}return s}function y(t,s,l){const e=Vue.ref(),u=m(l);return Vue.onMounted(()=>{const n=Vue.unref(t);if(n&&(e.value||(e.value=Vue.markRaw(echarts.init(n))),e.value)){for(const[c,a]of Object.entries(u))e.value.on(c,a);e.value.setOption(s.value||{})}}),Vue.onUnmounted(()=>{if(e.value){for(const n of Object.keys(u))e.value.off(n);e.value.dispose()}}),Vue.watch(s,async n=>{e.value&&e.value.setOption(n||{})},{deep:!0}),VueUse.useResizeObserver(t,VtjUtils.debounce(()=>{e.value&&e.value.resize()},150)),{instance:e,getEChart:()=>e.value}}function C(t,s,l){const e=Vue.ref(),u=Vue.shallowRef(null),i=Vue.ref({}),n=async(c,a)=>{var V;const o=(V=e.value)==null?void 0:V.instance;o&&o.showLoading();const f=typeof a=="string"?await fetch(a).then(j=>j.json()).catch(()=>null):a;return o&&o.hideLoading(),f&&echarts.registerMap(c,f),f};return Vue.watch([t,s],async()=>{u.value=await n(t.value,s.value)}),Vue.watch(u,c=>{if(c){const a=l.option||{};a.geo&&(a.geo.map=t.value),(a.series||[]).map(f=>{(f==null?void 0:f.type)==="map"&&(f.map=t.value)}),i.value=a}}),Vue.onMounted(async()=>{u.value=await n(t.value,s.value)}),Vue.onUnmounted(()=>{var a;const c=(a=e.value)==null?void 0:a.instance;c&&c.hideLoading()}),{chartRef:e,geoJSON:u,option:i}}const p=Vue.defineComponent({name:"XChart",props:{width:{type:String,default:"100%"},height:{type:String,default:"400px"},option:{type:Object}},setup(t,{expose:s,attrs:l}){const{width:e,height:u,option:i}=Vue.toRefs(t),n=Vue.ref(),c=Vue.computed(()=>({width:e.value,height:u.value})),{instance:a,getEChart:o}=y(n,i,l);return s({elRef:n,option:i,instance:a,getEChart:o}),{elRef:n,option:i,styles:c,instance:a,getEChart:o}},render(){return Vue.h("div",{class:"x-chart",ref:"elRef",style:this.styles})}}),v=Vue.defineComponent({name:"XMapChart",inheritAttrs:!1,props:{geoJson:{type:[String,Object],default:"https://unpkg.com/vtj-geojson@0.1.3/geo/100000/100000.geoJson"},name:{type:String,default:"china"}},emits:["ready"],async setup(t,{attrs:s,emit:l}){const{name:e,geoJson:u}=Vue.toRefs(t),{chartRef:i,geoJSON:n,option:c}=C(e,u,s),a=Vue.computed(()=>{var o;return(o=i.value)==null?void 0:o.instance});return Vue.watch(n,async o=>{o&&l("ready",o)}),{chartRef:i,option:c,instance:a,getChartRef:()=>i.value,getEChart:()=>{var o;return(o=i.value)==null?void 0:o.getEChart()},geoJSON:n}},render(){return Vue.h(p,{...this.$attrs,ref:"chartRef",class:"x-map-chart",option:this.option},this.$slots)},methods:{}}),g=[p,v];var b=echarts;const d=Symbol("INSTALLED_KEY");function S(t){t[d]||(t[d]=!0,g.forEach(s=>{s.name&&t.component(s.name,s)}))}r.INSTALLED_KEY=d,r.VTJ_CHARTS_VERSION=h,r.XChart=p,r.XMapChart=v,r.components=g,r.echarts=b,r.install=S,Object.defineProperty(r,Symbol.toStringTag,{value:"Module"})});
