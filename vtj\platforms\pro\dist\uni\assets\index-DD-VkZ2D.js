const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./@dcloudio-uni-h5-vue-BEVv3z-t.js","./vue-BK7aLblh.js","./@dcloudio-uni-shared-VStmjWbh.js","./vue-router-5EIDvL-H.js"])))=>i.map(i=>d[i]);
var la=Object.defineProperty,fa=Object.defineProperties;var ha=Object.getOwnPropertyDescriptors;var _n=Object.getOwnPropertySymbols;var pa=Object.prototype.hasOwnProperty,da=Object.prototype.propertyIsEnumerable;var Kt=(e,t)=>(t=Symbol[e])?t:Symbol.for("Symbol."+e),ma=e=>{throw TypeError(e)};var ar=(e,t,r)=>t in e?la(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,V=(e,t)=>{for(var r in t||(t={}))pa.call(t,r)&&ar(e,r,t[r]);if(_n)for(var r of _n(t))da.call(t,r)&&ar(e,r,t[r]);return e},tt=(e,t)=>fa(e,ha(t));var z=(e,t,r)=>ar(e,typeof t!="symbol"?t+"":t,r);var k=(e,t,r)=>new Promise((n,i)=>{var o=l=>{try{u(r.next(l))}catch(a){i(a)}},s=l=>{try{u(r.throw(l))}catch(a){i(a)}},u=l=>l.done?n(l.value):Promise.resolve(l.value).then(o,s);u((r=r.apply(e,t)).next())}),Ct=function(e,t){this[0]=e,this[1]=t},ur=(e,t,r)=>{var n=(s,u,l,a)=>{try{var c=r[s](u),f=(u=c.value)instanceof Ct,p=c.done;Promise.resolve(f?u[0]:u).then(h=>f?n(s==="return"?s:"next",u[1]?{done:h.done,value:h.value}:h,l,a):l({value:h,done:p})).catch(h=>n("throw",h,l,a))}catch(h){a(h)}},i=s=>o[s]=u=>new Promise((l,a)=>n(s,u,l,a)),o={};return r=r.apply(e,t),o[Kt("asyncIterator")]=()=>o,i("next"),i("throw"),i("return"),o},cr=e=>{var t=e[Kt("asyncIterator")],r=!1,n,i={};return t==null?(t=e[Kt("iterator")](),n=o=>i[o]=s=>t[o](s)):(t=t.call(e),n=o=>i[o]=s=>{if(r){if(r=!1,o==="throw")throw s;return s}return r=!0,{done:!1,value:new Ct(new Promise(u=>{var l=t[o](s);l instanceof Object||ma("Object expected"),u(l)}),1)}}),i[Kt("iterator")]=()=>i,n("next"),"throw"in t?n("throw"):i.throw=o=>{throw o},"return"in t&&n("return"),i},En=(e,t,r)=>(t=e[Kt("asyncIterator")])?t.call(e):(e=e[Kt("iterator")](),t={},r=(n,i)=>(i=e[n])&&(t[n]=o=>new Promise((s,u,l)=>(o=i.call(e,o),l=o.done,Promise.resolve(o.value).then(a=>s({value:a,done:l}),u)))),r("next"),r("return"),t);import{d as Xi,g as Sn,h as $r,r as ga,q as Ke,i as ya}from"./@dcloudio-uni-h5-vue-BEVv3z-t.js";import{useRoute as va}from"./vue-router-5EIDvL-H.js";import"./vue-BK7aLblh.js";import"./@dcloudio-uni-shared-VStmjWbh.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&n(s)}).observe(document,{childList:!0,subtree:!0});function r(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=r(i);fetch(i.href,o)}})();const ba="modulepreload",wa=function(e,t){return new URL(e,t).href},xn={},Tn=function(t,r,n){let i=Promise.resolve();if(r&&r.length>0){let s=function(c){return Promise.all(c.map(f=>Promise.resolve(f).then(p=>({status:"fulfilled",value:p}),p=>({status:"rejected",reason:p}))))};const u=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),a=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));i=s(r.map(c=>{if(c=wa(c,n),c in xn)return;xn[c]=!0;const f=c.endsWith(".css"),p=f?'[rel="stylesheet"]':"";if(!!n)for(let m=u.length-1;m>=0;m--){const b=u[m];if(b.href===c&&(!f||b.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${p}`))return;const d=document.createElement("link");if(d.rel=f?"stylesheet":ba,f||(d.as="script"),d.crossOrigin="",d.href=c,a&&d.setAttribute("nonce",a),document.head.appendChild(d),f)return new Promise((m,b)=>{d.addEventListener("load",m),d.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(s){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=s,window.dispatchEvent(u),!u.defaultPrevented)throw s}return i.then(s=>{for(const u of s||[])u.status==="rejected"&&o(u.reason);return t().catch(o)})};var _a={ENV_TYPE:"live",NODE_ENV:"production"},Ea=typeof global=="object"&&global&&global.Object===Object&&global;const to=Ea;var Sa=typeof self=="object"&&self&&self.Object===Object&&self,xa=to||Sa||Function("return this")();const dt=xa;var Ta=dt.Symbol;const yt=Ta;var eo=Object.prototype,Ra=eo.hasOwnProperty,Aa=eo.toString,ce=yt?yt.toStringTag:void 0;function Oa(e){var t=Ra.call(e,ce),r=e[ce];try{e[ce]=void 0;var n=!0}catch(o){}var i=Aa.call(e);return n&&(t?e[ce]=r:delete e[ce]),i}var $a=Object.prototype,ja=$a.toString;function Da(e){return ja.call(e)}var Ba="[object Null]",Ca="[object Undefined]",Rn=yt?yt.toStringTag:void 0;function St(e){return e==null?e===void 0?Ca:Ba:Rn&&Rn in Object(e)?Oa(e):Da(e)}function bt(e){return e!=null&&typeof e=="object"}var Na="[object Symbol]";function ro(e){return typeof e=="symbol"||bt(e)&&St(e)==Na}function no(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var Ia=Array.isArray;const Ut=Ia;var An=yt?yt.prototype:void 0,On=An?An.toString:void 0;function io(e){if(typeof e=="string")return e;if(Ut(e))return no(e,io)+"";if(ro(e))return On?On.call(e):"";var t=e+"";return t=="0"&&1/e==-1/0?"-0":t}var Pa=/\s/;function ka(e){for(var t=e.length;t--&&Pa.test(e.charAt(t)););return t}var Fa=/^\s+/;function Ma(e){return e&&e.slice(0,ka(e)+1).replace(Fa,"")}function ct(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var $n=NaN,Ua=/^[-+]0x[0-9a-f]+$/i,La=/^0b[01]+$/i,Va=/^0o[0-7]+$/i,Ha=parseInt;function jn(e){if(typeof e=="number")return e;if(ro(e))return $n;if(ct(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=ct(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Ma(e);var r=La.test(e);return r||Va.test(e)?Ha(e.slice(2),r?2:8):Ua.test(e)?$n:+e}function oo(e){return e}var qa="[object AsyncFunction]",za="[object Function]",Ka="[object GeneratorFunction]",Wa="[object Proxy]";function Lt(e){if(!ct(e))return!1;var t=St(e);return t==za||t==Ka||t==qa||t==Wa}var Ja=dt["__core-js_shared__"];const lr=Ja;var Dn=function(){var e=/[^.]+$/.exec(lr&&lr.keys&&lr.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function Ya(e){return!!Dn&&Dn in e}var Ga=Function.prototype,Za=Ga.toString;function Ht(e){if(e!=null){try{return Za.call(e)}catch(t){}try{return e+""}catch(t){}}return""}var Qa=/[\\^$.*+?()[\]{}|]/g,Xa=/^\[object .+?Constructor\]$/,tu=Function.prototype,eu=Object.prototype,ru=tu.toString,nu=eu.hasOwnProperty,iu=RegExp("^"+ru.call(nu).replace(Qa,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ou(e){if(!ct(e)||Ya(e))return!1;var t=Lt(e)?iu:Xa;return t.test(Ht(e))}function su(e,t){return e==null?void 0:e[t]}function qt(e,t){var r=su(e,t);return ou(r)?r:void 0}var au=qt(dt,"WeakMap");const jr=au;var Bn=Object.create,uu=function(){function e(){}return function(t){if(!ct(t))return{};if(Bn)return Bn(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();const cu=uu;function so(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}function lu(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}var fu=800,hu=16,pu=Date.now;function du(e){var t=0,r=0;return function(){var n=pu(),i=hu-(n-r);if(r=n,i>0){if(++t>=fu)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function mu(e){return function(){return e}}var gu=function(){try{var e=qt(Object,"defineProperty");return e({},"",{}),e}catch(t){}}();const Le=gu;var yu=Le?function(e,t){return Le(e,"toString",{configurable:!0,enumerable:!1,value:mu(t),writable:!0})}:oo;const vu=yu;var bu=du(vu);const wu=bu;function _u(e,t){for(var r=-1,n=e==null?0:e.length;++r<n&&t(e[r],r,e)!==!1;);return e}var Eu=9007199254740991,Su=/^(?:0|[1-9]\d*)$/;function ao(e,t){var r=typeof e;return t=t!=null?t:Eu,!!t&&(r=="number"||r!="symbol"&&Su.test(e))&&e>-1&&e%1==0&&e<t}function nn(e,t,r){t=="__proto__"&&Le?Le(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function we(e,t){return e===t||e!==e&&t!==t}var xu=Object.prototype,Tu=xu.hasOwnProperty;function uo(e,t,r){var n=e[t];(!(Tu.call(e,t)&&we(n,r))||r===void 0&&!(t in e))&&nn(e,t,r)}function co(e,t,r,n){var i=!r;r||(r={});for(var o=-1,s=t.length;++o<s;){var u=t[o],l=n?n(r[u],e[u],u,r,e):void 0;l===void 0&&(l=e[u]),i?nn(r,u,l):uo(r,u,l)}return r}var Cn=Math.max;function Ru(e,t,r){return t=Cn(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,o=Cn(n.length-t,0),s=Array(o);++i<o;)s[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(s),so(e,this,u)}}function lo(e,t){return wu(Ru(e,t,oo),e+"")}var Au=9007199254740991;function fo(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Au}function We(e){return e!=null&&fo(e.length)&&!Lt(e)}function Ou(e,t,r){if(!ct(r))return!1;var n=typeof t;return(n=="number"?We(r)&&ao(t,r.length):n=="string"&&t in r)?we(r[t],e):!1}function ho(e){return lo(function(t,r){var n=-1,i=r.length,o=i>1?r[i-1]:void 0,s=i>2?r[2]:void 0;for(o=e.length>3&&typeof o=="function"?(i--,o):void 0,s&&Ou(r[0],r[1],s)&&(o=i<3?void 0:o,i=1),t=Object(t);++n<i;){var u=r[n];u&&e(t,u,n,o)}return t})}var $u=Object.prototype;function on(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||$u;return e===r}function ju(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var Du="[object Arguments]";function Nn(e){return bt(e)&&St(e)==Du}var po=Object.prototype,Bu=po.hasOwnProperty,Cu=po.propertyIsEnumerable,Nu=Nn(function(){return arguments}())?Nn:function(e){return bt(e)&&Bu.call(e,"callee")&&!Cu.call(e,"callee")};const Dr=Nu;function Iu(){return!1}var mo=typeof exports=="object"&&exports&&!exports.nodeType&&exports,In=mo&&typeof module=="object"&&module&&!module.nodeType&&module,Pu=In&&In.exports===mo,Pn=Pu?dt.Buffer:void 0,ku=Pn?Pn.isBuffer:void 0,Fu=ku||Iu;const sn=Fu;var Mu="[object Arguments]",Uu="[object Array]",Lu="[object Boolean]",Vu="[object Date]",Hu="[object Error]",qu="[object Function]",zu="[object Map]",Ku="[object Number]",Wu="[object Object]",Ju="[object RegExp]",Yu="[object Set]",Gu="[object String]",Zu="[object WeakMap]",Qu="[object ArrayBuffer]",Xu="[object DataView]",tc="[object Float32Array]",ec="[object Float64Array]",rc="[object Int8Array]",nc="[object Int16Array]",ic="[object Int32Array]",oc="[object Uint8Array]",sc="[object Uint8ClampedArray]",ac="[object Uint16Array]",uc="[object Uint32Array]",G={};G[tc]=G[ec]=G[rc]=G[nc]=G[ic]=G[oc]=G[sc]=G[ac]=G[uc]=!0,G[Mu]=G[Uu]=G[Qu]=G[Lu]=G[Xu]=G[Vu]=G[Hu]=G[qu]=G[zu]=G[Ku]=G[Wu]=G[Ju]=G[Yu]=G[Gu]=G[Zu]=!1;function cc(e){return bt(e)&&fo(e.length)&&!!G[St(e)]}function an(e){return function(t){return e(t)}}var go=typeof exports=="object"&&exports&&!exports.nodeType&&exports,de=go&&typeof module=="object"&&module&&!module.nodeType&&module,lc=de&&de.exports===go,fr=lc&&to.process,fc=function(){try{var e=de&&de.require&&de.require("util").types;return e||fr&&fr.binding&&fr.binding("util")}catch(t){}}();const vt=fc;var kn=vt&&vt.isTypedArray,hc=kn?an(kn):cc;const yo=hc;var pc=Object.prototype,dc=pc.hasOwnProperty;function vo(e,t){var r=Ut(e),n=!r&&Dr(e),i=!r&&!n&&sn(e),o=!r&&!n&&!i&&yo(e),s=r||n||i||o,u=s?ju(e.length,String):[],l=u.length;for(var a in e)(t||dc.call(e,a))&&!(s&&(a=="length"||i&&(a=="offset"||a=="parent")||o&&(a=="buffer"||a=="byteLength"||a=="byteOffset")||ao(a,l)))&&u.push(a);return u}function bo(e,t){return function(r){return e(t(r))}}var mc=bo(Object.keys,Object);const gc=mc;var yc=Object.prototype,vc=yc.hasOwnProperty;function bc(e){if(!on(e))return gc(e);var t=[];for(var r in Object(e))vc.call(e,r)&&r!="constructor"&&t.push(r);return t}function wo(e){return We(e)?vo(e):bc(e)}function wc(e){var t=[];if(e!=null)for(var r in Object(e))t.push(r);return t}var _c=Object.prototype,Ec=_c.hasOwnProperty;function Sc(e){if(!ct(e))return wc(e);var t=on(e),r=[];for(var n in e)n=="constructor"&&(t||!Ec.call(e,n))||r.push(n);return r}function un(e){return We(e)?vo(e,!0):Sc(e)}var xc=ho(function(e,t,r,n){co(t,un(t),e,n)});const Fn=xc;var Tc=qt(Object,"create");const me=Tc;function Rc(){this.__data__=me?me(null):{},this.size=0}function Ac(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var Oc="__lodash_hash_undefined__",$c=Object.prototype,jc=$c.hasOwnProperty;function Dc(e){var t=this.__data__;if(me){var r=t[e];return r===Oc?void 0:r}return jc.call(t,e)?t[e]:void 0}var Bc=Object.prototype,Cc=Bc.hasOwnProperty;function Nc(e){var t=this.__data__;return me?t[e]!==void 0:Cc.call(t,e)}var Ic="__lodash_hash_undefined__";function Pc(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=me&&t===void 0?Ic:t,this}function Pt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Pt.prototype.clear=Rc,Pt.prototype.delete=Ac,Pt.prototype.get=Dc,Pt.prototype.has=Nc,Pt.prototype.set=Pc;function kc(){this.__data__=[],this.size=0}function Je(e,t){for(var r=e.length;r--;)if(we(e[r][0],t))return r;return-1}var Fc=Array.prototype,Mc=Fc.splice;function Uc(e){var t=this.__data__,r=Je(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():Mc.call(t,r,1),--this.size,!0}function Lc(e){var t=this.__data__,r=Je(t,e);return r<0?void 0:t[r][1]}function Vc(e){return Je(this.__data__,e)>-1}function Hc(e,t){var r=this.__data__,n=Je(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}function Et(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Et.prototype.clear=kc,Et.prototype.delete=Uc,Et.prototype.get=Lc,Et.prototype.has=Vc,Et.prototype.set=Hc;var qc=qt(dt,"Map");const ge=qc;function zc(){this.size=0,this.__data__={hash:new Pt,map:new(ge||Et),string:new Pt}}function Kc(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Ye(e,t){var r=e.__data__;return Kc(t)?r[typeof t=="string"?"string":"hash"]:r.map}function Wc(e){var t=Ye(this,e).delete(e);return this.size-=t?1:0,t}function Jc(e){return Ye(this,e).get(e)}function Yc(e){return Ye(this,e).has(e)}function Gc(e,t){var r=Ye(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}function At(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}At.prototype.clear=zc,At.prototype.delete=Wc,At.prototype.get=Jc,At.prototype.has=Yc,At.prototype.set=Gc;var Zc="Expected a function";function cn(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(Zc);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var s=e.apply(this,n);return r.cache=o.set(i,s)||o,s};return r.cache=new(cn.Cache||At),r}cn.Cache=At;var Qc=500;function Xc(e){var t=cn(e,function(n){return r.size===Qc&&r.clear(),n}),r=t.cache;return t}var tl=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,el=/\\(\\)?/g;Xc(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(tl,function(r,n,i,o){t.push(i?o.replace(el,"$1"):n||r)}),t});function ne(e){return e==null?"":io(e)}function rl(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var nl=bo(Object.getPrototypeOf,Object);const _o=nl;var il="[object Object]",ol=Function.prototype,sl=Object.prototype,Eo=ol.toString,al=sl.hasOwnProperty,ul=Eo.call(Object);function So(e){if(!bt(e)||St(e)!=il)return!1;var t=_o(e);if(t===null)return!0;var r=al.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&Eo.call(r)==ul}var cl="[object DOMException]",ll="[object Error]";function xo(e){if(!bt(e))return!1;var t=St(e);return t==ll||t==cl||typeof e.message=="string"&&typeof e.name=="string"&&!So(e)}var fl=lo(function(e,t){try{return so(e,void 0,t)}catch(r){return xo(r)?r:new Error(r)}});const hl=fl;function pl(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var o=Array(i);++n<i;)o[n]=e[n+t];return o}function dl(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:pl(e,t,r)}var ml="\\ud800-\\udfff",gl="\\u0300-\\u036f",yl="\\ufe20-\\ufe2f",vl="\\u20d0-\\u20ff",bl=gl+yl+vl,wl="\\ufe0e\\ufe0f",_l="\\u200d",El=RegExp("["+_l+ml+bl+wl+"]");function To(e){return El.test(e)}function Sl(e){return e.split("")}var Ro="\\ud800-\\udfff",xl="\\u0300-\\u036f",Tl="\\ufe20-\\ufe2f",Rl="\\u20d0-\\u20ff",Al=xl+Tl+Rl,Ol="\\ufe0e\\ufe0f",$l="["+Ro+"]",Br="["+Al+"]",Cr="\\ud83c[\\udffb-\\udfff]",jl="(?:"+Br+"|"+Cr+")",Ao="[^"+Ro+"]",Oo="(?:\\ud83c[\\udde6-\\uddff]){2}",$o="[\\ud800-\\udbff][\\udc00-\\udfff]",Dl="\\u200d",jo=jl+"?",Do="["+Ol+"]?",Bl="(?:"+Dl+"(?:"+[Ao,Oo,$o].join("|")+")"+Do+jo+")*",Cl=Do+jo+Bl,Nl="(?:"+[Ao+Br+"?",Br,Oo,$o,$l].join("|")+")",Il=RegExp(Cr+"(?="+Cr+")|"+Nl+Cl,"g");function Pl(e){return e.match(Il)||[]}function kl(e){return To(e)?Pl(e):Sl(e)}function Fl(e){return function(t){t=ne(t);var r=To(t)?kl(t):void 0,n=r?r[0]:t.charAt(0),i=r?dl(r,1).join(""):t.slice(1);return n[e]()+i}}var Ml=Fl("toUpperCase");const Bo=Ml;function Ul(e){return Bo(ne(e).toLowerCase())}function Ll(e,t,r,n){var i=-1,o=e==null?0:e.length;for(n;++i<o;)r=t(r,e[i],i,e);return r}function Co(e){return function(t){return e==null?void 0:e[t]}}var Vl={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},Hl=Co(Vl);const ql=Hl;var zl=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Kl="\\u0300-\\u036f",Wl="\\ufe20-\\ufe2f",Jl="\\u20d0-\\u20ff",Yl=Kl+Wl+Jl,Gl="["+Yl+"]",Zl=RegExp(Gl,"g");function Ql(e){return e=ne(e),e&&e.replace(zl,ql).replace(Zl,"")}var Xl=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;function tf(e){return e.match(Xl)||[]}var ef=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;function rf(e){return ef.test(e)}var No="\\ud800-\\udfff",nf="\\u0300-\\u036f",of="\\ufe20-\\ufe2f",sf="\\u20d0-\\u20ff",af=nf+of+sf,Io="\\u2700-\\u27bf",Po="a-z\\xdf-\\xf6\\xf8-\\xff",uf="\\xac\\xb1\\xd7\\xf7",cf="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",lf="\\u2000-\\u206f",ff=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ko="A-Z\\xc0-\\xd6\\xd8-\\xde",hf="\\ufe0e\\ufe0f",Fo=uf+cf+lf+ff,Mo="['’]",Mn="["+Fo+"]",pf="["+af+"]",Uo="\\d+",df="["+Io+"]",Lo="["+Po+"]",Vo="[^"+No+Fo+Uo+Io+Po+ko+"]",mf="\\ud83c[\\udffb-\\udfff]",gf="(?:"+pf+"|"+mf+")",yf="[^"+No+"]",Ho="(?:\\ud83c[\\udde6-\\uddff]){2}",qo="[\\ud800-\\udbff][\\udc00-\\udfff]",Gt="["+ko+"]",vf="\\u200d",Un="(?:"+Lo+"|"+Vo+")",bf="(?:"+Gt+"|"+Vo+")",Ln="(?:"+Mo+"(?:d|ll|m|re|s|t|ve))?",Vn="(?:"+Mo+"(?:D|LL|M|RE|S|T|VE))?",zo=gf+"?",Ko="["+hf+"]?",wf="(?:"+vf+"(?:"+[yf,Ho,qo].join("|")+")"+Ko+zo+")*",_f="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Ef="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Sf=Ko+zo+wf,xf="(?:"+[df,Ho,qo].join("|")+")"+Sf,Tf=RegExp([Gt+"?"+Lo+"+"+Ln+"(?="+[Mn,Gt,"$"].join("|")+")",bf+"+"+Vn+"(?="+[Mn,Gt+Un,"$"].join("|")+")",Gt+"?"+Un+"+"+Ln,Gt+"+"+Vn,Ef,_f,Uo,xf].join("|"),"g");function Rf(e){return e.match(Tf)||[]}function Af(e,t,r){return e=ne(e),t=t,t===void 0?rf(e)?Rf(e):tf(e):e.match(t)||[]}var Of="['’]",$f=RegExp(Of,"g");function jf(e){return function(t){return Ll(Af(Ql(t).replace($f,"")),e,"")}}var Df=jf(function(e,t,r){return t=t.toLowerCase(),e+(r?Ul(t):t)});const wt=Df;function Bf(){this.__data__=new Et,this.size=0}function Cf(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function Nf(e){return this.__data__.get(e)}function If(e){return this.__data__.has(e)}var Pf=200;function kf(e,t){var r=this.__data__;if(r instanceof Et){var n=r.__data__;if(!ge||n.length<Pf-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new At(n)}return r.set(e,t),this.size=r.size,this}function kt(e){var t=this.__data__=new Et(e);this.size=t.size}kt.prototype.clear=Bf,kt.prototype.delete=Cf,kt.prototype.get=Nf,kt.prototype.has=If,kt.prototype.set=kf;var Wo=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Hn=Wo&&typeof module=="object"&&module&&!module.nodeType&&module,Ff=Hn&&Hn.exports===Wo,qn=Ff?dt.Buffer:void 0,zn=qn?qn.allocUnsafe:void 0;function Jo(e,t){if(t)return e.slice();var r=e.length,n=zn?zn(r):new e.constructor(r);return e.copy(n),n}function Mf(e,t){for(var r=-1,n=e==null?0:e.length,i=0,o=[];++r<n;){var s=e[r];t(s,r,e)&&(o[i++]=s)}return o}function Uf(){return[]}var Lf=Object.prototype,Vf=Lf.propertyIsEnumerable,Kn=Object.getOwnPropertySymbols,Hf=Kn?function(e){return e==null?[]:(e=Object(e),Mf(Kn(e),function(t){return Vf.call(e,t)}))}:Uf;const qf=Hf;function zf(e,t,r){var n=t(e);return Ut(e)?n:rl(n,r(e))}function Kf(e){return zf(e,wo,qf)}var Wf=qt(dt,"DataView");const Nr=Wf;var Jf=qt(dt,"Promise");const Ir=Jf;var Yf=qt(dt,"Set");const Pr=Yf;var Wn="[object Map]",Gf="[object Object]",Jn="[object Promise]",Yn="[object Set]",Gn="[object WeakMap]",Zn="[object DataView]",Zf=Ht(Nr),Qf=Ht(ge),Xf=Ht(Ir),th=Ht(Pr),eh=Ht(jr),It=St;(Nr&&It(new Nr(new ArrayBuffer(1)))!=Zn||ge&&It(new ge)!=Wn||Ir&&It(Ir.resolve())!=Jn||Pr&&It(new Pr)!=Yn||jr&&It(new jr)!=Gn)&&(It=function(e){var t=St(e),r=t==Gf?e.constructor:void 0,n=r?Ht(r):"";if(n)switch(n){case Zf:return Zn;case Qf:return Wn;case Xf:return Jn;case th:return Yn;case eh:return Gn}return t});const ln=It;var rh=Object.prototype,nh=rh.hasOwnProperty;function ih(e){var t=e.length,r=new e.constructor(t);return t&&typeof e[0]=="string"&&nh.call(e,"index")&&(r.index=e.index,r.input=e.input),r}var oh=dt.Uint8Array;const Qn=oh;function fn(e){var t=new e.constructor(e.byteLength);return new Qn(t).set(new Qn(e)),t}function sh(e,t){var r=fn(e.buffer);return new e.constructor(r,e.byteOffset,e.byteLength)}var ah=/\w*$/;function uh(e){var t=new e.constructor(e.source,ah.exec(e));return t.lastIndex=e.lastIndex,t}var Xn=yt?yt.prototype:void 0,ti=Xn?Xn.valueOf:void 0;function ch(e){return ti?Object(ti.call(e)):{}}function Yo(e,t){var r=t?fn(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}var lh="[object Boolean]",fh="[object Date]",hh="[object Map]",ph="[object Number]",dh="[object RegExp]",mh="[object Set]",gh="[object String]",yh="[object Symbol]",vh="[object ArrayBuffer]",bh="[object DataView]",wh="[object Float32Array]",_h="[object Float64Array]",Eh="[object Int8Array]",Sh="[object Int16Array]",xh="[object Int32Array]",Th="[object Uint8Array]",Rh="[object Uint8ClampedArray]",Ah="[object Uint16Array]",Oh="[object Uint32Array]";function $h(e,t,r){var n=e.constructor;switch(t){case vh:return fn(e);case lh:case fh:return new n(+e);case bh:return sh(e);case wh:case _h:case Eh:case Sh:case xh:case Th:case Rh:case Ah:case Oh:return Yo(e,r);case hh:return new n;case ph:case gh:return new n(e);case dh:return uh(e);case mh:return new n;case yh:return ch(e)}}function Go(e){return typeof e.constructor=="function"&&!on(e)?cu(_o(e)):{}}var jh="[object Map]";function Dh(e){return bt(e)&&ln(e)==jh}var ei=vt&&vt.isMap,Bh=ei?an(ei):Dh;const Ch=Bh;var Nh="[object Set]";function Ih(e){return bt(e)&&ln(e)==Nh}var ri=vt&&vt.isSet,Ph=ri?an(ri):Ih;const kh=Ph;var Fh=1,Zo="[object Arguments]",Mh="[object Array]",Uh="[object Boolean]",Lh="[object Date]",Vh="[object Error]",Qo="[object Function]",Hh="[object GeneratorFunction]",qh="[object Map]",zh="[object Number]",Xo="[object Object]",Kh="[object RegExp]",Wh="[object Set]",Jh="[object String]",Yh="[object Symbol]",Gh="[object WeakMap]",Zh="[object ArrayBuffer]",Qh="[object DataView]",Xh="[object Float32Array]",tp="[object Float64Array]",ep="[object Int8Array]",rp="[object Int16Array]",np="[object Int32Array]",ip="[object Uint8Array]",op="[object Uint8ClampedArray]",sp="[object Uint16Array]",ap="[object Uint32Array]",Y={};Y[Zo]=Y[Mh]=Y[Zh]=Y[Qh]=Y[Uh]=Y[Lh]=Y[Xh]=Y[tp]=Y[ep]=Y[rp]=Y[np]=Y[qh]=Y[zh]=Y[Xo]=Y[Kh]=Y[Wh]=Y[Jh]=Y[Yh]=Y[ip]=Y[op]=Y[sp]=Y[ap]=!0,Y[Vh]=Y[Qo]=Y[Gh]=!1;function Ce(e,t,r,n,i,o){var s,u=t&Fh;if(s!==void 0)return s;if(!ct(e))return e;var l=Ut(e);if(l)s=ih(e);else{var a=ln(e),c=a==Qo||a==Hh;if(sn(e))return Jo(e,u);if(a==Xo||a==Zo||c&&!i)s=c?{}:Go(e);else{if(!Y[a])return i?e:{};s=$h(e,a,u)}}o||(o=new kt);var f=o.get(e);if(f)return f;o.set(e,s),kh(e)?e.forEach(function(d){s.add(Ce(d,t,r,d,e,o))}):Ch(e)&&e.forEach(function(d,m){s.set(m,Ce(d,t,r,m,e,o))});var p=Kf,h=l?void 0:p(e);return _u(h||e,function(d,m){h&&(m=d,d=e[m]),uo(s,m,Ce(d,t,r,m,e,o))}),s}var up=1,cp=4;function ni(e){return Ce(e,up|cp)}var ii=yt?yt.prototype:void 0;ii&&ii.valueOf;function lp(e){return function(t,r,n){for(var i=-1,o=Object(t),s=n(t),u=s.length;u--;){var l=s[++i];if(r(o[l],l,o)===!1)break}return t}}var fp=lp();const hp=fp;var pp=function(){return dt.Date.now()};const hr=pp;var dp="Expected a function",mp=Math.max,gp=Math.min;function ts(e,t,r){var n,i,o,s,u,l,a=0,c=!1,f=!1,p=!0;if(typeof e!="function")throw new TypeError(dp);t=jn(t)||0,ct(r)&&(c=!!r.leading,f="maxWait"in r,o=f?mp(jn(r.maxWait)||0,t):o,p="trailing"in r?!!r.trailing:p);function h(g){var x=n,A=i;return n=i=void 0,a=g,s=e.apply(A,x),s}function d(g){return a=g,u=setTimeout(_,t),c?h(g):s}function m(g){var x=g-l,A=g-a,U=t-x;return f?gp(U,o-A):U}function b(g){var x=g-l,A=g-a;return l===void 0||x>=t||x<0||f&&A>=o}function _(){var g=hr();if(b(g))return y(g);u=setTimeout(_,m(g))}function y(g){return u=void 0,p&&n?h(g):(n=i=void 0,s)}function v(){u!==void 0&&clearTimeout(u),a=0,n=l=i=u=void 0}function w(){return u===void 0?s:y(hr())}function S(){var g=hr(),x=b(g);if(n=arguments,i=this,l=g,x){if(u===void 0)return d(l);if(f)return clearTimeout(u),u=setTimeout(_,t),h(l)}return u===void 0&&(u=setTimeout(_,t)),s}return S.cancel=v,S.flush=w,S}function kr(e,t,r){(r!==void 0&&!we(e[t],r)||r===void 0&&!(t in e))&&nn(e,t,r)}function yp(e){return bt(e)&&We(e)}function Fr(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function vp(e){return co(e,un(e))}function bp(e,t,r,n,i,o,s){var u=Fr(e,r),l=Fr(t,r),a=s.get(l);if(a){kr(e,r,a);return}var c=o?o(u,l,r+"",e,t,s):void 0,f=c===void 0;if(f){var p=Ut(l),h=!p&&sn(l),d=!p&&!h&&yo(l);c=l,p||h||d?Ut(u)?c=u:yp(u)?c=lu(u):h?(f=!1,c=Jo(l,!0)):d?(f=!1,c=Yo(l,!0)):c=[]:So(l)||Dr(l)?(c=u,Dr(u)?c=vp(u):(!ct(u)||Lt(u))&&(c=Go(l))):f=!1}f&&(s.set(l,c),i(c,l,n,o,s),s.delete(l)),kr(e,r,c)}function es(e,t,r,n,i){e!==t&&hp(t,function(o,s){if(i||(i=new kt),ct(o))bp(e,t,s,r,es,n,i);else{var u=n?n(Fr(e,s),o,s+"",e,t,i):void 0;u===void 0&&(u=o),kr(e,s,u)}},un)}var wp={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},_p=Co(wp);const Ep=_p;var rs=/[&<>"']/g,Sp=RegExp(rs.source);function xp(e){return e=ne(e),e&&Sp.test(e)?e.replace(rs,Ep):e}var Tp="[object String]";function $t(e){return typeof e=="string"||!Ut(e)&&bt(e)&&St(e)==Tp}function Rp(e,t){return no(t,function(r){return e[r]})}vt&&vt.isArrayBuffer;vt&&vt.isDate;var Ap=ho(function(e,t,r){es(e,t,r)});const he=Ap;var ns=Object.prototype,Op=ns.hasOwnProperty;function oi(e,t,r,n){return e===void 0||we(e,ns[r])&&!Op.call(n,r)?t:e}var $p={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"};function jp(e){return"\\"+$p[e]}var Dp=/<%=([\s\S]+?)%>/g;const is=Dp;var Bp=/<%-([\s\S]+?)%>/g;const Cp=Bp;var Np=/<%([\s\S]+?)%>/g;const Ip=Np;var Pp={escape:Cp,evaluate:Ip,interpolate:is,variable:"",imports:{_:{escape:xp}}};const si=Pp;var kp="Invalid `variable` option passed into `_.template`",Fp=/\b__p \+= '';/g,Mp=/\b(__p \+=) '' \+/g,Up=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Lp=/[()=,{}\[\]\/\s]/,Vp=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,xe=/($^)/,Hp=/['\n\r\u2028\u2029\\]/g,qp=Object.prototype,ai=qp.hasOwnProperty;function zp(e,t,r){var n=si.imports._.templateSettings||si;e=ne(e),t=Fn({},t,n,oi);var i=Fn({},t.imports,n.imports,oi),o=wo(i),s=Rp(i,o),u,l,a=0,c=t.interpolate||xe,f="__p += '",p=RegExp((t.escape||xe).source+"|"+c.source+"|"+(c===is?Vp:xe).source+"|"+(t.evaluate||xe).source+"|$","g"),h=ai.call(t,"sourceURL")?"//# sourceURL="+(t.sourceURL+"").replace(/\s/g," ")+`
`:"";e.replace(p,function(b,_,y,v,w,S){return y||(y=v),f+=e.slice(a,S).replace(Hp,jp),_&&(u=!0,f+=`' +
__e(`+_+`) +
'`),w&&(l=!0,f+=`';
`+w+`;
__p += '`),y&&(f+=`' +
((__t = (`+y+`)) == null ? '' : __t) +
'`),a=S+b.length,b}),f+=`';
`;var d=ai.call(t,"variable")&&t.variable;if(!d)f=`with (obj) {
`+f+`
}
`;else if(Lp.test(d))throw new Error(kp);f=(l?f.replace(Fp,""):f).replace(Mp,"$1").replace(Up,"$1;"),f="function("+(d||"obj")+`) {
`+(d?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(u?", __e = _.escape":"")+(l?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+f+`return __p
}`;var m=hl(function(){return Function(o,h+"return "+f).apply(void 0,s)});if(m.source=f,xo(m))throw m;return m}var Kp="Expected a function";function Wp(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(Kp);return ct(r)&&(n="leading"in r?!0:n,i="trailing"in r?!1:i),ts(e,t,{leading:n,maxWait:t,trailing:i})}var it=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ie(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var os={exports:{}};function Jp(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var ui={exports:{}},ci;function jt(){return ci||(ci=1,function(e,t){(function(r,n){e.exports=n()})(it,function(){var r=r||function(n,i){var o;if(typeof window<"u"&&window.crypto&&(o=window.crypto),typeof self<"u"&&self.crypto&&(o=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(o=globalThis.crypto),!o&&typeof window<"u"&&window.msCrypto&&(o=window.msCrypto),!o&&typeof it<"u"&&it.crypto&&(o=it.crypto),!o&&typeof Jp=="function")try{o=require("crypto")}catch(y){}var s=function(){if(o){if(typeof o.getRandomValues=="function")try{return o.getRandomValues(new Uint32Array(1))[0]}catch(y){}if(typeof o.randomBytes=="function")try{return o.randomBytes(4).readInt32LE()}catch(y){}}throw new Error("Native crypto module could not be used to get secure random number.")},u=Object.create||function(){function y(){}return function(v){var w;return y.prototype=v,w=new y,y.prototype=null,w}}(),l={},a=l.lib={},c=a.Base=function(){return{extend:function(y){var v=u(this);return y&&v.mixIn(y),(!v.hasOwnProperty("init")||this.init===v.init)&&(v.init=function(){v.$super.init.apply(this,arguments)}),v.init.prototype=v,v.$super=this,v},create:function(){var y=this.extend();return y.init.apply(y,arguments),y},init:function(){},mixIn:function(y){for(var v in y)y.hasOwnProperty(v)&&(this[v]=y[v]);y.hasOwnProperty("toString")&&(this.toString=y.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),f=a.WordArray=c.extend({init:function(y,v){y=this.words=y||[],v!=i?this.sigBytes=v:this.sigBytes=y.length*4},toString:function(y){return(y||h).stringify(this)},concat:function(y){var v=this.words,w=y.words,S=this.sigBytes,g=y.sigBytes;if(this.clamp(),S%4)for(var x=0;x<g;x++){var A=w[x>>>2]>>>24-x%4*8&255;v[S+x>>>2]|=A<<24-(S+x)%4*8}else for(var U=0;U<g;U+=4)v[S+U>>>2]=w[U>>>2];return this.sigBytes+=g,this},clamp:function(){var y=this.words,v=this.sigBytes;y[v>>>2]&=4294967295<<32-v%4*8,y.length=n.ceil(v/4)},clone:function(){var y=c.clone.call(this);return y.words=this.words.slice(0),y},random:function(y){for(var v=[],w=0;w<y;w+=4)v.push(s());return new f.init(v,y)}}),p=l.enc={},h=p.Hex={stringify:function(y){for(var v=y.words,w=y.sigBytes,S=[],g=0;g<w;g++){var x=v[g>>>2]>>>24-g%4*8&255;S.push((x>>>4).toString(16)),S.push((x&15).toString(16))}return S.join("")},parse:function(y){for(var v=y.length,w=[],S=0;S<v;S+=2)w[S>>>3]|=parseInt(y.substr(S,2),16)<<24-S%8*4;return new f.init(w,v/2)}},d=p.Latin1={stringify:function(y){for(var v=y.words,w=y.sigBytes,S=[],g=0;g<w;g++){var x=v[g>>>2]>>>24-g%4*8&255;S.push(String.fromCharCode(x))}return S.join("")},parse:function(y){for(var v=y.length,w=[],S=0;S<v;S++)w[S>>>2]|=(y.charCodeAt(S)&255)<<24-S%4*8;return new f.init(w,v)}},m=p.Utf8={stringify:function(y){try{return decodeURIComponent(escape(d.stringify(y)))}catch(v){throw new Error("Malformed UTF-8 data")}},parse:function(y){return d.parse(unescape(encodeURIComponent(y)))}},b=a.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new f.init,this._nDataBytes=0},_append:function(y){typeof y=="string"&&(y=m.parse(y)),this._data.concat(y),this._nDataBytes+=y.sigBytes},_process:function(y){var v,w=this._data,S=w.words,g=w.sigBytes,x=this.blockSize,A=x*4,U=g/A;y?U=n.ceil(U):U=n.max((U|0)-this._minBufferSize,0);var E=U*x,T=n.min(E*4,g);if(E){for(var F=0;F<E;F+=x)this._doProcessBlock(S,F);v=S.splice(0,E),w.sigBytes-=T}return new f.init(v,T)},clone:function(){var y=c.clone.call(this);return y._data=this._data.clone(),y},_minBufferSize:0});a.Hasher=b.extend({cfg:c.extend(),init:function(y){this.cfg=this.cfg.extend(y),this.reset()},reset:function(){b.reset.call(this),this._doReset()},update:function(y){return this._append(y),this._process(),this},finalize:function(y){y&&this._append(y);var v=this._doFinalize();return v},blockSize:16,_createHelper:function(y){return function(v,w){return new y.init(w).finalize(v)}},_createHmacHelper:function(y){return function(v,w){return new _.HMAC.init(y,w).finalize(v)}}});var _=l.algo={};return l}(Math);return r})}(ui)),ui.exports}(function(e,t){(function(r,n){e.exports=n(jt())})(it,function(r){return function(n){var i=r,o=i.lib,s=o.WordArray,u=o.Hasher,l=i.algo,a=[];(function(){for(var m=0;m<64;m++)a[m]=n.abs(n.sin(m+1))*4294967296|0})();var c=l.MD5=u.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(m,b){for(var _=0;_<16;_++){var y=b+_,v=m[y];m[y]=(v<<8|v>>>24)&16711935|(v<<24|v>>>8)&4278255360}var w=this._hash.words,S=m[b+0],g=m[b+1],x=m[b+2],A=m[b+3],U=m[b+4],E=m[b+5],T=m[b+6],F=m[b+7],B=m[b+8],P=m[b+9],j=m[b+10],N=m[b+11],L=m[b+12],M=m[b+13],H=m[b+14],q=m[b+15],O=w[0],D=w[1],C=w[2],I=w[3];O=f(O,D,C,I,S,7,a[0]),I=f(I,O,D,C,g,12,a[1]),C=f(C,I,O,D,x,17,a[2]),D=f(D,C,I,O,A,22,a[3]),O=f(O,D,C,I,U,7,a[4]),I=f(I,O,D,C,E,12,a[5]),C=f(C,I,O,D,T,17,a[6]),D=f(D,C,I,O,F,22,a[7]),O=f(O,D,C,I,B,7,a[8]),I=f(I,O,D,C,P,12,a[9]),C=f(C,I,O,D,j,17,a[10]),D=f(D,C,I,O,N,22,a[11]),O=f(O,D,C,I,L,7,a[12]),I=f(I,O,D,C,M,12,a[13]),C=f(C,I,O,D,H,17,a[14]),D=f(D,C,I,O,q,22,a[15]),O=p(O,D,C,I,g,5,a[16]),I=p(I,O,D,C,T,9,a[17]),C=p(C,I,O,D,N,14,a[18]),D=p(D,C,I,O,S,20,a[19]),O=p(O,D,C,I,E,5,a[20]),I=p(I,O,D,C,j,9,a[21]),C=p(C,I,O,D,q,14,a[22]),D=p(D,C,I,O,U,20,a[23]),O=p(O,D,C,I,P,5,a[24]),I=p(I,O,D,C,H,9,a[25]),C=p(C,I,O,D,A,14,a[26]),D=p(D,C,I,O,B,20,a[27]),O=p(O,D,C,I,M,5,a[28]),I=p(I,O,D,C,x,9,a[29]),C=p(C,I,O,D,F,14,a[30]),D=p(D,C,I,O,L,20,a[31]),O=h(O,D,C,I,E,4,a[32]),I=h(I,O,D,C,B,11,a[33]),C=h(C,I,O,D,N,16,a[34]),D=h(D,C,I,O,H,23,a[35]),O=h(O,D,C,I,g,4,a[36]),I=h(I,O,D,C,U,11,a[37]),C=h(C,I,O,D,F,16,a[38]),D=h(D,C,I,O,j,23,a[39]),O=h(O,D,C,I,M,4,a[40]),I=h(I,O,D,C,S,11,a[41]),C=h(C,I,O,D,A,16,a[42]),D=h(D,C,I,O,T,23,a[43]),O=h(O,D,C,I,P,4,a[44]),I=h(I,O,D,C,L,11,a[45]),C=h(C,I,O,D,q,16,a[46]),D=h(D,C,I,O,x,23,a[47]),O=d(O,D,C,I,S,6,a[48]),I=d(I,O,D,C,F,10,a[49]),C=d(C,I,O,D,H,15,a[50]),D=d(D,C,I,O,E,21,a[51]),O=d(O,D,C,I,L,6,a[52]),I=d(I,O,D,C,A,10,a[53]),C=d(C,I,O,D,j,15,a[54]),D=d(D,C,I,O,g,21,a[55]),O=d(O,D,C,I,B,6,a[56]),I=d(I,O,D,C,q,10,a[57]),C=d(C,I,O,D,T,15,a[58]),D=d(D,C,I,O,M,21,a[59]),O=d(O,D,C,I,U,6,a[60]),I=d(I,O,D,C,N,10,a[61]),C=d(C,I,O,D,x,15,a[62]),D=d(D,C,I,O,P,21,a[63]),w[0]=w[0]+O|0,w[1]=w[1]+D|0,w[2]=w[2]+C|0,w[3]=w[3]+I|0},_doFinalize:function(){var m=this._data,b=m.words,_=this._nDataBytes*8,y=m.sigBytes*8;b[y>>>5]|=128<<24-y%32;var v=n.floor(_/4294967296),w=_;b[(y+64>>>9<<4)+15]=(v<<8|v>>>24)&16711935|(v<<24|v>>>8)&4278255360,b[(y+64>>>9<<4)+14]=(w<<8|w>>>24)&16711935|(w<<24|w>>>8)&4278255360,m.sigBytes=(b.length+1)*4,this._process();for(var S=this._hash,g=S.words,x=0;x<4;x++){var A=g[x];g[x]=(A<<8|A>>>24)&16711935|(A<<24|A>>>8)&4278255360}return S},clone:function(){var m=u.clone.call(this);return m._hash=this._hash.clone(),m}});function f(m,b,_,y,v,w,S){var g=m+(b&_|~b&y)+v+S;return(g<<w|g>>>32-w)+b}function p(m,b,_,y,v,w,S){var g=m+(b&y|_&~y)+v+S;return(g<<w|g>>>32-w)+b}function h(m,b,_,y,v,w,S){var g=m+(b^_^y)+v+S;return(g<<w|g>>>32-w)+b}function d(m,b,_,y,v,w,S){var g=m+(_^(b|~y))+v+S;return(g<<w|g>>>32-w)+b}i.MD5=u._createHelper(c),i.HmacMD5=u._createHmacHelper(c)}(Math),r.MD5})})(os);var ss=os.exports;ie(ss);var as={exports:{}};(function(e,t){(function(r,n){e.exports=n(jt())})(it,function(r){return function(){var n=r,i=n.lib,o=i.WordArray,s=n.enc;s.Base64={stringify:function(l){var a=l.words,c=l.sigBytes,f=this._map;l.clamp();for(var p=[],h=0;h<c;h+=3)for(var d=a[h>>>2]>>>24-h%4*8&255,m=a[h+1>>>2]>>>24-(h+1)%4*8&255,b=a[h+2>>>2]>>>24-(h+2)%4*8&255,_=d<<16|m<<8|b,y=0;y<4&&h+y*.75<c;y++)p.push(f.charAt(_>>>6*(3-y)&63));var v=f.charAt(64);if(v)for(;p.length%4;)p.push(v);return p.join("")},parse:function(l){var a=l.length,c=this._map,f=this._reverseMap;if(!f){f=this._reverseMap=[];for(var p=0;p<c.length;p++)f[c.charCodeAt(p)]=p}var h=c.charAt(64);if(h){var d=l.indexOf(h);d!==-1&&(a=d)}return u(l,a,f)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};function u(l,a,c){for(var f=[],p=0,h=0;h<a;h++)if(h%4){var d=c[l.charCodeAt(h-1)]<<h%4*2,m=c[l.charCodeAt(h)]>>>6-h%4*2,b=d|m;f[p>>>2]|=b<<24-p%4*8,p++}return o.create(f,p)}}(),r.enc.Base64})})(as);var us=as.exports;ie(us);var cs={exports:{}};(function(e,t){(function(r,n){e.exports=n(jt())})(it,function(r){return r.enc.Utf8})})(cs);var Yp=cs.exports;ie(Yp);var ls={exports:{}},li={exports:{}},fi={exports:{}},hi;function Gp(){return hi||(hi=1,function(e,t){(function(r,n){e.exports=n(jt())})(it,function(r){return function(){var n=r,i=n.lib,o=i.WordArray,s=i.Hasher,u=n.algo,l=[],a=u.SHA1=s.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(c,f){for(var p=this._hash.words,h=p[0],d=p[1],m=p[2],b=p[3],_=p[4],y=0;y<80;y++){if(y<16)l[y]=c[f+y]|0;else{var v=l[y-3]^l[y-8]^l[y-14]^l[y-16];l[y]=v<<1|v>>>31}var w=(h<<5|h>>>27)+_+l[y];y<20?w+=(d&m|~d&b)+1518500249:y<40?w+=(d^m^b)+1859775393:y<60?w+=(d&m|d&b|m&b)-1894007588:w+=(d^m^b)-899497514,_=b,b=m,m=d<<30|d>>>2,d=h,h=w}p[0]=p[0]+h|0,p[1]=p[1]+d|0,p[2]=p[2]+m|0,p[3]=p[3]+b|0,p[4]=p[4]+_|0},_doFinalize:function(){var c=this._data,f=c.words,p=this._nDataBytes*8,h=c.sigBytes*8;return f[h>>>5]|=128<<24-h%32,f[(h+64>>>9<<4)+14]=Math.floor(p/4294967296),f[(h+64>>>9<<4)+15]=p,c.sigBytes=f.length*4,this._process(),this._hash},clone:function(){var c=s.clone.call(this);return c._hash=this._hash.clone(),c}});n.SHA1=s._createHelper(a),n.HmacSHA1=s._createHmacHelper(a)}(),r.SHA1})}(fi)),fi.exports}var pi={exports:{}},di;function Zp(){return di||(di=1,function(e,t){(function(r,n){e.exports=n(jt())})(it,function(r){(function(){var n=r,i=n.lib,o=i.Base,s=n.enc,u=s.Utf8,l=n.algo;l.HMAC=o.extend({init:function(a,c){a=this._hasher=new a.init,typeof c=="string"&&(c=u.parse(c));var f=a.blockSize,p=f*4;c.sigBytes>p&&(c=a.finalize(c)),c.clamp();for(var h=this._oKey=c.clone(),d=this._iKey=c.clone(),m=h.words,b=d.words,_=0;_<f;_++)m[_]^=1549556828,b[_]^=909522486;h.sigBytes=d.sigBytes=p,this.reset()},reset:function(){var a=this._hasher;a.reset(),a.update(this._iKey)},update:function(a){return this._hasher.update(a),this},finalize:function(a){var c=this._hasher,f=c.finalize(a);c.reset();var p=c.finalize(this._oKey.clone().concat(f));return p}})})()})}(pi)),pi.exports}var mi;function fs(){return mi||(mi=1,function(e,t){(function(r,n,i){e.exports=n(jt(),Gp(),Zp())})(it,function(r){return function(){var n=r,i=n.lib,o=i.Base,s=i.WordArray,u=n.algo,l=u.MD5,a=u.EvpKDF=o.extend({cfg:o.extend({keySize:128/32,hasher:l,iterations:1}),init:function(c){this.cfg=this.cfg.extend(c)},compute:function(c,f){for(var p,h=this.cfg,d=h.hasher.create(),m=s.create(),b=m.words,_=h.keySize,y=h.iterations;b.length<_;){p&&d.update(p),p=d.update(c).finalize(f),d.reset();for(var v=1;v<y;v++)p=d.finalize(p),d.reset();m.concat(p)}return m.sigBytes=_*4,m}});n.EvpKDF=function(c,f,p){return a.create(p).compute(c,f)}}(),r.EvpKDF})}(li)),li.exports}var gi={exports:{}},yi;function Qp(){return yi||(yi=1,function(e,t){(function(r,n,i){e.exports=n(jt(),fs())})(it,function(r){r.lib.Cipher||function(n){var i=r,o=i.lib,s=o.Base,u=o.WordArray,l=o.BufferedBlockAlgorithm,a=i.enc;a.Utf8;var c=a.Base64,f=i.algo,p=f.EvpKDF,h=o.Cipher=l.extend({cfg:s.extend(),createEncryptor:function(E,T){return this.create(this._ENC_XFORM_MODE,E,T)},createDecryptor:function(E,T){return this.create(this._DEC_XFORM_MODE,E,T)},init:function(E,T,F){this.cfg=this.cfg.extend(F),this._xformMode=E,this._key=T,this.reset()},reset:function(){l.reset.call(this),this._doReset()},process:function(E){return this._append(E),this._process()},finalize:function(E){E&&this._append(E);var T=this._doFinalize();return T},keySize:128/32,ivSize:128/32,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function E(T){return typeof T=="string"?U:g}return function(T){return{encrypt:function(F,B,P){return E(B).encrypt(T,F,B,P)},decrypt:function(F,B,P){return E(B).decrypt(T,F,B,P)}}}}()});o.StreamCipher=h.extend({_doFinalize:function(){var E=this._process(!0);return E},blockSize:1});var d=i.mode={},m=o.BlockCipherMode=s.extend({createEncryptor:function(E,T){return this.Encryptor.create(E,T)},createDecryptor:function(E,T){return this.Decryptor.create(E,T)},init:function(E,T){this._cipher=E,this._iv=T}}),b=d.CBC=function(){var E=m.extend();E.Encryptor=E.extend({processBlock:function(F,B){var P=this._cipher,j=P.blockSize;T.call(this,F,B,j),P.encryptBlock(F,B),this._prevBlock=F.slice(B,B+j)}}),E.Decryptor=E.extend({processBlock:function(F,B){var P=this._cipher,j=P.blockSize,N=F.slice(B,B+j);P.decryptBlock(F,B),T.call(this,F,B,j),this._prevBlock=N}});function T(F,B,P){var j,N=this._iv;N?(j=N,this._iv=n):j=this._prevBlock;for(var L=0;L<P;L++)F[B+L]^=j[L]}return E}(),_=i.pad={},y=_.Pkcs7={pad:function(E,T){for(var F=T*4,B=F-E.sigBytes%F,P=B<<24|B<<16|B<<8|B,j=[],N=0;N<B;N+=4)j.push(P);var L=u.create(j,B);E.concat(L)},unpad:function(E){var T=E.words[E.sigBytes-1>>>2]&255;E.sigBytes-=T}};o.BlockCipher=h.extend({cfg:h.cfg.extend({mode:b,padding:y}),reset:function(){var E;h.reset.call(this);var T=this.cfg,F=T.iv,B=T.mode;this._xformMode==this._ENC_XFORM_MODE?E=B.createEncryptor:(E=B.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==E?this._mode.init(this,F&&F.words):(this._mode=E.call(B,this,F&&F.words),this._mode.__creator=E)},_doProcessBlock:function(E,T){this._mode.processBlock(E,T)},_doFinalize:function(){var E,T=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(T.pad(this._data,this.blockSize),E=this._process(!0)):(E=this._process(!0),T.unpad(E)),E},blockSize:128/32});var v=o.CipherParams=s.extend({init:function(E){this.mixIn(E)},toString:function(E){return(E||this.formatter).stringify(this)}}),w=i.format={},S=w.OpenSSL={stringify:function(E){var T,F=E.ciphertext,B=E.salt;return B?T=u.create([1398893684,1701076831]).concat(B).concat(F):T=F,T.toString(c)},parse:function(E){var T,F=c.parse(E),B=F.words;return B[0]==1398893684&&B[1]==1701076831&&(T=u.create(B.slice(2,4)),B.splice(0,4),F.sigBytes-=16),v.create({ciphertext:F,salt:T})}},g=o.SerializableCipher=s.extend({cfg:s.extend({format:S}),encrypt:function(E,T,F,B){B=this.cfg.extend(B);var P=E.createEncryptor(F,B),j=P.finalize(T),N=P.cfg;return v.create({ciphertext:j,key:F,iv:N.iv,algorithm:E,mode:N.mode,padding:N.padding,blockSize:E.blockSize,formatter:B.format})},decrypt:function(E,T,F,B){B=this.cfg.extend(B),T=this._parse(T,B.format);var P=E.createDecryptor(F,B).finalize(T.ciphertext);return P},_parse:function(E,T){return typeof E=="string"?T.parse(E,this):E}}),x=i.kdf={},A=x.OpenSSL={execute:function(E,T,F,B,P){if(B||(B=u.random(64/8)),P)var j=p.create({keySize:T+F,hasher:P}).compute(E,B);else var j=p.create({keySize:T+F}).compute(E,B);var N=u.create(j.words.slice(T),F*4);return j.sigBytes=T*4,v.create({key:j,iv:N,salt:B})}},U=o.PasswordBasedCipher=g.extend({cfg:g.cfg.extend({kdf:A}),encrypt:function(E,T,F,B){B=this.cfg.extend(B);var P=B.kdf.execute(F,E.keySize,E.ivSize,B.salt,B.hasher);B.iv=P.iv;var j=g.encrypt.call(this,E,T,P.key,B);return j.mixIn(P),j},decrypt:function(E,T,F,B){B=this.cfg.extend(B),T=this._parse(T,B.format);var P=B.kdf.execute(F,E.keySize,E.ivSize,T.salt,B.hasher);B.iv=P.iv;var j=g.decrypt.call(this,E,T,P.key,B);return j}})}()})}(gi)),gi.exports}(function(e,t){(function(r,n,i){e.exports=n(jt(),us,ss,fs(),Qp())})(it,function(r){return function(){var n=r,i=n.lib,o=i.BlockCipher,s=n.algo,u=[],l=[],a=[],c=[],f=[],p=[],h=[],d=[],m=[],b=[];(function(){for(var v=[],w=0;w<256;w++)w<128?v[w]=w<<1:v[w]=w<<1^283;for(var S=0,g=0,w=0;w<256;w++){var x=g^g<<1^g<<2^g<<3^g<<4;x=x>>>8^x&255^99,u[S]=x,l[x]=S;var A=v[S],U=v[A],E=v[U],T=v[x]*257^x*16843008;a[S]=T<<24|T>>>8,c[S]=T<<16|T>>>16,f[S]=T<<8|T>>>24,p[S]=T;var T=E*16843009^U*65537^A*257^S*16843008;h[x]=T<<24|T>>>8,d[x]=T<<16|T>>>16,m[x]=T<<8|T>>>24,b[x]=T,S?(S=A^v[v[v[E^A]]],g^=v[v[g]]):S=g=1}})();var _=[0,1,2,4,8,16,32,64,128,27,54],y=s.AES=o.extend({_doReset:function(){var v;if(!(this._nRounds&&this._keyPriorReset===this._key)){for(var w=this._keyPriorReset=this._key,S=w.words,g=w.sigBytes/4,x=this._nRounds=g+6,A=(x+1)*4,U=this._keySchedule=[],E=0;E<A;E++)E<g?U[E]=S[E]:(v=U[E-1],E%g?g>6&&E%g==4&&(v=u[v>>>24]<<24|u[v>>>16&255]<<16|u[v>>>8&255]<<8|u[v&255]):(v=v<<8|v>>>24,v=u[v>>>24]<<24|u[v>>>16&255]<<16|u[v>>>8&255]<<8|u[v&255],v^=_[E/g|0]<<24),U[E]=U[E-g]^v);for(var T=this._invKeySchedule=[],F=0;F<A;F++){var E=A-F;if(F%4)var v=U[E];else var v=U[E-4];F<4||E<=4?T[F]=v:T[F]=h[u[v>>>24]]^d[u[v>>>16&255]]^m[u[v>>>8&255]]^b[u[v&255]]}}},encryptBlock:function(v,w){this._doCryptBlock(v,w,this._keySchedule,a,c,f,p,u)},decryptBlock:function(v,w){var S=v[w+1];v[w+1]=v[w+3],v[w+3]=S,this._doCryptBlock(v,w,this._invKeySchedule,h,d,m,b,l);var S=v[w+1];v[w+1]=v[w+3],v[w+3]=S},_doCryptBlock:function(v,w,S,g,x,A,U,E){for(var T=this._nRounds,F=v[w]^S[0],B=v[w+1]^S[1],P=v[w+2]^S[2],j=v[w+3]^S[3],N=4,L=1;L<T;L++){var M=g[F>>>24]^x[B>>>16&255]^A[P>>>8&255]^U[j&255]^S[N++],H=g[B>>>24]^x[P>>>16&255]^A[j>>>8&255]^U[F&255]^S[N++],q=g[P>>>24]^x[j>>>16&255]^A[F>>>8&255]^U[B&255]^S[N++],O=g[j>>>24]^x[F>>>16&255]^A[B>>>8&255]^U[P&255]^S[N++];F=M,B=H,P=q,j=O}var M=(E[F>>>24]<<24|E[B>>>16&255]<<16|E[P>>>8&255]<<8|E[j&255])^S[N++],H=(E[B>>>24]<<24|E[P>>>16&255]<<16|E[j>>>8&255]<<8|E[F&255])^S[N++],q=(E[P>>>24]<<24|E[j>>>16&255]<<16|E[F>>>8&255]<<8|E[B&255])^S[N++],O=(E[j>>>24]<<24|E[F>>>16&255]<<16|E[B>>>8&255]<<8|E[P&255])^S[N++];v[w]=M,v[w+1]=H,v[w+2]=q,v[w+3]=O},keySize:256/32});n.AES=o._createHelper(y)}(),r.AES})})(ls);var Xp=ls.exports;ie(Xp);var td="0123456789abcdefghijklmnopqrstuvwxyz";function _t(e){return td.charAt(e)}function ed(e,t){return e&t}function Te(e,t){return e|t}function vi(e,t){return e^t}function bi(e,t){return e&~t}function rd(e){if(e==0)return-1;var t=0;return e&65535||(e>>=16,t+=16),e&255||(e>>=8,t+=8),e&15||(e>>=4,t+=4),e&3||(e>>=2,t+=2),e&1||++t,t}function nd(e){for(var t=0;e!=0;)e&=e-1,++t;return t}var Zt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",hs="=";function Ve(e){var t,r,n="";for(t=0;t+3<=e.length;t+=3)r=parseInt(e.substring(t,t+3),16),n+=Zt.charAt(r>>6)+Zt.charAt(r&63);for(t+1==e.length?(r=parseInt(e.substring(t,t+1),16),n+=Zt.charAt(r<<2)):t+2==e.length&&(r=parseInt(e.substring(t,t+2),16),n+=Zt.charAt(r>>2)+Zt.charAt((r&3)<<4));(n.length&3)>0;)n+=hs;return n}function wi(e){var t="",r,n=0,i=0;for(r=0;r<e.length&&e.charAt(r)!=hs;++r){var o=Zt.indexOf(e.charAt(r));o<0||(n==0?(t+=_t(o>>2),i=o&3,n=1):n==1?(t+=_t(i<<2|o>>4),i=o&15,n=2):n==2?(t+=_t(i),t+=_t(o>>2),i=o&3,n=3):(t+=_t(i<<2|o>>4),t+=_t(o&15),n=0))}return n==1&&(t+=_t(i<<2)),t}var Wt,id={decode:function(e){var t;if(Wt===void 0){var r="0123456789ABCDEF",n=` \f
\r	 \u2028\u2029`;for(Wt={},t=0;t<16;++t)Wt[r.charAt(t)]=t;for(r=r.toLowerCase(),t=10;t<16;++t)Wt[r.charAt(t)]=t;for(t=0;t<n.length;++t)Wt[n.charAt(t)]=-1}var i=[],o=0,s=0;for(t=0;t<e.length;++t){var u=e.charAt(t);if(u=="=")break;if(u=Wt[u],u!=-1){if(u===void 0)throw new Error("Illegal character at offset "+t);o|=u,++s>=2?(i[i.length]=o,o=0,s=0):o<<=4}}if(s)throw new Error("Hex encoding incomplete: 4 bits missing");return i}},Nt,Mr={decode:function(e){var t;if(Nt===void 0){var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=`= \f
\r	 \u2028\u2029`;for(Nt=Object.create(null),t=0;t<64;++t)Nt[r.charAt(t)]=t;for(Nt["-"]=62,Nt._=63,t=0;t<n.length;++t)Nt[n.charAt(t)]=-1}var i=[],o=0,s=0;for(t=0;t<e.length;++t){var u=e.charAt(t);if(u=="=")break;if(u=Nt[u],u!=-1){if(u===void 0)throw new Error("Illegal character at offset "+t);o|=u,++s>=4?(i[i.length]=o>>16,i[i.length]=o>>8&255,i[i.length]=o&255,o=0,s=0):o<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=o>>10;break;case 3:i[i.length]=o>>16,i[i.length]=o>>8&255;break}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(e){var t=Mr.re.exec(e);if(t)if(t[1])e=t[1];else if(t[2])e=t[2];else throw new Error("RegExp out of sync");return Mr.decode(e)}},Jt=1e13,pe=function(){function e(t){this.buf=[+t||0]}return e.prototype.mulAdd=function(t,r){var n=this.buf,i=n.length,o,s;for(o=0;o<i;++o)s=n[o]*t+r,s<Jt?r=0:(r=0|s/Jt,s-=r*Jt),n[o]=s;r>0&&(n[o]=r)},e.prototype.sub=function(t){var r=this.buf,n=r.length,i,o;for(i=0;i<n;++i)o=r[i]-t,o<0?(o+=Jt,t=1):t=0,r[i]=o;for(;r[r.length-1]===0;)r.pop()},e.prototype.toString=function(t){if((t||10)!=10)throw new Error("only base 10 is supported");for(var r=this.buf,n=r[r.length-1].toString(),i=r.length-2;i>=0;--i)n+=(Jt+r[i]).toString().substring(1);return n},e.prototype.valueOf=function(){for(var t=this.buf,r=0,n=t.length-1;n>=0;--n)r=r*Jt+t[n];return r},e.prototype.simplify=function(){var t=this.buf;return t.length==1?t[0]:this},e}(),ps="…",od=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,sd=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function Xt(e,t){return e.length>t&&(e=e.substring(0,t)+ps),e}var pr=function(){function e(t,r){this.hexDigits="0123456789ABCDEF",t instanceof e?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=r)}return e.prototype.get=function(t){if(t===void 0&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return typeof this.enc=="string"?this.enc.charCodeAt(t):this.enc[t]},e.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(t&15)},e.prototype.hexDump=function(t,r,n){for(var i="",o=t;o<r;++o)if(i+=this.hexByte(this.get(o)),n!==!0)switch(o&15){case 7:i+="  ";break;case 15:i+=`
`;break;default:i+=" "}return i},e.prototype.isASCII=function(t,r){for(var n=t;n<r;++n){var i=this.get(n);if(i<32||i>176)return!1}return!0},e.prototype.parseStringISO=function(t,r){for(var n="",i=t;i<r;++i)n+=String.fromCharCode(this.get(i));return n},e.prototype.parseStringUTF=function(t,r){for(var n="",i=t;i<r;){var o=this.get(i++);o<128?n+=String.fromCharCode(o):o>191&&o<224?n+=String.fromCharCode((o&31)<<6|this.get(i++)&63):n+=String.fromCharCode((o&15)<<12|(this.get(i++)&63)<<6|this.get(i++)&63)}return n},e.prototype.parseStringBMP=function(t,r){for(var n="",i,o,s=t;s<r;)i=this.get(s++),o=this.get(s++),n+=String.fromCharCode(i<<8|o);return n},e.prototype.parseTime=function(t,r,n){var i=this.parseStringISO(t,r),o=(n?od:sd).exec(i);return o?(n&&(o[1]=+o[1],o[1]+=+o[1]<70?2e3:1900),i=o[1]+"-"+o[2]+"-"+o[3]+" "+o[4],o[5]&&(i+=":"+o[5],o[6]&&(i+=":"+o[6],o[7]&&(i+="."+o[7]))),o[8]&&(i+=" UTC",o[8]!="Z"&&(i+=o[8],o[9]&&(i+=":"+o[9]))),i):"Unrecognized time: "+i},e.prototype.parseInteger=function(t,r){for(var n=this.get(t),i=n>127,o=i?255:0,s,u="";n==o&&++t<r;)n=this.get(t);if(s=r-t,s===0)return i?-1:0;if(s>4){for(u=n,s<<=3;!((+u^o)&128);)u=+u<<1,--s;u="("+s+` bit)
`}i&&(n=n-256);for(var l=new pe(n),a=t+1;a<r;++a)l.mulAdd(256,this.get(a));return u+l.toString()},e.prototype.parseBitString=function(t,r,n){for(var i=this.get(t),o=(r-t-1<<3)-i,s="("+o+` bit)
`,u="",l=t+1;l<r;++l){for(var a=this.get(l),c=l==r-1?i:0,f=7;f>=c;--f)u+=a>>f&1?"1":"0";if(u.length>n)return s+Xt(u,n)}return s+u},e.prototype.parseOctetString=function(t,r,n){if(this.isASCII(t,r))return Xt(this.parseStringISO(t,r),n);var i=r-t,o="("+i+` byte)
`;n/=2,i>n&&(r=t+n);for(var s=t;s<r;++s)o+=this.hexByte(this.get(s));return i>n&&(o+=ps),o},e.prototype.parseOID=function(t,r,n){for(var i="",o=new pe,s=0,u=t;u<r;++u){var l=this.get(u);if(o.mulAdd(128,l&127),s+=7,!(l&128)){if(i==="")if(o=o.simplify(),o instanceof pe)o.sub(80),i="2."+o.toString();else{var a=o<80?o<40?0:1:2;i=a+"."+(o-a*40)}else i+="."+o.toString();if(i.length>n)return Xt(i,n);o=new pe,s=0}}return s>0&&(i+=".incomplete"),i},e}(),ad=function(){function e(t,r,n,i,o){if(!(i instanceof _i))throw new Error("Invalid tag value.");this.stream=t,this.header=r,this.length=n,this.tag=i,this.sub=o}return e.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},e.prototype.content=function(t){if(this.tag===void 0)return null;t===void 0&&(t=1/0);var r=this.posContent(),n=Math.abs(this.length);if(!this.tag.isUniversal())return this.sub!==null?"("+this.sub.length+" elem)":this.stream.parseOctetString(r,r+n,t);switch(this.tag.tagNumber){case 1:return this.stream.get(r)===0?"false":"true";case 2:return this.stream.parseInteger(r,r+n);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(r,r+n,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(r,r+n,t);case 6:return this.stream.parseOID(r,r+n,t);case 16:case 17:return this.sub!==null?"("+this.sub.length+" elem)":"(no elem)";case 12:return Xt(this.stream.parseStringUTF(r,r+n),t);case 18:case 19:case 20:case 21:case 22:case 26:return Xt(this.stream.parseStringISO(r,r+n),t);case 30:return Xt(this.stream.parseStringBMP(r,r+n),t);case 23:case 24:return this.stream.parseTime(r,r+n,this.tag.tagNumber==23)}return null},e.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(this.sub===null?"null":this.sub.length)+"]"},e.prototype.toPrettyString=function(t){t===void 0&&(t="");var r=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(r+="+"),r+=this.length,this.tag.tagConstructed?r+=" (constructed)":this.tag.isUniversal()&&(this.tag.tagNumber==3||this.tag.tagNumber==4)&&this.sub!==null&&(r+=" (encapsulates)"),r+=`
`,this.sub!==null){t+="  ";for(var n=0,i=this.sub.length;n<i;++n)r+=this.sub[n].toPrettyString(t)}return r},e.prototype.posStart=function(){return this.stream.pos},e.prototype.posContent=function(){return this.stream.pos+this.header},e.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},e.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},e.decodeLength=function(t){var r=t.get(),n=r&127;if(n==r)return n;if(n>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(n===0)return null;r=0;for(var i=0;i<n;++i)r=r*256+t.get();return r},e.prototype.getHexStringValue=function(){var t=this.toHexString(),r=this.header*2,n=this.length*2;return t.substr(r,n)},e.decode=function(t){var r;t instanceof pr?r=t:r=new pr(t,0);var n=new pr(r),i=new _i(r),o=e.decodeLength(r),s=r.pos,u=s-n.pos,l=null,a=function(){var f=[];if(o!==null){for(var p=s+o;r.pos<p;)f[f.length]=e.decode(r);if(r.pos!=p)throw new Error("Content size is not correct for container starting at offset "+s)}else try{for(;;){var h=e.decode(r);if(h.tag.isEOC())break;f[f.length]=h}o=s-r.pos}catch(d){throw new Error("Exception while decoding undefined length content: "+d)}return f};if(i.tagConstructed)l=a();else if(i.isUniversal()&&(i.tagNumber==3||i.tagNumber==4))try{if(i.tagNumber==3&&r.get()!=0)throw new Error("BIT STRINGs with unused bits cannot encapsulate.");l=a();for(var c=0;c<l.length;++c)if(l[c].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(f){l=null}if(l===null){if(o===null)throw new Error("We can't skip over an invalid tag with undefined length at offset "+s);r.pos=s+Math.abs(o)}return new e(n,u,o,i,l)},e}(),_i=function(){function e(t){var r=t.get();if(this.tagClass=r>>6,this.tagConstructed=(r&32)!==0,this.tagNumber=r&31,this.tagNumber==31){var n=new pe;do r=t.get(),n.mulAdd(128,r&127);while(r&128);this.tagNumber=n.simplify()}}return e.prototype.isUniversal=function(){return this.tagClass===0},e.prototype.isEOC=function(){return this.tagClass===0&&this.tagNumber===0},e}(),Tt,ud=0xdeadbeefcafe,Ei=(ud&16777215)==15715070,nt=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],cd=(1<<26)/nt[nt.length-1],W=function(){function e(t,r,n){t!=null&&(typeof t=="number"?this.fromNumber(t,r,n):r==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,r))}return e.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var r;if(t==16)r=4;else if(t==8)r=3;else if(t==2)r=1;else if(t==32)r=5;else if(t==4)r=2;else return this.toRadix(t);var n=(1<<r)-1,i,o=!1,s="",u=this.t,l=this.DB-u*this.DB%r;if(u-- >0)for(l<this.DB&&(i=this[u]>>l)>0&&(o=!0,s=_t(i));u>=0;)l<r?(i=(this[u]&(1<<l)-1)<<r-l,i|=this[--u]>>(l+=this.DB-r)):(i=this[u]>>(l-=r)&n,l<=0&&(l+=this.DB,--u)),i>0&&(o=!0),o&&(s+=_t(i));return o?s:"0"},e.prototype.negate=function(){var t=J();return e.ZERO.subTo(this,t),t},e.prototype.abs=function(){return this.s<0?this.negate():this},e.prototype.compareTo=function(t){var r=this.s-t.s;if(r!=0)return r;var n=this.t;if(r=n-t.t,r!=0)return this.s<0?-r:r;for(;--n>=0;)if((r=this[n]-t[n])!=0)return r;return 0},e.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+Re(this[this.t-1]^this.s&this.DM)},e.prototype.mod=function(t){var r=J();return this.abs().divRemTo(t,null,r),this.s<0&&r.compareTo(e.ZERO)>0&&t.subTo(r,r),r},e.prototype.modPowInt=function(t,r){var n;return t<256||r.isEven()?n=new Si(r):n=new xi(r),this.exp(t,n)},e.prototype.clone=function(){var t=J();return this.copyTo(t),t},e.prototype.intValue=function(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},e.prototype.byteValue=function(){return this.t==0?this.s:this[0]<<24>>24},e.prototype.shortValue=function(){return this.t==0?this.s:this[0]<<16>>16},e.prototype.signum=function(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1},e.prototype.toByteArray=function(){var t=this.t,r=[];r[0]=this.s;var n=this.DB-t*this.DB%8,i,o=0;if(t-- >0)for(n<this.DB&&(i=this[t]>>n)!=(this.s&this.DM)>>n&&(r[o++]=i|this.s<<this.DB-n);t>=0;)n<8?(i=(this[t]&(1<<n)-1)<<8-n,i|=this[--t]>>(n+=this.DB-8)):(i=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),i&128&&(i|=-256),o==0&&(this.s&128)!=(i&128)&&++o,(o>0||i!=this.s)&&(r[o++]=i);return r},e.prototype.equals=function(t){return this.compareTo(t)==0},e.prototype.min=function(t){return this.compareTo(t)<0?this:t},e.prototype.max=function(t){return this.compareTo(t)>0?this:t},e.prototype.and=function(t){var r=J();return this.bitwiseTo(t,ed,r),r},e.prototype.or=function(t){var r=J();return this.bitwiseTo(t,Te,r),r},e.prototype.xor=function(t){var r=J();return this.bitwiseTo(t,vi,r),r},e.prototype.andNot=function(t){var r=J();return this.bitwiseTo(t,bi,r),r},e.prototype.not=function(){for(var t=J(),r=0;r<this.t;++r)t[r]=this.DM&~this[r];return t.t=this.t,t.s=~this.s,t},e.prototype.shiftLeft=function(t){var r=J();return t<0?this.rShiftTo(-t,r):this.lShiftTo(t,r),r},e.prototype.shiftRight=function(t){var r=J();return t<0?this.lShiftTo(-t,r):this.rShiftTo(t,r),r},e.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+rd(this[t]);return this.s<0?this.t*this.DB:-1},e.prototype.bitCount=function(){for(var t=0,r=this.s&this.DM,n=0;n<this.t;++n)t+=nd(this[n]^r);return t},e.prototype.testBit=function(t){var r=Math.floor(t/this.DB);return r>=this.t?this.s!=0:(this[r]&1<<t%this.DB)!=0},e.prototype.setBit=function(t){return this.changeBit(t,Te)},e.prototype.clearBit=function(t){return this.changeBit(t,bi)},e.prototype.flipBit=function(t){return this.changeBit(t,vi)},e.prototype.add=function(t){var r=J();return this.addTo(t,r),r},e.prototype.subtract=function(t){var r=J();return this.subTo(t,r),r},e.prototype.multiply=function(t){var r=J();return this.multiplyTo(t,r),r},e.prototype.divide=function(t){var r=J();return this.divRemTo(t,r,null),r},e.prototype.remainder=function(t){var r=J();return this.divRemTo(t,null,r),r},e.prototype.divideAndRemainder=function(t){var r=J(),n=J();return this.divRemTo(t,r,n),[r,n]},e.prototype.modPow=function(t,r){var n=t.bitLength(),i,o=Rt(1),s;if(n<=0)return o;n<18?i=1:n<48?i=3:n<144?i=4:n<768?i=5:i=6,n<8?s=new Si(r):r.isEven()?s=new fd(r):s=new xi(r);var u=[],l=3,a=i-1,c=(1<<i)-1;if(u[1]=s.convert(this),i>1){var f=J();for(s.sqrTo(u[1],f);l<=c;)u[l]=J(),s.mulTo(f,u[l-2],u[l]),l+=2}var p=t.t-1,h,d=!0,m=J(),b;for(n=Re(t[p])-1;p>=0;){for(n>=a?h=t[p]>>n-a&c:(h=(t[p]&(1<<n+1)-1)<<a-n,p>0&&(h|=t[p-1]>>this.DB+n-a)),l=i;!(h&1);)h>>=1,--l;if((n-=l)<0&&(n+=this.DB,--p),d)u[h].copyTo(o),d=!1;else{for(;l>1;)s.sqrTo(o,m),s.sqrTo(m,o),l-=2;l>0?s.sqrTo(o,m):(b=o,o=m,m=b),s.mulTo(m,u[h],o)}for(;p>=0&&!(t[p]&1<<n);)s.sqrTo(o,m),b=o,o=m,m=b,--n<0&&(n=this.DB-1,--p)}return s.revert(o)},e.prototype.modInverse=function(t){var r=t.isEven();if(this.isEven()&&r||t.signum()==0)return e.ZERO;for(var n=t.clone(),i=this.clone(),o=Rt(1),s=Rt(0),u=Rt(0),l=Rt(1);n.signum()!=0;){for(;n.isEven();)n.rShiftTo(1,n),r?((!o.isEven()||!s.isEven())&&(o.addTo(this,o),s.subTo(t,s)),o.rShiftTo(1,o)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;i.isEven();)i.rShiftTo(1,i),r?((!u.isEven()||!l.isEven())&&(u.addTo(this,u),l.subTo(t,l)),u.rShiftTo(1,u)):l.isEven()||l.subTo(t,l),l.rShiftTo(1,l);n.compareTo(i)>=0?(n.subTo(i,n),r&&o.subTo(u,o),s.subTo(l,s)):(i.subTo(n,i),r&&u.subTo(o,u),l.subTo(s,l))}if(i.compareTo(e.ONE)!=0)return e.ZERO;if(l.compareTo(t)>=0)return l.subtract(t);if(l.signum()<0)l.addTo(t,l);else return l;return l.signum()<0?l.add(t):l},e.prototype.pow=function(t){return this.exp(t,new ld)},e.prototype.gcd=function(t){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var i=r;r=n,n=i}var o=r.getLowestSetBit(),s=n.getLowestSetBit();if(s<0)return r;for(o<s&&(s=o),s>0&&(r.rShiftTo(s,r),n.rShiftTo(s,n));r.signum()>0;)(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n));return s>0&&n.lShiftTo(s,n),n},e.prototype.isProbablePrime=function(t){var r,n=this.abs();if(n.t==1&&n[0]<=nt[nt.length-1]){for(r=0;r<nt.length;++r)if(n[0]==nt[r])return!0;return!1}if(n.isEven())return!1;for(r=1;r<nt.length;){for(var i=nt[r],o=r+1;o<nt.length&&i<cd;)i*=nt[o++];for(i=n.modInt(i);r<o;)if(i%nt[r++]==0)return!1}return n.millerRabin(t)},e.prototype.copyTo=function(t){for(var r=this.t-1;r>=0;--r)t[r]=this[r];t.t=this.t,t.s=this.s},e.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},e.prototype.fromString=function(t,r){var n;if(r==16)n=4;else if(r==8)n=3;else if(r==256)n=8;else if(r==2)n=1;else if(r==32)n=5;else if(r==4)n=2;else{this.fromRadix(t,r);return}this.t=0,this.s=0;for(var i=t.length,o=!1,s=0;--i>=0;){var u=n==8?+t[i]&255:Ri(t,i);if(u<0){t.charAt(i)=="-"&&(o=!0);continue}o=!1,s==0?this[this.t++]=u:s+n>this.DB?(this[this.t-1]|=(u&(1<<this.DB-s)-1)<<s,this[this.t++]=u>>this.DB-s):this[this.t-1]|=u<<s,s+=n,s>=this.DB&&(s-=this.DB)}n==8&&+t[0]&128&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),o&&e.ZERO.subTo(this,this)},e.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},e.prototype.dlShiftTo=function(t,r){var n;for(n=this.t-1;n>=0;--n)r[n+t]=this[n];for(n=t-1;n>=0;--n)r[n]=0;r.t=this.t+t,r.s=this.s},e.prototype.drShiftTo=function(t,r){for(var n=t;n<this.t;++n)r[n-t]=this[n];r.t=Math.max(this.t-t,0),r.s=this.s},e.prototype.lShiftTo=function(t,r){for(var n=t%this.DB,i=this.DB-n,o=(1<<i)-1,s=Math.floor(t/this.DB),u=this.s<<n&this.DM,l=this.t-1;l>=0;--l)r[l+s+1]=this[l]>>i|u,u=(this[l]&o)<<n;for(var l=s-1;l>=0;--l)r[l]=0;r[s]=u,r.t=this.t+s+1,r.s=this.s,r.clamp()},e.prototype.rShiftTo=function(t,r){r.s=this.s;var n=Math.floor(t/this.DB);if(n>=this.t){r.t=0;return}var i=t%this.DB,o=this.DB-i,s=(1<<i)-1;r[0]=this[n]>>i;for(var u=n+1;u<this.t;++u)r[u-n-1]|=(this[u]&s)<<o,r[u-n]=this[u]>>i;i>0&&(r[this.t-n-1]|=(this.s&s)<<o),r.t=this.t-n,r.clamp()},e.prototype.subTo=function(t,r){for(var n=0,i=0,o=Math.min(t.t,this.t);n<o;)i+=this[n]-t[n],r[n++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;n<this.t;)i+=this[n],r[n++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;n<t.t;)i-=t[n],r[n++]=i&this.DM,i>>=this.DB;i-=t.s}r.s=i<0?-1:0,i<-1?r[n++]=this.DV+i:i>0&&(r[n++]=i),r.t=n,r.clamp()},e.prototype.multiplyTo=function(t,r){var n=this.abs(),i=t.abs(),o=n.t;for(r.t=o+i.t;--o>=0;)r[o]=0;for(o=0;o<i.t;++o)r[o+n.t]=n.am(0,i[o],r,o,0,n.t);r.s=0,r.clamp(),this.s!=t.s&&e.ZERO.subTo(r,r)},e.prototype.squareTo=function(t){for(var r=this.abs(),n=t.t=2*r.t;--n>=0;)t[n]=0;for(n=0;n<r.t-1;++n){var i=r.am(n,r[n],t,2*n,0,1);(t[n+r.t]+=r.am(n+1,2*r[n],t,2*n+1,i,r.t-n-1))>=r.DV&&(t[n+r.t]-=r.DV,t[n+r.t+1]=1)}t.t>0&&(t[t.t-1]+=r.am(n,r[n],t,2*n,0,1)),t.s=0,t.clamp()},e.prototype.divRemTo=function(t,r,n){var i=t.abs();if(!(i.t<=0)){var o=this.abs();if(o.t<i.t){r==null||r.fromInt(0),n!=null&&this.copyTo(n);return}n==null&&(n=J());var s=J(),u=this.s,l=t.s,a=this.DB-Re(i[i.t-1]);a>0?(i.lShiftTo(a,s),o.lShiftTo(a,n)):(i.copyTo(s),o.copyTo(n));var c=s.t,f=s[c-1];if(f!=0){var p=f*(1<<this.F1)+(c>1?s[c-2]>>this.F2:0),h=this.FV/p,d=(1<<this.F1)/p,m=1<<this.F2,b=n.t,_=b-c,y=r!=null?r:J();for(s.dlShiftTo(_,y),n.compareTo(y)>=0&&(n[n.t++]=1,n.subTo(y,n)),e.ONE.dlShiftTo(c,y),y.subTo(s,s);s.t<c;)s[s.t++]=0;for(;--_>=0;){var v=n[--b]==f?this.DM:Math.floor(n[b]*h+(n[b-1]+m)*d);if((n[b]+=s.am(0,v,n,_,0,c))<v)for(s.dlShiftTo(_,y),n.subTo(y,n);n[b]<--v;)n.subTo(y,n)}r!=null&&(n.drShiftTo(c,r),u!=l&&e.ZERO.subTo(r,r)),n.t=c,n.clamp(),a>0&&n.rShiftTo(a,n),u<0&&e.ZERO.subTo(n,n)}}},e.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(t&1))return 0;var r=t&3;return r=r*(2-(t&15)*r)&15,r=r*(2-(t&255)*r)&255,r=r*(2-((t&65535)*r&65535))&65535,r=r*(2-t*r%this.DV)%this.DV,r>0?this.DV-r:-r},e.prototype.isEven=function(){return(this.t>0?this[0]&1:this.s)==0},e.prototype.exp=function(t,r){if(t>4294967295||t<1)return e.ONE;var n=J(),i=J(),o=r.convert(this),s=Re(t)-1;for(o.copyTo(n);--s>=0;)if(r.sqrTo(n,i),(t&1<<s)>0)r.mulTo(i,o,n);else{var u=n;n=i,i=u}return r.revert(n)},e.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},e.prototype.toRadix=function(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var r=this.chunkSize(t),n=Math.pow(t,r),i=Rt(n),o=J(),s=J(),u="";for(this.divRemTo(i,o,s);o.signum()>0;)u=(n+s.intValue()).toString(t).substr(1)+u,o.divRemTo(i,o,s);return s.intValue().toString(t)+u},e.prototype.fromRadix=function(t,r){this.fromInt(0),r==null&&(r=10);for(var n=this.chunkSize(r),i=Math.pow(r,n),o=!1,s=0,u=0,l=0;l<t.length;++l){var a=Ri(t,l);if(a<0){t.charAt(l)=="-"&&this.signum()==0&&(o=!0);continue}u=r*u+a,++s>=n&&(this.dMultiply(i),this.dAddOffset(u,0),s=0,u=0)}s>0&&(this.dMultiply(Math.pow(r,s)),this.dAddOffset(u,0)),o&&e.ZERO.subTo(this,this)},e.prototype.fromNumber=function(t,r,n){if(typeof r=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,n),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),Te,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(e.ONE.shiftLeft(t-1),this);else{var i=[],o=t&7;i.length=(t>>3)+1,r.nextBytes(i),o>0?i[0]&=(1<<o)-1:i[0]=0,this.fromString(i,256)}},e.prototype.bitwiseTo=function(t,r,n){var i,o,s=Math.min(t.t,this.t);for(i=0;i<s;++i)n[i]=r(this[i],t[i]);if(t.t<this.t){for(o=t.s&this.DM,i=s;i<this.t;++i)n[i]=r(this[i],o);n.t=this.t}else{for(o=this.s&this.DM,i=s;i<t.t;++i)n[i]=r(o,t[i]);n.t=t.t}n.s=r(this.s,t.s),n.clamp()},e.prototype.changeBit=function(t,r){var n=e.ONE.shiftLeft(t);return this.bitwiseTo(n,r,n),n},e.prototype.addTo=function(t,r){for(var n=0,i=0,o=Math.min(t.t,this.t);n<o;)i+=this[n]+t[n],r[n++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;n<this.t;)i+=this[n],r[n++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;n<t.t;)i+=t[n],r[n++]=i&this.DM,i>>=this.DB;i+=t.s}r.s=i<0?-1:0,i>0?r[n++]=i:i<-1&&(r[n++]=this.DV+i),r.t=n,r.clamp()},e.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},e.prototype.dAddOffset=function(t,r){if(t!=0){for(;this.t<=r;)this[this.t++]=0;for(this[r]+=t;this[r]>=this.DV;)this[r]-=this.DV,++r>=this.t&&(this[this.t++]=0),++this[r]}},e.prototype.multiplyLowerTo=function(t,r,n){var i=Math.min(this.t+t.t,r);for(n.s=0,n.t=i;i>0;)n[--i]=0;for(var o=n.t-this.t;i<o;++i)n[i+this.t]=this.am(0,t[i],n,i,0,this.t);for(var o=Math.min(t.t,r);i<o;++i)this.am(0,t[i],n,i,0,r-i);n.clamp()},e.prototype.multiplyUpperTo=function(t,r,n){--r;var i=n.t=this.t+t.t-r;for(n.s=0;--i>=0;)n[i]=0;for(i=Math.max(r-this.t,0);i<t.t;++i)n[this.t+i-r]=this.am(r-i,t[i],n,0,0,this.t+i-r);n.clamp(),n.drShiftTo(1,n)},e.prototype.modInt=function(t){if(t<=0)return 0;var r=this.DV%t,n=this.s<0?t-1:0;if(this.t>0)if(r==0)n=this[0]%t;else for(var i=this.t-1;i>=0;--i)n=(r*n+this[i])%t;return n},e.prototype.millerRabin=function(t){var r=this.subtract(e.ONE),n=r.getLowestSetBit();if(n<=0)return!1;var i=r.shiftRight(n);t=t+1>>1,t>nt.length&&(t=nt.length);for(var o=J(),s=0;s<t;++s){o.fromInt(nt[Math.floor(Math.random()*nt.length)]);var u=o.modPow(i,this);if(u.compareTo(e.ONE)!=0&&u.compareTo(r)!=0){for(var l=1;l++<n&&u.compareTo(r)!=0;)if(u=u.modPowInt(2,this),u.compareTo(e.ONE)==0)return!1;if(u.compareTo(r)!=0)return!1}}return!0},e.prototype.square=function(){var t=J();return this.squareTo(t),t},e.prototype.gcda=function(t,r){var n=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(n.compareTo(i)<0){var o=n;n=i,i=o}var s=n.getLowestSetBit(),u=i.getLowestSetBit();if(u<0){r(n);return}s<u&&(u=s),u>0&&(n.rShiftTo(u,n),i.rShiftTo(u,i));var l=function(){(s=n.getLowestSetBit())>0&&n.rShiftTo(s,n),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),n.compareTo(i)>=0?(n.subTo(i,n),n.rShiftTo(1,n)):(i.subTo(n,i),i.rShiftTo(1,i)),n.signum()>0?setTimeout(l,0):(u>0&&i.lShiftTo(u,i),setTimeout(function(){r(i)},0))};setTimeout(l,10)},e.prototype.fromNumberAsync=function(t,r,n,i){if(typeof r=="number")if(t<2)this.fromInt(1);else{this.fromNumber(t,n),this.testBit(t-1)||this.bitwiseTo(e.ONE.shiftLeft(t-1),Te,this),this.isEven()&&this.dAddOffset(1,0);var o=this,s=function(){o.dAddOffset(2,0),o.bitLength()>t&&o.subTo(e.ONE.shiftLeft(t-1),o),o.isProbablePrime(r)?setTimeout(function(){i()},0):setTimeout(s,0)};setTimeout(s,0)}else{var u=[],l=t&7;u.length=(t>>3)+1,r.nextBytes(u),l>0?u[0]&=(1<<l)-1:u[0]=0,this.fromString(u,256)}},e}(),ld=function(){function e(){}return e.prototype.convert=function(t){return t},e.prototype.revert=function(t){return t},e.prototype.mulTo=function(t,r,n){t.multiplyTo(r,n)},e.prototype.sqrTo=function(t,r){t.squareTo(r)},e}(),Si=function(){function e(t){this.m=t}return e.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},e.prototype.revert=function(t){return t},e.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},e.prototype.mulTo=function(t,r,n){t.multiplyTo(r,n),this.reduce(n)},e.prototype.sqrTo=function(t,r){t.squareTo(r),this.reduce(r)},e}(),xi=function(){function e(t){this.m=t,this.mp=t.invDigit(),this.mpl=this.mp&32767,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return e.prototype.convert=function(t){var r=J();return t.abs().dlShiftTo(this.m.t,r),r.divRemTo(this.m,null,r),t.s<0&&r.compareTo(W.ZERO)>0&&this.m.subTo(r,r),r},e.prototype.revert=function(t){var r=J();return t.copyTo(r),this.reduce(r),r},e.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var r=0;r<this.m.t;++r){var n=t[r]&32767,i=n*this.mpl+((n*this.mph+(t[r]>>15)*this.mpl&this.um)<<15)&t.DM;for(n=r+this.m.t,t[n]+=this.m.am(0,i,t,r,0,this.m.t);t[n]>=t.DV;)t[n]-=t.DV,t[++n]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},e.prototype.mulTo=function(t,r,n){t.multiplyTo(r,n),this.reduce(n)},e.prototype.sqrTo=function(t,r){t.squareTo(r),this.reduce(r)},e}(),fd=function(){function e(t){this.m=t,this.r2=J(),this.q3=J(),W.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return e.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var r=J();return t.copyTo(r),this.reduce(r),r},e.prototype.revert=function(t){return t},e.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},e.prototype.mulTo=function(t,r,n){t.multiplyTo(r,n),this.reduce(n)},e.prototype.sqrTo=function(t,r){t.squareTo(r),this.reduce(r)},e}();function J(){return new W(null)}function Z(e,t){return new W(e,t)}var Ti=typeof navigator<"u";Ti&&Ei&&navigator.appName=="Microsoft Internet Explorer"?(W.prototype.am=function(e,t,r,n,i,o){for(var s=t&32767,u=t>>15;--o>=0;){var l=this[e]&32767,a=this[e++]>>15,c=u*l+a*s;l=s*l+((c&32767)<<15)+r[n]+(i&1073741823),i=(l>>>30)+(c>>>15)+u*a+(i>>>30),r[n++]=l&1073741823}return i},Tt=30):Ti&&Ei&&navigator.appName!="Netscape"?(W.prototype.am=function(e,t,r,n,i,o){for(;--o>=0;){var s=t*this[e++]+r[n]+i;i=Math.floor(s/67108864),r[n++]=s&67108863}return i},Tt=26):(W.prototype.am=function(e,t,r,n,i,o){for(var s=t&16383,u=t>>14;--o>=0;){var l=this[e]&16383,a=this[e++]>>14,c=u*l+a*s;l=s*l+((c&16383)<<14)+r[n]+i,i=(l>>28)+(c>>14)+u*a,r[n++]=l&268435455}return i},Tt=28),W.prototype.DB=Tt,W.prototype.DM=(1<<Tt)-1,W.prototype.DV=1<<Tt;var dr=52;W.prototype.FV=Math.pow(2,dr),W.prototype.F1=dr-Tt,W.prototype.F2=2*Tt-dr;var Ge=[],ee,ft;for(ee=48,ft=0;ft<=9;++ft)Ge[ee++]=ft;for(ee=97,ft=10;ft<36;++ft)Ge[ee++]=ft;for(ee=65,ft=10;ft<36;++ft)Ge[ee++]=ft;function Ri(e,t){var r=Ge[e.charCodeAt(t)];return r!=null?r:-1}function Rt(e){var t=J();return t.fromInt(e),t}function Re(e){var t=1,r;return(r=e>>>16)!=0&&(e=r,t+=16),(r=e>>8)!=0&&(e=r,t+=8),(r=e>>4)!=0&&(e=r,t+=4),(r=e>>2)!=0&&(e=r,t+=2),(r=e>>1)!=0&&(e=r,t+=1),t}W.ZERO=Rt(0),W.ONE=Rt(1);var hd=function(){function e(){this.i=0,this.j=0,this.S=[]}return e.prototype.init=function(t){var r,n,i;for(r=0;r<256;++r)this.S[r]=r;for(n=0,r=0;r<256;++r)n=n+this.S[r]+t[r%t.length]&255,i=this.S[r],this.S[r]=this.S[n],this.S[n]=i;this.i=0,this.j=0},e.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},e}();function pd(){return new hd}var ds=256,Ae,Ot=null,ht;if(Ot==null){Ot=[],ht=0;var Oe=void 0;if(typeof window<"u"&&window.crypto&&window.crypto.getRandomValues){var mr=new Uint32Array(256);for(window.crypto.getRandomValues(mr),Oe=0;Oe<mr.length;++Oe)Ot[ht++]=mr[Oe]&255}var $e=0,je=function(e){if($e=$e||0,$e>=256||ht>=ds){window.removeEventListener?window.removeEventListener("mousemove",je,!1):window.detachEvent&&window.detachEvent("onmousemove",je);return}try{var t=e.x+e.y;Ot[ht++]=t&255,$e+=1}catch(r){}};typeof window<"u"&&(window.addEventListener?window.addEventListener("mousemove",je,!1):window.attachEvent&&window.attachEvent("onmousemove",je))}function dd(){if(Ae==null){for(Ae=pd();ht<ds;){var e=Math.floor(65536*Math.random());Ot[ht++]=e&255}for(Ae.init(Ot),ht=0;ht<Ot.length;++ht)Ot[ht]=0;ht=0}return Ae.next()}var Ur=function(){function e(){}return e.prototype.nextBytes=function(t){for(var r=0;r<t.length;++r)t[r]=dd()},e}();function md(e,t){if(t<e.length+22)return console.error("Message too long for RSA"),null;for(var r=t-e.length-6,n="",i=0;i<r;i+=2)n+="ff";var o="0001"+n+"00"+e;return Z(o,16)}function gd(e,t){if(t<e.length+11)return console.error("Message too long for RSA"),null;for(var r=[],n=e.length-1;n>=0&&t>0;){var i=e.charCodeAt(n--);i<128?r[--t]=i:i>127&&i<2048?(r[--t]=i&63|128,r[--t]=i>>6|192):(r[--t]=i&63|128,r[--t]=i>>6&63|128,r[--t]=i>>12|224)}r[--t]=0;for(var o=new Ur,s=[];t>2;){for(s[0]=0;s[0]==0;)o.nextBytes(s);r[--t]=s[0]}return r[--t]=2,r[--t]=0,new W(r)}var yd=function(){function e(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return e.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},e.prototype.doPrivate=function(t){if(this.p==null||this.q==null)return t.modPow(this.d,this.n);for(var r=t.mod(this.p).modPow(this.dmp1,this.p),n=t.mod(this.q).modPow(this.dmq1,this.q);r.compareTo(n)<0;)r=r.add(this.p);return r.subtract(n).multiply(this.coeff).mod(this.p).multiply(this.q).add(n)},e.prototype.setPublic=function(t,r){t!=null&&r!=null&&t.length>0&&r.length>0?(this.n=Z(t,16),this.e=parseInt(r,16)):console.error("Invalid RSA public key")},e.prototype.encrypt=function(t){var r=this.n.bitLength()+7>>3,n=gd(t,r);if(n==null)return null;var i=this.doPublic(n);if(i==null)return null;for(var o=i.toString(16),s=o.length,u=0;u<r*2-s;u++)o="0"+o;return o},e.prototype.setPrivate=function(t,r,n){t!=null&&r!=null&&t.length>0&&r.length>0?(this.n=Z(t,16),this.e=parseInt(r,16),this.d=Z(n,16)):console.error("Invalid RSA private key")},e.prototype.setPrivateEx=function(t,r,n,i,o,s,u,l){t!=null&&r!=null&&t.length>0&&r.length>0?(this.n=Z(t,16),this.e=parseInt(r,16),this.d=Z(n,16),this.p=Z(i,16),this.q=Z(o,16),this.dmp1=Z(s,16),this.dmq1=Z(u,16),this.coeff=Z(l,16)):console.error("Invalid RSA private key")},e.prototype.generate=function(t,r){var n=new Ur,i=t>>1;this.e=parseInt(r,16);for(var o=new W(r,16);;){for(;this.p=new W(t-i,1,n),!(this.p.subtract(W.ONE).gcd(o).compareTo(W.ONE)==0&&this.p.isProbablePrime(10)););for(;this.q=new W(i,1,n),!(this.q.subtract(W.ONE).gcd(o).compareTo(W.ONE)==0&&this.q.isProbablePrime(10)););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var u=this.p.subtract(W.ONE),l=this.q.subtract(W.ONE),a=u.multiply(l);if(a.gcd(o).compareTo(W.ONE)==0){this.n=this.p.multiply(this.q),this.d=o.modInverse(a),this.dmp1=this.d.mod(u),this.dmq1=this.d.mod(l),this.coeff=this.q.modInverse(this.p);break}}},e.prototype.decrypt=function(t){var r=Z(t,16),n=this.doPrivate(r);return n==null?null:vd(n,this.n.bitLength()+7>>3)},e.prototype.generateAsync=function(t,r,n){var i=new Ur,o=t>>1;this.e=parseInt(r,16);var s=new W(r,16),u=this,l=function(){var a=function(){if(u.p.compareTo(u.q)<=0){var p=u.p;u.p=u.q,u.q=p}var h=u.p.subtract(W.ONE),d=u.q.subtract(W.ONE),m=h.multiply(d);m.gcd(s).compareTo(W.ONE)==0?(u.n=u.p.multiply(u.q),u.d=s.modInverse(m),u.dmp1=u.d.mod(h),u.dmq1=u.d.mod(d),u.coeff=u.q.modInverse(u.p),setTimeout(function(){n()},0)):setTimeout(l,0)},c=function(){u.q=J(),u.q.fromNumberAsync(o,1,i,function(){u.q.subtract(W.ONE).gcda(s,function(p){p.compareTo(W.ONE)==0&&u.q.isProbablePrime(10)?setTimeout(a,0):setTimeout(c,0)})})},f=function(){u.p=J(),u.p.fromNumberAsync(t-o,1,i,function(){u.p.subtract(W.ONE).gcda(s,function(p){p.compareTo(W.ONE)==0&&u.p.isProbablePrime(10)?setTimeout(c,0):setTimeout(f,0)})})};setTimeout(f,0)};setTimeout(l,0)},e.prototype.sign=function(t,r,n){var i=bd(n),o=i+r(t).toString(),s=md(o,this.n.bitLength()/4);if(s==null)return null;var u=this.doPrivate(s);if(u==null)return null;var l=u.toString(16);return l.length&1?"0"+l:l},e.prototype.verify=function(t,r,n){var i=Z(r,16),o=this.doPublic(i);if(o==null)return null;var s=o.toString(16).replace(/^1f+00/,""),u=wd(s);return u==n(t).toString()},e}();function vd(e,t){for(var r=e.toByteArray(),n=0;n<r.length&&r[n]==0;)++n;if(r.length-n!=t-1||r[n]!=2)return null;for(++n;r[n]!=0;)if(++n>=r.length)return null;for(var i="";++n<r.length;){var o=r[n]&255;o<128?i+=String.fromCharCode(o):o>191&&o<224?(i+=String.fromCharCode((o&31)<<6|r[n+1]&63),++n):(i+=String.fromCharCode((o&15)<<12|(r[n+1]&63)<<6|r[n+2]&63),n+=2)}return i}var Ne={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};function bd(e){return Ne[e]||""}function wd(e){for(var t in Ne)if(Ne.hasOwnProperty(t)){var r=Ne[t],n=r.length;if(e.substr(0,n)==r)return e.substr(n)}return e}/*!
Copyright (c) 2011, Yahoo! Inc. All rights reserved.
Code licensed under the BSD License:
http://developer.yahoo.com/yui/license.html
version: 2.9.0
*/var X={};X.lang={extend:function(e,t,r){if(!t||!e)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var n=function(){};if(n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e,e.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),r){var i;for(i in r)e.prototype[i]=r[i];var o=function(){},s=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(o=function(u,l){for(i=0;i<s.length;i=i+1){var a=s[i],c=l[a];typeof c=="function"&&c!=Object.prototype[a]&&(u[a]=c)}})}catch(u){}o(e.prototype,r)}}};/**
* @fileOverview
* @name asn1-1.0.js
* <AUTHOR>
* @version asn1 1.0.13 (2017-Jun-02)
* @since jsrsasign 2.1
* @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
*/var $={};(typeof $.asn1>"u"||!$.asn1)&&($.asn1={}),$.asn1.ASN1Util=new function(){this.integerToByteHex=function(e){var t=e.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(e){var t=e.toString(16);if(t.substr(0,1)!="-")t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var r=t.substr(1),n=r.length;n%2==1?n+=1:t.match(/^[0-7]/)||(n+=2);for(var i="",o=0;o<n;o++)i+="f";var s=new W(i,16),u=s.xor(e).add(W.ONE);t=u.toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(e,t){return hextopem(e,t)},this.newObject=function(e){var t=$,r=t.asn1,n=r.DERBoolean,i=r.DERInteger,o=r.DERBitString,s=r.DEROctetString,u=r.DERNull,l=r.DERObjectIdentifier,a=r.DEREnumerated,c=r.DERUTF8String,f=r.DERNumericString,p=r.DERPrintableString,h=r.DERTeletexString,d=r.DERIA5String,m=r.DERUTCTime,b=r.DERGeneralizedTime,_=r.DERSequence,y=r.DERSet,v=r.DERTaggedObject,w=r.ASN1Util.newObject,S=Object.keys(e);if(S.length!=1)throw"key of param shall be only one.";var g=S[0];if(":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+g+":")==-1)throw"undefined key: "+g;if(g=="bool")return new n(e[g]);if(g=="int")return new i(e[g]);if(g=="bitstr")return new o(e[g]);if(g=="octstr")return new s(e[g]);if(g=="null")return new u(e[g]);if(g=="oid")return new l(e[g]);if(g=="enum")return new a(e[g]);if(g=="utf8str")return new c(e[g]);if(g=="numstr")return new f(e[g]);if(g=="prnstr")return new p(e[g]);if(g=="telstr")return new h(e[g]);if(g=="ia5str")return new d(e[g]);if(g=="utctime")return new m(e[g]);if(g=="gentime")return new b(e[g]);if(g=="seq"){for(var x=e[g],A=[],U=0;U<x.length;U++){var E=w(x[U]);A.push(E)}return new _({array:A})}if(g=="set"){for(var x=e[g],A=[],U=0;U<x.length;U++){var E=w(x[U]);A.push(E)}return new y({array:A})}if(g=="tag"){var T=e[g];if(Object.prototype.toString.call(T)==="[object Array]"&&T.length==3){var F=w(T[2]);return new v({tag:T[0],explicit:T[1],obj:F})}else{var B={};if(T.explicit!==void 0&&(B.explicit=T.explicit),T.tag!==void 0&&(B.tag=T.tag),T.obj===void 0)throw"obj shall be specified for 'tag'.";return B.obj=w(T.obj),new v(B)}}},this.jsonToASN1HEX=function(e){var t=this.newObject(e);return t.getEncodedHex()}},$.asn1.ASN1Util.oidHexToInt=function(e){for(var i="",t=parseInt(e.substr(0,2),16),r=Math.floor(t/40),n=t%40,i=r+"."+n,o="",s=2;s<e.length;s+=2){var u=parseInt(e.substr(s,2),16),l=("00000000"+u.toString(2)).slice(-8);if(o=o+l.substr(1,7),l.substr(0,1)=="0"){var a=new W(o,2);i=i+"."+a.toString(10),o=""}}return i},$.asn1.ASN1Util.oidIntToHex=function(e){var t=function(u){var l=u.toString(16);return l.length==1&&(l="0"+l),l},r=function(u){var l="",a=new W(u,10),c=a.toString(2),f=7-c.length%7;f==7&&(f=0);for(var p="",h=0;h<f;h++)p+="0";c=p+c;for(var h=0;h<c.length-1;h+=7){var d=c.substr(h,7);h!=c.length-7&&(d="1"+d),l+=t(parseInt(d,2))}return l};if(!e.match(/^[0-9.]+$/))throw"malformed oid string: "+e;var n="",i=e.split("."),o=parseInt(i[0])*40+parseInt(i[1]);n+=t(o),i.splice(0,2);for(var s=0;s<i.length;s++)n+=r(i[s]);return n},$.asn1.ASN1Object=function(){var e="";this.getLengthHexFromValue=function(){if(typeof this.hV>"u"||this.hV==null)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+e.length+",v="+this.hV;var t=this.hV.length/2,r=t.toString(16);if(r.length%2==1&&(r="0"+r),t<128)return r;var n=r.length/2;if(n>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);var i=128+n;return i.toString(16)+r},this.getEncodedHex=function(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},$.asn1.DERAbstractString=function(e){$.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e=="string"?this.setString(e):typeof e.str<"u"?this.setString(e.str):typeof e.hex<"u"&&this.setStringHex(e.hex))},X.lang.extend($.asn1.DERAbstractString,$.asn1.ASN1Object),$.asn1.DERAbstractTime=function(e){$.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){utc=t.getTime()+t.getTimezoneOffset()*6e4;var r=new Date(utc);return r},this.formatDate=function(t,r,n){var i=this.zeroPadding,o=this.localDateToUTC(t),s=String(o.getFullYear());r=="utc"&&(s=s.substr(2,2));var u=i(String(o.getMonth()+1),2),l=i(String(o.getDate()),2),a=i(String(o.getHours()),2),c=i(String(o.getMinutes()),2),f=i(String(o.getSeconds()),2),p=s+u+l+a+c+f;if(n===!0){var h=o.getMilliseconds();if(h!=0){var d=i(String(h),3);d=d.replace(/[0]+$/,""),p=p+"."+d}}return p+"Z"},this.zeroPadding=function(t,r){return t.length>=r?t:new Array(r-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,r,n,i,o,s){var u=new Date(Date.UTC(t,r-1,n,i,o,s,0));this.setByDate(u)},this.getFreshValueHex=function(){return this.hV}},X.lang.extend($.asn1.DERAbstractTime,$.asn1.ASN1Object),$.asn1.DERAbstractStructured=function(e){$.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,typeof e<"u"&&typeof e.array<"u"&&(this.asn1Array=e.array)},X.lang.extend($.asn1.DERAbstractStructured,$.asn1.ASN1Object),$.asn1.DERBoolean=function(){$.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},X.lang.extend($.asn1.DERBoolean,$.asn1.ASN1Object),$.asn1.DERInteger=function(e){$.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=$.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var r=new W(String(t),10);this.setByBigInteger(r)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e.bigint<"u"?this.setByBigInteger(e.bigint):typeof e.int<"u"?this.setByInteger(e.int):typeof e=="number"?this.setByInteger(e):typeof e.hex<"u"&&this.setValueHex(e.hex))},X.lang.extend($.asn1.DERInteger,$.asn1.ASN1Object),$.asn1.DERBitString=function(e){if(e!==void 0&&typeof e.obj<"u"){var t=$.asn1.ASN1Util.newObject(e.obj);e.hex="00"+t.getEncodedHex()}$.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(r){this.hTLV=null,this.isModified=!0,this.hV=r},this.setUnusedBitsAndHexValue=function(r,n){if(r<0||7<r)throw"unused bits shall be from 0 to 7: u = "+r;var i="0"+r;this.hTLV=null,this.isModified=!0,this.hV=i+n},this.setByBinaryString=function(r){r=r.replace(/0+$/,"");var n=8-r.length%8;n==8&&(n=0);for(var i=0;i<=n;i++)r+="0";for(var o="",i=0;i<r.length-1;i+=8){var s=r.substr(i,8),u=parseInt(s,2).toString(16);u.length==1&&(u="0"+u),o+=u}this.hTLV=null,this.isModified=!0,this.hV="0"+n+o},this.setByBooleanArray=function(r){for(var n="",i=0;i<r.length;i++)r[i]==!0?n+="1":n+="0";this.setByBinaryString(n)},this.newFalseArray=function(r){for(var n=new Array(r),i=0;i<r;i++)n[i]=!1;return n},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e=="string"&&e.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(e):typeof e.hex<"u"?this.setHexValueIncludingUnusedBits(e.hex):typeof e.bin<"u"?this.setByBinaryString(e.bin):typeof e.array<"u"&&this.setByBooleanArray(e.array))},X.lang.extend($.asn1.DERBitString,$.asn1.ASN1Object),$.asn1.DEROctetString=function(e){if(e!==void 0&&typeof e.obj<"u"){var t=$.asn1.ASN1Util.newObject(e.obj);e.hex=t.getEncodedHex()}$.asn1.DEROctetString.superclass.constructor.call(this,e),this.hT="04"},X.lang.extend($.asn1.DEROctetString,$.asn1.DERAbstractString),$.asn1.DERNull=function(){$.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},X.lang.extend($.asn1.DERNull,$.asn1.ASN1Object),$.asn1.DERObjectIdentifier=function(e){var t=function(n){var i=n.toString(16);return i.length==1&&(i="0"+i),i},r=function(n){var i="",o=new W(n,10),s=o.toString(2),u=7-s.length%7;u==7&&(u=0);for(var l="",a=0;a<u;a++)l+="0";s=l+s;for(var a=0;a<s.length-1;a+=7){var c=s.substr(a,7);a!=s.length-7&&(c="1"+c),i+=t(parseInt(c,2))}return i};$.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(n){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueOidString=function(n){if(!n.match(/^[0-9.]+$/))throw"malformed oid string: "+n;var i="",o=n.split("."),s=parseInt(o[0])*40+parseInt(o[1]);i+=t(s),o.splice(0,2);for(var u=0;u<o.length;u++)i+=r(o[u]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(n){var i=$.asn1.x509.OID.name2oid(n);if(i!=="")this.setValueOidString(i);else throw"DERObjectIdentifier oidName undefined: "+n},this.getFreshValueHex=function(){return this.hV},e!==void 0&&(typeof e=="string"?e.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(e):this.setValueName(e):e.oid!==void 0?this.setValueOidString(e.oid):e.hex!==void 0?this.setValueHex(e.hex):e.name!==void 0&&this.setValueName(e.name))},X.lang.extend($.asn1.DERObjectIdentifier,$.asn1.ASN1Object),$.asn1.DEREnumerated=function(e){$.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=$.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var r=new W(String(t),10);this.setByBigInteger(r)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e.int<"u"?this.setByInteger(e.int):typeof e=="number"?this.setByInteger(e):typeof e.hex<"u"&&this.setValueHex(e.hex))},X.lang.extend($.asn1.DEREnumerated,$.asn1.ASN1Object),$.asn1.DERUTF8String=function(e){$.asn1.DERUTF8String.superclass.constructor.call(this,e),this.hT="0c"},X.lang.extend($.asn1.DERUTF8String,$.asn1.DERAbstractString),$.asn1.DERNumericString=function(e){$.asn1.DERNumericString.superclass.constructor.call(this,e),this.hT="12"},X.lang.extend($.asn1.DERNumericString,$.asn1.DERAbstractString),$.asn1.DERPrintableString=function(e){$.asn1.DERPrintableString.superclass.constructor.call(this,e),this.hT="13"},X.lang.extend($.asn1.DERPrintableString,$.asn1.DERAbstractString),$.asn1.DERTeletexString=function(e){$.asn1.DERTeletexString.superclass.constructor.call(this,e),this.hT="14"},X.lang.extend($.asn1.DERTeletexString,$.asn1.DERAbstractString),$.asn1.DERIA5String=function(e){$.asn1.DERIA5String.superclass.constructor.call(this,e),this.hT="16"},X.lang.extend($.asn1.DERIA5String,$.asn1.DERAbstractString),$.asn1.DERUTCTime=function(e){$.asn1.DERUTCTime.superclass.constructor.call(this,e),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return typeof this.date>"u"&&typeof this.s>"u"&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},e!==void 0&&(e.str!==void 0?this.setString(e.str):typeof e=="string"&&e.match(/^[0-9]{12}Z$/)?this.setString(e):e.hex!==void 0?this.setStringHex(e.hex):e.date!==void 0&&this.setByDate(e.date))},X.lang.extend($.asn1.DERUTCTime,$.asn1.DERAbstractTime),$.asn1.DERGeneralizedTime=function(e){$.asn1.DERGeneralizedTime.superclass.constructor.call(this,e),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},e!==void 0&&(e.str!==void 0?this.setString(e.str):typeof e=="string"&&e.match(/^[0-9]{14}Z$/)?this.setString(e):e.hex!==void 0?this.setStringHex(e.hex):e.date!==void 0&&this.setByDate(e.date),e.millis===!0&&(this.withMillis=!0))},X.lang.extend($.asn1.DERGeneralizedTime,$.asn1.DERAbstractTime),$.asn1.DERSequence=function(e){$.asn1.DERSequence.superclass.constructor.call(this,e),this.hT="30",this.getFreshValueHex=function(){for(var t="",r=0;r<this.asn1Array.length;r++){var n=this.asn1Array[r];t+=n.getEncodedHex()}return this.hV=t,this.hV}},X.lang.extend($.asn1.DERSequence,$.asn1.DERAbstractStructured),$.asn1.DERSet=function(e){$.asn1.DERSet.superclass.constructor.call(this,e),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,r=0;r<this.asn1Array.length;r++){var n=this.asn1Array[r];t.push(n.getEncodedHex())}return this.sortFlag==!0&&t.sort(),this.hV=t.join(""),this.hV},typeof e<"u"&&typeof e.sortflag<"u"&&e.sortflag==!1&&(this.sortFlag=!1)},X.lang.extend($.asn1.DERSet,$.asn1.DERAbstractStructured),$.asn1.DERTaggedObject=function(e){$.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,r,n){this.hT=r,this.isExplicit=t,this.asn1Object=n,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=n.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,r),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},typeof e<"u"&&(typeof e.tag<"u"&&(this.hT=e.tag),typeof e.explicit<"u"&&(this.isExplicit=e.explicit),typeof e.obj<"u"&&(this.asn1Object=e.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},X.lang.extend($.asn1.DERTaggedObject,$.asn1.ASN1Object);var _d=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,i){n.__proto__=i}||function(n,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(n[o]=i[o])},e(t,r)};return function(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),Ai=function(e){_d(t,e);function t(r){var n=e.call(this)||this;return r&&(typeof r=="string"?n.parseKey(r):(t.hasPrivateKeyProperty(r)||t.hasPublicKeyProperty(r))&&n.parsePropertiesFrom(r)),n}return t.prototype.parseKey=function(r){try{var n=0,i=0,o=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/,s=o.test(r)?id.decode(r):Mr.unarmor(r),u=ad.decode(s);if(u.sub.length===3&&(u=u.sub[2].sub[0]),u.sub.length===9){n=u.sub[1].getHexStringValue(),this.n=Z(n,16),i=u.sub[2].getHexStringValue(),this.e=parseInt(i,16);var l=u.sub[3].getHexStringValue();this.d=Z(l,16);var a=u.sub[4].getHexStringValue();this.p=Z(a,16);var c=u.sub[5].getHexStringValue();this.q=Z(c,16);var f=u.sub[6].getHexStringValue();this.dmp1=Z(f,16);var p=u.sub[7].getHexStringValue();this.dmq1=Z(p,16);var h=u.sub[8].getHexStringValue();this.coeff=Z(h,16)}else if(u.sub.length===2)if(u.sub[0].sub){var d=u.sub[1],m=d.sub[0];n=m.sub[0].getHexStringValue(),this.n=Z(n,16),i=m.sub[1].getHexStringValue(),this.e=parseInt(i,16)}else n=u.sub[0].getHexStringValue(),this.n=Z(n,16),i=u.sub[1].getHexStringValue(),this.e=parseInt(i,16);else return!1;return!0}catch(b){return!1}},t.prototype.getPrivateBaseKey=function(){var r={array:[new $.asn1.DERInteger({int:0}),new $.asn1.DERInteger({bigint:this.n}),new $.asn1.DERInteger({int:this.e}),new $.asn1.DERInteger({bigint:this.d}),new $.asn1.DERInteger({bigint:this.p}),new $.asn1.DERInteger({bigint:this.q}),new $.asn1.DERInteger({bigint:this.dmp1}),new $.asn1.DERInteger({bigint:this.dmq1}),new $.asn1.DERInteger({bigint:this.coeff})]},n=new $.asn1.DERSequence(r);return n.getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return Ve(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var r=new $.asn1.DERSequence({array:[new $.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new $.asn1.DERNull]}),n=new $.asn1.DERSequence({array:[new $.asn1.DERInteger({bigint:this.n}),new $.asn1.DERInteger({int:this.e})]}),i=new $.asn1.DERBitString({hex:"00"+n.getEncodedHex()}),o=new $.asn1.DERSequence({array:[r,i]});return o.getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return Ve(this.getPublicBaseKey())},t.wordwrap=function(r,n){if(n=n||64,!r)return r;var i="(.{1,"+n+`})( +|$
?)|(.{1,`+n+"})";return r.match(RegExp(i,"g")).join(`
`)},t.prototype.getPrivateKey=function(){var r=`-----BEGIN RSA PRIVATE KEY-----
`;return r+=t.wordwrap(this.getPrivateBaseKeyB64())+`
`,r+="-----END RSA PRIVATE KEY-----",r},t.prototype.getPublicKey=function(){var r=`-----BEGIN PUBLIC KEY-----
`;return r+=t.wordwrap(this.getPublicBaseKeyB64())+`
`,r+="-----END PUBLIC KEY-----",r},t.hasPublicKeyProperty=function(r){return r=r||{},r.hasOwnProperty("n")&&r.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(r){return r=r||{},r.hasOwnProperty("n")&&r.hasOwnProperty("e")&&r.hasOwnProperty("d")&&r.hasOwnProperty("p")&&r.hasOwnProperty("q")&&r.hasOwnProperty("dmp1")&&r.hasOwnProperty("dmq1")&&r.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(r){this.n=r.n,this.e=r.e,r.hasOwnProperty("d")&&(this.d=r.d,this.p=r.p,this.q=r.q,this.dmp1=r.dmp1,this.dmq1=r.dmq1,this.coeff=r.coeff)},t}(yd),gr,Ed=typeof process<"u"?(gr=_a)===null||gr===void 0?void 0:gr.npm_package_version:void 0,Sd=function(){function e(t){t===void 0&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return e.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Ai(t)},e.prototype.setPrivateKey=function(t){this.setKey(t)},e.prototype.setPublicKey=function(t){this.setKey(t)},e.prototype.decrypt=function(t){try{return this.getKey().decrypt(wi(t))}catch(r){return!1}},e.prototype.encrypt=function(t){try{return Ve(this.getKey().encrypt(t))}catch(r){return!1}},e.prototype.sign=function(t,r,n){try{return Ve(this.getKey().sign(t,r,n))}catch(i){return!1}},e.prototype.verify=function(t,r,n){try{return this.getKey().verify(t,wi(r),n)}catch(i){return!1}},e.prototype.getKey=function(t){if(!this.key){if(this.key=new Ai,t&&{}.toString.call(t)==="[object Function]"){this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);return}this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},e.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},e.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},e.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},e.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},e.version=Ed,e}();const xd="MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAt3LFMIQHlZwadt5TI3jGfVyzzP7S0Lva+Bt38v32iblNnQ59GpFvpjtEl92KYH0snK+bI/1PU/SNaseSi2ryJQIDAQABAkBGHyXG7MDlbD0lcMhAx9q/cp773fABf70sl3tbM754V+mH8dEiAim398NhhrADz4F4K12H74R39O/0Hjr/VAZlAiEA5+3MoAbnsDOzFP+oT4YZJBjx8MmsukU3AaguPW4tYcMCIQDKfN+Qk/jD3vZlWsUJ23013D2NDSjgcH4gdV71oYb19wIgUMWYhgLhnZPjwmRnEYr6JoApglo6NYT1azZPJEXCuFECIDyQUQYbXCKpw6TZG2oxXigH8dkIgJtwyijHMlnhsE5NAiBxF7HA7U2rDnZ6+VRDXtIg+joD3o1h3bFy2A+QX5yDYA==";function Oi(e,t=xd){const r=new Sd;return r.setPrivateKey(t),r.decrypt(e)}const ms=/^(http|https):\/\/[\w.:\-@]*/;function gs(e){return ms.test(e)}var ys={exports:{}};/*! @preserve
* numeral.js
* version : 2.0.6
* author : Adam Draper
* license : MIT
* http://adamwdraper.github.com/Numeral-js/
*/(function(e){(function(t,r){e.exports?e.exports=r():t.numeral=r()})(it,function(){var t,r,n="2.0.6",i={},o={},s={currentLocale:"en",zeroFormat:null,nullFormat:null,defaultFormat:"0,0",scalePercentBy100:!0},u={currentLocale:s.currentLocale,zeroFormat:s.zeroFormat,nullFormat:s.nullFormat,defaultFormat:s.defaultFormat,scalePercentBy100:s.scalePercentBy100};function l(a,c){this._input=a,this._value=c}return t=function(a){var c,f,p,h;if(t.isNumeral(a))c=a.value();else if(a===0||typeof a>"u")c=0;else if(a===null||r.isNaN(a))c=null;else if(typeof a=="string")if(u.zeroFormat&&a===u.zeroFormat)c=0;else if(u.nullFormat&&a===u.nullFormat||!a.replace(/[^0-9]+/g,"").length)c=null;else{for(f in i)if(h=typeof i[f].regexps.unformat=="function"?i[f].regexps.unformat():i[f].regexps.unformat,h&&a.match(h)){p=i[f].unformat;break}p=p||t._.stringToNumber,c=p(a)}else c=Number(a)||null;return new l(a,c)},t.version=n,t.isNumeral=function(a){return a instanceof l},t._=r={numberToFormat:function(a,c,f){var p=o[t.options.currentLocale],h=!1,d=!1,m=0,b="",_=1e12,y=1e9,v=1e6,w=1e3,S="",g=!1,x,A,U,E,T,F,B;if(a=a||0,A=Math.abs(a),t._.includes(c,"(")?(h=!0,c=c.replace(/[\(|\)]/g,"")):(t._.includes(c,"+")||t._.includes(c,"-"))&&(T=t._.includes(c,"+")?c.indexOf("+"):a<0?c.indexOf("-"):-1,c=c.replace(/[\+|\-]/g,"")),t._.includes(c,"a")&&(x=c.match(/a(k|m|b|t)?/),x=x?x[1]:!1,t._.includes(c," a")&&(b=" "),c=c.replace(new RegExp(b+"a[kmbt]?"),""),A>=_&&!x||x==="t"?(b+=p.abbreviations.trillion,a=a/_):A<_&&A>=y&&!x||x==="b"?(b+=p.abbreviations.billion,a=a/y):A<y&&A>=v&&!x||x==="m"?(b+=p.abbreviations.million,a=a/v):(A<v&&A>=w&&!x||x==="k")&&(b+=p.abbreviations.thousand,a=a/w)),t._.includes(c,"[.]")&&(d=!0,c=c.replace("[.]",".")),U=a.toString().split(".")[0],E=c.split(".")[1],F=c.indexOf(","),m=(c.split(".")[0].split(",")[0].match(/0/g)||[]).length,E?(t._.includes(E,"[")?(E=E.replace("]",""),E=E.split("["),S=t._.toFixed(a,E[0].length+E[1].length,f,E[1].length)):S=t._.toFixed(a,E.length,f),U=S.split(".")[0],t._.includes(S,".")?S=p.delimiters.decimal+S.split(".")[1]:S="",d&&Number(S.slice(1))===0&&(S="")):U=t._.toFixed(a,0,f),b&&!x&&Number(U)>=1e3&&b!==p.abbreviations.trillion)switch(U=String(Number(U)/1e3),b){case p.abbreviations.thousand:b=p.abbreviations.million;break;case p.abbreviations.million:b=p.abbreviations.billion;break;case p.abbreviations.billion:b=p.abbreviations.trillion;break}if(t._.includes(U,"-")&&(U=U.slice(1),g=!0),U.length<m)for(var P=m-U.length;P>0;P--)U="0"+U;return F>-1&&(U=U.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+p.delimiters.thousands)),c.indexOf(".")===0&&(U=""),B=U+S+(b||""),h?B=(h&&g?"(":"")+B+(h&&g?")":""):T>=0?B=T===0?(g?"-":"+")+B:B+(g?"-":"+"):g&&(B="-"+B),B},stringToNumber:function(a){var c=o[u.currentLocale],f=a,p={thousand:3,million:6,billion:9,trillion:12},h,d,m;if(u.zeroFormat&&a===u.zeroFormat)d=0;else if(u.nullFormat&&a===u.nullFormat||!a.replace(/[^0-9]+/g,"").length)d=null;else{d=1,c.delimiters.decimal!=="."&&(a=a.replace(/\./g,"").replace(c.delimiters.decimal,"."));for(h in p)if(m=new RegExp("[^a-zA-Z]"+c.abbreviations[h]+"(?:\\)|(\\"+c.currency.symbol+")?(?:\\))?)?$"),f.match(m)){d*=Math.pow(10,p[h]);break}d*=(a.split("-").length+Math.min(a.split("(").length-1,a.split(")").length-1))%2?1:-1,a=a.replace(/[^0-9\.]+/g,""),d*=Number(a)}return d},isNaN:function(a){return typeof a=="number"&&isNaN(a)},includes:function(a,c){return a.indexOf(c)!==-1},insert:function(a,c,f){return a.slice(0,f)+c+a.slice(f)},reduce:function(a,c){if(this===null)throw new TypeError("Array.prototype.reduce called on null or undefined");if(typeof c!="function")throw new TypeError(c+" is not a function");var f=Object(a),p=f.length>>>0,h=0,d;if(arguments.length===3)d=arguments[2];else{for(;h<p&&!(h in f);)h++;if(h>=p)throw new TypeError("Reduce of empty array with no initial value");d=f[h++]}for(;h<p;h++)h in f&&(d=c(d,f[h],h,f));return d},multiplier:function(a){var c=a.toString().split(".");return c.length<2?1:Math.pow(10,c[1].length)},correctionFactor:function(){var a=Array.prototype.slice.call(arguments);return a.reduce(function(c,f){var p=r.multiplier(f);return c>p?c:p},1)},toFixed:function(a,c,f,p){var h=a.toString().split("."),d=c-(p||0),m,b,_,y;return h.length===2?m=Math.min(Math.max(h[1].length,d),c):m=d,_=Math.pow(10,m),y=(f(a+"e+"+m)/_).toFixed(m),p>c-m&&(b=new RegExp("\\.?0{1,"+(p-(c-m))+"}$"),y=y.replace(b,"")),y}},t.options=u,t.formats=i,t.locales=o,t.locale=function(a){return a&&(u.currentLocale=a.toLowerCase()),u.currentLocale},t.localeData=function(a){if(!a)return o[u.currentLocale];if(a=a.toLowerCase(),!o[a])throw new Error("Unknown locale : "+a);return o[a]},t.reset=function(){for(var a in s)u[a]=s[a]},t.zeroFormat=function(a){u.zeroFormat=typeof a=="string"?a:null},t.nullFormat=function(a){u.nullFormat=typeof a=="string"?a:null},t.defaultFormat=function(a){u.defaultFormat=typeof a=="string"?a:"0.0"},t.register=function(a,c,f){if(c=c.toLowerCase(),this[a+"s"][c])throw new TypeError(c+" "+a+" already registered.");return this[a+"s"][c]=f,f},t.validate=function(a,c){var f,p,h,d,m,b,_,y;if(typeof a!="string"&&(a+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",a)),a=a.trim(),a.match(/^\d+$/))return!0;if(a==="")return!1;try{_=t.localeData(c)}catch(v){_=t.localeData(t.locale())}return h=_.currency.symbol,m=_.abbreviations,f=_.delimiters.decimal,_.delimiters.thousands==="."?p="\\.":p=_.delimiters.thousands,y=a.match(/^[^\d]+/),y!==null&&(a=a.substr(1),y[0]!==h)||(y=a.match(/[^\d]+$/),y!==null&&(a=a.slice(0,-1),y[0]!==m.thousand&&y[0]!==m.million&&y[0]!==m.billion&&y[0]!==m.trillion))?!1:(b=new RegExp(p+"{2}"),a.match(/[^\d.,]/g)?!1:(d=a.split(f),d.length>2?!1:d.length<2?!!d[0].match(/^\d+.*\d$/)&&!d[0].match(b):d[0].length===1?!!d[0].match(/^\d+$/)&&!d[0].match(b)&&!!d[1].match(/^\d+$/):!!d[0].match(/^\d+.*\d$/)&&!d[0].match(b)&&!!d[1].match(/^\d+$/)))},t.fn=l.prototype={clone:function(){return t(this)},format:function(a,c){var f=this._value,p=a||u.defaultFormat,h,d,m;if(c=c||Math.round,f===0&&u.zeroFormat!==null)d=u.zeroFormat;else if(f===null&&u.nullFormat!==null)d=u.nullFormat;else{for(h in i)if(p.match(i[h].regexps.format)){m=i[h].format;break}m=m||t._.numberToFormat,d=m(f,p,c)}return d},value:function(){return this._value},input:function(){return this._input},set:function(a){return this._value=Number(a),this},add:function(a){var c=r.correctionFactor.call(null,this._value,a);function f(p,h,d,m){return p+Math.round(c*h)}return this._value=r.reduce([this._value,a],f,0)/c,this},subtract:function(a){var c=r.correctionFactor.call(null,this._value,a);function f(p,h,d,m){return p-Math.round(c*h)}return this._value=r.reduce([a],f,Math.round(this._value*c))/c,this},multiply:function(a){function c(f,p,h,d){var m=r.correctionFactor(f,p);return Math.round(f*m)*Math.round(p*m)/Math.round(m*m)}return this._value=r.reduce([this._value,a],c,1),this},divide:function(a){function c(f,p,h,d){var m=r.correctionFactor(f,p);return Math.round(f*m)/Math.round(p*m)}return this._value=r.reduce([this._value,a],c),this},difference:function(a){return Math.abs(t(this._value).subtract(a).value())}},t.register("locale","en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(a){var c=a%10;return~~(a%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"$"}}),function(){t.register("format","bps",{regexps:{format:/(BPS)/,unformat:/(BPS)/},format:function(a,c,f){var p=t._.includes(c," BPS")?" ":"",h;return a=a*1e4,c=c.replace(/\s?BPS/,""),h=t._.numberToFormat(a,c,f),t._.includes(h,")")?(h=h.split(""),h.splice(-1,0,p+"BPS"),h=h.join("")):h=h+p+"BPS",h},unformat:function(a){return+(t._.stringToNumber(a)*1e-4).toFixed(15)}})}(),function(){var a={base:1e3,suffixes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]},c={base:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},f=a.suffixes.concat(c.suffixes.filter(function(h){return a.suffixes.indexOf(h)<0})),p=f.join("|");p="("+p.replace("B","B(?!PS)")+")",t.register("format","bytes",{regexps:{format:/([0\s]i?b)/,unformat:new RegExp(p)},format:function(h,d,m){var b,_=t._.includes(d,"ib")?c:a,y=t._.includes(d," b")||t._.includes(d," ib")?" ":"",v,w,S;for(d=d.replace(/\s?i?b/,""),v=0;v<=_.suffixes.length;v++)if(w=Math.pow(_.base,v),S=Math.pow(_.base,v+1),h===null||h===0||h>=w&&h<S){y+=_.suffixes[v],w>0&&(h=h/w);break}return b=t._.numberToFormat(h,d,m),b+y},unformat:function(h){var d=t._.stringToNumber(h),m,b;if(d){for(m=a.suffixes.length-1;m>=0;m--){if(t._.includes(h,a.suffixes[m])){b=Math.pow(a.base,m);break}if(t._.includes(h,c.suffixes[m])){b=Math.pow(c.base,m);break}}d*=b||1}return d}})}(),function(){t.register("format","currency",{regexps:{format:/(\$)/},format:function(a,c,f){var p=t.locales[t.options.currentLocale],h={before:c.match(/^([\+|\-|\(|\s|\$]*)/)[0],after:c.match(/([\+|\-|\)|\s|\$]*)$/)[0]},d,m,b;for(c=c.replace(/\s?\$\s?/,""),d=t._.numberToFormat(a,c,f),a>=0?(h.before=h.before.replace(/[\-\(]/,""),h.after=h.after.replace(/[\-\)]/,"")):a<0&&!t._.includes(h.before,"-")&&!t._.includes(h.before,"(")&&(h.before="-"+h.before),b=0;b<h.before.length;b++)switch(m=h.before[b],m){case"$":d=t._.insert(d,p.currency.symbol,b);break;case" ":d=t._.insert(d," ",b+p.currency.symbol.length-1);break}for(b=h.after.length-1;b>=0;b--)switch(m=h.after[b],m){case"$":d=b===h.after.length-1?d+p.currency.symbol:t._.insert(d,p.currency.symbol,-(h.after.length-(1+b)));break;case" ":d=b===h.after.length-1?d+" ":t._.insert(d," ",-(h.after.length-(1+b)+p.currency.symbol.length-1));break}return d}})}(),function(){t.register("format","exponential",{regexps:{format:/(e\+|e-)/,unformat:/(e\+|e-)/},format:function(a,c,f){var p,h=typeof a=="number"&&!t._.isNaN(a)?a.toExponential():"0e+0",d=h.split("e");return c=c.replace(/e[\+|\-]{1}0/,""),p=t._.numberToFormat(Number(d[0]),c,f),p+"e"+d[1]},unformat:function(a){var c=t._.includes(a,"e+")?a.split("e+"):a.split("e-"),f=Number(c[0]),p=Number(c[1]);p=t._.includes(a,"e-")?p*=-1:p;function h(d,m,b,_){var y=t._.correctionFactor(d,m),v=d*y*(m*y)/(y*y);return v}return t._.reduce([f,Math.pow(10,p)],h,1)}})}(),function(){t.register("format","ordinal",{regexps:{format:/(o)/},format:function(a,c,f){var p=t.locales[t.options.currentLocale],h,d=t._.includes(c," o")?" ":"";return c=c.replace(/\s?o/,""),d+=p.ordinal(a),h=t._.numberToFormat(a,c,f),h+d}})}(),function(){t.register("format","percentage",{regexps:{format:/(%)/,unformat:/(%)/},format:function(a,c,f){var p=t._.includes(c," %")?" ":"",h;return t.options.scalePercentBy100&&(a=a*100),c=c.replace(/\s?\%/,""),h=t._.numberToFormat(a,c,f),t._.includes(h,")")?(h=h.split(""),h.splice(-1,0,p+"%"),h=h.join("")):h=h+p+"%",h},unformat:function(a){var c=t._.stringToNumber(a);return t.options.scalePercentBy100?c*.01:c}})}(),function(){t.register("format","time",{regexps:{format:/(:)/,unformat:/(:)/},format:function(a,c,f){var p=Math.floor(a/60/60),h=Math.floor((a-p*60*60)/60),d=Math.round(a-p*60*60-h*60);return p+":"+(h<10?"0"+h:h)+":"+(d<10?"0"+d:d)},unformat:function(a){var c=a.split(":"),f=0;return c.length===3?(f=f+Number(c[0])*60*60,f=f+Number(c[1])*60,f=f+Number(c[2])):c.length===2&&(f=f+Number(c[0])*60,f=f+Number(c[1])),Number(f)}})}(),t})})(ys);var Td=ys.exports;ie(Td);var $i={exports:{}},ji;function vs(){return ji||(ji=1,function(e,t){(function(r,n){e.exports=n()})(it,function(){var r=1e3,n=6e4,i=36e5,o="millisecond",s="second",u="minute",l="hour",a="day",c="week",f="month",p="quarter",h="year",d="date",m="Invalid Date",b=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,_=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,y={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(P){var j=["th","st","nd","rd"],N=P%100;return"["+P+(j[(N-20)%10]||j[N]||j[0])+"]"}},v=function(P,j,N){var L=String(P);return!L||L.length>=j?P:""+Array(j+1-L.length).join(N)+P},w={s:v,z:function(P){var j=-P.utcOffset(),N=Math.abs(j),L=Math.floor(N/60),M=N%60;return(j<=0?"+":"-")+v(L,2,"0")+":"+v(M,2,"0")},m:function P(j,N){if(j.date()<N.date())return-P(N,j);var L=12*(N.year()-j.year())+(N.month()-j.month()),M=j.clone().add(L,f),H=N-M<0,q=j.clone().add(L+(H?-1:1),f);return+(-(L+(N-M)/(H?M-q:q-M))||0)},a:function(P){return P<0?Math.ceil(P)||0:Math.floor(P)},p:function(P){return{M:f,y:h,w:c,d:a,D:d,h:l,m:u,s,ms:o,Q:p}[P]||String(P||"").toLowerCase().replace(/s$/,"")},u:function(P){return P===void 0}},S="en",g={};g[S]=y;var x="$isDayjsObject",A=function(P){return P instanceof F||!(!P||!P[x])},U=function P(j,N,L){var M;if(!j)return S;if(typeof j=="string"){var H=j.toLowerCase();g[H]&&(M=H),N&&(g[H]=N,M=H);var q=j.split("-");if(!M&&q.length>1)return P(q[0])}else{var O=j.name;g[O]=j,M=O}return!L&&M&&(S=M),M||!L&&S},E=function(P,j){if(A(P))return P.clone();var N=typeof j=="object"?j:{};return N.date=P,N.args=arguments,new F(N)},T=w;T.l=U,T.i=A,T.w=function(P,j){return E(P,{locale:j.$L,utc:j.$u,x:j.$x,$offset:j.$offset})};var F=function(){function P(N){this.$L=U(N.locale,null,!0),this.parse(N),this.$x=this.$x||N.x||{},this[x]=!0}var j=P.prototype;return j.parse=function(N){this.$d=function(L){var M=L.date,H=L.utc;if(M===null)return new Date(NaN);if(T.u(M))return new Date;if(M instanceof Date)return new Date(M);if(typeof M=="string"&&!/Z$/i.test(M)){var q=M.match(b);if(q){var O=q[2]-1||0,D=(q[7]||"0").substring(0,3);return H?new Date(Date.UTC(q[1],O,q[3]||1,q[4]||0,q[5]||0,q[6]||0,D)):new Date(q[1],O,q[3]||1,q[4]||0,q[5]||0,q[6]||0,D)}}return new Date(M)}(N),this.init()},j.init=function(){var N=this.$d;this.$y=N.getFullYear(),this.$M=N.getMonth(),this.$D=N.getDate(),this.$W=N.getDay(),this.$H=N.getHours(),this.$m=N.getMinutes(),this.$s=N.getSeconds(),this.$ms=N.getMilliseconds()},j.$utils=function(){return T},j.isValid=function(){return this.$d.toString()!==m},j.isSame=function(N,L){var M=E(N);return this.startOf(L)<=M&&M<=this.endOf(L)},j.isAfter=function(N,L){return E(N)<this.startOf(L)},j.isBefore=function(N,L){return this.endOf(L)<E(N)},j.$g=function(N,L,M){return T.u(N)?this[L]:this.set(M,N)},j.unix=function(){return Math.floor(this.valueOf()/1e3)},j.valueOf=function(){return this.$d.getTime()},j.startOf=function(N,L){var M=this,H=!!T.u(L)||L,q=T.p(N),O=function(Bt,ot){var xt=T.w(M.$u?Date.UTC(M.$y,ot,Bt):new Date(M.$y,ot,Bt),M);return H?xt:xt.endOf(a)},D=function(Bt,ot){return T.w(M.toDate()[Bt].apply(M.toDate("s"),(H?[0,0,0,0]:[23,59,59,999]).slice(ot)),M)},C=this.$W,I=this.$M,ut=this.$D,zt="set"+(this.$u?"UTC":"");switch(q){case h:return H?O(1,0):O(31,11);case f:return H?O(1,I):O(0,I+1);case c:var Dt=this.$locale().weekStart||0,ae=(C<Dt?C+7:C)-Dt;return O(H?ut-ae:ut+(6-ae),I);case a:case d:return D(zt+"Hours",0);case l:return D(zt+"Minutes",1);case u:return D(zt+"Seconds",2);case s:return D(zt+"Milliseconds",3);default:return this.clone()}},j.endOf=function(N){return this.startOf(N,!1)},j.$set=function(N,L){var M,H=T.p(N),q="set"+(this.$u?"UTC":""),O=(M={},M[a]=q+"Date",M[d]=q+"Date",M[f]=q+"Month",M[h]=q+"FullYear",M[l]=q+"Hours",M[u]=q+"Minutes",M[s]=q+"Seconds",M[o]=q+"Milliseconds",M)[H],D=H===a?this.$D+(L-this.$W):L;if(H===f||H===h){var C=this.clone().set(d,1);C.$d[O](D),C.init(),this.$d=C.set(d,Math.min(this.$D,C.daysInMonth())).$d}else O&&this.$d[O](D);return this.init(),this},j.set=function(N,L){return this.clone().$set(N,L)},j.get=function(N){return this[T.p(N)]()},j.add=function(N,L){var M,H=this;N=Number(N);var q=T.p(L),O=function(I){var ut=E(H);return T.w(ut.date(ut.date()+Math.round(I*N)),H)};if(q===f)return this.set(f,this.$M+N);if(q===h)return this.set(h,this.$y+N);if(q===a)return O(1);if(q===c)return O(7);var D=(M={},M[u]=n,M[l]=i,M[s]=r,M)[q]||1,C=this.$d.getTime()+N*D;return T.w(C,this)},j.subtract=function(N,L){return this.add(-1*N,L)},j.format=function(N){var L=this,M=this.$locale();if(!this.isValid())return M.invalidDate||m;var H=N||"YYYY-MM-DDTHH:mm:ssZ",q=T.z(this),O=this.$H,D=this.$m,C=this.$M,I=M.weekdays,ut=M.months,zt=M.meridiem,Dt=function(ot,xt,ue,Se){return ot&&(ot[xt]||ot(L,H))||ue[xt].slice(0,Se)},ae=function(ot){return T.s(O%12||12,ot,"0")},Bt=zt||function(ot,xt,ue){var Se=ot<12?"AM":"PM";return ue?Se.toLowerCase():Se};return H.replace(_,function(ot,xt){return xt||function(ue){switch(ue){case"YY":return String(L.$y).slice(-2);case"YYYY":return T.s(L.$y,4,"0");case"M":return C+1;case"MM":return T.s(C+1,2,"0");case"MMM":return Dt(M.monthsShort,C,ut,3);case"MMMM":return Dt(ut,C);case"D":return L.$D;case"DD":return T.s(L.$D,2,"0");case"d":return String(L.$W);case"dd":return Dt(M.weekdaysMin,L.$W,I,2);case"ddd":return Dt(M.weekdaysShort,L.$W,I,3);case"dddd":return I[L.$W];case"H":return String(O);case"HH":return T.s(O,2,"0");case"h":return ae(1);case"hh":return ae(2);case"a":return Bt(O,D,!0);case"A":return Bt(O,D,!1);case"m":return String(D);case"mm":return T.s(D,2,"0");case"s":return String(L.$s);case"ss":return T.s(L.$s,2,"0");case"SSS":return T.s(L.$ms,3,"0");case"Z":return q}return null}(ot)||q.replace(":","")})},j.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},j.diff=function(N,L,M){var H,q=this,O=T.p(L),D=E(N),C=(D.utcOffset()-this.utcOffset())*n,I=this-D,ut=function(){return T.m(q,D)};switch(O){case h:H=ut()/12;break;case f:H=ut();break;case p:H=ut()/3;break;case c:H=(I-C)/6048e5;break;case a:H=(I-C)/864e5;break;case l:H=I/i;break;case u:H=I/n;break;case s:H=I/r;break;default:H=I}return M?H:T.a(H)},j.daysInMonth=function(){return this.endOf(f).$D},j.$locale=function(){return g[this.$L]},j.locale=function(N,L){if(!N)return this.$L;var M=this.clone(),H=U(N,L,!0);return H&&(M.$L=H),M},j.clone=function(){return T.w(this.$d,this)},j.toDate=function(){return new Date(this.valueOf())},j.toJSON=function(){return this.isValid()?this.toISOString():null},j.toISOString=function(){return this.$d.toISOString()},j.toString=function(){return this.$d.toUTCString()},P}(),B=F.prototype;return E.prototype=B,[["$ms",o],["$s",s],["$m",u],["$H",l],["$W",a],["$M",f],["$y",h],["$D",d]].forEach(function(P){B[P[1]]=function(j){return this.$g(j,P[0],P[1])}}),E.extend=function(P,j){return P.$i||(P(j,F,E),P.$i=!0),E},E.locale=U,E.isDayjs=A,E.unix=function(P){return E(1e3*P)},E.en=g[S],E.Ls=g,E.p={},E})}($i)),$i.exports}var Rd=vs();const Ad=ie(Rd);var Od={exports:{}};(function(e,t){(function(r,n){e.exports=n(vs())})(it,function(r){function n(s){return s&&typeof s=="object"&&"default"in s?s:{default:s}}var i=n(r),o={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(s,u){return u==="W"?s+"周":s+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(s,u){var l=100*s+u;return l<600?"凌晨":l<900?"早上":l<1100?"上午":l<1300?"中午":l<1800?"下午":"晚上"}};return i.default.locale(o,null,!0),o})})(Od),Ad.locale("zh-cn");function $d(e=!0){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(r){const n=Math.random()*16|0;return(r==="x"?n:n&3|8).toString(16)});return e?t.toLowerCase():t.replace(/-/gi,"")}function hn(e=0){return k(this,null,function*(){return new Promise(t=>{setTimeout(t,e)})})}function jd(e){return[...e.entries()].reduce((t,[r,n])=>(t[r]=n,t),{})}function Dd(e,t){return Array.from(new Set(e))}function Di(e){return e?[].concat(e):[]}function yr(e,t){const r={};if(Array.isArray(t))Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])});else{const n=t;Object.entries(e).forEach(([i,o])=>{n(i,o)||(r[i]=o)})}return r}function Bd(e,t){const r={};if(Array.isArray(t))Object.keys(e).forEach(n=>{t.includes(n)&&(r[n]=e[n])});else{const n=t;Object.entries(e).forEach(([i,o])=>{n(i,o)&&(r[i]=o)})}return r}var bs={exports:{}};(function(e){var t=function(){var r=String.fromCharCode,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",o={};function s(l,a){if(!o[l]){o[l]={};for(var c=0;c<l.length;c++)o[l][l.charAt(c)]=c}return o[l][a]}var u={compressToBase64:function(l){if(l==null)return"";var a=u._compress(l,6,function(c){return n.charAt(c)});switch(a.length%4){default:case 0:return a;case 1:return a+"===";case 2:return a+"==";case 3:return a+"="}},decompressFromBase64:function(l){return l==null?"":l==""?null:u._decompress(l.length,32,function(a){return s(n,l.charAt(a))})},compressToUTF16:function(l){return l==null?"":u._compress(l,15,function(a){return r(a+32)})+" "},decompressFromUTF16:function(l){return l==null?"":l==""?null:u._decompress(l.length,16384,function(a){return l.charCodeAt(a)-32})},compressToUint8Array:function(l){for(var a=u.compress(l),c=new Uint8Array(a.length*2),f=0,p=a.length;f<p;f++){var h=a.charCodeAt(f);c[f*2]=h>>>8,c[f*2+1]=h%256}return c},decompressFromUint8Array:function(l){if(l==null)return u.decompress(l);for(var a=new Array(l.length/2),c=0,f=a.length;c<f;c++)a[c]=l[c*2]*256+l[c*2+1];var p=[];return a.forEach(function(h){p.push(r(h))}),u.decompress(p.join(""))},compressToEncodedURIComponent:function(l){return l==null?"":u._compress(l,6,function(a){return i.charAt(a)})},decompressFromEncodedURIComponent:function(l){return l==null?"":l==""?null:(l=l.replace(/ /g,"+"),u._decompress(l.length,32,function(a){return s(i,l.charAt(a))}))},compress:function(l){return u._compress(l,16,function(a){return r(a)})},_compress:function(l,a,c){if(l==null)return"";var f,p,h={},d={},m="",b="",_="",y=2,v=3,w=2,S=[],g=0,x=0,A;for(A=0;A<l.length;A+=1)if(m=l.charAt(A),Object.prototype.hasOwnProperty.call(h,m)||(h[m]=v++,d[m]=!0),b=_+m,Object.prototype.hasOwnProperty.call(h,b))_=b;else{if(Object.prototype.hasOwnProperty.call(d,_)){if(_.charCodeAt(0)<256){for(f=0;f<w;f++)g=g<<1,x==a-1?(x=0,S.push(c(g)),g=0):x++;for(p=_.charCodeAt(0),f=0;f<8;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1}else{for(p=1,f=0;f<w;f++)g=g<<1|p,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=0;for(p=_.charCodeAt(0),f=0;f<16;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1}y--,y==0&&(y=Math.pow(2,w),w++),delete d[_]}else for(p=h[_],f=0;f<w;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1;y--,y==0&&(y=Math.pow(2,w),w++),h[b]=v++,_=String(m)}if(_!==""){if(Object.prototype.hasOwnProperty.call(d,_)){if(_.charCodeAt(0)<256){for(f=0;f<w;f++)g=g<<1,x==a-1?(x=0,S.push(c(g)),g=0):x++;for(p=_.charCodeAt(0),f=0;f<8;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1}else{for(p=1,f=0;f<w;f++)g=g<<1|p,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=0;for(p=_.charCodeAt(0),f=0;f<16;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1}y--,y==0&&(y=Math.pow(2,w),w++),delete d[_]}else for(p=h[_],f=0;f<w;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1;y--,y==0&&(y=Math.pow(2,w),w++)}for(p=2,f=0;f<w;f++)g=g<<1|p&1,x==a-1?(x=0,S.push(c(g)),g=0):x++,p=p>>1;for(;;)if(g=g<<1,x==a-1){S.push(c(g));break}else x++;return S.join("")},decompress:function(l){return l==null?"":l==""?null:u._decompress(l.length,32768,function(a){return l.charCodeAt(a)})},_decompress:function(l,a,c){var f=[],p=4,h=4,d=3,m="",b=[],_,y,v,w,S,g,x,A={val:c(0),position:a,index:1};for(_=0;_<3;_+=1)f[_]=_;for(v=0,S=Math.pow(2,2),g=1;g!=S;)w=A.val&A.position,A.position>>=1,A.position==0&&(A.position=a,A.val=c(A.index++)),v|=(w>0?1:0)*g,g<<=1;switch(v){case 0:for(v=0,S=Math.pow(2,8),g=1;g!=S;)w=A.val&A.position,A.position>>=1,A.position==0&&(A.position=a,A.val=c(A.index++)),v|=(w>0?1:0)*g,g<<=1;x=r(v);break;case 1:for(v=0,S=Math.pow(2,16),g=1;g!=S;)w=A.val&A.position,A.position>>=1,A.position==0&&(A.position=a,A.val=c(A.index++)),v|=(w>0?1:0)*g,g<<=1;x=r(v);break;case 2:return""}for(f[3]=x,y=x,b.push(x);;){if(A.index>l)return"";for(v=0,S=Math.pow(2,d),g=1;g!=S;)w=A.val&A.position,A.position>>=1,A.position==0&&(A.position=a,A.val=c(A.index++)),v|=(w>0?1:0)*g,g<<=1;switch(x=v){case 0:for(v=0,S=Math.pow(2,8),g=1;g!=S;)w=A.val&A.position,A.position>>=1,A.position==0&&(A.position=a,A.val=c(A.index++)),v|=(w>0?1:0)*g,g<<=1;f[h++]=r(v),x=h-1,p--;break;case 1:for(v=0,S=Math.pow(2,16),g=1;g!=S;)w=A.val&A.position,A.position>>=1,A.position==0&&(A.position=a,A.val=c(A.index++)),v|=(w>0?1:0)*g,g<<=1;f[h++]=r(v),x=h-1,p--;break;case 2:return b.join("")}if(p==0&&(p=Math.pow(2,d),d++),f[x])m=f[x];else if(x===h)m=y+y.charAt(0);else return null;b.push(m),f[h++]=y+m.charAt(0),p--,y=m,p==0&&(p=Math.pow(2,d),d++)}}};return u}();e!=null?e.exports=t:typeof angular<"u"&&angular!=null&&angular.module("LZString",[]).factory("LZString",function(){return t})})(bs);var ws=bs.exports;function Cd(e){for(var t=[],r=0;r<e.length;){var n=e[r];if(n==="*"||n==="+"||n==="?"){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if(n==="\\"){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if(n==="{"){t.push({type:"OPEN",index:r,value:e[r++]});continue}if(n==="}"){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(n===":"){for(var i="",o=r+1;o<e.length;){var s=e.charCodeAt(o);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||s===95){i+=e[o++];continue}break}if(!i)throw new TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=o;continue}if(n==="("){var u=1,l="",o=r+1;if(e[o]==="?")throw new TypeError('Pattern cannot start with "?" at '.concat(o));for(;o<e.length;){if(e[o]==="\\"){l+=e[o++]+e[o++];continue}if(e[o]===")"){if(u--,u===0){o++;break}}else if(e[o]==="("&&(u++,e[o+1]!=="?"))throw new TypeError("Capturing groups are not allowed at ".concat(o));l+=e[o++]}if(u)throw new TypeError("Unbalanced pattern at ".concat(r));if(!l)throw new TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}function _s(e,t){t===void 0&&(t={});for(var r=Cd(e),n=t.prefixes,i=n===void 0?"./":n,o="[^".concat(Qt(t.delimiter||"/#?"),"]+?"),s=[],u=0,l=0,a="",c=function(g){if(l<r.length&&r[l].type===g)return r[l++].value},f=function(g){var x=c(g);if(x!==void 0)return x;var A=r[l],U=A.type,E=A.index;throw new TypeError("Unexpected ".concat(U," at ").concat(E,", expected ").concat(g))},p=function(){for(var g="",x;x=c("CHAR")||c("ESCAPED_CHAR");)g+=x;return g};l<r.length;){var h=c("CHAR"),d=c("NAME"),m=c("PATTERN");if(d||m){var b=h||"";i.indexOf(b)===-1&&(a+=b,b=""),a&&(s.push(a),a=""),s.push({name:d||u++,prefix:b,suffix:"",pattern:m||o,modifier:c("MODIFIER")||""});continue}var _=h||c("ESCAPED_CHAR");if(_){a+=_;continue}a&&(s.push(a),a="");var y=c("OPEN");if(y){var b=p(),v=c("NAME")||"",w=c("PATTERN")||"",S=p();f("CLOSE"),s.push({name:v||(w?u++:""),pattern:v&&!w?o:w,prefix:b,suffix:S,modifier:c("MODIFIER")||""});continue}f("END")}return s}function Nd(e,t){return Id(_s(e,t),t)}function Id(e,t){t===void 0&&(t={});var r=pn(t),n=t.encode,i=n===void 0?function(l){return l}:n,o=t.validate,s=o===void 0?!0:o,u=e.map(function(l){if(typeof l=="object")return new RegExp("^(?:".concat(l.pattern,")$"),r)});return function(l){for(var a="",c=0;c<e.length;c++){var f=e[c];if(typeof f=="string"){a+=f;continue}var p=l?l[f.name]:void 0,h=f.modifier==="?"||f.modifier==="*",d=f.modifier==="*"||f.modifier==="+";if(Array.isArray(p)){if(!d)throw new TypeError('Expected "'.concat(f.name,'" to not repeat, but got an array'));if(p.length===0){if(h)continue;throw new TypeError('Expected "'.concat(f.name,'" to not be empty'))}for(var m=0;m<p.length;m++){var b=i(p[m],f);if(s&&!u[c].test(b))throw new TypeError('Expected all "'.concat(f.name,'" to match "').concat(f.pattern,'", but got "').concat(b,'"'));a+=f.prefix+b+f.suffix}continue}if(typeof p=="string"||typeof p=="number"){var b=i(String(p),f);if(s&&!u[c].test(b))throw new TypeError('Expected "'.concat(f.name,'" to match "').concat(f.pattern,'", but got "').concat(b,'"'));a+=f.prefix+b+f.suffix;continue}if(!h){var _=d?"an array":"a string";throw new TypeError('Expected "'.concat(f.name,'" to be ').concat(_))}}return a}}function Pd(e,t){var r=[],n=dn(e,r,t);return kd(n,r,t)}function kd(e,t,r){r===void 0&&(r={});var n=r.decode,i=n===void 0?function(o){return o}:n;return function(o){var s=e.exec(o);if(!s)return!1;for(var u=s[0],l=s.index,a=Object.create(null),c=function(p){if(s[p]===void 0)return"continue";var h=t[p-1];h.modifier==="*"||h.modifier==="+"?a[h.name]=s[p].split(h.prefix+h.suffix).map(function(d){return i(d,h)}):a[h.name]=i(s[p],h)},f=1;f<s.length;f++)c(f);return{path:u,index:l,params:a}}}function Qt(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function pn(e){return e&&e.sensitive?"":"i"}function Fd(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}function Md(e,t,r){var n=e.map(function(i){return dn(i,t,r).source});return new RegExp("(?:".concat(n.join("|"),")"),pn(r))}function Ud(e,t,r){return Ld(_s(e,r),t,r)}function Ld(e,t,r){r===void 0&&(r={});for(var n=r.strict,i=n===void 0?!1:n,o=r.start,s=o===void 0?!0:o,u=r.end,l=u===void 0?!0:u,a=r.encode,c=a===void 0?function(E){return E}:a,f=r.delimiter,p=f===void 0?"/#?":f,h=r.endsWith,d=h===void 0?"":h,m="[".concat(Qt(d),"]|$"),b="[".concat(Qt(p),"]"),_=s?"^":"",y=0,v=e;y<v.length;y++){var w=v[y];if(typeof w=="string")_+=Qt(c(w));else{var S=Qt(c(w.prefix)),g=Qt(c(w.suffix));if(w.pattern)if(t&&t.push(w),S||g)if(w.modifier==="+"||w.modifier==="*"){var x=w.modifier==="*"?"?":"";_+="(?:".concat(S,"((?:").concat(w.pattern,")(?:").concat(g).concat(S,"(?:").concat(w.pattern,"))*)").concat(g,")").concat(x)}else _+="(?:".concat(S,"(").concat(w.pattern,")").concat(g,")").concat(w.modifier);else w.modifier==="+"||w.modifier==="*"?_+="((?:".concat(w.pattern,")").concat(w.modifier,")"):_+="(".concat(w.pattern,")").concat(w.modifier);else _+="(?:".concat(S).concat(g,")").concat(w.modifier)}}if(l)i||(_+="".concat(b,"?")),_+=r.endsWith?"(?=".concat(m,")"):"$";else{var A=e[e.length-1],U=typeof A=="string"?b.indexOf(A[A.length-1])>-1:A===void 0;i||(_+="(?:".concat(b,"(?=").concat(m,"))?")),U||(_+="(?=".concat(b,"|").concat(m,")"))}return new RegExp(_,pn(r))}function dn(e,t,r){return e instanceof RegExp?Fd(e,t):Array.isArray(e)?Md(e,t,r):Ud(e,t,r)}var Vd=Object.defineProperty,Hd=(e,t,r)=>t in e?Vd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,le=(e,t,r)=>(Hd(e,typeof t!="symbol"?t+"":t,r),r);class qd{constructor(){le(this,"queue",[]),le(this,"isProcessing",!1),le(this,"results",[]),le(this,"cache",new Map),le(this,"pendingTasks",new Map)}add(t,r){const n=this.cache.get(t);if(n)return n.status==="fulfilled"?Promise.resolve(n.value):Promise.reject(n.reason);const i=this.pendingTasks.get(t);if(i)return i;const o=new Promise((s,u)=>{this.queue.push({key:t,task:r,resolve:s,reject:u}),this.isProcessing||this.processQueue()});return this.pendingTasks.set(t,o),o}getAllResults(){return[...this.results]}getResult(t){return this.cache.get(t)}clearCacheForKey(t){this.cache.delete(t)}clearAllCache(){this.cache.clear()}processQueue(){return k(this,null,function*(){if(this.queue.length===0){this.isProcessing=!1;return}this.isProcessing=!0;const{key:t,task:r,resolve:n,reject:i}=this.queue.shift();try{const o=this.cache.get(t);if(o){o.status==="fulfilled"?n(o.value):i(o.reason),this.pendingTasks.delete(t),this.processQueue();return}const s=yield r(),u={key:t,status:"fulfilled",value:s};this.results.push(u),this.cache.set(t,u),n(s),this.pendingTasks.delete(t)}catch(o){const s={key:t,status:"rejected",reason:o};this.results.push(s),this.cache.set(t,s),i(o),this.pendingTasks.delete(t)}finally{this.processQueue()}})}}ws.compress;ws.decompress;/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/core 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const zd="VueMaterial",Kd="VueRouterMaterial",Wd={[zd]:["Transition","TransitionGroup","KeepAlive","Teleport","Suspense"],[Kd]:["RouterView","RouterLink"]};class Jd{constructor(){z(this,"listeners",[]);z(this,"isReady",!1)}triggerReady(){this.isReady=!0;for(const t of this.listeners)t();this.listeners=[]}ready(t){this.isReady?t():this.listeners.push(t)}resetReady(){this.isReady=!1}}var Yd=Object.defineProperty,Gd=(e,t,r)=>t in e?Yd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,lt=(e,t,r)=>Gd(e,typeof t!="symbol"?t+"":t,r);(function(){if(typeof window>"u"||typeof EventTarget>"u")return;const e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(t,r,n){typeof n!="boolean"&&(n=n||{},n.passive=!1),e.call(this,t,r,n)}})();const Zd=e=>typeof e=="symbol";new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Zd));function Es(e,t){return function(){return e.apply(t,arguments)}}const{toString:Qd}=Object.prototype,{getPrototypeOf:mn}=Object,{iterator:Ze,toStringTag:Ss}=Symbol,Qe=(e=>t=>{const r=Qd.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),mt=e=>(e=e.toLowerCase(),t=>Qe(t)===e),Xe=e=>t=>typeof t===e,{isArray:oe}=Array,ye=Xe("undefined");function Xd(e){return e!==null&&!ye(e)&&e.constructor!==null&&!ye(e.constructor)&&st(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const xs=mt("ArrayBuffer");function tm(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&xs(e.buffer),t}const em=Xe("string"),st=Xe("function"),Ts=Xe("number"),tr=e=>e!==null&&typeof e=="object",rm=e=>e===!0||e===!1,Ie=e=>{if(Qe(e)!=="object")return!1;const t=mn(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ss in e)&&!(Ze in e)},nm=mt("Date"),im=mt("File"),om=mt("Blob"),sm=mt("FileList"),am=e=>tr(e)&&st(e.pipe),um=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||st(e.append)&&((t=Qe(e))==="formdata"||t==="object"&&st(e.toString)&&e.toString()==="[object FormData]"))},cm=mt("URLSearchParams"),[lm,fm,hm,pm]=["ReadableStream","Request","Response","Headers"].map(mt),dm=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function _e(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,i;if(typeof e!="object"&&(e=[e]),oe(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let u;for(n=0;n<s;n++)u=o[n],t.call(null,e[u],u,e)}}function Rs(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,i;for(;n-- >0;)if(i=r[n],t===i.toLowerCase())return i;return null}const Ft=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,As=e=>!ye(e)&&e!==Ft;function Lr(){const{caseless:e}=As(this)&&this||{},t={},r=(n,i)=>{const o=e&&Rs(t,i)||i;Ie(t[o])&&Ie(n)?t[o]=Lr(t[o],n):Ie(n)?t[o]=Lr({},n):oe(n)?t[o]=n.slice():t[o]=n};for(let n=0,i=arguments.length;n<i;n++)arguments[n]&&_e(arguments[n],r);return t}const mm=(e,t,r,{allOwnKeys:n}={})=>(_e(t,(i,o)=>{r&&st(i)?e[o]=Es(i,r):e[o]=i},{allOwnKeys:n}),e),gm=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),ym=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},vm=(e,t,r,n)=>{let i,o,s;const u={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],(!n||n(s,e,t))&&!u[s]&&(t[s]=e[s],u[s]=!0);e=r!==!1&&mn(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},bm=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},wm=e=>{if(!e)return null;if(oe(e))return e;let t=e.length;if(!Ts(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},_m=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&mn(Uint8Array)),Em=(e,t)=>{const r=(e&&e[Ze]).call(e);let n;for(;(n=r.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},Sm=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},xm=mt("HTMLFormElement"),Tm=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,r,n){return r.toUpperCase()+n}),Bi=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Rm=mt("RegExp"),Os=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};_e(r,(i,o)=>{let s;(s=t(i,o,e))!==!1&&(n[o]=s||i)}),Object.defineProperties(e,n)},Am=e=>{Os(e,(t,r)=>{if(st(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(st(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Om=(e,t)=>{const r={},n=i=>{i.forEach(o=>{r[o]=!0})};return oe(e)?n(e):n(String(e).split(t)),r},$m=()=>{},jm=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Dm(e){return!!(e&&st(e.append)&&e[Ss]==="FormData"&&e[Ze])}const Bm=e=>{const t=new Array(10),r=(n,i)=>{if(tr(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[i]=n;const o=oe(n)?[]:{};return _e(n,(s,u)=>{const l=r(s,i+1);!ye(l)&&(o[u]=l)}),t[i]=void 0,o}}return n};return r(e,0)},Cm=mt("AsyncFunction"),Nm=e=>e&&(tr(e)||st(e))&&st(e.then)&&st(e.catch),$s=((e,t)=>e?setImmediate:t?((r,n)=>(Ft.addEventListener("message",({source:i,data:o})=>{i===Ft&&o===r&&n.length&&n.shift()()},!1),i=>{n.push(i),Ft.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",st(Ft.postMessage)),Im=typeof queueMicrotask<"u"?queueMicrotask.bind(Ft):typeof process<"u"&&process.nextTick||$s,Pm=e=>e!=null&&st(e[Ze]),R={isArray:oe,isArrayBuffer:xs,isBuffer:Xd,isFormData:um,isArrayBufferView:tm,isString:em,isNumber:Ts,isBoolean:rm,isObject:tr,isPlainObject:Ie,isReadableStream:lm,isRequest:fm,isResponse:hm,isHeaders:pm,isUndefined:ye,isDate:nm,isFile:im,isBlob:om,isRegExp:Rm,isFunction:st,isStream:am,isURLSearchParams:cm,isTypedArray:_m,isFileList:sm,forEach:_e,merge:Lr,extend:mm,trim:dm,stripBOM:gm,inherits:ym,toFlatObject:vm,kindOf:Qe,kindOfTest:mt,endsWith:bm,toArray:wm,forEachEntry:Em,matchAll:Sm,isHTMLForm:xm,hasOwnProperty:Bi,hasOwnProp:Bi,reduceDescriptors:Os,freezeMethods:Am,toObjectSet:Om,toCamelCase:Tm,noop:$m,toFiniteNumber:jm,findKey:Rs,global:Ft,isContextDefined:As,isSpecCompliantForm:Dm,toJSONObject:Bm,isAsyncFn:Cm,isThenable:Nm,setImmediate:$s,asap:Im,isIterable:Pm};function K(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}R.inherits(K,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:R.toJSONObject(this.config),code:this.code,status:this.status}}});const js=K.prototype,Ds={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ds[e]={value:e}});Object.defineProperties(K,Ds);Object.defineProperty(js,"isAxiosError",{value:!0});K.from=(e,t,r,n,i,o)=>{const s=Object.create(js);return R.toFlatObject(e,s,function(u){return u!==Error.prototype},u=>u!=="isAxiosError"),K.call(s,e.message,t,r,n,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};const km=null;function Vr(e){return R.isPlainObject(e)||R.isArray(e)}function Bs(e){return R.endsWith(e,"[]")?e.slice(0,-2):e}function Ci(e,t,r){return e?e.concat(t).map(function(n,i){return n=Bs(n),!r&&i?"["+n+"]":n}).join(r?".":""):t}function Fm(e){return R.isArray(e)&&!e.some(Vr)}const Mm=R.toFlatObject(R,{},null,function(e){return/^is[A-Z]/.test(e)});function er(e,t,r){if(!R.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=R.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(h,d){return!R.isUndefined(d[h])});const n=r.metaTokens,i=r.visitor||a,o=r.dots,s=r.indexes,u=(r.Blob||typeof Blob<"u"&&Blob)&&R.isSpecCompliantForm(t);if(!R.isFunction(i))throw new TypeError("visitor must be a function");function l(h){if(h===null)return"";if(R.isDate(h))return h.toISOString();if(R.isBoolean(h))return h.toString();if(!u&&R.isBlob(h))throw new K("Blob is not supported. Use a Buffer instead.");return R.isArrayBuffer(h)||R.isTypedArray(h)?u&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function a(h,d,m){let b=h;if(h&&!m&&typeof h=="object"){if(R.endsWith(d,"{}"))d=n?d:d.slice(0,-2),h=JSON.stringify(h);else if(R.isArray(h)&&Fm(h)||(R.isFileList(h)||R.endsWith(d,"[]"))&&(b=R.toArray(h)))return d=Bs(d),b.forEach(function(_,y){!(R.isUndefined(_)||_===null)&&t.append(s===!0?Ci([d],y,o):s===null?d:d+"[]",l(_))}),!1}return Vr(h)?!0:(t.append(Ci(m,d,o),l(h)),!1)}const c=[],f=Object.assign(Mm,{defaultVisitor:a,convertValue:l,isVisitable:Vr});function p(h,d){if(!R.isUndefined(h)){if(c.indexOf(h)!==-1)throw Error("Circular reference detected in "+d.join("."));c.push(h),R.forEach(h,function(m,b){(!(R.isUndefined(m)||m===null)&&i.call(t,m,R.isString(b)?b.trim():b,d,f))===!0&&p(m,d?d.concat(b):[b])}),c.pop()}}if(!R.isObject(e))throw new TypeError("data must be an object");return p(e),t}function Ni(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function gn(e,t){this._pairs=[],e&&er(e,this,t)}const Cs=gn.prototype;Cs.append=function(e,t){this._pairs.push([e,t])};Cs.toString=function(e){const t=e?function(r){return e.call(this,r,Ni)}:Ni;return this._pairs.map(function(r){return t(r[0])+"="+t(r[1])},"").join("&")};function Um(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ns(e,t,r){if(!t)return e;const n=r&&r.encode||Um;R.isFunction(r)&&(r={serialize:r});const i=r&&r.serialize;let o;if(i?o=i(t,r):o=R.isURLSearchParams(t)?t.toString():new gn(t,r).toString(n),o){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}let Ii=class{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){R.forEach(this.handlers,function(r){r!==null&&t(r)})}};const Is={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Lm=typeof URLSearchParams<"u"?URLSearchParams:gn,Vm=typeof FormData<"u"?FormData:null,Hm=typeof Blob<"u"?Blob:null,qm={isBrowser:!0,classes:{URLSearchParams:Lm,FormData:Vm,Blob:Hm},protocols:["http","https","file","blob","url","data"]},yn=typeof window<"u"&&typeof document<"u",Hr=typeof navigator=="object"&&navigator||void 0,zm=yn&&(!Hr||["ReactNative","NativeScript","NS"].indexOf(Hr.product)<0),Km=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Wm=yn&&window.location.href||"http://localhost",Jm=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:yn,hasStandardBrowserEnv:zm,hasStandardBrowserWebWorkerEnv:Km,navigator:Hr,origin:Wm},Symbol.toStringTag,{value:"Module"})),rt=V(V({},Jm),qm);function Ym(e,t){return er(e,new rt.classes.URLSearchParams,Object.assign({visitor:function(r,n,i,o){return rt.isNode&&R.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Gm(e){return R.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Zm(e){const t={},r=Object.keys(e);let n;const i=r.length;let o;for(n=0;n<i;n++)o=r[n],t[o]=e[o];return t}function Ps(e){function t(r,n,i,o){let s=r[o++];if(s==="__proto__")return!0;const u=Number.isFinite(+s),l=o>=r.length;return s=!s&&R.isArray(i)?i.length:s,l?(R.hasOwnProp(i,s)?i[s]=[i[s],n]:i[s]=n,!u):((!i[s]||!R.isObject(i[s]))&&(i[s]=[]),t(r,n,i[s],o)&&R.isArray(i[s])&&(i[s]=Zm(i[s])),!u)}if(R.isFormData(e)&&R.isFunction(e.entries)){const r={};return R.forEachEntry(e,(n,i)=>{t(Gm(n),i,r,0)}),r}return null}function Qm(e,t,r){if(R.isString(e))try{return(t||JSON.parse)(e),R.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Ee={transitional:Is,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const r=t.getContentType()||"",n=r.indexOf("application/json")>-1,i=R.isObject(e);if(i&&R.isHTMLForm(e)&&(e=new FormData(e)),R.isFormData(e))return n?JSON.stringify(Ps(e)):e;if(R.isArrayBuffer(e)||R.isBuffer(e)||R.isStream(e)||R.isFile(e)||R.isBlob(e)||R.isReadableStream(e))return e;if(R.isArrayBufferView(e))return e.buffer;if(R.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Ym(e,this.formSerializer).toString();if((o=R.isFileList(e))||r.indexOf("multipart/form-data")>-1){const s=this.env&&this.env.FormData;return er(o?{"files[]":e}:e,s&&new s,this.formSerializer)}}return i||n?(t.setContentType("application/json",!1),Qm(e)):e}],transformResponse:[function(e){const t=this.transitional||Ee.transitional,r=t&&t.forcedJSONParsing,n=this.responseType==="json";if(R.isResponse(e)||R.isReadableStream(e))return e;if(e&&R.isString(e)&&(r&&!this.responseType||n)){const i=!(t&&t.silentJSONParsing)&&n;try{return JSON.parse(e)}catch(o){if(i)throw o.name==="SyntaxError"?K.from(o,K.ERR_BAD_RESPONSE,this,null,this.response):o}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rt.classes.FormData,Blob:rt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};R.forEach(["delete","get","head","post","put","patch"],e=>{Ee.headers[e]={}});const Xm=R.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),tg=e=>{const t={};let r,n,i;return e&&e.split(`
`).forEach(function(o){i=o.indexOf(":"),r=o.substring(0,i).trim().toLowerCase(),n=o.substring(i+1).trim(),!(!r||t[r]&&Xm[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Pi=Symbol("internals");function fe(e){return e&&String(e).trim().toLowerCase()}function Pe(e){return e===!1||e==null?e:R.isArray(e)?e.map(Pe):String(e)}function eg(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const rg=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function vr(e,t,r,n,i){if(R.isFunction(n))return n.call(this,t,r);if(i&&(t=r),!!R.isString(t)){if(R.isString(n))return t.indexOf(n)!==-1;if(R.isRegExp(n))return n.test(t)}}function ng(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function ig(e,t){const r=R.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(i,o,s){return this[n].call(this,t,i,o,s)},configurable:!0})})}let at=class{constructor(t){t&&this.set(t)}set(t,r,n){const i=this;function o(u,l,a){const c=fe(l);if(!c)throw new Error("header name must be a non-empty string");const f=R.findKey(i,c);(!f||i[f]===void 0||a===!0||a===void 0&&i[f]!==!1)&&(i[f||l]=Pe(u))}const s=(u,l)=>R.forEach(u,(a,c)=>o(a,c,l));if(R.isPlainObject(t)||t instanceof this.constructor)s(t,r);else if(R.isString(t)&&(t=t.trim())&&!rg(t))s(tg(t),r);else if(R.isObject(t)&&R.isIterable(t)){let u={},l,a;for(const c of t){if(!R.isArray(c))throw TypeError("Object iterator must return a key-value pair");u[a=c[0]]=(l=u[a])?R.isArray(l)?[...l,c[1]]:[l,c[1]]:c[1]}s(u,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=fe(t),t){const n=R.findKey(this,t);if(n){const i=this[n];if(!r)return i;if(r===!0)return eg(i);if(R.isFunction(r))return r.call(this,i,n);if(R.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=fe(t),t){const n=R.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||vr(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let i=!1;function o(s){if(s=fe(s),s){const u=R.findKey(n,s);u&&(!r||vr(n,n[u],u,r))&&(delete n[u],i=!0)}}return R.isArray(t)?t.forEach(o):o(t),i}clear(t){const r=Object.keys(this);let n=r.length,i=!1;for(;n--;){const o=r[n];(!t||vr(this,this[o],o,t,!0))&&(delete this[o],i=!0)}return i}normalize(t){const r=this,n={};return R.forEach(this,(i,o)=>{const s=R.findKey(n,o);if(s){r[s]=Pe(i),delete r[o];return}const u=t?ng(o):String(o).trim();u!==o&&delete r[o],r[u]=Pe(i),n[u]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return R.forEach(this,(n,i)=>{n!=null&&n!==!1&&(r[i]=t&&R.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(i=>n.set(i)),n}static accessor(t){const r=(this[Pi]=this[Pi]={accessors:{}}).accessors,n=this.prototype;function i(o){const s=fe(o);r[s]||(ig(n,o),r[s]=!0)}return R.isArray(t)?t.forEach(i):i(t),this}};at.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);R.reduceDescriptors(at.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});R.freezeMethods(at);function br(e,t){const r=this||Ee,n=t||r,i=at.from(n.headers);let o=n.data;return R.forEach(e,function(s){o=s.call(r,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function ks(e){return!!(e&&e.__CANCEL__)}function se(e,t,r){K.call(this,e!=null?e:"canceled",K.ERR_CANCELED,t,r),this.name="CanceledError"}R.inherits(se,K,{__CANCEL__:!0});function Fs(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new K("Request failed with status code "+r.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function og(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function sg(e,t){e=e||10;const r=new Array(e),n=new Array(e);let i=0,o=0,s;return t=t!==void 0?t:1e3,function(u){const l=Date.now(),a=n[o];s||(s=l),r[i]=u,n[i]=l;let c=o,f=0;for(;c!==i;)f+=r[c++],c=c%e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),l-s<t)return;const p=a&&l-a;return p?Math.round(f*1e3/p):void 0}}function ag(e,t){let r=0,n=1e3/t,i,o;const s=(u,l=Date.now())=>{r=l,i=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const l=Date.now(),a=l-r;a>=n?s(u,l):(i=u,o||(o=setTimeout(()=>{o=null,s(i)},n-a)))},()=>i&&s(i)]}const He=(e,t,r=3)=>{let n=0;const i=sg(50,250);return ag(o=>{const s=o.loaded,u=o.lengthComputable?o.total:void 0,l=s-n,a=i(l),c=s<=u;n=s;const f={loaded:s,total:u,progress:u?s/u:void 0,bytes:l,rate:a||void 0,estimated:a&&u&&c?(u-s)/a:void 0,event:o,lengthComputable:u!=null,[t?"download":"upload"]:!0};e(f)},r)},ki=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Fi=e=>(...t)=>R.asap(()=>e(...t)),ug=rt.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,rt.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(rt.origin),rt.navigator&&/(msie|trident)/i.test(rt.navigator.userAgent)):()=>!0,cg=rt.hasStandardBrowserEnv?{write(e,t,r,n,i,o){const s=[e+"="+encodeURIComponent(t)];R.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),R.isString(n)&&s.push("path="+n),R.isString(i)&&s.push("domain="+i),o===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function lg(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function fg(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ms(e,t,r){let n=!lg(t);return e&&(n||r==!1)?fg(e,t):t}const Mi=e=>e instanceof at?V({},e):e;function Vt(e,t){t=t||{};const r={};function n(a,c,f,p){return R.isPlainObject(a)&&R.isPlainObject(c)?R.merge.call({caseless:p},a,c):R.isPlainObject(c)?R.merge({},c):R.isArray(c)?c.slice():c}function i(a,c,f,p){if(R.isUndefined(c)){if(!R.isUndefined(a))return n(void 0,a,f,p)}else return n(a,c,f,p)}function o(a,c){if(!R.isUndefined(c))return n(void 0,c)}function s(a,c){if(R.isUndefined(c)){if(!R.isUndefined(a))return n(void 0,a)}else return n(void 0,c)}function u(a,c,f){if(f in t)return n(a,c);if(f in e)return n(void 0,a)}const l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:u,headers:(a,c,f)=>i(Mi(a),Mi(c),f,!0)};return R.forEach(Object.keys(Object.assign({},e,t)),function(a){const c=l[a]||i,f=c(e[a],t[a],a);R.isUndefined(f)&&c!==u||(r[a]=f)}),r}const Us=e=>{const t=Vt({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:i,xsrfCookieName:o,headers:s,auth:u}=t;t.headers=s=at.from(s),t.url=Ns(Ms(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),u&&s.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):"")));let l;if(R.isFormData(r)){if(rt.hasStandardBrowserEnv||rt.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((l=s.getContentType())!==!1){const[a,...c]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];s.setContentType([a||"multipart/form-data",...c].join("; "))}}if(rt.hasStandardBrowserEnv&&(n&&R.isFunction(n)&&(n=n(t)),n||n!==!1&&ug(t.url))){const a=i&&o&&cg.read(o);a&&s.set(i,a)}return t},hg=typeof XMLHttpRequest<"u",pg=hg&&function(e){return new Promise(function(t,r){const n=Us(e);let i=n.data;const o=at.from(n.headers).normalize();let{responseType:s,onUploadProgress:u,onDownloadProgress:l}=n,a,c,f,p,h;function d(){p&&p(),h&&h(),n.cancelToken&&n.cancelToken.unsubscribe(a),n.signal&&n.signal.removeEventListener("abort",a)}let m=new XMLHttpRequest;m.open(n.method.toUpperCase(),n.url,!0),m.timeout=n.timeout;function b(){if(!m)return;const y=at.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),v={data:!s||s==="text"||s==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:y,config:e,request:m};Fs(function(w){t(w),d()},function(w){r(w),d()},v),m=null}"onloadend"in m?m.onloadend=b:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(b)},m.onabort=function(){m&&(r(new K("Request aborted",K.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new K("Network Error",K.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let y=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const v=n.transitional||Is;n.timeoutErrorMessage&&(y=n.timeoutErrorMessage),r(new K(y,v.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,m)),m=null},i===void 0&&o.setContentType(null),"setRequestHeader"in m&&R.forEach(o.toJSON(),function(y,v){m.setRequestHeader(v,y)}),R.isUndefined(n.withCredentials)||(m.withCredentials=!!n.withCredentials),s&&s!=="json"&&(m.responseType=n.responseType),l&&([f,h]=He(l,!0),m.addEventListener("progress",f)),u&&m.upload&&([c,p]=He(u),m.upload.addEventListener("progress",c),m.upload.addEventListener("loadend",p)),(n.cancelToken||n.signal)&&(a=y=>{m&&(r(!y||y.type?new se(null,e,m):y),m.abort(),m=null)},n.cancelToken&&n.cancelToken.subscribe(a),n.signal&&(n.signal.aborted?a():n.signal.addEventListener("abort",a)));const _=og(n.url);if(_&&rt.protocols.indexOf(_)===-1){r(new K("Unsupported protocol "+_+":",K.ERR_BAD_REQUEST,e));return}m.send(i||null)})},dg=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,i;const o=function(a){if(!i){i=!0,u();const c=a instanceof Error?a:this.reason;n.abort(c instanceof K?c:new se(c instanceof Error?c.message:c))}};let s=t&&setTimeout(()=>{s=null,o(new K(`timeout ${t} of ms exceeded`,K.ETIMEDOUT))},t);const u=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(a=>{a.unsubscribe?a.unsubscribe(o):a.removeEventListener("abort",o)}),e=null)};e.forEach(a=>a.addEventListener("abort",o));const{signal:l}=n;return l.unsubscribe=()=>R.asap(u),l}},mg=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,i;for(;n<r;)i=n+t,yield e.slice(n,i),n=i},gg=function(e,t){return ur(this,null,function*(){try{for(var r=En(yg(e)),n,i,o;n=!(i=yield new Ct(r.next())).done;n=!1){const s=i.value;yield*cr(mg(s,t))}}catch(i){o=[i]}finally{try{n&&(i=r.return)&&(yield new Ct(i.call(r)))}finally{if(o)throw o[0]}}})},yg=function(e){return ur(this,null,function*(){if(e[Symbol.asyncIterator]){yield*cr(e);return}const t=e.getReader();try{for(;;){const{done:r,value:n}=yield new Ct(t.read());if(r)break;yield n}}finally{yield new Ct(t.cancel())}})},Ui=(e,t,r,n)=>{const i=gg(e,t);let o=0,s,u=a=>{s||(s=!0,n&&n(a))};return new ReadableStream({pull(a){return k(this,null,function*(){try{const{done:c,value:f}=yield i.next();if(c){u(),a.close();return}let p=f.byteLength;if(r){let h=o+=p;r(h)}a.enqueue(new Uint8Array(f))}catch(c){throw u(c),c}})},cancel(a){return u(a),i.return()}},{highWaterMark:2})},rr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ls=rr&&typeof ReadableStream=="function",vg=rr&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):e=>k(null,null,function*(){return new Uint8Array(yield new Response(e).arrayBuffer())})),Vs=(e,...t)=>{try{return!!e(...t)}catch(r){return!1}},bg=Ls&&Vs(()=>{let e=!1;const t=new Request(rt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Li=64*1024,qr=Ls&&Vs(()=>R.isReadableStream(new Response("").body)),qe={stream:qr&&(e=>e.body)};rr&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!qe[t]&&(qe[t]=R.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new K(`Response type '${t}' is not supported`,K.ERR_NOT_SUPPORT,n)})})})(new Response);const wg=e=>k(null,null,function*(){if(e==null)return 0;if(R.isBlob(e))return e.size;if(R.isSpecCompliantForm(e))return(yield new Request(rt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(R.isArrayBufferView(e)||R.isArrayBuffer(e))return e.byteLength;if(R.isURLSearchParams(e)&&(e=e+""),R.isString(e))return(yield vg(e)).byteLength}),_g=(e,t)=>k(null,null,function*(){const r=R.toFiniteNumber(e.getContentLength());return r!=null?r:wg(t)}),Eg=rr&&(e=>k(null,null,function*(){let{url:t,method:r,data:n,signal:i,cancelToken:o,timeout:s,onDownloadProgress:u,onUploadProgress:l,responseType:a,headers:c,withCredentials:f="same-origin",fetchOptions:p}=Us(e);a=a?(a+"").toLowerCase():"text";let h=dg([i,o&&o.toAbortSignal()],s),d;const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let b;try{if(l&&bg&&r!=="get"&&r!=="head"&&(b=yield _g(c,n))!==0){let S=new Request(t,{method:"POST",body:n,duplex:"half"}),g;if(R.isFormData(n)&&(g=S.headers.get("content-type"))&&c.setContentType(g),S.body){const[x,A]=ki(b,He(Fi(l)));n=Ui(S.body,Li,x,A)}}R.isString(f)||(f=f?"include":"omit");const _="credentials"in Request.prototype;d=new Request(t,tt(V({},p),{signal:h,method:r.toUpperCase(),headers:c.normalize().toJSON(),body:n,duplex:"half",credentials:_?f:void 0}));let y=yield fetch(d,p);const v=qr&&(a==="stream"||a==="response");if(qr&&(u||v&&m)){const S={};["status","statusText","headers"].forEach(U=>{S[U]=y[U]});const g=R.toFiniteNumber(y.headers.get("content-length")),[x,A]=u&&ki(g,He(Fi(u),!0))||[];y=new Response(Ui(y.body,Li,x,()=>{A&&A(),m&&m()}),S)}a=a||"text";let w=yield qe[R.findKey(qe,a)||"text"](y,e);return!v&&m&&m(),yield new Promise((S,g)=>{Fs(S,g,{data:w,headers:at.from(y.headers),status:y.status,statusText:y.statusText,config:e,request:d})})}catch(_){throw m&&m(),_&&_.name==="TypeError"&&/Load failed|fetch/i.test(_.message)?Object.assign(new K("Network Error",K.ERR_NETWORK,e,d),{cause:_.cause||_}):K.from(_,_&&_.code,e,d)}})),zr={http:km,xhr:pg,fetch:Eg};R.forEach(zr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(r){}Object.defineProperty(e,"adapterName",{value:t})}});const Vi=e=>`- ${e}`,Sg=e=>R.isFunction(e)||e===null||e===!1,Hs={getAdapter:e=>{e=R.isArray(e)?e:[e];const{length:t}=e;let r,n;const i={};for(let o=0;o<t;o++){r=e[o];let s;if(n=r,!Sg(r)&&(n=zr[(s=String(r)).toLowerCase()],n===void 0))throw new K(`Unknown adapter '${s}'`);if(n)break;i[s||"#"+o]=n}if(!n){const o=Object.entries(i).map(([u,l])=>`adapter ${u} `+(l===!1?"is not supported by the environment":"is not available in the build"));let s=t?o.length>1?`since :
`+o.map(Vi).join(`
`):" "+Vi(o[0]):"as no adapter specified";throw new K("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return n},adapters:zr};function wr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new se(null,e)}function Hi(e){return wr(e),e.headers=at.from(e.headers),e.data=br.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Hs.getAdapter(e.adapter||Ee.adapter)(e).then(function(t){return wr(e),t.data=br.call(e,e.transformResponse,t),t.headers=at.from(t.headers),t},function(t){return ks(t)||(wr(e),t&&t.response&&(t.response.data=br.call(e,e.transformResponse,t.response),t.response.headers=at.from(t.response.headers))),Promise.reject(t)})}const qs="1.10.0",nr={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{nr[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const qi={};nr.transitional=function(e,t,r){function n(i,o){return"[Axios v"+qs+"] Transitional option '"+i+"'"+o+(r?". "+r:"")}return(i,o,s)=>{if(e===!1)throw new K(n(o," has been removed"+(t?" in "+t:"")),K.ERR_DEPRECATED);return t&&!qi[o]&&(qi[o]=!0,console.warn(n(o," has been deprecated since v"+t+" and will be removed in the near future"))),e?e(i,o,s):!0}};nr.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};function xg(e,t,r){if(typeof e!="object")throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let i=n.length;for(;i-- >0;){const o=n[i],s=t[o];if(s){const u=e[o],l=u===void 0||s(u,o,e);if(l!==!0)throw new K("option "+o+" must be "+l,K.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new K("Unknown option "+o,K.ERR_BAD_OPTION)}}const ke={assertOptions:xg,validators:nr},gt=ke.validators;let Mt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Ii,response:new Ii}}request(t,r){return k(this,null,function*(){try{return yield this._request(t,r)}catch(n){if(n instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const o=i.stack?i.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch(s){}}throw n}})}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=Vt(this.defaults,r);const{transitional:n,paramsSerializer:i,headers:o}=r;n!==void 0&&ke.assertOptions(n,{silentJSONParsing:gt.transitional(gt.boolean),forcedJSONParsing:gt.transitional(gt.boolean),clarifyTimeoutError:gt.transitional(gt.boolean)},!1),i!=null&&(R.isFunction(i)?r.paramsSerializer={serialize:i}:ke.assertOptions(i,{encode:gt.function,serialize:gt.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),ke.assertOptions(r,{baseUrl:gt.spelling("baseURL"),withXsrfToken:gt.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let s=o&&R.merge(o.common,o[r.method]);o&&R.forEach(["delete","get","head","post","put","patch","common"],d=>{delete o[d]}),r.headers=at.concat(s,o);const u=[];let l=!0;this.interceptors.request.forEach(function(d){typeof d.runWhen=="function"&&d.runWhen(r)===!1||(l=l&&d.synchronous,u.unshift(d.fulfilled,d.rejected))});const a=[];this.interceptors.response.forEach(function(d){a.push(d.fulfilled,d.rejected)});let c,f=0,p;if(!l){const d=[Hi.bind(this),void 0];for(d.unshift.apply(d,u),d.push.apply(d,a),p=d.length,c=Promise.resolve(r);f<p;)c=c.then(d[f++],d[f++]);return c}p=u.length;let h=r;for(f=0;f<p;){const d=u[f++],m=u[f++];try{h=d(h)}catch(b){m.call(this,b);break}}try{c=Hi.call(this,h)}catch(d){return Promise.reject(d)}for(f=0,p=a.length;f<p;)c=c.then(a[f++],a[f++]);return c}getUri(t){t=Vt(this.defaults,t);const r=Ms(t.baseURL,t.url,t.allowAbsoluteUrls);return Ns(r,t.params,t.paramsSerializer)}};R.forEach(["delete","get","head","options"],function(e){Mt.prototype[e]=function(t,r){return this.request(Vt(r||{},{method:e,url:t,data:(r||{}).data}))}});R.forEach(["post","put","patch"],function(e){function t(r){return function(n,i,o){return this.request(Vt(o||{},{method:e,headers:r?{"Content-Type":"multipart/form-data"}:{},url:n,data:i}))}}Mt.prototype[e]=t(),Mt.prototype[e+"Form"]=t(!0)});let Tg=class zs{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const n=this;this.promise.then(i=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](i);n._listeners=null}),this.promise.then=i=>{let o;const s=new Promise(u=>{n.subscribe(u),o=u}).then(i);return s.cancel=function(){n.unsubscribe(o)},s},t(function(i,o,s){n.reason||(n.reason=new se(i,o,s),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new zs(function(r){t=r}),cancel:t}}};function Rg(e){return function(t){return e.apply(null,t)}}function Ag(e){return R.isObject(e)&&e.isAxiosError===!0}const Kr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Kr).forEach(([e,t])=>{Kr[t]=e});function Ks(e){const t=new Mt(e),r=Es(Mt.prototype.request,t);return R.extend(r,Mt.prototype,t,{allOwnKeys:!0}),R.extend(r,t,null,{allOwnKeys:!0}),r.create=function(n){return Ks(Vt(e,n))},r}const Q=Ks(Ee);Q.Axios=Mt;Q.CanceledError=se;Q.CancelToken=Tg;Q.isCancel=ks;Q.VERSION=qs;Q.toFormData=er;Q.AxiosError=K;Q.Cancel=Q.CanceledError;Q.all=function(e){return Promise.all(e)};Q.spread=Rg;Q.isAxiosError=Ag;Q.mergeConfig=Vt;Q.AxiosHeaders=at;Q.formToJSON=e=>Ps(R.isHTMLForm(e)?new FormData(e):e);Q.getAdapter=Hs.getAdapter;Q.HttpStatusCode=Kr;Q.default=Q;const{Axios:e1,AxiosError:r1,CanceledError:n1,isCancel:i1,CancelToken:o1,VERSION:s1,all:a1,Cancel:u1,isAxiosError:c1,spread:l1,toFormData:f1,AxiosHeaders:h1,HttpStatusCode:p1,formToJSON:d1,getAdapter:m1,mergeConfig:g1}=Q,Og={form:"application/x-www-form-urlencoded",json:"application/json",data:"multipart/form-data"},$g=["put","post","patch"],zi="Local-Request-Id",jg=100,Dg=300;let Bg=class{constructor(e={}){lt(this,"axios"),lt(this,"settings"),lt(this,"records",{}),lt(this,"isLoading",!1),lt(this,"stopSkipWarn"),lt(this,"showLoading"),lt(this,"showError"),this.settings=Object.assign({type:"form"},e.settings||{});const t=yr(e,["settings","query"]);this.axios=Q.create(he({headers:{"Content-Type":"application/x-www-form-urlencoded"},timeout:120*1e3},t)),this.setupSkipWarn(this.settings),this.showLoading=ts(this.openLoading.bind(this),jg),this.showError=Wp(this._showError.bind(this),Dg,{leading:!0,trailing:!1})}setConfig(e={}){this.settings=he(this.settings,e.settings||{});const t=yr(e,["settings","query"]);this.axios.defaults=he(this.axios.defaults,t),this.setupSkipWarn(this.settings)}cancel(e,t="请求已取消"){if(e){const r=this.records[e];if(!r)return;r.source.cancel(t)}else for(const r of Object.values(this.records))r.source.cancel(t)}createHeaders(e,t,r){const n=t.injectHeaders?typeof t.headers=="function"?t.headers(e,r,t):t.headers||{}:{},i=V(V({"Content-Type":Og[t.type||"form"]},r.headers),n);return t.skipWarn&&(i[zi]=e),i}isJsonType(e){return Object.entries(e).some(([t,r])=>t.toLowerCase()==="content-type"&&String(r).includes("application/json"))}toFormData(e,t="data"){if(e instanceof FormData||e instanceof URLSearchParams)return e;const r=t==="data"?new FormData:new URLSearchParams;return Object.entries(e).forEach(([n,i])=>{r.append(n,i)}),r}createSendData(e,t,r,n,i={}){const{type:o,skipWarn:s}=e,{name:u="skipWarn"}=s||{};let{data:l,params:a={},method:c="get"}=t;const f=n?{[u]:!0}:{};return $g.includes(c.toLowerCase())?(l=Object.assign(l||{},f),l=o!=="json"||!this.isJsonType(r)?this.toFormData(l,o):l,a=V({},i)):o==="form"?a=V(V(V({},l||{}),i),f):(l&&(o!=="json"||!this.isJsonType(r))&&(l=this.toFormData(l,o)),a=V(V({},i),f)),{data:l,params:a}}createUrl(e){let{url:t,params:r}=e;if(t){let n=gs(t)?new URL(t).origin:"";const i=n?t.replace(n,""):t;try{const o=Nd(i,{encode:encodeURIComponent});return n+o(r||{})}catch(o){console.warn("createUrl","pathToRegexpCompile error",t)}}return t}openLoading(e){const{loading:t,showLoading:r}=e;t&&r&&Object.keys(this.records).length>0&&(this.isLoading=!0,r())}closeLoading(e){const{loading:t,hideLoading:r}=e;if(!t)return;this.isLoading=!1;const n=Object.keys(this.records);r&&n.length===0&&(this.isLoading=!1,r())}_showError(e,t){var i;const{failMessage:r,showError:n}=e;if(r&&n){const o=(i=t==null?void 0:t.response)==null?void 0:i.data,s=(o==null?void 0:o.message)||(o==null?void 0:o.msg)||(t==null?void 0:t.message)||(t==null?void 0:t.msg)||"未知错误";n(s,t)}}validResponse(e,t){const{validSuccess:r,validate:n}=e;return r&&n?!!n(t):!0}isSkipWarnResponse(e){return!!e.promise}send(e={},t=!1){const r=he({},this.settings,e.settings||{}),n=e.query||{},i=yr(e,["settings","query"]),o=$d(!1),s=Q.CancelToken.source();this.records[o]={settings:r,config:i,source:s};const u=this.createUrl(i),l=this.createHeaders(o,r,i),{data:a,params:c}=this.createSendData(r,i,l,t,n);return this.showLoading(r),new Promise((f,p)=>{this.axios(tt(V({cancelToken:s.token},i),{url:u,headers:l,data:a,params:c})).then(h=>{var d;return this.isSkipWarnResponse(h)?f(h.promise):this.validResponse(r,h)?f(r.originResponse?h:(d=h.data)==null?void 0:d.data):(this.showError(r,h.data),p(h.data))}).catch(h=>(this.showError(r,h),p(h))).finally(()=>{delete this.records[o],this.closeLoading(r)})})}useResponse(e,t){const{response:r}=this.axios.interceptors,n=r.use(e,t);return()=>r.eject(n)}useRequest(e,t){const{request:r}=this.axios.interceptors,n=r.use(e,t);return()=>r.eject(n)}setupSkipWarn(e){if(this.stopSkipWarn&&(this.stopSkipWarn(),this.stopSkipWarn=void 0),!e.skipWarn)return;const{code:t,executor:r,callback:n,complete:i}=e.skipWarn;this.stopSkipWarn=this.useResponse(o=>{const s=(o.config.headers||{})[zi],u=this.records[s];if(!u)return o;const{data:l}=o;if(!l||typeof l!="object")return o;if((l==null?void 0:l.code)===t){n&&n(o);const a=new Promise(r).then(()=>this.send(tt(V({},u.config),{settings:u.settings}),!0));a.catch(c=>c).finally(()=>{i&&i()}),o.promise=a}return o})}};function vn(e={}){const t=new Bg(e),r=t.send.bind(t),n=t.cancel.bind(t),i=t.setConfig.bind(t),o=t.useRequest.bind(t),s=t.useResponse.bind(t);return Object.assign(r,tt(V({},t),{instance:t,send:r,cancel:n,setConfig:i,useRequest:o,useResponse:s}))}const Wr=vn({settings:{injectHeaders:!0,loading:!0,originResponse:!0}}),Jr=typeof window<"u";function Cg(e){const t={};return e?(e.forEach((r,n)=>{t[n]=typeof r=="string"?decodeURIComponent(r):r}),t):{}}let Ws=class{constructor(t={}){lt(this,"options",{type:"cache",expired:0,prefix:"__VTJ_"}),lt(this,"caches",{}),lt(this,"types"),this.types={local:Jr?window.localStorage:this.caches,session:Jr?window.sessionStorage:this.caches,cache:this.caches},this.config(t)}config(t={}){this.options=Object.assign(this.options,t)}save(t,r,n={}){const{type:i,expired:o,prefix:s}=V(V({},this.options),n),u=Date.now(),l=s+t,a=this.types[i]||this.caches,c={value:r,timestamp:u,expired:o};a===this.caches?a[l]=c:a.setItem(l,JSON.stringify(c))}get(t,r={}){const{type:n,prefix:i}=V(V({},this.options),r),o=i+t,s=this.types[n]||this.caches;let u;if(s===this.caches)u=s[o];else{const f=s.getItem(o);f&&(u=JSON.parse(f))}if(!u)return null;const{value:l,timestamp:a,expired:c}=u;return c>0&&a+c<Date.now()?(this.remove(t,r),null):l}remove(t,r={}){const{type:n,prefix:i}=V(V({},this.options),r),o=this.types[n]||this.caches,s=i+t;o===this.caches?delete o[s]:o.removeItem(s)}clear(t={}){const{type:r}=V(V({},this.options),t),n=this.types[r]||this.caches;n===this.caches?this.caches={}:n.clear()}};const _r=new Ws;function Js(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Fe={exports:{}},Ng=Fe.exports,Ki;function Ig(){return Ki||(Ki=1,function(e,t){(function(r,n){n(t,e)})(Ng,function(r,n){var i={timeout:5e3,jsonpCallback:"callback"};function o(){return"jsonp_"+Date.now()+"_"+Math.ceil(Math.random()*1e5)}function s(a){try{delete window[a]}catch(c){window[a]=void 0}}function u(a){var c=document.getElementById(a);c&&document.getElementsByTagName("head")[0].removeChild(c)}function l(a){var c=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],f=a,p=c.timeout||i.timeout,h=c.jsonpCallback||i.jsonpCallback,d=void 0;return new Promise(function(m,b){var _=c.jsonpCallbackFunction||o(),y=h+"_"+_;window[_]=function(w){m({ok:!0,json:function(){return Promise.resolve(w)}}),d&&clearTimeout(d),u(y),s(_)},f+=f.indexOf("?")===-1?"?":"&";var v=document.createElement("script");v.setAttribute("src",""+f+h+"="+_),c.charset&&v.setAttribute("charset",c.charset),c.nonce&&v.setAttribute("nonce",c.nonce),c.referrerPolicy&&v.setAttribute("referrerPolicy",c.referrerPolicy),c.crossorigin&&v.setAttribute("crossorigin","true"),v.id=y,document.getElementsByTagName("head")[0].appendChild(v),d=setTimeout(function(){b(new Error("JSONP request to "+a+" timed out")),s(_),u(y),window[_]=function(){s(_)}},p),v.onerror=function(){b(new Error("JSONP request to "+a+" failed")),s(_),u(y),d&&clearTimeout(d)}})}n.exports=l})}(Fe,Fe.exports)),Fe.exports}var Pg=Ig();const kg=Js(Pg);function Fg(e){if(Jr){const{protocol:t,host:r,pathname:n}=location;return`${t}//${r}${e?n:""}`}else return null}function Mg(e=""){const t=e.match(ms);return t?t[0]:""}function Ys(e){const t=[];for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push([r,encodeURIComponent(e[r])].join("="));return t.join("&")}function Yr(e,t,r){const n={};e=(e||location.search).replace(/^[^]*\?/,""),t=t||"&",r=r||"=";let i;const o=new RegExp("(?:^|\\"+t+")([^\\"+r+"\\"+t+"]+)(?:\\"+r+"([^\\"+t+"]*))?","g");for(;(i=o.exec(e))!==null;)i[1]!==e&&(n[decodeURIComponent(i[1])]=decodeURIComponent(i[2]||""));return n}function Gs(e,t){t=typeof t=="string"?Yr(t):t;const r=e.split("?")[0],n=Yr(e),i=Object.assign({},n,t),o=Ys(i);return o?[r,o].join("?"):e}const Me=Object.freeze(Object.defineProperty({__proto__:null,append:Gs,getCurrentHost:Fg,getHost:Mg,parse:Yr,stringify:Ys},Symbol.toStringTag,{value:"Module"}));function Zs(r){return k(this,arguments,function*(e,t={}){const{query:n={}}=t;e.includes("${")&&(e=zp(e)(n||{}));const i=Gs(e,n);return yield(yield kg(i,t)).json()})}var Er,Wi;function Ug(){if(Wi)return Er;Wi=1,Er=function(n,i,o){var s=document.head||document.getElementsByTagName("head")[0],u=document.createElement("script");typeof i=="function"&&(o=i,i={}),i=i||{},o=o||function(){},u.type=i.type||"text/javascript",u.charset=i.charset||"utf8",u.async="async"in i?!!i.async:!0,u.src=n,i.attrs&&e(u,i.attrs),i.text&&(u.text=""+i.text);var l="onload"in u?t:r;l(u,o),u.onload||t(u,o),s.appendChild(u)};function e(n,i){for(var o in i)n.setAttribute(o,i[o])}function t(n,i){n.onload=function(){this.onerror=this.onload=null,i(null,n)},n.onerror=function(){this.onerror=this.onload=null,i(new Error("Failed to load "+this.src),n)}}function r(n,i){n.onreadystatechange=function(){this.readyState!="complete"&&this.readyState!="loaded"||(this.onreadystatechange=null,i(null,n))}}return Er}var Lg=Ug();const Vg=Js(Lg);function Ji(e,t={}){return new Promise((r,n)=>{const{library:i}=t;Vg(e,t,(o,s)=>{o?n(o):r(i?window[i]:void 0)})})}const Yi={debug:-1,log:0,info:0,warn:1,error:2},Hg=function(e,t,r,n){return function(...i){if(t&&Yi[t]<=Yi[e]&&console[e].apply&&(n==="*"||r.startsWith(n)))return console[e].apply(console,qg(i,r))}};function qg(e,t){return t!=="*"&&(typeof e[0]=="string"?e[0]=`[${t}] ${e[0]}`:e=["["+t+"]"].concat(e)),e}function zg(e,t){if(!e)return{targetLevel:t.level,targetBizName:t.bizName};if(~e.indexOf(":")){const r=e.split(":");return{targetLevel:r[0],targetBizName:r[1]}}return{targetLevel:e,targetBizName:"*"}}const Kg={level:"warn",bizName:"*"};class Wg{constructor(t){lt(this,"config"),lt(this,"options"),this.options=V(V({},Kg),t);const r=typeof location<"u"?location:{},n=(/__(?:logConf|logLevel)__=([^#/&]*)/.exec(r.href)||[])[1];this.config=zg(n,t)}_log(t){const{targetLevel:r,targetBizName:n}=this.config,{bizName:i}=this.options;return Hg(t,r,i,n)}debug(...t){return this._log("debug")(...t)}log(...t){return this._log("log")(...t)}info(...t){return this._log("info")(...t)}warn(...t){return this._log("warn")(...t)}error(...t){return this._log("error")(...t)}}function Jg(e){return new Wg(e)}const te=Jg({level:"log",bizName:"VTJ"});/*! js-cookie v3.0.5 | MIT */function De(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)e[n]=r[n]}return e}var Yg={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function Gr(e,t){function r(i,o,s){if(!(typeof document>"u")){s=De({},t,s),typeof s.expires=="number"&&(s.expires=new Date(Date.now()+s.expires*864e5)),s.expires&&(s.expires=s.expires.toUTCString()),i=encodeURIComponent(i).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var u="";for(var l in s)s[l]&&(u+="; "+l,s[l]!==!0&&(u+="="+s[l].split(";")[0]));return document.cookie=i+"="+e.write(o,i)+u}}function n(i){if(!(typeof document>"u"||arguments.length&&!i)){for(var o=document.cookie?document.cookie.split("; "):[],s={},u=0;u<o.length;u++){var l=o[u].split("="),a=l.slice(1).join("=");try{var c=decodeURIComponent(l[0]);if(s[c]=e.read(a,c),i===c)break}catch(f){}}return i?s[i]:s}}return Object.create({set:r,get:n,remove:function(i,o){r(i,"",De({},o,{expires:-1}))},withAttributes:function(i){return Gr(this.converter,De({},this.attributes,i))},withConverter:function(i){return Gr(De({},this.converter,i),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var bn=Gr(Yg,{path:"/"});function Gg(e,t,r){bn.set(e,t,r)}function Zg(e){return bn.get(e)}function Qg(e,t){bn.remove(e,t)}const Sr=Object.freeze(Object.defineProperty({__proto__:null,get:Zg,remove:Qg,set:Gg},Symbol.toStringTag,{value:"Module"}));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/renderer 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const xr="0.12.70";var et=(e=>(e.Runtime="Runtime",e.Design="Design",e.Raw="Raw",e.VNode="VNode",e))(et||{});const Gi=["$el","$emit","$nextTick","$parent","$root","$attrs","$slots","$watch","$props","$options","$forceUpdate"],Xg=["vIf","vElseIf","vElse","vShow","vModel","vFor","vBind","vHtml"],t0={String,Number,Boolean,Array,Object,Function,Date},Ue="VtjPage",Tr="VtjHomepage",e0="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot,svg".split(","),r0="component,slot".split(",");function Be(e,t){return e.map(r=>gs(r)||r.startsWith("/")?r:`${t}${r}`)}function ir(e){return/\.css$/.test(e)}function or(e){return/\.js$/.test(e)}function n0(e){return/\.json$/.test(e)}function i0(e,t=!1){return t&&e.endsWith(".prod.js")?e.replace(".prod.js",".js"):e}function o0(e,t,r=!1){const n=e.filter(p=>!!p.enabled),i=[],o=[],s=[],u=[],l={},a={},c=[],f={};return n.forEach(({urls:p,assetsUrl:h,library:d,assetsLibrary:m,localeLibrary:b})=>{p==null||p.forEach(_=>{or(_)&&i.push(i0(_,r)),ir(_)&&o.push(_)}),d&&(u.push(d),l[d]=Be(p||[],t),b&&(a[d]=b)),h&&s.push(h),m&&c.push(m),d&&m&&(f[m]=d)}),{scripts:Be(i,t),css:Be(o,t),materials:Be(s,t),libraryExports:u,materialExports:Dd(c),materialMapLibrary:f,libraryMap:l,libraryLocaleMap:a}}function s0(e,t){var o;const{name:r,parent:n,alias:i}=e;return n?(o=t[n])==null?void 0:o[i||r]:t[i||r]}function a0(e=[]){const t=e.filter(n=>ir(n)),r=e.filter(n=>or(n));return{css:t,js:r}}function u0(e,t){const r=a=>{const c=[];let f=0;for(;f<a.length;){if(/\s/.test(a[f])){f++;continue}if(a.substring(f,f+2)==="/*"){const _=a.indexOf("*/",f+2);if(_!==-1){f=_+2;continue}}if(a[f]==="@"){const _=f;for(;f<a.length&&a[f]!=="{"&&a[f]!==";";)f++;const y=a.substring(_,f).trim(),v=y.includes("@keyframes");if(a[f]===";")c.push({type:"simple-at-rule",content:a.substring(_,f+1)}),f++;else if(a[f]==="{"){const w=f+1;let S=1;for(f++;f<a.length&&S>0;)a[f]==="{"?S++:a[f]==="}"&&S--,f++;const g=a.substring(_,f),x=a.substring(w,f-1);c.push({type:v?"keyframes":"at-rule",rule:y,content:g,inner:x})}continue}const p=f;for(;f<a.length&&a[f]!=="{";)f++;if(f>=a.length)break;const h=a.substring(p,f).trim();if(!h){f++;continue}const d=f+1;let m=1;for(f++;f<a.length&&m>0;)a[f]==="{"?m++:a[f]==="}"&&m--,f++;const b=a.substring(d,f-1);c.push({type:"rule",selector:h,content:b.trim()})}return c},n=a=>{const c=a.trim();return/^(from|to|\d+(\.\d+)?%)$/.test(c)},i=a=>a.replace(/::v-deep\(/g,":deep(").replace(/::v-deep\s+/g,":deep(").replace(/\/deep\//g," ").replace(/>>>/g," ").replace(/(.*?):deep\(([^)]+)\)/g,(c,f,p)=>{const h=f.trim(),d=p.trim();return h?`${h}[${t}] ${d}`:d}),o=a=>{const c=a.trim();if(!c||c.includes(`[${t}]`)||/^(:root|:host|html|body)(\s|$|:|\.|\#|\[)/.test(c)||n(c))return c;const f=c.match(/^(.+?)((?:::?[\w-]+(?:\([^)]*\))?)*)\s*$/);if(f){const[,p,h=""]=f,d=p.trim();return d?`${d}[${t}]${h}`:c}return`${c}[${t}]`},s=a=>i(a).split(",").map(c=>o(c)).filter(c=>c.trim()).join(", "),u=a=>a.map(c=>{switch(c.type){case"simple-at-rule":return c.content;case"keyframes":return c.content;case"at-rule":try{const p=r(c.inner),h=u(p);return`${c.rule} { ${h} }`}catch(p){return c.content}case"rule":if(!c.selector||!c.content)return"";const f=s(c.selector);return f.trim()?`${f} { ${c.content} }`:"";default:return""}}).filter(c=>c.trim()).join(" "),l=a=>a.replace(/\s*{\s*/g," { ").replace(/\s*}\s*/g," } ").replace(/\s*;\s*/g,"; ").replace(/\s*,\s*/g,", ").replace(/\s+/g," ").replace(/^\s+|\s+$/g,"").replace(/\s*}\s*}/g," } }").trim();try{const a=e.replace(/\/\*(?!\s*!)[\s\S]*?\*\//g,"").replace(/^\s+|\s+$/gm,"").replace(/\n\s*\n/g,`
`),c=r(a),f=u(c);return l(f)}catch(a){return console.error("CSS scoping failed:",a),console.error("Input CSS:",e),e.replace(/\/\*[\s\S]*?\*\//g,"").replace(/(@keyframes\s+[^{]+\s*{[^{}]*(?:{[^}]*}[^{}]*)*})/g,c=>c).replace(/([^{}@]+)(?=\s*{)/g,c=>{const f=c.trim();return!f||f.startsWith("@")||f.includes(`[${t}]`)||n(f)?c:`${f}[${t}]`})}}function Qs(e){return $t(e)?e:JSON.stringify(e)}function wn(e,t,r,n=!1){const i=e.CSSStyleSheet,o=n?`data-v-${t}`:t,s=n?u0(r,o):r;if(i.prototype.replaceSync){const u=new i;u.id=t,u.replaceSync(s);const l=e.document,a=l.adoptedStyleSheets,c=Array.from(a).filter(f=>f.id!==t);l.adoptedStyleSheets=[...c,u]}else{const u=e.document;let l=u.getElementById(t);l?l.innerHTML=s:(l=u.createElement("style"),l.id=t,l.innerHTML=s,u.head.appendChild(l))}}function c0(e,t){return k(this,null,function*(){const r=yield window.fetch(t).then(n=>n.text()).catch(()=>"");r&&wn(window,e,r)})}function Xs(e,t=window){const r=t.document,n=t.document.head;for(const i of e)if(!r.getElementById(i)){const o=r.createElement("link");o.rel="stylesheet",o.id=i,o.href=i,n.appendChild(o)}}function ta(n,i){return k(this,arguments,function*(e,t,r=window){const o=r.document,s=r.document.head;let u=r[t];return u?u.default||u:new Promise((l,a)=>{for(const c of e){const f=o.createElement("script");f.src=c,f.onload=()=>{u=r[t],u?l(u.default||u):a(null)},f.onerror=p=>{a(p)},s.appendChild(f)}})})}function l0(e){return Lt(e)||Lt(e==null?void 0:e.install)}function f0(e){return r0.includes(e)}function h0(e){return e0.includes(e)}function ve(e=window){const t=window==null?void 0:window.Mock;if(t)return t;const r=e==null?void 0:e.Mock;if(r&&window)return window.Mock=r,r}function be(e,t,r=!1,n=!1){var i;try{const o=['"use strict";',"var __self = arguments[0];"];o.push("return ");let s=(e.value||"").trim();s=s.replace(/this(\W|$)/g,(l,a)=>`__self${a}`),s=o.join(`
`)+s;const u=`with(${r?"{}":"$scope || {}"}) { ${s} }`;return new Function("$scope",u)(t)}catch(o){if(te.error("parseExpression.error",o,e,(i=t==null?void 0:t.__self)!=null?i:t),n)throw o}}function ze(e,t,r=!1,n=!1){var o;const i=be(e,t,r,n);if(typeof i!="function"&&(te.error("parseFunction.error","not a function",e,(o=t==null?void 0:t.__self)!=null?o:t),n))throw new Error(`"${e.value}" not a function`);return i}function pt(e){return e&&e.type==="JSExpression"}function sr(e){return typeof e=="object"&&e&&e.type==="JSFunction"}const p0={session:!1,authKey:"Authorization",storageKey:"ACCESS_STORAGE",storagePrefix:"__VTJ_",unauthorized:void 0,auth:"/#/login",redirectParam:"r",unauthorizedCode:401,unauthorizedMessage:"登录已经失效，请重新登录！",noPermissionMessage:"无权限访问该页面",appName:"",statusKey:"code"},d0=Symbol("access");class m0{constructor(t){z(this,"options");z(this,"data",null);z(this,"mode",et.Raw);z(this,"interceptResponse",!0);this.options=Object.assign({},p0,t),this.loadData()}enableIntercept(){this.interceptResponse=!0}disableIntercept(){this.interceptResponse=!1}connect(t){const{mode:r,router:n,request:i}=t;this.mode=r,n&&this.mode===et.Raw&&this.setGuard(n),i&&this.setRequest(i)}login(t){const{storageKey:r,storagePrefix:n,session:i,authKey:o}=this.options;this.setData(t),this.data&&(_r.save(r,t,{type:"local",prefix:n}),i&&Sr.set(o,this.data.token))}clear(){const{storageKey:t,storagePrefix:r,session:n,authKey:i}=this.options;this.data=null,_r.remove(t,{type:"local",prefix:r}),n&&Sr.remove(i)}logout(){this.clear(),this.toLogin()}getData(){return this.data?this.data:(this.loadData(),this.data)}getToken(){var t;return this.data||this.loadData(),(t=this.data)==null?void 0:t.token}can(t){const{appName:r}=this.options,{permissions:n={}}=this.data||{};return typeof t=="function"?t(n):Di(t).every(i=>n[i]||n[r+"."+i])}some(t){const{appName:r}=this.options,{permissions:n={}}=this.data||{};return Di(t).some(i=>n[i]||n[r+"."+i])}install(t){t.config.globalProperties.$access=this,t.provide(d0,this)}isAuthPath(t){const{auth:r,isAuth:n}=this.options;if(n)return n(t);if(t.path&&typeof r=="string"){const i=r.split("#")[1]||r;return t.path===i}return!1}toLogin(){const{auth:t,redirectParam:r}=this.options;if(!t)return;const n=r?`?${r}=${encodeURIComponent(location.href)}`:"";typeof t=="function"?t(n):location.href=r?`${t}${n}`:t}setData(t){const{privateKey:r}=this.options;if(Array.isArray(t)&&r){const n=t.map(i=>Oi(i,r));try{this.data=JSON.parse(n.join(""))}catch(i){console.warn(i)}return}if(typeof t=="string")try{const n=r?Oi(t,r):t;n?this.data=JSON.parse(n):console.warn("RSA解密失败或登录信息缺失")}catch(n){console.warn(n)}else this.data=t}loadData(){const{storageKey:t,storagePrefix:r}=this.options,n=_r.get(t,{type:"local",prefix:r});this.setData(n||null)}isLogined(){const{session:t,authKey:r}=this.options;return t&&r?!!Sr.get(r):!!this.getToken()}hasRoutePermission(t){if(t.name===Ue){const r=t.params.id;return r&&this.can(r)}return!0}setGuard(t){t.beforeEach((r,n,i)=>this.guard(r,i))}guard(t,r){return k(this,null,function*(){if(this.isWhiteList(t)||this.isAuthPath(t))return r();if(this.isLogined()){if(this.hasRoutePermission(t))return r();{const{noPermissionMessage:n="无权限访问",unauthorized:i=!1}=this.options;return yield this.showTip(n),Lt(i)?(i(),r(!1)):$t(i)?r(i):r(!1)}}r(!1),this.toLogin()})}isWhiteList(t){const{whiteList:r}=this.options;return r?Array.isArray(r)?r.some(n=>t.fullPath.startsWith(n)):r(t):!1}isUnauthorized(t){var i;const{unauthorizedCode:r=401,statusKey:n="code"}=this.options;return t.status===r||((i=t.data)==null?void 0:i[n])===r}showUnauthorizedAlert(t){return k(this,null,function*(){const{unauthorizedMessage:r="登录已失效"}=this.options;this.isUnauthorized(t)&&(yield this.showTip(r),this.toLogin())})}showTip(t){return k(this,null,function*(){var n;const{alert:r}=this.options;return r?(yield hn(150),yield(n=r(t,{title:"提示",type:"warning"}))==null?void 0:n.catch(()=>!1)):!1})}setRequest(t){t.useRequest(r=>{var n,i;return(n=this.data)!=null&&n.token&&(r.headers[this.options.authKey]=(i=this.data)==null?void 0:i.token),r}),t.useResponse(r=>k(this,null,function*(){return this.interceptResponse&&(yield this.showUnauthorizedAlert(r)),r}),r=>k(this,null,function*(){if(!this.interceptResponse)return Promise.reject(r);const n=r.response||r||{};return yield this.showUnauthorizedAlert(n),Promise.reject(r)}))}}function g0(e={}){const{notify:t,loading:r,settings:n={},Startup:i,access:o,useTitle:s,alert:u}=e;let l=null;return{request:vn({settings:V({type:"form",validSuccess:!0,originResponse:!1,loading:!0,validate:a=>{var c,f;return((c=a.data)==null?void 0:c.code)===0||!!((f=a.data)!=null&&f.success)},failMessage:!0,showError:a=>{t&&t(a||"未知错误")},showLoading:()=>{l&&l.close(),r&&(l=r())},hideLoading:()=>{l&&(l.close(),l=null)}},n)}),jsonp:Zs,notify:t,loading:r,useTitle:s,startupComponent:i,access:o?new m0(V({alert:u},o)):void 0}}function y0(e,t){const{jsonp:r,request:n}=t;if(e.method==="jsonp")return(i={})=>r(e.url,tt(V({},e.jsonpOptions),{query:i}));{const i=e.headers?be(e.headers,{},!0):void 0,o={url:e.url,method:e.method,settings:tt(V({},e.settings),{headers:i})};return(s,u)=>(delete o.data,n.send(he(o,u||{},{data:s})))}}function v0(e,t){const{metaQuery:r}=t;if(!r)return;const{code:n,queryCode:i}=e;return(o,s)=>{if(!r){console.warn("adapter.metaQuery is not defined!");return}return r(n,i,o,s)}}function b0(e=[],t=[],r){const n={};for(const i of e)n[i.id]=y0(i,r);for(const i of t)n[i.id]=v0(i,r);return n}function w0(){return k(this,arguments,function*(e=[],t=window){const r=ve(t);r&&(ea(t),e.forEach(n=>E0(r,n)))})}function _0(e,t=window){const r=sr(e.mockTemplate)&&e.mockTemplate.value?ze(e.mockTemplate,{},!0):void 0,n=ve(t);return(...i)=>k(null,null,function*(){let o={};if(r)try{o=yield r.apply(r,i)}catch(s){te.warn("模拟数据模版异常",s)}return n==null?void 0:n.mock(o)})}function E0(e,t){if(!t.mock)return;const{url:r,mockTemplate:n}=t;if(r&&n){const i=dn(`${r}(.*)`),o=Pd(r,{decode:decodeURIComponent}),s=be(n,{},!0);e.mock(i,u=>{var f;const l=Me.parse(u.url)||{},a=u.body instanceof FormData?Cg(u.body):u.body,c=(f=o(u.url))==null?void 0:f.params;return Object.assign(u,{data:a,params:l,query:c}),e.mock(s(u))})}}function ea(e=window){const t=ve(e);t&&(t._mocked={})}class S0{constructor(t){z(this,"__id",null);z(this,"__mode");z(this,"__instance",null);z(this,"__contextRefs",{});z(this,"__refs",{});z(this,"context",{});z(this,"state",{});z(this,"props",{});z(this,"$props",{});z(this,"$refs",{});z(this,"$el",null);z(this,"$emit",null);z(this,"$nextTick",null);z(this,"$parent",null);z(this,"$root",null);z(this,"$attrs",null);z(this,"$slots",null);z(this,"$watch",null);z(this,"$options",null);z(this,"$forceUpdate",null);z(this,"$components",{});z(this,"$libs",{});z(this,"$apis",{});z(this,"__transform",{});const{mode:r,dsl:n,attrs:i}=t;this.__mode=r,n&&(this.__id=n.id||null,this.__transform=n.transform||{}),i&&Object.assign(this,i)}setup(t,r=Ke){const n=r.getCurrentInstance();if(!n)return;this.__refs={},this.$refs={},this.context={},this.__contextRefs={},this.__instance=n.proxy;const i=n.appContext.config.globalProperties;Object.assign(this,i),Object.assign(this,t||{}),this.__proxy(),r.onMounted(()=>{this.__proxy()}),r.onUnmounted(()=>{this.__cleanup()}),r.onBeforeUpdate(()=>{this.__reset()})}__proxy(){this.__instance&&Gi.forEach(t=>{var r;this[t]=(r=this.__instance)==null?void 0:r[t]})}__cleanup(){Gi.forEach(t=>{this[t]=null}),this.__reset()}__reset(){this.__refs={},this.$refs={},this.__contextRefs={},this.context={}}__parseFunction(t){var r;if(t)if(this.__mode===et.Runtime){const{id:n,type:i}=t,o=n&&(r=this.__transform[n])!=null?r:t.value;return ze({type:i,value:o},this)}else return ze(t,this)}__parseExpression(t){var r;if(t)if(this.__mode===et.Runtime){const{id:n,type:i}=t,o=n&&(r=this.__transform[n])!=null?r:t.value;return be({type:i,value:o},this)}else return be(t,this)}__ref(t=null,r){if(this.__mode!==et.VNode)return t&&t!==this.__id&&(this.__contextRefs[t]=this),n=>k(this,null,function*(){var o,s;yield hn(0);let i=(n==null?void 0:n.$vtjEl)||(n==null?void 0:n.$el)||((s=(o=n==null?void 0:n._)==null?void 0:o.vnode)==null?void 0:s.el)||n;if(!i){typeof r=="string"&&(delete this.$refs[r],t&&delete this.__refs[t]);return}return i.nodeType===3&&i.nextSibling&&(i=i.nextSibling),i.__vtj__=t,et.Design===this.__mode&&(i.__context__=this,i.draggable=!0),t&&(this.__refs[t]=this.__getRefEl(this.__refs,t,n)),typeof r=="function"?r(n):r&&(this.$refs[r]=this.__getRefEl(this.$refs,r,n)),n})}__getRefEl(t,r,n){const i=t[r];if(i&&n!==i){const o=new Set([].concat(i,n));return Array.from(o)}else return n}__clone(t={}){const r=V(V({},this.context),t),n=tt(V({},r),{context:r});return n.context.__proto__=this.context,n.__proto__=this,n}}function Zr(e){var f,p;const{Vue:t=Ke,mode:r=et.Runtime,components:n={},libs:i={},apis:o={},loader:s}=e,u=t.computed(()=>e.dsl),l={$components:n,$libs:i,$apis:o},a=new S0({mode:r,dsl:u.value,attrs:l}),c=t.defineComponent(V({name:u.value.name,__scopeId:u.value.id?`data-v-${u.value.id}`:void 0,props:V({},T0((f=u.value.props)!=null?f:[],a)),setup(h){var v,w,S,g;a.$props=h,a.props=h,u.value.id&&wn(e.window||window,u.value.id,u.value.css||"",!0),a.state=R0(t,(v=u.value.state)!=null?v:{},a);const d=A0(t,(w=u.value.computed)!=null?w:{},a),m=O0((S=u.value.methods)!=null?S:{},a),b=$0(t,u.value.inject,a);for(const[x,A]of Object.entries(b||{}))b[x]=t.inject(x,A);const _=j0(u.value.dataSources||{},a),y=V(V(V(V({},b),d),m),_);return a.setup(y,t),D0(t,(g=u.value.watch)!=null?g:[],a),{vtj:a}},emits:x0(u.value.emits),expose:["vtj"],render(){if(!u.value.nodes)return null;const h=u.value.nodes||[];return h.length===1?re(h[0],a,t,s,h):h.map(d=>re(d,a,t,s,h))}},B0((p=u.value.lifeCycles)!=null?p:{},a)));return{renderer:t.markRaw(c),context:a}}function x0(e=[]){return e.map(t=>$t(t)?t:t.name)}function T0(e=[],t){const r=n=>n?(Array.isArray(n)?n:[n]).map(i=>t0[i]):void 0;return e.map(n=>$t(n)?{name:n}:{name:n.name,type:n.type,required:n.required,default:pt(n.default)?t.__parseExpression(n.default):n.default}).reduce((n,i)=>(n[i.name]={type:r(i.type),required:i.required,default:i.default},n),{})}function R0(e,t,r){return e.reactive(Object.keys(t||{}).reduce((n,i)=>{let o=t[i];return pt(o)?o=r.__parseExpression(o):sr(o)&&(o=r.__parseFunction(o)),n[i]=o,n},{}))}function A0(e,t,r){return Object.entries(t!=null?t:{}).reduce((n,[i,o])=>(n[i]=e.computed(r.__parseFunction(o)),n),{})}function O0(e,t){return Object.entries(e!=null?e:{}).reduce((r,[n,i])=>(r[n]=t.__parseFunction(i),r),{})}function $0(e,t=[],r){return t.reduce((n,i)=>{var a;const{name:o,from:s}=i||{};i.default;const u=pt(s)?r.__parseExpression(s)||o:s!=null?s:o,l=pt(i.default)?r.__parseExpression(i.default):(a=i.default)!=null?a:null;return n[o]=e.inject(u,l),n},{})}function j0(e,t){return Object.keys(e).reduce((r,n)=>{const i=e[n];if(i.type==="mock")r[n]=_0(i);else if(i.ref){const o=t.$apis[i.ref],s=sr(i.transform)?i.transform.value?t.__parseFunction(i.transform):void 0:i.transform;r[n]=(...u)=>k(null,null,function*(){const l=yield o.apply(t,u);return s?s(l):l})}return r},{})}function D0(e,t=[],r){t.forEach(n=>{e.watch(r.__parseExpression(n.source),r.__parseFunction(n.handler),{deep:n.deep,immediate:n.immediate})})}function B0(e,t){return Object.entries(e!=null?e:{}).reduce((r,[n,i])=>{const o=t.__parseFunction(i);return r[n]=()=>k(null,null,function*(){yield hn(0),Lt(o)&&o()}),r},{})}const C0=new qd;let Rr=[],Yt={};const N0=e=>e;function ra(r){return k(this,arguments,function*(e,t=window){const{urls:n=[],library:i}=e,o=n.filter(u=>or(u));if(o.length===0||!i)return null;const s=n.filter(u=>ir(u));return s.length&&Xs(s,t),yield ta(o,i,t).catch(u=>(console.warn("loadScriptUrl error",o,i,u),null))})}function Qr(e){const{getDsl:t,getDslByUrl:r,options:n}=e;return n.window&&(Rr.forEach(i=>{delete n.window[i]}),Rr=[]),(i,o,s=Ke)=>{if(!o||typeof o=="string")return i;if(o.type==="Schema"&&o.id)return s.defineAsyncComponent(()=>k(null,null,function*(){const u=Yt[o.id]||(yield C0.add(o.id,()=>t(o.id)));return u&&(u.name=i,Yt[o.id]=u),u?Zr(tt(V({},n),{Vue:s,dsl:ni(u),mode:et.Runtime,loader:Qr(e)})).renderer:null}));if(o.type==="UrlSchema"&&o.url)return s.defineAsyncComponent(()=>k(null,null,function*(){const u=Yt[o.url]||(yield r(o.url));return u&&(u.name=i,Yt[o.url]=u),u?Zr(tt(V({},n),{Vue:s,dsl:ni(u),mode:et.Runtime,loader:Qr(e)})).renderer:null}));if(o.type==="Plugin"){let u=o.library?Yt[o.library]:null;return u||(o.library&&Rr.push(o.library),u=Yt[o.library||Symbol()]=s.defineAsyncComponent(()=>k(null,null,function*(){return(yield ra(o,n.window))||(console.warn("getPlugin result is null",o),null)})),u)}return i}}function re(e,t,r=Ke,n=N0,i=[],o=!1){var v;if(!e||!e.name||e.invisible)return null;const s=(v=r.getCurrentInstance())==null?void 0:v.appContext,{id:u=null,directives:l=[]}=e,{vIf:a,vElseIf:c,vElse:f,vFor:p,vShow:h,vModels:d,vBind:m,vHtml:b,others:_}=na(l);if(!o&&(c||f))return null;if(a&&!P0(a,t))return U0(e,t,r,n,i);const y=(w,S=0)=>{var j,N,L,M;const g=w.$components,x=(()=>{var q,O,D,C;if(e.name==="component")return k0(w,(q=e.props)==null?void 0:q.is);if(e.name==="slot")return e.name;const H=n(e.name,e.from,r);if($t(H))return f0(H)||h0(H)?H:(C=(D=g[H])!=null?D:(O=s==null?void 0:s.app)==null?void 0:O.component(H))!=null?C:H;if(ct(H)&&e.id){const I=`Loader${e.id}_${S}`;return g[I]||(g[I]=H)}return H})(),A=F0(u,(j=e.props)!=null?j:{},w),U=M0(r,(N=e.events)!=null?N:{},w);if(e.name==="slot")return L0(r,e,A,w,n);m&&Object.assign(A,w.__parseExpression(m.value)),h&&(A.style=Object.assign((L=A.style)!=null?L:{},H0(h,w))),b&&Object.assign(A,q0(b,w)),d.forEach(H=>{Object.assign(A,z0(r,H,w))});const E=K0(r,(M=e.children)!=null?M:[],w,n,e),T=w!=null&&w.__id?`data-v-${w.__id}`:void 0,F=T?{[T]:""}:{};let B=r.createVNode(x,V(V(V({key:`${u}_${S}`},F),A),U),E);const P=s?I0(s,_,w):[];return P.length&&(B=r.withDirectives(B,P)),B};return p?Y0(p,y,t):y(t)}function I0(e,t,r){const n=e.app;return t.map(i=>{const o=typeof i.name=="string"?n.directive(i.name):r.__parseExpression(i.name);if(!o)return null;const s=[o];return i.value&&s.push(r.__parseExpression(i.value)),i.arg&&s.push(i.arg),i.modifiers&&s.push(i.modifiers),s}).filter(i=>!!i)}function na(e=[]){const t=e.find(c=>wt(c.name)==="vIf"),r=e.find(c=>wt(c.name)==="vElseIf"),n=e.find(c=>wt(c.name)==="vElse"),i=e.find(c=>wt(c.name)==="vFor"),o=e.find(c=>wt(c.name)==="vShow"),s=e.find(c=>wt(c.name)==="vBind"),u=e.find(c=>wt(c.name)==="vHtml"),l=e.filter(c=>wt(c.name)==="vModel"),a=e.filter(c=>!Xg.includes(wt(c.name)));return{vIf:t,vElseIf:r,vElse:n,vFor:i,vShow:o,vModels:l,vBind:s,others:a,vHtml:u}}function P0(e,t){return!!t.__parseExpression(e.value)}function k0(e,t){return t?pt(t)?e.__parseExpression(t):t:"div"}function F0(e,t,r){const n=Xr(t,r);return n.ref=r.__ref(e,n.ref),n}function Xr(e,t){return pt(e)?t.__parseExpression(e):sr(e)?t.__parseFunction(e):Array.isArray(e)?e.map(r=>Xr(r,t)):typeof e=="object"?Object.keys(e||{}).reduce((r,n)=>{let i=e[n];return r[n]=Xr(i,t),r},{}):e}function M0(e,t,r){const n=["passive","capture","once"],i={capture:"Capture",once:"Once",passive:"OnceCapture"};return Object.keys(t||{}).reduce((o,s)=>{const u=t[s],l=ia(u.modifiers),a=l.find(p=>n.includes(p)),c="on"+Bo(s)+(a&&i[a]||""),f=r.__parseFunction(u.handler);return f&&(o[c]=e.withModifiers(f,l)),o},{})}function U0(e,t,r,n,i=[]){let o=i.findIndex(s=>s.id===e.id);for(let s=++o;s<i.length;s++){const{directives:u=[]}=i[s],{vElseIf:l,vElse:a}=na(u);if(l){if(t.__parseExpression(l.value))return re(i[s],t,r,n,i,!0);continue}if(a)return re(i[s],t,r,n,i,!0)}return null}function ia(e={},t=!1){const r=Object.keys(e);return t?r.map(n=>"."+n):r}function L0(e,t,r,n,i){var l;const{children:o}=t,s=V0(t,n),u=(l=n.$slots)==null?void 0:l[s.name];return u?u(r):o?$t(o)?e.createTextVNode(o):pt(o)?e.createTextVNode(Qs(n.__parseExpression(o))):Array.isArray(o)?o.map(a=>re(a,n,e,i,o)):null:null}function V0(e,t){const{props:r}=e,n=(r==null?void 0:r.name)||"default";return{name:pt(n)?t.__parseExpression(n):n,params:[]}}function H0(e,t){return t.__parseExpression(e.value)?{}:{display:"none"}}function q0(e,t){return{innerHTML:t.__parseExpression(e.value)||""}}function z0(e,t,r){var u;const n={type:"JSFunction",value:(u=t.value)!=null&&u.value?`(v) => {
        ${t.value.value} = v;
      }`:"(v) => {}"},i=r.__parseFunction(n),o=ia(pt(t.modifiers)?r.__parseExpression(t.modifiers):t.modifiers),s=pt(t.arg)?r.__parseExpression(t.arg):t.arg||"modelValue";return{[s]:r.__parseExpression(t.value),[`onUpdate:${s}`]:o.length&&i?e.withModifiers(i,o):i}}function K0(e,t,r,n,i){if(!t)return null;if($t(t))return{default:()=>t};if(pt(t))return{default:()=>Qs(r.__parseExpression(t))};if(Array.isArray(t)&&t.length>0){const o=W0(t),s=(u,l)=>!u||!i?{}:i!=null&&i.id&&Object.keys(u).length?l?{[l]:u}:{[`scope_${i.id}`]:u}:l?{[l]:Object.create(null)}:{};return Object.entries(o).reduce((u,[l,{nodes:a,params:c,scope:f}])=>(u[l]=p=>{const h=c.length?Bd(p!=null?p:{},c):s(p,f);return a.map(d=>re(d,r.__clone(h),e,n,a))},u),{})}return null}function W0(e){const t={};for(const r of e){const n=J0(r.slot),i=n.name;t[i]?(t[i].nodes.push(r),t[i].params=t[i].params.concat(n.params),t[i].scope=n.scope||""):t[i]={nodes:[r],params:n.params,scope:n.scope||""}}return t}function J0(e="default"){return $t(e)?{name:e,params:[],scope:""}:V({params:[],scope:""},e)}function Y0(e,t,r){const{value:n,iterator:i}=e,{item:o="item",index:s="index"}=i||{};let u=r.__parseExpression(n)||[];return Number.isInteger(u)&&(u=new Array(u).fill(!0).map((l,a)=>a+1)),Array.isArray(u)?u.map((l,a)=>t(r.__clone({[o]:l,[s]:a}),a)):(console.warn("[vForRender]:",`${n==null?void 0:n.value} is not a Arrary`),[])}const Ar=Xi({name:"VtjPageContainer",setup(){return k(this,null,function*(){const e=X0(),t=va(),r=t.meta.__vtj__||t.params.id,n=r?e.getPage(r):e.getHomepage(),i=n?yield e.getRenderComponent(n.id):null,o=ga(Symbol());if(n){Object.assign(t.meta,n.meta||{},{cache:n.cache});const{useTitle:s}=e==null?void 0:e.adapter;s&&s(n.title||"VTJ")}return{provider:e,component:i,file:n,query:t.query,meta:t.meta,sid:o,route:t}})},render(){const{component:e,query:t,sid:r}=this;return e?$r(e,tt(V({},t),{key:r})):$r("div","页面不存在")},activated(){this.meta.cache===!1&&(this.sid=Symbol())}}),G0=Xi({name:"VtjStartupContainer",render(){return $r("div","page not found!")}});function tn(e,t,r=[]){const n=[];for(const i of r){const{id:o,title:s,icon:u,children:l,hidden:a,layout:c}=i;if(c){const f=tn(e,t,l||[]);n.push(...f)}else{const f={id:o,title:s,icon:u,hidden:a,url:`${e}/${t}/${o}`,children:l&&l.length?tn(e,t,l):void 0};n.push(f)}}return n}function Or(e,t){const{id:r,title:n,meta:i}=e,{name:o="page",prefix:s="",component:u,routeMeta:l}=t;return{name:r,path:`${s}${o}/${r}`,component:u,meta:tt(V(V({title:n},l),i),{__vtj__:r})}}function en(e){const{name:t="page",prefix:r="",pages:n=[],component:i,loader:o,routeMeta:s,homepage:u}=e,l=[];for(const a of n){const{id:c,title:f,dir:p,layout:h,children:d,meta:m}=a;if(p){const b=en({name:t,prefix:r,component:i,routeMeta:s,homepage:u,loader:o,pages:d||[]});l.push(...b)}else if(h){const b=en({name:t,prefix:r,component:i,routeMeta:s,homepage:u,loader:o,pages:d||[]}),_={name:`layout_${c}`,path:r,component:()=>o(c),meta:tt(V(V({title:f},s),m),{__vtj__:c}),children:b};l.push(_),l.push(Or(a,e))}else if(l.push(Or(a,e)),u===c){const b=Or(a,e);b.path="",b.name=`home_${c}`,l.push(b)}}return l}const oa=Symbol("Provider");class Z0 extends Jd{constructor(r){super();z(this,"mode");z(this,"globals",{});z(this,"modules",{});z(this,"adapter",{request:Wr,jsonp:Zs});z(this,"apis",{});z(this,"dependencies",{});z(this,"materials",{});z(this,"library",{});z(this,"service");z(this,"project",null);z(this,"components",{});z(this,"nodeEnv","development");z(this,"router",null);z(this,"materialPath","./");z(this,"urlDslCaches",{});this.options=r;const{service:n,mode:i=et.Raw,dependencies:o,materials:s,project:u={},adapter:l={},globals:a={},modules:c={},router:f=null,materialPath:p="./",nodeEnv:h="development"}=r;this.mode=i,this.modules=c,this.service=n,this.router=f,this.materialPath=p,this.nodeEnv=h,o&&(this.dependencies=o),s&&(this.materials=s),Object.assign(this.globals,a),Object.assign(this.adapter,l);const{access:d,request:m}=this.adapter;d&&d.connect({mode:i,router:f,request:m}),u&&i!==et.Design?this.load(u):this.project=u}createMock(r){return(...n)=>k(null,null,function*(){var o;let i={};if(r)try{i=yield r.apply(r,n)}catch(s){te.warn("模拟数据模版异常",s)}return(o=ve())==null?void 0:o.mock(i)})}load(r){return k(this,null,function*(){const{vtjDir:n=".vtj"}=this.options,i=this.modules[`${n}/projects/${r.id}.json`]||this.modules[`/src/${n}/projects/${r.id}.json`];if(this.project=i?yield i():yield this.service.init(r),!this.project)throw new Error("project is null");const{apis:o=[],meta:s=[]}=this.project,u=window;u&&(u.CKEDITOR_VERSION=void 0),this.mode===et.Raw?yield this.loadDependencies(u):yield this.loadAssets(u),this.initMock(u),this.apis=b0(o,s,this.adapter),ea(u),w0(o,u),r.platform!=="uniapp"&&this.initRouter(),this.triggerReady()})}initMock(r){const n=ve(r);n&&n.setup({timeout:"50-500"})}loadDependencies(r){return k(this,null,function*(){const n=Object.entries(this.dependencies);for(const[i,o]of n)r[i]||(r[i]=this.library[i]=yield o())})}loadAssets(r){return k(this,null,function*(){const{dependencies:n=[]}=this.project,{dependencies:i,library:o,components:s,materialPath:u,nodeEnv:l}=this,{libraryExports:a,libraryMap:c,materials:f,materialExports:p,materialMapLibrary:h}=o0(n,u,l==="development");for(const d of a){const m=i[d],b=r[d];if(b)o[d]=b;else if(m)r[d]=o[d]=yield m();else{const _=c[d]||[];for(const y of _)ir(y)&&(yield c0(y,Me.append(y,{v:xr}))),or(y)&&(yield Ji(Me.append(y,{v:xr})));o[d]=r[d]}}if(l==="development"){for(const m of f)yield Ji(Me.append(m,{v:xr}));const d=this.materials||{};for(const m of p){const b=r[h[m]],_=Wd[m];if(_)b&&_.forEach(y=>{s[y]=b[y]});else{const y=d[m]?(yield d[m]()).default:r[m];y&&b&&(y.components||[]).forEach(v=>{s[v.name]=s0(v,b)})}}}})}initRouter(){const{router:r,project:n,options:i,adapter:o}=this;if(!r)return;const s=(n==null?void 0:n.platform)==="uniapp"?"pages":"page",{routeAppendTo:u,pageRouteName:l=s,routeMeta:a}=i,c=u?"":"/",f={path:`${c}${l}/:id`,name:Ue,component:Ar},p={path:c,name:Tr,component:n!=null&&n.homepage?Ar:o.startupComponent||G0,meta:a};if(r.hasRoute(Ue)&&r.removeRoute(Ue),r.hasRoute(Tr)&&r.removeRoute(Tr),i.enableStaticRoute){const h=(n==null?void 0:n.pages)||[];en({name:l,prefix:c,pages:h,component:Ar,loader:this.getRenderComponent.bind(this),homepage:n==null?void 0:n.homepage}).forEach(d=>{u?r.addRoute(u,d):r.addRoute(d)}),n!=null&&n.homepage||(u?r.addRoute(u,p):r.addRoute(p))}else u?(r.addRoute(u,f),r.addRoute(u,p)):(r.addRoute(f),r.addRoute(p))}install(r){const n=r.config.globalProperties.installed||{};for(const[i,o]of Object.entries(this.library))!n[i]&&l0(o)&&(r.use(o),n[i]=!0);this.options.install&&r.use(this.options.install),this.adapter.access&&r.use(this.adapter.access),r.provide(oa,this),r.config.globalProperties.$provider=this,r.config.globalProperties.installed=n,this.mode===et.Design&&(r.config.errorHandler=(i,o,s)=>{const u=o==null?void 0:o.$options.name,l=typeof i=="string"?i:(i==null?void 0:i.message)||(i==null?void 0:i.msg)||"未知错误",a=`[ ${u} ] ${l} ${s}`;console.error("[VTJ Error]:",{err:i,instance:o,info:s},i==null?void 0:i.stack),this.adapter.notify&&this.adapter.notify(a,"组件渲染错误","error")}),this.options.enhance&&r.use(this.options.enhance,this)}getFile(r){const{blocks:n=[]}=this.project||{};return this.getPage(r)||n.find(i=>i.id===r)||null}getPage(r){const{pages:n=[]}=this.project||{},i=(o,s=[])=>{for(const u of s){if(u.id===o)return u;if(u.children&&u.children.length){const l=i(o,u.children);if(l)return l}}};return i(r,n)||null}getMenus(r="page",n=""){var i;return tn(n,r,((i=this.project)==null?void 0:i.pages)||[])}getHomepage(){const{homepage:r}=this.project||{};return r?this.getPage(r):null}getDsl(r){return k(this,null,function*(){const{vtjDir:n=".vtj"}=this.options,i=this.modules[`${n}/files/${r}.json`]||this.modules[`/src/${n}/files/${r}.json`];return i?yield i():this.service.getFile(r,this.project||void 0).catch(()=>null)})}getDslByUrl(r){return k(this,null,function*(){return this.urlDslCaches[r]||(this.adapter.request?this.urlDslCaches[r]=this.adapter.request.send({url:r,method:"get",settings:{validSuccess:!1,originResponse:!0}}).then(i=>i.data).catch(()=>null):null)})}createDslRenderer(r,n={}){const{library:i,components:o,mode:s,apis:u}=this,l=V({mode:s,Vue:i.Vue,components:o,libs:i,apis:u,window},n),a=Qr({getDsl:c=>k(this,null,function*(){return(yield this.getDsl(c))||null}),getDslByUrl:c=>k(this,null,function*(){return(yield this.getDslByUrl(c))||null}),options:l});return Zr(tt(V({},l),{dsl:r,loader:a}))}getRenderComponent(r,n){return k(this,null,function*(){var a;const i=this.getFile(r);if(!i)return te.warn(`Can not find file: ${r}`),null;n&&n(i);const{vtjRawDir:o=".vtj/vue"}=this.options,s=`${o}/${r}.vue`,u=this.modules[s]||this.modules[`/src/pages/${r}.vue`];if(u)return(a=yield u())==null?void 0:a.default;const l=yield this.getDsl(i.id);return l?this.createDslRenderer(l).renderer:(te.warn(`Can not find dsl: ${r}`),null)})}defineUrlSchemaComponent(r,n){return Sn(()=>k(this,null,function*(){const i=yield this.getDslByUrl(r);return i?(i.name=n||i.name,this.createDslRenderer(i).renderer):null}))}definePluginComponent(r){return Sn(()=>k(null,null,function*(){return yield ra(r,window)}))}}function Q0(e){const t=new Z0(e);return{provider:t,onReady:r=>t.ready(r)}}function X0(e={}){const t=ya(oa,null);if(!t)throw new Error("Can not find provider");if(t.nodeEnv==="development"){const{id:r,version:n}=e;r&&n&&k(null,null,function*(){const i=yield t.getDsl(r);(i==null?void 0:i.__VERSION__)!==n&&t.adapter.notify&&t.adapter.notify(`[ ${i==null?void 0:i.name} ] 组件源码版本与运行时版本不一致，请重新发布组件`,"版本不一致","warning")})}return t}function ty(e,t=""){return k(this,null,function*(){const{name:r,urls:n=[]}=e||{},i=n.map(u=>t+u),{css:o,js:s}=a0(i);if(o.length&&Xs(o),s.length)return yield ta(s,r).catch(()=>{})})}const sa={type:"json",validSuccess:!0,originResponse:!1,failMessage:!0,validate:e=>{var t;return((t=e.data)==null?void 0:t.code)===0}},ey=(e,t="/__vtj__/api/:type.json")=>(r,n,i)=>e.send({url:t,method:"post",params:{type:r},query:i,data:{type:r,data:n},settings:sa}),ry=(e,t="/__vtj__/api/uploader.json")=>(r,n)=>k(null,null,function*(){return yield e.send({url:t,method:"post",data:{files:r,projectId:n},settings:tt(V({},sa),{type:"data"})}).then(i=>i&&i[0]?i[0]:null).catch(()=>null)});function ny(e){return vn({settings:{type:"json",validSuccess:!0,originResponse:!1,failMessage:!0,validate:t=>{var r;return((r=t.data)==null?void 0:r.code)===0},showError:t=>{e&&e(t||"未知错误")}}})}class iy{constructor(t=Wr){z(this,"api");z(this,"pluginCaches",{});z(this,"uploader");this.req=t,this.api=ey(t),this.uploader=ry(t)}getExtension(){return k(this,null,function*(){console.log("BaseService.getExtension")})}init(t){return k(this,null,function*(){return console.log("BaseService.init",t),{}})}saveProject(t,r){return k(this,null,function*(){return!!(yield this.api("saveProject",t,{type:r}).catch(()=>!1))})}saveMaterials(t,r){return k(this,null,function*(){return console.log("BaseService.saveMaterials",t,r),!1})}saveFile(t){return k(this,null,function*(){return console.log("BaseService.saveFile",t),!1})}getFile(t){return k(this,null,function*(){return console.log("BaseService.getFile",t),{}})}removeFile(t){return k(this,null,function*(){return console.log("BaseService.removeFile",t),!1})}saveHistory(t){return k(this,null,function*(){return console.log("BaseService.saveHistory",t),!1})}removeHistory(t){return k(this,null,function*(){return console.log("BaseService.removeHistory",t),!1})}getHistory(t){return k(this,null,function*(){return console.log("BaseService.getHistory",t),{}})}getHistoryItem(t,r){return k(this,null,function*(){return console.log("BaseService.getHistoryItem",t,r),{}})}saveHistoryItem(t,r){return k(this,null,function*(){return console.log("BaseService.saveHistoryItem",t,r),!1})}removeHistoryItem(t,r){return k(this,null,function*(){return console.log("BaseService.removeHistoryItem",t,r),!1})}publish(t){return k(this,null,function*(){return!!(yield this.api("publish",t).catch(()=>!1))})}publishFile(t,r){return k(this,null,function*(){return!!(yield this.api("publishFile",{project:t,file:r}).catch(()=>!1))})}genVueContent(t,r){return k(this,null,function*(){return yield this.api("genVueContent",{project:t,dsl:r}).catch(()=>"")})}parseVue(t,r){return k(this,null,function*(){return yield this.api("parseVue",V({project:t},r))})}createRawPage(t){return k(this,null,function*(){return yield this.api("createRawPage",t).catch(()=>"")})}removeRawPage(t){return k(this,null,function*(){return yield this.api("removeRawPage",t).catch(()=>"")})}uploadStaticFile(t,r){return k(this,null,function*(){return yield this.uploader(t,r).catch(()=>null)})}getStaticFiles(t){return k(this,null,function*(){return yield this.api("getStaticFiles",t).catch(()=>[])})}removeStaticFile(t,r){return k(this,null,function*(){return yield this.api("removeStaticFile",{name:t,projectId:r}).catch(()=>"")})}clearStaticFiles(t){return k(this,null,function*(){return yield this.api("clearStaticFiles",t).catch(()=>"")})}getPluginMaterial(t){return k(this,null,function*(){const{urls:r=[]}=t,n=r.filter(o=>n0(o))[0];return n?this.pluginCaches[n]||(this.pluginCaches[n]=Wr.send({url:n,method:"get",settings:{validSuccess:!1,originResponse:!0}}).then(o=>o.data).catch(()=>null)):null})}genSource(t){return k(this,null,function*(){return console.log("BaseService.genSource",t),""})}}new Ws({type:"local",expired:0,prefix:"__VTJ_"});class oy extends iy{constructor(){super(...arguments);z(this,"getFileCaches",{})}getExtension(){return k(this,null,function*(){return yield this.api("getExtension",{}).catch(()=>{})})}init(r){return k(this,null,function*(){return(yield this.api("init",r).catch(()=>null))||{}})}saveProject(r,n){return k(this,null,function*(){return!!(yield this.api("saveProject",r,{type:n}).catch(()=>!1))})}saveMaterials(r,n){return k(this,null,function*(){return!!(yield this.api("saveMaterials",{project:r,materials:jd(n)}).catch(()=>!1))})}saveFile(r){return k(this,null,function*(){return!!(yield this.api("saveFile",r).catch(()=>!1))})}getFile(r){return k(this,null,function*(){return this.getFileCaches[r]||(this.getFileCaches[r]=this.api("getFile",r).catch(()=>null)).finally(()=>{delete this.getFileCaches[r]})})}removeFile(r){return k(this,null,function*(){return!!(yield this.api("removeFile",r).catch(()=>!1))})}saveHistory(r){return k(this,null,function*(){return!!(yield this.api("saveHistory",r).catch(()=>!1))})}removeHistory(r){return k(this,null,function*(){return!!(yield this.api("removeHistory",r).catch(()=>!1))})}getHistory(r){return k(this,null,function*(){return(yield this.api("getHistory",r).catch(()=>null))||{}})}getHistoryItem(r,n){return k(this,null,function*(){return(yield this.api("getHistoryItem",{fId:r,id:n}).catch(()=>null))||{}})}saveHistoryItem(r,n){return k(this,null,function*(){return!!(yield this.api("saveHistoryItem",{fId:r,item:n}).catch(()=>!1))})}removeHistoryItem(r,n){return k(this,null,function*(){return!!(yield this.api("removeHistoryItem",{fId:r,ids:n}).catch(()=>!1))})}}/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/uni 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const sy=["onLaunch","onShow","onHide","onError","onPageNotFound","onUnhandledRejection","onThemeChange","onPageNotFound","onUniNViewMessage","onExit"],Zi="/pages",ay="VTJ",uy="__UNI__1FC118B",cy="VTJ移动跨端项目",ly="1.0.0",fy="100",hy=!1,py={},dy={enable:!1},my="3",gy={name:ay,appid:uy,description:cy,versionName:ly,versionCode:fy,transformPx:hy,"app-plus":{usingComponents:!0,nvueStyleCompiler:"uni-app",compilerVersion:3,splashscreen:{alwaysShowBeforeRender:!0,waiting:!0,autoclose:!0,delay:0},modules:{},distribute:{android:{permissions:['<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>','<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>','<uses-permission android:name="android.permission.VIBRATE"/>','<uses-permission android:name="android.permission.READ_LOGS"/>','<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>','<uses-feature android:name="android.hardware.camera.autofocus"/>','<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>','<uses-permission android:name="android.permission.CAMERA"/>','<uses-permission android:name="android.permission.GET_ACCOUNTS"/>','<uses-permission android:name="android.permission.READ_PHONE_STATE"/>','<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>','<uses-permission android:name="android.permission.WAKE_LOCK"/>','<uses-permission android:name="android.permission.FLASHLIGHT"/>','<uses-feature android:name="android.hardware.camera"/>','<uses-permission android:name="android.permission.WRITE_SETTINGS"/>']},ios:{},sdkConfigs:{},icons:{android:{hdpi:"src/static/logo.png",xhdpi:"src/static/logo.png",xxhdpi:"src/static/logo.png",xxxhdpi:"src/static/logo.png"}}}},quickapp:py,"mp-weixin":{appid:"",setting:{urlCheck:!1},usingComponents:!0},"mp-alipay":{usingComponents:!0},"mp-baidu":{usingComponents:!0},"mp-toutiao":{usingComponents:!0},uniStatistics:dy,vueVersion:my},yy=[],vy={navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},by={autoscan:!0,custom:{"^uni-(.*)":"@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}},wy={pages:yy,globalStyle:vy,easycom:by};function _y(e){let{manifestJson:t={},pagesJson:r={},routes:n=[]}=e;const i=n.map(o=>{const{path:s,style:u,needLogin:l}=o;return{path:s,style:u,needLogin:l}});return tt(V({},e),{manifestJson:Object.assign({},gy,t),pagesJson:Object.assign({},wy,r,{pages:i})})}const rn={navigationBarBackgroundColor:"backgroundColor",navigationBarTextStyle:"titleColor",navigationBarTitleText:"titleText",navigationStyle:"style",titleImage:"titleImage",titlePenetrate:"titlePenetrate",transparentTitle:"transparentTitle"};function aa(e){const t={},r={black:"#000000",white:"#ffffff"};for(const n in e)rn[n]&&(t[rn[n]]=n==="navigationBarTextStyle"?r[e[n]]||r.black:e[n]);return t}function Ey(e){const t={};for(const r in e)rn[r]||(t[r]=e[r]);return t}function Sy(e){var t,r;return((r=(t=e.h5)==null?void 0:t.router)==null?void 0:r.mode)||"hash"}function xy(e){var t,r,n;return{topWindow:!!((t=e.topWindow)!=null&&t.path),leftWindow:!!((r=e.leftWindow)!=null&&r.path),rightWindow:!!((n=e.rightWindow)!=null&&n.path)}}function Ty(e){const{pagesJson:t={}}=e,{globalStyle:r,pages:n=[]}=t;return!!(r!=null&&r.enablePullDownRefresh||n.find(i=>{var o;return!!((o=i.style)!=null&&o.enablePullDownRefresh)}))}function Ry(e){const{pagesJson:t={}}=e,{globalStyle:r,pages:n=[]}=t;let i=!1;return(r==null?void 0:r.navigationStyle)==="custom"?(i=!0,n.find(o=>{var s;return((s=o.style)==null?void 0:s.navigationStyle)==="default"})&&(i=!1)):n.every(o=>{var s;return((s=o.style)==null?void 0:s.navigationStyle)==="custom"})&&(i=!0),i}function Ay(e){var u;const t=Ry(e),r={navigationBar:!1,navigationBarButtons:!1,navigationBarSearchInput:!1,navigationBarTransparent:!1},{pagesJson:n={}}=e,{globalStyle:i,pages:o=[]}=n;if(t)return r;r.navigationBar=!0;const s=(u=i==null?void 0:i.h5)==null?void 0:u.titleNView;return o.find(l=>{var a,c,f;return!!((f=(c=(a=l.style)==null?void 0:a.h5)==null?void 0:c.titleNView)!=null&&f.buttons.length)})&&(r.navigationBarButtons=!0),(s!=null&&s.searchInput||o.find(l=>{var a,c,f;return!!((f=(c=(a=l.style)==null?void 0:a.h5)==null?void 0:c.titleNView)!=null&&f.searchInput)}))&&(r.navigationBarSearchInput=!0),((s==null?void 0:s.type)==="transparent"||o.find(l=>{var a,c,f;return((f=(c=(a=l.style)==null?void 0:a.h5)==null?void 0:c.titleNView)==null?void 0:f.type)==="transparent"}))&&(r.navigationBarTransparent=!0),r}function Oy(e,t=window){var p,h,d,m;const{pagesJson:r={},manifestJson:n={}}=e,{topWindow:i,leftWindow:o,rightWindow:s}=xy(r),{navigationBar:u,navigationBarButtons:l,navigationBarSearchInput:a,navigationBarTransparent:c}=Ay(e),f={__VUE_OPTIONS_API__:!0,__VUE_PROD_DEVTOOLS__:!1,__VUE_PROD_HYDRATION_MISMATCH_DETAILS__:!1,__UNI_FEATURE_WX__:!1,__UNI_FEATURE_WXS__:!1,__UNI_FEATURE_RPX__:!1,__UNI_FEATURE_PROMISE__:!1,__UNI_FEATURE_LONGPRESS__:!1,__UNI_FEATURE_I18N_EN__:!1,__UNI_FEATURE_I18N_ES__:!1,__UNI_FEATURE_I18N_FR__:!1,__UNI_FEATURE_I18N_ZH_HANS__:!0,__UNI_FEATURE_I18N_ZH_HANT__:!1,__UNI_FEATURE_UNI_CLOUD__:!1,__UNI_FEATURE_I18N_LOCALE__:!1,__UNI_FEATURE_NVUE__:!1,__UNI_FEATURE_ROUTER_MODE__:Sy(n),__UNI_FEATURE_PAGES__:!!((p=r.pages)!=null&&p.length),__UNI_FEATURE_TABBAR__:!!((d=(h=r.tabBar)==null?void 0:h.list)!=null&&d.length),__UNI_FEATURE_TABBAR_MIDBUTTON__:!!((m=r.tabBar)!=null&&m.midButton),__UNI_FEATURE_TOPWINDOW__:i,__UNI_FEATURE_LEFTWINDOW__:o,__UNI_FEATURE_RIGHTWINDOW__:s,__UNI_FEATURE_RESPONSIVE__:!1,__UNI_FEATURE_NAVIGATIONBAR__:u,__UNI_FEATURE_PULL_DOWN_REFRESH__:Ty(e),__UNI_FEATURE_NAVIGATIONBAR_BUTTONS__:l,__UNI_FEATURE_NAVIGATIONBAR_SEARCHINPUT__:a,__UNI_FEATURE_NAVIGATIONBAR_TRANSPARENT__:c};Object.entries(f).forEach(([b,_])=>{t[b]=_})}const $y="4.57";function jy(e,t=window){var f,p;const{pagesJson:r={},manifestJson:n={}}=e,{easycom:i={}}=r,{appid:o="",name:s="",versionCode:u="",versionName:l=""}=n,a=r.globalStyle,c=((f=n.h5)==null?void 0:f.router)||{};t.__uniConfig={easycom:i,globalStyle:tt(V({},Ey(a)),{navigationBar:aa(a),isNVue:!1}),compilerVersion:$y,appId:o,appName:s,appVersion:l,appVersionCode:String(u),async:V({loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0},((p=n.h5)==null?void 0:p.async)||{}),debug:!1,networkTimeout:V({request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},n.networkTimeout||{}),sdkConfigs:{},nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:{},router:V({mode:"hash",base:"/",assets:"assets",routerBase:c.base||"/"},c),darkmode:!1,themeConfig:{},tabBar:r.tabBar?V({position:"bottom",color:"#7A7E83",selectedColor:"#3cc51f",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",backgroundColor:"#ffffff",selectedIndex:0,shown:!0},r.tabBar):void 0}}function Dy(e,t=window){const{UniServiceJSBridge:r,UniViewJSBridge:n,getApp:i,uni:o,getCurrentPages:s,upx2px:u,setupPage:l}=e;t.UniServiceJSBridge=r,t.UniViewJSBridge=n,t.getApp=i,t.uni=o,t.wx=o,t.getCurrentPages=s,t.upx2px=u,t.__setupPage=a=>l(a)}function By(e,t,r){const{openBlock:n,createBlock:i,withCtx:o,createVNode:s}=e,{PageComponent:u,setupPage:l,getApp:a}=t;return{mpType:"page",setup(){return k(this,null,function*(){var m,b;const f=((b=(m=a())==null?void 0:m.$route)==null?void 0:b.query)||{},{loader:p,component:h}=r,d=p?yield p(r):h||{};return()=>(n(),i(u,null,{page:o(()=>[s(l(d),Object.assign({},f,{ref:"page"}),null,512)]),_:1}))})}}}function Cy(e,t,r){var h;const{path:n,style:i={},meta:o={},home:s,id:u}=t,l=((h=e.tabBar)==null?void 0:h.list)||[],a=l.findIndex(d=>{var m;return d.pagePath===n||((m=d.pagePath)==null?void 0:m.endsWith(u))}),c=l[a],f=!!c,p=r===0;return V({isTabBar:f,tabBarIndex:a,isQuit:s!=null?s:p,isEntry:s!=null?s:p,navigationBar:V({type:"default"},aa(i)),isNVue:!1,route:c?c.pagePath:n},o)}function Ny(e,t,r,n,i=window){const o=r.map((s,u)=>{const l=Cy(n,s,u),a=By(e,t,s),{path:c}=s;return{path:c,alias:c,meta:l,component:{render(){return e.h(e.Suspense,[e.h(a)])}}}});i.__uniRoutes=o,i.__uniLayout={}}function Iy(e,t,r=window){wn(r,String(e),t)}function Py(e,t){t&&["View","ScrollView","Swiper","MovableArea","MovableView","CoverView","CoverImage","Icon","Text","RichText","Progress","Button","CheckboxGroup","Checkbox","Editor","Form","Input","Label","Picker","PickerView","RadioGroup","Radio","Slider","Switch","Textarea","Navigator","Image","Video","Map","Canvas","WebView","PickerViewColumn","ResizeSensor","SwiperItem"].forEach(r=>{const n=t[r];e.component(r,n)})}function ky(e){const t=_y(e),{Vue:r,App:n,UniH5:i,routes:o=[],pagesJson:s={},manifestJson:u={},window:l,css:a=""}=t;if(!i)return r.createApp(n);const{plugin:c,setupApp:f}=i;Oy(t,l),jy(t,l),Dy(i,l),Ny(r,i,o,s,l),Iy(u.appid||Date.now(),a,l);const p=r.createApp(f(n));return p.use(Py,i),p.use(c),p}function Fy(e,t){const r={};return Object.entries(e).forEach(([n,i])=>{sy.includes(n)&&i&&(r[n]=t(i))}),r}function Qi(e,t){return()=>k(null,null,function*(){const r=yield e.getDsl(t);if(r){const{renderer:n}=e.createDslRenderer(r);return n}return null})}function My(n){return k(this,arguments,function*(e,t=!1,r=Zi){var u,l,a;const i=((u=e.project)==null?void 0:u.pages)||[],o=[];for(const c of i){const f=((l=e.project)==null?void 0:l.homepage)===c.id;o.push({id:c.id,path:`${r}/${c.id}`,loader:Qi(e,c.id),style:V({navigationBarTitleText:c.title},c.style),needLogin:c.needLogin,home:f})}const s=o.find(c=>!!c.home)||o[0];if(s){const c=r===Zi?"/":r;o.unshift(tt(V({},s),{path:c}))}if(t){const c=((a=e.project)==null?void 0:a.blocks)||[];for(const f of c)o.push({id:f.id,path:`${r}/${f.id}`,loader:Qi(e,f.id),style:{navigationStyle:"custom"}})}return o})}function Uy(){var e;return(e=window.uni)!=null&&e.showLoading&&window.uni.showLoading({title:"加载中...",mask:!0}),{close:()=>{var t;(t=window.uni)!=null&&t.hideLoading&&window.uni.hideLoading()}}}function ua(e,t="",r="warning"){return k(this,null,function*(){var n;return(n=window.uni)!=null&&n.showModal?window.uni.showModal({title:t,content:e,showCancel:!1}):Promise.reject(new Error("window.uni.showModal is undefined"))})}function Ly(e=et.Runtime){return e===et.Runtime?Object.assign({}):Object.assign({})}const Vy=g0({loading:Uy,notify:ua}),ca=new oy(ny(ua)),Hy=Ly(),{provider:qy,onReady:zy}=Q0({nodeEnv:"production",mode:et.Runtime,modules:Hy,materialPath:"../",adapter:Vy,service:ca,dependencies:{Vue:()=>Tn(()=>import("./@dcloudio-uni-h5-vue-BEVv3z-t.js").then(e=>e.q),__vite__mapDeps([0,1,2]),import.meta.url),VueRouter:()=>Tn(()=>import("./vue-router-5EIDvL-H.js"),__vite__mapDeps([3,0,1,2]),import.meta.url)}}),Ky=e=>k(null,null,function*(){const t=(yield ca.getExtension().catch(()=>null))||{},r=top||window,n=yield ty(t.enhance,r.location.pathname),{Vue:i,UniH5:o}=window,s=e.project;if(!s)return;const u=s.uniConfig||{},l=Fy(u,d=>ze(d,window,!1,!0)),{manifestJson:a,pagesJson:c,css:f}=u,p=yield My(e,!0),h=ky({Vue:i,App:l,UniH5:o,routes:p,css:f,manifestJson:a,pagesJson:c});h.use(e),n&&h.use(n,e),h.mount(document.body)});zy(()=>Ky(qy));
