<template>
  <ElDialog ref="dialog" :model-value="true" :title="dialogTitle">
    <ElForm ref="form" label-width="100px" :model="model" :rules="rules">
      <ElFormItem label="所属平台" prop="platform">
        <ElRadioGroup v-model="model.platform" :disabled="!!model.id">
          <ElRadioButton label="Web" value="Web"></ElRadioButton>
          <ElRadioButton label="H5" value="H5"></ElRadioButton>
          <ElRadioButton label="UniApp" value="UniApp"></ElRadioButton>
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="应用名称" prop="name">
        <ElInput
          v-model.trim="model.name"
          :maxlength="50"
          :disabled="!!model.id">
          <template v-if="hasPrefix" #prepend>{{ prefix }}</template>
        </ElInput>
      </ElFormItem>
      <ElFormItem label="应用标题" prop="label">
        <ElInput v-model.trim="model.label"></ElInput>
      </ElFormItem>
      <ElFormItem label="应用权限" prop="platform">
        <ElRadioGroup v-model="model.scope">
          <ElRadioButton label="受限" value="protected"></ElRadioButton>
          <ElRadioButton label="公开" value="public"></ElRadioButton>
          <ElRadioButton label="私密" value="private"></ElRadioButton>
        </ElRadioGroup>
        <ElAlert :title="scopeTip" type="warning" :closable="false"></ElAlert>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="onCancel">取消</ElButton>
      <ElButton type="primary" @click="onSubmit">提交</ElButton>
    </template>
  </ElDialog>
</template>
<script lang="ts" setup>
import { useTemplateRef, reactive, computed } from 'vue';
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElRadioGroup,
  ElRadioButton,
  ElAlert,
  type DialogInstance,
  type FormInstance
} from 'element-plus';

import type { LowCodeAppVO } from '@/shared';
import { saveLowCodeApp } from '@/apis';
import { usePrefix } from '@/hooks';

export interface Props {
  data?: LowCodeAppVO | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  submit: [data: LowCodeAppVO];
}>();
const dialog = useTemplateRef<DialogInstance>('dialog');
const form = useTemplateRef<FormInstance>('form');
const { hasPrefix, prefix, skipPrefix, withPrefix } = usePrefix(props.data);
const model = reactive<LowCodeAppVO>({
  label: '',
  platform: 'Web',
  scope: 'protected',
  ...(props.data || {}),
  name: skipPrefix(props.data?.name || '')
});

const rules = {
  platform: [{ required: true, message: '应用名称是必填项' }],
  name: [
    { required: true, message: '应用名称是必填项' },
    {
      message: '名称格式错误,名称为英文驼峰格式',
      pattern: /^[A-Za-z_$][\:\w_-]*$/
    }
  ],
  label: [{ required: true, message: '应用标题是必填项' }]
};

const dialogTitle = computed(() => {
  return props.data?.id ? '编辑应用' : '创建应用';
});

const scopeTip = computed(() => {
  const tips = {
    public: '无需任何身份验证即可访问该应用',
    private: '仅限自己访问',
    protected: '只允许登录的用户都可以访问该应用'
  };
  return tips[model.scope] || tips.protected;
});

const onCancel = () => {
  dialog.value?.handleClose();
};

const onSubmit = async () => {
  const valid = await form.value?.validate().catch(() => false);
  if (valid) {
    const data: LowCodeAppVO = {
      ...model,
      name: withPrefix(model.name)
    };
    const ret = await saveLowCodeApp(data).catch(() => false);
    if (ret) {
      emit('submit', data);
      dialog.value?.handleClose();
    }
  }
};
</script>

<style lang="scss" scoped>
.el-alert {
  margin-top: 10px;
}
</style>
