import { Entity, Column, Index } from 'typeorm';
import { SchemaType, BaseEntity } from '../../shared';

@Entity({
  name: 'schemas',
  comment: '低代码DSL',
  orderBy: {
    createdAt: 'DESC'
  }
})
@Index(['app', 'name', 'type'])
export class Schema extends BaseEntity {
  @Index()
  @Column({ comment: '应用名称' })
  app: string;

  @Index()
  @Column({ comment: '存储Key' })
  name: string;

  @Index()
  @Column('enum', {
    name: 'type',
    enum: SchemaType,
    comment: '数据类型'
  })
  type: SchemaType;

  @Column('json', { comment: '数据内容' })
  content: object;
}
