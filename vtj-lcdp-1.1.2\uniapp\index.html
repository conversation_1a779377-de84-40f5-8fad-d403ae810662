<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="shortcut icon" href="../favicon.ico" type="image/x-icon" />
    <script>
      var coverSupport =
        'CSS' in window &&
        typeof CSS.supports === 'function' &&
        (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
          (coverSupport ? ', viewport-fit=cover' : '') +
          '" />'
      );
    </script>
    <script>
      window.__UNI_FEATURE_UNI_CLOUD__ = false;
      window.__UNI_FEATURE_WX__ = false;
      window.__UNI_FEATURE_WXS__ = false;
      window.__UNI_FEATURE_PAGES__ = false;
    </script>
    <title></title>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/src/platform/uniapp/main.ts"></script>
  </body>
</html>
