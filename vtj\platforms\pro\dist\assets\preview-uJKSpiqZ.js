const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./vue-router-CezURGfh.js","./vue-ipWmmxHk.js"])))=>i.map(i=>d[i]);
var R=Object.defineProperty;var w=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable;var h=(o,e,t)=>e in o?R(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t,d=(o,e)=>{for(var t in e||(e={}))x.call(e,t)&&h(o,t,e[t]);if(w)for(var t of w(e))P.call(e,t)&&h(o,t,e[t]);return o};var f=(o,e,t)=>new Promise((c,s)=>{var _=a=>{try{i(t.next(a))}catch(m){s(m)}},n=a=>{try{i(t.throw(a))}catch(m){s(m)}},i=a=>a.done?c(a.value):Promise.resolve(a.value).then(_,n);i((t=t.apply(o,e)).next())});import{_ as A}from"./monaco-editor-B8sWZqMY.js";import{useRoute as V}from"./vue-router-CezURGfh.js";import"./element-plus-COProxbp.js";import"./vxe-Bet8YtVU.js";import{b as g,j as S,v as T,S as k,w as B,$ as D,A as I,m as O}from"./Editor-Dgw5r9tb-C0tzAbRO.js";import{a as b,n as E,l as j,E as L}from"./utils-Bopp5ewb.js";import{s as $}from"./@vueuse-WO_0ftym.js";import{d as z,az as y,e as H,K,O as N,P as q,o as F,h as G}from"./vue-ipWmmxHk.js";import"./lodash-es-BL-d_OSa.js";import"./@element-plus-icons-vue-0BR09xN9.js";import"./shared-Bnc4f-Fv.js";import"./dayjs-DO-COJPZ.js";import"./html2canvas-CIV5arX1.js";import"./mockjs-DG3OW7C0.js";import"./marked-BQZxLJfc.js";const me=z({__name:"preview",setup(o){return f(this,null,function*(){let e,t;const c=new g(S(E)),s=([e,t]=y(()=>c.getExtension().catch(()=>null)),e=yield e,t(),e||{}),_=T({loading:j,notify:E,useTitle:$,alert:b,access:s==null?void 0:s.access}),n=s?([e,t]=y(()=>new L(s).load()),e=yield e,t(),e):{},{__BASE_PATH__:i="/"}=s||{},{provider:a,onReady:m}=k({mode:B.Runtime,service:c,materialPath:i,adapter:d(d({},_),n.adapter),dependencies:{Vue:()=>A(()=>import("./vue-ipWmmxHk.js").then(p=>p.aA),[],import.meta.url),VueRouter:()=>A(()=>import("./vue-router-CezURGfh.js"),__vite__mapDeps([0,1]),import.meta.url)}}),v=V(),u=H(),l=G();return m(()=>f(null,null,function*(){const p=yield D(s.enhance),r=l==null?void 0:l.appContext.app;r&&(n.install&&n.install(r),p&&r.use(p,a),r.use(I),r.use(a),u.value=yield a.getRenderComponent(v.params.id.toString(),C=>{O(r,v,C)}))})),(p,r)=>u.value?(F(),K(q(u.value),{key:0})):N("",!0)})}});export{me as default};
