import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import type { Request } from 'express';
import axios from 'axios';
import { Gitte, OAuthType } from '../shared';
import { UsersService } from '../users/users.service';
import { UserDto } from '../users/dto/user.dto';

@Injectable()
export class OAuthService {
  constructor(
    private config: ConfigService,
    private users: UsersService
  ) {}

  getGiteeRediectUri(req: Request) {
    const host = req.headers.host;
    const protocol = req.url.startsWith('https') ? 'https' : 'http';
    return `${protocol}://${host}/api/oauth/gitee/callback`;
  }
  async getGiteeAccessToken(code: string, rediectUri: string) {
    const { clientSecret, clientId } = this.config.get<Gitte>('gitee');
    const api = 'https://gitee.com/oauth/token';
    try {
      const res = await axios.post(api, {
        code,
        grant_type: 'authorization_code',
        client_id: clientId,
        client_secret: clientSecret,
        redirect_uri: rediectUri
      });

      return res.data;
    } catch (e) {
      throw new BadRequestException(e);
    }
  }
  async saveGiteeUser(accessToken: string) {
    const api = `https://gitee.com/api/v5/user?access_token=${accessToken}`;
    try {
      const res = await axios.get(api);
      const { id, login, avatar_url, email } = res?.data || {};
      const user = await this.users.getOAuthUser(OAuthType.Gitee, id);
      if (!user) {
        const dto = new UserDto();
        dto.name = login;
        dto.avatar = avatar_url;
        dto.email = email === '未公开邮箱' ? '' : email;
        dto.oauthType = OAuthType.Gitee;
        dto.oauthId = id;
        return await this.users.save(dto);
      } else {
        if (user.freeze) {
          throw new BadRequestException('用户已被冻结');
        }
      }
      return user;
    } catch (e) {
      throw new BadRequestException(e);
    }
  }

  async giteeStarred(accessToken: string) {
    const api = 'https://gitee.com/api/v5/user/starred/newgateway/vtj';
    try {
      await axios.put(api, { access_token: accessToken });
    } catch (e) {
      // console.log('giteeStarred error', e);
    }
  }

  async giteeIsStarred(accessToken: string) {
    const api = 'https://gitee.com/api/v5/user/starred/newgateway/vtj';
    try {
      return await axios
        .get(api, { params: { access_token: accessToken } })
        .then(() => true)
        .catch(() => false);
    } catch (e) {
      return false;
    }
  }

  async giteeLogin(code: string, redirect: string) {
    const res = await this.getGiteeAccessToken(code, redirect);
    const user = await this.saveGiteeUser(res.access_token);
    const starred = await this.giteeIsStarred(res.access_token);
    (user as any).giteeStarred = starred;
    (user as any).giteeAccessToken = res.access_token;
    return this.users.login(user);
  }
}
