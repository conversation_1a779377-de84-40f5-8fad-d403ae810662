{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/csp/csp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/csp/csp.ts\nvar conf = {\n  brackets: [],\n  autoClosingPairs: [],\n  surroundingPairs: []\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  // defaultToken: 'invalid',\n  keywords: [],\n  typeKeywords: [],\n  tokenPostfix: \".csp\",\n  operators: [],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  tokenizer: {\n    root: [\n      [/child-src/, \"string.quote\"],\n      [/connect-src/, \"string.quote\"],\n      [/default-src/, \"string.quote\"],\n      [/font-src/, \"string.quote\"],\n      [/frame-src/, \"string.quote\"],\n      [/img-src/, \"string.quote\"],\n      [/manifest-src/, \"string.quote\"],\n      [/media-src/, \"string.quote\"],\n      [/object-src/, \"string.quote\"],\n      [/script-src/, \"string.quote\"],\n      [/style-src/, \"string.quote\"],\n      [/worker-src/, \"string.quote\"],\n      [/base-uri/, \"string.quote\"],\n      [/plugin-types/, \"string.quote\"],\n      [/sandbox/, \"string.quote\"],\n      [/disown-opener/, \"string.quote\"],\n      [/form-action/, \"string.quote\"],\n      [/frame-ancestors/, \"string.quote\"],\n      [/report-uri/, \"string.quote\"],\n      [/report-to/, \"string.quote\"],\n      [/upgrade-insecure-requests/, \"string.quote\"],\n      [/block-all-mixed-content/, \"string.quote\"],\n      [/require-sri-for/, \"string.quote\"],\n      [/reflected-xss/, \"string.quote\"],\n      [/referrer/, \"string.quote\"],\n      [/policy-uri/, \"string.quote\"],\n      [/'self'/, \"string.quote\"],\n      [/'unsafe-inline'/, \"string.quote\"],\n      [/'unsafe-eval'/, \"string.quote\"],\n      [/'strict-dynamic'/, \"string.quote\"],\n      [/'unsafe-hashed-attributes'/, \"string.quote\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU,CAAC;AAAA,EACX,kBAAkB,CAAC;AAAA,EACnB,kBAAkB,CAAC;AACrB;AACA,IAAI,WAAW;AAAA;AAAA;AAAA,EAGb,UAAU,CAAC;AAAA,EACX,cAAc,CAAC;AAAA,EACf,cAAc;AAAA,EACd,WAAW,CAAC;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,eAAe,cAAc;AAAA,MAC9B,CAAC,eAAe,cAAc;AAAA,MAC9B,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,WAAW,cAAc;AAAA,MAC1B,CAAC,gBAAgB,cAAc;AAAA,MAC/B,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,cAAc,cAAc;AAAA,MAC7B,CAAC,cAAc,cAAc;AAAA,MAC7B,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,cAAc,cAAc;AAAA,MAC7B,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,gBAAgB,cAAc;AAAA,MAC/B,CAAC,WAAW,cAAc;AAAA,MAC1B,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,eAAe,cAAc;AAAA,MAC9B,CAAC,mBAAmB,cAAc;AAAA,MAClC,CAAC,cAAc,cAAc;AAAA,MAC7B,CAAC,aAAa,cAAc;AAAA,MAC5B,CAAC,6BAA6B,cAAc;AAAA,MAC5C,CAAC,2BAA2B,cAAc;AAAA,MAC1C,CAAC,mBAAmB,cAAc;AAAA,MAClC,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,cAAc,cAAc;AAAA,MAC7B,CAAC,UAAU,cAAc;AAAA,MACzB,CAAC,mBAAmB,cAAc;AAAA,MAClC,CAAC,iBAAiB,cAAc;AAAA,MAChC,CAAC,oBAAoB,cAAc;AAAA,MACnC,CAAC,8BAA8B,cAAc;AAAA,IAC/C;AAAA,EACF;AACF;", "names": []}