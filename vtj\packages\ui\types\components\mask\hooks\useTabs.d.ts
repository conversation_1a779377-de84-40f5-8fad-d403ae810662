import { ComputedRef, Ref } from 'vue';
import { RouteLocationRaw } from 'vue-router';
import { MaskProps, MaskEmitsFn, MaskTab } from '../types';
import { MenuDataItem } from '../../';
export declare function useTabs(_props: MaskProps, _emit: MaskEmitsFn, menus: ComputedRef<MenuDataItem[]>, active: Ref<MenuDataItem | null>, home: ComputedRef<MaskTab>): {
    tabRef: Ref<any, any>;
    tabs: Ref<MaskTab[], MaskTab[]>;
    showTabs: ComputedRef<MaskTab[]>;
    currentTab: ComputedRef<MaskTab | undefined>;
    changeTab: (tab: MaskTab) => void;
    removeTab: (tab: MaskTab, silent?: boolean) => Promise<MaskTab | undefined>;
    updateTab: (tab: MaskTab) => void;
    addTab: (tab: MaskTab) => void;
    home: ComputedRef<MaskTab>;
    tabValue: Ref<string, string>;
    isCurrentTab: (tab: MaskTab) => boolean;
    activeHome: () => void;
    activeTab: (tab: MaskTab) => void;
    dropdownTabs: ComputedRef<MaskTab[]>;
    removeAllTabs: () => Promise<MaskTab[] | undefined>;
    removeOtherTabs: () => Promise<MaskTab[] | undefined>;
    moveToShow: (tab: MaskTab) => void;
    closeCurrnetTab: (to?: RouteLocationRaw) => Promise<void>;
};
