import {
  useRoute
} from "./chunk-6UAT7FWP.js";
import {
  Q,
  S,
  ee,
  te
} from "./chunk-RK5LLB6U.js";
import {
  Co,
  Cs,
  Lo,
  Po,
  So,
  jo,
  vo,
  vs,
  xo,
  xs
} from "./chunk-VXBA6WZ5.js";
import {
  $d,
  $l,
  F0,
  G,
  H0,
  Pe,
  Qt,
  al,
  cr,
  fd,
  gn,
  id,
  ld,
  qc,
  sd,
  ud,
  wd
} from "./chunk-6QPGK3DF.js";
import {
  defineAsyncComponent,
  defineComponent,
  h,
  inject,
  ref,
  vue_runtime_esm_bundler_exports,
  watchEffect
} from "./chunk-K7IPTB2M.js";
import {
  __export,
  __publicField
} from "./chunk-LK32TJAX.js";

// node_modules/@vtj/renderer/dist/index.mjs
var dist_exports = {};
__export(dist_exports, {
  ACCESS: () => us,
  ACCESS_KEY: () => Ae,
  Access: () => Ie,
  BUILD_IN_TAGS: () => ot,
  BUILT_IN_DIRECTIVES: () => rt,
  BaseService: () => ae,
  CONTEXT_HOST: () => ve,
  Context: () => Rt,
  ContextMode: () => w,
  DATA_TYPES: () => nt,
  HOMEPAGE_ROUTE_NAME: () => N,
  HTML_TAGS: () => it,
  JSCodeToString: () => hs,
  LIFE_CYCLES_LIST: () => cs,
  LocalService: () => js,
  MemoryService: () => rs,
  NodeEnv: () => Zt,
  PAGE_ROUTE_NAME: () => M,
  Provider: () => es,
  REMOTE: () => ls,
  StorageService: () => $s,
  VTJ_RENDERER_VERSION: () => k,
  adoptedStyleSheets: () => Re,
  clearLoaderCache: () => vs2,
  compileScopedCSS: () => ft,
  createAccess: () => gs,
  createAdapter: () => ms,
  createAssetScripts: () => ps,
  createAssetsCss: () => fs,
  createDataSources: () => Mt,
  createLoader: () => se,
  createMemoryService: () => Es,
  createMenus: () => oe,
  createMetaApi: () => St,
  createMock: () => jt,
  createProvider: () => _s,
  createRenderer: () => te2,
  createSchemaApi: () => wt,
  createSchemaApis: () => $t,
  createServiceRequest: () => Ss,
  defaultLoader: () => Dt,
  fillBasePath: () => x,
  getMock: () => O,
  getModifiers: () => Te,
  getPlugin: () => Me,
  getRawComponent: () => ut,
  isBuiltInTag: () => mt,
  isCSSUrl: () => q,
  isJSCode: () => vt,
  isJSExpression: () => $,
  isJSFunction: () => D,
  isJSON: () => at,
  isJSUrl: () => K,
  isNativeTag: () => gt,
  isVuePlugin: () => ht,
  loadCss: () => dt,
  loadCssUrl: () => Pe2,
  loadEnhance: () => ws,
  loadScriptUrl: () => Ce,
  menusFilter: () => De,
  mockApi: () => bt,
  mockApis: () => Et,
  mockCleanup: () => Fe,
  nodeRender: () => F,
  parseDeps: () => lt,
  parseExpression: () => T,
  parseFunction: () => ee2,
  parseUrls: () => pt,
  providerKey: () => He,
  removeProdFlag: () => ct,
  setupPageSetting: () => ds,
  toString: () => be,
  useAccess: () => _t,
  useMask: () => ys,
  useProvider: () => xe
});
var k = "0.12.47";
var w = ((r) => (r.Runtime = "Runtime", r.Design = "Design", r.Raw = "Raw", r.VNode = "VNode", r))(w || {});
var ve = [
  "$el",
  "$emit",
  "$nextTick",
  "$parent",
  "$root",
  "$attrs",
  "$slots",
  "$watch",
  "$props",
  "$options",
  "$forceUpdate"
];
var cs = [
  "beforeCreate",
  "created",
  "beforeMount",
  "mounted",
  "beforeUpdate",
  "updated",
  "beforeUnmount",
  "unmounted",
  "errorCaptured",
  "renderTracked",
  "renderTriggered",
  "activated",
  "deactivated"
];
var rt = [
  "vIf",
  "vElseIf",
  "vElse",
  "vShow",
  "vModel",
  "vFor",
  "vBind",
  "vHtml"
];
var nt = {
  String,
  Number,
  Boolean,
  Array,
  Object,
  Function,
  Date
};
var M = "VtjPage";
var N = "VtjHomepage";
var it = "html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot,svg".split(
  ","
);
var ot = "component,slot".split(",");
var ls = [
  "h",
  "t",
  "t",
  "p",
  "s",
  ":",
  "/",
  "/",
  "l",
  "c",
  "d",
  "p",
  ".",
  "v",
  "t",
  "j",
  ".",
  "p",
  "r",
  "o"
].join("");
var us = {
  auth: [
    "h",
    "t",
    "t",
    "p",
    "s",
    ":",
    "/",
    "/",
    "l",
    "c",
    "d",
    "p",
    ".",
    "v",
    "t",
    "j",
    ".",
    "p",
    "r",
    "o",
    "/",
    "a",
    "u",
    "t",
    "h",
    ".",
    "h",
    "t",
    "m",
    "l"
  ].join(""),
  storageKey: [
    "R",
    "R",
    "O",
    "_",
    "I",
    "D",
    "E",
    "_",
    "A",
    "C",
    "C",
    "E",
    "S",
    "S",
    "_",
    "S",
    "T",
    "O",
    "R",
    "A",
    "G",
    "E",
    "_",
    "_"
  ].join(""),
  privateKey: "MIIBOgIBAAJBAKoIzmn1FYQ1YOhOBw9EhABxZ+PySAIaydI+zdhoKflrdgJ4A5E4/5gbQmRpk09hPWG8nvX7h+l/QLU8kXxAIBECAwEAAQJAAlgpxQY6sByLsXqzJcthC8LSGsLf2JEJkHwlnpwFqlEV8UCkoINpuZ2Wzl+aftURu5rIfAzRCQBvHmeOTW9/zQIhAO5ufWDmnSLyfAAsNo5JRNpVuLFCFodR8Xm+ulDlosR/AiEAtpAltyP9wmCABKG/v/hrtTr3mcvFNGCjoGa9bUAok28CIHbrVs9w1ijrBlvTsXYwJw46uP539uKRRT4ymZzlm9QjAiB+1KH/G9f9pEEL9rtaSOG7JF5D0JcOjlze4MGVFs+ZrQIhALKOUFBNr2zEsyJIjw2PlvEucdlG77UniszjXTROHSPd"
};
function x(r, e) {
  return r.map((t) => H0(t) || t.startsWith("/") ? t : `${e}${t}`);
}
function q(r) {
  return /\.css$/.test(r);
}
function K(r) {
  return /\.js$/.test(r);
}
function at(r) {
  return /\.json$/.test(r);
}
function ps(r) {
  return r.map(
    (e) => `<script src="${Co.append(e, { v: k })}"><\/script>`
  ).join("");
}
function fs(r = []) {
  return r.map(
    (e) => `<link rel="stylesheet" href="${Co.append(e, { v: k })}" />`
  ).join("");
}
function ct(r, e = false) {
  return e && r.endsWith(".prod.js") ? r.replace(".prod.js", ".js") : r;
}
function lt(r, e, t = false) {
  const s = r.filter((h2) => !!h2.enabled), n = [], i = [], a = [], o = [], p = {}, c = {}, u = [], l = {};
  return s.forEach(
    ({ urls: h2, assetsUrl: f, library: d, assetsLibrary: g, localeLibrary: y }) => {
      h2 == null ? void 0 : h2.forEach((m) => {
        K(m) && n.push(ct(m, t)), q(m) && i.push(m);
      }), d && (o.push(d), p[d] = x(h2 || [], e), y && (c[d] = y)), f && a.push(f), g && u.push(g), d && g && (l[g] = d);
    }
  ), {
    scripts: x(n, e),
    css: x(i, e),
    materials: x(a, e),
    libraryExports: o,
    materialExports: ud(u),
    materialMapLibrary: l,
    libraryMap: p,
    libraryLocaleMap: c
  };
}
function ut(r, e) {
  var _a;
  const { name: t, parent: s, alias: n } = r;
  return s ? (_a = e[s]) == null ? void 0 : _a[n || t] : e[n || t];
}
function pt(r = []) {
  const e = r.filter((s) => q(s)), t = r.filter((s) => K(s));
  return {
    css: e,
    js: t
  };
}
function ft(r, e) {
  const t = (c) => {
    const u = [];
    let l = 0;
    for (; l < c.length; ) {
      if (/\s/.test(c[l])) {
        l++;
        continue;
      }
      if (c.substring(l, l + 2) === "/*") {
        const m = c.indexOf("*/", l + 2);
        if (m !== -1) {
          l = m + 2;
          continue;
        }
      }
      if (c[l] === "@") {
        const m = l;
        for (; l < c.length && c[l] !== "{" && c[l] !== ";"; )
          l++;
        const E = c.substring(m, l).trim(), v = E.includes("@keyframes");
        if (c[l] === ";")
          u.push({
            type: "simple-at-rule",
            content: c.substring(m, l + 1)
          }), l++;
        else if (c[l] === "{") {
          const H = l + 1;
          let P = 1;
          for (l++; l < c.length && P > 0; )
            c[l] === "{" ? P++ : c[l] === "}" && P--, l++;
          const z = c.substring(m, l), j = c.substring(H, l - 1);
          u.push({
            type: v ? "keyframes" : "at-rule",
            rule: E,
            content: z,
            inner: j
          });
        }
        continue;
      }
      const h2 = l;
      for (; l < c.length && c[l] !== "{"; )
        l++;
      if (l >= c.length) break;
      const f = c.substring(h2, l).trim();
      if (!f) {
        l++;
        continue;
      }
      const d = l + 1;
      let g = 1;
      for (l++; l < c.length && g > 0; )
        c[l] === "{" ? g++ : c[l] === "}" && g--, l++;
      const y = c.substring(d, l - 1);
      u.push({
        type: "rule",
        selector: f,
        content: y.trim()
      });
    }
    return u;
  }, s = (c) => {
    const u = c.trim();
    return /^(from|to|\d+(\.\d+)?%)$/.test(u);
  }, n = (c) => c.replace(/::v-deep\(/g, ":deep(").replace(/::v-deep\s+/g, ":deep(").replace(/\/deep\//g, " ").replace(/>>>/g, " ").replace(/(.*?):deep\(([^)]+)\)/g, (u, l, h2) => {
    const f = l.trim(), d = h2.trim();
    return f ? `${f}[${e}] ${d}` : d;
  }), i = (c) => {
    const u = c.trim();
    if (!u || u.includes(`[${e}]`) || /^(:root|:host|html|body)(\s|$|:|\.|\#|\[)/.test(u) || s(u))
      return u;
    const l = u.match(/^(.+?)((?:::?[\w-]+(?:\([^)]*\))?)*)\s*$/);
    if (l) {
      const [, h2, f = ""] = l, d = h2.trim();
      return d ? `${d}[${e}]${f}` : u;
    }
    return `${u}[${e}]`;
  }, a = (c) => n(c).split(",").map((l) => i(l)).filter((l) => l.trim()).join(", "), o = (c) => c.map((u) => {
    switch (u.type) {
      case "simple-at-rule":
        return u.content;
      case "keyframes":
        return u.content;
      case "at-rule":
        try {
          const h2 = t(u.inner), f = o(h2);
          return `${u.rule} { ${f} }`;
        } catch {
          return u.content;
        }
      case "rule":
        if (!u.selector || !u.content)
          return "";
        const l = a(u.selector);
        return l.trim() ? `${l} { ${u.content} }` : "";
      default:
        return "";
    }
  }).filter((u) => u.trim()).join(" "), p = (c) => c.replace(/\s*{\s*/g, " { ").replace(/\s*}\s*/g, " } ").replace(/\s*;\s*/g, "; ").replace(/\s*,\s*/g, ", ").replace(/\s+/g, " ").replace(/^\s+|\s+$/g, "").replace(/\s*}\s*}/g, " } }").trim();
  try {
    const c = r.replace(/\/\*(?!\s*!)[\s\S]*?\*\//g, "").replace(/^\s+|\s+$/gm, "").replace(/\n\s*\n/g, `
`), u = t(c), l = o(u);
    return p(l);
  } catch (c) {
    return console.error("CSS scoping failed:", c), console.error("Input CSS:", r), r.replace(/\/\*[\s\S]*?\*\//g, "").replace(
      /(@keyframes\s+[^{]+\s*{[^{}]*(?:{[^}]*}[^{}]*)*})/g,
      (u) => u
    ).replace(/([^{}@]+)(?=\s*{)/g, (u) => {
      const l = u.trim();
      return !l || l.startsWith("@") || l.includes(`[${e}]`) || s(l) ? u : `${l}[${e}]`;
    });
  }
}
function be(r) {
  return al(r) ? r : JSON.stringify(r);
}
function Re(r, e, t, s = false) {
  const n = r.CSSStyleSheet, i = s ? `data-v-${e}` : e, a = s ? ft(t, i) : t;
  if (n.prototype.replaceSync) {
    const o = new n();
    o.id = e, o.replaceSync(a);
    const p = r.document, c = p.adoptedStyleSheets, u = Array.from(c).filter(
      (l) => l.id !== e
    );
    p.adoptedStyleSheets = [...u, o];
  } else {
    const o = r.document;
    let p = o.getElementById(e);
    p ? p.innerHTML = a : (p = o.createElement("style"), p.id = e, p.innerHTML = a, o.head.appendChild(p));
  }
}
async function dt(r, e) {
  const t = await window.fetch(e).then((s) => s.text()).catch(() => "");
  t && Re(window, r, t);
}
function Pe2(r, e = window) {
  const t = e.document, s = e.document.head;
  for (const n of r)
    if (!t.getElementById(n)) {
      const a = t.createElement("link");
      a.rel = "stylesheet", a.id = n, a.href = n, s.appendChild(a);
    }
}
async function Ce(r, e, t = window) {
  const s = t.document, n = t.document.head;
  let i = t[e];
  return i ? i.default || i : new Promise((a, o) => {
    for (const p of r) {
      const c = s.createElement("script");
      c.src = p, c.onload = () => {
        i = t[e], i ? a(i.default || i) : o(null);
      }, c.onerror = (u) => {
        o(u);
      }, n.appendChild(c);
    }
  });
}
function ht(r) {
  return Qt(r) || Qt(r == null ? void 0 : r.install);
}
function mt(r) {
  return ot.includes(r);
}
function gt(r) {
  return it.includes(r);
}
function O(r = window) {
  const e = window == null ? void 0 : window.Mock;
  if (e) return e;
  const t = r == null ? void 0 : r.Mock;
  if (t && window)
    return window.Mock = t, t;
}
function ds(r, e, t) {
  Object.assign(e.meta, t.meta);
  const s = r == null ? void 0 : r._container;
  (t == null ? void 0 : t.type) === "page" && s.classList.add("is-page"), (t == null ? void 0 : t.pure) && s.classList.add("is-pure");
}
function T(r, e, t = false, s = false) {
  try {
    const n = ['"use strict";', "var __self = arguments[0];"];
    n.push("return ");
    let i = (r.value || "").trim();
    i = i.replace(/this(\W|$)/g, (o, p) => `__self${p}`), i = n.join(`
`) + i;
    const a = `with(${t ? "{}" : "$scope || {}"}) { ${i} }`;
    return new Function("$scope", a)(e);
  } catch (n) {
    if (jo.error("parseExpression.error", n, r, (e == null ? void 0 : e.__self) ?? e), s)
      throw n;
  }
}
function ee2(r, e, t = false, s = false) {
  const n = T(r, e, t, s);
  if (typeof n != "function" && (jo.error(
    "parseFunction.error",
    "not a function",
    r,
    (e == null ? void 0 : e.__self) ?? e
  ), s))
    throw new Error(`"${r.value}" not a function`);
  return n;
}
function $(r) {
  return r && r.type === "JSExpression";
}
function D(r) {
  return typeof r == "object" && r && r.type === "JSFunction";
}
function vt(r) {
  return $(r) || D(r);
}
function hs(r) {
  return vt(r) ? r.value : JSON.stringify(r);
}
var yt = {
  session: false,
  authKey: "Authorization",
  storageKey: "ACCESS_STORAGE",
  storagePrefix: "__VTJ_",
  unauthorized: void 0,
  auth: "/#/login",
  redirectParam: "r",
  unauthorizedCode: 401,
  unauthorizedMessage: "登录已经失效，请重新登录！",
  noPermissionMessage: "无权限访问该页面",
  appName: "",
  statusKey: "code"
};
var Ae = Symbol("access");
var Ie = class {
  constructor(e) {
    __publicField(this, "options");
    __publicField(this, "data", null);
    __publicField(this, "mode", w.Raw);
    __publicField(this, "interceptResponse", true);
    this.options = Object.assign({}, yt, e), this.loadData();
  }
  enableIntercept() {
    this.interceptResponse = true;
  }
  disableIntercept() {
    this.interceptResponse = false;
  }
  connect(e) {
    const { mode: t, router: s, request: n } = e;
    this.mode = t, s && this.mode === w.Raw && this.setGuard(s), n && this.setRequest(n);
  }
  login(e) {
    const { storageKey: t, storagePrefix: s, session: n, authKey: i } = this.options;
    this.setData(e), this.data && (xo.save(t, e, {
      type: "local",
      prefix: s
    }), n && Lo.set(i, this.data.token));
  }
  clear() {
    const { storageKey: e, storagePrefix: t, session: s, authKey: n } = this.options;
    this.data = null, xo.remove(e, {
      type: "local",
      prefix: t
    }), s && Lo.remove(n);
  }
  logout() {
    this.clear(), this.toLogin();
  }
  getData() {
    return this.data ? this.data : (this.loadData(), this.data);
  }
  getToken() {
    var _a;
    return this.data || this.loadData(), (_a = this.data) == null ? void 0 : _a.token;
  }
  can(e) {
    const { appName: t } = this.options, { permissions: s = {} } = this.data || {};
    return typeof e == "function" ? e(s) : fd(e).every((i) => s[i] || s[t + "." + i]);
  }
  some(e) {
    const { appName: t } = this.options, { permissions: s = {} } = this.data || {};
    return fd(e).some((i) => s[i] || s[t + "." + i]);
  }
  install(e) {
    e.config.globalProperties.$access = this, e.provide(Ae, this);
  }
  isAuthPath(e) {
    const { auth: t, isAuth: s } = this.options;
    if (s)
      return s(e);
    if (e.path && typeof t == "string") {
      const n = t.split("#")[1] || t;
      return e.path === n;
    }
    return false;
  }
  toLogin() {
    const { auth: e, redirectParam: t } = this.options;
    if (!e) return;
    const s = t ? `?${t}=${encodeURIComponent(location.href)}` : "";
    typeof e == "function" ? e(s) : location.href = t ? `${e}${s}` : e;
  }
  setData(e) {
    const { privateKey: t } = this.options;
    if (Array.isArray(e) && t) {
      const s = e.map((n) => F0(n, t));
      try {
        this.data = JSON.parse(s.join(""));
      } catch (n) {
        console.warn(n);
      }
      return;
    }
    if (typeof e == "string")
      try {
        const s = t ? F0(e, t) : e;
        s ? this.data = JSON.parse(s) : console.warn("RSA解密失败或登录信息缺失");
      } catch (s) {
        console.warn(s);
      }
    else
      this.data = e;
  }
  loadData() {
    const { storageKey: e, storagePrefix: t } = this.options, s = xo.get(e, {
      type: "local",
      prefix: t
    });
    this.setData(s || null);
  }
  isLogined() {
    const { session: e, authKey: t } = this.options;
    return e && t ? !!Lo.get(t) : !!this.getToken();
  }
  hasRoutePermission(e) {
    if (e.name === M) {
      const t = e.params.id;
      return t && this.can(t);
    }
    return true;
  }
  setGuard(e) {
    e.beforeEach((t, s, n) => this.guard(t, n));
  }
  async guard(e, t) {
    if (this.isWhiteList(e) || this.isAuthPath(e))
      return t();
    if (this.isLogined()) {
      if (this.hasRoutePermission(e))
        return t();
      {
        const { noPermissionMessage: s = "无权限访问", unauthorized: n = false } = this.options;
        return await this.showTip(s), Qt(n) ? (n(), t(false)) : al(n) ? t(n) : t(false);
      }
    }
    t(false), this.toLogin();
  }
  isWhiteList(e) {
    const { whiteList: t } = this.options;
    return t ? Array.isArray(t) ? t.some((s) => e.fullPath.startsWith(s)) : t(e) : false;
  }
  isUnauthorized(e) {
    var _a;
    const { unauthorizedCode: t = 401, statusKey: s = "code" } = this.options;
    return e.status === t || ((_a = e.data) == null ? void 0 : _a[s]) === t;
  }
  async showUnauthorizedAlert(e) {
    const { unauthorizedMessage: t = "登录已失效" } = this.options;
    this.isUnauthorized(e) && (await this.showTip(t), this.toLogin());
  }
  async showTip(e) {
    var _a;
    const { alert: t } = this.options;
    return t ? (await id(150), await ((_a = t(e, {
      title: "提示",
      type: "warning"
    })) == null ? void 0 : _a.catch(() => false))) : false;
  }
  setRequest(e) {
    e.useRequest((t) => {
      var _a, _b;
      return ((_a = this.data) == null ? void 0 : _a.token) && (t.headers[this.options.authKey] = (_b = this.data) == null ? void 0 : _b.token), t;
    }), e.useResponse(
      async (t) => (this.interceptResponse && await this.showUnauthorizedAlert(t), t),
      async (t) => {
        if (!this.interceptResponse) return Promise.reject(t);
        const s = t.response || t || {};
        return await this.showUnauthorizedAlert(s), Promise.reject(t);
      }
    );
  }
};
function _t() {
  return inject(Ae, null);
}
function ms(r = {}) {
  const {
    notify: e,
    loading: t,
    settings: s = {},
    Startup: n,
    access: i,
    useTitle: a,
    alert: o
  } = r;
  let p = null;
  return {
    request: xs({
      settings: {
        type: "form",
        validSuccess: true,
        originResponse: false,
        loading: true,
        validate: (u) => {
          var _a, _b;
          return ((_a = u.data) == null ? void 0 : _a.code) === 0 || !!((_b = u.data) == null ? void 0 : _b.success);
        },
        failMessage: true,
        showError: (u) => {
          e && e(u || "未知错误");
        },
        showLoading: () => {
          p && p.close(), t && (p = t());
        },
        hideLoading: () => {
          p && (p.close(), p = null);
        },
        ...s
      }
    }),
    jsonp: Po,
    notify: e,
    loading: t,
    useTitle: a,
    startupComponent: n,
    access: i ? new Ie({ alert: o, ...i }) : void 0
  };
}
function gs(r = {}) {
  return new Ie(r);
}
function wt(r, e) {
  const { jsonp: t, request: s } = e;
  if (r.method === "jsonp")
    return (n = {}) => t(r.url, {
      ...r.jsonpOptions,
      query: n
    });
  {
    const n = r.headers ? T(r.headers, {}, true) : void 0, i = {
      url: r.url,
      method: r.method,
      settings: {
        ...r.settings,
        headers: n
      }
    };
    return (a, o) => (delete i.data, s.send($l(i, o || {}, { data: a })));
  }
}
function St(r, e) {
  const { metaQuery: t } = e;
  if (!t) return;
  const { code: s, queryCode: n } = r;
  return (i, a) => {
    if (!t) {
      console.warn("adapter.metaQuery is not defined!");
      return;
    }
    return t(s, n, i, a);
  };
}
function $t(r = [], e = [], t) {
  const s = {};
  for (const n of r)
    s[n.id] = wt(n, t);
  for (const n of e)
    s[n.id] = St(n, t);
  return s;
}
async function Et(r = [], e = window) {
  const t = O(e);
  t && (Fe(e), r.forEach((s) => bt(t, s)));
}
function jt(r, e = window) {
  const t = D(r.mockTemplate) && r.mockTemplate.value ? ee2(r.mockTemplate, {}, true) : void 0, s = O(e);
  return async (...n) => {
    let i = {};
    if (t)
      try {
        i = await t.apply(t, n);
      } catch (a) {
        jo.warn("模拟数据模版异常", a);
      }
    return s == null ? void 0 : s.mock(i);
  };
}
function bt(r, e) {
  if (!e.mock) return;
  const { url: t, mockTemplate: s } = e;
  if (t && s) {
    const n = cr(`${t}(.*)`), i = wd(t, { decode: decodeURIComponent }), a = T(s, {}, true);
    r.mock(n, (o) => {
      var _a;
      const p = Co.parse(o.url) || {}, c = o.body instanceof FormData ? So(o.body) : o.body, u = (_a = i(o.url)) == null ? void 0 : _a.params;
      return Object.assign(o, { data: c, params: p, query: u }), r.mock(a(o));
    });
  }
}
function Fe(r = window) {
  const e = O(r);
  e && (e._mocked = {});
}
var Rt = class {
  constructor(e) {
    __publicField(this, "__id", null);
    __publicField(this, "__mode");
    __publicField(this, "__instance", null);
    __publicField(this, "__contextRefs", {});
    __publicField(this, "__refs", {});
    __publicField(this, "context", {});
    __publicField(this, "state", {});
    __publicField(this, "props", {});
    __publicField(this, "$props", {});
    __publicField(this, "$refs", {});
    __publicField(this, "$el", null);
    __publicField(this, "$emit", null);
    __publicField(this, "$nextTick", null);
    __publicField(this, "$parent", null);
    __publicField(this, "$root", null);
    __publicField(this, "$attrs", null);
    __publicField(this, "$slots", null);
    __publicField(this, "$watch", null);
    __publicField(this, "$options", null);
    __publicField(this, "$forceUpdate", null);
    __publicField(this, "$components", {});
    __publicField(this, "$libs", {});
    __publicField(this, "$apis", {});
    __publicField(this, "__transform", {});
    const { mode: t, dsl: s, attrs: n } = e;
    this.__mode = t, s && (this.__id = s.id || null, this.__transform = s.transform || {}), n && Object.assign(this, n);
  }
  setup(e, t = vue_runtime_esm_bundler_exports) {
    const s = t.getCurrentInstance();
    if (!s) return;
    this.__refs = {}, this.$refs = {}, this.context = {}, this.__contextRefs = {}, this.__instance = s.proxy;
    const n = s.appContext.config.globalProperties;
    Object.assign(this, n), Object.assign(this, e || {}), this.__proxy(), t.onMounted(() => {
      this.__proxy();
    }), t.onUnmounted(() => {
      this.__cleanup();
    }), t.onBeforeUpdate(() => {
      this.__reset();
    });
  }
  __proxy() {
    this.__instance && ve.forEach((e) => {
      var _a;
      this[e] = (_a = this.__instance) == null ? void 0 : _a[e];
    });
  }
  __cleanup() {
    ve.forEach((e) => {
      this[e] = null;
    }), this.__reset();
  }
  __reset() {
    this.__refs = {}, this.$refs = {}, this.__contextRefs = {}, this.context = {};
  }
  __parseFunction(e) {
    if (e)
      if (this.__mode === w.Runtime) {
        const { id: t, type: s } = e, n = t ? this.__transform[t] ?? e.value : e.value;
        return ee2({ type: s, value: n }, this);
      } else
        return ee2(e, this);
  }
  __parseExpression(e) {
    if (e)
      if (this.__mode === w.Runtime) {
        const { id: t, type: s } = e, n = t ? this.__transform[t] ?? e.value : e.value;
        return T({ type: s, value: n }, this);
      } else
        return T(e, this);
  }
  __ref(e = null, t) {
    if (this.__mode !== w.VNode)
      return e && e !== this.__id && (this.__contextRefs[e] = this), async (s) => {
        var _a, _b;
        await id(0);
        let n = (s == null ? void 0 : s.$vtjEl) || (s == null ? void 0 : s.$el) || ((_b = (_a = s == null ? void 0 : s._) == null ? void 0 : _a.vnode) == null ? void 0 : _b.el) || s;
        if (!n) {
          typeof t == "string" && (delete this.$refs[t], e && delete this.__refs[e]);
          return;
        }
        return n.nodeType === 3 && n.nextSibling && (n = n.nextSibling), n.__vtj__ = e, w.Design === this.__mode && (n.__context__ = this, n.draggable = true), e && (this.__refs[e] = this.__getRefEl(this.__refs, e, s)), typeof t == "function" ? t(s) : t && (this.$refs[t] = this.__getRefEl(this.$refs, t, s)), s;
      };
  }
  __getRefEl(e, t, s) {
    const n = e[t];
    if (n && s !== n) {
      const i = new Set([].concat(n, s));
      return Array.from(i);
    } else
      return s;
  }
  __clone(e = {}) {
    const t = { ...this.context, ...e }, s = {
      ...t,
      context: t
    };
    return s.context.__proto__ = this.context, s.__proto__ = this, s;
  }
};
function te2(r) {
  const {
    Vue: e = vue_runtime_esm_bundler_exports,
    mode: t = w.Runtime,
    components: s = {},
    libs: n = {},
    apis: i = {},
    loader: a
  } = r, o = e.computed(() => r.dsl), p = {
    $components: s,
    $libs: n,
    $apis: i
  }, c = new Rt({
    mode: t,
    dsl: o.value,
    attrs: p
  }), u = e.defineComponent({
    name: o.value.name,
    __scopeId: o.value.id ? `data-v-${o.value.id}` : void 0,
    props: {
      ...Ct(o.value.props ?? [], c)
    },
    setup(l) {
      c.$props = l, c.props = l, o.value.id && Re(
        r.window || window,
        o.value.id,
        o.value.css || "",
        true
      ), c.state = At(e, o.value.state ?? {}, c);
      const h2 = It(e, o.value.computed ?? {}, c), f = Ft(o.value.methods ?? {}, c), d = kt(e, o.value.inject, c);
      for (const [m, E] of Object.entries(d || {}))
        d[m] = e.inject(m, E);
      const g = Mt(
        o.value.dataSources || {},
        c
      ), y = {
        ...d,
        ...h2,
        ...f,
        ...g
      };
      return c.setup(y, e), Ot(e, o.value.watch ?? [], c), {
        vtj: c
      };
    },
    emits: Pt(o.value.emits),
    expose: ["vtj"],
    render() {
      if (!o.value.nodes) return null;
      const l = o.value.nodes || [];
      return l.length === 1 ? F(l[0], c, e, a, l) : l.map(
        (h2) => F(h2, c, e, a, l)
      );
    },
    ...Tt(o.value.lifeCycles ?? {}, c)
  });
  return {
    renderer: e.markRaw(u),
    context: c
  };
}
function Pt(r = []) {
  return r.map((e) => al(e) ? e : e.name);
}
function Ct(r = [], e) {
  const t = (s) => s ? (Array.isArray(s) ? s : [s]).map((i) => nt[i]) : void 0;
  return r.map((s) => al(s) ? {
    name: s
  } : {
    name: s.name,
    type: s.type,
    required: s.required,
    default: $(s.default) ? e.__parseExpression(s.default) : s.default
  }).reduce(
    (s, n) => (s[n.name] = {
      type: t(n.type),
      required: n.required,
      default: n.default
    }, s),
    {}
  );
}
function At(r, e, t) {
  return r.reactive(
    Object.keys(e || {}).reduce(
      (s, n) => {
        let i = e[n];
        return $(i) ? i = t.__parseExpression(i) : D(i) && (i = t.__parseFunction(i)), s[n] = i, s;
      },
      {}
    )
  );
}
function It(r, e, t) {
  return Object.entries(e ?? {}).reduce(
    (s, [n, i]) => (s[n] = r.computed(t.__parseFunction(i)), s),
    {}
  );
}
function Ft(r, e) {
  return Object.entries(r ?? {}).reduce(
    (t, [s, n]) => (t[s] = e.__parseFunction(n), t),
    {}
  );
}
function kt(r, e = [], t) {
  return e.reduce(
    (s, n) => {
      const { name: i, from: a } = n || {};
      n.default;
      const o = $(a) ? t.__parseExpression(a) || i : a ?? i, p = $(n.default) ? t.__parseExpression(n.default) : n.default ?? null;
      return s[i] = r.inject(o, p), s;
    },
    {}
  );
}
function Mt(r, e) {
  return Object.keys(r).reduce(
    (t, s) => {
      const n = r[s];
      if (n.type === "mock")
        t[s] = jt(n);
      else if (n.ref) {
        const i = e.$apis[n.ref], a = D(n.transform) ? n.transform.value ? e.__parseFunction(n.transform) : void 0 : n.transform;
        t[s] = async (...o) => {
          const p = await i.apply(e, o);
          return a ? a(p) : p;
        };
      }
      return t;
    },
    {}
  );
}
function Ot(r, e = [], t) {
  e.forEach((s) => {
    r.watch(
      t.__parseExpression(s.source),
      t.__parseFunction(s.handler),
      {
        deep: s.deep,
        immediate: s.immediate
      }
    );
  });
}
function Tt(r, e) {
  return Object.entries(r ?? {}).reduce(
    (t, [s, n]) => {
      const i = e.__parseFunction(n);
      return t[s] = async () => {
        await id(0), Qt(i) && i();
      }, t;
    },
    {}
  );
}
var ke = new $d();
var Q2 = [];
var C = {};
var Dt = (r) => r;
async function Me(r, e = window) {
  const { urls: t = [], library: s } = r, n = t.filter((o) => K(o));
  if (n.length === 0 || !s) return null;
  const i = t.filter((o) => q(o));
  return i.length && Pe2(i, e), await Ce(n, s, e).catch(
    (o) => (console.warn("loadScriptUrl error", n, s, o), null)
  );
}
function se(r) {
  const { getDsl: e, getDslByUrl: t, options: s } = r;
  return s.window && (Q2.forEach((n) => {
    delete s.window[n];
  }), Q2 = []), (n, i, a = vue_runtime_esm_bundler_exports) => {
    if (!i || typeof i == "string") return n;
    if (i.type === "Schema" && i.id)
      return a.defineAsyncComponent(async () => {
        const o = C[i.id] || await ke.add(
          i.id,
          () => e(i.id)
        );
        return o && (o.name = n, C[i.id] = o), o ? te2({
          ...s,
          Vue: a,
          dsl: qc(o),
          mode: w.Runtime,
          loader: se(r)
        }).renderer : null;
      });
    if (i.type === "UrlSchema" && i.url)
      return a.defineAsyncComponent(async () => {
        const o = C[i.url] || await t(i.url);
        return o && (o.name = n, C[i.url] = o), o ? te2({
          ...s,
          Vue: a,
          dsl: qc(o),
          mode: w.Runtime,
          loader: se(r)
        }).renderer : null;
      });
    if (i.type === "Plugin") {
      let o = i.library ? C[i.library] : null;
      return o || (i.library && Q2.push(i.library), o = C[i.library || Symbol()] = a.defineAsyncComponent(
        async () => {
          const p = await Me(i, s.window);
          return p || (console.warn("getPlugin result is null", i), null);
        }
      ), o);
    }
    return n;
  };
}
function vs2() {
  C = {}, ke.clearAllCache();
}
function F(r, e, t = vue_runtime_esm_bundler_exports, s = Dt, n = [], i = false) {
  var _a;
  if (!r || !r.name || r.invisible) return null;
  const a = (_a = t.getCurrentInstance()) == null ? void 0 : _a.appContext, { id: o = null, directives: p = [] } = r, { vIf: c, vElseIf: u, vElse: l, vFor: h2, vShow: f, vModels: d, vBind: g, vHtml: y, others: m } = Oe(p);
  if (!i && (u || l))
    return null;
  if (c && !xt(c, e))
    return Lt(r, e, t, s, n);
  const E = (v, H = 0) => {
    const P = v.$components, z = (() => {
      var _a2, _b;
      if (r.name === "component")
        return Bt(v, (_a2 = r.props) == null ? void 0 : _a2.is);
      if (r.name === "slot") return r.name;
      const S2 = s(r.name, r.from, t);
      if (al(S2))
        return mt(S2) || gt(S2) ? S2 : P[S2] ?? ((_b = a == null ? void 0 : a.app) == null ? void 0 : _b.component(S2)) ?? S2;
      if (G(S2) && r.id) {
        const ue = `Loader${r.id}_${H}`, pe = P[ue];
        return pe || (P[ue] = S2);
      }
      return S2;
    })(), j = Nt(o, r.props ?? {}, v), Ne = Ut(t, r.events ?? {}, v);
    if (r.name === "slot")
      return Jt(t, r, j, v, s);
    g && Object.assign(j, v.__parseExpression(g.value)), f && (j.style = Object.assign(
      j.style ?? {},
      Kt(f, v)
    )), y && Object.assign(j, zt(y, v)), d.forEach((S2) => {
      Object.assign(j, Gt(t, S2, v));
    });
    const Ue = Vt(
      t,
      r.children ?? [],
      v,
      s,
      r
    ), ce = (v == null ? void 0 : v.__id) ? `data-v-${v.__id}` : void 0, Le = ce ? { [ce]: "" } : {};
    let G2 = t.createVNode(
      z,
      { key: `${o}_${H}`, ...Le, ...j, ...Ne },
      Ue
    );
    const le = a ? Ht(a, m, v) : [];
    return le.length && (G2 = t.withDirectives(G2, le)), G2;
  };
  return h2 ? Xt(h2, E, e) : E(e);
}
function Ht(r, e, t) {
  const s = r.app;
  return e.map((n) => {
    const i = typeof n.name == "string" ? s.directive(n.name) : t.__parseExpression(n.name);
    if (!i) return null;
    const a = [i];
    return n.value && a.push(t.__parseExpression(n.value)), n.arg && a.push(n.arg), n.modifiers && a.push(n.modifiers), a;
  }).filter((n) => !!n);
}
function Oe(r = []) {
  const e = r.find((u) => gn(u.name) === "vIf"), t = r.find(
    (u) => gn(u.name) === "vElseIf"
  ), s = r.find((u) => gn(u.name) === "vElse"), n = r.find((u) => gn(u.name) === "vFor"), i = r.find((u) => gn(u.name) === "vShow"), a = r.find((u) => gn(u.name) === "vBind"), o = r.find((u) => gn(u.name) === "vHtml"), p = r.filter(
    (u) => gn(u.name) === "vModel"
  ), c = r.filter(
    (u) => !rt.includes(gn(u.name))
  );
  return {
    vIf: e,
    vElseIf: t,
    vElse: s,
    vFor: n,
    vShow: i,
    vModels: p,
    vBind: a,
    others: c,
    vHtml: o
  };
}
function xt(r, e) {
  return !!e.__parseExpression(r.value);
}
function Bt(r, e) {
  return e ? $(e) ? r.__parseExpression(e) : e : "div";
}
function Nt(r, e, t) {
  const s = re(e, t);
  return s.ref = t.__ref(r, s.ref), s;
}
function re(r, e) {
  return $(r) ? e.__parseExpression(r) : D(r) ? e.__parseFunction(r) : Array.isArray(r) ? r.map((t) => re(t, e)) : typeof r == "object" ? Object.keys(r || {}).reduce(
    (t, s) => {
      let n = r[s];
      return t[s] = re(n, e), t;
    },
    {}
  ) : r;
}
function Ut(r, e, t) {
  const s = ["passive", "capture", "once"], n = {
    capture: "Capture",
    once: "Once",
    passive: "OnceCapture"
  };
  return Object.keys(e || {}).reduce(
    (i, a) => {
      const o = e[a], p = Te(o.modifiers), c = p.find((h2) => s.includes(h2)), u = "on" + Pe(a) + (c && n[c] || ""), l = t.__parseFunction(o.handler);
      return l && (i[u] = r.withModifiers(l, p)), i;
    },
    {}
  );
}
function Lt(r, e, t, s, n = []) {
  let i = n.findIndex((a) => a.id === r.id);
  for (let a = ++i; a < n.length; a++) {
    const { directives: o = [] } = n[a], { vElseIf: p, vElse: c } = Oe(o);
    if (p) {
      if (e.__parseExpression(p.value))
        return F(n[a], e, t, s, n, true);
      continue;
    }
    if (c)
      return F(n[a], e, t, s, n, true);
  }
  return null;
}
function Te(r = {}, e = false) {
  const t = Object.keys(r);
  return e ? t.map((s) => "." + s) : t;
}
function Jt(r, e, t, s, n) {
  var _a;
  const { children: i } = e, a = qt(e, s), o = (_a = s.$slots) == null ? void 0 : _a[a.name];
  return o ? o(t) : i ? al(i) ? r.createTextVNode(i) : $(i) ? r.createTextVNode(
    be(s.__parseExpression(i))
  ) : Array.isArray(i) ? i.map(
    (p) => F(p, s, r, n, i)
  ) : null : null;
}
function qt(r, e) {
  const { props: t } = r, s = (t == null ? void 0 : t.name) || "default";
  return {
    name: $(s) ? e.__parseExpression(s) : s,
    params: []
  };
}
function Kt(r, e) {
  return e.__parseExpression(r.value) ? {} : {
    display: "none"
  };
}
function zt(r, e) {
  return {
    innerHTML: e.__parseExpression(r.value) || ""
  };
}
function Gt(r, e, t) {
  var _a;
  const s = {
    type: "JSFunction",
    value: ((_a = e.value) == null ? void 0 : _a.value) ? `(v) => {
        ${e.value.value} = v;
      }` : "(v) => {}"
  }, n = t.__parseFunction(s), i = Te(
    $(e.modifiers) ? t.__parseExpression(e.modifiers) : e.modifiers
  ), a = $(e.arg) ? t.__parseExpression(e.arg) : e.arg || "modelValue";
  return {
    [a]: t.__parseExpression(e.value),
    [`onUpdate:${a}`]: i.length && n ? r.withModifiers(n, i) : n
  };
}
function Vt(r, e, t, s, n) {
  if (!e) return null;
  if (al(e))
    return { default: () => e };
  if ($(e))
    return {
      default: () => be(t.__parseExpression(e))
    };
  if (Array.isArray(e) && e.length > 0) {
    const i = Wt(e), a = (o) => !o || !n ? {} : (n == null ? void 0 : n.id) && Object.keys(o).length ? {
      [`scope_${n.id}`]: o
    } : {};
    return Object.entries(i).reduce((o, [p, { nodes: c, params: u }]) => (o[p] = (l) => {
      const h2 = u.length ? ld(l ?? {}, u) : a(l);
      return c.map(
        (f) => F(f, t.__clone(h2), r, s, c)
      );
    }, o), {});
  }
  return null;
}
function Wt(r) {
  const e = {};
  for (const t of r) {
    const s = Qt2(t.slot), n = s.name;
    e[n] ? (e[n].nodes.push(t), e[n].params = e[n].params.concat(s.params)) : e[n] = {
      nodes: [t],
      params: s.params
    };
  }
  return e;
}
function Qt2(r = "default") {
  return al(r) ? { name: r, params: [] } : { params: [], ...r };
}
function Xt(r, e, t) {
  const { value: s, iterator: n } = r, { item: i = "item", index: a = "index" } = n || {};
  let o = t.__parseExpression(s) || [];
  return Number.isInteger(o) && (o = new Array(o).fill(true).map((p, c) => c + 1)), Array.isArray(o) ? o.map((p, c) => e(t.__clone({ [i]: p, [a]: c }), c)) : (console.warn("[vForRender]:", `${s == null ? void 0 : s.value} is not a Arrary`), []);
}
var ye = defineComponent({
  name: "VtjPageContainer",
  async setup() {
    const r = xe(), e = useRoute(), t = e.params.id, s = t ? r.getPage(t) : r.getHomepage(), n = s ? await r.getRenderComponent(s.id) : null, i = ref(Symbol());
    if (s) {
      Object.assign(e.meta, s.meta || {}, { cache: s.cache });
      const { useTitle: a } = r == null ? void 0 : r.adapter;
      a && a(s.title || "VTJ");
    }
    return {
      provider: r,
      component: n,
      file: s,
      query: e.query,
      meta: e.meta,
      sid: i,
      route: e
    };
  },
  render() {
    const { component: r, query: e, sid: t } = this;
    return r ? h(r, { ...e, key: t }) : h("div", "页面不存在");
  },
  activated() {
    this.meta.cache === false && (this.sid = Symbol());
  }
});
var Yt = defineComponent({
  name: "VtjStartupContainer",
  render() {
    return h("div", "page not found!");
  }
});
function oe(r, e, t = []) {
  return t.map((s) => {
    const { id: n, title: i, icon: a, children: o, hidden: p } = s;
    return {
      id: n,
      title: i,
      icon: a,
      hidden: p,
      url: `${r}/${e}/${n}`,
      children: o && o.length ? oe(r, e, o) : void 0
    };
  });
}
function De(r, e) {
  if (!e) return r;
  let t = [];
  for (const s of r)
    if (s.children && s.children.length) {
      const n = De(s.children, e);
      n.length && (s.children = n, t.push(s));
    } else
      e.can(s.id.toString()) && t.push(s);
  return t;
}
function ys(r) {
  const {
    menuPathPrefix: e = "",
    pageRouteName: t = "page",
    disableMenusFilter: s = false
  } = r || {}, n = xe(), i = useRoute(), a = _t(), o = ref(false), p = ref(false), c = n.project;
  watchEffect(() => {
    const { name: h2, params: f, meta: d } = i;
    if (h2 === M) {
      const g = n.getPage(f.id);
      o.value = !(g == null ? void 0 : g.mask), p.value = !!(g == null ? void 0 : g.pure);
    } else if (h2 === N) {
      const g = n.getHomepage();
      o.value = !(g == null ? void 0 : g.mask), p.value = !!(g == null ? void 0 : g.pure);
    } else
      o.value = !d.mask, p.value = !!d.pure;
  });
  const u = oe(
    e,
    t,
    c == null ? void 0 : c.pages
  ), l = c == null ? void 0 : c.config;
  return {
    disabled: o,
    logo: l == null ? void 0 : l.logo,
    themeSwitchable: l == null ? void 0 : l.themeSwitchable,
    title: (l == null ? void 0 : l.title) || (c == null ? void 0 : c.description) || (c == null ? void 0 : c.name) || "VTJ App",
    menus: s ? u : De(u, a),
    pure: p
  };
}
var He = Symbol("Provider");
var Zt = ((r) => (r.Production = "production", r.Development = "development", r))(Zt || {});
var es = class extends ee {
  // DSL缓存
  /**
   * 创建Provider实例
   * @param options 配置选项
   */
  constructor(e) {
    super();
    __publicField(this, "mode");
    // 当前运行模式(设计/源码/预览等)
    __publicField(this, "globals", {});
    // 全局变量
    __publicField(this, "modules", {});
    // 异步模块加载器
    __publicField(this, "adapter", { request: Cs, jsonp: Po });
    // 适配器接口
    __publicField(this, "apis", {});
    // API集合
    __publicField(this, "dependencies", {});
    // 依赖项
    __publicField(this, "materials", {});
    // 物料资源
    __publicField(this, "library", {});
    // 第三方库
    __publicField(this, "service");
    // 核心服务
    __publicField(this, "project", null);
    // 当前项目配置
    __publicField(this, "components", {});
    // 组件集合
    __publicField(this, "nodeEnv", "development");
    // 运行环境
    __publicField(this, "router", null);
    // 路由实例
    __publicField(this, "materialPath", "./");
    // 物料路径
    __publicField(this, "urlDslCaches", {});
    this.options = e;
    const {
      service: t,
      mode: s = w.Raw,
      dependencies: n,
      materials: i,
      project: a = {},
      adapter: o = {},
      globals: p = {},
      modules: c = {},
      router: u = null,
      materialPath: l = "./",
      nodeEnv: h2 = "development"
      /* Development */
    } = e;
    this.mode = s, this.modules = c, this.service = t, this.router = u, this.materialPath = l, this.nodeEnv = h2, n && (this.dependencies = n), i && (this.materials = i), Object.assign(this.globals, p), Object.assign(this.adapter, o);
    const { access: f, request: d } = this.adapter;
    f && f.connect({ mode: s, router: u, request: d }), a && s !== w.Design ? this.load(a) : this.project = a;
  }
  createMock(e) {
    return async (...t) => {
      var _a;
      let s = {};
      if (e)
        try {
          s = await e.apply(e, t);
        } catch (i) {
          jo.warn("模拟数据模版异常", i);
        }
      return (_a = O()) == null ? void 0 : _a.mock(s);
    };
  }
  /**
   * 加载项目配置并初始化
   * 1. 从模块或服务加载项目配置
   * 2. 根据运行模式加载依赖或资源
   * 3. 初始化Mock数据
   * 4. 创建API接口
   * 5. 初始化路由(非uniapp平台)
   * @param project 项目配置
   */
  async load(e) {
    const t = this.modules[`.vtj/projects/${e.id}.json`] || this.modules[`/src/.vtj/projects/${e.id}.json`];
    if (this.project = t ? await t() : await this.service.init(e), !this.project)
      throw new Error("project is null");
    const { apis: s = [], meta: n = [] } = this.project, i = window;
    i && (i.CKEDITOR_VERSION = void 0), this.mode === w.Raw ? await this.loadDependencies(i) : await this.loadAssets(i), this.initMock(i), this.apis = $t(s, n, this.adapter), Fe(i), Et(s, i), e.platform !== "uniapp" && this.initRouter(), this.triggerReady();
  }
  initMock(e) {
    const t = O(e);
    t && t.setup({
      timeout: "50-500"
    });
  }
  async loadDependencies(e) {
    const t = Object.entries(this.dependencies);
    for (const [s, n] of t)
      e[s] || (e[s] = this.library[s] = await n());
  }
  async loadAssets(e) {
    const { dependencies: t = [] } = this.project, { dependencies: s, library: n, components: i, materialPath: a, nodeEnv: o } = this, {
      libraryExports: p,
      libraryMap: c,
      materials: u,
      materialExports: l,
      materialMapLibrary: h2
    } = lt(
      t,
      a,
      o === "development"
      /* Development */
    );
    for (const f of p) {
      const d = s[f], g = e[f];
      if (g)
        n[f] = g;
      else if (d)
        e[f] = n[f] = await d();
      else {
        const y = c[f] || [];
        for (const m of y)
          q(m) && await dt(m, Co.append(m, { v: k })), K(m) && await vo(Co.append(m, { v: k }));
        n[f] = e[f];
      }
    }
    if (o === "development") {
      for (const d of u)
        await vo(Co.append(d, { v: k }));
      const f = this.materials || {};
      for (const d of l) {
        const g = e[h2[d]], y = Q[d];
        if (y)
          g && y.forEach((m) => {
            i[m] = g[m];
          });
        else {
          const m = f[d] ? (await f[d]()).default : e[d];
          m && g && (m.components || []).forEach((E) => {
            i[E.name] = ut(E, g);
          });
        }
      }
    }
  }
  initRouter() {
    const { router: e, project: t, options: s, adapter: n } = this;
    if (!e) return;
    const i = (t == null ? void 0 : t.platform) === "uniapp" ? "pages" : "page", {
      routeAppendTo: a,
      pageRouteName: o = i,
      routeMeta: p
    } = s, c = a ? "" : "/", u = {
      path: `${c}${o}/:id`,
      name: M,
      component: ye
    }, l = {
      path: c,
      name: N,
      component: (t == null ? void 0 : t.homepage) ? ye : n.startupComponent || Yt,
      meta: p
    };
    e.hasRoute(M) && e.removeRoute(M), e.hasRoute(N) && e.removeRoute(N), a ? (e.addRoute(a, u), e.addRoute(a, l)) : (e.addRoute(u), e.addRoute(l));
  }
  /**
   * Vue 插件安装方法
   * 1. 安装所有第三方库插件
   * 2. 执行自定义安装函数(如果提供)
   * 3. 安装访问适配器
   * 4. 提供全局 Provider 实例
   * 5. 设计模式下设置错误处理器
   * 6. 执行增强函数(如果提供)
   * @param app Vue 应用实例
   */
  install(e) {
    const t = e.config.globalProperties.installed || {};
    for (const [s, n] of Object.entries(this.library))
      !t[s] && ht(n) && (e.use(n), t[s] = true);
    this.options.install && e.use(this.options.install), this.adapter.access && e.use(this.adapter.access), e.provide(He, this), e.config.globalProperties.$provider = this, e.config.globalProperties.installed = t, this.mode === w.Design && (e.config.errorHandler = (s, n, i) => {
      const a = n == null ? void 0 : n.$options.name, o = typeof s == "string" ? s : (s == null ? void 0 : s.message) || (s == null ? void 0 : s.msg) || "未知错误", p = `[ ${a} ] ${o} ${i}`;
      console.error(
        "[VTJ Error]:",
        {
          err: s,
          instance: n,
          info: i
        },
        s == null ? void 0 : s.stack
      ), this.adapter.notify && this.adapter.notify(p, "组件渲染错误", "error");
    }), this.options.enhance && e.use(this.options.enhance, this);
  }
  getFile(e) {
    const { blocks: t = [] } = this.project || {};
    return this.getPage(e) || t.find((s) => s.id === e) || null;
  }
  getPage(e) {
    const { pages: t = [] } = this.project || {}, s = (n, i = []) => {
      for (const a of i) {
        if (a.id === n)
          return a;
        if (a.children && a.children.length) {
          const o = s(n, a.children);
          if (o)
            return o;
        }
      }
    };
    return s(e, t) || null;
  }
  getMenus(e = "page", t = "") {
    var _a;
    return oe(t, e, ((_a = this.project) == null ? void 0 : _a.pages) || []);
  }
  getHomepage() {
    const { homepage: e } = this.project || {};
    return e ? this.getPage(e) : null;
  }
  async getDsl(e) {
    const t = this.modules[`.vtj/files/${e}.json`] || this.modules[`/src/.vtj/files/${e}.json`];
    return t ? await t() : this.service.getFile(e, this.project || void 0).catch(() => null);
  }
  async getDslByUrl(e) {
    const t = this.urlDslCaches[e];
    return t || (this.adapter.request ? this.urlDslCaches[e] = this.adapter.request.send({
      url: e,
      method: "get",
      settings: {
        validSuccess: false,
        originResponse: true
      }
    }).then((s) => s.data).catch(() => null) : null);
  }
  /**
   * 创建 DSL 渲染器
   * 1. 合并默认选项和自定义选项
   * 2. 创建 DSL 加载器
   * 3. 返回渲染器实例
   * @param dsl 区块 DSL 配置
   * @param opts 渲染选项
   * @returns 渲染器实例
   */
  createDslRenderer(e, t = {}) {
    const { library: s, components: n, mode: i, apis: a } = this, o = {
      mode: i,
      Vue: s.Vue,
      components: n,
      libs: s,
      apis: a,
      window,
      ...t
    }, p = se({
      getDsl: async (c) => await this.getDsl(c) || null,
      getDslByUrl: async (c) => await this.getDslByUrl(c) || null,
      options: o
    });
    return te2({
      ...o,
      dsl: e,
      loader: p
    });
  }
  /**
   * 获取渲染组件
   * 1. 根据ID查找文件(页面或区块)
   * 2. 如果找到文件且提供了output回调，则调用它
   * 3. 尝试从模块缓存加载原始Vue组件
   * 4. 如果找不到原始组件，则获取DSL并创建渲染器
   * @param id 文件ID
   * @param output 找到文件时的回调函数
   * @returns Promise<Vue组件>
   */
  async getRenderComponent(e, t) {
    var _a;
    const s = this.getFile(e);
    if (!s)
      return jo.warn(`Can not find file: ${e}`), null;
    t && t(s);
    const n = `.vtj/vue/${e}.vue`, i = this.modules[n] || this.modules[`/src/pages/${e}.vue`];
    if (i)
      return (_a = await i()) == null ? void 0 : _a.default;
    const a = await this.getDsl(s.id);
    return a ? this.createDslRenderer(a).renderer : (jo.warn(`Can not find dsl: ${e}`), null);
  }
  /**
   * 定义基于URL的异步组件
   * 1. 根据URL获取DSL配置
   * 2. 如果获取成功，设置组件名称
   * 3. 创建并返回DSL渲染器
   * @param url DSL配置URL
   * @param name 可选的自定义组件名称
   * @returns Vue异步组件
   */
  defineUrlSchemaComponent(e, t) {
    return defineAsyncComponent(async () => {
      const s = await this.getDslByUrl(e);
      return s ? (s.name = t || s.name, this.createDslRenderer(s).renderer) : null;
    });
  }
  /**
   * 定义基于插件的异步组件
   * 1. 根据插件来源获取插件实例
   * 2. 返回插件组件
   * @param from 插件来源配置
   * @returns Vue异步组件
   */
  definePluginComponent(e) {
    return defineAsyncComponent(async () => await Me(e, window));
  }
};
function _s(r) {
  const e = new es(r);
  return {
    provider: e,
    onReady: (s) => e.ready(s)
  };
}
function xe(r = {}) {
  const e = inject(He, null);
  if (!e)
    throw new Error("Can not find provider");
  if (e.nodeEnv === "development") {
    const { id: t, version: s } = r;
    t && s && (async () => {
      const n = await e.getDsl(t);
      (n == null ? void 0 : n.__VERSION__) !== s && e.adapter.notify && e.adapter.notify(
        `[ ${n == null ? void 0 : n.name} ] 组件源码版本与运行时版本不一致，请重新发布组件`,
        "版本不一致",
        "warning"
      );
    })();
  }
  return e;
}
async function ws(r, e = "") {
  const { name: t, urls: s = [] } = r || {}, n = s.map((o) => e + o), { css: i, js: a } = pt(n);
  if (i.length && Pe2(i), a.length)
    return await Ce(a, t).catch(
      () => {
      }
    );
}
var Be = {
  type: "json",
  validSuccess: true,
  originResponse: false,
  failMessage: true,
  validate: (r) => {
    var _a;
    return ((_a = r.data) == null ? void 0 : _a.code) === 0;
  }
};
var ts = (r, e = "/__vtj__/api/:type.json") => (t, s, n) => r.send({
  url: e,
  method: "post",
  params: { type: t },
  query: n,
  data: {
    type: t,
    data: s
  },
  settings: Be
});
var ss = (r, e = "/__vtj__/api/uploader.json") => async (t, s) => await r.send({
  url: e,
  method: "post",
  data: {
    files: t,
    projectId: s
  },
  settings: {
    ...Be,
    type: "data"
  }
}).then((n) => n && n[0] ? n[0] : null).catch(() => null);
function Ss(r) {
  return xs({
    settings: {
      type: "json",
      validSuccess: true,
      originResponse: false,
      failMessage: true,
      validate: (e) => {
        var _a;
        return ((_a = e.data) == null ? void 0 : _a.code) === 0;
      },
      showError: (e) => {
        r && r(e || "未知错误");
      }
    }
  });
}
var ae = class {
  constructor(e = Cs) {
    __publicField(this, "api");
    __publicField(this, "pluginCaches", {});
    __publicField(this, "uploader");
    this.req = e, this.api = ts(e), this.uploader = ss(e);
  }
  async getExtension() {
    console.log("BaseService.getExtension");
  }
  async init(e) {
    return console.log("BaseService.init", e), {};
  }
  async saveProject(e, t) {
    return !!await this.api("saveProject", e, { type: t }).catch(
      () => false
    );
  }
  async saveMaterials(e, t) {
    return console.log("BaseService.saveMaterials", e, t), false;
  }
  async saveFile(e) {
    return console.log("BaseService.saveFile", e), false;
  }
  async getFile(e) {
    return console.log("BaseService.getFile", e), {};
  }
  async removeFile(e) {
    return console.log("BaseService.removeFile", e), false;
  }
  async saveHistory(e) {
    return console.log("BaseService.saveHistory", e), false;
  }
  async removeHistory(e) {
    return console.log("BaseService.removeHistory", e), false;
  }
  async getHistory(e) {
    return console.log("BaseService.getHistory", e), {};
  }
  async getHistoryItem(e, t) {
    return console.log("BaseService.getHistoryItem", e, t), {};
  }
  async saveHistoryItem(e, t) {
    return console.log("BaseService.saveHistoryItem", e, t), false;
  }
  async removeHistoryItem(e, t) {
    return console.log("BaseService.removeHistoryItem", e, t), false;
  }
  async publish(e) {
    return !!await this.api("publish", e).catch(() => false);
  }
  async publishFile(e, t) {
    return !!await this.api("publishFile", { project: e, file: t }).catch(
      () => false
    );
  }
  async genVueContent(e, t) {
    return await this.api("genVueContent", { project: e, dsl: t }).catch(() => "");
  }
  async parseVue(e, t) {
    return await this.api("parseVue", {
      project: e,
      ...t
    });
  }
  async createRawPage(e) {
    return await this.api("createRawPage", e).catch(() => "");
  }
  async removeRawPage(e) {
    return await this.api("removeRawPage", e).catch(() => "");
  }
  async uploadStaticFile(e, t) {
    return await this.uploader(e, t).catch(() => null);
  }
  async getStaticFiles(e) {
    return await this.api("getStaticFiles", e).catch(() => []);
  }
  async removeStaticFile(e, t) {
    return await this.api("removeStaticFile", { name: e, projectId: t }).catch(
      () => ""
    );
  }
  async clearStaticFiles(e) {
    return await this.api("clearStaticFiles", e).catch(() => "");
  }
  async getPluginMaterial(e) {
    const { urls: t = [] } = e, s = t.filter((i) => at(i))[0];
    if (!s) return null;
    const n = this.pluginCaches[s];
    return n || (this.pluginCaches[s] = Cs.send({
      url: s,
      method: "get",
      settings: {
        validSuccess: false,
        originResponse: true
      }
    }).then((i) => i.data).catch(() => null));
  }
  async genSource(e) {
    return console.log("BaseService.genSource", e), "";
  }
};
var _ = new vs({
  type: "local",
  expired: 0,
  prefix: "__VTJ_"
});
var $s = class extends ae {
  init(e) {
    const t = new S(e), s = _.get(`project_${t.id}`), n = Object.assign(t.toDsl(), s || {});
    return _.save(`project_${t.id}`, n), Promise.resolve(n);
  }
  saveProject(e) {
    const t = new S(e);
    return _.save(`project_${t.id}`, t.toDsl()), Promise.resolve(true);
  }
  saveMaterials(e, t) {
    return _.save(`materials_${e.id}`, sd(t)), Promise.resolve(true);
  }
  saveFile(e) {
    return _.save(`file_${e.id}`, e), Promise.resolve(true);
  }
  getFile(e) {
    const t = _.get(`file_${e}`);
    return t ? Promise.resolve(t) : Promise.reject(null);
  }
  removeFile(e) {
    return _.remove(`file_${e}`), Promise.resolve(true);
  }
  saveHistory(e) {
    return _.save(`history_${e.id}`, e), Promise.resolve(true);
  }
  removeHistory(e) {
    const t = _.get(`history_${e}`);
    if (t) {
      const n = (t.items || []).map((i) => i.id);
      this.removeHistoryItem(e, n), _.remove(`history_${e}`);
    }
    return Promise.resolve(true);
  }
  getHistory(e) {
    const t = _.get(`history_${e}`), s = new te(t || { id: e });
    return Promise.resolve(s.toDsl());
  }
  getHistoryItem(e, t) {
    const s = _.get(`history_${e}_${t}`);
    return Promise.resolve(s);
  }
  saveHistoryItem(e, t) {
    return _.save(`history_${e}_${t.id}`, t), Promise.resolve(true);
  }
  removeHistoryItem(e, t) {
    return t.forEach((s) => {
      _.remove(`history_${e}_${s}`);
    }), Promise.resolve(true);
  }
};
var rs = class extends ae {
  constructor() {
    super(...arguments);
    __publicField(this, "projects", {});
    __publicField(this, "materials", {});
    __publicField(this, "files", {});
    __publicField(this, "histories", {});
    __publicField(this, "historyItems", {});
  }
  init(e) {
    const t = new S(e), s = this.projects[t.id] || {}, n = Object.assign(t.toDsl(), s);
    return this.projects[n.id] = n, Promise.resolve(n);
  }
  saveProject(e) {
    const t = new S(e);
    return this.projects[t.id] = t.toDsl(), Promise.resolve(true);
  }
  saveMaterials(e, t) {
    return e.id && (this.materials[e.id] = sd(t)), Promise.resolve(true);
  }
  saveFile(e) {
    return this.files[e.id] = e, Promise.resolve(true);
  }
  getFile(e) {
    const t = this.files[e];
    return t ? Promise.resolve(t) : Promise.reject(null);
  }
  removeFile(e) {
    return delete this.files[e], Promise.resolve(true);
  }
  saveHistory(e) {
    return this.histories[e.id] = e, Promise.resolve(true);
  }
  removeHistory(e) {
    const t = this.histories[e];
    if (t) {
      const n = (t.items || []).map((i) => i.id);
      this.removeHistoryItem(e, n), delete this.historyItems[e];
    }
    return Promise.resolve(true);
  }
  getHistory(e) {
    const t = this.histories[e], s = new te(t || { id: e });
    return Promise.resolve(s);
  }
  getHistoryItem(e, t) {
    const s = `${e}_${t}`, n = this.historyItems[s] || {};
    return Promise.resolve(n);
  }
  saveHistoryItem(e, t) {
    const s = `${e}_${t.id}`;
    return this.historyItems[s] = t, Promise.resolve(true);
  }
  removeHistoryItem(e, t) {
    return t.forEach((s) => {
      const n = `${e}_${s}`;
      delete this.historyItems[n];
    }), Promise.resolve(true);
  }
};
var B = null;
function Es() {
  return B || (B = new rs(), B);
}
var js = class extends ae {
  constructor() {
    super(...arguments);
    __publicField(this, "getFileCaches", {});
  }
  async getExtension() {
    return await this.api("getExtension", {}).catch(() => {
    });
  }
  async init(e) {
    return await this.api("init", e).catch(() => null) || {};
  }
  async saveProject(e, t) {
    return !!await this.api("saveProject", e, { type: t }).catch(
      () => false
    );
  }
  async saveMaterials(e, t) {
    return !!await this.api("saveMaterials", {
      project: e,
      materials: sd(t)
    }).catch(() => false);
  }
  async saveFile(e) {
    return !!await this.api("saveFile", e).catch(() => false);
  }
  async getFile(e) {
    const t = this.getFileCaches[e];
    return t || (this.getFileCaches[e] = this.api("getFile", e).catch(
      () => null
    )).finally(() => {
      delete this.getFileCaches[e];
    });
  }
  async removeFile(e) {
    return !!await this.api("removeFile", e).catch(() => false);
  }
  async saveHistory(e) {
    return !!await this.api("saveHistory", e).catch(() => false);
  }
  async removeHistory(e) {
    return !!await this.api("removeHistory", e).catch(() => false);
  }
  async getHistory(e) {
    return await this.api("getHistory", e).catch(() => null) || {};
  }
  async getHistoryItem(e, t) {
    return await this.api("getHistoryItem", { fId: e, id: t }).catch(() => null) || {};
  }
  async saveHistoryItem(e, t) {
    return !!await this.api("saveHistoryItem", { fId: e, item: t }).catch(
      () => false
    );
  }
  async removeHistoryItem(e, t) {
    return !!await this.api("removeHistoryItem", { fId: e, ids: t }).catch(
      () => false
    );
  }
};

export {
  k,
  w,
  ve,
  cs,
  rt,
  nt,
  M,
  N,
  it,
  ot,
  ls,
  us,
  x,
  q,
  K,
  at,
  ps,
  fs,
  ct,
  lt,
  ut,
  pt,
  ft,
  be,
  Re,
  dt,
  Pe2 as Pe,
  Ce,
  ht,
  mt,
  gt,
  O,
  ds,
  T,
  ee2 as ee,
  $,
  D,
  vt,
  hs,
  Ae,
  Ie,
  _t,
  ms,
  gs,
  wt,
  St,
  $t,
  Et,
  jt,
  bt,
  Fe,
  Rt,
  te2 as te,
  Mt,
  Dt,
  Me,
  se,
  vs2 as vs,
  F,
  Te,
  oe,
  De,
  ys,
  He,
  Zt,
  es,
  _s,
  xe,
  ws,
  Ss,
  ae,
  $s,
  rs,
  Es,
  js,
  dist_exports
};
/*! Bundled license information:

@vtj/renderer/dist/index.mjs:
  (**!
   * Copyright (c) 2025, VTJ.PRO All rights reserved.
   * @name @vtj/renderer 
   * @<NAME_EMAIL> 
   * @version 0.12.47
   * @license <a href="https://vtj.pro/license.html">MIT License</a>
   *)
*/
//# sourceMappingURL=chunk-ZE33OC6W.js.map
