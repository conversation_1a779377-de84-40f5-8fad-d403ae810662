{"version": 3, "file": "trace-mapping.umd.js", "sources": ["../src/resolve.ts", "../src/strip-filename.ts", "../src/sourcemap-segment.ts", "../src/sort.ts", "../src/binary-search.ts", "../src/by-source.ts", "../src/any-map.ts", "../src/trace-mapping.ts"], "sourcesContent": ["import resolveUri from '@jridgewell/resolve-uri';\n\nexport default function resolve(input: string, base: string | undefined): string {\n  // The base is always treated as a directory, if it's not empty.\n  // https://github.com/mozilla/source-map/blob/8cb3ee57/lib/util.js#L327\n  // https://github.com/chromium/chromium/blob/da4adbb3/third_party/blink/renderer/devtools/front_end/sdk/SourceMap.js#L400-L401\n  if (base && !base.endsWith('/')) base += '/';\n\n  return resolveUri(input, base);\n}\n", "/**\n * Removes everything after the last \"/\", but leaves the slash.\n */\nexport default function stripFilename(path: string | undefined | null): string {\n  if (!path) return '';\n  const index = path.lastIndexOf('/');\n  return path.slice(0, index + 1);\n}\n", "type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\ntype GeneratedLine = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport type ReverseSegment = [SourceColumn, GeneratedLine, GeneratedColumn];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n\nexport const REV_GENERATED_LINE = 1;\nexport const REV_GENERATED_COLUMN = 2;\n", "import { COLUMN } from './sourcemap-segment';\n\nimport type { SourceMapSegment } from './sourcemap-segment';\n\nexport default function maybeSort(\n  mappings: SourceMapSegment[][],\n  owned: boolean,\n): SourceMapSegment[][] {\n  const unsortedIndex = nextUnsortedSegmentLine(mappings, 0);\n  if (unsortedIndex === mappings.length) return mappings;\n\n  // If we own the array (meaning we parsed it from JSON), then we're free to directly mutate it. If\n  // not, we do not want to modify the consumer's input array.\n  if (!owned) mappings = mappings.slice();\n\n  for (let i = unsortedIndex; i < mappings.length; i = nextUnsortedSegmentLine(mappings, i + 1)) {\n    mappings[i] = sortSegments(mappings[i], owned);\n  }\n  return mappings;\n}\n\nfunction nextUnsortedSegmentLine(mappings: SourceMapSegment[][], start: number): number {\n  for (let i = start; i < mappings.length; i++) {\n    if (!isSorted(mappings[i])) return i;\n  }\n  return mappings.length;\n}\n\nfunction isSorted(line: SourceMapSegment[]): boolean {\n  for (let j = 1; j < line.length; j++) {\n    if (line[j][COLUMN] < line[j - 1][COLUMN]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction sortSegments(line: SourceMapSegment[], owned: boolean): SourceMapSegment[] {\n  if (!owned) line = line.slice();\n  return line.sort(sortComparator);\n}\n\nfunction sortComparator(a: SourceMapSegment, b: SourceMapSegment): number {\n  return a[COLUMN] - b[COLUMN];\n}\n", "import type { SourceMapSegment, ReverseSegment } from './sourcemap-segment';\nimport { COLUMN } from './sourcemap-segment';\n\nexport type MemoState = {\n  lastKey: number;\n  lastNeedle: number;\n  lastIndex: number;\n};\n\nexport let found = false;\n\n/**\n * A binary search implementation that returns the index if a match is found.\n * If no match is found, then the left-index (the index associated with the item that comes just\n * before the desired index) is returned. To maintain proper sort order, a splice would happen at\n * the next index:\n *\n * ```js\n * const array = [1, 3];\n * const needle = 2;\n * const index = binarySearch(array, needle, (item, needle) => item - needle);\n *\n * assert.equal(index, 0);\n * array.splice(index + 1, 0, needle);\n * assert.deepEqual(array, [1, 2, 3]);\n * ```\n */\nexport function binarySearch(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  low: number,\n  high: number,\n): number {\n  while (low <= high) {\n    const mid = low + ((high - low) >> 1);\n    const cmp = haystack[mid][COLUMN] - needle;\n\n    if (cmp === 0) {\n      found = true;\n      return mid;\n    }\n\n    if (cmp < 0) {\n      low = mid + 1;\n    } else {\n      high = mid - 1;\n    }\n  }\n\n  found = false;\n  return low - 1;\n}\n\nexport function upperBound(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  index: number,\n): number {\n  for (let i = index + 1; i < haystack.length; index = i++) {\n    if (haystack[i][COLUMN] !== needle) break;\n  }\n  return index;\n}\n\nexport function lowerBound(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  index: number,\n): number {\n  for (let i = index - 1; i >= 0; index = i--) {\n    if (haystack[i][COLUMN] !== needle) break;\n  }\n  return index;\n}\n\nexport function memoizedState(): MemoState {\n  return {\n    lastKey: -1,\n    lastNeedle: -1,\n    lastIndex: -1,\n  };\n}\n\n/**\n * This overly complicated beast is just to record the last tested line/column and the resulting\n * index, allowing us to skip a few tests if mappings are monotonically increasing.\n */\nexport function memoizedBinarySearch(\n  haystack: SourceMapSegment[] | ReverseSegment[],\n  needle: number,\n  state: MemoState,\n  key: number,\n): number {\n  const { lastKey, lastNeedle, lastIndex } = state;\n\n  let low = 0;\n  let high = haystack.length - 1;\n  if (key === lastKey) {\n    if (needle === lastNeedle) {\n      found = lastIndex !== -1 && haystack[lastIndex][COLUMN] === needle;\n      return lastIndex;\n    }\n\n    if (needle >= lastNeedle) {\n      // lastIndex may be -1 if the previous needle was not found.\n      low = lastIndex === -1 ? 0 : lastIndex;\n    } else {\n      high = lastIndex;\n    }\n  }\n  state.lastKey = key;\n  state.lastNeedle = needle;\n\n  return (state.lastIndex = binarySearch(haystack, needle, low, high));\n}\n", "import { COLUMN, SOURCES_INDEX, SOURCE_LINE, SOURCE_COLUMN } from './sourcemap-segment';\nimport { memoizedBinarySearch, upperBound } from './binary-search';\n\nimport type { ReverseSegment, SourceMapSegment } from './sourcemap-segment';\nimport type { MemoState } from './binary-search';\n\nexport type Source = {\n  __proto__: null;\n  [line: number]: Exclude<ReverseSegment, [number]>[];\n};\n\n// Rebuilds the original source files, with mappings that are ordered by source line/column instead\n// of generated line/column.\nexport default function buildBySources(\n  decoded: readonly SourceMapSegment[][],\n  memos: MemoState[],\n): Source[] {\n  const sources: Source[] = memos.map(buildNullArray);\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n      if (seg.length === 1) continue;\n\n      const sourceIndex = seg[SOURCES_INDEX];\n      const sourceLine = seg[SOURCE_LINE];\n      const sourceColumn = seg[SOURCE_COLUMN];\n      const originalSource = sources[sourceIndex];\n      const originalLine = (originalSource[sourceLine] ||= []);\n      const memo = memos[sourceIndex];\n\n      // The binary search either found a match, or it found the left-index just before where the\n      // segment should go. Either way, we want to insert after that. And there may be multiple\n      // generated segments associated with an original location, so there may need to move several\n      // indexes before we find where we need to insert.\n      let index = upperBound(\n        originalLine,\n        sourceColumn,\n        memoizedBinarySearch(originalLine, sourceColumn, memo, sourceLine),\n      );\n\n      memo.lastIndex = ++index;\n      insert(originalLine, index, [sourceColumn, i, seg[COLUMN]]);\n    }\n  }\n\n  return sources;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\n// Null arrays allow us to use ordered index keys without actually allocating contiguous memory like\n// a real array. We use a null-prototype object to avoid prototype pollution and deoptimizations.\n// Numeric properties on objects are magically sorted in ascending order by the engine regardless of\n// the insertion order. So, by setting any numeric keys, even out of order, we'll get ascending\n// order when iterating with for-in.\nfunction buildNullArray<T extends { __proto__: null }>(): T {\n  return { __proto__: null } as T;\n}\n", "import { TraceMap, presortedDecodedMap, decodedMappings } from './trace-mapping';\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type {\n  DecodedSourceMap,\n  DecodedSourceMapXInput,\n  EncodedSourceMapXInput,\n  SectionedSourceMapXInput,\n  SectionedSourceMapInput,\n  SectionXInput,\n} from './types';\nimport type { SourceMapSegment } from './sourcemap-segment';\n\ntype AnyMap = {\n  new (map: SectionedSourceMapInput, mapUrl?: string | null): TraceMap;\n  (map: SectionedSourceMapInput, mapUrl?: string | null): TraceMap;\n};\n\nexport const AnyMap: AnyMap = function (map, mapUrl) {\n  const parsed = parse(map);\n\n  if (!('sections' in parsed)) {\n    return new TraceMap(parsed as DecodedSourceMapXInput | EncodedSourceMapXInput, mapUrl);\n  }\n\n  const mappings: SourceMapSegment[][] = [];\n  const sources: string[] = [];\n  const sourcesContent: (string | null)[] = [];\n  const names: string[] = [];\n  const ignoreList: number[] = [];\n\n  recurse(\n    parsed,\n    mapUrl,\n    mappings,\n    sources,\n    sourcesContent,\n    names,\n    ignoreList,\n    0,\n    0,\n    Infinity,\n    Infinity,\n  );\n\n  const joined: DecodedSourceMap = {\n    version: 3,\n    file: parsed.file,\n    names,\n    sources,\n    sourcesContent,\n    mappings,\n    ignoreList,\n  };\n\n  return presortedDecodedMap(joined);\n} as AnyMap;\n\nfunction parse<T>(map: T): Exclude<T, string> {\n  return typeof map === 'string' ? JSON.parse(map) : map;\n}\n\nfunction recurse(\n  input: SectionedSourceMapXInput,\n  mapUrl: string | null | undefined,\n  mappings: SourceMapSegment[][],\n  sources: string[],\n  sourcesContent: (string | null)[],\n  names: string[],\n  ignoreList: number[],\n  lineOffset: number,\n  columnOffset: number,\n  stopLine: number,\n  stopColumn: number,\n) {\n  const { sections } = input;\n  for (let i = 0; i < sections.length; i++) {\n    const { map, offset } = sections[i];\n\n    let sl = stopLine;\n    let sc = stopColumn;\n    if (i + 1 < sections.length) {\n      const nextOffset = sections[i + 1].offset;\n      sl = Math.min(stopLine, lineOffset + nextOffset.line);\n\n      if (sl === stopLine) {\n        sc = Math.min(stopColumn, columnOffset + nextOffset.column);\n      } else if (sl < stopLine) {\n        sc = columnOffset + nextOffset.column;\n      }\n    }\n\n    addSection(\n      map,\n      mapUrl,\n      mappings,\n      sources,\n      sourcesContent,\n      names,\n      ignoreList,\n      lineOffset + offset.line,\n      columnOffset + offset.column,\n      sl,\n      sc,\n    );\n  }\n}\n\nfunction addSection(\n  input: SectionXInput['map'],\n  mapUrl: string | null | undefined,\n  mappings: SourceMapSegment[][],\n  sources: string[],\n  sourcesContent: (string | null)[],\n  names: string[],\n  ignoreList: number[],\n  lineOffset: number,\n  columnOffset: number,\n  stopLine: number,\n  stopColumn: number,\n) {\n  const parsed = parse(input);\n  if ('sections' in parsed) return recurse(...(arguments as unknown as Parameters<typeof recurse>));\n\n  const map = new TraceMap(parsed, mapUrl);\n  const sourcesOffset = sources.length;\n  const namesOffset = names.length;\n  const decoded = decodedMappings(map);\n  const { resolvedSources, sourcesContent: contents, ignoreList: ignores } = map;\n\n  append(sources, resolvedSources);\n  append(names, map.names);\n\n  if (contents) append(sourcesContent, contents);\n  else for (let i = 0; i < resolvedSources.length; i++) sourcesContent.push(null);\n\n  if (ignores) for (let i = 0; i < ignores.length; i++) ignoreList.push(ignores[i] + sourcesOffset);\n\n  for (let i = 0; i < decoded.length; i++) {\n    const lineI = lineOffset + i;\n\n    // We can only add so many lines before we step into the range that the next section's map\n    // controls. When we get to the last line, then we'll start checking the segments to see if\n    // they've crossed into the column range. But it may not have any columns that overstep, so we\n    // still need to check that we don't overstep lines, too.\n    if (lineI > stopLine) return;\n\n    // The out line may already exist in mappings (if we're continuing the line started by a\n    // previous section). Or, we may have jumped ahead several lines to start this section.\n    const out = getLine(mappings, lineI);\n    // On the 0th loop, the section's column offset shifts us forward. On all other lines (since the\n    // map can be multiple lines), it doesn't.\n    const cOffset = i === 0 ? columnOffset : 0;\n\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n      const column = cOffset + seg[COLUMN];\n\n      // If this segment steps into the column range that the next section's map controls, we need\n      // to stop early.\n      if (lineI === stopLine && column >= stopColumn) return;\n\n      if (seg.length === 1) {\n        out.push([column]);\n        continue;\n      }\n\n      const sourcesIndex = sourcesOffset + seg[SOURCES_INDEX];\n      const sourceLine = seg[SOURCE_LINE];\n      const sourceColumn = seg[SOURCE_COLUMN];\n      out.push(\n        seg.length === 4\n          ? [column, sourcesIndex, sourceLine, sourceColumn]\n          : [column, sourcesIndex, sourceLine, sourceColumn, namesOffset + seg[NAMES_INDEX]],\n      );\n    }\n  }\n}\n\nfunction append<T>(arr: T[], other: T[]) {\n  for (let i = 0; i < other.length; i++) arr.push(other[i]);\n}\n\nfunction getLine<T>(arr: T[][], index: number): T[] {\n  for (let i = arr.length; i <= index; i++) arr[i] = [];\n  return arr[index];\n}\n", "import { encode, decode } from '@jridgewell/sourcemap-codec';\n\nimport resolve from './resolve';\nimport stripFilename from './strip-filename';\nimport maybeSort from './sort';\nimport buildBySources from './by-source';\nimport {\n  memoizedState,\n  memoizedBinarySearch,\n  upperBound,\n  lowerBound,\n  found as bsFound,\n} from './binary-search';\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n  REV_GENERATED_LINE,\n  REV_GENERATED_COLUMN,\n} from './sourcemap-segment';\n\nimport type { SourceMapSegment, ReverseSegment } from './sourcemap-segment';\nimport type {\n  SourceMapV3,\n  DecodedSourceMap,\n  EncodedSourceMap,\n  InvalidOriginalMapping,\n  OriginalMapping,\n  InvalidGeneratedMapping,\n  GeneratedMapping,\n  SourceMapInput,\n  Needle,\n  SourceNeedle,\n  SourceMap,\n  EachMapping,\n  Bias,\n  XInput,\n} from './types';\nimport type { Source } from './by-source';\nimport type { MemoState } from './binary-search';\n\nexport type { SourceMapSegment } from './sourcemap-segment';\nexport type {\n  SourceMap,\n  DecodedSourceMap,\n  EncodedSourceMap,\n  Section,\n  SectionedSourceMap,\n  SourceMapV3,\n  Bias,\n  EachMapping,\n  GeneratedMapping,\n  InvalidGeneratedMapping,\n  InvalidOriginalMapping,\n  Needle,\n  OriginalMapping,\n  OriginalMapping as Mapping,\n  SectionedSourceMapInput,\n  SourceMapInput,\n  SourceNeedle,\n  XInput,\n  EncodedSourceMapXInput,\n  DecodedSourceMapXInput,\n  SectionedSourceMapXInput,\n  SectionXInput,\n} from './types';\n\ninterface PublicMap {\n  _encoded: TraceMap['_encoded'];\n  _decoded: TraceMap['_decoded'];\n  _decodedMemo: TraceMap['_decodedMemo'];\n  _bySources: TraceMap['_bySources'];\n  _bySourceMemos: TraceMap['_bySourceMemos'];\n}\n\nconst LINE_GTR_ZERO = '`line` must be greater than 0 (lines start at line 1)';\nconst COL_GTR_EQ_ZERO = '`column` must be greater than or equal to 0 (columns start at column 0)';\n\nexport const LEAST_UPPER_BOUND = -1;\nexport const GREATEST_LOWER_BOUND = 1;\n\nexport { AnyMap } from './any-map';\n\nexport class TraceMap implements SourceMap {\n  declare version: SourceMapV3['version'];\n  declare file: SourceMapV3['file'];\n  declare names: SourceMapV3['names'];\n  declare sourceRoot: SourceMapV3['sourceRoot'];\n  declare sources: SourceMapV3['sources'];\n  declare sourcesContent: SourceMapV3['sourcesContent'];\n  declare ignoreList: SourceMapV3['ignoreList'];\n\n  declare resolvedSources: string[];\n  private declare _encoded: string | undefined;\n\n  private declare _decoded: SourceMapSegment[][] | undefined;\n  private declare _decodedMemo: MemoState;\n\n  private declare _bySources: Source[] | undefined;\n  private declare _bySourceMemos: MemoState[] | undefined;\n\n  constructor(map: SourceMapInput, mapUrl?: string | null) {\n    const isString = typeof map === 'string';\n\n    if (!isString && (map as unknown as { _decodedMemo: any })._decodedMemo) return map as TraceMap;\n\n    const parsed = (isString ? JSON.parse(map) : map) as DecodedSourceMap | EncodedSourceMap;\n\n    const { version, file, names, sourceRoot, sources, sourcesContent } = parsed;\n    this.version = version;\n    this.file = file;\n    this.names = names || [];\n    this.sourceRoot = sourceRoot;\n    this.sources = sources;\n    this.sourcesContent = sourcesContent;\n    this.ignoreList = parsed.ignoreList || (parsed as XInput).x_google_ignoreList || undefined;\n\n    const from = resolve(sourceRoot || '', stripFilename(mapUrl));\n    this.resolvedSources = sources.map((s) => resolve(s || '', from));\n\n    const { mappings } = parsed;\n    if (typeof mappings === 'string') {\n      this._encoded = mappings;\n      this._decoded = undefined;\n    } else {\n      this._encoded = undefined;\n      this._decoded = maybeSort(mappings, isString);\n    }\n\n    this._decodedMemo = memoizedState();\n    this._bySources = undefined;\n    this._bySourceMemos = undefined;\n  }\n}\n\n/**\n * Typescript doesn't allow friend access to private fields, so this just casts the map into a type\n * with public access modifiers.\n */\nfunction cast(map: unknown): PublicMap {\n  return map as any;\n}\n\n/**\n * Returns the encoded (VLQ string) form of the SourceMap's mappings field.\n */\nexport function encodedMappings(map: TraceMap): EncodedSourceMap['mappings'] {\n  return (cast(map)._encoded ??= encode(cast(map)._decoded!));\n}\n\n/**\n * Returns the decoded (array of lines of segments) form of the SourceMap's mappings field.\n */\nexport function decodedMappings(map: TraceMap): Readonly<DecodedSourceMap['mappings']> {\n  return (cast(map)._decoded ||= decode(cast(map)._encoded!));\n}\n\n/**\n * A low-level API to find the segment associated with a generated line/column (think, from a\n * stack trace). Line and column here are 0-based, unlike `originalPositionFor`.\n */\nexport function traceSegment(\n  map: TraceMap,\n  line: number,\n  column: number,\n): Readonly<SourceMapSegment> | null {\n  const decoded = decodedMappings(map);\n\n  // It's common for parent source maps to have pointers to lines that have no\n  // mapping (like a \"//# sourceMappingURL=\") at the end of the child file.\n  if (line >= decoded.length) return null;\n\n  const segments = decoded[line];\n  const index = traceSegmentInternal(\n    segments,\n    cast(map)._decodedMemo,\n    line,\n    column,\n    GREATEST_LOWER_BOUND,\n  );\n\n  return index === -1 ? null : segments[index];\n}\n\n/**\n * A higher-level API to find the source/line/column associated with a generated line/column\n * (think, from a stack trace). Line is 1-based, but column is 0-based, due to legacy behavior in\n * `source-map` library.\n */\nexport function originalPositionFor(\n  map: TraceMap,\n  needle: Needle,\n): OriginalMapping | InvalidOriginalMapping {\n  let { line, column, bias } = needle;\n  line--;\n  if (line < 0) throw new Error(LINE_GTR_ZERO);\n  if (column < 0) throw new Error(COL_GTR_EQ_ZERO);\n\n  const decoded = decodedMappings(map);\n\n  // It's common for parent source maps to have pointers to lines that have no\n  // mapping (like a \"//# sourceMappingURL=\") at the end of the child file.\n  if (line >= decoded.length) return OMapping(null, null, null, null);\n\n  const segments = decoded[line];\n  const index = traceSegmentInternal(\n    segments,\n    cast(map)._decodedMemo,\n    line,\n    column,\n    bias || GREATEST_LOWER_BOUND,\n  );\n\n  if (index === -1) return OMapping(null, null, null, null);\n\n  const segment = segments[index];\n  if (segment.length === 1) return OMapping(null, null, null, null);\n\n  const { names, resolvedSources } = map;\n  return OMapping(\n    resolvedSources[segment[SOURCES_INDEX]],\n    segment[SOURCE_LINE] + 1,\n    segment[SOURCE_COLUMN],\n    segment.length === 5 ? names[segment[NAMES_INDEX]] : null,\n  );\n}\n\n/**\n * Finds the generated line/column position of the provided source/line/column source position.\n */\nexport function generatedPositionFor(\n  map: TraceMap,\n  needle: SourceNeedle,\n): GeneratedMapping | InvalidGeneratedMapping {\n  const { source, line, column, bias } = needle;\n  return generatedPosition(map, source, line, column, bias || GREATEST_LOWER_BOUND, false);\n}\n\n/**\n * Finds all generated line/column positions of the provided source/line/column source position.\n */\nexport function allGeneratedPositionsFor(map: TraceMap, needle: SourceNeedle): GeneratedMapping[] {\n  const { source, line, column, bias } = needle;\n  // SourceMapConsumer uses LEAST_UPPER_BOUND for some reason, so we follow suit.\n  return generatedPosition(map, source, line, column, bias || LEAST_UPPER_BOUND, true);\n}\n\n/**\n * Iterates each mapping in generated position order.\n */\nexport function eachMapping(map: TraceMap, cb: (mapping: EachMapping) => void): void {\n  const decoded = decodedMappings(map);\n  const { names, resolvedSources } = map;\n\n  for (let i = 0; i < decoded.length; i++) {\n    const line = decoded[i];\n    for (let j = 0; j < line.length; j++) {\n      const seg = line[j];\n\n      const generatedLine = i + 1;\n      const generatedColumn = seg[0];\n      let source = null;\n      let originalLine = null;\n      let originalColumn = null;\n      let name = null;\n      if (seg.length !== 1) {\n        source = resolvedSources[seg[1]];\n        originalLine = seg[2] + 1;\n        originalColumn = seg[3];\n      }\n      if (seg.length === 5) name = names[seg[4]];\n\n      cb({\n        generatedLine,\n        generatedColumn,\n        source,\n        originalLine,\n        originalColumn,\n        name,\n      } as EachMapping);\n    }\n  }\n}\n\nfunction sourceIndex(map: TraceMap, source: string): number {\n  const { sources, resolvedSources } = map;\n  let index = sources.indexOf(source);\n  if (index === -1) index = resolvedSources.indexOf(source);\n  return index;\n}\n\n/**\n * Retrieves the source content for a particular source, if its found. Returns null if not.\n */\nexport function sourceContentFor(map: TraceMap, source: string): string | null {\n  const { sourcesContent } = map;\n  if (sourcesContent == null) return null;\n  const index = sourceIndex(map, source);\n  return index === -1 ? null : sourcesContent[index];\n}\n\n/**\n * Determines if the source is marked to ignore by the source map.\n */\nexport function isIgnored(map: TraceMap, source: string): boolean {\n  const { ignoreList } = map;\n  if (ignoreList == null) return false;\n  const index = sourceIndex(map, source);\n  return index === -1 ? false : ignoreList.includes(index);\n}\n\n/**\n * A helper that skips sorting of the input map's mappings array, which can be expensive for larger\n * maps.\n */\nexport function presortedDecodedMap(map: DecodedSourceMap, mapUrl?: string): TraceMap {\n  const tracer = new TraceMap(clone(map, []), mapUrl);\n  cast(tracer)._decoded = map.mappings;\n  return tracer;\n}\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function decodedMap(\n  map: TraceMap,\n): Omit<DecodedSourceMap, 'mappings'> & { mappings: readonly SourceMapSegment[][] } {\n  return clone(map, decodedMappings(map));\n}\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport function encodedMap(map: TraceMap): EncodedSourceMap {\n  return clone(map, encodedMappings(map));\n}\n\nfunction clone<T extends string | readonly SourceMapSegment[][]>(\n  map: TraceMap | DecodedSourceMap,\n  mappings: T,\n): T extends string ? EncodedSourceMap : DecodedSourceMap {\n  return {\n    version: map.version,\n    file: map.file,\n    names: map.names,\n    sourceRoot: map.sourceRoot,\n    sources: map.sources,\n    sourcesContent: map.sourcesContent,\n    mappings,\n    ignoreList: map.ignoreList || (map as XInput).x_google_ignoreList,\n  } as any;\n}\n\nfunction OMapping(source: null, line: null, column: null, name: null): InvalidOriginalMapping;\nfunction OMapping(\n  source: string,\n  line: number,\n  column: number,\n  name: string | null,\n): OriginalMapping;\nfunction OMapping(\n  source: string | null,\n  line: number | null,\n  column: number | null,\n  name: string | null,\n): OriginalMapping | InvalidOriginalMapping {\n  return { source, line, column, name } as any;\n}\n\nfunction GMapping(line: null, column: null): InvalidGeneratedMapping;\nfunction GMapping(line: number, column: number): GeneratedMapping;\nfunction GMapping(\n  line: number | null,\n  column: number | null,\n): GeneratedMapping | InvalidGeneratedMapping {\n  return { line, column } as any;\n}\n\nfunction traceSegmentInternal(\n  segments: SourceMapSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number;\nfunction traceSegmentInternal(\n  segments: ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number;\nfunction traceSegmentInternal(\n  segments: SourceMapSegment[] | ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): number {\n  let index = memoizedBinarySearch(segments, column, memo, line);\n  if (bsFound) {\n    index = (bias === LEAST_UPPER_BOUND ? upperBound : lowerBound)(segments, column, index);\n  } else if (bias === LEAST_UPPER_BOUND) index++;\n\n  if (index === -1 || index === segments.length) return -1;\n  return index;\n}\n\nfunction sliceGeneratedPositions(\n  segments: ReverseSegment[],\n  memo: MemoState,\n  line: number,\n  column: number,\n  bias: Bias,\n): GeneratedMapping[] {\n  let min = traceSegmentInternal(segments, memo, line, column, GREATEST_LOWER_BOUND);\n\n  // We ignored the bias when tracing the segment so that we're guarnateed to find the first (in\n  // insertion order) segment that matched. Even if we did respect the bias when tracing, we would\n  // still need to call `lowerBound()` to find the first segment, which is slower than just looking\n  // for the GREATEST_LOWER_BOUND to begin with. The only difference that matters for us is when the\n  // binary search didn't match, in which case GREATEST_LOWER_BOUND just needs to increment to\n  // match LEAST_UPPER_BOUND.\n  if (!bsFound && bias === LEAST_UPPER_BOUND) min++;\n\n  if (min === -1 || min === segments.length) return [];\n\n  // We may have found the segment that started at an earlier column. If this is the case, then we\n  // need to slice all generated segments that match _that_ column, because all such segments span\n  // to our desired column.\n  const matchedColumn = bsFound ? column : segments[min][COLUMN];\n\n  // The binary search is not guaranteed to find the lower bound when a match wasn't found.\n  if (!bsFound) min = lowerBound(segments, matchedColumn, min);\n  const max = upperBound(segments, matchedColumn, min);\n\n  const result = [];\n  for (; min <= max; min++) {\n    const segment = segments[min];\n    result.push(GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]));\n  }\n  return result;\n}\n\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: false,\n): GeneratedMapping | InvalidGeneratedMapping;\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: true,\n): GeneratedMapping[];\nfunction generatedPosition(\n  map: TraceMap,\n  source: string,\n  line: number,\n  column: number,\n  bias: Bias,\n  all: boolean,\n): GeneratedMapping | InvalidGeneratedMapping | GeneratedMapping[] {\n  line--;\n  if (line < 0) throw new Error(LINE_GTR_ZERO);\n  if (column < 0) throw new Error(COL_GTR_EQ_ZERO);\n\n  const { sources, resolvedSources } = map;\n  let sourceIndex = sources.indexOf(source);\n  if (sourceIndex === -1) sourceIndex = resolvedSources.indexOf(source);\n  if (sourceIndex === -1) return all ? [] : GMapping(null, null);\n\n  const generated = (cast(map)._bySources ||= buildBySources(\n    decodedMappings(map),\n    (cast(map)._bySourceMemos = sources.map(memoizedState)),\n  ));\n\n  const segments = generated[sourceIndex][line];\n  if (segments == null) return all ? [] : GMapping(null, null);\n\n  const memo = cast(map)._bySourceMemos![sourceIndex];\n\n  if (all) return sliceGeneratedPositions(segments, memo, line, column, bias);\n\n  const index = traceSegmentInternal(segments, memo, line, column, bias);\n  if (index === -1) return GMapping(null, null);\n\n  const segment = segments[index];\n  return GMapping(segment[REV_GENERATED_LINE] + 1, segment[REV_GENERATED_COLUMN]);\n}\n"], "names": ["encode", "decode", "bsFound"], "mappings": ";;;;;;IAEc,SAAU,OAAO,CAAC,KAAa,EAAE,IAAwB,EAAA;;;;QAIrE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;YAAE,IAAI,IAAI,GAAG,CAAC;IAE7C,IAAA,OAAO,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACjC;;ICTA;;IAEG;IACqB,SAAA,aAAa,CAAC,IAA+B,EAAA;IACnE,IAAA,IAAI,CAAC,IAAI;IAAE,QAAA,OAAO,EAAE,CAAC;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IAClC;;ICQO,MAAM,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,WAAW,GAAG,CAAC,CAAC;IACtB,MAAM,aAAa,GAAG,CAAC,CAAC;IACxB,MAAM,WAAW,GAAG,CAAC,CAAC;IAEtB,MAAM,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAM,oBAAoB,GAAG,CAAC;;IClBvB,SAAU,SAAS,CAC/B,QAA8B,EAC9B,KAAc,EAAA;QAEd,MAAM,aAAa,GAAG,uBAAuB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC3D,IAAA,IAAI,aAAa,KAAK,QAAQ,CAAC,MAAM;IAAE,QAAA,OAAO,QAAQ,CAAC;;;IAIvD,IAAA,IAAI,CAAC,KAAK;IAAE,QAAA,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,uBAAuB,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE;IAC7F,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;SAChD;IACD,IAAA,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,SAAS,uBAAuB,CAAC,QAA8B,EAAE,KAAa,EAAA;IAC5E,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IAC5C,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAAE,YAAA,OAAO,CAAC,CAAC;SACtC;QACD,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,SAAS,QAAQ,CAAC,IAAwB,EAAA;IACxC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,QAAA,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;IACzC,YAAA,OAAO,KAAK,CAAC;aACd;SACF;IACD,IAAA,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,YAAY,CAAC,IAAwB,EAAE,KAAc,EAAA;IAC5D,IAAA,IAAI,CAAC,KAAK;IAAE,QAAA,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAChC,IAAA,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACnC,CAAC;IAED,SAAS,cAAc,CAAC,CAAmB,EAAE,CAAmB,EAAA;QAC9D,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;IAC/B;;ICnCO,IAAI,KAAK,GAAG,KAAK,CAAC;IAEzB;;;;;;;;;;;;;;;IAeG;IACG,SAAU,YAAY,CAC1B,QAA+C,EAC/C,MAAc,EACd,GAAW,EACX,IAAY,EAAA;IAEZ,IAAA,OAAO,GAAG,IAAI,IAAI,EAAE;IAClB,QAAA,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;YACtC,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;IAE3C,QAAA,IAAI,GAAG,KAAK,CAAC,EAAE;gBACb,KAAK,GAAG,IAAI,CAAC;IACb,YAAA,OAAO,GAAG,CAAC;aACZ;IAED,QAAA,IAAI,GAAG,GAAG,CAAC,EAAE;IACX,YAAA,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;aACf;iBAAM;IACL,YAAA,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;aAChB;SACF;QAED,KAAK,GAAG,KAAK,CAAC;QACd,OAAO,GAAG,GAAG,CAAC,CAAC;IACjB,CAAC;aAEe,UAAU,CACxB,QAA+C,EAC/C,MAAc,EACd,KAAa,EAAA;IAEb,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;YACxD,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,MAAM;gBAAE,MAAM;SAC3C;IACD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;aAEe,UAAU,CACxB,QAA+C,EAC/C,MAAc,EACd,KAAa,EAAA;IAEb,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;YAC3C,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,MAAM;gBAAE,MAAM;SAC3C;IACD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;aAEe,aAAa,GAAA;QAC3B,OAAO;YACL,OAAO,EAAE,CAAC,CAAC;YACX,UAAU,EAAE,CAAC,CAAC;YACd,SAAS,EAAE,CAAC,CAAC;SACd,CAAC;IACJ,CAAC;IAED;;;IAGG;IACG,SAAU,oBAAoB,CAClC,QAA+C,EAC/C,MAAc,EACd,KAAgB,EAChB,GAAW,EAAA;QAEX,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QAEjD,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,IAAA,IAAI,IAAI,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,IAAA,IAAI,GAAG,KAAK,OAAO,EAAE;IACnB,QAAA,IAAI,MAAM,KAAK,UAAU,EAAE;IACzB,YAAA,KAAK,GAAG,SAAS,KAAK,CAAC,CAAC,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC;IACnE,YAAA,OAAO,SAAS,CAAC;aAClB;IAED,QAAA,IAAI,MAAM,IAAI,UAAU,EAAE;;IAExB,YAAA,GAAG,GAAG,SAAS,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;aACxC;iBAAM;gBACL,IAAI,GAAG,SAAS,CAAC;aAClB;SACF;IACD,IAAA,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;IACpB,IAAA,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;IAE1B,IAAA,QAAQ,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE;IACvE;;ICvGA;IACA;IACc,SAAU,cAAc,CACpC,OAAsC,EACtC,KAAkB,EAAA;QAElB,MAAM,OAAO,GAAa,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAEpD,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACvC,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;oBAAE,SAAS;IAE/B,YAAA,MAAM,WAAW,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC;IACvC,YAAA,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;IACpC,YAAA,MAAM,YAAY,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC;IACxC,YAAA,MAAM,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IAC5C,YAAA,MAAM,YAAY,IAAI,cAAc,CAAC,UAAU,CAAzB,KAAA,cAAc,CAAC,UAAU,CAAM,GAAA,EAAE,EAAC,CAAC;IACzD,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;;;;;IAMhC,YAAA,IAAI,KAAK,GAAG,UAAU,CACpB,YAAY,EACZ,YAAY,EACZ,oBAAoB,CAAC,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE,UAAU,CAAC,CACnE,CAAC;IAEF,YAAA,IAAI,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC;IACzB,YAAA,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,YAAY,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;aAC7D;SACF;IAED,IAAA,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;IACpD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;YACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;SACzB;IACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;IACA;IACA;IACA;IACA;IACA,SAAS,cAAc,GAAA;IACrB,IAAA,OAAO,EAAE,SAAS,EAAE,IAAI,EAAO,CAAC;IAClC;;ACxCa,UAAA,MAAM,GAAW,UAAU,GAAG,EAAE,MAAM,EAAA;IACjD,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IAE1B,IAAA,IAAI,EAAE,UAAU,IAAI,MAAM,CAAC,EAAE;IAC3B,QAAA,OAAO,IAAI,QAAQ,CAAC,MAAyD,EAAE,MAAM,CAAC,CAAC;SACxF;QAED,MAAM,QAAQ,GAAyB,EAAE,CAAC;QAC1C,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,cAAc,GAAsB,EAAE,CAAC;QAC7C,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,OAAO,CACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,OAAO,EACP,cAAc,EACd,KAAK,EACL,UAAU,EACV,CAAC,EACD,CAAC,EACD,QAAQ,EACR,QAAQ,CACT,CAAC;IAEF,IAAA,MAAM,MAAM,GAAqB;IAC/B,QAAA,OAAO,EAAE,CAAC;YACV,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,KAAK;YACL,OAAO;YACP,cAAc;YACd,QAAQ;YACR,UAAU;SACX,CAAC;IAEF,IAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;IACrC,EAAY;IAEZ,SAAS,KAAK,CAAI,GAAM,EAAA;IACtB,IAAA,OAAO,OAAO,GAAG,KAAK,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IACzD,CAAC;IAED,SAAS,OAAO,CACd,KAA+B,EAC/B,MAAiC,EACjC,QAA8B,EAC9B,OAAiB,EACjB,cAAiC,EACjC,KAAe,EACf,UAAoB,EACpB,UAAkB,EAClB,YAAoB,EACpB,QAAgB,EAChB,UAAkB,EAAA;IAElB,IAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;IAC3B,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEpC,IAAI,EAAE,GAAG,QAAQ,CAAC;YAClB,IAAI,EAAE,GAAG,UAAU,CAAC;YACpB,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE;gBAC3B,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1C,YAAA,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAEtD,YAAA,IAAI,EAAE,KAAK,QAAQ,EAAE;IACnB,gBAAA,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;iBAC7D;IAAM,iBAAA,IAAI,EAAE,GAAG,QAAQ,EAAE;IACxB,gBAAA,EAAE,GAAG,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;iBACvC;aACF;IAED,QAAA,UAAU,CACR,GAAG,EACH,MAAM,EACN,QAAQ,EACR,OAAO,EACP,cAAc,EACd,KAAK,EACL,UAAU,EACV,UAAU,GAAG,MAAM,CAAC,IAAI,EACxB,YAAY,GAAG,MAAM,CAAC,MAAM,EAC5B,EAAE,EACF,EAAE,CACH,CAAC;SACH;IACH,CAAC;IAED,SAAS,UAAU,CACjB,KAA2B,EAC3B,MAAiC,EACjC,QAA8B,EAC9B,OAAiB,EACjB,cAAiC,EACjC,KAAe,EACf,UAAoB,EACpB,UAAkB,EAClB,YAAoB,EACpB,QAAgB,EAChB,UAAkB,EAAA;IAElB,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,UAAU,IAAI,MAAM;IAAE,QAAA,OAAO,OAAO,CAAC,GAAI,SAAmD,CAAC,CAAC;QAElG,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,IAAA,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;IACrC,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;IACjC,IAAA,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IACrC,IAAA,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;IAE/E,IAAA,MAAM,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACjC,IAAA,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;IAEzB,IAAA,IAAI,QAAQ;IAAE,QAAA,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;;IAC1C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE;IAAE,YAAA,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEhF,IAAA,IAAI,OAAO;IAAE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE;gBAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;IAElG,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACvC,QAAA,MAAM,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;;;;;YAM7B,IAAI,KAAK,GAAG,QAAQ;gBAAE,OAAO;;;YAI7B,MAAM,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;;;IAGrC,QAAA,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,CAAC,CAAC;IAE3C,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,MAAM,GAAG,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;;;IAIrC,YAAA,IAAI,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAI,UAAU;oBAAE,OAAO;IAEvD,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;IACpB,gBAAA,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;oBACnB,SAAS;iBACV;gBAED,MAAM,YAAY,GAAG,aAAa,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC;IACxD,YAAA,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC;IACpC,YAAA,MAAM,YAAY,GAAG,GAAG,CAAC,aAAa,CAAC,CAAC;IACxC,YAAA,GAAG,CAAC,IAAI,CACN,GAAG,CAAC,MAAM,KAAK,CAAC;sBACZ,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC;IAClD,kBAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CACrF,CAAC;aACH;SACF;IACH,CAAC;IAED,SAAS,MAAM,CAAI,GAAQ,EAAE,KAAU,EAAA;IACrC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,SAAS,OAAO,CAAI,GAAU,EAAE,KAAa,EAAA;IAC3C,IAAA,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE;IAAE,QAAA,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IACtD,IAAA,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;IACpB;;ICpHA,MAAM,aAAa,GAAG,uDAAuD,CAAC;IAC9E,MAAM,eAAe,GAAG,yEAAyE,CAAC;AAErF,UAAA,iBAAiB,GAAG,CAAC,EAAE;AAC7B,UAAM,oBAAoB,GAAG,EAAE;UAIzB,QAAQ,CAAA;QAkBnB,WAAY,CAAA,GAAmB,EAAE,MAAsB,EAAA;IACrD,QAAA,MAAM,QAAQ,GAAG,OAAO,GAAG,KAAK,QAAQ,CAAC;IAEzC,QAAA,IAAI,CAAC,QAAQ,IAAK,GAAwC,CAAC,YAAY;IAAE,YAAA,OAAO,GAAe,CAAC;IAEhG,QAAA,MAAM,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,CAAwC,CAAC;IAEzF,QAAA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC;IAC7E,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;IACzB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC7B,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACrC,QAAA,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAK,MAAiB,CAAC,mBAAmB,IAAI,SAAS,CAAC;IAE3F,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9D,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;IAElE,QAAA,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;IAC5B,QAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;IAChC,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,YAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;aAC3B;iBAAM;IACL,YAAA,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;gBAC1B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAC/C;IAED,QAAA,IAAI,CAAC,YAAY,GAAG,aAAa,EAAE,CAAC;IACpC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC5B,QAAA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;SACjC;IACF,CAAA;IAED;;;IAGG;IACH,SAAS,IAAI,CAAC,GAAY,EAAA;IACxB,IAAA,OAAO,GAAU,CAAC;IACpB,CAAC;IAED;;IAEG;IACG,SAAU,eAAe,CAAC,GAAa,EAAA;;;QAC3C,QAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAC,IAAI,CAAC,GAAG,CAAC,EAAC,QAAQ,uCAAR,QAAQ,GAAKA,qBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAS,CAAC,GAAE;IAC9D,CAAC;IAED;;IAEG;IACG,SAAU,eAAe,CAAC,GAAa,EAAA;;QAC3C,QAAO,CAAA,EAAA,GAAC,IAAI,CAAC,GAAG,CAAC,EAAC,QAAQ,QAAR,QAAQ,GAAKC,qBAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAS,CAAC,GAAE;IAC9D,CAAC;IAED;;;IAGG;aACa,YAAY,CAC1B,GAAa,EACb,IAAY,EACZ,MAAc,EAAA;IAEd,IAAA,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;;;IAIrC,IAAA,IAAI,IAAI,IAAI,OAAO,CAAC,MAAM;IAAE,QAAA,OAAO,IAAI,CAAC;IAExC,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAA,MAAM,KAAK,GAAG,oBAAoB,CAChC,QAAQ,EACR,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,EACtB,IAAI,EACJ,MAAM,EACN,oBAAoB,CACrB,CAAC;IAEF,IAAA,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAED;;;;IAIG;IACa,SAAA,mBAAmB,CACjC,GAAa,EACb,MAAc,EAAA;QAEd,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;IACpC,IAAA,IAAI,EAAE,CAAC;QACP,IAAI,IAAI,GAAG,CAAC;IAAE,QAAA,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,MAAM,GAAG,CAAC;IAAE,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IAEjD,IAAA,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;;;IAIrC,IAAA,IAAI,IAAI,IAAI,OAAO,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAEpE,IAAA,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,oBAAoB,CAChC,QAAQ,EACR,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,EACtB,IAAI,EACJ,MAAM,EACN,IAAI,IAAI,oBAAoB,CAC7B,CAAC;QAEF,IAAI,KAAK,KAAK,CAAC,CAAC;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAE1D,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,IAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAElE,IAAA,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;IACvC,IAAA,OAAO,QAAQ,CACb,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EACvC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EACxB,OAAO,CAAC,aAAa,CAAC,EACtB,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI,CAC1D,CAAC;IACJ,CAAC;IAED;;IAEG;IACa,SAAA,oBAAoB,CAClC,GAAa,EACb,MAAoB,EAAA;QAEpB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;IAC9C,IAAA,OAAO,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,oBAAoB,EAAE,KAAK,CAAC,CAAC;IAC3F,CAAC;IAED;;IAEG;IACa,SAAA,wBAAwB,CAAC,GAAa,EAAE,MAAoB,EAAA;QAC1E,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;;IAE9C,IAAA,OAAO,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,IAAI,iBAAiB,EAAE,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;IAEG;IACa,SAAA,WAAW,CAAC,GAAa,EAAE,EAAkC,EAAA;IAC3E,IAAA,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IACrC,IAAA,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;IAEvC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACvC,QAAA,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACxB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACpC,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAEpB,YAAA,MAAM,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,YAAA,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC/B,IAAI,MAAM,GAAG,IAAI,CAAC;gBAClB,IAAI,YAAY,GAAG,IAAI,CAAC;gBACxB,IAAI,cAAc,GAAG,IAAI,CAAC;gBAC1B,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjC,gBAAA,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1B,gBAAA,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;iBACzB;IACD,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;oBAAE,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE3C,YAAA,EAAE,CAAC;oBACD,aAAa;oBACb,eAAe;oBACf,MAAM;oBACN,YAAY;oBACZ,cAAc;oBACd,IAAI;IACU,aAAA,CAAC,CAAC;aACnB;SACF;IACH,CAAC;IAED,SAAS,WAAW,CAAC,GAAa,EAAE,MAAc,EAAA;IAChD,IAAA,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;QACzC,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,KAAK,KAAK,CAAC,CAAC;IAAE,QAAA,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC1D,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACa,SAAA,gBAAgB,CAAC,GAAa,EAAE,MAAc,EAAA;IAC5D,IAAA,MAAM,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC;QAC/B,IAAI,cAAc,IAAI,IAAI;IAAE,QAAA,OAAO,IAAI,CAAC;QACxC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACvC,IAAA,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACrD,CAAC;IAED;;IAEG;IACa,SAAA,SAAS,CAAC,GAAa,EAAE,MAAc,EAAA;IACrD,IAAA,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;QAC3B,IAAI,UAAU,IAAI,IAAI;IAAE,QAAA,OAAO,KAAK,CAAC;QACrC,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACvC,IAAA,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC3D,CAAC;IAED;;;IAGG;IACa,SAAA,mBAAmB,CAAC,GAAqB,EAAE,MAAe,EAAA;IACxE,IAAA,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;IACrC,IAAA,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;IAGG;IACG,SAAU,UAAU,CACxB,GAAa,EAAA;QAEb,OAAO,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED;;;IAGG;IACG,SAAU,UAAU,CAAC,GAAa,EAAA;QACtC,OAAO,KAAK,CAAC,GAAG,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1C,CAAC;IAED,SAAS,KAAK,CACZ,GAAgC,EAChC,QAAW,EAAA;QAEX,OAAO;YACL,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,cAAc,EAAE,GAAG,CAAC,cAAc;YAClC,QAAQ;IACR,QAAA,UAAU,EAAE,GAAG,CAAC,UAAU,IAAK,GAAc,CAAC,mBAAmB;SAC3D,CAAC;IACX,CAAC;IASD,SAAS,QAAQ,CACf,MAAqB,EACrB,IAAmB,EACnB,MAAqB,EACrB,IAAmB,EAAA;QAEnB,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAS,CAAC;IAC/C,CAAC;IAID,SAAS,QAAQ,CACf,IAAmB,EACnB,MAAqB,EAAA;IAErB,IAAA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAS,CAAC;IACjC,CAAC;IAgBD,SAAS,oBAAoB,CAC3B,QAA+C,EAC/C,IAAe,EACf,IAAY,EACZ,MAAc,EACd,IAAU,EAAA;IAEV,IAAA,IAAI,KAAK,GAAG,oBAAoB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/D,IAAIC,KAAO,EAAE;YACX,KAAK,GAAG,CAAC,IAAI,KAAK,iBAAiB,GAAG,UAAU,GAAG,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;SACzF;aAAM,IAAI,IAAI,KAAK,iBAAiB;IAAE,QAAA,KAAK,EAAE,CAAC;QAE/C,IAAI,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,MAAM;YAAE,OAAO,CAAC,CAAC,CAAC;IACzD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,uBAAuB,CAC9B,QAA0B,EAC1B,IAAe,EACf,IAAY,EACZ,MAAc,EACd,IAAU,EAAA;IAEV,IAAA,IAAI,GAAG,GAAG,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,oBAAoB,CAAC,CAAC;;;;;;;IAQnF,IAAA,IAAI,CAACA,KAAO,IAAI,IAAI,KAAK,iBAAiB;IAAE,QAAA,GAAG,EAAE,CAAC;QAElD,IAAI,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,KAAK,QAAQ,CAAC,MAAM;IAAE,QAAA,OAAO,EAAE,CAAC;;;;IAKrD,IAAA,MAAM,aAAa,GAAGA,KAAO,GAAG,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;;IAG/D,IAAA,IAAI,CAACA,KAAO;YAAE,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;QAC7D,MAAM,GAAG,GAAG,UAAU,CAAC,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC;QAErD,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAA,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE;IACxB,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC9B,QAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;SACvF;IACD,IAAA,OAAO,MAAM,CAAC;IAChB,CAAC;IAkBD,SAAS,iBAAiB,CACxB,GAAa,EACb,MAAc,EACd,IAAY,EACZ,MAAc,EACd,IAAU,EACV,GAAY,EAAA;;IAEZ,IAAA,IAAI,EAAE,CAAC;QACP,IAAI,IAAI,GAAG,CAAC;IAAE,QAAA,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,MAAM,GAAG,CAAC;IAAE,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IAEjD,IAAA,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,GAAG,CAAC;QACzC,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,WAAW,KAAK,CAAC,CAAC;IAAE,QAAA,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACtE,IAAI,WAAW,KAAK,CAAC,CAAC;IAAE,QAAA,OAAO,GAAG,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAE/D,IAAA,MAAM,SAAS,IAAG,CAAA,EAAA,GAAC,IAAI,CAAC,GAAG,CAAC,EAAC,UAAU,KAAA,EAAA,CAAV,UAAU,GAAK,cAAc,CACxD,eAAe,CAAC,GAAG,CAAC,GACnB,IAAI,CAAC,GAAG,CAAC,CAAC,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,EACvD,EAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,QAAQ,IAAI,IAAI;IAAE,QAAA,OAAO,GAAG,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAE7D,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,WAAW,CAAC,CAAC;IAEpD,IAAA,IAAI,GAAG;IAAE,QAAA,OAAO,uBAAuB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAE5E,IAAA,MAAM,KAAK,GAAG,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QACvE,IAAI,KAAK,KAAK,CAAC,CAAC;IAAE,QAAA,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAE9C,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,IAAA,OAAO,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAClF;;;;;;;;;;;;;;;;;;;;;;;"}