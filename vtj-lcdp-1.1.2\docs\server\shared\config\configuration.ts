export interface Database {
  /**
   * 数据库主机
   */
  host: string;
  /**
   * 数据库端口
   */
  port: number;
  /**
   * 登录用户名
   */
  username: string;
  /**
   * 密码
   */
  password: string;
  /**
   * 数据库名
   */
  database: string;
  /**
   * 同步，设置 synchronize: true 不应在生产中使用 - 否则你可能会丢失生产数据。
   */
  synchronize: boolean;
}

export interface Gitte {
  /**
   * Client ID
   */
  clientId: string;
  /**
   * Client Secret
   */
  clientSecret: string;
}

export interface AliOSS {
  region: string;
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
}

export interface AI {
  baseURL: string;
  apiKey: string;
  apiKeys: string[];
  openrouter: string;
}

export interface MailServer {
  host: string;
  port: number;
  user: string;
  pass: string;
}

export interface Configuration {
  /**
   * 服务器启动端口号
   */
  port: number;

  /**
   * 数据库配置
   */
  database: Database;

  /**
   * gitee 应用配置
   */
  gitee: Gitte;

  /**
   * OSS
   */
  oss: AliOSS;

  ai: AI;

  mail: MailServer;
}

export const configuration: () => Configuration = () => {
  return {
    port: parseInt(process.env.PORT, 10) || 3000,
    database: {
      host: process.env.DATABASE_HOST || '127.0.0.1',
      port: parseInt(process.env.DATABASE_PORT, 10) || 3306,
      username: process.env.DATABASE_USER || '',
      password: process.env.DATABASE_PASSWORD || '',
      database: process.env.DATABASE_NAME || '',
      synchronize: process.env.NODE_ENV === 'development'
      // logging: process.env.NODE_ENV === 'development'
    },
    gitee: {
      clientId: '',
      clientSecret: ''
    },
    oss: {
      region: '',
      accessKeyId: '',
      accessKeySecret: '',
      bucket: ''
    },
    ai: {
      baseURL: 'https://api.deepseek.com/v1',
      apiKey: '',
      apiKeys: [],
      openrouter: 'https://openrouter.ai/api/v1'
    },
    mail: {
      host: '',
      port: 465,
      user: '',
      pass: ''
    }
  };
};
