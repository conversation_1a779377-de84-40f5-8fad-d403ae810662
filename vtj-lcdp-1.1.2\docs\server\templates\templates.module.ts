import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TemplatesService } from './templates.service';
import { TemplatesController } from './templates.controller';
import { Template } from './entities/template.entity';
import { TemplateDsl } from './entities/template-dsl.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Template, TemplateDsl])],
  controllers: [TemplatesController],
  providers: [TemplatesService],
  exports: [TypeOrmModule, TemplatesService]
})
export class TemplatesModule {}
