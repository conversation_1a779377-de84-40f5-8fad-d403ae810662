import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  ForbiddenException
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { pathToRegexp } from '@vtj/node';
import type { Request } from 'express';
import { CacheService } from '../cache/cache.service';
import { IS_PUBLIC_KEY } from '../shared';
import { LoginUserDto } from './dto/login-user.dto';

@Injectable()
export class UsersGuard implements CanActivate {
  constructor(
    private cache: CacheService,
    private reflector: Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass()
    ]);
    if (isPublic) return true;
    const request = context.switchToHttp().getRequest<Request>();
    const token = request.headers.authorization;
    if (!token) {
      throw new UnauthorizedException('未登录');
    }
    const login = await this.cache.get(token);
    if (!login) {
      throw new UnauthorizedException('登录已经过期');
    }

    if (
      !this.validateApiPermission(
        request.method,
        request.url,
        login.value as LoginUserDto
      )
    ) {
      throw new ForbiddenException(
        `没有权限调用接口：${request.method} ${request.url}`
      );
    }
    request['user'] = login.value;
    return true;
  }

  validateApiPermission(apiMethod: string, apiUrl: string, info: LoginUserDto) {
    if (this.isSuperRole(info)) return true;
    const apis = info.apis || {};
    const name = `${apiMethod},${apiUrl}`.toLowerCase();
    if (apis[name]) return true;
    const keys = Object.keys(apis);
    for (const key of keys) {
      const [method, path] = key.split(',');
      if (method.toLowerCase() !== apiMethod.toLowerCase()) continue;
      const regexp = pathToRegexp(path);
      const url = apiUrl.split('?')[0];
      const match = regexp?.test(url);
      if (match) {
        return true;
      }
    }

    return false;
  }

  isSuperRole(info: LoginUserDto) {
    const roles = info.roles || [];
    return roles.some((n) => n.isSuper);
  }
}
