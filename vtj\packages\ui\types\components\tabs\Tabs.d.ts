import { ActionProps, ActionMenuItem } from '../';
import { TabsItem } from './types';
import { IconParam } from '../icon';
import { DefineComponent, ExtractPropTypes, PropType, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
import { ActionMode } from '../action';
import { ElTooltipProps, BadgeProps, TooltipTriggerType, PopperEffect, ButtonProps } from 'element-plus';
import { BaseSize, BaseType } from '../shared';
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: Partial<Record<string, (_: {
        label: string;
        name?: string | number;
        icon?: IconParam;
        value?: string | number;
        data?: any;
        disabled?: boolean;
        closable?: boolean;
        lazy?: boolean;
        actions?: ActionProps[];
        component?: any;
        props?: Record<string, any>;
        slot?: string;
    }) => any>> & {
        label?(_: {
            label: string;
            name?: string | number;
            icon?: IconParam;
            value?: string | number;
            data?: any;
            disabled?: boolean;
            closable?: boolean;
            lazy?: boolean;
            actions?: ActionProps[];
            component?: any;
            props?: Record<string, any>;
            slot?: string;
        }): any;
        default?(_: {
            label: string;
            name?: string | number;
            icon?: IconParam;
            value?: string | number;
            data?: any;
            disabled?: boolean;
            closable?: boolean;
            lazy?: boolean;
            actions?: ActionProps[];
            component?: any;
            props?: Record<string, any>;
            slot?: string;
        }): any;
    };
    refs: {};
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: DefineComponent<ExtractPropTypes<{
    items: {
        type: PropType<TabsItem[]>;
        default(): never[];
    };
    border: {
        type: BooleanConstructor;
    };
    fit: {
        type: BooleanConstructor;
    };
    align: {
        type: PropType<"left" | "center" | "right">;
    };
}>, {
    $vtjDynamicSlots: () => (string | undefined)[];
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    actionClick: (props: Readonly<Partial< ExtractPropTypes<{
        name: {
            type: StringConstructor;
        };
        label: {
            type: StringConstructor;
        };
        value: {
            type: PropType<unknown>;
        };
        icon: {
            type: PropType<IconParam>;
        };
        mode: {
            type: PropType<ActionMode>;
            default: string;
        };
        menus: {
            type: PropType<ActionMenuItem[]>;
        };
        tooltip: {
            type: PropType<string | Partial< ElTooltipProps>>;
        };
        badge: {
            type: PropType<string | number | Partial< BadgeProps>>;
        };
        dropdown: {
            type: PropType<Partial< ExtractPropTypes<{
                readonly trigger: {
                    readonly type: PropType< TooltipTriggerType | TooltipTriggerType[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "hover";
                };
                readonly triggerKeys: {
                    readonly type: PropType<string[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => string[];
                };
                readonly effect: {
                    readonly default: "light";
                    readonly type: PropType<PopperEffect>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    readonly __epPropKey: true;
                };
                readonly type: {
                    readonly type: PropType<"" | "primary" | "success" | "warning" | "info" | "danger" | "default" | "text">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly placement: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "bottom";
                };
                readonly popperOptions: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => {};
                };
                readonly id: StringConstructor;
                readonly size: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly splitButton: BooleanConstructor;
                readonly hideOnClick: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly loop: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly showTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly hideTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly tabindex: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 0;
                };
                readonly maxHeight: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly popperClass: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly disabled: BooleanConstructor;
                readonly role: {
                    readonly type: PropType<"dialog" | "menu" | "grid" | "listbox" | "tooltip" | "tree" | "group" | "navigation">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "menu";
                };
                readonly buttonProps: {
                    readonly type: PropType<Partial< ButtonProps>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly teleported: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly persistent: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
            }>>>;
        };
        button: {
            type: PropType<Partial< ButtonProps>>;
        };
        disabled: {
            type: PropType<boolean | (() => boolean)>;
        };
        size: {
            type: PropType<BaseSize>;
            default: string;
        };
        type: {
            type: PropType<BaseType>;
        };
        background: {
            type: PropType<"always" | "hover" | "none">;
            default: string;
        };
        circle: {
            type: BooleanConstructor;
        };
        draggable: {
            type: BooleanConstructor;
        };
    }>>>) => any;
    actionCommand: (item: ActionMenuItem) => any;
}, string, PublicProps, Readonly< ExtractPropTypes<{
    items: {
        type: PropType<TabsItem[]>;
        default(): never[];
    };
    border: {
        type: BooleanConstructor;
    };
    fit: {
        type: BooleanConstructor;
    };
    align: {
        type: PropType<"left" | "center" | "right">;
    };
}>> & Readonly<{
    onActionClick?: ((props: Readonly<Partial< ExtractPropTypes<{
        name: {
            type: StringConstructor;
        };
        label: {
            type: StringConstructor;
        };
        value: {
            type: PropType<unknown>;
        };
        icon: {
            type: PropType<IconParam>;
        };
        mode: {
            type: PropType<ActionMode>;
            default: string;
        };
        menus: {
            type: PropType<ActionMenuItem[]>;
        };
        tooltip: {
            type: PropType<string | Partial< ElTooltipProps>>;
        };
        badge: {
            type: PropType<string | number | Partial< BadgeProps>>;
        };
        dropdown: {
            type: PropType<Partial< ExtractPropTypes<{
                readonly trigger: {
                    readonly type: PropType< TooltipTriggerType | TooltipTriggerType[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "hover";
                };
                readonly triggerKeys: {
                    readonly type: PropType<string[]>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => string[];
                };
                readonly effect: {
                    readonly default: "light";
                    readonly type: PropType<PopperEffect>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    readonly __epPropKey: true;
                };
                readonly type: {
                    readonly type: PropType<"" | "primary" | "success" | "warning" | "info" | "danger" | "default" | "text">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly placement: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "bottom";
                };
                readonly popperOptions: {
                    readonly type: PropType<any>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: () => {};
                };
                readonly id: StringConstructor;
                readonly size: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly splitButton: BooleanConstructor;
                readonly hideOnClick: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly loop: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly showTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly hideTimeout: {
                    readonly type: PropType<number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 150;
                };
                readonly tabindex: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: 0;
                };
                readonly maxHeight: {
                    readonly type: PropType<string | number>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly popperClass: {
                    readonly type: PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "";
                };
                readonly disabled: BooleanConstructor;
                readonly role: {
                    readonly type: PropType<"dialog" | "menu" | "grid" | "listbox" | "tooltip" | "tree" | "group" | "navigation">;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: "menu";
                };
                readonly buttonProps: {
                    readonly type: PropType<Partial< ButtonProps>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly teleported: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
                readonly persistent: {
                    readonly type: PropType<boolean>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                } & {
                    readonly default: true;
                };
            }>>>;
        };
        button: {
            type: PropType<Partial< ButtonProps>>;
        };
        disabled: {
            type: PropType<boolean | (() => boolean)>;
        };
        size: {
            type: PropType<BaseSize>;
            default: string;
        };
        type: {
            type: PropType<BaseType>;
        };
        background: {
            type: PropType<"always" | "hover" | "none">;
            default: string;
        };
        circle: {
            type: BooleanConstructor;
        };
        draggable: {
            type: BooleanConstructor;
        };
    }>>>) => any) | undefined;
    onActionCommand?: ((item: ActionMenuItem) => any) | undefined;
}>, {
    items: TabsItem[];
    fit: boolean;
    border: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
