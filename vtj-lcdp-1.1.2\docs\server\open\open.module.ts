import { Module } from '@nestjs/common';
import { OpenService } from './open.service';
import { OpenController } from './open.controller';
import { UsersModule } from '../users/users.module';
import { UsersService } from '../users/users.service';
import { RolesModule } from '../roles/roles.module';
import { RolesService } from '../roles/roles.service';
import { DictsModule } from '../dicts/dicts.module';
import { DictsService } from '../dicts/dicts.service';
import { OAuthModule } from '../oauth/oauth.module';
import { OAuthService } from '../oauth/oauth.service';
import { OssModule } from '../oss/oss.module';
import { OssService } from '../oss/oss.service';
import { TemplatesModule } from '../templates/templates.module';
import { TemplatesService } from '../templates/templates.service';
import { ReportModule } from '../report/report.module';
import { ReportService } from '../report/report.service';
import { TopicsModule } from '../topics/topics.module';
import { TopicsService } from '../topics/topics.service';
import { OrdersModule } from '../orders/orders.module';
import { OrdersService } from '../orders/orders.service';

@Module({
  imports: [
    UsersModule,
    RolesModule,
    DictsModule,
    OAuthModule,
    OssModule,
    TemplatesModule,
    ReportModule,
    TopicsModule,
    OrdersModule
  ],
  controllers: [OpenController],
  providers: [
    OpenService,
    UsersService,
    RolesService,
    DictsService,
    OAuthService,
    OssService,
    TemplatesService,
    ReportService,
    TopicsService,
    OrdersService
  ],
  exports: [OpenService]
})
export class OpenModule {}
