import { DefineComponent, ExtractPropTypes, Ref, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
declare function __VLS_template(): {
    attrs: Partial<{}>;
    slots: Partial<Record<string, (_: {}) => any>> & {
        default?(_: {
            props: {
                readonly booleanProp: boolean;
                readonly modelValue?: string | undefined;
                readonly stringProp?: string | undefined;
                readonly numberProp?: number | undefined;
                readonly selectProp?: string | undefined;
                readonly objectProp?: Record<string, any> | undefined;
                readonly arrayProp?: unknown[] | undefined;
                readonly iconProp?: string | undefined;
                readonly colorProp?: string | undefined;
                readonly syncProp?: string | undefined;
            };
            data: any;
        }): any;
        extra?(_: {
            props: {
                readonly booleanProp: boolean;
                readonly modelValue?: string | undefined;
                readonly stringProp?: string | undefined;
                readonly numberProp?: number | undefined;
                readonly selectProp?: string | undefined;
                readonly objectProp?: Record<string, any> | undefined;
                readonly arrayProp?: unknown[] | undefined;
                readonly iconProp?: string | undefined;
                readonly colorProp?: string | undefined;
                readonly syncProp?: string | undefined;
            };
            data: any;
        }): any;
    };
    refs: {};
    rootEl: any;
};
type __VLS_TemplateResult = ReturnType<typeof __VLS_template>;
declare const __VLS_component: DefineComponent<ExtractPropTypes<{
    stringProp: {
        type: StringConstructor;
    };
    booleanProp: {
        type: BooleanConstructor;
    };
    numberProp: {
        type: NumberConstructor;
    };
    selectProp: {
        type: StringConstructor;
    };
    objectProp: {
        type: ObjectConstructor;
    };
    arrayProp: {
        type: ArrayConstructor;
    };
    iconProp: {
        type: StringConstructor;
    };
    colorProp: {
        type: StringConstructor;
    };
    modelValue: {
        type: StringConstructor;
    };
    syncProp: {
        type: StringConstructor;
    };
}>, {
    click: () => void;
    submit: () => void;
    data: Ref<any, any>;
    change: (params: any) => void;
    $vtjDynamicSlots: () => string[];
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    click: (props: Readonly<Partial< ExtractPropTypes<{
        stringProp: {
            type: StringConstructor;
        };
        booleanProp: {
            type: BooleanConstructor;
        };
        numberProp: {
            type: NumberConstructor;
        };
        selectProp: {
            type: StringConstructor;
        };
        objectProp: {
            type: ObjectConstructor;
        };
        arrayProp: {
            type: ArrayConstructor;
        };
        iconProp: {
            type: StringConstructor;
        };
        colorProp: {
            type: StringConstructor;
        };
        modelValue: {
            type: StringConstructor;
        };
        syncProp: {
            type: StringConstructor;
        };
    }>>>) => any;
    submit: (props: Readonly<Partial< ExtractPropTypes<{
        stringProp: {
            type: StringConstructor;
        };
        booleanProp: {
            type: BooleanConstructor;
        };
        numberProp: {
            type: NumberConstructor;
        };
        selectProp: {
            type: StringConstructor;
        };
        objectProp: {
            type: ObjectConstructor;
        };
        arrayProp: {
            type: ArrayConstructor;
        };
        iconProp: {
            type: StringConstructor;
        };
        colorProp: {
            type: StringConstructor;
        };
        modelValue: {
            type: StringConstructor;
        };
        syncProp: {
            type: StringConstructor;
        };
    }>>>) => any;
    change: (data: any) => any;
    "update:modelValue": (value?: string | undefined) => any;
    "update:syncProp": (value?: string | undefined) => any;
}, string, PublicProps, Readonly< ExtractPropTypes<{
    stringProp: {
        type: StringConstructor;
    };
    booleanProp: {
        type: BooleanConstructor;
    };
    numberProp: {
        type: NumberConstructor;
    };
    selectProp: {
        type: StringConstructor;
    };
    objectProp: {
        type: ObjectConstructor;
    };
    arrayProp: {
        type: ArrayConstructor;
    };
    iconProp: {
        type: StringConstructor;
    };
    colorProp: {
        type: StringConstructor;
    };
    modelValue: {
        type: StringConstructor;
    };
    syncProp: {
        type: StringConstructor;
    };
}>> & Readonly<{
    onClick?: ((props: Readonly<Partial< ExtractPropTypes<{
        stringProp: {
            type: StringConstructor;
        };
        booleanProp: {
            type: BooleanConstructor;
        };
        numberProp: {
            type: NumberConstructor;
        };
        selectProp: {
            type: StringConstructor;
        };
        objectProp: {
            type: ObjectConstructor;
        };
        arrayProp: {
            type: ArrayConstructor;
        };
        iconProp: {
            type: StringConstructor;
        };
        colorProp: {
            type: StringConstructor;
        };
        modelValue: {
            type: StringConstructor;
        };
        syncProp: {
            type: StringConstructor;
        };
    }>>>) => any) | undefined;
    onSubmit?: ((props: Readonly<Partial< ExtractPropTypes<{
        stringProp: {
            type: StringConstructor;
        };
        booleanProp: {
            type: BooleanConstructor;
        };
        numberProp: {
            type: NumberConstructor;
        };
        selectProp: {
            type: StringConstructor;
        };
        objectProp: {
            type: ObjectConstructor;
        };
        arrayProp: {
            type: ArrayConstructor;
        };
        iconProp: {
            type: StringConstructor;
        };
        colorProp: {
            type: StringConstructor;
        };
        modelValue: {
            type: StringConstructor;
        };
        syncProp: {
            type: StringConstructor;
        };
    }>>>) => any) | undefined;
    onChange?: ((data: any) => any) | undefined;
    "onUpdate:modelValue"?: ((value?: string | undefined) => any) | undefined;
    "onUpdate:syncProp"?: ((value?: string | undefined) => any) | undefined;
}>, {
    booleanProp: boolean;
}, {}, {}, {}, string, ComponentProvideOptions, true, {}, any>;
declare const _default: __VLS_WithTemplateSlots<typeof __VLS_component, __VLS_TemplateResult["slots"]>;
export default _default;
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
