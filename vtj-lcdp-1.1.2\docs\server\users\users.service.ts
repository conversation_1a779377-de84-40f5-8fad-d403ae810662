import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, In, MoreThanOrEqual } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { uuid, RSA, base64, unBase64, dayjs } from '@vtj/node';
import { RolesService } from '../roles/roles.service';
import { User } from './entities/user.entity';
import { UserDto } from './dto/user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { LoginUserDto } from './dto/login-user.dto';
import { CacheService } from '../cache/cache.service';
import { SettingsService } from '../settings/settings.service';
import { RegisterUserDto } from './dto/register-user.dto';
import {
  passwordHash,
  USER_INIT_PASSWORD,
  pager,
  RSA_PUBLIC_KEY,
  USER_LOGIN_EXPIRED,
  OAuthType,
  sendMailValidateCode,
  MailServer
} from '../shared';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User) private repo: Repository<User>,
    private roles: RolesService,
    private cache: CacheService,
    private settings: SettingsService,
    private config: ConfigService
  ) {}

  async save(dto: UserDto) {
    const user = new User();
    Object.assign(user, dto);
    if (!user.id) {
      // 初始密码
      user.password = passwordHash(USER_INIT_PASSWORD);
      if (!user.roles) {
        // 默认角色
        const role = await this.roles.getDefaultRole();
        if (role) {
          user.roles = [role];
        }
      }
    }
    return this.repo.save(user);
  }

  async remove(id: string | string[]) {
    return this.repo.softDelete(id);
  }

  async find(dto: QueryUserDto) {
    const { page, limit, skip, take } = pager(dto);
    const { withDeleted, keyword, freeze, oauthType, order } = dto;
    const [list, total] = await this.repo.findAndCount({
      withDeleted,
      skip,
      take,
      relations: {
        roles: true
      },
      where: [
        {
          name: keyword ? Like(`%${keyword}%`) : undefined,
          freeze: freeze || undefined,
          oauthType: oauthType || undefined
        },
        {
          email: keyword ? Like(`%${keyword}%`) : undefined,
          freeze: freeze || undefined,
          oauthType: oauthType || undefined
        },
        {
          phone: keyword ? Like(`%${keyword}%`) : undefined,
          freeze: freeze || undefined,
          oauthType: oauthType || undefined
        }
      ],
      order: order
        ? {
            ...order
          }
        : {
            createdAt: 'DESC'
          }
    });
    return {
      page,
      limit,
      list,
      total
    };
  }

  async getUserWithPassword(id: string) {
    return this.repo
      .createQueryBuilder('users')
      .select(['users'])
      .addSelect('users.password')
      .where({ id })
      .getOne();
  }

  async resetPassword(id: string) {
    const user = await this.repo.findOneBy({ id });
    const newPassword = Date.now().toString(36);
    if (user) {
      user.password = passwordHash(newPassword);
      await this.repo.save(user);
    } else {
      throw new BadRequestException('用户不存在');
    }
    return newPassword;
  }

  async modifyPassword(id: string, oldPassword: string, newPassword: string) {
    const user = await this.getUserWithPassword(id);
    if (user) {
      const oldPsw = passwordHash(oldPassword);
      if (user.password !== oldPsw) {
        throw new BadRequestException('旧密码不正确');
      }
      user.password = passwordHash(newPassword);
      await this.repo.save(user);
    } else {
      throw new BadRequestException('用户不存在');
    }
    return !!user;
  }

  getUserByAccount(account: string, password: string) {
    const psw = passwordHash(password);
    return this.repo.findOne({
      relations: {
        roles: true
      },
      where: [
        {
          name: account,
          password: psw
        },
        {
          email: account,
          password: psw
        },
        {
          phone: account,
          password: psw
        }
      ]
    });
  }

  getUserWithRolesById(id: string) {
    return this.repo.findOne({
      relations: {
        roles: true
      },
      where: {
        id
      }
    });
  }

  loginUserErypted(loginUser: LoginUserDto) {
    const content = JSON.stringify(loginUser);
    const size = 40;
    const length = Math.ceil(content.length / size);
    const codes = [];
    for (let i = 0; i < length; i++) {
      const text = content.substring(i * size, (i + 1) * size);
      codes.push(RSA(text, RSA_PUBLIC_KEY));
    }
    return codes;
  }

  async createLoginUser(user: User) {
    const token = uuid();
    const roleIds = (user.roles || []).map((n) => n.id);
    const loginUser = new LoginUserDto();
    const { permissions, apis, apps } =
      await this.roles.getPermissions(roleIds);
    const roles = (user.roles || []).map((n) => {
      const { id, name, isSuper, label } = n;
      return {
        id,
        name,
        isSuper,
        label
      };
    });
    Object.assign(loginUser, user, { token, permissions, apis, apps, roles });
    await this.cache.set(token, loginUser, USER_LOGIN_EXPIRED);
    return loginUser;
  }
  async login(user: User) {
    user.lastLoginAt = new Date();
    await this.repo.save(user);
    const loginUser = await this.createLoginUser(user);
    return this.loginUserErypted(loginUser);
  }

  async loginByPassword(account: string, password: string) {
    const user = await this.getUserByAccount(account, password);
    if (!user) {
      throw new BadRequestException('账号或密码错误');
    }
    return this.login(user);
  }

  async loginBySign(code: string) {
    const content = unBase64(code);
    const [userId, sign] = content.split(',');
    const user = await this.getUserWithPassword(userId);
    if (user && passwordHash(user.password) === sign) {
      const info = await this.getUserWithRolesById(user.id);
      return this.login(info);
    } else {
      throw new BadRequestException('签名错误');
    }
  }

  async logout(token: string) {
    return token ? !!(await this.cache.removeByKey(token)) : true;
  }

  getOAuthUser(type: OAuthType, id: string) {
    return this.repo.findOne({
      relations: {
        roles: true
      },
      where: {
        oauthType: type,
        oauthId: id
      }
    });
  }

  async getLoginUserByToken(token: string, erypted: boolean = true) {
    const cache = await this.cache.get(token);
    if (cache) {
      return erypted
        ? this.loginUserErypted(cache.value as LoginUserDto)
        : (cache.value as LoginUserDto);
    } else {
      throw new BadRequestException('登录已失效');
    }
  }

  getUsersByIds(ids: string[]) {
    return this.repo.findBy({
      id: In(ids)
    });
  }

  getUserById(id: string) {
    return this.repo.findOne({
      where: { id }
    });
  }

  getUserByName(name: string) {
    return this.repo.findOne({
      where: { name }
    });
  }

  async getUserSign(id: string) {
    const user = await this.getUserWithPassword(id);
    if (!user) {
      return new BadRequestException('用户不存在');
    }
    const content = user.id + ',' + passwordHash(user.password);
    return base64(content);
  }

  async count(start: string, end: string, field: string) {
    const builder = this.repo
      .createQueryBuilder('users')
      .select(`COUNT(DISTINCT users.id)`, 'count')
      .where(`users.${field} > :start and users.${field} < :end`, {
        start,
        end
      });

    const res = await builder.getRawOne();
    return Number(res?.count || 0) as number;
  }

  /**
   * 多少日内的留存率
   * @param start
   * @param end
   * @param day
   * @returns
   */
  async retention(_start: string, _end: string, day: number) {
    const start = dayjs(new Date(_start))
      .subtract(day, 'day')
      .format('YYYY-MM-DD');
    const end = dayjs(new Date(_end)).subtract(day, 'day').format('YYYY-MM-DD');

    const builder = this.repo
      .createQueryBuilder('u')
      .leftJoin(
        'report',
        'a',
        `a.user_id = u.id AND DATE(a.created_at) = DATE_ADD(DATE(u.created_at), INTERVAL ${day} DAY)`
      )
      .select('COUNT(DISTINCT u.id)', 'total_users')
      .addSelect('COUNT(DISTINCT a.user_id)', 'retained_users')
      .addSelect(
        'COALESCE((COUNT(DISTINCT a.user_id) * 100.0 / NULLIF(COUNT(DISTINCT u.id), 0)), 0)',
        'retention_rate'
      )
      .where('u.created_at > :start AND u.created_at <= :end', {
        start,
        end
      });
    const res = await builder.getRawOne();
    return Number.parseInt(res?.retention_rate || 0) as number;
  }

  async today(type: 'total' | 'new' | 'active') {
    if (type === 'total') {
      return await this.repo.countBy({});
    }

    if (type === 'new') {
      const start = dayjs(new Date()).format('YYYY-MM-DD');
      return await this.repo.countBy({
        createdAt: MoreThanOrEqual(new Date(start))
      });
    }

    if (type === 'active') {
      const start = dayjs(new Date()).format('YYYY-MM-DD');
      return await this.repo.countBy({
        lastLoginAt: MoreThanOrEqual(new Date(start))
      });
    }
    return -1;
  }

  async sendMailVaildateCode(email: string) {
    const { host, port, user } = this.config.get<MailServer>('mail');
    const setting = await this.settings.get();
    const pass = setting.mailPass;
    const code = String(Math.floor(Math.random() * 1000000)).padEnd(6, '0');
    const to = email;
    const result = await sendMailValidateCode({
      host,
      port,
      user,
      pass,
      to,
      code
    }).catch((e: any) => {
      console.warn('邮件发送失败', e);
      return null;
    });
    if (result) {
      // 缓存30分钟
      this.cache.set(to, code, 30 * 60 * 1000);
      return {
        id: to
      };
    } else {
      throw new BadRequestException('邮件发送失败');
    }
  }

  async userRegister(rUser: RegisterUserDto) {
    const vCode = (await this.cache.get(rUser.email))?.value || '';
    if (vCode.toString() !== rUser.code?.toString()) {
      throw new BadRequestException('验证码不正确');
    }

    let existUser = await this.repo.findOneBy({
      name: rUser.name
    });
    if (existUser) {
      throw new BadRequestException('账号名已被注册，请更换！');
    }

    existUser = await this.repo.findOneBy({
      email: rUser.email
    });

    if (existUser) {
      throw new BadRequestException('该邮箱已注册，请更换！');
    }

    const user = new User();
    user.password = passwordHash(rUser.password);
    // 默认角色
    const role = await this.roles.getDefaultRole();
    if (role) {
      user.roles = [role];
    }
    user.name = rUser.name;
    user.email = rUser.email;

    return this.repo.save(user);
  }

  async userFindPassword(dto: RegisterUserDto) {
    const vCode = (await this.cache.get(dto.email))?.value || '';

    if (vCode.toString() !== dto.code?.toString()) {
      throw new BadRequestException('验证码不正确');
    }

    const user = await this.repo.findOneBy({
      name: dto.name,
      email: dto.email
    });

    if (!user) {
      throw new BadRequestException('用户名不存在, 或用户名和与邮箱不匹配');
    }

    user.password = passwordHash(dto.password);
    return this.repo.save(user);
  }
}
