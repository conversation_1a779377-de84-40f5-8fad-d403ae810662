<template>
  <div class="designer" ref="container"></div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Engine, useAccess, type PlatformType } from '@vtj/pro';
import { delay } from '@vtj/utils';
import { LcdpService, REMOTE, AUTH_CODE, MATERIAL_PATH } from '@/shared';
import { notify, createPageRoute } from '@/utils';
import { install, CustomOpenApi } from '@/extension';

const access = useAccess();
const route = useRoute();
const container = ref();
const service = new LcdpService(notify);
const openApi = new CustomOpenApi(access);
const id = route.params.id as string;
const platform = route.params.platform as PlatformType;
const { pageBasePath, pageRouteName } = createPageRoute(platform, id);

const engine = new Engine({
  noMask: true,
  install,
  openApi,
  container,
  service,
  access,
  project: {
    id,
    platform
  },
  materialPath: MATERIAL_PATH,
  pageBasePath,
  pageRouteName,
  remote: REMOTE,
  auth: AUTH_CODE
});

engine.ready(async () => {
  if (route.query.id) {
    await delay(1000);
    engine.openFile(route.query.id as string);
  }
});

watch(
  () => route.query.id,
  (id) => {
    engine.openFile(id as string);
  }
);
</script>

<style lang="scss" scoped>
.designer {
  height: 100%;
}
</style>
