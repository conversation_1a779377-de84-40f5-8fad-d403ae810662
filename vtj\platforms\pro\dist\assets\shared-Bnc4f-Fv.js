var ei=Object.defineProperty,ni=Object.defineProperties;var ri=Object.getOwnPropertyDescriptors;var ie=Object.getOwnPropertySymbols;var Mn=Object.prototype.hasOwnProperty,Pn=Object.prototype.propertyIsEnumerable;var Fn=(e,t,n)=>t in e?ei(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,I=(e,t)=>{for(var n in t||(t={}))Mn.call(t,n)&&Fn(e,n,t[n]);if(ie)for(var n of ie(t))Pn.call(t,n)&&Fn(e,n,t[n]);return e},ut=(e,t)=>ni(e,ri(t));var Fe=(e,t)=>{var n={};for(var r in e)Mn.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&ie)for(var r of ie(e))t.indexOf(r)<0&&Pn.call(e,r)&&(n[r]=e[r]);return n};var st=(e,t,n)=>new Promise((r,i)=>{var o=f=>{try{s(n.next(f))}catch(c){i(c)}},a=f=>{try{s(n.throw(f))}catch(c){i(c)}},s=f=>f.done?r(f.value):Promise.resolve(f.value).then(o,a);s((n=n.apply(e,t)).next())});var W="top",G="bottom",Z="right",V="left",Je="auto",Zt=[W,G,Z,V],qt="start",zt="end",ii="clippingParents",sr="viewport",Wt="popper",oi="reference",Tn=Zt.reduce(function(e,t){return e.concat([t+"-"+qt,t+"-"+zt])},[]),fr=[].concat(Zt,[Je]).reduce(function(e,t){return e.concat([t,t+"-"+qt,t+"-"+zt])},[]),ai="beforeRead",si="read",fi="afterRead",ci="beforeMain",ui="main",li="afterMain",di="beforeWrite",pi="write",hi="afterWrite",gi=[ai,si,fi,ci,ui,li,di,pi,hi];function rt(e){return e?(e.nodeName||"").toLowerCase():null}function tt(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Dt(e){var t=tt(e).Element;return e instanceof t||e instanceof Element}function Y(e){var t=tt(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function Qe(e){if(typeof ShadowRoot=="undefined")return!1;var t=tt(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function mi(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},i=t.attributes[n]||{},o=t.elements[n];!Y(o)||!rt(o)||(Object.assign(o.style,r),Object.keys(i).forEach(function(a){var s=i[a];s===!1?o.removeAttribute(a):o.setAttribute(a,s===!0?"":s)}))})}function vi(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var i=t.elements[r],o=t.attributes[r]||{},a=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),s=a.reduce(function(f,c){return f[c]="",f},{});!Y(i)||!rt(i)||(Object.assign(i.style,s),Object.keys(o).forEach(function(f){i.removeAttribute(f)}))})}}var cr={name:"applyStyles",enabled:!0,phase:"write",fn:mi,effect:vi,requires:["computeStyles"]};function et(e){return e.split("-")[0]}var At=Math.max,ge=Math.min,Nt=Math.round;function kt(e,t){t===void 0&&(t=!1);var n=e.getBoundingClientRect(),r=1,i=1;if(Y(e)&&t){var o=e.offsetHeight,a=e.offsetWidth;a>0&&(r=Nt(n.width)/a||1),o>0&&(i=Nt(n.height)/o||1)}return{width:n.width/r,height:n.height/i,top:n.top/i,right:n.right/r,bottom:n.bottom/i,left:n.left/r,x:n.left/r,y:n.top/i}}function tn(e){var t=kt(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function ur(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Qe(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function ct(e){return tt(e).getComputedStyle(e)}function yi(e){return["table","td","th"].indexOf(rt(e))>=0}function gt(e){return((Dt(e)?e.ownerDocument:e.document)||window.document).documentElement}function be(e){return rt(e)==="html"?e:e.assignedSlot||e.parentNode||(Qe(e)?e.host:null)||gt(e)}function qn(e){return!Y(e)||ct(e).position==="fixed"?null:e.offsetParent}function bi(e){var t=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,n=navigator.userAgent.indexOf("Trident")!==-1;if(n&&Y(e)){var r=ct(e);if(r.position==="fixed")return null}var i=be(e);for(Qe(i)&&(i=i.host);Y(i)&&["html","body"].indexOf(rt(i))<0;){var o=ct(i);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||t&&o.willChange==="filter"||t&&o.filter&&o.filter!=="none")return i;i=i.parentNode}return null}function Kt(e){for(var t=tt(e),n=qn(e);n&&yi(n)&&ct(n).position==="static";)n=qn(n);return n&&(rt(n)==="html"||rt(n)==="body"&&ct(n).position==="static")?t:n||bi(e)||t}function en(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function _t(e,t,n){return At(e,ge(t,n))}function xi(e,t,n){var r=_t(e,t,n);return r>n?n:r}function lr(){return{top:0,right:0,bottom:0,left:0}}function dr(e){return Object.assign({},lr(),e)}function pr(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var wi=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,dr(typeof e!="number"?e:pr(e,Zt))};function Oi(e){var t,n=e.state,r=e.name,i=e.options,o=n.elements.arrow,a=n.modifiersData.popperOffsets,s=et(n.placement),f=en(s),c=[V,Z].indexOf(s)>=0,l=c?"height":"width";if(!(!o||!a)){var h=wi(i.padding,n),m=tn(o),p=f==="y"?W:V,v=f==="y"?G:Z,u=n.rects.reference[l]+n.rects.reference[f]-a[f]-n.rects.popper[l],y=a[f]-n.rects.reference[f],d=Kt(o),w=d?f==="y"?d.clientHeight||0:d.clientWidth||0:0,g=u/2-y/2,b=h[p],x=w-m[l]-h[v],O=w/2-m[l]/2+g,A=_t(b,O,x),E=f;n.modifiersData[r]=(t={},t[E]=A,t.centerOffset=A-O,t)}}function Ai(e){var t=e.state,n=e.options,r=n.element,i=r===void 0?"[data-popper-arrow]":r;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||!ur(t.elements.popper,i)||(t.elements.arrow=i))}var Ei={name:"arrow",enabled:!0,phase:"main",fn:Oi,effect:Ai,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function jt(e){return e.split("-")[1]}var Ri={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Si(e){var t=e.x,n=e.y,r=window,i=r.devicePixelRatio||1;return{x:Nt(t*i)/i||0,y:Nt(n*i)/i||0}}function Dn(e){var t,n=e.popper,r=e.popperRect,i=e.placement,o=e.variation,a=e.offsets,s=e.position,f=e.gpuAcceleration,c=e.adaptive,l=e.roundOffsets,h=e.isFixed,m=a.x,p=m===void 0?0:m,v=a.y,u=v===void 0?0:v,y=typeof l=="function"?l({x:p,y:u}):{x:p,y:u};p=y.x,u=y.y;var d=a.hasOwnProperty("x"),w=a.hasOwnProperty("y"),g=V,b=W,x=window;if(c){var O=Kt(n),A="clientHeight",E="clientWidth";if(O===tt(n)&&(O=gt(n),ct(O).position!=="static"&&s==="absolute"&&(A="scrollHeight",E="scrollWidth")),O=O,i===W||(i===V||i===Z)&&o===zt){b=G;var P=h&&O===x&&x.visualViewport?x.visualViewport.height:O[A];u-=P-r.height,u*=f?1:-1}if(i===V||(i===W||i===G)&&o===zt){g=Z;var T=h&&O===x&&x.visualViewport?x.visualViewport.width:O[E];p-=T-r.width,p*=f?1:-1}}var F=Object.assign({position:s},c&&Ri),M=l===!0?Si({x:p,y:u}):{x:p,y:u};if(p=M.x,u=M.y,f){var S;return Object.assign({},F,(S={},S[b]=w?"0":"",S[g]=d?"0":"",S.transform=(x.devicePixelRatio||1)<=1?"translate("+p+"px, "+u+"px)":"translate3d("+p+"px, "+u+"px, 0)",S))}return Object.assign({},F,(t={},t[b]=w?u+"px":"",t[g]=d?p+"px":"",t.transform="",t))}function Fi(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=r===void 0?!0:r,o=n.adaptive,a=o===void 0?!0:o,s=n.roundOffsets,f=s===void 0?!0:s,c={placement:et(t.placement),variation:jt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Dn(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Dn(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var hr={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:Fi,data:{}},oe={passive:!0};function Mi(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=i===void 0?!0:i,a=r.resize,s=a===void 0?!0:a,f=tt(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach(function(l){l.addEventListener("scroll",n.update,oe)}),s&&f.addEventListener("resize",n.update,oe),function(){o&&c.forEach(function(l){l.removeEventListener("scroll",n.update,oe)}),s&&f.removeEventListener("resize",n.update,oe)}}var gr={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:Mi,data:{}},Pi={left:"right",right:"left",bottom:"top",top:"bottom"};function ue(e){return e.replace(/left|right|bottom|top/g,function(t){return Pi[t]})}var Ti={start:"end",end:"start"};function Nn(e){return e.replace(/start|end/g,function(t){return Ti[t]})}function nn(e){var t=tt(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function rn(e){return kt(gt(e)).left+nn(e).scrollLeft}function qi(e){var t=tt(e),n=gt(e),r=t.visualViewport,i=n.clientWidth,o=n.clientHeight,a=0,s=0;return r&&(i=r.width,o=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(a=r.offsetLeft,s=r.offsetTop)),{width:i,height:o,x:a+rn(e),y:s}}function Di(e){var t,n=gt(e),r=nn(e),i=(t=e.ownerDocument)==null?void 0:t.body,o=At(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),a=At(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),s=-r.scrollLeft+rn(e),f=-r.scrollTop;return ct(i||n).direction==="rtl"&&(s+=At(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:a,x:s,y:f}}function on(e){var t=ct(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function mr(e){return["html","body","#document"].indexOf(rt(e))>=0?e.ownerDocument.body:Y(e)&&on(e)?e:mr(be(e))}function It(e,t){var n;t===void 0&&(t=[]);var r=mr(e),i=r===((n=e.ownerDocument)==null?void 0:n.body),o=tt(r),a=i?[o].concat(o.visualViewport||[],on(r)?r:[]):r,s=t.concat(a);return i?s:s.concat(It(be(a)))}function ke(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Ni(e){var t=kt(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function kn(e,t){return t===sr?ke(qi(e)):Dt(t)?Ni(t):ke(Di(gt(e)))}function ki(e){var t=It(be(e)),n=["absolute","fixed"].indexOf(ct(e).position)>=0,r=n&&Y(e)?Kt(e):e;return Dt(r)?t.filter(function(i){return Dt(i)&&ur(i,r)&&rt(i)!=="body"}):[]}function ji(e,t,n){var r=t==="clippingParents"?ki(e):[].concat(t),i=[].concat(r,[n]),o=i[0],a=i.reduce(function(s,f){var c=kn(e,f);return s.top=At(c.top,s.top),s.right=ge(c.right,s.right),s.bottom=ge(c.bottom,s.bottom),s.left=At(c.left,s.left),s},kn(e,o));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function vr(e){var t=e.reference,n=e.element,r=e.placement,i=r?et(r):null,o=r?jt(r):null,a=t.x+t.width/2-n.width/2,s=t.y+t.height/2-n.height/2,f;switch(i){case W:f={x:a,y:t.y-n.height};break;case G:f={x:a,y:t.y+t.height};break;case Z:f={x:t.x+t.width,y:s};break;case V:f={x:t.x-n.width,y:s};break;default:f={x:t.x,y:t.y}}var c=i?en(i):null;if(c!=null){var l=c==="y"?"height":"width";switch(o){case qt:f[c]=f[c]-(t[l]/2-n[l]/2);break;case zt:f[c]=f[c]+(t[l]/2-n[l]/2);break}}return f}function Ut(e,t){t===void 0&&(t={});var n=t,r=n.placement,i=r===void 0?e.placement:r,o=n.boundary,a=o===void 0?ii:o,s=n.rootBoundary,f=s===void 0?sr:s,c=n.elementContext,l=c===void 0?Wt:c,h=n.altBoundary,m=h===void 0?!1:h,p=n.padding,v=p===void 0?0:p,u=dr(typeof v!="number"?v:pr(v,Zt)),y=l===Wt?oi:Wt,d=e.rects.popper,w=e.elements[m?y:l],g=ji(Dt(w)?w:w.contextElement||gt(e.elements.popper),a,f),b=kt(e.elements.reference),x=vr({reference:b,element:d,placement:i}),O=ke(Object.assign({},d,x)),A=l===Wt?O:b,E={top:g.top-A.top+u.top,bottom:A.bottom-g.bottom+u.bottom,left:g.left-A.left+u.left,right:A.right-g.right+u.right},P=e.modifiersData.offset;if(l===Wt&&P){var T=P[i];Object.keys(E).forEach(function(F){var M=[Z,G].indexOf(F)>=0?1:-1,S=[W,G].indexOf(F)>=0?"y":"x";E[F]+=T[S]*M})}return E}function Hi(e,t){t===void 0&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,a=n.padding,s=n.flipVariations,f=n.allowedAutoPlacements,c=f===void 0?fr:f,l=jt(r),h=l?s?Tn:Tn.filter(function(v){return jt(v)===l}):Zt,m=h.filter(function(v){return c.indexOf(v)>=0});m.length===0&&(m=h);var p=m.reduce(function(v,u){return v[u]=Ut(e,{placement:u,boundary:i,rootBoundary:o,padding:a})[et(u)],v},{});return Object.keys(p).sort(function(v,u){return p[v]-p[u]})}function Li(e){if(et(e)===Je)return[];var t=ue(e);return[Nn(e),t,Nn(t)]}function Ci(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,o=i===void 0?!0:i,a=n.altAxis,s=a===void 0?!0:a,f=n.fallbackPlacements,c=n.padding,l=n.boundary,h=n.rootBoundary,m=n.altBoundary,p=n.flipVariations,v=p===void 0?!0:p,u=n.allowedAutoPlacements,y=t.options.placement,d=et(y),w=d===y,g=f||(w||!v?[ue(y)]:Li(y)),b=[y].concat(g).reduce(function(vt,at){return vt.concat(et(at)===Je?Hi(t,{placement:at,boundary:l,rootBoundary:h,padding:c,flipVariations:v,allowedAutoPlacements:u}):at)},[]),x=t.rects.reference,O=t.rects.popper,A=new Map,E=!0,P=b[0],T=0;T<b.length;T++){var F=b[T],M=et(F),S=jt(F)===qt,_=[W,G].indexOf(M)>=0,j=_?"width":"height",q=Ut(t,{placement:F,boundary:l,rootBoundary:h,altBoundary:m,padding:c}),D=_?S?Z:V:S?G:W;x[j]>O[j]&&(D=ue(D));var C=ue(D),L=[];if(o&&L.push(q[M]<=0),s&&L.push(q[D]<=0,q[C]<=0),L.every(function(vt){return vt})){P=F,E=!1;break}A.set(F,L)}if(E)for(var H=v?3:1,X=function(vt){var at=b.find(function(ne){var Bt=A.get(ne);if(Bt)return Bt.slice(0,vt).every(function(Ft){return Ft})});if(at)return P=at,"break"},mt=H;mt>0;mt--){var ee=X(mt);if(ee==="break")break}t.placement!==P&&(t.modifiersData[r]._skip=!0,t.placement=P,t.reset=!0)}}var Bi={name:"flip",enabled:!0,phase:"main",fn:Ci,requiresIfExists:["offset"],data:{_skip:!1}};function jn(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Hn(e){return[W,Z,G,V].some(function(t){return e[t]>=0})}function Wi(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,a=Ut(t,{elementContext:"reference"}),s=Ut(t,{altBoundary:!0}),f=jn(a,r),c=jn(s,i,o),l=Hn(f),h=Hn(c);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:c,isReferenceHidden:l,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":h})}var Vi={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Wi};function _i(e,t,n){var r=et(e),i=[V,W].indexOf(r)>=0?-1:1,o=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,a=o[0],s=o[1];return a=a||0,s=(s||0)*i,[V,Z].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}function Ii(e){var t=e.state,n=e.options,r=e.name,i=n.offset,o=i===void 0?[0,0]:i,a=fr.reduce(function(l,h){return l[h]=_i(h,t.rects,o),l},{}),s=a[t.placement],f=s.x,c=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=c),t.modifiersData[r]=a}var $i={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Ii};function zi(e){var t=e.state,n=e.name;t.modifiersData[n]=vr({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})}var yr={name:"popperOffsets",enabled:!0,phase:"read",fn:zi,data:{}};function Ui(e){return e==="x"?"y":"x"}function Xi(e){var t=e.state,n=e.options,r=e.name,i=n.mainAxis,o=i===void 0?!0:i,a=n.altAxis,s=a===void 0?!1:a,f=n.boundary,c=n.rootBoundary,l=n.altBoundary,h=n.padding,m=n.tether,p=m===void 0?!0:m,v=n.tetherOffset,u=v===void 0?0:v,y=Ut(t,{boundary:f,rootBoundary:c,padding:h,altBoundary:l}),d=et(t.placement),w=jt(t.placement),g=!w,b=en(d),x=Ui(b),O=t.modifiersData.popperOffsets,A=t.rects.reference,E=t.rects.popper,P=typeof u=="function"?u(Object.assign({},t.rects,{placement:t.placement})):u,T=typeof P=="number"?{mainAxis:P,altAxis:P}:Object.assign({mainAxis:0,altAxis:0},P),F=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,M={x:0,y:0};if(O){if(o){var S,_=b==="y"?W:V,j=b==="y"?G:Z,q=b==="y"?"height":"width",D=O[b],C=D+y[_],L=D-y[j],H=p?-E[q]/2:0,X=w===qt?A[q]:E[q],mt=w===qt?-E[q]:-A[q],ee=t.elements.arrow,vt=p&&ee?tn(ee):{width:0,height:0},at=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:lr(),ne=at[_],Bt=at[j],Ft=_t(0,A[q],vt[q]),Yr=g?A[q]/2-H-Ft-ne-T.mainAxis:X-Ft-ne-T.mainAxis,Gr=g?-A[q]/2+H+Ft+Bt+T.mainAxis:mt+Ft+Bt+T.mainAxis,Re=t.elements.arrow&&Kt(t.elements.arrow),Zr=Re?b==="y"?Re.clientTop||0:Re.clientLeft||0:0,yn=(S=F==null?void 0:F[b])!=null?S:0,Kr=D+Yr-yn-Zr,Jr=D+Gr-yn,bn=_t(p?ge(C,Kr):C,D,p?At(L,Jr):L);O[b]=bn,M[b]=bn-D}if(s){var xn,Qr=b==="x"?W:V,ti=b==="x"?G:Z,yt=O[x],re=x==="y"?"height":"width",wn=yt+y[Qr],On=yt-y[ti],Se=[W,V].indexOf(d)!==-1,An=(xn=F==null?void 0:F[x])!=null?xn:0,En=Se?wn:yt-A[re]-E[re]-An+T.altAxis,Rn=Se?yt+A[re]+E[re]-An-T.altAxis:On,Sn=p&&Se?xi(En,yt,Rn):_t(p?En:wn,yt,p?Rn:On);O[x]=Sn,M[x]=Sn-yt}t.modifiersData[r]=M}}var Yi={name:"preventOverflow",enabled:!0,phase:"main",fn:Xi,requiresIfExists:["offset"]};function Gi(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Zi(e){return e===tt(e)||!Y(e)?nn(e):Gi(e)}function Ki(e){var t=e.getBoundingClientRect(),n=Nt(t.width)/e.offsetWidth||1,r=Nt(t.height)/e.offsetHeight||1;return n!==1||r!==1}function Ji(e,t,n){n===void 0&&(n=!1);var r=Y(t),i=Y(t)&&Ki(t),o=gt(t),a=kt(e,i),s={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(r||!r&&!n)&&((rt(t)!=="body"||on(o))&&(s=Zi(t)),Y(t)?(f=kt(t,!0),f.x+=t.clientLeft,f.y+=t.clientTop):o&&(f.x=rn(o))),{x:a.left+s.scrollLeft-f.x,y:a.top+s.scrollTop-f.y,width:a.width,height:a.height}}function Qi(e){var t=new Map,n=new Set,r=[];e.forEach(function(o){t.set(o.name,o)});function i(o){n.add(o.name);var a=[].concat(o.requires||[],o.requiresIfExists||[]);a.forEach(function(s){if(!n.has(s)){var f=t.get(s);f&&i(f)}}),r.push(o)}return e.forEach(function(o){n.has(o.name)||i(o)}),r}function to(e){var t=Qi(e);return gi.reduce(function(n,r){return n.concat(t.filter(function(i){return i.phase===r}))},[])}function eo(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function no(e){var t=e.reduce(function(n,r){var i=n[r.name];return n[r.name]=i?Object.assign({},i,r,{options:Object.assign({},i.options,r.options),data:Object.assign({},i.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var Ln={placement:"bottom",modifiers:[],strategy:"absolute"};function Cn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function an(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,i=t.defaultOptions,o=i===void 0?Ln:i;return function(a,s,f){f===void 0&&(f=o);var c={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ln,o),modifiersData:{},elements:{reference:a,popper:s},attributes:{},styles:{}},l=[],h=!1,m={state:c,setOptions:function(u){var y=typeof u=="function"?u(c.options):u;v(),c.options=Object.assign({},o,c.options,y),c.scrollParents={reference:Dt(a)?It(a):a.contextElement?It(a.contextElement):[],popper:It(s)};var d=to(no([].concat(r,c.options.modifiers)));return c.orderedModifiers=d.filter(function(w){return w.enabled}),p(),m.update()},forceUpdate:function(){if(!h){var u=c.elements,y=u.reference,d=u.popper;if(Cn(y,d)){c.rects={reference:Ji(y,Kt(d),c.options.strategy==="fixed"),popper:tn(d)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach(function(E){return c.modifiersData[E.name]=Object.assign({},E.data)});for(var w=0;w<c.orderedModifiers.length;w++){if(c.reset===!0){c.reset=!1,w=-1;continue}var g=c.orderedModifiers[w],b=g.fn,x=g.options,O=x===void 0?{}:x,A=g.name;typeof b=="function"&&(c=b({state:c,options:O,name:A,instance:m})||c)}}}},update:eo(function(){return new Promise(function(u){m.forceUpdate(),u(c)})}),destroy:function(){v(),h=!0}};if(!Cn(a,s))return m;m.setOptions(f).then(function(u){!h&&f.onFirstUpdate&&f.onFirstUpdate(u)});function p(){c.orderedModifiers.forEach(function(u){var y=u.name,d=u.options,w=d===void 0?{}:d,g=u.effect;if(typeof g=="function"){var b=g({state:c,name:y,instance:m,options:w}),x=function(){};l.push(b||x)}})}function v(){l.forEach(function(u){return u()}),l=[]}return m}}an();var ro=[gr,yr,hr,cr];an({defaultModifiers:ro});var io=[gr,yr,hr,cr,$i,Bi,Yi,Ei,Vi],Ya=an({defaultModifiers:io});function k(e,t){oo(e)&&(e="100%");var n=ao(e);return e=t===360?e:Math.min(t,Math.max(0,parseFloat(e))),n&&(e=parseInt(String(e*t),10)/100),Math.abs(e-t)<1e-6?1:(t===360?e=(e<0?e%t+t:e%t)/parseFloat(String(t)):e=e%t/parseFloat(String(t)),e)}function ae(e){return Math.min(1,Math.max(0,e))}function oo(e){return typeof e=="string"&&e.indexOf(".")!==-1&&parseFloat(e)===1}function ao(e){return typeof e=="string"&&e.indexOf("%")!==-1}function br(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function se(e){return e<=1?"".concat(Number(e)*100,"%"):e}function wt(e){return e.length===1?"0"+e:String(e)}function so(e,t,n){return{r:k(e,255)*255,g:k(t,255)*255,b:k(n,255)*255}}function Bn(e,t,n){e=k(e,255),t=k(t,255),n=k(n,255);var r=Math.max(e,t,n),i=Math.min(e,t,n),o=0,a=0,s=(r+i)/2;if(r===i)a=0,o=0;else{var f=r-i;switch(a=s>.5?f/(2-r-i):f/(r+i),r){case e:o=(t-n)/f+(t<n?6:0);break;case t:o=(n-e)/f+2;break;case n:o=(e-t)/f+4;break}o/=6}return{h:o,s:a,l:s}}function Me(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*(6*n):n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function fo(e,t,n){var r,i,o;if(e=k(e,360),t=k(t,100),n=k(n,100),t===0)i=n,o=n,r=n;else{var a=n<.5?n*(1+t):n+t-n*t,s=2*n-a;r=Me(s,a,e+1/3),i=Me(s,a,e),o=Me(s,a,e-1/3)}return{r:r*255,g:i*255,b:o*255}}function Wn(e,t,n){e=k(e,255),t=k(t,255),n=k(n,255);var r=Math.max(e,t,n),i=Math.min(e,t,n),o=0,a=r,s=r-i,f=r===0?0:s/r;if(r===i)o=0;else{switch(r){case e:o=(t-n)/s+(t<n?6:0);break;case t:o=(n-e)/s+2;break;case n:o=(e-t)/s+4;break}o/=6}return{h:o,s:f,v:a}}function co(e,t,n){e=k(e,360)*6,t=k(t,100),n=k(n,100);var r=Math.floor(e),i=e-r,o=n*(1-t),a=n*(1-i*t),s=n*(1-(1-i)*t),f=r%6,c=[n,a,o,o,s,n][f],l=[s,n,n,a,o,o][f],h=[o,o,s,n,n,a][f];return{r:c*255,g:l*255,b:h*255}}function Vn(e,t,n,r){var i=[wt(Math.round(e).toString(16)),wt(Math.round(t).toString(16)),wt(Math.round(n).toString(16))];return r&&i[0].startsWith(i[0].charAt(1))&&i[1].startsWith(i[1].charAt(1))&&i[2].startsWith(i[2].charAt(1))?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function uo(e,t,n,r,i){var o=[wt(Math.round(e).toString(16)),wt(Math.round(t).toString(16)),wt(Math.round(n).toString(16)),wt(lo(r))];return i&&o[0].startsWith(o[0].charAt(1))&&o[1].startsWith(o[1].charAt(1))&&o[2].startsWith(o[2].charAt(1))&&o[3].startsWith(o[3].charAt(1))?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0)+o[3].charAt(0):o.join("")}function lo(e){return Math.round(parseFloat(e)*255).toString(16)}function _n(e){return $(e)/255}function $(e){return parseInt(e,16)}function po(e){return{r:e>>16,g:(e&65280)>>8,b:e&255}}var je={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function ho(e){var t={r:0,g:0,b:0},n=1,r=null,i=null,o=null,a=!1,s=!1;return typeof e=="string"&&(e=vo(e)),typeof e=="object"&&(ft(e.r)&&ft(e.g)&&ft(e.b)?(t=so(e.r,e.g,e.b),a=!0,s=String(e.r).substr(-1)==="%"?"prgb":"rgb"):ft(e.h)&&ft(e.s)&&ft(e.v)?(r=se(e.s),i=se(e.v),t=co(e.h,r,i),a=!0,s="hsv"):ft(e.h)&&ft(e.s)&&ft(e.l)&&(r=se(e.s),o=se(e.l),t=fo(e.h,r,o),a=!0,s="hsl"),Object.prototype.hasOwnProperty.call(e,"a")&&(n=e.a)),n=br(n),{ok:a,format:e.format||s,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:n}}var go="[-\\+]?\\d+%?",mo="[-\\+]?\\d*\\.\\d+%?",dt="(?:".concat(mo,")|(?:").concat(go,")"),Pe="[\\s|\\(]+(".concat(dt,")[,|\\s]+(").concat(dt,")[,|\\s]+(").concat(dt,")\\s*\\)?"),Te="[\\s|\\(]+(".concat(dt,")[,|\\s]+(").concat(dt,")[,|\\s]+(").concat(dt,")[,|\\s]+(").concat(dt,")\\s*\\)?"),K={CSS_UNIT:new RegExp(dt),rgb:new RegExp("rgb"+Pe),rgba:new RegExp("rgba"+Te),hsl:new RegExp("hsl"+Pe),hsla:new RegExp("hsla"+Te),hsv:new RegExp("hsv"+Pe),hsva:new RegExp("hsva"+Te),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function vo(e){if(e=e.trim().toLowerCase(),e.length===0)return!1;var t=!1;if(je[e])e=je[e],t=!0;else if(e==="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var n=K.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=K.rgba.exec(e),n?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=K.hsl.exec(e),n?{h:n[1],s:n[2],l:n[3]}:(n=K.hsla.exec(e),n?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=K.hsv.exec(e),n?{h:n[1],s:n[2],v:n[3]}:(n=K.hsva.exec(e),n?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=K.hex8.exec(e),n?{r:$(n[1]),g:$(n[2]),b:$(n[3]),a:_n(n[4]),format:t?"name":"hex8"}:(n=K.hex6.exec(e),n?{r:$(n[1]),g:$(n[2]),b:$(n[3]),format:t?"name":"hex"}:(n=K.hex4.exec(e),n?{r:$(n[1]+n[1]),g:$(n[2]+n[2]),b:$(n[3]+n[3]),a:_n(n[4]+n[4]),format:t?"name":"hex8"}:(n=K.hex3.exec(e),n?{r:$(n[1]+n[1]),g:$(n[2]+n[2]),b:$(n[3]+n[3]),format:t?"name":"hex"}:!1)))))))))}function ft(e){return!!K.CSS_UNIT.exec(String(e))}var Ga=function(){function e(t,n){t===void 0&&(t=""),n===void 0&&(n={});var r;if(t instanceof e)return t;typeof t=="number"&&(t=po(t)),this.originalInput=t;var i=ho(t);this.originalInput=t,this.r=i.r,this.g=i.g,this.b=i.b,this.a=i.a,this.roundA=Math.round(100*this.a)/100,this.format=(r=n.format)!==null&&r!==void 0?r:i.format,this.gradientType=n.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=i.ok}return e.prototype.isDark=function(){return this.getBrightness()<128},e.prototype.isLight=function(){return!this.isDark()},e.prototype.getBrightness=function(){var t=this.toRgb();return(t.r*299+t.g*587+t.b*114)/1e3},e.prototype.getLuminance=function(){var t=this.toRgb(),n,r,i,o=t.r/255,a=t.g/255,s=t.b/255;return o<=.03928?n=o/12.92:n=Math.pow((o+.055)/1.055,2.4),a<=.03928?r=a/12.92:r=Math.pow((a+.055)/1.055,2.4),s<=.03928?i=s/12.92:i=Math.pow((s+.055)/1.055,2.4),.2126*n+.7152*r+.0722*i},e.prototype.getAlpha=function(){return this.a},e.prototype.setAlpha=function(t){return this.a=br(t),this.roundA=Math.round(100*this.a)/100,this},e.prototype.isMonochrome=function(){var t=this.toHsl().s;return t===0},e.prototype.toHsv=function(){var t=Wn(this.r,this.g,this.b);return{h:t.h*360,s:t.s,v:t.v,a:this.a}},e.prototype.toHsvString=function(){var t=Wn(this.r,this.g,this.b),n=Math.round(t.h*360),r=Math.round(t.s*100),i=Math.round(t.v*100);return this.a===1?"hsv(".concat(n,", ").concat(r,"%, ").concat(i,"%)"):"hsva(".concat(n,", ").concat(r,"%, ").concat(i,"%, ").concat(this.roundA,")")},e.prototype.toHsl=function(){var t=Bn(this.r,this.g,this.b);return{h:t.h*360,s:t.s,l:t.l,a:this.a}},e.prototype.toHslString=function(){var t=Bn(this.r,this.g,this.b),n=Math.round(t.h*360),r=Math.round(t.s*100),i=Math.round(t.l*100);return this.a===1?"hsl(".concat(n,", ").concat(r,"%, ").concat(i,"%)"):"hsla(".concat(n,", ").concat(r,"%, ").concat(i,"%, ").concat(this.roundA,")")},e.prototype.toHex=function(t){return t===void 0&&(t=!1),Vn(this.r,this.g,this.b,t)},e.prototype.toHexString=function(t){return t===void 0&&(t=!1),"#"+this.toHex(t)},e.prototype.toHex8=function(t){return t===void 0&&(t=!1),uo(this.r,this.g,this.b,this.a,t)},e.prototype.toHex8String=function(t){return t===void 0&&(t=!1),"#"+this.toHex8(t)},e.prototype.toHexShortString=function(t){return t===void 0&&(t=!1),this.a===1?this.toHexString(t):this.toHex8String(t)},e.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},e.prototype.toRgbString=function(){var t=Math.round(this.r),n=Math.round(this.g),r=Math.round(this.b);return this.a===1?"rgb(".concat(t,", ").concat(n,", ").concat(r,")"):"rgba(".concat(t,", ").concat(n,", ").concat(r,", ").concat(this.roundA,")")},e.prototype.toPercentageRgb=function(){var t=function(n){return"".concat(Math.round(k(n,255)*100),"%")};return{r:t(this.r),g:t(this.g),b:t(this.b),a:this.a}},e.prototype.toPercentageRgbString=function(){var t=function(n){return Math.round(k(n,255)*100)};return this.a===1?"rgb(".concat(t(this.r),"%, ").concat(t(this.g),"%, ").concat(t(this.b),"%)"):"rgba(".concat(t(this.r),"%, ").concat(t(this.g),"%, ").concat(t(this.b),"%, ").concat(this.roundA,")")},e.prototype.toName=function(){if(this.a===0)return"transparent";if(this.a<1)return!1;for(var t="#"+Vn(this.r,this.g,this.b,!1),n=0,r=Object.entries(je);n<r.length;n++){var i=r[n],o=i[0],a=i[1];if(t===a)return o}return!1},e.prototype.toString=function(t){var n=!!t;t=t!=null?t:this.format;var r=!1,i=this.a<1&&this.a>=0,o=!n&&i&&(t.startsWith("hex")||t==="name");return o?t==="name"&&this.a===0?this.toName():this.toRgbString():(t==="rgb"&&(r=this.toRgbString()),t==="prgb"&&(r=this.toPercentageRgbString()),(t==="hex"||t==="hex6")&&(r=this.toHexString()),t==="hex3"&&(r=this.toHexString(!0)),t==="hex4"&&(r=this.toHex8String(!0)),t==="hex8"&&(r=this.toHex8String()),t==="name"&&(r=this.toName()),t==="hsl"&&(r=this.toHslString()),t==="hsv"&&(r=this.toHsvString()),r||this.toHexString())},e.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},e.prototype.clone=function(){return new e(this.toString())},e.prototype.lighten=function(t){t===void 0&&(t=10);var n=this.toHsl();return n.l+=t/100,n.l=ae(n.l),new e(n)},e.prototype.brighten=function(t){t===void 0&&(t=10);var n=this.toRgb();return n.r=Math.max(0,Math.min(255,n.r-Math.round(255*-(t/100)))),n.g=Math.max(0,Math.min(255,n.g-Math.round(255*-(t/100)))),n.b=Math.max(0,Math.min(255,n.b-Math.round(255*-(t/100)))),new e(n)},e.prototype.darken=function(t){t===void 0&&(t=10);var n=this.toHsl();return n.l-=t/100,n.l=ae(n.l),new e(n)},e.prototype.tint=function(t){return t===void 0&&(t=10),this.mix("white",t)},e.prototype.shade=function(t){return t===void 0&&(t=10),this.mix("black",t)},e.prototype.desaturate=function(t){t===void 0&&(t=10);var n=this.toHsl();return n.s-=t/100,n.s=ae(n.s),new e(n)},e.prototype.saturate=function(t){t===void 0&&(t=10);var n=this.toHsl();return n.s+=t/100,n.s=ae(n.s),new e(n)},e.prototype.greyscale=function(){return this.desaturate(100)},e.prototype.spin=function(t){var n=this.toHsl(),r=(n.h+t)%360;return n.h=r<0?360+r:r,new e(n)},e.prototype.mix=function(t,n){n===void 0&&(n=50);var r=this.toRgb(),i=new e(t).toRgb(),o=n/100,a={r:(i.r-r.r)*o+r.r,g:(i.g-r.g)*o+r.g,b:(i.b-r.b)*o+r.b,a:(i.a-r.a)*o+r.a};return new e(a)},e.prototype.analogous=function(t,n){t===void 0&&(t=6),n===void 0&&(n=30);var r=this.toHsl(),i=360/n,o=[this];for(r.h=(r.h-(i*t>>1)+720)%360;--t;)r.h=(r.h+i)%360,o.push(new e(r));return o},e.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new e(t)},e.prototype.monochromatic=function(t){t===void 0&&(t=6);for(var n=this.toHsv(),r=n.h,i=n.s,o=n.v,a=[],s=1/t;t--;)a.push(new e({h:r,s:i,v:o})),o=(o+s)%1;return a},e.prototype.splitcomplement=function(){var t=this.toHsl(),n=t.h;return[this,new e({h:(n+72)%360,s:t.s,l:t.l}),new e({h:(n+216)%360,s:t.s,l:t.l})]},e.prototype.onBackground=function(t){var n=this.toRgb(),r=new e(t).toRgb(),i=n.a+r.a*(1-n.a);return new e({r:(n.r*n.a+r.r*r.a*(1-n.a))/i,g:(n.g*n.a+r.g*r.a*(1-n.a))/i,b:(n.b*n.a+r.b*r.a*(1-n.a))/i,a:i})},e.prototype.triad=function(){return this.polyad(3)},e.prototype.tetrad=function(){return this.polyad(4)},e.prototype.polyad=function(t){for(var n=this.toHsl(),r=n.h,i=[this],o=360/t,a=1;a<t;a++)i.push(new e({h:(r+a*o)%360,s:n.s,l:n.l}));return i},e.prototype.equals=function(t){return this.toRgbString()===new e(t).toRgbString()},e}();function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ot.apply(this,arguments)}function yo(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Xt(e,t)}function He(e){return He=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},He(e)}function Xt(e,t){return Xt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,i){return r.__proto__=i,r},Xt(e,t)}function bo(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function le(e,t,n){return bo()?le=Reflect.construct.bind():le=function(i,o,a){var s=[null];s.push.apply(s,o);var f=Function.bind.apply(i,s),c=new f;return a&&Xt(c,a.prototype),c},le.apply(null,arguments)}function xo(e){return Function.toString.call(e).indexOf("[native code]")!==-1}function Le(e){var t=typeof Map=="function"?new Map:void 0;return Le=function(r){if(r===null||!xo(r))return r;if(typeof r!="function")throw new TypeError("Super expression must either be null or a function");if(typeof t!="undefined"){if(t.has(r))return t.get(r);t.set(r,i)}function i(){return le(r,arguments,He(this).constructor)}return i.prototype=Object.create(r.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Xt(i,r)},Le(e)}var wo=/%[sdj%]/g,Oo=function(){};function Ce(e){if(!e||!e.length)return null;var t={};return e.forEach(function(n){var r=n.field;t[r]=t[r]||[],t[r].push(n)}),t}function z(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=0,o=n.length;if(typeof e=="function")return e.apply(null,n);if(typeof e=="string"){var a=e.replace(wo,function(s){if(s==="%%")return"%";if(i>=o)return s;switch(s){case"%s":return String(n[i++]);case"%d":return Number(n[i++]);case"%j":try{return JSON.stringify(n[i++])}catch(f){return"[Circular]"}break;default:return s}});return a}return e}function Ao(e){return e==="string"||e==="url"||e==="hex"||e==="email"||e==="date"||e==="pattern"}function N(e,t){return!!(e==null||t==="array"&&Array.isArray(e)&&!e.length||Ao(t)&&typeof e=="string"&&!e)}function Eo(e,t,n){var r=[],i=0,o=e.length;function a(s){r.push.apply(r,s||[]),i++,i===o&&n(r)}e.forEach(function(s){t(s,a)})}function In(e,t,n){var r=0,i=e.length;function o(a){if(a&&a.length){n(a);return}var s=r;r=r+1,s<i?t(e[s],o):n([])}o([])}function Ro(e){var t=[];return Object.keys(e).forEach(function(n){t.push.apply(t,e[n]||[])}),t}var $n=function(e){yo(t,e);function t(n,r){var i;return i=e.call(this,"Async Validation Error")||this,i.errors=n,i.fields=r,i}return t}(Le(Error));function So(e,t,n,r,i){if(t.first){var o=new Promise(function(m,p){var v=function(d){return r(d),d.length?p(new $n(d,Ce(d))):m(i)},u=Ro(e);In(u,n,v)});return o.catch(function(m){return m}),o}var a=t.firstFields===!0?Object.keys(e):t.firstFields||[],s=Object.keys(e),f=s.length,c=0,l=[],h=new Promise(function(m,p){var v=function(y){if(l.push.apply(l,y),c++,c===f)return r(l),l.length?p(new $n(l,Ce(l))):m(i)};s.length||(r(l),m(i)),s.forEach(function(u){var y=e[u];a.indexOf(u)!==-1?In(y,n,v):Eo(y,n,v)})});return h.catch(function(m){return m}),h}function Fo(e){return!!(e&&e.message!==void 0)}function Mo(e,t){for(var n=e,r=0;r<t.length;r++){if(n==null)return n;n=n[t[r]]}return n}function zn(e,t){return function(n){var r;return e.fullFields?r=Mo(t,e.fullFields):r=t[n.field||e.fullField],Fo(n)?(n.field=n.field||e.fullField,n.fieldValue=r,n):{message:typeof n=="function"?n():n,fieldValue:r,field:n.field||e.fullField}}}function Un(e,t){if(t){for(var n in t)if(t.hasOwnProperty(n)){var r=t[n];typeof r=="object"&&typeof e[n]=="object"?e[n]=Ot({},e[n],r):e[n]=r}}return e}var xr=function(t,n,r,i,o,a){t.required&&(!r.hasOwnProperty(t.field)||N(n,a||t.type))&&i.push(z(o.messages.required,t.fullField))},Po=function(t,n,r,i,o){(/^\s+$/.test(n)||n==="")&&i.push(z(o.messages.whitespace,t.fullField))},fe,To=function(){if(fe)return fe;var e="[a-fA-F\\d:]",t=function(b){return b&&b.includeBoundaries?"(?:(?<=\\s|^)(?="+e+")|(?<="+e+")(?=\\s|$))":""},n="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",r="[a-fA-F\\d]{1,4}",i=(`
(?:
(?:`+r+":){7}(?:"+r+`|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:`+r+":){6}(?:"+n+"|:"+r+`|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:`+r+":){5}(?::"+n+"|(?::"+r+`){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:`+r+":){4}(?:(?::"+r+"){0,1}:"+n+"|(?::"+r+`){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:`+r+":){3}(?:(?::"+r+"){0,2}:"+n+"|(?::"+r+`){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:`+r+":){2}(?:(?::"+r+"){0,3}:"+n+"|(?::"+r+`){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:`+r+":){1}(?:(?::"+r+"){0,4}:"+n+"|(?::"+r+`){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::`+r+"){0,5}:"+n+"|(?::"+r+`){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),o=new RegExp("(?:^"+n+"$)|(?:^"+i+"$)"),a=new RegExp("^"+n+"$"),s=new RegExp("^"+i+"$"),f=function(b){return b&&b.exact?o:new RegExp("(?:"+t(b)+n+t(b)+")|(?:"+t(b)+i+t(b)+")","g")};f.v4=function(g){return g&&g.exact?a:new RegExp(""+t(g)+n+t(g),"g")},f.v6=function(g){return g&&g.exact?s:new RegExp(""+t(g)+i+t(g),"g")};var c="(?:(?:[a-z]+:)?//)",l="(?:\\S+(?::\\S*)?@)?",h=f.v4().source,m=f.v6().source,p="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",v="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",u="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",y="(?::\\d{2,5})?",d='(?:[/?#][^\\s"]*)?',w="(?:"+c+"|www\\.)"+l+"(?:localhost|"+h+"|"+m+"|"+p+v+u+")"+y+d;return fe=new RegExp("(?:^"+w+"$)","i"),fe},Xn={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},Vt={integer:function(t){return Vt.number(t)&&parseInt(t,10)===t},float:function(t){return Vt.number(t)&&!Vt.integer(t)},array:function(t){return Array.isArray(t)},regexp:function(t){if(t instanceof RegExp)return!0;try{return!!new RegExp(t)}catch(n){return!1}},date:function(t){return typeof t.getTime=="function"&&typeof t.getMonth=="function"&&typeof t.getYear=="function"&&!isNaN(t.getTime())},number:function(t){return isNaN(t)?!1:typeof t=="number"},object:function(t){return typeof t=="object"&&!Vt.array(t)},method:function(t){return typeof t=="function"},email:function(t){return typeof t=="string"&&t.length<=320&&!!t.match(Xn.email)},url:function(t){return typeof t=="string"&&t.length<=2048&&!!t.match(To())},hex:function(t){return typeof t=="string"&&!!t.match(Xn.hex)}},qo=function(t,n,r,i,o){if(t.required&&n===void 0){xr(t,n,r,i,o);return}var a=["integer","float","array","regexp","object","method","email","number","date","url","hex"],s=t.type;a.indexOf(s)>-1?Vt[s](n)||i.push(z(o.messages.types[s],t.fullField,t.type)):s&&typeof n!==t.type&&i.push(z(o.messages.types[s],t.fullField,t.type))},Do=function(t,n,r,i,o){var a=typeof t.len=="number",s=typeof t.min=="number",f=typeof t.max=="number",c=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,l=n,h=null,m=typeof n=="number",p=typeof n=="string",v=Array.isArray(n);if(m?h="number":p?h="string":v&&(h="array"),!h)return!1;v&&(l=n.length),p&&(l=n.replace(c,"_").length),a?l!==t.len&&i.push(z(o.messages[h].len,t.fullField,t.len)):s&&!f&&l<t.min?i.push(z(o.messages[h].min,t.fullField,t.min)):f&&!s&&l>t.max?i.push(z(o.messages[h].max,t.fullField,t.max)):s&&f&&(l<t.min||l>t.max)&&i.push(z(o.messages[h].range,t.fullField,t.min,t.max))},Mt="enum",No=function(t,n,r,i,o){t[Mt]=Array.isArray(t[Mt])?t[Mt]:[],t[Mt].indexOf(n)===-1&&i.push(z(o.messages[Mt],t.fullField,t[Mt].join(", ")))},ko=function(t,n,r,i,o){if(t.pattern){if(t.pattern instanceof RegExp)t.pattern.lastIndex=0,t.pattern.test(n)||i.push(z(o.messages.pattern.mismatch,t.fullField,n,t.pattern));else if(typeof t.pattern=="string"){var a=new RegExp(t.pattern);a.test(n)||i.push(z(o.messages.pattern.mismatch,t.fullField,n,t.pattern))}}},R={required:xr,whitespace:Po,type:qo,range:Do,enum:No,pattern:ko},jo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n,"string")&&!t.required)return r();R.required(t,n,i,a,o,"string"),N(n,"string")||(R.type(t,n,i,a,o),R.range(t,n,i,a,o),R.pattern(t,n,i,a,o),t.whitespace===!0&&R.whitespace(t,n,i,a,o))}r(a)},Ho=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&R.type(t,n,i,a,o)}r(a)},Lo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(n===""&&(n=void 0),N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&(R.type(t,n,i,a,o),R.range(t,n,i,a,o))}r(a)},Co=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&R.type(t,n,i,a,o)}r(a)},Bo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),N(n)||R.type(t,n,i,a,o)}r(a)},Wo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&(R.type(t,n,i,a,o),R.range(t,n,i,a,o))}r(a)},Vo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&(R.type(t,n,i,a,o),R.range(t,n,i,a,o))}r(a)},_o=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(n==null&&!t.required)return r();R.required(t,n,i,a,o,"array"),n!=null&&(R.type(t,n,i,a,o),R.range(t,n,i,a,o))}r(a)},Io=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&R.type(t,n,i,a,o)}r(a)},$o="enum",zo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o),n!==void 0&&R[$o](t,n,i,a,o)}r(a)},Uo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n,"string")&&!t.required)return r();R.required(t,n,i,a,o),N(n,"string")||R.pattern(t,n,i,a,o)}r(a)},Xo=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n,"date")&&!t.required)return r();if(R.required(t,n,i,a,o),!N(n,"date")){var f;n instanceof Date?f=n:f=new Date(n),R.type(t,f,i,a,o),f&&R.range(t,f.getTime(),i,a,o)}}r(a)},Yo=function(t,n,r,i,o){var a=[],s=Array.isArray(n)?"array":typeof n;R.required(t,n,i,a,o,s),r(a)},qe=function(t,n,r,i,o){var a=t.type,s=[],f=t.required||!t.required&&i.hasOwnProperty(t.field);if(f){if(N(n,a)&&!t.required)return r();R.required(t,n,i,s,o,a),N(n,a)||R.type(t,n,i,s,o)}r(s)},Go=function(t,n,r,i,o){var a=[],s=t.required||!t.required&&i.hasOwnProperty(t.field);if(s){if(N(n)&&!t.required)return r();R.required(t,n,i,a,o)}r(a)},$t={string:jo,method:Ho,number:Lo,boolean:Co,regexp:Bo,integer:Wo,float:Vo,array:_o,object:Io,enum:zo,pattern:Uo,date:Xo,url:qe,hex:qe,email:qe,required:Yo,any:Go};function Be(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var t=JSON.parse(JSON.stringify(this));return t.clone=this.clone,t}}}var We=Be(),xe=function(){function e(n){this.rules=null,this._messages=We,this.define(n)}var t=e.prototype;return t.define=function(r){var i=this;if(!r)throw new Error("Cannot configure a schema with no rules");if(typeof r!="object"||Array.isArray(r))throw new Error("Rules must be an object");this.rules={},Object.keys(r).forEach(function(o){var a=r[o];i.rules[o]=Array.isArray(a)?a:[a]})},t.messages=function(r){return r&&(this._messages=Un(Be(),r)),this._messages},t.validate=function(r,i,o){var a=this;i===void 0&&(i={}),o===void 0&&(o=function(){});var s=r,f=i,c=o;if(typeof f=="function"&&(c=f,f={}),!this.rules||Object.keys(this.rules).length===0)return c&&c(null,s),Promise.resolve(s);function l(u){var y=[],d={};function w(b){if(Array.isArray(b)){var x;y=(x=y).concat.apply(x,b)}else y.push(b)}for(var g=0;g<u.length;g++)w(u[g]);y.length?(d=Ce(y),c(y,d)):c(null,s)}if(f.messages){var h=this.messages();h===We&&(h=Be()),Un(h,f.messages),f.messages=h}else f.messages=this.messages();var m={},p=f.keys||Object.keys(this.rules);p.forEach(function(u){var y=a.rules[u],d=s[u];y.forEach(function(w){var g=w;typeof g.transform=="function"&&(s===r&&(s=Ot({},s)),d=s[u]=g.transform(d)),typeof g=="function"?g={validator:g}:g=Ot({},g),g.validator=a.getValidationMethod(g),g.validator&&(g.field=u,g.fullField=g.fullField||u,g.type=a.getType(g),m[u]=m[u]||[],m[u].push({rule:g,value:d,source:s,field:u}))})});var v={};return So(m,f,function(u,y){var d=u.rule,w=(d.type==="object"||d.type==="array")&&(typeof d.fields=="object"||typeof d.defaultField=="object");w=w&&(d.required||!d.required&&u.value),d.field=u.field;function g(O,A){return Ot({},A,{fullField:d.fullField+"."+O,fullFields:d.fullFields?[].concat(d.fullFields,[O]):[O]})}function b(O){O===void 0&&(O=[]);var A=Array.isArray(O)?O:[O];!f.suppressWarning&&A.length&&e.warning("async-validator:",A),A.length&&d.message!==void 0&&(A=[].concat(d.message));var E=A.map(zn(d,s));if(f.first&&E.length)return v[d.field]=1,y(E);if(!w)y(E);else{if(d.required&&!u.value)return d.message!==void 0?E=[].concat(d.message).map(zn(d,s)):f.error&&(E=[f.error(d,z(f.messages.required,d.field))]),y(E);var P={};d.defaultField&&Object.keys(u.value).map(function(M){P[M]=d.defaultField}),P=Ot({},P,u.rule.fields);var T={};Object.keys(P).forEach(function(M){var S=P[M],_=Array.isArray(S)?S:[S];T[M]=_.map(g.bind(null,M))});var F=new e(T);F.messages(f.messages),u.rule.options&&(u.rule.options.messages=f.messages,u.rule.options.error=f.error),F.validate(u.value,u.rule.options||f,function(M){var S=[];E&&E.length&&S.push.apply(S,E),M&&M.length&&S.push.apply(S,M),y(S.length?S:null)})}}var x;if(d.asyncValidator)x=d.asyncValidator(d,u.value,b,u.source,f);else if(d.validator){try{x=d.validator(d,u.value,b,u.source,f)}catch(O){console.error==null||console.error(O),f.suppressValidatorError||setTimeout(function(){throw O},0),b(O.message)}x===!0?b():x===!1?b(typeof d.message=="function"?d.message(d.fullField||d.field):d.message||(d.fullField||d.field)+" fails"):x instanceof Array?b(x):x instanceof Error&&b(x.message)}x&&x.then&&x.then(function(){return b()},function(O){return b(O)})},function(u){l(u)},s)},t.getType=function(r){if(r.type===void 0&&r.pattern instanceof RegExp&&(r.type="pattern"),typeof r.validator!="function"&&r.type&&!$t.hasOwnProperty(r.type))throw new Error(z("Unknown rule type %s",r.type));return r.type||"string"},t.getValidationMethod=function(r){if(typeof r.validator=="function")return r.validator;var i=Object.keys(r),o=i.indexOf("message");return o!==-1&&i.splice(o,1),i.length===1&&i[0]==="required"?$t.required:$t[this.getType(r)]||void 0},e}();xe.register=function(t,n){if(typeof n!="function")throw new Error("Cannot register a validator by type, validator is not a function");$t[t]=n};xe.warning=Oo;xe.messages=We;xe.validators=$t;var Yn=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function Zo(e,t){return!!(e===t||Yn(e)&&Yn(t))}function Ko(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!Zo(e[n],t[n]))return!1;return!0}function Za(e,t){t===void 0&&(t=Ko);var n=null;function r(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];if(n&&n.lastThis===this&&t(i,n.lastArgs))return n.lastResult;var a=e.apply(this,i);return n={lastResult:a,lastArgs:i,lastThis:this},a}return r.clear=function(){n=null},r}var Gn=!1,xt,Ve,_e,de,pe,wr,he,Ie,$e,ze,Or,Ue,Xe,Ar,Er;function B(){if(!Gn){Gn=!0;var e=navigator.userAgent,t=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e),n=/(Mac OS X)|(Windows)|(Linux)/.exec(e);if(Ue=/\b(iPhone|iP[ao]d)/.exec(e),Xe=/\b(iP[ao]d)/.exec(e),ze=/Android/i.exec(e),Ar=/FBAN\/\w+;/i.exec(e),Er=/Mobile/i.exec(e),Or=!!/Win64/.exec(e),t){xt=t[1]?parseFloat(t[1]):t[5]?parseFloat(t[5]):NaN,xt&&document&&document.documentMode&&(xt=document.documentMode);var r=/(?:Trident\/(\d+.\d+))/.exec(e);wr=r?parseFloat(r[1])+4:xt,Ve=t[2]?parseFloat(t[2]):NaN,_e=t[3]?parseFloat(t[3]):NaN,de=t[4]?parseFloat(t[4]):NaN,de?(t=/(?:Chrome\/(\d+\.\d+))/.exec(e),pe=t&&t[1]?parseFloat(t[1]):NaN):pe=NaN}else xt=Ve=_e=pe=de=NaN;if(n){if(n[1]){var i=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);he=i?parseFloat(i[1].replace("_",".")):!0}else he=!1;Ie=!!n[2],$e=!!n[3]}else he=Ie=$e=!1}}var Ye={ie:function(){return B()||xt},ieCompatibilityMode:function(){return B()||wr>xt},ie64:function(){return Ye.ie()&&Or},firefox:function(){return B()||Ve},opera:function(){return B()||_e},webkit:function(){return B()||de},safari:function(){return Ye.webkit()},chrome:function(){return B()||pe},windows:function(){return B()||Ie},osx:function(){return B()||he},linux:function(){return B()||$e},iphone:function(){return B()||Ue},mobile:function(){return B()||Ue||Xe||ze||Er},nativeApp:function(){return B()||Ar},android:function(){return B()||ze},ipad:function(){return B()||Xe}},Jo=Ye,Qo=!!(typeof window<"u"&&window.document&&window.document.createElement),ta={canUseDOM:Qo},Rr=ta,Sr;Rr.canUseDOM&&(Sr=document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature("","")!==!0);function ea(e,t){if(!Rr.canUseDOM||t&&!("addEventListener"in document))return!1;var n="on"+e,r=n in document;if(!r){var i=document.createElement("div");i.setAttribute(n,"return;"),r=typeof i[n]=="function"}return!r&&Sr&&e==="wheel"&&(r=document.implementation.hasFeature("Events.wheel","3.0")),r}var na=ea,Zn=10,Kn=40,Jn=800;function Fr(e){var t=0,n=0,r=0,i=0;return"detail"in e&&(n=e.detail),"wheelDelta"in e&&(n=-e.wheelDelta/120),"wheelDeltaY"in e&&(n=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=n,n=0),r=t*Zn,i=n*Zn,"deltaY"in e&&(i=e.deltaY),"deltaX"in e&&(r=e.deltaX),(r||i)&&e.deltaMode&&(e.deltaMode==1?(r*=Kn,i*=Kn):(r*=Jn,i*=Jn)),r&&!t&&(t=r<1?-1:1),i&&!n&&(n=i<1?-1:1),{spinX:t,spinY:n,pixelX:r,pixelY:i}}Fr.getEventType=function(){return Jo.firefox()?"DOMMouseScroll":na("wheel")?"wheel":"mousewheel"};var Ka=Fr;/**
* Checks if an event is supported in the current execution environment.
*
* NOTE: This will not work correctly for non-generic events such as `change`,
* `reset`, `load`, `error`, and `select`.
*
* Borrows from Modernizr.
*
* @param {string} eventNameSuffix Event name, e.g. "click".
* @param {?boolean} capture Check if the capture phase is supported.
* @return {boolean} True if the event is supported.
* @internal
* @license Modernizr 3.0.0pre (Custom Build) | MIT
*/const Ht=Math.min,Et=Math.max,me=Math.round,ce=Math.floor,nt=e=>({x:e,y:e}),ra={left:"right",right:"left",bottom:"top",top:"bottom"},ia={start:"end",end:"start"};function Ge(e,t,n){return Et(e,Ht(t,n))}function Jt(e,t){return typeof e=="function"?e(t):e}function Rt(e){return e.split("-")[0]}function Qt(e){return e.split("-")[1]}function Mr(e){return e==="x"?"y":"x"}function sn(e){return e==="y"?"height":"width"}const oa=new Set(["top","bottom"]);function pt(e){return oa.has(Rt(e))?"y":"x"}function fn(e){return Mr(pt(e))}function aa(e,t,n){n===void 0&&(n=!1);const r=Qt(e),i=fn(e),o=sn(i);let a=i==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=ve(a)),[a,ve(a)]}function sa(e){const t=ve(e);return[Ze(e),t,Ze(t)]}function Ze(e){return e.replace(/start|end/g,t=>ia[t])}const Qn=["left","right"],tr=["right","left"],fa=["top","bottom"],ca=["bottom","top"];function ua(e,t,n){switch(e){case"top":case"bottom":return n?t?tr:Qn:t?Qn:tr;case"left":case"right":return t?fa:ca;default:return[]}}function la(e,t,n,r){const i=Qt(e);let o=ua(Rt(e),n==="start",r);return i&&(o=o.map(a=>a+"-"+i),t&&(o=o.concat(o.map(Ze)))),o}function ve(e){return e.replace(/left|right|bottom|top/g,t=>ra[t])}function da(e){return I({top:0,right:0,bottom:0,left:0},e)}function Pr(e){return typeof e!="number"?da(e):{top:e,right:e,bottom:e,left:e}}function ye(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function er(e,t,n){let{reference:r,floating:i}=e;const o=pt(t),a=fn(t),s=sn(a),f=Rt(t),c=o==="y",l=r.x+r.width/2-i.width/2,h=r.y+r.height/2-i.height/2,m=r[s]/2-i[s]/2;let p;switch(f){case"top":p={x:l,y:r.y-i.height};break;case"bottom":p={x:l,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:h};break;case"left":p={x:r.x-i.width,y:h};break;default:p={x:r.x,y:r.y}}switch(Qt(t)){case"start":p[a]-=m*(n&&c?-1:1);break;case"end":p[a]+=m*(n&&c?-1:1);break}return p}const pa=(e,t,n)=>st(null,null,function*(){const{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:a}=n,s=o.filter(Boolean),f=yield a.isRTL==null?void 0:a.isRTL(t);let c=yield a.getElementRects({reference:e,floating:t,strategy:i}),{x:l,y:h}=er(c,r,f),m=r,p={},v=0;for(let u=0;u<s.length;u++){const{name:y,fn:d}=s[u],{x:w,y:g,data:b,reset:x}=yield d({x:l,y:h,initialPlacement:r,placement:m,strategy:i,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});l=w!=null?w:l,h=g!=null?g:h,p=ut(I({},p),{[y]:I(I({},p[y]),b)}),x&&v<=50&&(v++,typeof x=="object"&&(x.placement&&(m=x.placement),x.rects&&(c=x.rects===!0?yield a.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:l,y:h}=er(c,m,f)),u=-1)}return{x:l,y:h,placement:m,strategy:i,middlewareData:p}});function cn(e,t){return st(this,null,function*(){var n;t===void 0&&(t={});const{x:r,y:i,platform:o,rects:a,elements:s,strategy:f}=e,{boundary:c="clippingAncestors",rootBoundary:l="viewport",elementContext:h="floating",altBoundary:m=!1,padding:p=0}=Jt(t,e),v=Pr(p),y=s[m?h==="floating"?"reference":"floating":h],d=ye(yield o.getClippingRect({element:(n=yield o.isElement==null?void 0:o.isElement(y))==null||n?y:y.contextElement||(yield o.getDocumentElement==null?void 0:o.getDocumentElement(s.floating)),boundary:c,rootBoundary:l,strategy:f})),w=h==="floating"?{x:r,y:i,width:a.floating.width,height:a.floating.height}:a.reference,g=yield o.getOffsetParent==null?void 0:o.getOffsetParent(s.floating),b=(yield o.isElement==null?void 0:o.isElement(g))?(yield o.getScale==null?void 0:o.getScale(g))||{x:1,y:1}:{x:1,y:1},x=ye(o.convertOffsetParentRelativeRectToViewportRelativeRect?yield o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:w,offsetParent:g,strategy:f}):w);return{top:(d.top-x.top+v.top)/b.y,bottom:(x.bottom-d.bottom+v.bottom)/b.y,left:(d.left-x.left+v.left)/b.x,right:(x.right-d.right+v.right)/b.x}})}const ha=e=>({name:"arrow",options:e,fn(n){return st(this,null,function*(){const{x:r,y:i,placement:o,rects:a,platform:s,elements:f,middlewareData:c}=n,{element:l,padding:h=0}=Jt(e,n)||{};if(l==null)return{};const m=Pr(h),p={x:r,y:i},v=fn(o),u=sn(v),y=yield s.getDimensions(l),d=v==="y",w=d?"top":"left",g=d?"bottom":"right",b=d?"clientHeight":"clientWidth",x=a.reference[u]+a.reference[v]-p[v]-a.floating[u],O=p[v]-a.reference[v],A=yield s.getOffsetParent==null?void 0:s.getOffsetParent(l);let E=A?A[b]:0;(!E||!(yield s.isElement==null?void 0:s.isElement(A)))&&(E=f.floating[b]||a.floating[u]);const P=x/2-O/2,T=E/2-y[u]/2-1,F=Ht(m[w],T),M=Ht(m[g],T),S=F,_=E-y[u]-M,j=E/2-y[u]/2+P,q=Ge(S,j,_),D=!c.arrow&&Qt(o)!=null&&j!==q&&a.reference[u]/2-(j<S?F:M)-y[u]/2<0,C=D?j<S?j-S:j-_:0;return{[v]:p[v]+C,data:I({[v]:q,centerOffset:j-q-C},D&&{alignmentOffset:C}),reset:D}})}}),ga=function(e){return e===void 0&&(e={}),{name:"flip",options:e,fn(n){return st(this,null,function*(){var r,i;const{placement:o,middlewareData:a,rects:s,initialPlacement:f,platform:c,elements:l}=n,j=Jt(e,n),{mainAxis:h=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:u="none",flipAlignment:y=!0}=j,d=Fe(j,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((r=a.arrow)!=null&&r.alignmentOffset)return{};const w=Rt(o),g=pt(f),b=Rt(f)===f,x=yield c.isRTL==null?void 0:c.isRTL(l.floating),O=p||(b||!y?[ve(f)]:sa(f)),A=u!=="none";!p&&A&&O.push(...la(f,y,u,x));const E=[f,...O],P=yield cn(n,d),T=[];let F=((i=a.flip)==null?void 0:i.overflows)||[];if(h&&T.push(P[w]),m){const q=aa(o,s,x);T.push(P[q[0]],P[q[1]])}if(F=[...F,{placement:o,overflows:T}],!T.every(q=>q<=0)){var M,S;const q=(((M=a.flip)==null?void 0:M.index)||0)+1,D=E[q];if(D&&(!(m==="alignment"?g!==pt(D):!1)||F.every(H=>pt(H.placement)===g?H.overflows[0]>0:!0)))return{data:{index:q,overflows:F},reset:{placement:D}};let C=(S=F.filter(L=>L.overflows[0]<=0).sort((L,H)=>L.overflows[1]-H.overflows[1])[0])==null?void 0:S.placement;if(!C)switch(v){case"bestFit":{var _;const L=(_=F.filter(H=>{if(A){const X=pt(H.placement);return X===g||X==="y"}return!0}).map(H=>[H.placement,H.overflows.filter(X=>X>0).reduce((X,mt)=>X+mt,0)]).sort((H,X)=>H[1]-X[1])[0])==null?void 0:_[0];L&&(C=L);break}case"initialPlacement":C=f;break}if(o!==C)return{reset:{placement:C}}}return{}})}}},ma=new Set(["left","top"]);function va(e,t){return st(this,null,function*(){const{placement:n,platform:r,elements:i}=e,o=yield r.isRTL==null?void 0:r.isRTL(i.floating),a=Rt(n),s=Qt(n),f=pt(n)==="y",c=ma.has(a)?-1:1,l=o&&f?-1:1,h=Jt(t,e);let{mainAxis:m,crossAxis:p,alignmentAxis:v}=typeof h=="number"?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return s&&typeof v=="number"&&(p=s==="end"?v*-1:v),f?{x:p*l,y:m*c}:{x:m*c,y:p*l}})}const ya=function(e){return e===void 0&&(e=0),{name:"offset",options:e,fn(n){return st(this,null,function*(){var r,i;const{x:o,y:a,placement:s,middlewareData:f}=n,c=yield va(n,e);return s===((r=f.offset)==null?void 0:r.placement)&&(i=f.arrow)!=null&&i.alignmentOffset?{}:{x:o+c.x,y:a+c.y,data:ut(I({},c),{placement:s})}})}}},ba=function(e){return e===void 0&&(e={}),{name:"shift",options:e,fn(n){return st(this,null,function*(){const{x:r,y:i,placement:o}=n,d=Jt(e,n),{mainAxis:a=!0,crossAxis:s=!1,limiter:f={fn:w=>{let{x:g,y:b}=w;return{x:g,y:b}}}}=d,c=Fe(d,["mainAxis","crossAxis","limiter"]),l={x:r,y:i},h=yield cn(n,c),m=pt(Rt(o)),p=Mr(m);let v=l[p],u=l[m];if(a){const w=p==="y"?"top":"left",g=p==="y"?"bottom":"right",b=v+h[w],x=v-h[g];v=Ge(b,v,x)}if(s){const w=m==="y"?"top":"left",g=m==="y"?"bottom":"right",b=u+h[w],x=u-h[g];u=Ge(b,u,x)}const y=f.fn(ut(I({},n),{[p]:v,[m]:u}));return ut(I({},y),{data:{x:y.x-r,y:y.y-i,enabled:{[p]:a,[m]:s}}})})}}};function we(){return typeof window!="undefined"}function Ct(e){return Tr(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ot(e){var t;return(t=(Tr(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Tr(e){return we()?e instanceof Node||e instanceof U(e).Node:!1}function J(e){return we()?e instanceof Element||e instanceof U(e).Element:!1}function it(e){return we()?e instanceof HTMLElement||e instanceof U(e).HTMLElement:!1}function nr(e){return!we()||typeof ShadowRoot=="undefined"?!1:e instanceof ShadowRoot||e instanceof U(e).ShadowRoot}const xa=new Set(["inline","contents"]);function te(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=Q(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!xa.has(i)}const wa=new Set(["table","td","th"]);function Oa(e){return wa.has(Ct(e))}const Aa=[":popover-open",":modal"];function Oe(e){return Aa.some(t=>{try{return e.matches(t)}catch(n){return!1}})}const Ea=["transform","translate","scale","rotate","perspective"],Ra=["transform","translate","scale","rotate","perspective","filter"],Sa=["paint","layout","strict","content"];function un(e){const t=ln(),n=J(e)?Q(e):e;return Ea.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Ra.some(r=>(n.willChange||"").includes(r))||Sa.some(r=>(n.contain||"").includes(r))}function Fa(e){let t=ht(e);for(;it(t)&&!Lt(t);){if(un(t))return t;if(Oe(t))return null;t=ht(t)}return null}function ln(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Ma=new Set(["html","body","#document"]);function Lt(e){return Ma.has(Ct(e))}function Q(e){return U(e).getComputedStyle(e)}function Ae(e){return J(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ht(e){if(Ct(e)==="html")return e;const t=e.assignedSlot||e.parentNode||nr(e)&&e.host||ot(e);return nr(t)?t.host:t}function qr(e){const t=ht(e);return Lt(t)?e.ownerDocument?e.ownerDocument.body:e.body:it(t)&&te(t)?t:qr(t)}function Yt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=qr(e),o=i===((r=e.ownerDocument)==null?void 0:r.body),a=U(i);if(o){const s=Ke(a);return t.concat(a,a.visualViewport||[],te(i)?i:[],s&&n?Yt(s):[])}return t.concat(i,Yt(i,[],n))}function Ke(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Dr(e){const t=Q(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=it(e),o=i?e.offsetWidth:n,a=i?e.offsetHeight:r,s=me(n)!==o||me(r)!==a;return s&&(n=o,r=a),{width:n,height:r,$:s}}function dn(e){return J(e)?e:e.contextElement}function Pt(e){const t=dn(e);if(!it(t))return nt(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:o}=Dr(t);let a=(o?me(n.width):n.width)/r,s=(o?me(n.height):n.height)/i;return(!a||!Number.isFinite(a))&&(a=1),(!s||!Number.isFinite(s))&&(s=1),{x:a,y:s}}const Pa=nt(0);function Nr(e){const t=U(e);return!ln()||!t.visualViewport?Pa:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ta(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==U(e)?!1:t}function St(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),o=dn(e);let a=nt(1);t&&(r?J(r)&&(a=Pt(r)):a=Pt(e));const s=Ta(o,n,r)?Nr(o):nt(0);let f=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,l=i.width/a.x,h=i.height/a.y;if(o){const m=U(o),p=r&&J(r)?U(r):r;let v=m,u=Ke(v);for(;u&&r&&p!==v;){const y=Pt(u),d=u.getBoundingClientRect(),w=Q(u),g=d.left+(u.clientLeft+parseFloat(w.paddingLeft))*y.x,b=d.top+(u.clientTop+parseFloat(w.paddingTop))*y.y;f*=y.x,c*=y.y,l*=y.x,h*=y.y,f+=g,c+=b,v=U(u),u=Ke(v)}}return ye({width:l,height:h,x:f,y:c})}function pn(e,t){const n=Ae(e).scrollLeft;return t?t.left+n:St(ot(e)).left+n}function kr(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=r.left+t.scrollLeft-(n?0:pn(e,r)),o=r.top+t.scrollTop;return{x:i,y:o}}function qa(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e;const o=i==="fixed",a=ot(r),s=t?Oe(t.floating):!1;if(r===a||s&&o)return n;let f={scrollLeft:0,scrollTop:0},c=nt(1);const l=nt(0),h=it(r);if((h||!h&&!o)&&((Ct(r)!=="body"||te(a))&&(f=Ae(r)),it(r))){const p=St(r);c=Pt(r),l.x=p.x+r.clientLeft,l.y=p.y+r.clientTop}const m=a&&!h&&!o?kr(a,f,!0):nt(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+l.x+m.x,y:n.y*c.y-f.scrollTop*c.y+l.y+m.y}}function Da(e){return Array.from(e.getClientRects())}function Na(e){const t=ot(e),n=Ae(e),r=e.ownerDocument.body,i=Et(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=Et(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+pn(e);const s=-n.scrollTop;return Q(r).direction==="rtl"&&(a+=Et(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}function ka(e,t){const n=U(e),r=ot(e),i=n.visualViewport;let o=r.clientWidth,a=r.clientHeight,s=0,f=0;if(i){o=i.width,a=i.height;const c=ln();(!c||c&&t==="fixed")&&(s=i.offsetLeft,f=i.offsetTop)}return{width:o,height:a,x:s,y:f}}const ja=new Set(["absolute","fixed"]);function Ha(e,t){const n=St(e,!0,t==="fixed"),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=it(e)?Pt(e):nt(1),a=e.clientWidth*o.x,s=e.clientHeight*o.y,f=i*o.x,c=r*o.y;return{width:a,height:s,x:f,y:c}}function rr(e,t,n){let r;if(t==="viewport")r=ka(e,n);else if(t==="document")r=Na(ot(e));else if(J(t))r=Ha(t,n);else{const i=Nr(e);r={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return ye(r)}function jr(e,t){const n=ht(e);return n===t||!J(n)||Lt(n)?!1:Q(n).position==="fixed"||jr(n,t)}function La(e,t){const n=t.get(e);if(n)return n;let r=Yt(e,[],!1).filter(s=>J(s)&&Ct(s)!=="body"),i=null;const o=Q(e).position==="fixed";let a=o?ht(e):e;for(;J(a)&&!Lt(a);){const s=Q(a),f=un(a);!f&&s.position==="fixed"&&(i=null),(o?!f&&!i:!f&&s.position==="static"&&!!i&&ja.has(i.position)||te(a)&&!f&&jr(e,a))?r=r.filter(l=>l!==a):i=s,a=ht(a)}return t.set(e,r),r}function Ca(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e;const a=[...n==="clippingAncestors"?Oe(t)?[]:La(t,this._c):[].concat(n),r],s=a[0],f=a.reduce((c,l)=>{const h=rr(t,l,i);return c.top=Et(h.top,c.top),c.right=Ht(h.right,c.right),c.bottom=Ht(h.bottom,c.bottom),c.left=Et(h.left,c.left),c},rr(t,s,i));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function Ba(e){const{width:t,height:n}=Dr(e);return{width:t,height:n}}function Wa(e,t,n){const r=it(t),i=ot(t),o=n==="fixed",a=St(e,!0,o,t);let s={scrollLeft:0,scrollTop:0};const f=nt(0);function c(){f.x=pn(i)}if(r||!r&&!o)if((Ct(t)!=="body"||te(i))&&(s=Ae(t)),r){const p=St(t,!0,o,t);f.x=p.x+t.clientLeft,f.y=p.y+t.clientTop}else i&&c();o&&!r&&i&&c();const l=i&&!r&&!o?kr(i,s):nt(0),h=a.left+s.scrollLeft-f.x-l.x,m=a.top+s.scrollTop-f.y-l.y;return{x:h,y:m,width:a.width,height:a.height}}function De(e){return Q(e).position==="static"}function ir(e,t){if(!it(e)||Q(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ot(e)===n&&(n=n.ownerDocument.body),n}function Hr(e,t){const n=U(e);if(Oe(e))return n;if(!it(e)){let i=ht(e);for(;i&&!Lt(i);){if(J(i)&&!De(i))return i;i=ht(i)}return n}let r=ir(e,t);for(;r&&Oa(r)&&De(r);)r=ir(r,t);return r&&Lt(r)&&De(r)&&!un(r)?n:r||Fa(e)||n}const Va=function(e){return st(this,null,function*(){const t=this.getOffsetParent||Hr,n=this.getDimensions,r=yield n(e.floating);return{reference:Wa(e.reference,yield t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}})};function _a(e){return Q(e).direction==="rtl"}const Ia={convertOffsetParentRelativeRectToViewportRelativeRect:qa,getDocumentElement:ot,getClippingRect:Ca,getOffsetParent:Hr,getElementRects:Va,getClientRects:Da,getDimensions:Ba,getScale:Pt,isElement:J,isRTL:_a};function Lr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function $a(e,t){let n=null,r;const i=ot(e);function o(){var s;clearTimeout(r),(s=n)==null||s.disconnect(),n=null}function a(s,f){s===void 0&&(s=!1),f===void 0&&(f=1),o();const c=e.getBoundingClientRect(),{left:l,top:h,width:m,height:p}=c;if(s||t(),!m||!p)return;const v=ce(h),u=ce(i.clientWidth-(l+m)),y=ce(i.clientHeight-(h+p)),d=ce(l),g={rootMargin:-v+"px "+-u+"px "+-y+"px "+-d+"px",threshold:Et(0,Ht(1,f))||1};let b=!0;function x(O){const A=O[0].intersectionRatio;if(A!==f){if(!b)return a();A?a(!1,A):r=setTimeout(()=>{a(!1,1e-7)},1e3)}A===1&&!Lr(c,e.getBoundingClientRect())&&a(),b=!1}try{n=new IntersectionObserver(x,ut(I({},g),{root:i.ownerDocument}))}catch(O){n=new IntersectionObserver(x,g)}n.observe(e)}return a(!0),o}function Ja(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:o=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:s=typeof IntersectionObserver=="function",animationFrame:f=!1}=r,c=dn(e),l=i||o?[...c?Yt(c):[],...Yt(t)]:[];l.forEach(d=>{i&&d.addEventListener("scroll",n,{passive:!0}),o&&d.addEventListener("resize",n)});const h=c&&s?$a(c,n):null;let m=-1,p=null;a&&(p=new ResizeObserver(d=>{let[w]=d;w&&w.target===c&&p&&(p.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var g;(g=p)==null||g.observe(t)})),n()}),c&&!f&&p.observe(c),p.observe(t));let v,u=f?St(e):null;f&&y();function y(){const d=St(e);u&&!Lr(u,d)&&n(),u=d,v=requestAnimationFrame(y)}return n(),()=>{var d;l.forEach(w=>{i&&w.removeEventListener("scroll",n),o&&w.removeEventListener("resize",n)}),h==null||h(),(d=p)==null||d.disconnect(),p=null,f&&cancelAnimationFrame(v)}}const Qa=cn,ts=ya,es=ba,ns=ga,rs=ha,is=(e,t,n)=>{const r=new Map,i=I({platform:Ia},n),o=ut(I({},i.platform),{_c:r});return pa(e,t,ut(I({},i),{platform:o}))};var Tt=null,Ne=null,lt=null,or="z-index-manage",bt=null,ar="z-index-style",Cr="m",Br="s",Gt={m:1e3,s:1e3};function hn(){return Tt||typeof document!="undefined"&&(Tt=document),Tt}function Wr(){return Tt&&!Ne&&(Ne=Tt.body||Tt.getElementsByTagName("body")[0]),Ne}function za(){var e=0,t=hn();if(t){var n=Wr();if(n)for(var r=n.getElementsByTagName("*"),i=0;i<r.length;i++){var o=r[i];if(o&&o.style&&o.nodeType===1){var a=o.style.zIndex;a&&/^\d+$/.test(a)&&(e=Math.max(e,Number(a)))}}}return e}function Ua(){if(!bt){var e=hn();e&&(bt=e.getElementById(ar),bt||(bt=e.createElement("style"),bt.id=ar,e.getElementsByTagName("head")[0].appendChild(bt)))}return bt}function Vr(){var e=Ua();if(e){var t="--dom-",n="-z-index";e.innerHTML=":root{"+t+"main"+n+":"+Ee()+";"+t+"sub"+n+":"+vn()+"}"}}function _r(){if(!lt){var e=hn();if(e&&(lt=e.getElementById(or),!lt)){var t=Wr();t&&(lt=e.createElement("div"),lt.id=or,lt.style.display="none",t.appendChild(lt),gn(Gt.m),mn(Gt.s))}}return lt}function Ir(e){return function(t){if(t){t=Number(t),Gt[e]=t;var n=_r();n&&(n.dataset?n.dataset[e]=t+"":n.setAttribute("data-"+e,t+""))}return Vr(),Gt[e]}}var gn=Ir(Cr);function $r(e,t){return function(r){var i,o=_r();if(o){var a=o.dataset?o.dataset[e]:o.getAttribute("data-"+e);a&&(i=Number(a))}return i||(i=Gt[e]),r?Number(r)<i?t():r:i}}var Ee=$r(Cr,zr);function zr(){return gn(Ee()+1)}var mn=Ir(Br),Ur=$r(Br,Xr);function vn(){return Ee()+Ur()}function Xr(){return mn(Ur()+1),vn()}var os={setCurrent:gn,getCurrent:Ee,getNext:zr,setSubCurrent:mn,getSubCurrent:vn,getSubNext:Xr,getMax:za};Vr();export{os as D,fr as E,xe as S,Ga as T,Ka as Y,rs as a,Ja as b,is as c,Qa as d,ns as f,Za as m,ts as o,es as s,Ya as y};
