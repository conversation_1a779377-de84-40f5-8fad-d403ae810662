import { RouteLocationNormalizedLoaded } from 'vue-router';
import { MaskTab, MaskProps } from '../types';
import { useTabs } from './useTabs';
import { VNode, RendererNode, RendererElement, Ref } from 'vue';
export type UseContentOptions = ReturnType<typeof useTabs>;
export declare function useContent(options: Partial<UseContentOptions>, props: MaskProps): {
    createView: (module: any, route: RouteLocationNormalizedLoaded) => any;
    openDialog: (tab: MaskTab) => Promise<{
        vnode: VNode<RendererNode, RendererElement, {
            [key: string]: any;
        }>;
        destroy: () => void;
    }>;
    refresh: (tab: MaskTab) => Promise<void>;
    exclude: Ref<string[], string[]>;
    cleanCache: (tabs: MaskTab[]) => Promise<void>;
    hasDialog: (url: string) => boolean;
    closeDialog: (tab: MaskTab) => void;
    closeDialogs: (tabs?: MaskTab[]) => void;
};
