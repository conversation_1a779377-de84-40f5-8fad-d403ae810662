(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.UniUIMaterial=e())})(this,function(){"use strict";var _=Object.defineProperty,ee=Object.defineProperties;var te=Object.getOwnPropertyDescriptors;var r=Object.getOwnPropertySymbols;var le=Object.prototype.hasOwnProperty,ae=Object.prototype.propertyIsEnumerable;var s=(t,e,l)=>e in t?_(t,e,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[e]=l,n=(t,e)=>{for(var l in e||(e={}))le.call(e,l)&&s(t,l,e[l]);if(r)for(var l of r(e))ae.call(e,l)&&s(t,l,e[l]);return t},i=(t,e)=>ee(t,te(e));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const t="0.12.70";function e(X,Y){return X.map(Z=>i(n({},Z),{package:Y}))}const l={name:"UniBadge",label:"数字角标",categoryId:"ext",props:[{name:"text",title:"角标内容",setters:"StringSetter"},{name:"type",title:"颜色类型",defaultValue:"default",setters:"SelectSetter",options:["default","primary","success","warning","error"]},{name:"size",title:"Badge 大小",defaultValue:"small",setters:"SelectSetter",options:["normal","small"]},{name:"is-dot",title:"不展示数字，只有一个小点",defaultValue:!1,setters:"BooleanSetter"},{name:"max-num",title:"展示封顶的数字值，超过 99 显示99+",defaultValue:"99",setters:["StringSetter","NumberSetter"]},{name:"custom-style",title:"自定义 Badge 样式, 样式对象语法",defaultValue:{},setters:"ObjectSetter"},{name:"inverted",title:"是否无需背景颜色，为 true 时，背景颜色将变为文字的字体颜色",defaultValue:!1,setters:"BooleanSetter"},{name:"absolute",title:"开启绝对定位, 角标将定位到其包裹的标签的四个角上",defaultValue:"rightTop",setters:"SelectSetter",options:["rightTop","rightBottom","leftBottom","leftTop"]},{name:"offset",title:"距定位角中心点的偏移量",defaultValue:[0,0],setters:"ArraySetter"}],events:["click"],snippet:{props:{text:"1"}}},o=[{name:"UniBreadcrumb",label:"面包屑",categoryId:"ext",props:[{name:"separator",title:"分隔符",defaultValue:"/",setters:"StringSetter"},{name:"separatorClass",title:"图标分隔符 class",setters:"StringSetter"}],snippet:{props:{separator:"/"},children:[{name:"UniBreadcrumbItem",children:"首页"}]}},{name:"UniBreadcrumbItem",label:"面包屑项",parentIncludes:["UniBreadcrumb"],categoryId:"ext",props:[{name:"to",title:"路由跳转页面路径",setters:"StringSetter"},{name:"replace",title:"在使用 to 进行路由跳转时，启用 replace 将不会向 history 添加新记录(仅 h5 支持）",setters:"BooleanSetter"}],snippet:{children:"菜单项"}}],u={name:"UniCalendar",label:"日历",categoryId:"ext",props:[{name:"date",title:"自定义当前时间，默认为今天",setters:"StringSetter"},{name:"lunar",title:"显示农历",defaultValue:!1,setters:"BooleanSetter"},{name:"startDate",title:"日期选择范围-开始日期",setters:"StringSetter"},{name:"endDate",title:"日期选择范围-结束日期",setters:"StringSetter"},{name:"range",title:"范围选择",defaultValue:!1,setters:"BooleanSetter"},{name:"insert",title:"插入模式,可选值",defaultValue:!1,setters:"BooleanSetter"},{name:"clearDate",title:"弹窗模式是否清空上次选择内容",defaultValue:!0,setters:"BooleanSetter"},{name:"selected",title:"打点",setters:"ArraySetter"},{name:"showMonth",title:"是否显示月份为背景",defaultValue:!0,setters:"BooleanSetter"}],events:["change","confirm","monthSwitch","open"],snippet:{}},d={name:"UniCard",label:"卡片",categoryId:"ext",props:[{name:"title",title:"标题文字",setters:"StringSetter"},{name:"sub-title",title:"副标题文字",defaultValue:"",setters:"StringSetter"},{name:"extra",title:"标题额外信息",defaultValue:"",setters:"StringSetter"},{name:"thumbnail",title:"标题左侧缩略图,支持网络图片，本地图片，本图片需要传入一个绝对路径",defaultValue:"",setters:"StringSetter"},{name:"cover",title:"封面图,支持网络图片，本地图片，本图片需要传入一个绝对路径",defaultValue:"",setters:"StringSetter"},{name:"is-full",title:"卡片内容是否通栏，为true时将去除padding值",defaultValue:!1,setters:"BooleanSetter"},{name:"is-shadow",title:"卡片内容是否开启阴影",defaultValue:!1,setters:"BooleanSetter"},{name:"shadow",title:"卡片阴影,需符合 css 值",defaultValue:"0px 0px 3px 1px rgba(0, 0, 0, 0.08)",setters:"StringSetter"},{name:"margin",title:"卡片外边距",defaultValue:"10px",setters:"StringSetter"},{name:"spacing",title:"卡片内边距",defaultValue:"10px",setters:"StringSetter"},{name:"padding",title:"卡片内容内边距",defaultValue:"10px",setters:"StringSetter"},{name:"border",title:"卡片边框",defaultValue:!0,setters:"BooleanSetter"},{name:"mode",title:"卡片模式(弃用)",defaultValue:"basic",setters:"SelectSetter",options:["basic","style","title"]},{name:"note",title:"底部信息[弃用]",defaultValue:"",setters:"StringSetter"}],events:["click"],slots:["cover","title","actions","header","footer"],snippet:{props:{"is-full":!0,"is-shadow":!1,title:"基础卡片",extra:"额外信息"},children:[{name:"View",children:"卡片组件通用来显示完整独立的一段信息，同时让用户理解他的作用。例如一篇文章的预览图、作者信息、时间等，卡片通常是更复杂和更详细信息的入口点。"}]}},m=[{name:"UniCollapse",label:"折叠面板",categoryId:"ext",props:[{name:"modelValue",title:"当前激活面板改变时触发(如果是手风琴模式，参数类型为string，否则为array)",setters:["StringSetter","ArraySetter"]},{name:"accordion",title:"是否开启手风琴效果",defaultValue:!1,setters:"BooleanSetter"}],events:["change","update:modelValue"],snippet:{props:{modelValue:["0"]},children:[{name:"UniCollapseItem",props:{title:'默认开启"'},children:[{name:"Text",children:"折叠内容主体，这是一段比较长内容。默认折叠主要内容，只显示当前项标题。点击标题展开，才能看到这段文字。再次点击标题，折叠内容。"}]},{name:"UniCollapseItem",props:{title:"折叠内容"},children:[{name:"Text",children:"折叠内容主体，这是一段比较长内容。默认折叠主要内容，只显示当前项标题。点击标题展开，才能看到这段文字。再次点击标题，折叠内容。"}]}]}},{name:"UniCollapseItem",label:"折叠面板项",parentIncludes:["UniCollapse"],categoryId:"ext",props:[{name:"title",title:"标题文字",defaultValue:"",setters:"StringSetter"},{name:"thumb",title:"标题左侧缩略图",defaultValue:"",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"open",title:"是否展开面板",defaultValue:!1,setters:"BooleanSetter"},{name:"show-animation",title:"开启动画",defaultValue:!1,setters:"BooleanSetter"},{name:"border",title:"折叠面板内容分隔线",defaultValue:!0,setters:"BooleanSetter"},{name:"title-border",title:"折叠面板标题分隔线",defaultValue:"auto",setters:"SelectSetter",options:["auto","none","show"]},{name:"show-arrow",title:"是否显示右侧箭头",defaultValue:!0,setters:"BooleanSetter"}],slots:["default","title"],snippet:{props:{title:"基本使用"},children:[{name:"Text",children:"折叠内容"}]}}],c={name:"UniCombox",label:"组合框",categoryId:"ext",props:[{name:"label",title:"标签文字",setters:"StringSetter"},{name:"modelValue",title:"combox的值",setters:"StringSetter"},{name:"labelWidth",title:"标签宽度",defaultValue:"auto",setters:"StringSetter"},{name:"placeholder",title:"输入框占位符",setters:"StringSetter"},{name:"candidates",title:"候选字段",defaultValue:[],setters:["ArraySetter","StringSetter"]},{name:"emptyTips",title:"无匹配项时的提示语",defaultValue:"无匹配项",setters:"StringSetter"}],events:["input","update:modelValue"],snippet:{props:{candidates:["北京","南京","东京","武汉","天津","上海","海口"],placeholder:"请选择所在城市"}}},p={name:"UniCountdown",label:"倒计时",categoryId:"ext",props:[{name:"backgroundColor",title:"背景色",defaultValue:"#FFFFFF",setters:"ColorSetter"},{name:"color",title:"文字颜色",defaultValue:"#000000",setters:"ColorSetter"},{name:"splitorColor",title:"分割符号颜色",defaultValue:"#000000",setters:"ColorSetter"},{name:"day",title:"天数",defaultValue:0,setters:"NumberSetter"},{name:"hour",title:"小时",defaultValue:0,setters:"NumberSetter"},{name:"minute",title:"分钟",defaultValue:0,setters:"NumberSetter"},{name:"second",title:"秒",defaultValue:0,setters:"NumberSetter"},{name:"showDay",title:"是否显示天数",defaultValue:!0,setters:"BooleanSetter"},{name:"showHour",title:"是否显示小时",defaultValue:!0,setters:"BooleanSetter"},{name:"showMinute",title:"是否显示分钟",defaultValue:!0,setters:"BooleanSetter"},{name:"showColon",title:"是否以冒号为分隔符",defaultValue:!0,setters:"BooleanSetter"},{name:"start",title:"是否初始化组件后就开始倒计时",defaultValue:!0,setters:"BooleanSetter"}],events:["timeup"],snippet:{props:{day:1,hour:1,minute:2,second:40}}},S={name:"UniDataCheckbox",label:"数据选择器",categoryId:"ext",props:[{name:"modelValue",title:"默认值，multiple=true时为 Array类型，否则为 String或Number类型",setters:["ArraySetter","StringSetter","NumberSetter"]},{name:"localdata",title:"本地渲染数据",setters:"ArraySetter"},{name:"mode",title:"显示模式",defaultValue:"default",setters:"SelectSetter",options:["default","list","button","tag"]},{name:"multiple",title:"是否多选",defaultValue:!1,setters:"BooleanSetter"},{name:"min",title:"最小选择个数 ，multiple为true时生效",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"max",title:"最大选择个数 ，multiple为true时生效",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"wrap",title:"是否换行显示",defaultValue:"",setters:"BooleanSetter"},{name:"icon",title:"list 列表模式下 icon 显示的位置",defaultValue:"left",setters:"SelectSetter",options:["left","right"]},{name:"selectedColor",title:"选中颜色",defaultValue:"#007aff",setters:["ColorSetter","StringSetter"]},{name:"selectedTextColor",title:"选中文本颜色，如不填写则自动显示",defaultValue:"#333",setters:["ColorSetter","StringSetter"]},{name:"emptyText",title:"没有数据时显示的文字 ，本地数据无效",defaultValue:"暂无数据",setters:"StringSetter"},{name:"map",title:"字段映射，将text/value映射到数据中的其他字段",defaultValue:{text:"text",value:"value"},setters:"ObjectSetter"}],events:["change","update:modelValue"],snippet:{props:{localdata:[{text:"男",value:0},{text:"女",value:1,disable:!0},{text:"未知",value:2}],modelValue:0}}},f={name:"UniDataPicker",label:"级联选择框",categoryId:"ext",props:[{name:"modelValue",title:"绑定数据",setters:["StringSetter","NumberSetter"]},{name:"spaceInfo",title:"服务空间配置",setters:"ObjectSetter"},{name:"localdata",title:"数据",setters:"ArraySetter"},{name:"preload",title:"预加载数据",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"clear-icon",title:"是否显示清除按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"ellipsis",title:"是否隐藏 tab 标签过长的文本",defaultValue:!0,setters:"BooleanSetter"},{name:"step-searh",title:"分步查询时，点击节点请求数据",defaultValue:!0,setters:"BooleanSetter"},{name:"self-field",title:"分步查询时当前字段名称",defaultValue:"",setters:"StringSetter"},{name:"parent-field",title:"分步查询时父字段名称",defaultValue:"",setters:"StringSetter"},{name:"collection",title:"表名。支持输入多个表名，用 , 分割",defaultValue:"",setters:"StringSetter"},{name:"field",title:"查询字段，多个字段用 , 分割",defaultValue:"",setters:"StringSetter"},{name:"where",title:"查询条件",setters:"StringSetter"},{name:"orderby",title:"排序字段及正序倒叙设置",defaultValue:"",setters:"StringSetter"},{name:"popup-title",title:"弹出层标题",defaultValue:"",setters:"StringSetter"},{name:"map",title:"字段映射，将text/value映射到数据中的其他字段",defaultValue:{text:"text",value:"value"},setters:"ObjectSetter"}],events:["change","nodeclick","popupopened","popupclosed"],slots:["default"],snippet:{props:{placeholder:"请选择班级","popup-title":"请选择所在地区",localdata:[{text:"一年级",value:"1-0",children:[{text:"1.1班",value:"1-1"},{text:"1.2班",value:"1-2"}]},{text:"二年级",value:"2-0",children:[{text:"2.1班",value:"2-1"},{text:"2.2班",value:"2-2"}]},{text:"三年级",value:"3-0",disable:!0}]}}},V={name:"UniDataSelect",label:"下拉框",categoryId:"ext",props:[{name:"modelValue",title:"已选择数据的 value 值（当其值为0时不进行初始化赋值）",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"localdata",title:"本地渲染数据",setters:"ArraySetter"},{name:"clear",title:"是否可以清空已选项",setters:"BooleanSetter"},{name:"label",title:"左侧标题",setters:"StringSetter"},{name:"placeholder",title:"输入框的提示文字",defaultValue:"请选择",setters:"StringSetter"},{name:"emptyTips",title:"没有数据时显示的文字 ，本地数据无效",defaultValue:"暂无数据",setters:"StringSetter"},{name:"placement",title:"弹出时位置",defaultValue:"bottom",setters:"SelectSetter",options:["bottom","top"]},{name:"page-size",title:"返回的数据量",defaultValue:20,setters:"NumberSetter"}],events:["change","update:modelValue"],snippet:{props:{localdata:[{value:0,text:"篮球"},{value:1,text:"足球"},{value:2,text:"游泳"}],modelValue:1}}},g={name:"UniDateformat",label:"日期格式化",categoryId:"ext",props:[{name:"date",title:"要格式化的日期对象/日期字符串/时间戳",defaultValue:Date.now(),setters:["ObjectSetter","StringSetter","NumberSetter"]},{name:"threshold",title:"转化类型阈值",defaultValue:[0,0],setters:"ArraySetter"},{name:"format",title:"格式字符串",defaultValue:"yyyy/MM/dd hh:mm:ss",setters:"StringSetter"},{name:"locale",title:"格式化使用的语言",defaultValue:"zh",setters:"SelectSetter",options:["zh","en"]}],events:["change","update:modelValue"],snippet:{props:{date:"2020/10/20 20:20:20"}}},b={name:"UniDatetimePicker",label:"日期选择器",categoryId:"ext",props:[{name:"type",title:"选择器类型",defaultValue:"datetime",setters:"SelectSetter",options:["date","daterange","datetime","datetimerange"]},{name:"value",title:"输入框当前值",defaultValue:"",setters:["StringSetter","BooleanSetter","ArraySetter","ExpressionSetter"]},{name:"start",title:"最小值，可以使用日期的字符串(String)、时间戳(Number)",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"end",title:"最大值，可以使用日期的字符串(String)、时间戳(Number)",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"return-type",title:"返回值格式",defaultValue:"string",setters:"SelectSetter",options:["timestamp","string","date"]},{name:"border",title:"是否有边框",defaultValue:!0,setters:"BooleanSetter"},{name:"rangeSeparator",title:"选择范围时的分隔符",defaultValue:"-",setters:"StringSetter"},{name:"placeholder",title:"非范围选择时的占位内容",defaultValue:"",setters:"StringSetter"},{name:"start-placeholder",title:"范围选择时开始日期的占位内容",defaultValue:"",setters:"StringSetter"},{name:"end-placeholder",title:"范围选择时结束日期的占位内容",defaultValue:"",setters:"StringSetter"},{name:"disabled",title:"是否不可选择",defaultValue:!1,setters:"BooleanSetter"},{name:"clear-icon",title:"是否显示清除按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"hide-second",title:"是否显示秒，只显示时分",defaultValue:!1,setters:"BooleanSetter"}],events:["change","maskClick","show"],slots:["default"],snippet:{props:{type:"date"}}},h={name:"UniDrawer",label:"抽屉侧滑菜单",categoryId:"ext",props:[{name:"mask",title:"是否显示遮罩",defaultValue:!0,setters:"BooleanSetter"},{name:"maskClick",title:"点击遮罩是否可以关闭抽屉",defaultValue:!0,setters:"BooleanSetter"},{name:"mode",title:"Drawer滑出位置",defaultValue:"left",setters:"SelectSetter",options:["left","right"]},{name:"width",title:"Drawer 宽度",defaultValue:220,setters:"NumberSetter"}],events:["change"],snippet:{}},x={name:"UniEasyinput",label:"增强输入框",categoryId:"ext",props:[{name:"modelValue",title:"输入内容",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"type",title:"输入框的类型",defaultValue:"text",setters:"SelectSetter",options:["text","textarea","password","number","idcard","digit"]},{name:"clearable",title:"是否显示右侧清空内容的图标控件(输入框有内容且不禁用时显示)，点击可清空输入框内容",defaultValue:!0,setters:"BooleanSetter"},{name:"autoHeight",title:"是否自动增高输入区域，type为textarea时有效",defaultValue:!1,setters:"BooleanSetter"},{name:"placeholder",title:"输入框的提示文字",defaultValue:"",setters:"StringSetter"},{name:"placeholderStyle",title:"placeholder的样式",defaultValue:"",setters:"StringSetter"},{name:"focus",title:"是否自动获得焦点",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否不可输入",defaultValue:!1,setters:"BooleanSetter"},{name:"maxlength",title:"最大输入长度，设置为 -1 的时候不限制最大长度",defaultValue:140,setters:"NumberSetter"},{name:"confirmType",title:'设置键盘右下角按钮的文字，仅在type="text"时生效',defaultValue:"done",setters:"SelectSetter",options:["send","search","next","go","done"]},{name:"clearSize",title:"清除图标的大小，单位px",defaultValue:24,setters:"NumberSetter"},{name:"prefixIcon",title:"输入框头部图标",defaultValue:"",setters:"StringSetter"},{name:"suffixIcon",title:"输入框尾部图标",defaultValue:"",setters:"StringSetter"},{name:"trim",title:"是否自动去除空格，传入类型为 Boolean 时，自动去除前后空格",setters:["SelectSetter"],defaultValue:!1,options:[{label:"false",value:!1},{label:"true",value:!0},{label:"both",value:"both"},{label:"left",value:"left"},{label:"right",value:"right"},{label:"all",value:"all"},{label:"none",value:"none"}]},{name:"inputBorder",title:"是否显示input输入框的边框",defaultValue:!0,setters:"BooleanSetter"},{name:"styles",title:"样式自定义",defaultValue:"",setters:"ObjectSetter"},{name:"passwordIcon",title:"type=password 时，是否显示小眼睛图标",defaultValue:!0,setters:"BooleanSetter"},{name:"adjust-position",title:"弹起键盘时，是否上推页面，平台差异性与内置input组件一致",defaultValue:!0,setters:"BooleanSetter"},{name:"primaryColor",title:"设置清除按钮focus时的颜色",defaultValue:"#2979ff",setters:"ColorSetter"},{name:"cursorSpacing",title:"指定光标与键盘的距离，单位 px ",defaultValue:0,setters:"NumberSetter"}],events:["input","clear","focus","blur","confirm","iconClick","change","keyboardheightchange","update:modelValue"],snippet:{props:{placeholder:"请输入内容"}}},y={name:"UniFab",label:"悬浮按钮",categoryId:"ext",props:[{name:"pattern",title:"可选样式配置项",setters:"ObjectSetter"},{name:"horizontal",title:"水平对齐方式",defaultValue:"left",setters:"SelectSetter",options:["left","right"]},{name:"vertical",title:"垂直对齐方式",defaultValue:"bottom",setters:"SelectSetter",options:["bottom","top"]},{name:"direction",title:"展开菜单显示方式",defaultValue:"horizontal",setters:"SelectSetter",options:["horizontal","vertical"]},{name:"popMenu",title:"是否使用弹出菜单",defaultValue:!0,setters:"BooleanSetter"},{name:"content",title:"展开菜单内容配置项",setters:"ArraySetter"}],events:["trigger","fabClick"],snippet:{props:{horizontal:"left",vertical:"bottom",direction:"horizontal",pattern:{color:"#7A7E83",backgroundColor:"#fff",selectedColor:"#007AFF",buttonColor:"#007AFF",iconColor:"#fff"},content:[{iconPath:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",selectedIconPath:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",text:"相册",active:!1},{iconPath:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",selectedIconPath:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",text:"首页",active:!1},{iconPath:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",selectedIconPath:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",text:"收藏",active:!1}]}}},B={name:"UniFav",label:"收藏按钮",categoryId:"ext",props:[{name:"star",title:"按钮是否带星星",defaultValue:!0,setters:"BooleanSetter"},{name:"bgColor",title:"未收藏时的背景色",defaultValue:"#eeeeee",setters:"ColorSetter"},{name:"bgColorChecked",title:"已收藏时的背景色",defaultValue:"#007aff",setters:"ColorSetter"},{name:"fgColor",title:"未收藏时的文字颜色",defaultValue:"#666666",setters:"ColorSetter"},{name:"fgColorChecked",title:"已收藏时的文字颜色",defaultValue:"#FFFFFF",setters:"ColorSetter"},{name:"circle",title:"是否为圆角",defaultValue:!1,setters:"BooleanSetter"},{name:"checked",title:"是否为已收藏",defaultValue:!1,setters:"BooleanSetter"},{name:"contentText",title:"收藏按钮文字",defaultValue:{contentDefault:"收藏",contentFav:"已收藏"},setters:"ObjectSetter"}],events:["click"],snippet:{props:{checked:!1}}},v={name:"UniFilePicker",label:"文件选择上传",categoryId:"ext",props:[{name:"modelValue",title:"组件数据，通常用来回显",setters:["ArraySetter","ObjectSetter"]},{name:"disabled",title:"组件禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"组件只读，不可选择，不显示进度，不显示删除按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"return-type",title:"限制 value 格式，当为 object 时 ，组件只能单选，且会覆盖",defaultValue:"array",setters:"SelectSetter",options:["array","object"]},{name:"disable-preview",title:"用图片预览，仅 mode:grid生效",defaultValue:!1,setters:"BooleanSetter"},{name:"del-icon",title:"是否显示删除按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"auto-upload",title:"是否自动上传，值为false则只触发@select,可自行上传",defaultValue:!0,setters:"BooleanSetter"},{name:"limit",title:"最大选择个数 ，h5 会自动忽略多选的部分",defaultValue:9,setters:["NumberSetter","StringSetter"]},{name:"title	",title:"组件标题，右侧显示上传计数",defaultValue:"",setters:"StringSetter"},{name:"mode",title:"选择文件后的文件列表样式",defaultValue:"list",setters:"SelectSetter",options:["list","grid"]},{name:"file-mediatype",title:"选择文件类型,all 只支持 H5 和微信小程序平台",defaultValue:"image",setters:"SelectSetter",options:["image","video","all"]},{name:"file-extname",title:"选择文件后缀，字符串的情况下需要用逗号分隔",defaultValue:"",setters:["ArraySetter","StringSetter"]},{name:"list-styles",title:"mode:list 时的样式",defaultValue:"",setters:"ObjectSetter"},{name:"image-styles",title:"mode:grid 时的样式",defaultValue:"",setters:"ObjectSetter"},{name:"sizeType",title:"original 原图，compressed 压缩图，默认二者都有",defaultValue:["original","compressed"],setters:"SelectSetter",options:[{label:"default",value:["original","compressed"]},{label:"original",value:"original"},{label:"compressed",value:"compressed"}]},{name:"sourceType",title:"album 从相册选图，camera 使用相机，默认二者都有",defaultValue:["album","camera"],setters:"SelectSetter",options:[{label:"default",value:["album","camera"]},{label:"album",value:"album"},{label:"camera",value:"camera"}]}],events:["select","progress","success","fail","delete"],slots:["default"],snippet:{props:{limit:"9",title:"最多选择9张图片"}}},I=[{name:"UniForms",label:"表单",categoryId:"ext",props:[{name:"mode",title:"表单数据",defaultValue:"",setters:"ObjectSetter"},{name:"rules",title:"表单校验规则",defaultValue:"",setters:"ObjectSetter"},{name:"validateTrigger",title:"表单校验时机,blur仅在 uni-easyinput 中生效	",defaultValue:"submit",setters:"SelectSetter",options:["bind","submit","blur"]},{name:"label-position",title:"label 位置",defaultValue:"left",setters:"SelectSetter",options:["left","top"]},{name:"label-width",title:"label 宽度，单位 px",defaultValue:75,setters:["NumberSetter","StringSetter"]},{name:"label-align",title:"label 居中方式",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"err-show-type",title:"表单错误信息提示方式",defaultValue:"undertext",setters:"SelectSetter",options:["undertext","toast","modal"]},{name:"border",title:"是否显示分格线",defaultValue:!1,setters:"BooleanSetter"},{name:"value",title:"表单数据，兼容vue2",defaultValue:"",setters:"ObjectSetter"},{name:"modelValue",title:"表单数据，兼容vue3",defaultValue:"",setters:"ObjectSetter"}],events:["validate","update:modelValue"],snippet:{props:{modelValue:{name:"",age:""}},children:[{name:"UniFormsItem",props:{label:"姓名",required:!0},children:[{name:"UniEasyinput",props:{placeholder:"请输入姓名"}}]},{name:"UniFormsItem",props:{label:"年龄",required:!0},children:[{name:"UniEasyinput",props:{placeholder:"请输入年龄"}}]}]}},{name:"UniFormsItem",label:"表单项",categoryId:"ext",props:[{name:"name",title:"表单域的属性名，在使用校验规则时必填",defaultValue:"",setters:["StringSetter","ArraySetter"]},{name:"rules",title:"表单校验规则",defaultValue:"",setters:"ObjectSetter"},{name:"required",title:'label 右边显示红色"*"号，样式显示不会对校验规则产生效果',defaultValue:!1,setters:"BooleanSetter"},{name:"label",title:"输入框左边的文字提示",defaultValue:"",setters:"StringSetter"},{name:"label-width",title:"label的宽度，单位px",defaultValue:70,setters:"NumberSetter"},{name:"error-message",title:"显示的错误提示内容，如果为空字符串或者false，则不显示错误信息",defaultValue:"",setters:"StringSetter"},{name:"label-align",title:"label的文字对齐方式",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"label-position",title:"label的文字的位置",defaultValue:"left",setters:"SelectSetter",options:["top","left"]},{name:"validateTrigger",title:"表单校验时机",defaultValue:"submit",setters:"SelectSetter",options:["bind","submit"]},{name:"left-icon",title:"label左边的图标，限uni-ui的图标名称",defaultValue:"",setters:"StringSetter"},{name:"icon-color",title:"左边通过icon配置的图标的颜色",defaultValue:"#606266",setters:"ColorSetter"}],slots:["default","label"],snippet:{props:{label:"姓名",required:!0},children:[{name:"UniEasyinput",props:{placeholder:"请输入姓名"}}]}}],C={name:"UniGoodsNav",label:"商品导航",categoryId:"ext",props:[{name:"options",title:"组件参数",defaultValue:"",setters:"ArraySetter"},{name:"buttonGroup",title:"组件按钮组参数",defaultValue:"",setters:"ArraySetter"},{name:"fill",title:"按钮是否平铺",defaultValue:!1,setters:"BooleanSetter"}],events:["click","buttonClick"],snippet:{}},U=[{name:"UniGrid",label:"宫格",categoryId:"ext",props:[{name:"column",title:"每列显示个数",defaultValue:3,setters:"NumberSetter"},{name:"borderColor",title:"边框颜色",defaultValue:"#d0dee5",setters:"ColorSetter"},{name:"showBorder",title:"是否显示边框",defaultValue:!0,setters:"BooleanSetter"},{name:"square",title:"是否方形显示",defaultValue:!0,setters:"BooleanSetter"},{name:"highlight",title:"点击背景是否高亮",defaultValue:!0,setters:"BooleanSetter"}],events:["change"],snippet:{props:{column:"4",highlight:!0},children:[{name:"UniGridItem",children:[{name:"View",children:[{name:"UniIcons",props:{type:"image",size:30,color:"#777"}},{name:"Text",children:"文本信息"}]}]}]}},{name:"UniGridItem",label:"宫格项",parentIncludes:["UniGrid"],categoryId:"ext",props:[{name:"index",title:"子组件的唯一标识 ，点击grid会返回当前的标识",defaultValue:"",setters:"NumberSetter"}],snippet:{children:[{name:"View",children:[{name:"UniIcons",props:{type:"image",size:30,color:"#777"}},{name:"Text",children:"文本信息"}]}]}}],w={name:"UniGroup",label:"分组",categoryId:"ext",props:[{name:"title",title:"主标题",defaultValue:"",setters:"StringSetter"},{name:"top",title:"分组间隔",defaultValue:"",setters:"NumberSetter"},{name:"mode",title:"模式 ，card 为卡片模式",defaultValue:"",setters:"SelectSetter",options:["card"]}],snippet:{props:{title:"基本模式"},children:[{name:"View",children:" 分组内容 "}]}},N={name:"UniIcons",label:"图标",categoryId:"ext",props:[{name:"size",title:"图标大小",defaultValue:24,setters:"NumberSetter"},{name:"type",title:"图标图案，参考示例",defaultValue:"",setters:"StringSetter"},{name:"color",title:"图标颜色",defaultValue:"",setters:"ColorSetter"},{name:"customPrefix",title:"自定义图标",defaultValue:"",setters:"StringSetter"},{name:"fontFamily",title:"自定义图标",defaultValue:"",setters:"StringSetter"}],events:["click"],snippet:{props:{type:"home"}}},T={name:"UniIndexedList",label:"索引列表",categoryId:"ext",props:[{name:"options",title:"索引列表需要的数据对象",setters:"ObjectSetter"},{name:"showSelect",title:"展示模式",defaultValue:!1,setters:"BooleanSetter"}],events:["click"],snippet:{props:{options:[{letter:"A",data:["阿克苏机场","阿拉山口机场","阿勒泰机场","阿里昆莎机场","安庆天柱山机场","澳门国际机场"]},{letter:"B",data:["保山机场","包头机场","北海福成机场","北京南苑机场","北京首都国际机场"]}],showSelect:!0}}},k={name:"UniLink",label:"超链接",categoryId:"ext",props:[{name:"href",title:"链接地址",defaultValue:"",setters:"StringSetter"},{name:"text",title:"显示文字",defaultValue:"",setters:"StringSetter"},{name:"download",title:"H5平台下载文件名",defaultValue:"",setters:"StringSetter"},{name:"showUnderLine",title:"是否显示下划线",defaultValue:!0,setters:"BooleanSetter"},{name:"copyTips",title:"在小程序端复制链接时的提示语",defaultValue:"已自动复制网址，请在手机浏览器里粘贴该网址",setters:"StringSetter"},{name:"color",title:"链接文字颜色",defaultValue:"#999999",setters:"ColorSetter"},{name:"fontSize",title:"链接文字大小，单位px",defaultValue:"14",setters:"StringSetter"}],slots:["default"],snippet:{props:{href:"https://uniapp.dcloud.io/",text:"https://uniapp.dcloud.io/"}}},j=[{name:"UniList",label:"列表",categoryId:"ext",props:[{name:"border",title:"是否显示边框",defaultValue:!0,setters:"BooleanSetter"}],snippet:{children:[{name:"UniListItem",props:{title:"列表文字"}},{name:"UniListItem",props:{title:"列表禁用状态",disabled:"true"}}]}},{name:"UniListItem",label:"列表项",categoryId:"ext",props:[{name:"title",title:"标题",defaultValue:"",setters:"StringSetter"},{name:"note",title:"描述",defaultValue:"",setters:"StringSetter"},{name:"ellipsis",title:"title 是否溢出隐藏",defaultValue:0,setters:"SelectSetter",options:[{label:"0",value:0},{label:"1",value:1},{label:"2",value:2}]},{name:"thumb",title:"左侧缩略图，若thumb有值，则不会显示扩展图标",defaultValue:"",setters:"StringSetter"},{name:"thumbSize",title:"略缩图尺寸",defaultValue:"medium",setters:"SelectSetter",options:["lg","medium","sm"]},{name:"showBadge",title:"是否显示数字角标",defaultValue:!1,setters:"BooleanSetter"},{name:"badgeText",title:"数字角标内容",defaultValue:"",setters:"StringSetter"},{name:"badgeType",title:"数字角标类型",defaultValue:"",setters:"ObjectSetter"},{name:"rightText",title:"右侧文字内容",defaultValue:"",setters:"StringSetter"},{name:"disabled",title:"是否禁用",defaultValue:!1,setters:"BooleanSetter"},{name:"showArrow",title:"是否显示箭头图标",defaultValue:!1,setters:"BooleanSetter"},{name:"link",title:"新页面跳转方式",defaultValue:"navigateTo",setters:"SelectSetter",options:["navigateTo","redirectTo","reLaunch","switchTab"]},{name:"to",title:"新页面跳转地址",defaultValue:"",setters:"StringSetter"},{name:"clickable",title:"是否开启点击反馈",defaultValue:!1,setters:"BooleanSetter"},{name:"showSwitch",title:"是否显示Switch",defaultValue:!1,setters:"BooleanSetter"},{name:"switchChecked",title:"Switch是否被选中",defaultValue:!1,setters:"BooleanSetter"},{name:"showExtraIcon",title:"左侧是否显示扩展图标",defaultValue:!1,setters:"BooleanSetter"},{name:"extraIcon",title:"扩展图标参数",defaultValue:"",setters:"ObjectSetter"},{name:"direction",title:"排版方向",defaultValue:"row",setters:"SelectSetter",options:["row","column"]}],events:["click","switchChange"],slots:["header","body","footer"],snippet:{props:{title:"列表文字"}}},{name:"UniListChat",label:"列表聊天项",categoryId:"ext",props:[{name:"title",title:"标题",defaultValue:"",setters:"StringSetter"},{name:"note",title:"描述",defaultValue:"",setters:"StringSetter"},{name:"clickable",title:"是否开启点击反馈",defaultValue:!1,setters:"BooleanSetter"},{name:"badgeText",title:"数字角标内容，设置为 dot 将显示圆点",defaultValue:"",setters:"StringSetter"},{name:"badgePositon",title:"角标位置",defaultValue:"right",setters:"StringSetter"},{name:"link",title:"是否展示右侧箭头并开启点击反馈，可选值见下表",defaultValue:"navigateTo",setters:"SelectSetter",options:["navigateTo","redirectTo","reLaunch","switchTab"]},{name:"to",title:"跳转页面地址",defaultValue:"",setters:"StringSetter"},{name:"time",title:"右侧时间显示",defaultValue:"",setters:"StringSetter"},{name:"avatarCircle",title:"是否显示圆形头像",defaultValue:!1,setters:"BooleanSetter"},{name:"avatar",title:"头像地址，avatarCircle 不填时生效",defaultValue:"",setters:"StringSetter"},{name:"avatarList",title:'头像组，格式为 [{url:""}]',defaultValue:"",setters:"ArraySetter"}],events:["click"],slots:["default"],snippet:{props:{avatarCircle:!0,badgeText:12,title:"uni-app",avatar:"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg",note:"您收到一条新的消息",time:"2020-02-02 20:20"}}}],$={name:"UniLoadMore",label:"加载更多",categoryId:"ext",props:[{name:"iconSize",title:"指定图标大小",defaultValue:24,setters:"NumberSetter"},{name:"status",title:"loading 的状态",defaultValue:"more",setters:"SelectSetter",options:["more","loading","noMore"]},{name:"showIcon",title:"是否显示 loading 图标",defaultValue:!0,setters:"BooleanSetter"},{name:"showText",title:"是否显示文本",defaultValue:!0,setters:"BooleanSetter"},{name:"iconType",title:"指定图标样式",defaultValue:"auto",setters:"SelectSetter",options:["snow","circle","auto"]},{name:"color",title:"图标和文字颜色",defaultValue:"#777777",setters:"ColorSetter"},{name:"contentText",title:"各状态文字说明",defaultValue:{contentdown:"上拉显示更多",contentrefresh:"正在加载...",contentnomore:"没有更多数据了"},setters:"ObjectSetter"}],events:["clickLoadMore"],snippet:{}},F={name:"UniNavBar",label:"导航栏",categoryId:"ext",props:[{name:"title",title:"标题文字",defaultValue:"",setters:"StringSetter"},{name:"leftText",title:"左侧按钮文本",defaultValue:"",setters:"StringSetter"},{name:"rightText",title:"右侧按钮文本",defaultValue:"",setters:"StringSetter"},{name:"leftIcon",title:"左侧按钮图标",defaultValue:"",setters:"StringSetter"},{name:"rightIcon",title:"右侧按钮图标",defaultValue:"",setters:"StringSetter"},{name:"color",title:"图标和文字颜色",defaultValue:"#000000",setters:"ColorSetter"},{name:"backgroundColor",title:"导航栏背景颜色",defaultValue:"#ffffff",setters:"ColorSetter"},{name:"fixed",title:"是否固定顶部",defaultValue:!1,setters:"BooleanSetter"},{name:"statusBar",title:"是否包含状态栏",defaultValue:!1,setters:"BooleanSetter"},{name:"shadow",title:"导航栏下是否有阴影",defaultValue:!1,setters:"BooleanSetter"},{name:"border",title:"导航栏下是否有边框",defaultValue:!0,setters:"BooleanSetter"},{name:"height",title:"导航栏高度",defaultValue:44,setters:["NumberSetter","StringSetter"]},{name:"dark",title:"导航栏开启暗黑模式",defaultValue:!1,setters:"BooleanSetter"},{name:"leftWidth",title:"导航栏左侧插槽宽度",defaultValue:120,setters:["NumberSetter","StringSetter"]},{name:"rightWidth",title:"导航栏右侧插槽宽度",defaultValue:120,setters:["NumberSetter","StringSetter"]},{name:"stat",title:"是否开启统计标题功能",defaultValue:"60px",setters:["BooleanSetter","StringSetter"]}],events:["clickLeft","clickRight"],slots:["default","left","right"],snippet:{props:{dark:!0,fixed:!0,shadow:!0,backgroundColor:"#007AFF",statusBar:!0,leftIcon:"left",leftText:"返回",title:"自定义导航栏"}}},A={name:"UniNoticeBar",label:"通告栏	",categoryId:"ext",props:[{name:"speed",title:"文字滚动的速度，默认100px/秒",defaultValue:100,setters:"NumberSetter"},{name:"text",title:"显示文字",defaultValue:"",setters:"StringSetter"},{name:"background-color",title:"背景颜色",defaultValue:"#fffbe8",setters:"ColorSetter"},{name:"color",title:"文字颜色",defaultValue:"#de8c17",setters:"ColorSetter"},{name:"moreColor",title:"查看更多文字的颜色",defaultValue:"#999999",setters:"ColorSetter"},{name:"moreText",title:"设置“查看更多”的文本",defaultValue:"",setters:"StringSetter"},{name:"single",title:"是否单行",defaultValue:!1,setters:"BooleanSetter"},{name:"scrollable",title:"是否滚动，为true时，NoticeBar为单行",defaultValue:!1,setters:"BooleanSetter"},{name:"showIcon",title:"是否显示左侧喇叭图标",defaultValue:!1,setters:"BooleanSetter"},{name:"showClose",title:"是否显示右侧关闭按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"showGetMore",title:"是否显示右侧查看更多图标，为true时，NoticeBar为单行",defaultValue:!1,setters:"BooleanSetter"}],events:["click","close","getmore"],snippet:{props:{text:"uni-app 版正式发布，开发一次，同时发布iOS、Android、H5、微信小程序、支付宝小程序、百度小程序、头条小程序等7大平台。"}}},O={name:"UniNumberBox",label:"数字输入框",categoryId:"ext",props:[{name:"modelValue",title:"输入框当前值",defaultValue:0,setters:"NumberSetter"},{name:"min",title:"最小值",defaultValue:0,setters:"NumberSetter"},{name:"max",title:"最大值",defaultValue:100,setters:"NumberSetter"},{name:"step",title:"每次点击改变的间隔大小",defaultValue:1,setters:"NumberSetter"},{name:"disabled",title:"是否为禁用状态",defaultValue:!1,setters:"BooleanSetter"},{name:"width",title:"宽度（单位：px）",defaultValue:40,setters:"NumberSetter"}],events:["change","focus","blur"],snippet:{}},z={name:"UniPagination",label:"分页器",categoryId:"ext",props:[{name:"prevText",title:"左侧按钮文字",defaultValue:"上一页",setters:"StringSetter"},{name:"nextText",title:"右侧按钮文字",defaultValue:"下一页",setters:"StringSetter"},{name:"modelValue",title:"当前页",defaultValue:1,setters:"NumberSetter"},{name:"current",title:"当前页, 优先级高于 value",defaultValue:1,setters:"NumberSetter"},{name:"total",title:"数据总量",defaultValue:0,setters:"NumberSetter"},{name:"pageSize",title:"每页数据量",defaultValue:10,setters:"NumberSetter"},{name:"showIcon",title:"是否以 icon 形式展示按钮",defaultValue:!1,setters:"BooleanSetter"}],events:["change"],snippet:{props:{total:50}}},P=[{name:"UniPopup",label:"弹出层",categoryId:"ext",props:[{name:"animation",title:"是否开启动画",defaultValue:!0,setters:"BooleanSetter"},{name:"type",title:"弹出方式",defaultValue:"center",setters:"SelectSetter",options:["top","center","bottom","left","right","message","dialog","share"]},{name:"mask-click",title:"蒙版点击是否关闭弹窗",defaultValue:!0,setters:"BooleanSetter"},{name:"is-mask-click",title:"蒙版点击是否关闭弹窗",defaultValue:!0,setters:"BooleanSetter"},{name:"mask-background-color",title:"蒙版颜色，建议使用 rgba 颜色值",defaultValue:"rgba(0,0,0,0.4)",setters:["ColorSetter","StringSetter"]},{name:"background-color",title:"主窗口背景色",defaultValue:"none",setters:"StringSetter"},{name:"borderRadius",title:"设置圆角",defaultValue:"",setters:"StringSetter"},{name:"safe-area",title:"是否适配底部安全区",defaultValue:!0,setters:"BooleanSetter"}],events:["change","maskClick"],snippet:{children:[{name:"View",children:"popup 内容"}]}},{name:"UniPopupMessage",label:"提示信息",categoryId:"ext",props:[{name:"type",title:"消息提示主题",defaultValue:"success",setters:"SelectSetter",options:["success","warn","error","info"]},{name:"message",title:"消息提示文字",defaultValue:"",setters:"StringSetter"},{name:"duration",title:"消息显示时间，超过显示时间组件自动关闭，设置为0 将不会关闭，需手动调用 close 方法关闭",defaultValue:3e3,setters:"NumberSetter"}],slots:["default"],snippet:{props:{type:"success",message:"这是一条成功提示",duration:"2000"}}},{name:"UniPopupDialog",label:"对话框",categoryId:"ext",props:[{name:"type",title:"对话框标题主题",defaultValue:"success",setters:"SelectSetter",options:["success","warn","info","error"]},{name:"mode",title:"对话框模式",defaultValue:"base",setters:"SelectSetter",options:["base","input"]},{name:"title",title:"对话框标题",defaultValue:"",setters:"StringSetter"},{name:"content",title:"对话框内容，base模式下生效",defaultValue:"",setters:"StringSetter"},{name:"confirmText",title:"定义确定按钮文本",defaultValue:"",setters:"StringSetter"},{name:"cancelText",title:"定义取消按钮文本",defaultValue:"",setters:"StringSetter"},{name:"maxlength",title:'限制输入框字数（当mode="input"时生效）',defaultValue:"",setters:"NumberSetter"},{name:"showClose",title:"是否显示取消按钮",defaultValue:"",setters:"BooleanSetter"},{name:"modelValue",title:"输入框值",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"placeholder",title:"输入框提示文字，input模式下生效",defaultValue:"",setters:"StringSetter"},{name:"borderRadius",title:"四周圆角值（左上、右上、右下、左下）",defaultValue:"",setters:"StringSetter"},{name:"before-close",title:"是否拦截按钮事件",defaultValue:!1,setters:"BooleanSetter"}],events:["close","confirm","update:modelValue"],slots:["default"],snippet:{}},{name:"UniPopupShare",label:"分享示例",categoryId:"ext",props:[{name:"title",title:"分享弹窗标题",defaultValue:"",setters:"StringSetter"},{name:"before-close",title:"是否拦截按钮事件",defaultValue:!1,setters:"BooleanSetter"}],events:["select"]}],D={name:"UniRate",label:"评分",categoryId:"ext",props:[{name:"modelValue",title:"当前评分",defaultValue:0,setters:"NumberSetter"},{name:"color",title:"未选中状态的星星颜色",defaultValue:"	#ececec",setters:"ColorSetter"},{name:"activeColor",title:"选中状态的星星颜色",defaultValue:"#ffca3e",setters:"ColorSetter"},{name:"disabledColor",title:"禁用状态的星星颜色",defaultValue:"#c0c0c0",setters:"ColorSetter"},{name:"size",title:"星星的大小",defaultValue:"24",setters:"NumberSetter"},{name:"max",title:"最大评分评分数量，目前一分一颗星",defaultValue:5,setters:"NumberSetter"},{name:"margin",title:"星星的间距，单位 px",defaultValue:0,setters:"NumberSetter"},{name:"isFill",title:"星星的类型，是否为实心类型",defaultValue:!0,setters:"BooleanSetter"},{name:"disabled",title:"是否为禁用状态",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态",defaultValue:!1,setters:"BooleanSetter"},{name:"allowHalf",title:"是否展示半星",defaultValue:!1,setters:"BooleanSetter"},{name:"touchable",title:"是否支持滑动手势",defaultValue:!0,setters:"BooleanSetter"}],events:["change","update:modelValue"],snippet:{props:{modelValue:3}}},L=[{name:"UniRow",label:"布局行",categoryId:"ext",props:[{name:"gutter",title:"栅格间隔",defaultValue:0,setters:"NumberSetter"},{name:"width",title:"nvue 中无百分比 width，使用 span 等属性时，需配置此项rpx值。此项不会影响其他平台展示效果",defaultValue:"750rpx",setters:["NumberSetter","StringSetter"]}],snippet:{props:{gutter:20},children:[{name:"UniCol",props:{span:8,style:{backgroundColor:"#d3dce6"}},children:[{name:"View",children:"布局列"}]},{name:"UniCol",props:{span:8,style:{backgroundColor:"#d3dce6"}},children:[{name:"View",children:"布局列"}]}]}},{name:"UniCol",label:"布局列",categoryId:"ext",props:[{name:"span",title:"栅格占据的列数",defaultValue:24,setters:"NumberSetter"},{name:"offset",title:"栅格左侧间隔格数",setters:"NumberSetter"},{name:"push",title:"栅格向右偏移格数",setters:"NumberSetter"},{name:"pull",title:"栅格向左偏移格数",setters:"NumberSetter"},{name:"xs",title:"屏幕宽度<768px时，要显示的栅格规则",setters:["NumberSetter","ObjectSetter"]},{name:"sm",title:"屏幕宽度≥768px时，要显示的栅格规则",setters:["NumberSetter","ObjectSetter"]},{name:"md",title:"屏幕宽度≥992px时，要显示的栅格规则",setters:["NumberSetter","ObjectSetter"]},{name:"lg",title:"屏幕宽度≥1200px时，要显示的栅格规则",setters:["NumberSetter","ObjectSetter"]},{name:"xl",title:"屏幕宽度≥1920px时，要显示的栅格规则",setters:["NumberSetter","ObjectSetter"]}],snippet:{props:{span:4},children:[{name:"View",props:{style:{backgroundColor:"#d3dce6"}},children:"布局列"}]}}],M={name:"UniSearchBar",label:"搜索栏",categoryId:"ext",props:[{name:"modelValue",title:"搜索栏绑定值",defaultValue:"",setters:["StringSetter","NumberSetter"]},{name:"placeholder",title:"搜索栏Placeholder",defaultValue:"搜索",setters:"StringSetter"},{name:"radius",title:"搜索栏圆角，单位px",defaultValue:10,setters:"NumberSetter"},{name:"clearButton",title:"是否显示清除按钮",defaultValue:"auto",setters:"SelectSetter",options:["always","auto","none"]},{name:"cancelButton",title:"是否显示取消按钮",defaultValue:"auto",setters:"SelectSetter",options:["always","auto","none"]},{name:"cancelText",title:"取消按钮的文字",defaultValue:"取消",setters:"StringSetter"},{name:"bgColor",title:"输入框背景颜色",defaultValue:"#F8F8F8",setters:"ColorSetter"},{name:"textColor",title:"输入时文字颜色",defaultValue:"#F8F8F8",setters:"ColorSetter"},{name:"maxlength",title:"输入最大长度",defaultValue:100,setters:"NumberSetter"},{name:"focus",title:"自动获取焦点",defaultValue:!1,setters:"BooleanSetter"}],events:["confirm","input","cancel","clear","focus","blur","update:modelValue"],slots:["searchIcon","clearIcon"],snippet:{}},E={name:"UniSection",label:"标题栏",categoryId:"ext",props:[{name:"type",title:"装饰类型",defaultValue:"",setters:"SelectSetter",options:["line","circle","square"]},{name:"title",title:"主标题",defaultValue:"",setters:"StringSetter"},{name:"titleFontSize",title:"主标题字体大小",defaultValue:"14px",setters:"StringSetter"},{name:"titleColor",title:"主标题字体颜色",defaultValue:"#333",setters:"ColorSetter"},{name:"subTitle",title:"副标题",defaultValue:"",setters:"StringSetter"},{name:"subTitleFontSize",title:"副标题字体大小",defaultValue:"12px",setters:"StringSetter"},{name:"subTitleColor",title:"副标题字体颜色",defaultValue:"#999",setters:"ColorSetter"},{name:"padding",title:"默认插槽容器的 padding 值，传入类型为 Boolean 时，设置为 10px 或 0",defaultValue:!1,setters:["BooleanSetter","StringSetter"]}],events:["click"],snippet:{props:{title:"基础用法",subTitle:"副标题"}}},G={name:"UniSegmentedControl",label:"分段器",categoryId:"ext",props:[{name:"current",title:"当前选中的tab索引值，从0计数",defaultValue:0,setters:"NumberSetter"},{name:"styleType",title:"分段器样式类型",defaultValue:"button",setters:"SelectSetter",options:["button","text"]},{name:"activeColor",title:"选中的标签背景色与边框颜色",defaultValue:"#007aff",setters:"ColorSetter"},{name:"inActiveColor",title:"未激活时的颜色",defaultValue:"transparent",setters:"StringSetter"},{name:"values",title:"选项数组",defaultValue:"",setters:"ArraySetter"}],events:["clickItem"],snippet:{props:{values:["选项卡1","选项卡2","选项卡3"]}}},q={name:"UniSteps",label:"步骤条",categoryId:"ext",props:[{name:"active",title:"当前步骤",defaultValue:0,setters:"NumberSetter"},{name:"direction",title:"排列方向",defaultValue:"row",setters:"SelectSetter",options:["row","column"]},{name:"active-color",title:"选中状态的颜色",defaultValue:"#1aad19",setters:"ColorSetter"},{name:"options",title:"数据源",setters:"ArraySetter"}],events:[],snippet:{props:{active:1,options:[{title:"事件一"},{title:"事件二"},{title:"事件三"},{title:"事件四"}]}}},H=[{name:"UniTable",label:"表格",childIncludes:["UniTr"],categoryId:"ext",props:[{name:"border",title:"是否带有纵向边框",defaultValue:!1,setters:"BooleanSetter"},{name:"stripe",title:"是否显示斑马线样式",defaultValue:!0,setters:"BooleanSetter"},{name:"type",title:'值为type="selection" 时开启多选',defaultValue:"",setters:"StringSetter"},{name:"emptyText",title:"空数据时显示的文本内容",defaultValue:"没有更多数据",setters:"StringSetter"},{name:"loading",title:"显示加载中",defaultValue:!1,setters:"BooleanSetter"}],events:["selection-change"],snippet:{props:{border:!0,stripe:!0,emptyText:"暂无更多数据"},children:[{name:"UniTr",children:[{name:"UniTh",props:{align:"center"},children:"日期"},{name:"UniTh",props:{align:"center"},children:"姓名"},{name:"UniTh",props:{align:"center"},children:"地址"}]},{name:"UniTr",children:[{name:"UniTd",children:"2020-10-20"},{name:"UniTd",children:"Jason"},{name:"UniTd",children:"北京市海淀区"}]}]}},{name:"UniTr",label:"表格行",childIncludes:["UniTh","UniTd"],categoryId:"ext",props:[],snippet:{children:"tr"}},{name:"UniTh",label:"表格头",categoryId:"ext",props:[{name:"width",title:"单元格宽度",defaultValue:"",setters:"StringSetter"},{name:"align",title:"表头对齐方式",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"filter-type",title:"筛选类型",setters:"SelectSetter",options:["search","select","range","date"]},{name:"filter-data",title:"筛选数据",setters:"ArraySetter"},{name:"sortable",title:"是否启用排序",defaultValue:!1,setters:"BooleanSetter"}],events:["sort-change","filter-change"],snippet:{children:"th"}},{name:"UniTd",label:"单元格",categoryId:"ext",props:[{name:"align",title:"表头对齐方式",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]}],snippet:{children:"td"}}],R={name:"UniTag",label:"标签",categoryId:"ext",props:[{name:"text",title:"标签内容",defaultValue:"",setters:"StringSetter"},{name:"size",title:"大小尺寸",defaultValue:"normal",setters:"SelectSetter",options:["normal","small"]},{name:"type",title:"颜色类型",defaultValue:"default",setters:"SelectSetter",options:["default","primary","success","warning","error","royal"]},{name:"disabled",title:"是否为禁用状态",defaultValue:!1,setters:"BooleanSetter"},{name:"inverted",title:"是否无需背景颜色（空心标签）",defaultValue:!1,setters:"BooleanSetter"},{name:"circle",title:"是否为圆角",defaultValue:!1,setters:"BooleanSetter"}],events:["click"],snippet:{props:{text:"标签",type:"royal"}}},W={name:"UniTitle",label:"章节标题",categoryId:"ext",props:[{name:"type",title:"标题类型",defaultValue:"",setters:"SelectSetter",options:["h1","h2","h3","h4","h5"]},{name:"title",title:"章节标题内容",defaultValue:"",setters:"StringSetter"},{name:"align",title:"对齐方式",setters:"SelectSetter",options:["left","center","right"]},{name:"color",title:"字体颜色",defaultValue:"",setters:"ColorSetter"},{name:"stat",title:"是否开启统计功能呢",defaultValue:!1,setters:"BooleanSetter"}],snippet:{props:{type:"h1",title:"h1 一级标题",color:"#027fff"}}},J={name:"UniTooltip",label:"文字提示",categoryId:"ext",props:[{name:"content",title:"弹出层显示的内容",defaultValue:"",setters:"StringSetter"},{name:"placement",title:"Tooltip 的出现位置",defaultValue:"left",setters:"SelectSetter",options:["left","right","top","bottom"]}],slots:["default","content"],snippet:{props:{content:"示例文字",placement:"top"},children:[{name:"View",children:"上"}]}},K={name:"UniTransition",label:"过渡动画",categoryId:"ext",props:[{name:"show",title:"控制组件显示或隐藏",defaultValue:!1,setters:"BooleanSetter"},{name:"mode-class",title:"内置过渡动画类型",defaultValue:"",setters:["SelectSetter","ArraySetter","StringSetter"],options:["fade","slide-top","slide-right","slide-bottom","slide-left","zoom-in","zoom-out"]},{name:"custom-class",title:"自定义类名",defaultValue:"",setters:"StringSetter"},{name:"duration",title:"过渡动画持续时间",defaultValue:30,setters:"NumberSetter"},{name:"styles",title:"组件样式",defaultValue:"",setters:"ObjectSetter"}],events:["click","change"],snippet:{props:{"mode-class":"fade",styles:{width:"100px",height:"100px",backgroundColor:"red"},show:"show"}}},a="@dcloudio/uni-ui",Q=[l,o,u,d,m,c,p,S,f,V,g,b,h,x,y,B,v,I,C,U,w,N,T,k,j,$,F,A,O,z,P,D,L,M,E,G,q,H,R,W,J,K].flat();return{name:a,version:t,label:"UniUI",library:"UniUIMaterial",order:5,categories:[{id:"ext",category:"扩展组件"}],components:e(Q,a)}});
