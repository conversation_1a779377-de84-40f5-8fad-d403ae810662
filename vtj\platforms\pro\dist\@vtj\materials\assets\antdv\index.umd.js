(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.AntdvMaterial=e())})(this,function(){"use strict";var Te=Object.defineProperty,Oe=Object.defineProperties;var Ne=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var Pe=Object.prototype.hasOwnProperty,Fe=Object.prototype.propertyIsEnumerable;var p=(t,e,l)=>e in t?Te(t,e,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[e]=l,a=(t,e)=>{for(var l in e||(e={}))Pe.call(e,l)&&p(t,l,e[l]);if(c)for(var l of c(e))Fe.call(e,l)&&p(t,l,e[l]);return t},m=(t,e)=>Oe(t,Ne(e));/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const t="0.12.70";function e(r,Ce){return r.map(Ie=>m(a({},Ie),{package:Ce}))}function l(){return[{value:"guide",label:"Guide",children:[{value:"disciplines",label:"Disciplines",children:[{value:"consistency",label:"Consistency"},{value:"feedback",label:"Feedback"},{value:"efficiency",label:"Efficiency"},{value:"controllability",label:"Controllability"}]},{value:"navigation",label:"Navigation",children:[{value:"side nav",label:"Side Navigation"},{value:"top nav",label:"Top Navigation"}]}]},{value:"component",label:"Component",children:[{value:"basic",label:"Basic",children:[{value:"layout",label:"Layout"},{value:"color",label:"Color"},{value:"typography",label:"Typography"},{value:"icon",label:"Icon"},{value:"button",label:"Button"}]},{value:"form",label:"Form",children:[{value:"radio",label:"Radio"},{value:"checkbox",label:"Checkbox"},{value:"input",label:"Input"},{value:"input-number",label:"InputNumber"},{value:"select",label:"Select"},{value:"cascader",label:"Cascader"},{value:"switch",label:"Switch"},{value:"slider",label:"Slider"},{value:"time-picker",label:"TimePicker"},{value:"date-picker",label:"DatePicker"},{value:"datetime-picker",label:"DateTimePicker"},{value:"upload",label:"Upload"},{value:"rate",label:"Rate"},{value:"form",label:"Form"}]},{value:"data",label:"Data",children:[{value:"table",label:"Table"},{value:"tag",label:"Tag"},{value:"progress",label:"Progress"},{value:"tree",label:"Tree"},{value:"pagination",label:"Pagination"},{value:"badge",label:"Badge"}]},{value:"notice",label:"Notice",children:[{value:"alert",label:"Alert"},{value:"loading",label:"Loading"},{value:"message",label:"Message"},{value:"message-box",label:"MessageBox"},{value:"notification",label:"Notification"}]},{value:"navigation",label:"Navigation",children:[{value:"menu",label:"Menu"},{value:"tabs",label:"Tabs"},{value:"breadcrumb",label:"Breadcrumb"},{value:"dropdown",label:"Dropdown"},{value:"steps",label:"Steps"}]},{value:"others",label:"Others",children:[{value:"dialog",label:"Dialog"},{value:"tooltip",label:"Tooltip"},{value:"popover",label:"Popover"},{value:"card",label:"Card"},{value:"carousel",label:"Carousel"},{value:"collapse",label:"Collapse"}]}]},{value:"resource",label:"Resource",children:[{value:"axure",label:"Axure Components"},{value:"sketch",label:"Sketch Templates"},{value:"docs",label:"Design Documentation"}]}]}const n={textAlign:"center",color:"#fff",height:64,paddingInline:50,lineHeight:"64px",backgroundColor:"#7dbcea"},s={textAlign:"center",minHeight:120,lineHeight:"120px",color:"#fff",backgroundColor:"#108ee9"},o={textAlign:"center",lineHeight:"120px",color:"#fff",backgroundColor:"#3ba0e9"},i={textAlign:"center",color:"#fff",backgroundColor:"#7dbcea"},b=[{name:"AButton",alias:"Button",label:"按钮",categoryId:"base",doc:"https://www.antdv.com/components/button-cn",props:[{name:"block",label:"block",title:"将按钮宽度调整为其父宽度的选项",setters:"BooleanSetter",defaultValue:!1},{name:"danger",label:"danger",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",setters:"BooleanSetter",defaultValue:!1},{name:"ghost",label:"ghost",setters:"BooleanSetter",defaultValue:!1},{name:"href",label:"href",setters:"InputSetter"},{name:"loading",label:"loading",setters:"BooleanSetter",defaultValue:!1},{name:"shape",label:"shape",setters:"SelectSetter",options:["default","circle","round"],defaultValue:"default"},{name:"size",label:"size",setters:"SelectSetter",options:["large","middle","small"],defaultValue:"middle"},{name:"target",label:"target",setters:"InputSetter",title:"相当于 a 链接的 target 属性，href 存在时生效"},{name:"type",label:"type",setters:"SelectSetter",options:["primary","ghost","dashed","link","text","default"],defaultValue:"default"}],events:["click"],slots:["default","icon"],snippet:{children:"Button"}},{name:"AButtonGroup",alias:"Group",parent:"Button",label:"按钮组",categoryId:"base",doc:"https://www.antdv.com/components/button-cn",props:[{name:"size",label:"size",setters:"SelectSetter",options:["large","middle","small"],defaultValue:"middle"}],snippet:{children:[{name:"AButton",children:"Button1"},{name:"AButton",children:"Button2"},{name:"AButton",children:"Button3"}]}}],S=[{name:"ATypography",alias:"Typography",label:"排版",childIncludes:["ATypographyText","ATypographyTitle","ATypographyParagraph"],categoryId:"base",doc:"https://www.antdv.com/components/typography-cn",slots:["copyableIcon","copyableTooltip","editableIcon","editableTooltip","ellipsisSymbol","ellipsisTooltip","enterEnterIcon"],snippet:{children:[{name:"ATypographyTitle",children:"Introduction"},{name:"ATypographyParagraph",children:"In the process of internal desktop applications development, many different design specs and implementations would be involved, which might cause designers and developers difficulties and duplication and reduce the efficiency of development"},{name:"ATypographyParagraph",children:[{name:"ATypographyText",children:"uniform the user interface specs for internal background projects, lower the unnecessary cost of design differences and implementation and liberate the resources of design and front-end development."}]}]}},{name:"ATypographyText",alias:"Text",parent:"Typography",label:"文本内容",categoryId:"base",doc:"https://www.antdv.com/components/button-cn",props:[{name:"code",label:"code",title:"添加代码样式",setters:"BooleanSetter",defaultValue:!1},{name:"content",label:"content",title:"当使用 ellipsis 或 editable 时，使用 content 代替 children",setters:"StringSetter"},{name:"copyable",label:"copyable",title:"是否可拷贝，为对象时可进行各种自定义",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"delete",label:"delete",title:"添加删除线样式",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"禁用文本",setters:"BooleanSetter",defaultValue:!1},{name:"editable",label:"editable",title:"是否可编辑，为对象时可对编辑进行控制",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"ellipsis",label:"ellipsis",title:"自动溢出省略，为对象时可设置省略行数、是否可展开、添加后缀等",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"keyboard",label:"keyboard",title:"添加键盘样式",setters:"BooleanSetter",defaultValue:!1},{name:"mark",label:"mark",title:"添加标记样式",setters:"BooleanSetter",defaultValue:!1},{name:"strong",label:"strong",title:"是否加粗",setters:"BooleanSetter",defaultValue:!1},{name:"type",label:"type",title:"文本类型",setters:"SelectSetter",options:["secondary","success","warning","danger"]},{name:"underline",label:"underline",title:"添加下划线样式",setters:"BooleanSetter",defaultValue:!1}],snippet:{children:"uniform the user interface specs for internal background projects, lower the unnecessary cost of design differences and implementation and liberate the resources of design and front-end development."},events:["update:content"]},{name:"ATypographyTitle",alias:"Title",parent:"Typography",label:"文本标题",categoryId:"base",doc:"https://www.antdv.com/components/button-cn",props:[{name:"code",label:"code",title:"添加代码样式",setters:"BooleanSetter",defaultValue:!1},{name:"content",label:"content",title:"当使用 ellipsis 或 editable 时，使用 content 代替 children",setters:"StringSetter"},{name:"copyable",label:"copyable",title:"是否可拷贝，为对象时可进行各种自定义",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"delete",label:"delete",title:"添加删除线样式",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"禁用文本",setters:"BooleanSetter",defaultValue:!1},{name:"editable",label:"editable",title:"是否可编辑，为对象时可对编辑进行控制",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"ellipsis",label:"ellipsis",title:"自动溢出省略，为对象时可设置省略行数、是否可展开、添加后缀等",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"level",label:"level",title:"重要程度，相当于 h1、h2、h3、h4、h5",setters:["SelectSetter","NumberSetter"],options:[{label:"H1",value:1},{label:"H2",value:2},{label:"H3",value:3},{label:"H4",value:4},{label:"H5",value:5}],defaultValue:1},{name:"mark",label:"mark",title:"添加标记样式",setters:"BooleanSetter",defaultValue:!1},{name:"type",label:"type",title:"文本类型",setters:"SelectSetter",options:["secondary","success","warning","danger"]},{name:"underline",label:"underline",title:"添加下划线样式",setters:"BooleanSetter",defaultValue:!1}],snippet:{children:"Introduction"},events:["update:content"]},{name:"ATypographyParagraph",alias:"Paragraph",parent:"Typography",label:"文本段落",categoryId:"base",doc:"https://www.antdv.com/components/button-cn",props:[{name:"code",label:"code",title:"添加代码样式",setters:"BooleanSetter",defaultValue:!1},{name:"content",label:"content",title:"当使用 ellipsis 或 editable 时，使用 content 代替 children",setters:"StringSetter"},{name:"copyable",label:"copyable",title:"是否可拷贝，为对象时可进行各种自定义",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"delete",label:"delete",title:"添加删除线样式",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"禁用文本",setters:"BooleanSetter",defaultValue:!1},{name:"editable",label:"editable",title:"是否可编辑，为对象时可对编辑进行控制",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"ellipsis",label:"ellipsis",title:"自动溢出省略，为对象时可设置省略行数、是否可展开、添加后缀等",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"mark",label:"mark",title:"添加标记样式",setters:"BooleanSetter",defaultValue:!1},{name:"strong",label:"strong",title:"是否加粗",setters:"BooleanSetter",defaultValue:!1},{name:"type",label:"type",title:"文本类型",setters:"SelectSetter",options:["secondary","success","warning","danger"]},{name:"underline",label:"underline",title:"添加下划线样式",setters:"BooleanSetter",defaultValue:!1}],snippet:{children:[{name:"ATypographyParagraph",children:"In the process of internal desktop applications development, many different design specs and implementations would be involved, which might cause designers and developers difficulties and duplication and reduce the efficiency of development"},{name:"ATypographyParagraph",children:[{name:"ATypographyText",children:"uniform the user interface specs for internal background projects, lower the unnecessary cost of design differences and implementation and liberate the resources of design and front-end development."}]}]},events:["update:content"],slots:["copyableIcon","copyableTooltip","editableIcon","editableTooltip","ellipsisSymbol","ellipsisTooltip","enterEnterIcon"]},{name:"ATypographyLink",alias:"Paragraph",parent:"Typography",label:"超链接",categoryId:"base",doc:"https://www.antdv.com/components/button-cn",props:[{name:"href",label:"超链接",setters:"string",defaultValue:""},{name:"target",label:"目标",setters:"string",defaultValue:""}]}],f=[{name:"ADivider",alias:"Divider",label:"分割线",categoryId:"layout",doc:"https://www.antdv.com/components/divider-cn",props:[{name:"dashed",label:"dashed",title:"是否虚线",setters:"BooleanSetter",defaultValue:!1},{name:"orientation",label:"orientation",title:"分割线标题的位置",setters:"SelectSetter",options:["left","right","center"],defaultValue:"center"},{name:"orientationMargin",label:"orientationMargin",title:"标题和最近 left/right 边框之间的距离，去除了分割线，同时 orientation 必须为 left 或 right",setters:["StringSetter","NumberSetter"]},{name:"plain",label:"plain",title:"文字是否显示为普通正文样式",setters:"BooleanSetter",defaultValue:!1},{name:"type",label:"type",title:"水平还是垂直类型",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"horizontal"}],snippet:{props:{type:"horizontal",dashed:!0,style:{height:"60px",borderColor:"#7cb305"}},children:"Text"}}],g=[{name:"AFlex",alias:"Flex",label:"弹性布局",categoryId:"layout",doc:"https://www.antdv.com/components/flex-cn",props:[{name:"vertical",label:"vertical",title:"flex 主轴的方向是否垂直，使用 flex-direction: column",setters:"BooleanSetter",defaultValue:!1},{name:"wrap",label:"wrap",title:"设置元素单行显示还是多行显示",setters:"SelectSetter",options:["nowrap ","wrap","wrap-reverse","inherit","initial","revert","unset"],defaultValue:"nowrap"},{name:"justify",label:"justify",title:"设置元素在主轴方向上的对齐方式",setters:"SelectSetter",options:["start ","center","end","flex-start","flex-end","left","right","baseline","first baseline","last baseline","space-between","space-around","space-evenly","stretch","safe center","unsafe center","inherit","initial","unset"],defaultValue:"normal"},{name:"align",label:"align",title:"设置元素在交叉轴方向上的对齐方式",setters:"SelectSetter",options:["normal","stretch","center","start ","end","flex-start","flex-end","self-start","self-end","baseline","first baseline","last baseline","safe center","unsafe center","inherit","initial","revert","revert-layer","unset"],defaultValue:"normal"},{name:"flex",label:"flex",title:"flex CSS 简写属性",setters:["SelectSetter","NumberSetter","StringSetter"],options:["auto","initial","none","inherit","initial","revert","revert-layer","unset"],defaultValue:"normal"},{name:"gap",label:"gap",title:"设置网格之间的间隙",setters:["SelectSetter","NumberSetter","StringSetter"],options:["small","middle","large"],defaultValue:""},{name:"component",label:"component",title:"自定义元素类型",setters:"StringSetter",defaultValue:"div"}],slots:["default","component"],snippet:{props:{vertical:!1,gap:"middle",style:{width:"100%"}},children:[{name:"component",props:{is:"div",style:{width:"25%",height:"54px",color:"#fff",background:"#1677ff"}},children:"flex item111"},{name:"component",props:{is:"div",style:{width:"25%",height:"54px",color:"#fff",background:"#1677ffbf"}},children:"flex item222"},{name:"component",props:{is:"div",style:{width:"25%",height:"54px",color:"#fff",background:"#1677ff"}},children:"flex item333"},{name:"component",props:{is:"div",style:{width:"25%",height:"54px",color:"#fff",background:"#1677ffbf"}},children:"flex item444"}]}}],h=[{name:"ARow",alias:"Row",label:"布局行",categoryId:"layout",doc:"https://www.antdv.com/components/grid-cn",props:[{name:"align",label:"align",title:"垂直对齐方式",setters:"SelectSetter",options:["top","middle","bottom","stretch"],defaultValue:"top"},{name:"gutter",label:"gutter",title:"栅格间隔，可以写成像素值或支持响应式的对象写法来设置水平间隔 { xs: 8, sm: 16, md: 24}。或者使用数组形式同时设置 [水平间距, 垂直间距]（1.5.0 后支持）。",setters:["NumberSetter","ObjectSetter","ArraySetter"],defaultValue:0},{name:"justify",label:"justify",title:"水平排列方式",setters:"SelectSetter",options:["start","end","center","space-around","space-between","space-evenly"],defaultValue:"start"},{name:"wrap",label:"wrap",title:"是否自动换行",setters:"BooleanSetter",defaultValue:"false"}],snippet:{props:{gutter:10},children:[{name:"ACol",props:{span:6},children:[{name:"component",props:{is:"div",style:{height:"50px",background:"#ecf5ff"}}}],directives:[{name:"vFor",value:{type:"JSExpression",value:"4"}}]}]}},{name:"ACol",alias:"Col",label:"布局列",categoryId:"layout",doc:"https://www.antdv.com/components/grid-cn",props:[{name:"flex",label:"flex",title:"flex 布局填充",setters:["StringSetter","NumberSetter"]},{name:"offset",label:"offset",title:"栅格左侧的间隔格数，间隔内不可以有栅格",setters:"NumberSetter",defaultValue:0},{name:"order",label:"order",title:"栅格顺序，flex 布局模式下有效",setters:"NumberSetter",defaultValue:0},{name:"pull",label:"pull",title:"栅格向左移动格数",setters:"NumberSetter",defaultValue:0},{name:"push",label:"push",title:"栅格向右移动格数",setters:"NumberSetter",defaultValue:0},{name:"span",label:"span",title:"栅格占位格数，为 0 时相当于 display: none",setters:"NumberSetter",defaultValue:0},{name:"xs",label:"xs",title:"<576px 响应式栅格，可为栅格数或一个包含其他属性的对象",setters:["NumberSetter","ObjectSetter"]},{name:"sm",label:"sm",title:"≥576px 响应式栅格，可为栅格数或一个包含其他属性的对象",setters:["NumberSetter","ObjectSetter"]},{name:"md",label:"md",title:"≥768px 响应式栅格，可为栅格数或一个包含其他属性的对象",setters:["NumberSetter","ObjectSetter"]},{name:"lg",label:"lg",title:"≥992px 响应式栅格，可为栅格数或一个包含其他属性的对象",setters:["NumberSetter","ObjectSetter"]},{name:"xl",label:"xl",title:"≥1200px 响应式栅格，可为栅格数或一个包含其他属性的对象",setters:["NumberSetter","ObjectSetter"]},{name:"xxl",label:"xxl",title:"≥1600px 响应式栅格，可为栅格数或一个包含其他属性的对象",setters:["NumberSetter","ObjectSetter"]}],snippet:{props:{span:6},children:[{name:"component",props:{is:"div",style:{height:"50px",background:"#ecf5ff"}},children:"ACol"}]}}],y=[{name:"ALayout",alias:"Layout",label:"布局容器",categoryId:"layout",doc:"https://www.antdv.com/components/layout-cn#components-layout-demo-fixed",props:[{name:"class",label:"class",title:"容器 class",setters:"StringSetter"},{name:"hasSider",label:"hasSider",title:"表示子元素里有 Sider，一般不用指定。可用于服务端渲染时避免样式闪动",setters:"BooleanSetter",defaultValue:!1},{name:"style",label:"style",title:"指定样式",setters:"ObjectSetter"}],snippet:{children:[{name:"ALayoutSider",children:"ALayoutSider",props:{style:a({},o)}},{name:"ALayout",children:[{name:"ALayoutHeader",children:"header",props:{style:a({},n)}},{name:"ALayoutContent",children:"content",props:{style:a({},s)}},{name:"ALayoutFooter",children:"footer",props:{style:a({},i)}}]}]}},{name:"ALayoutHeader",alias:"Header",parent:"Layout",label:"顶栏容器",categoryId:"layout",doc:"https://www.antdv.com/components/layout-cn#components-layout-demo-fixed",snippet:{children:"header",props:{style:a({},n)}}},{name:"ALayoutContent",alias:"Content",parent:"Layout",label:"主要内容容器",categoryId:"layout",doc:"https://www.antdv.com/components/layout-cn#components-layout-demo-fixed",snippet:{children:"content",props:{style:a({},s)}}},{name:"ALayoutFooter",alias:"Footer",parent:"Layout",label:"底栏容器",categoryId:"layout",doc:"https://www.antdv.com/components/layout-cn#components-layout-demo-fixed",snippet:{children:"footer",props:{style:a({},i)}}},{name:"ALayoutSider",alias:"Sider",parent:"Layout",label:"侧边栏容器",categoryId:"layout",doc:"https://www.antdv.com/components/layout-cn#components-layout-demo-fixed",props:[{name:"breakpoint",label:"breakpoint",title:"触发响应式布局的断点",setters:"StringSetter"},{name:"class",label:"class",title:"容器 class",setters:"StringSetter"},{name:"collapsed",label:"collapsed",title:"当前收起状态",setters:"BooleanSetter"},{name:"collapsedWidth",label:"collapsedWidth",title:"收缩宽度，设置为 0 会出现特殊 trigger",setters:"NumberSetter",defaultValue:80},{name:"collapsible",label:"collapsible",title:"是否可收起",setters:"BooleanSetter",defaultValue:!1},{name:"defaultCollapsed",label:"defaultCollapsed",title:"是否默认收起",setters:"BooleanSetter",defaultValue:!1},{name:"reverseArrow",label:"reverseArrow",title:"翻转折叠提示箭头的方向，当 Sider 在右边时可以使用",setters:"BooleanSetter",defaultValue:!1},{name:"style",label:"style",title:"指定样式",setters:["ObjectSetter","StringSetter"]},{name:"theme",label:"theme",title:"主题颜色",setters:"SelectSetter",options:["light","dark"],defaultValue:"dark"},{name:"trigger",label:"trigger",title:"自定义 trigger，设置为 null 时隐藏 trigger",setters:"StringSetter"},{name:"width",label:"width",title:"宽度",setters:["NumberSetter","StringSetter"],defaultValue:200},{name:"zeroWidthTriggerStyle",label:"zeroWidthTriggerStyle",title:"指定当 collapsedWidth 为 0 时出现的特殊 trigger 的样式",setters:"ObjectSetter"}],events:["breakpoint","collapse","update:collapsed"],slots:["default","trigger"],snippet:{children:"sider",props:{style:a({},o)}}}],v=[{name:"ASpace",alias:"Space",label:"间距",categoryId:"layout",doc:"https://www.antdv.com/components/space-cn",props:[{name:"align",label:"align",title:"垂直对齐方式",setters:"SelectSetter",options:["top","middle","bottom","stretch"],defaultValue:"top"},{name:"direction",label:"direction",title:"间距方向",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"horizontal"},{name:"size",label:"size",title:"间距大小",setters:["SelectSetter","NumberSetter"],options:["small","middle","large"],defaultValue:"small"},{name:"split",label:"split",title:"设置拆分",setters:["StringSetter","ExpressionSetter"],defaultValue:"-"},{name:"wrap",label:"wrap",title:"是否自动换行，仅在 horizontal 时有效",setters:"BooleanSetter",defaultValue:!1}],slots:["default","split"],snippet:{props:{style:{width:"100%"}},children:[{name:"ACard",children:[{name:"component",props:{is:"p"},children:"List item"}],directives:[{name:"vFor",value:{type:"JSExpression",value:"2"}}]}]}},{name:"ASpaceCompact",alias:"Compact",parent:"Space",label:"紧凑间距",categoryId:"layout",doc:"https://www.antdv.com/components/space-cn",props:[{name:"block",label:"block",title:"将宽度调整为父元素宽度的选项",setters:"BooleanSetter",defaultValue:!1},{name:"direction",label:"direction",title:"指定排列方向",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"horizontal"},{name:"size",label:"size",title:"子组件大小",setters:"SelectSetter",options:["small","middle","large"],defaultValue:"small"}],slots:["default"],snippet:{props:{style:{width:"100%"}},children:[{name:"ACard",children:[{name:"component",props:{is:"p"},children:"List item"}],directives:[{name:"vFor",value:{type:"JSExpression",value:"4"}}]}],directives:[{name:"vFor",value:{type:"JSExpression",value:"4"}}]}}],w=[{name:"AAnchor",alias:"Anchor",label:"锚点",categoryId:"nav",doc:"https://www.antdv.com/components/anchor-cn",props:[{name:"affix",label:"affix",title:"固定模式",setters:"BooleanSetter",defaultValue:!0},{name:"bounds",label:"bounds",title:"锚点区域边界",setters:"NumberSetter",defaultValue:5},{name:"getContainer",label:"getContainer",title:"指定滚动的容器",setters:"FunctionSetter",defaultValue:{type:"Function",value:"() => window"}},{name:"getCurrentAnchor",label:"getCurrentAnchor",title:"自定义高亮的锚点",setters:"FunctionSetter"},{name:"offsetBottom",label:"offsetBottom",title:"距离窗口底部达到指定偏移量后触发",setters:"NumberSetter"},{name:"offsetTop",label:"offsetTop",title:"距离窗口顶部达到指定偏移量后触发",setters:"NumberSetter"},{name:"showInkInFixed",label:"showInkInFixed",title:':affix="false" 时是否显示小方块',setters:"BooleanSetter",defaultValue:!1},{name:"targetOffset",label:"targetOffset",title:"锚点滚动偏移量，默认与 offsetTop 相同",setters:"NumberSetter"},{name:"wrapperClass",label:"wrapperClass",title:"容器的类名",setters:"StringSetter"},{name:"wrapperStyle",label:"wrapperStyle",title:"容器样式",setters:"ObjectSetter"},{name:"items",label:"items",title:"数据化配置选项内容，支持通过 children 嵌套",setters:"ArraySetter"},{name:"direction",label:"direction",title:"设置导航方向",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"vertical"}],slots:["default","customTitle"],snippet:{props:{direction:"horizontal",items:[{key:"horizontally-part-1",href:"#horizontally-part-1",title:"Part 1"},{key:"horizontally-part-2",href:"#horizontally-part-2",title:"Part 2"},{key:"horizontally-part-3",href:"#horizontally-part-3",title:"Part 3"},{key:"horizontally-part-4",href:"#horizontally-part-4",title:"Part 4"},{key:"horizontally-part-5",href:"#horizontally-part-5",title:"Part 5"},{key:"horizontally-part-6",href:"#horizontally-part-6",title:"Part 6"}]}}}],V=[{name:"ABreadcrumb",alias:"Breadcrumb",label:"面包屑",categoryId:"nav",doc:"https://www.antdv.com/components/breadcrumb-cn",props:[{name:"itemRender",label:"itemRender",title:'自定义链接函数，和 vue-router 配置使用， 也可使用 #itemRender="props"'},{name:"params",label:"params",title:"路由的参数",setters:"ObjectSetter"},{name:"routes",label:"routes",title:"router 的路由栈信息",setters:"ArraySetter"},{name:"separator",label:"separator",title:"分隔符自定义",setters:"StringSetter",defaultValue:"/"}],slots:["default","separator"],snippet:{children:[{name:"BreadcrumbItem",children:"Home"},{name:"ABreadcrumbSeparator"},{name:"BreadcrumbItem",children:"Application Center"},{name:"ABreadcrumbSeparator"},{name:"BreadcrumbItem",children:"Application List"},{name:"ABreadcrumbSeparator"},{name:"BreadcrumbItem",props:{style:{fontWeight:"700",color:"#000"}},children:"An Application"}]}},{name:"ABreadcrumbItem",alias:"Item",parent:"Breadcrumb",label:"面包屑项",categoryId:"nav",doc:"https://www.antdv.com/components/breadcrumb-cn",props:[{name:"href",label:"href",title:"链接的目的地",setters:"StringSetter"},{name:"overlay",label:"overlay",title:"下拉菜单的内容",setters:["ObjectSetter","FunctionSetter"]}],events:["click"],snippet:{children:"面包屑项"}},{name:"ABreadcrumbSeparator",alias:"Separator",parent:"Breadcrumb",label:"面包屑分隔符",categoryId:"nav",doc:"https://www.antdv.com/components/breadcrumb-cn"}],B=[{name:"ADropdown",alias:"Dropdown",label:"下拉菜单",categoryId:"nav",doc:"https://www.antdv.com/components/button-cn",props:[{name:"align",label:"align",title:"该值将合并到 placement 的配置中",setters:"ObjectSetter"},{name:"arrow",label:"arrow",title:"下拉框箭头是否显示",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"destroyPopupOnHide",label:"destroyPopupOnHide",title:"关闭后是否销毁 Dropdown",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"菜单是否禁用",defaultValue:!1},{name:"getPopupContainer",label:"getPopupContainer",title:"菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位",setters:"FunctionSetter",defaultValue:{type:"Function",value:"() => document.body"}},{name:"overlayClassName",label:"overlayClassName",title:"下拉根元素的类名称",setters:"StringSetter"},{name:"overlayStyle",label:"overlayStyle",title:"下拉根元素的样式",setters:"ObjectSetter"},{name:"placement",label:"placement",title:"菜单弹出位置",setters:"SelectSetter",options:["bottomLeft","bottom","bottomRight","topLeft","top","topRight"],defaultValue:"bottomLeft	"},{name:"trigger",label:"trigger",title:"触发下拉的行为, 移动端不支持 hover",setters:"ArraySetter",defaultValue:["hover"]},{name:"open",label:"open",title:"菜单是否显示",setters:"BooleanSetter"}],events:["openChange","update:open"],slots:["default","overlay"],snippet:{name:"ADropdownButton",children:[{name:"AButton",children:[{name:"component",props:{is:"span"},children:"下拉菜单"},{name:"component",props:{is:"span"},children:" V"}]},{name:"AMenu",slot:"overlay",children:[{name:"AMenuItem",children:"Action 1"},{name:"AMenuItem",children:"Action 2"},{name:"AMenuItem",children:"Action 3"}]}]}},{name:"ADropdownButton",alias:"Button",parent:"Dropdown",label:"下拉菜单按钮",categoryId:"nav",doc:"https://www.antdv.com/components/button-cn",props:[{name:"disabled",label:"disabled",title:"菜单是否禁用",setters:"BooleanSetter"},{name:"loading",label:"loading",title:"设置按钮载入状态",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"placement",label:"placement",title:"菜单弹出位置",setters:"SelectSetter",options:["bottomLeft","bottom","bottomRight","topLeft","top","topRight"],defaultValue:"bottomLeft"},{name:"size",label:"size",title:"按钮大小，和 Button 一致",setters:"StringSetter",defaultValue:"default"},{name:"trigger",label:"trigger",title:"触发下拉的行为",setters:"ArraySetter",defaultValue:["hover"]},{name:"type",label:"type",title:"按钮类型，和 Button 一致",setters:"StringSetter",defaultValue:"default"},{name:"open",label:"open",title:"菜单是否显示",setters:["BooleanSetter"]}],events:["click","openChange","update:open"],slots:["default","icon","overlay"],snippet:{name:"button",children:[{name:"a",children:"Dropdown"},{name:"AMenu",slot:"overlay",props:{mode:"vertical"},children:[{name:"AMenuItem",children:"1st menu item"},{name:"AMenuItem",children:"2nd menu item"},{name:"AMenuItem",children:"3rd menu item"}]}]}}],A=[{name:"AMenu",alias:"Menu",label:"导航菜单",categoryId:"nav",doc:"https://www.antdv.com/components/menu-cn",props:[{name:"forceSubMenuRender",label:"forceSubMenuRender",title:"在子菜单展示之前就渲染进 DOM",setters:"BooleanSetter",defaultValue:!1},{name:"inlineCollapsed",label:"inlineCollapsed",title:"inline 时菜单是否收起状态",setters:"BooleanSetter"},{name:"inlineIndent",label:"inlineIndent",title:"inline 模式的菜单缩进宽度",setters:"NumberSetter",defaultValue:24},{name:"items",label:"items",title:"菜单内容",setters:"ArraySetter"},{name:"mode",label:"mode",title:"菜单类型，现在支持垂直、水平、和内嵌模式三种",setters:"SelectSetter",options:["vertical","horizontal","inline"],defaultValue:"vertical"},{name:"multiple",label:"multiple",title:"是否允许多选",setters:"BooleanSetter",defaultValue:!1},{name:"openKeys",label:"openKeys",title:"当前展开的 SubMenu 菜单项 key 数组",setters:"ArraySetter"},{name:"overflowedIndicator",label:"overflowedIndicator",title:"用于自定义 Menu 水平空间不足时的省略收缩的图标",setters:"IconSetter"},{name:"selectable",label:"selectable",title:"是否允许选中",setters:"BooleanSetter",defaultValue:!0},{name:"selectedKeys",label:"selectedKeys",title:"当前选中的菜单项 key 数组",setters:"ArraySetter"},{name:"subMenuCloseDelay",label:"subMenuCloseDelay",title:"用户鼠标离开子菜单后关闭延时，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"subMenuOpenDelay",label:"subMenuOpenDelay",title:"用户鼠标进入子菜单后开启延时，单位：秒",setters:"NumberSetter",defaultValue:0},{name:"theme",label:"theme",title:"主题颜色",setters:"SelectSetter",options:["light","dark"],defaultValue:"light"},{name:"triggerSubMenuAction",label:"triggerSubMenuAction",title:"修改 Menu 子菜单的触发方式",setters:"SelectSetter",options:["click","hover"],defaultValue:"hover"}],events:["click","deselect","openChange","select","update:openKeys","update:selectedKeys"],slots:["default","overflowedIndicator"],snippet:{props:{selectedKeys:["mail"],mode:"horizontal",items:[{key:"mail",label:"Navigation One",title:"Navigation One"},{key:"app",label:"Navigation Two",title:"Navigation Two"},{key:"sub1",label:"Navigation Three - Submenu",title:"Navigation Three - Submenu",children:[{type:"group",label:"Item 1",children:[{label:"Option 1",key:"setting:1"},{label:"Option 2",key:"setting:2"}]},{type:"group",label:"Item 2",children:[{label:"Option 3",key:"setting:3"},{label:"Option 4",key:"setting:4"}]}]},{key:"alipay",label:"Navigation Four - Link",title:"Navigation Four - Link"}]}}},{name:"AMenuItem",alias:"Item",parent:"Menu",label:"导航菜单项",categoryId:"nav",doc:"https://www.antdv.com/components/menu-cn",props:[{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"key",label:"key",title:"item 的唯一标志",setters:["StringSetter","NumberSetter"]},{name:"title",label:"title",title:"设置收缩时展示的悬浮标题",setters:"StringSetter"}],slots:["default","icon","title"],snippet:{children:"菜单项",props:{key:"key",title:"Navigation One"}}},{name:"AMenuSubMenu",alias:"SubMenu",childIncludes:["AMenuItem","AMenuSubMenu"],parent:"Menu",label:"导航子菜单",categoryId:"nav",doc:"https://www.antdv.com/components/menu-cn",props:[{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"key",label:"key",title:"唯一标志, 必填",setters:["StringSetter","NumberSetter"]},{name:"popupClassName",label:"popupClassName",title:"子菜单样式",setters:"StringSetter"},{name:"popupOffset",label:"popupOffset",title:'子菜单偏移量，mode="inline" 时无效',setters:"ArraySetter"},{name:"title",label:"title",title:"子菜单项值",setters:"StringSetter"}],events:["titleClick"],slots:["default","expandIcon","icon","title"],snippet:{children:[{name:"AMenuItem",children:"菜单项",props:{key:"key",title:"Navigation One -- AMenuItem"}}]}},{name:"AMenuItemGroup",alias:"ItemGroup",childIncludes:["AMenuItem"],parent:"Menu",label:"导航菜单项组",categoryId:"nav",doc:"https://www.antdv.com/components/menu-cn",props:[{name:"title",label:"title",title:"分组标题",setters:"StringSetter"}],slots:["default","title"],snippet:{children:[{name:"AMenuItem",children:"菜单项",props:{key:"key",title:"Navigation One -- AMenuItem"}}]}},{name:"AMenuDivider",alias:"Divider",parent:"Menu",label:"菜单项分割线",categoryId:"nav",doc:"https://www.antdv.com/components/menu-cn",props:[{name:"dashed",label:"dashed",title:"是否虚线",setters:"BooleanSetter",defaultValue:!1}]}],k=[{name:"APageHeader",alias:"PageHeader",label:"页头",categoryId:"nav",doc:"https://www.antdv.com/components/page-header-cn",props:[{name:"avatar",label:"avatar",title:"标题栏旁的头像",setters:"ObjectSetter"},{name:"backIcon",label:"backIcon",title:"自定义 back icon ，如果为 false 不渲染 back icon",setters:"IconSetter"},{name:"breadcrumb",label:"breadcrumb",title:"面包屑的配置",setters:["ExpressionSetter","ArraySetter"]},{name:"extra",label:"extra",title:"操作区，位于 title 行的行尾",setters:"StringSetter"},{name:"footer",label:"footer",title:"PageHeader 的页脚，一般用于渲染 TabBar",setters:"StringSetter"},{name:"ghost",label:"ghost",title:"pageHeader 的类型，将会改变背景颜色",setters:"BooleanSetter",defaultValue:"true"},{name:"subTitle",label:"subTitle",title:"自定义的二级标题文字",setters:"StringSetter"},{name:"tags",label:"tags",title:"title 旁的 tag 列表",setters:["ObjectSetter","ArraySetter"]},{name:"title",label:"title",title:"自定义标题文字",setters:"StringSetter"}],events:["back"],slots:["default","backIcon","extra","footer","subTitle","title"],snippet:{name:"APageHeader",props:{title:"Title",subTitle:"This is a subtitle",style:{border:"1px solid rgb(235, 237, 240)"}}}}],x=[{name:"APagination",alias:"Pagination",label:"分页",categoryId:"nav",doc:"https://www.antdv.com/components/pagination-cn",props:[{name:"current",label:"current",title:"当前页数",setters:"NumberSetter"},{name:"defaultPageSize",label:"defaultPageSize",title:"默认的每页条数",setters:"NumberSetter",defaultValue:10},{name:"disabled",label:"disabled",title:"禁用分页",setters:"BooleanSetter"},{name:"hideOnSinglePage",label:"hideOnSinglePage",title:"只有一页时是否隐藏分页器",setters:"BooleanSetter",defaultValue:!1},{name:"itemRender",label:"itemRender",title:"用于自定义页码的结构，可用于优化 SEO",setters:"FunctionSetter"},{name:"pageSize",label:"pageSize",title:"每页条数",setters:"NumberSetter"},{name:"pageSizeOptions",label:"pageSizeOptions",title:"指定每页可以显示多少条",setters:"ArraySetter",defaultValue:["10","20","50","100"]},{name:"responsive",label:"responsive",title:"当 size 未指定时，根据屏幕宽度自动调整尺寸",setters:"BooleanSetter"},{name:"showLessItems",label:"showLessItems",title:"是否显示较少页面内容",setters:"BooleanSetter",defaultValue:!1},{name:"showQuickJumper",label:"showQuickJumper",title:"是否可以快速跳转至某页",setters:"BooleanSetter",defaultValue:!1},{name:"showSizeChanger",label:"showSizeChanger",title:"是否展示 pageSize 切换器，当 total 大于 50 时默认为 true",setters:"BooleanSetter"},{name:"showTotal",label:"showTotal",title:"用于显示数据总量和当前数据顺序",setters:"FunctionSetter"},{name:"simple",label:"simple",title:"当添加该属性时，显示为简单分页",setters:"BooleanSetter"},{name:"size",label:"size",title:"当为「small」时，是小尺寸分页",setters:"StringSetter",defaultValue:""},{name:"total",label:"total",title:"数据总数",setters:"NumberSetter",defaultValue:0}],events:["change","showSizeChange","update:current","update:pageSize"],snippet:{name:"APagination",props:{total:1e3,showLessItems:!0,showQuickJumper:!0,showSizeChanger:!0}}}],C=[{name:"ASteps",alias:"Steps",label:"步骤条",categoryId:"nav",doc:"https://www.antdv.com/components/steps-cn",props:[{name:"current",label:"current",title:"指定当前步骤，从 0 开始记数。在子 Step 元素中，可以通过 status 属性覆盖状态",setters:"NumberSetter",defaultValue:0},{name:"direction",label:"direction",title:"指定步骤条方向",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"horizontal"},{name:"initial",label:"initial",title:"起始序号，从 0 开始记数",setters:"NumberSetter",defaultValue:0},{name:"labelPlacement",label:"labelPlacement",title:"指定标签放置位置，默认水平放图标右侧，可选vertical放图标下方",setters:"SelectSetter",options:["vertical","horizontal"],defaultValue:"horizontal"},{name:"percent",label:"percent",title:"当前 process 步骤显示的进度条进度（只对基本类型的 Steps 生效）",setters:"NumberSetter"},{name:"progressDot",label:"progressDot",title:"点状步骤条，可以设置为一个 作用域插槽,labelPlacement 将强制为vertical",setters:"BooleanSetter",defaultValue:!1},{name:"responsive",label:"responsive",title:"当屏幕宽度小于 532px 时自动变为垂直模式",setters:"BooleanSetter",defaultValue:!0},{name:"size",label:"size",title:"指定大小",setters:"SelectSetter",options:["default","small"],defaultValue:"default"},{name:"status",label:"status",title:"指定当前步骤的状态，可选 wait process finish error",setters:"SelectSetter",options:["wait","process","finish","error"],defaultValue:"default"},{name:"items",label:"items",title:"配置选项卡内容",setters:"ArraySetter",defaultValue:[]}],events:["change","update:current"],slots:["default","progressDot"],snippet:{props:{current:1,items:[{title:"Finished",description:"This is a description."},{title:"In Progress",description:"This is a description.",subTitle:"Left 00:00:08"},{title:"Waiting",description:"This is a description."}]}}},{name:"AStep",alias:"Step",parent:"Steps",label:"步骤项",categoryId:"nav",doc:"https://www.antdv.com/components/steps-cn",props:[{name:"description",label:"description",title:"步骤的详情描述",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"	禁用点击",setters:"BooleanSetter",defaultValue:!1},{name:"icon",label:"icon",title:"步骤图标的类型",setters:"StringSetter"},{name:"status",label:"status",title:"指定状态。当不配置该属性时，会使用 Steps 的 current 来自动指定状态",setters:"SelectSetter",options:["wait","process","finish"],defaultValue:"wait"},{name:"subTitle",label:"subTitle",title:"子标题",setters:"StringSetter"},{name:"title",label:"title",title:"标题",setters:"StringSetter"}],slots:["description","icon","subTitle","title"],snippet:{props:{title:"步骤",description:"步骤的详情描述"}}}],I=[{name:"AAutoComplete",alias:"AutoComplete",label:"自动补全输入框",categoryId:"input",doc:"https://www.antdv.com/components/auto-complete-cn",props:[{name:"allowClear",label:"allowClear",title:"支持清除, 单选模式有效",setters:"BooleanSetter",defaultValue:!1},{name:"backfill",label:"backfill",title:"使用键盘选择选项的时候把选中项回填到输入框中",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"defaultActiveFirstOption",label:"defaultActiveFirstOption",title:"是否默认高亮第一个选项。",setters:"BooleanSetter",defaultValue:!0},{name:"defaultOpen",label:"defaultOpen",title:"是否默认展开下拉菜单",setters:"BooleanSetter"},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"popupClassName",label:"popupClassName",title:"下拉菜单的 className 属性",setters:"StringSetter"},{name:"dropdownMatchSelectWidth",label:"dropdownMatchSelectWidth",title:"下拉菜单和选择器同宽。默认将设置 min-width，当值小于选择框宽度时会被忽略。false 时会关闭虚拟滚动",setters:["BooleanSetter","NumberSetter"],defaultValue:!0},{name:"dropdownMenuStyle",label:"dropdownMenuStyle",title:"dropdown 菜单自定义样式",setters:"ObjectSetter"},{name:"filterOption",label:"filterOption",title:"是否根据输入项进行筛选。当其为一个函数时，会接收 inputValue option 两个参数，当 option 符合筛选条件时，应返回 true，反之则返回 false。",setters:["BooleanSetter","FunctionSetter"],defaultValue:!0},{name:"open",label:"open",title:"是否展开下拉菜单",setters:"BooleanSetter"},{name:"options",label:"options",title:"自动完成的数据源",setters:"ArraySetter"},{name:"placeholder",label:"placeholder",title:"输入框提示",setters:"StringSetter"},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"value",label:"value",title:"指定当前选中的条目",setters:["StringSetter","ArraySetter","ObjectSetter"]}],events:["blur","change","dropdownVisibleChange","focus","search","select","clear","update:value"],slots:[{name:"default"},{name:"clearIcon"},{name:"default"},{name:"option",params:["value","label","array"]},{name:"placeholder"}],snippet:{props:{placeholder:"input here",style:{width:"200px"}}}}],T=[{name:"ACascader",alias:"Cascader",label:"级联选择器",categoryId:"input",doc:"https://www.antdv.com/components/cascader-cn",props:[{name:"allowClear",label:"allowClear",title:"是否支持清除",setters:"BooleanSetter",defaultValue:!0},{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"changeOnSelect",label:"changeOnSelect",title:"（单选时生效）当此项为 true 时，点选每级菜单选项值都会发生变化，具体见上面的演示",setters:"BooleanSetter",defaultValue:!1},{name:"defaultValue",label:"defaultValue",title:"默认的选中项",setters:"ArraySetter",defaultValue:[]},{name:"disabled",label:"disabled",title:"禁用",setters:"BooleanSetter",defaultValue:!1},{name:"displayRender",label:"displayRender",title:'选择后展示的渲染函数,可使用 #displayRender="{labels, selectedOptions}"',setters:"FunctionSetter"},{name:"popupClassName",label:"popupClassName",title:"自定义浮层类名",setters:"StringSetter"},{name:"dropdownStyle",label:"dropdownStyle",title:"自定义浮层样式",setters:"ObjectSetter",defaultValue:{}},{name:"expandTrigger",label:"expandTrigger",title:"次级菜单的展开方式",setters:"SelectSetter",options:["click","hover"],defaultValue:"click"},{name:"fieldNames",label:"fieldNames",title:"自定义 options 中 label value children 的字段",setters:"ObjectSetter",defaultValue:{label:"label",value:"value",children:"children"}},{name:"getPopupContainer",label:"getPopupContainer",title:"菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位",setters:"FunctionSetter"},{name:"loadData",label:"loadData",title:"用于动态加载选项，无法与 showSearch 一起使用",setters:"FunctionSetter"},{name:"maxTagCount",label:"maxTagCount",title:"最多显示多少个 tag，响应式模式会对性能产生损耗",setters:["NumberSetter","SelectSetter"],options:["responsive"]},{name:"maxTagPlaceholder",label:"maxTagPlaceholder",title:"隐藏 tag 时显示的内容",setters:"FunctionSetter"},{name:"multiple",label:"multiple",title:"支持多选节点",setters:"BooleanSetter"},{name:"notFoundContent",label:"notFoundContent",title:"当下拉列表为空时显示的内容",setters:"StringSetter",defaultValue:"Not Found"},{name:"open",label:"open",title:"控制浮层显隐",setters:"BooleanSetter"},{name:"options",label:"options",title:"可选项数据源",setters:"ArraySetter"},{name:"placeholder",label:"placeholder",title:"输入框占位文本",setters:"StringSetter",defaultValue:"请选择"},{name:"placement",label:"placement",title:"浮层预设位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"showCheckedStrategy",label:"showCheckedStrategy",title:"定义选中项回填的方式。Cascader.SHOW_CHILD: 只显示选中的子节点。Cascader.SHOW_PARENT: 只显示父节点（当父节点下所有子节点都选中时）",setters:"SelectSetter",options:["Cascader.SHOW_PARENT","Cascader.SHOW_PARENT"],defaultValue:"Cascader.SHOW_PARENT"},{name:"searchValue",label:"searchValue",title:"设置搜索的值，需要与 showSearch 配合使用",setters:"StringSetter"},{name:"showSearch",label:"showSearch",title:"在选择框中显示搜索框",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"size",label:"size",title:"输入框大小",setters:"SelectSetter",options:["larger","default","small"],defaultValue:"default"},{name:"suffixIcon",label:"suffixIcon",title:"自定义的选择框后缀图标",setters:"StringSetter"},{name:"value",label:"value",title:"指定选中项",setters:"ArraySetter"}],events:["change","dropdownVisibleChange","search","update:value"],slots:["default","clearIcon","expandIcon","maxTagPlaceholder","notFoundContent","removeIcon","suffixIcon","tagRender"],snippet:{props:{options:l(),props:{},value:[]}}}],O=[{name:"ACheckbox",alias:"Checkbox",label:"多选框",categoryId:"input",doc:"https://www.antdv.com/components/checkbox-cn",props:[{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"checked",label:"checked",title:"指定当前是否选中",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"失效状态",setters:"BooleanSetter",defaultValue:!1},{name:"indeterminate",label:"indeterminate",title:"设置 indeterminate 状态，只负责样式控制",setters:"BooleanSetter",defaultValue:!1},{name:"value",label:"value",title:"与 CheckboxGroup 组合使用时的值",setters:["BooleanSetter","StringSetter","NumberSetter"]}],events:["change","update:checked"],snippet:{children:"checkbox"}},{name:"ACheckboxGroup",alias:"Group",parent:"Checkbox",label:"多选框组",categoryId:"input",doc:"https://www.antdv.com/components/checkbox-cn",props:[{name:"disabled",label:"disabled",title:"整组失效",setters:"BooleanSetter",defaultValue:!1},{name:"name",label:"name",title:'CheckboxGroup 下所有 input[type="checkbox"] 的 name 属性',setters:"StringSetter"},{name:"options",label:"options",title:'指定可选项，可以通过 slot="label" slot-scope="option" 定制label',setters:"ArraySetter",defaultValue:[]},{name:"value",label:"value",title:"指定选中的选项",setters:"ArraySetter",defaultValue:[]}],events:["change","update:value"],slots:["default","label"],snippet:{props:{options:[{label:"Apple",value:"Apple"},{label:"Pear",value:"Pear"},{label:"Orange",value:"Orange"}]}}}],N=[{name:"ADatePicker",alias:"DatePicker",label:"日期选择框",categoryId:"input",doc:"https://www.antdv.com/components/date-picker-cn",props:[{name:"allowClear",label:"allowClear",title:"是否显示清除按钮",setters:"BooleanSetter",defaultValue:!0},{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"disabled",label:"disabled",title:"禁用",setters:"BooleanSetter",defaultValue:!1},{name:"disabledDate",label:"disabledDate",title:"不可选择的日期",setters:"FunctionSetter"},{name:"format",label:"format",title:"设置日期格式，为数组时支持多格式匹配，展示以第一个为准",setters:"StringSetter",defaultValue:"YYYY-MM-DD"},{name:"dropdownClassName",label:"dropdownClassName",title:"额外的弹出日历 className",setters:"StringSetter"},{name:"getPopupContainer",label:"getPopupContainer",title:"定义浮层的容器，默认为 body 上新建 div",setters:"FunctionSetter"},{name:"inputReadOnly",label:"inputReadOnly",title:"设置输入框为只读（避免在移动设备上打开虚拟键盘）",setters:"BooleanSetter",defaultValue:!1},{name:"local",label:"local",title:"国际化配置",setters:"ObjectSetter"},{name:"mode",label:"mode",title:"日期面板的状态",setters:"SelectSetter",options:["time","date","month","year","decade"]},{name:"open",label:"open",title:"控制弹层是否展开",setters:"BooleanSetter"},{name:"picker",label:"picker",title:"设置选择器类型",setters:"SelectSetter",options:["date","week","month","quarter","year"],defaultValue:"date"},{name:"placeholder",label:"placeholder",title:"输入框提示文字",setters:["StringSetter","ArraySetter"]},{name:"placement",label:"placement",title:"选择框弹出的位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"popupStyle",label:"popupStyle",title:"额外的弹出日历样式",setters:"ObjectSetter",defaultValue:{}},{name:"presets",label:"presets",title:"预设时间范围快捷选择",setters:"ArraySetter"},{name:"size",label:"size",title:"输入框大小，large 高度为 40px，small 为 24px，默认是 32px",setters:"SelectSetter",options:["large","middle","small"]},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"valueFormat",label:"valueFormat",title:"可选，绑定值的格式，对 value、defaultValue、defaultPickerValue 起作用。不指定则绑定值为 dayjs 对象",setters:"StringSetter"},{name:"defaultPickerValue",label:"defaultPickerValue",title:"默认面板日期",setters:"FunctionSetter"},{name:"disabledTime",label:"disabledTime",title:"不可选择的时间",setters:"FunctionSetter"},{name:"format",label:"format",title:"展示的日期格式，配置参考 dayjs",setters:"StringSetter",defaultValue:"YYYY-MM-DD"},{name:"showNow",label:"showNow",title:"当设定了 showTime 的时候，面板是否显示“此刻”按钮",setters:"BooleanSetter"},{name:"showTime",label:"showTime",title:"增加时间选择功能",setters:["ObjectSetter","BooleanSetter"]},{name:"showTime.defaultValue",label:"showTime.defaultValue",title:"设置用户选择日期时默认的时分秒",setters:"FunctionSetter"},{name:"showToday",label:"showToday",title:"是否展示“今天”按钮",setters:"BooleanSetter",defaultValue:!0},{name:"value",label:"value",title:"日期",setters:"FunctionSetter"}],events:["openChange","panelChange","change","ok","update:value"],slots:["default","dateRender","nextIcon","prevIcon","suffixIcon","superNextIcon","superPrevIcon","renderExtraFooter"]},{name:"ARangePicker",alias:"RangePicker",label:"日期选择框",categoryId:"input",doc:"https://www.antdv.com/components/date-picker-cn",props:[{name:"allowClear",label:"allowClear",title:"是否显示清除按钮",setters:"BooleanSetter",defaultValue:!0},{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"dateRender",label:"dateRender",title:"自定义日期单元格的内容"},{name:"disabled",label:"disabled",title:"禁用",setters:"BooleanSetter",defaultValue:!1},{name:"disabledDate",label:"disabledDate",title:"不可选择的日期",setters:"FunctionSetter"},{name:"format",label:"format",title:"设置日期格式，为数组时支持多格式匹配，展示以第一个为准",setters:"StringSetter",defaultValue:"YYYY-MM-DD"},{name:"dropdownClassName",label:"dropdownClassName",title:"额外的弹出日历 className",setters:"StringSetter"},{name:"getPopupContainer",label:"getPopupContainer",title:"定义浮层的容器，默认为 body 上新建 div",setters:"FunctionSetter"},{name:"inputReadOnly",label:"inputReadOnly",title:"设置输入框为只读（避免在移动设备上打开虚拟键盘）",setters:"BooleanSetter",defaultValue:!1},{name:"local",label:"local",title:"国际化配置",setters:"ObjectSetter"},{name:"mode",label:"mode",title:"日期面板的状态",setters:"SelectSetter",options:["time","date","month","year","decade"]},{name:"open",label:"open",title:"控制弹层是否展开",setters:"BooleanSetter"},{name:"picker",label:"picker",title:"设置选择器类型",setters:"SelectSetter",options:["date","week","month","quarter","year"],defaultValue:"date"},{name:"placeholder",label:"placeholder",title:"输入框提示文字",setters:["StringSetter","ArraySetter"]},{name:"placement",label:"placement",title:"选择框弹出的位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"popupStyle",label:"popupStyle",title:"额外的弹出日历样式",setters:"ObjectSetter",defaultValue:{}},{name:"presets",label:"presets",title:"预设时间范围快捷选择",setters:"ArraySetter"},{name:"size",label:"size",title:"输入框大小，large 高度为 40px，small 为 24px，默认是 32px",setters:"SelectSetter",options:["large","middle","small"]},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"valueFormat",label:"valueFormat",title:"可选，绑定值的格式，对 value、defaultValue、defaultPickerValue 起作用。不指定则绑定值为 dayjs 对象",setters:"StringSetter"},{name:"allowEmpty",label:"allowEmpty",title:"允许起始项部分为空",setters:["BooleanSetter","BooleanSetter"],defaultValue:[!1,!1]},{name:"defaultPickerValue",label:"defaultPickerValue",title:"默认面板日期",setters:"ArraySetter"},{name:"disabled",label:"disabled",title:"禁用起始项",setters:["BooleanSetter","BooleanSetter"]},{name:"disabledTime",label:"disabledTime",title:"不可选择的时间",setters:"FunctionSetter"},{name:"format",label:"format",title:"展示的日期格式",setters:"StringSetter",defaultValue:"YYYY-MM-DD HH:mm:ss"},{name:"presets",label:"presets",title:"预设时间范围快捷选择",setters:"ArraySetter"},{name:"ranges",label:"ranges",title:"预设时间范围快捷选择",setters:"ObjectSetter"},{name:"separator",label:"separator",title:"设置分隔符",setters:"StringSetter"},{name:"showTime",label:"showTime",title:"增加时间选择功能",setters:["ObjectSetter","BooleanSetter"]},{name:"showTime.defaultValue",label:"showTime.defaultValue",title:"设置用户选择日期时默认的时分秒",setters:"ArraySetter"},{name:"value",label:"value",title:"日期",setters:"ArraySetter"}],events:["openChange","panelChange","calendarChange","change","ok","update:value"],slots:["default","dateRender","nextIcon","prevIcon","suffixIcon","superNextIcon","superPrevIcon","dateRender","renderExtraFooter"]}],P=[{name:"AForm",alias:"Form",label:"表单",categoryId:"input",doc:"https://www.antdv.com/components/form-cn",props:[{name:"colon",label:"colon",title:"配置 Form.Item 的 colon 的默认值 (只有在属性 layout 为 horizontal 时有效)",setters:"BooleanSetter",defaultValue:!0},{name:"disabled",label:"disabled",title:"设置表单组件禁用",setters:"BooleanSetter",defaultValue:!1},{name:"hideRequiredMark",label:"hideRequiredMark",title:"隐藏所有表单项的必选标记",setters:"BooleanSetter",defaultValue:!1},{name:"labelAlign",label:"labelAlign",title:"label 标签的文本对齐方式",setters:"SelectSetter",options:["left","right"],defaultValue:"right"},{name:"labelCol",label:"labelCol",title:"label 标签布局，同 <Col> 组件，设置 span offset 值，如 {span: 3, offset: 12} 或 sm: {span: 3, offset: 12}",setters:"ObjectSetter"},{name:"labelWrap",label:"labelWrap",title:"label 标签的文本换行方式",setters:"BooleanSetter",defaultValue:!1},{name:"layout",label:"layout",title:"表单布局",setters:"SelectSetter",options:["horizontal","vertical","inline"],defaultValue:"horizontal"},{name:"model",label:"model",title:"表单数据对象",setters:"ObjectSetter"},{name:"name",label:"name",title:"表单名称，会作为表单字段 id 前缀使用",setters:"StringSetter"},{name:"noStyle",label:"noStyle",title:"为 true 时不带样式，作为纯字段控件使用",setters:"BooleanSetter",defaultValue:!1},{name:"rules",label:"rules",title:"表单验证规则",setters:"ObjectSetter"},{name:"scrollToFirstError",label:"scrollToFirstError",title:"提交失败自动滚动到第一个错误字段",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"validateOnRuleChange",label:"validateOnRuleChange",title:"是否在 rules 属性改变后立即触发一次验证",setters:"BooleanSetter",defaultValue:!0},{name:"validateTrigger",label:"validateTrigger",title:"统一设置字段校验规则",setters:["StringSetter","ArraySetter"],defaultValue:"change"},{name:"wrapperCol",label:"wrapperCol",title:"需要为输入控件设置布局样式时，使用该属性，用法同 labelCol",setters:"ObjectSetter"}],events:["finish","finishFailed","submit","validate"],snippet:{children:[{name:"AFormItem",props:{label:"Username",name:"username",rules:[{required:!0,message:"Please input your username!"}]},children:[{name:"AInput"}]},{name:"AFormItem",props:{label:"Password",name:"password",rules:[{required:!0,message:"Please input your password!"}]},children:[{name:"AInputPassword"}]},{name:"AFormItem",props:{name:"remember",wrapperCol:{offset:8,span:16}},children:[{name:"ACheckbox",children:"Remember me"}]},{name:"AFormItem",props:{wrapperCol:{offset:8,span:16}},children:[{name:"AButton",props:{type:"primary"},children:"Submit"}]}]}},{name:"AFormItem",alias:"Item",parent:"Form",label:"表单项",categoryId:"input",doc:"https://www.antdv.com/components/form-cn",props:[{name:"autoLink",label:"autoLink",title:"是否自动关联表单域，对于大部分情况都可以使用自动关联，如果不满足自动关联的条件，可以手动关联",setters:"BooleanSetter",defaultValue:!0},{name:"colon",label:"colon",title:"配合 label 属性使用，表示是否显示 label 后面的冒号",setters:"BooleanSetter",defaultValue:!0},{name:"extra",label:"extra",title:"额外的提示信息，和 help 类似，当需要错误信息和提示文案同时出现时，可以使用这个",setters:"StringSetter"},{name:"hasFeedback",label:"hasFeedback",title:"配合 validateStatus 属性使用，展示校验状态图标，建议只配合 Input 组件使用",setters:"BooleanSetter",defaultValue:!1},{name:"help",label:"help",title:"提示信息，如不设置，则会根据校验规则自动生成",setters:"StringSetter"},{name:"htmlFor",label:"htmlFor",title:"设置子元素 label htmlFor 属性",setters:"StringSetter"},{name:"label",label:"label",title:"label 标签的文本",setters:"StringSetter"},{name:"labelAlign",label:"labelAlign",title:"label 标签的文本对齐方式",setters:"SelectSetter",options:["left","right"],defaultValue:"right"},{name:"labelCol",label:"labelCol",title:"label 标签布局，同 <Col> 组件，设置 span offset 值，如 {span: 3, offset: 12} 或 sm: {span: 3, offset: 12}",setters:"ObjectSetter"},{name:"name",label:"name",title:"表单域 model 字段，在使用 validate、resetFields 方法的情况下，该属性是必填的",setters:["StringSetter","NumberSetter","ArraySetter"]},{name:"required",label:"required",title:"是否必填，如不设置，则会根据校验规则自动生成",setters:"BooleanSetter",defaultValue:!1},{name:"rules",label:"rules",title:"表单验证规则",setters:["ObjectSetter","ArraySetter"]},{name:"tooltip",label:"tooltip",title:"配置提示信息",setters:"StringSetter"},{name:"validateFirst",label:"validateFirst",title:"当某一规则校验不通过时，是否停止剩下的规则的校验",setters:"BooleanSetter",defaultValue:!1},{name:"validateStatus",label:"validateStatus",title:"校验状态，如不设置，则会根据校验规则自动生成",setters:"SelectSetter",options:["success","warning","error","validating"]},{name:"validateTrigger",label:"validateTrigger",title:"设置字段校验的时机",setters:["StringSetter","ArraySetter"],defaultValue:"change"},{name:"wrapperCol",label:"wrapperCol",title:"要为输入控件设置布局样式时，使用该属性，用法同 labelCol",setters:"ObjectSetter"}],slots:["default","extra","help","label","tooltip"],snippet:{props:{label:"表单项"},children:[{name:"AInput"}]}}],F=[{name:"AInput",alias:"Input",label:"输入框",categoryId:"input",doc:"https://www.antdv.com/components/input-cn",props:[{name:"addonAfter",label:"addonAfter",title:"带标签的 input，设置后置标签",setters:"StringSetter"},{name:"addonBefore",label:"addonBefore",title:"带标签的 input，设置前置标签",setters:"StringSetter"},{name:"allowClear",label:"allowClear",title:"可以点击清除图标删除内容",setters:"BooleanSetter"},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"defaultValue",label:"defaultValue",title:"输入框默认内容",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"是否禁用状态，默认为 false",setters:"BooleanSetter",defaultValue:!1},{name:"id",label:"id",title:"输入框的 id",setters:"StringSetter"},{name:"placeholder",label:"placeholder",title:"输入框的提示文字",setters:"StringSetter"},{name:"maxlength",label:"maxlength",title:"最大长度",setters:"NumberSetter"},{name:"prefix",label:"prefix",title:"带有前缀图标的 input",setters:"StringSetter"},{name:"showCount",label:"showCount",title:"是否展示字数",setters:"BooleanSetter",defaultValue:!1},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"size",label:"size",title:"控件大小。注：标准表单内的输入框大小限制为 middle。可选 large middle small",setters:"SelectSetter",options:["large","middle","small"]},{name:"suffix",label:"suffix",title:"带有后缀图标的 input",setters:"StringSetter"},{name:"type",label:"type",title:'声明 input 类型，同原生 input 标签的 type 属性，见：MDN(请直接使用 <a-textarea /> 代替 type="textarea")',setters:"StringSetter",defaultValue:"text"},{name:"value",label:"value",title:"输入框内容",setters:"StringSetter",defaultValue:"text"}],events:["change","pressEnter","update:value"],slots:["default","addonAfter","addonBefore","clearIcon","prefix","suffix"]},{name:"ATextarea",alias:"Textarea",label:"文本域",categoryId:"input",doc:"https://www.antdv.com/components/input-cn",props:[{name:"allowClear",label:"allowClear",title:"可以点击清除图标删除内容",setters:"BooleanSetter"},{name:"autosize",label:"autosize",title:"自适应内容高度，可设置为 true | false 或对象：{ minRows: 2, maxRows: 6 }",setters:["BooleanSetter","ObjectSetter"]},{name:"defaultValue",label:"defaultValue",title:"输入框默认内容",setters:"StringSetter"},{name:"placeholder",label:"placeholder",title:"输入框的提示文字",setters:"StringSetter"},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"showCount",label:"showCount",title:"是否展示字数",setters:"BooleanSetter",defaultValue:!1},{name:"value",label:"value",title:"输入框内容",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"是否禁用状态",setters:"BooleanSetter",defaultValue:!1},{name:"rows",label:"rows",title:"指定输入框的行数",setters:"NumberSetter",defaultValue:2},{name:"maxlength",label:"maxlength",title:"最大长度",setters:"NumberSetter"},{name:"minlength",label:"minlength",title:"最小长度",setters:"NumberSetter"},{name:"placeholder",label:"placeholder",title:"输入框的提示文字",setters:"StringSetter"},{name:"readonly",label:"readonly",title:"是否只读",setters:"BooleanSetter",defaultValue:!1},{name:"wrap",label:"wrap",title:"文本域的文本换行方式",setters:"SelectSetter",options:["soft","hard","off"],defaultValue:"soft"}],events:["pressEnter","update:value"]},{name:"AInputSearch",alias:"Search",parent:"Input",label:"搜索框",categoryId:"input",doc:"https://www.antdv.com/components/input-cn",props:[{name:"enterButton",label:"enterButton",title:"是否有确认按钮，可设为按钮文字。该属性会与 addon 冲突",setters:"BooleanSetter",defaultValue:!1},{name:"loading",label:"loading",title:"搜索 loading",setters:"BooleanSetter"},{name:"addonAfter",label:"addonAfter",title:"带标签的 input，设置后置标签",setters:"StringSetter"},{name:"addonBefore",label:"addonBefore",title:"带标签的 input，设置前置标签",setters:"StringSetter"},{name:"allowClear",label:"allowClear",title:"可以点击清除图标删除内容",setters:"BooleanSetter"},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"defaultValue",label:"defaultValue",title:"输入框默认内容",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"是否禁用状态，默认为 false",setters:"BooleanSetter",defaultValue:!1},{name:"id",label:"id",title:"输入框的 id",setters:"StringSetter"},{name:"placeholder",label:"placeholder",title:"输入框的提示文字",setters:"StringSetter"},{name:"maxlength",label:"maxlength",title:"最大长度",setters:"NumberSetter"},{name:"prefix",label:"prefix",title:"带有前缀图标的 input",setters:"StringSetter"},{name:"showCount",label:"showCount",title:"是否展示字数",setters:"BooleanSetter",defaultValue:!1},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"size",label:"size",title:"控件大小。注：标准表单内的输入框大小限制为 middle。可选 large middle small",setters:"SelectSetter",options:["large","middle","small"]},{name:"suffix",label:"suffix",title:"带有后缀图标的 input",setters:"StringSetter"},{name:"type",label:"type",title:'声明 input 类型，同原生 input 标签的 type 属性，见：MDN(请直接使用 <a-textarea /> 代替 type="textarea")',setters:"StringSetter",defaultValue:"text"},{name:"value",label:"value",title:"输入框内容",setters:"StringSetter",defaultValue:"text"}],events:["change","pressEnter","search","update:value"],slots:["enterButton"]},{name:"AInputGroup",alias:"Group",parent:"Input",label:"输入框组合",categoryId:"input",doc:"https://www.antdv.com/components/input-cn",props:[{name:"compact",label:"compact",title:"是否用紧凑模式",setters:"BooleanSetter",defaultValue:!1},{name:"size",label:"size",title:"Input.Group 中所有的 Input 的大小，可选 large default small",setters:"SelectSetter",options:["large","default","small"],defaultValue:"default"}],snippet:{children:[{name:"AInput",children:"input1"},{name:"AInput",children:"input2"},{name:"AInput",children:"input3"}]}},{name:"AInputPassword",alias:"Password",parent:"Input",label:"密码输入框",categoryId:"input",doc:"https://www.antdv.com/components/input-cn",props:[{name:"visible",label:"visible",title:"密码是否可见",setters:"BooleanSetter",defaultValue:!1},{name:"placeholder",label:"placeholder",title:"输入框的提示文字",setters:"StringSetter"},{name:"visibilityToggle",label:"visibilityToggle",title:"是否显示切换按钮或者控制密码显隐",setters:"BooleanSetter",defaultValue:!0}],events:["update:visible"],slots:["iconRender"]}],j=[{name:"AInputNumber",alias:"InputNumber",label:"数字输入框",categoryId:"input",doc:"https://www.antdv.com/components/input-number-cn",props:[{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"controls",label:"controls",title:"是否显示增减按钮",setters:"BooleanSetter",defaultValue:!0},{name:"decimalSeparator",label:"decimalSeparator",title:"小数点",setters:"StringSetter"},{name:"defaultValue",label:"defaultValue",title:"初始值",setters:"NumberSetter"},{name:"disabled",label:"disabled",title:"禁用",setters:"BooleanSetter",defaultValue:!1},{name:"formatter",label:"formatter",title:"指定输入框展示值的格式",setters:"FunctionSetter"},{name:"keyboard",label:"keyboard",title:"是否启用键盘快捷行为",setters:"BooleanSetter",defaultValue:!0},{name:"max",label:"max",title:"最大值",setters:"NumberSetter",defaultValue:1/0},{name:"min",label:"min",title:"最小值",setters:"NumberSetter",defaultValue:-1/0},{name:"parser",label:"parser",title:"指定从 formatter 里转换回数字的方式，和 formatter 搭配使用",setters:"FunctionSetter"},{name:"precision",label:"precision",title:"数值精度",setters:"NumberSetter"},{name:"size",label:"size",title:"输入框大小",setters:"SelectSetter",options:["large","","small"]},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"step",label:"step",title:"每次改变步数，可以为小数",setters:["StringSetter","NumberSetter"],defaultValue:1},{name:"stringMode",label:"stringMode",title:"字符值模式，开启后支持高精度小数。同时 change 事件将返回 string 类型",setters:"BooleanSetter",defaultValue:1},{name:"value",label:"value",title:"当前值",setters:"NumberSetter"}],events:["change","pressEnter","step","update:value"],slots:["default","addonAfter","addonBefore","prefix","upIcon","downIcon"]}],D=[{name:"AMentions",alias:"Mentions",label:"提及",categoryId:"input",doc:"https://www.antdv.com/components/mentions-cn",props:[{name:"autofocus",label:"autofocus",title:"自动获得焦点",setters:"BooleanSetter",defaultValue:!1},{name:"defaultValue",label:"defaultValue",title:"默认值",setters:"StringSetter"},{name:"filterOption",label:"filterOption",title:"自定义过滤逻辑",setters:["BooleanSetter","FunctionSetter"]},{name:"getPopupContainer",label:"getPopupContainer",title:"指定建议框挂载的 HTML 节点",setters:"FunctionSetter"},{name:"notFoundContent",label:"notFoundContent",title:"当下拉列表为空时显示的内容",setters:"StringSetter",defaultValue:"Not Found"},{name:"placement",label:"placement",title:"弹出层展示位置",setters:"SelectSetter",options:["top","bottom"],defaultValue:"bottom"},{name:"prefix",label:"prefix",title:"设置触发关键字",setters:["StringSetter","ArraySetter"],defaultValue:"@"},{name:"split",label:"split",title:"设置选中项前后分隔符",setters:"StringSetter",defaultValue:" "},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"validateSearch",label:"validateSearch",title:"自定义触发验证逻辑",setters:"FunctionSetter"},{name:"value",label:"value",title:"设置值",setters:"StringSetter"},{name:"options",label:"options",title:"选项配置",setters:"ObjectSetter",defaultValue:[]},{name:"placeholder",label:"placeholder",title:"输入框的提示文字",setters:"StringSetter"},{name:"loading",label:"loading",title:"是否加载中",setters:"BoolSetter"}],events:["blur","change","focus","search","select","update:value"],slots:[{name:"default"},{name:"notFoundContent"},{name:"option",params:["object"]}],snippet:{props:{value:"@afc163",options:[{value:"afc163",label:"afc163"},{value:"zombieJ",label:"zombieJ"},{value:"yesmeck",label:"yesmeck"}]}}},{name:"AMentionOption",alias:"MentionOption",parent:"Mention",label:"提及项",categoryId:"input",doc:"https://www.antdv.com/components/mentions-cn",props:[{name:"value",label:"value",title:"选择时填充的值",setters:"StringSetter",defaultValue:""}],slots:["label"]}],z=[{name:"ARadio",alias:"Radio",label:"单选框",categoryId:"input",doc:"https://www.antdv.com/components/radio-cn",props:[{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"checked",label:"checked",title:"指定当前是否选中",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"禁用 Radio",setters:"BooleanSetter",defaultValue:!1},{name:"value",label:"value",title:"根据 value 进行比较，判断是否选中",setters:"StringSetter"}],events:["update:checked"],snippet:{children:"radio"}},{name:"ARadioButton",alias:"RadioButton",label:"单选按钮",categoryId:"input",doc:"https://www.antdv.com/components/radio-cn",props:[{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"checked",label:"checked",title:"指定当前是否选中",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"禁用 Radio",setters:"BooleanSetter",defaultValue:!1},{name:"value",label:"value",title:"根据 value 进行比较，判断是否选中",setters:"StringSetter"}],events:["update:checked"],snippet:{props:{value:"value1"},children:"Hangzhou"}},{name:"ARadioGroup",alias:"RadioGroup",label:"单选框组合",categoryId:"input",doc:"https://www.antdv.com/components/radio-cn",props:[{name:"buttonStyle",label:"buttonStyle",title:"RadioButton 的风格样式，目前有描边和填色两种风格",setters:"SelectSetter",options:["outline","solid"],defaultValue:"outline"},{name:"disabled",label:"disabled",title:"禁选所有子单选器",setters:"BooleanSetter",defaultValue:!1},{name:"name",label:"name",title:'RadioGroup 下所有 input[type="radio"] 的 name 属性',setters:"StringSetter"},{name:"options",label:"options",title:"以配置形式设置子元素",setters:"ArraySetter"},{name:"optionType",label:"optionType",title:"用于设置 Radio options 类型",setters:"SelectSetter",options:["default","button"],defaultValue:"default"},{name:"size",label:"size",title:"大小，只对按钮样式生效",setters:"SelectSetter",options:["large","default","small"],defaultValue:"default"},{name:"value",label:"value",title:"用于设置当前选中的值",setters:"StringSetter"}],events:["change","update:value"],snippet:{props:{value:"value2",optionType:"button",options:[{label:"Apple",value:"Apple"},{label:"Pear",value:"Pear"},{label:"Orange",value:"Orange",disabled:!0}]}}}],R=[{name:"ARate",alias:"Rate",label:"评分",categoryId:"input",doc:"https://www.antdv.com/components/rate-cn",props:[{name:"allowClear",label:"allowClear",title:"是否允许再次点击后清除",setters:"BooleanSetter",defaultValue:!0},{name:"allowHalf",label:"allowHalf",title:"是否允许半选",setters:"BooleanSetter",defaultValue:!1},{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"character",label:"character",title:"自定义字符",setters:"StringSetter"},{name:"count",label:"count",title:"star 总数",setters:"NumberSetter",defaultValue:5},{name:"disabled",label:"disabled",title:"只读，无法进行交互",setters:"BooleanSetter",defaultValue:!1},{name:"tooltips",label:"tooltips",title:"自定义每项的提示信息",setters:"ArraySetter"},{name:"value",label:"value",title:"当前数，受控值",setters:"NumberSetter"}],events:["blur","change","focus","hoverChange","keydown","update:value"],slots:["default","character"],snippet:{props:{value:2}}}],L=[{name:"ASelect",alias:"Select",label:"选择器",categoryId:"input",doc:"https://www.antdv.com/components/select-cn",props:[{name:"allowClear",label:"allowClear",title:"支持清除",setters:"BooleanSetter",defaultValue:!1},{name:"autoClearSearchValue",label:"autoClearSearchValue",title:"是否在选中项后清空搜索框，只在 mode 为 multiple 或 tags 时有效",setters:"BooleanSetter",defaultValue:!0},{name:"autofocus",label:"autofocus",title:"默认获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"defaultActiveFirstOption",label:"defaultActiveFirstOption",title:"是否默认高亮第一个选项",setters:"BooleanSetter",defaultValue:!0},{name:"defaultOpen",label:"defaultOpen",title:"是否默认展开下拉菜单",setters:"BooleanSetter"},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"popupClassName",label:"popupClassName",title:"下拉菜单的 className 属性",setters:"StringSetter"},{name:"dropdownMatchSelectWidth",label:"dropdownMatchSelectWidth",title:"下拉菜单和选择器同宽。默认将设置 min-width，当值小于选择框宽度时会被忽略。false 时会关闭虚拟滚动",setters:["BooleanSetter","StringSetter"],defaultValue:!0},{name:"dropdownMenuStyle",label:"dropdownMenuStyle",title:"dropdown 菜单自定义样式",setters:"ObjectSetter"},{name:"dropdownRender",label:"dropdownRender",title:"自定义下拉框内容",setters:"FunctionSetter"},{name:"fieldNames",label:"fieldNames",title:"自定义节点 label、value、options 的字段",setters:"ObjectSetter",defaultValue:{label:"label",value:"value",options:"options"}},{name:"filterOption",label:"filterOption",title:"是否根据输入项进行筛选。当其为一个函数时，会接收 inputValue option 两个参数，当 option 符合筛选条件时，应返回 true，反之则返回 false",setters:["BooleanSetter","FunctionSetter"],defaultValue:!0},{name:"filterSort",label:"filterSort",title:"搜索时对筛选结果项的排序函数, 类似Array.sort里的 compareFunction",setters:"FunctionSetter"},{name:"firstActiveValue",label:"firstActiveValue",title:"默认高亮的选项",setters:["StringSetter","ArraySetter"]},{name:"getPopupContainer",label:"getPopupContainer",title:"菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位",setters:"FunctionSetter"},{name:"labelInValue",label:"labelInValue",title:"是否把每个选项的 label 包装到 value 中，会把 Select 的 value 类型从 string 变为 {key: string, label: vNodes, originLabel: any} 的格式, originLabel（3.1） 保持原始类型，如果通过 a-select-option children 构造的节点，该值是是个函数（即 a-select-option 的默认插槽）",setters:"BooleanSetter",defaultValue:!1},{name:"listHeight",label:"listHeight",title:"设置弹窗滚动高度",setters:"NumberSetter",defaultValue:256},{name:"loading",label:"loading",title:"是否显示加载中状态",setters:"BooleanSetter",defaultValue:!1},{name:"maxTagCount",label:"maxTagCount",title:"最多显示多少个 tag",setters:"NumberSetter"},{name:"maxTagPlaceholder",label:"maxTagPlaceholder",title:"隐藏 tag 时显示的内容",setters:"FunctionSetter"},{name:"maxTagTextLength",label:"maxTagTextLength",title:"最大显示的 tag 文本长度",setters:"NumberSetter"},{name:"mode",label:"mode",title:"设置 Select 的模式为多选或标签",setters:"SelectSetter",options:["multiple","tags","combobox"]},{name:"notFoundContent",label:"notFoundContent",title:"当下拉列表为空时显示的内容",setters:"StringSetter",defaultValue:"Not Found"},{name:"open",label:"open",title:"是否展开下拉菜单",setters:"BooleanSetter"},{name:"optionFilterProp",label:"optionFilterProp",title:"搜索时过滤对应的 option 属性，不支持 children",setters:"StringSetter",defaultValue:"value"},{name:"optionLabelProp",label:"optionLabelProp",title:"回填到选择框的 Option 的属性值，默认是 Option 的子元素。比如在子元素需要高亮效果时，此值可以设为 value",setters:"StringSetter",defaultValue:"children"},{name:"options",label:"options",title:"options 数据，如果设置则不需要手动构造 selectOption 节点",setters:"ArraySetter",defaultValue:[]},{name:"placeholder",label:"placeholder",title:"选择框默认文字",setters:"StringSetter"},{name:"placement",label:"placement",title:"选择框弹出的位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"searchValue",label:"searchValue",title:"控制搜索文本",setters:"StringSetter"},{name:"showArrow",label:"showArrow",title:"是否显示下拉小箭头",setters:"BooleanSetter"},{name:"showSearch",label:"showSearch",title:"配置是否可搜索",setters:"BooleanSetter"},{name:"size",label:"size",title:"选择框大小，可选 large small",setters:"SelectSetter",options:["large","default","small"],defaultValue:"default"},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"tagRender",label:"tagRender",title:"自定义 tag 内容 render，仅在 mode 为 multiple 或 tags 时生效",setters:"FunctionSetter"},{name:"tokenSeparators",label:"tokenSeparators",title:'自动分词的分隔符，仅在 mode="tags" 时生效',setters:"ArraySetter"},{name:"value",label:"value",title:"指定当前选中的条目",setters:["StringSetter","NumberSetter","ArraySetter"]},{name:"virtual",label:"virtual",title:"设置 false 时关闭虚拟滚动",setters:"BooleanSetter",defaultValue:!0}],events:["blur","change","deselect","dropdownVisibleChange","focus","inputKeyDown","mouseenter","mouseleave","popupScroll","search","select","update:value"],slots:[{name:"default"},{name:"clearIcon"},{name:"maxTagPlaceholder"},{name:"menuItemSelectedIcon"},{name:"notFoundContent"},{name:"option",params:["value","label","array"]},{name:"placeholder"},{name:"removeIcon"},{name:"suffixIcon"},{name:"tagRender"}],snippet:{props:{style:{width:"120px"},value:"lucy"},children:[{name:"ASelectOption",children:"Jack",props:{value:"jack"}},{name:"ASelectOption",children:"Lucy",props:{value:"lucy"}},{name:"ASelectOption",children:"Disabled",props:{value:"disabled",disabled:!0}},{name:"ASelectOption",children:"Wendy",props:{value:"wendy"}}]}},{name:"ASelectOption",alias:"SelectOption",parentIncludes:["ASelect","ASelectOptGroup"],parent:"Select",label:"选择器项",categoryId:"input",doc:"https://www.antdv.com/components/select-cn",props:[{name:"class",label:"class",title:"Option 器类名",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"key",label:"key",title:"和 value 含义一致。如果 Vue 需要你设置此项，此项值与 value 的值相同，然后可以省略 value 设置",setters:"StringSetter"},{name:"title",label:"title",title:"选中该 Option 后，Select 的 title",setters:"StringSetter"},{name:"value",label:"value",title:"默认根据此属性值进行筛选",setters:["StringSetter","NumberSetter"]}],snippet:{children:"SelectOption",props:{value:"SelectOption"}}},{name:"ASelectOptGroup",alias:"SelectOptGroup",parentIncludes:["ASelect"],parent:"Select",label:"选择器组",categoryId:"input",doc:"https://www.antdv.com/components/select-cn",props:[{name:"key",label:"key",setters:"StringSetter"},{name:"label",label:"label",title:"组名",setters:["StringSetter","FunctionSetter"]}],slots:["label"],snippet:{slot:"label",children:[{name:"span",slot:{name:"label"},children:"Manager"},{name:"ASelectOption",children:"Jack",props:{value:"jack"}},{name:"ASelectOption",children:"Lucy",props:{value:"lucy"}}]}}],M=[{name:"ASlider",alias:"Slider",label:"滑动输入条",categoryId:"input",doc:"https://www.antdv.com/components/slider-cn",props:[{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"值为 true 时，滑块为禁用状态",setters:"BooleanSetter",defaultValue:!1},{name:"dots",label:"dots",title:"是否只能拖拽到刻度上",setters:"BooleanSetter",defaultValue:!1},{name:"included",label:"included",title:"marks 不为空对象时有效，值为 true 时表示值为包含关系，false 表示并列",setters:"BooleanSetter",defaultValue:!0},{name:"marks",label:"marks",title:"刻度标记，key 的类型必须为 number 且取值在闭区间 [min, max] 内，每个标签可以单独设置样式",setters:"ObjectSetter"},{name:"max",label:"max",title:"最大值",setters:"NumberSetter",defaultValue:100},{name:"min",label:"min",title:"最小值",setters:"NumberSetter",defaultValue:0},{name:"range",label:"range",title:"双滑块模式",setters:"BooleanSetter",defaultValue:!1},{name:"reverse",label:"reverse",title:"反向坐标轴",setters:"BooleanSetter",defaultValue:!1},{name:"step",label:"step",title:"步长，取值必须大于 0，并且可被 (max - min) 整除。当 marks 不为空对象时，可以设置 step 为 null，此时 Slider 的可选值仅有 marks 标出来的部分",setters:["NumberSetter","ObjectSetter"],defaultValue:1},{name:"value",label:"value",title:"设置当前取值。当 range 为 false 时，使用 number，否则用 [number, number]",setters:["NumberSetter","ArraySetter"]},{name:"vertical",label:"vertical",title:"值为 true 时，Slider 为垂直方向",setters:"BooleanSetter",defaultValue:!1},{name:"tipFormatter",label:"tipFormatter",title:"Slider 会把当前值传给 tipFormatter，并在 Tooltip 中显示 tipFormatter 的返回值，若为 null，则隐藏 Tooltip",setters:"FunctionSetter"},{name:"tooltipPlacement",label:"tooltipPlacement",title:"设置 Tooltip 展示位置",setters:"StringSetter"},{name:"tooltipOpen",label:"tooltipOpen",title:"值为true时，Tooltip 将会始终显示；否则始终不显示，哪怕在拖拽及移入时",setters:"BooleanSetter"},{name:"getTooltipPopupContainer",label:"getTooltipPopupContainer",title:"Tooltip 渲染父节点，默认渲染到 body 上",setters:"FunctionSetter",defaultValue:{type:"Function",value:"() => document.body	"}}],events:["change","afterChange","update:value"],slots:["default","mark"]}],H=[{name:"ASwitch",alias:"Switch",label:"开关",categoryId:"input",doc:"https://www.antdv.com/components/switch-cn",props:[{name:"autofocus",label:"autofocus",title:"组件自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"checked",label:"checked",title:"指定当前是否选中",setters:"BooleanSetter",defaultValue:!1},{name:"checkedChildren",label:"checkedChildren",title:"选中时的内容",setters:"StringSetter"},{name:"checkedValue",label:"checkedValue",title:"选中时的值",setters:["BooleanSetter","StringSetter","NumberSetter"],defaultValue:!0},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"loading",label:"loading",title:"加载中的开关",setters:"BooleanSetter",defaultValue:!1},{name:"size",label:"size",title:"开关大小，可选值：default small",setters:"SelectSetter",options:["default","small"],defaultValue:"default"},{name:"unCheckedChildren",label:"unCheckedChildren",title:"非选中时的内容",setters:"StringSetter"},{name:"unCheckedValue",label:"unCheckedValue",title:"非选中时的值",setters:["BooleanSetter","StringSetter","NumberSetter"],defaultValue:!1}],events:["change","click","update:checked"],slots:["default","checkedChildren","unCheckedChildren"]}],E=[{name:"ATimePicker",alias:"TimePicker",label:"时间选择框",categoryId:"input",doc:"https://www.antdv.com/components/time-picker-cn",props:[{name:"allowClear",label:"allowClear",title:"是否展示清除按钮",setters:"BooleanSetter",defaultValue:!0},{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"clearText",label:"clearText",title:"清除按钮的提示文案",setters:"StringSetter",defaultValue:"clear"},{name:"disabled",label:"disabled",title:"禁用全部操作",setters:"BooleanSetter",defaultValue:!1},{name:"disabledTime",label:"disabledTime",title:"不可选择的时间",setters:"FunctionSetter"},{name:"format",label:"format",title:"展示的时间格式",setters:"StringSetter",defaultValue:"HH:mm:ss"},{name:"getPopupContainer",label:"getPopupContainer",title:"定义浮层的容器，默认为 body 上新建 div",setters:"FunctionSetter"},{name:"hideDisabledOptions",label:"hideDisabledOptions",title:"隐藏禁止选择的选项",setters:"BooleanSetter",defaultValue:!1},{name:"hourStep",label:"hourStep",title:"小时选项间隔",setters:"NumberSetter",defaultValue:1},{name:"inputReadOnly",label:"inputReadOnly",title:"设置输入框为只读",setters:"BooleanSetter",defaultValue:!1},{name:"minuteStep",label:"minuteStep",title:"分钟选项间隔",setters:"NumberSetter",defaultValue:1},{name:"open",label:"open",title:"面板是否打开",setters:"BooleanSetter",defaultValue:!1},{name:"placeholder",label:"placeholder",title:"没有值的时候显示的内容",setters:["StringSetter","ArraySetter"],defaultValue:"请选择时间"},{name:"placement",label:"placement",title:"选择框弹出的位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"popupClassName",label:"popupClassName",title:"弹出层类名",setters:"StringSetter"},{name:"popupStyle",label:"popupStyle",title:"弹出层样式对象",setters:"ObjectSetter"},{name:"secondStep",label:"secondStep",title:"秒选项间隔",setters:"NumberSetter",defaultValue:1},{name:"showNow",label:"showNow",title:"面板是否显示“此刻”按钮",setters:"BooleanSetter"},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"use12Hours",label:"use12Hours",title:"使用 12 小时制，为 true 时 format 默认为 h:mm:ss a",setters:"BooleanSetter",defaultValue:!1},{name:"value",label:"value",title:"当前时间",setters:"StringSetter"},{name:"valueFormat",label:"valueFormat",title:"可选，绑定值的格式，对 value、defaultValue 起作用。不指定则绑定值为 dayjs 对象",setters:["StringSetter","ObjectSetter"]}],events:["change","openChange","update:open","update:value"],slots:["default","clearIcon","renderExtraFooter","suffixIcon"]},{name:"ATimeRangePicker",alias:"TimeRangePicker",parent:"TimePicker",label:"时间范围选择框",categoryId:"input",doc:"https://www.antdv.com/components/time-picker-cn",props:[{name:"allowClear",label:"allowClear",title:"是否显示清除按钮",setters:"BooleanSetter",defaultValue:!0},{name:"autofocus",label:"autofocus",title:"自动获取焦点",setters:"BooleanSetter",defaultValue:!1},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"dateRender",label:"dateRender",title:"自定义日期单元格的内容"},{name:"disabled",label:"disabled",title:"禁用",setters:"BooleanSetter",defaultValue:!1},{name:"disabledDate",label:"disabledDate",title:"不可选择的日期",setters:"FunctionSetter"},{name:"format",label:"format",title:"设置日期格式，为数组时支持多格式匹配，展示以第一个为准",setters:"StringSetter",defaultValue:"YYYY-MM-DD"},{name:"dropdownClassName",label:"dropdownClassName",title:"额外的弹出日历 className",setters:"StringSetter"},{name:"getPopupContainer",label:"getPopupContainer",title:"定义浮层的容器，默认为 body 上新建 div",setters:"FunctionSetter"},{name:"inputReadOnly",label:"inputReadOnly",title:"设置输入框为只读（避免在移动设备上打开虚拟键盘）",setters:"BooleanSetter",defaultValue:!1},{name:"local",label:"local",title:"国际化配置",setters:"ObjectSetter"},{name:"mode",label:"mode",title:"日期面板的状态",setters:"SelectSetter",options:["time","date","month","year","decade"]},{name:"open",label:"open",title:"控制弹层是否展开",setters:"BooleanSetter"},{name:"picker",label:"picker",title:"设置选择器类型",setters:"SelectSetter",options:["date","week","month","quarter","year"],defaultValue:"date"},{name:"placeholder",label:"placeholder",title:"输入框提示文字",setters:["StringSetter","ArraySetter"]},{name:"placement",label:"placement",title:"选择框弹出的位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"popupStyle",label:"popupStyle",title:"额外的弹出日历样式",setters:"ObjectSetter",defaultValue:{}},{name:"presets",label:"presets",title:"预设时间范围快捷选择",setters:"ArraySetter"},{name:"size",label:"size",title:"输入框大小，large 高度为 40px，small 为 24px，默认是 32px",setters:"SelectSetter",options:["large","middle","small"]},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"valueFormat",label:"valueFormat",title:"可选，绑定值的格式，对 value、defaultValue、defaultPickerValue 起作用。不指定则绑定值为 dayjs 对象",setters:"StringSetter"},{name:"order",label:"order",title:"始末时间是否自动排序",setters:"BooleanSetter",defaultValue:!0},{name:"disabledTime",label:"disabledTime",title:"不可选择的时间",setters:"FunctionSetter"}],events:["change","openChange"],slots:["default","dateRender","nextIcon","prevIcon","suffixIcon","superNextIcon","superPrevIcon"]}],d=[];for(let r=0;r<20;r++)d.push({key:r.toString(),title:`content${r+1}`,description:`description of content${r+1}`,disabled:r%3<1});const K=[{name:"ATransfer",alias:"Transfer",label:"穿梭框",categoryId:"input",doc:"https://www.antdv.com/components/transfer-cn",props:[{name:"dataSource",label:"dataSource",title:"数据源，其中的数据将会被渲染到左边一栏中，targetKeys 中指定的除外",setters:"ArraySetter",defaultValue:[]},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"filterOption",label:"filterOption",title:"接收 inputValue option 两个参数，当 option 符合筛选条件时，应返回 true，反之则返回 false",setters:"FunctionSetter"},{name:"footer",label:"footer",title:"可以设置为一个 作用域插槽"},{name:"listStyle",label:"listStyle",title:"两个穿梭框的自定义样式",setters:"ObjectSetter"},{name:"locale",label:"locale",title:"各种语言",setters:"ObjectSetter",defaultValue:{itemUnit:"项",itemsUnit:"项",notFoundContent:"列表为空",searchPlaceholder:"请输入搜索内容"}},{name:"oneWay",label:"oneWay",title:"展示为单向样式",setters:"BooleanSetter",defaultValue:!1},{name:"operations",label:"operations",title:"操作文案集合，顺序从上至下",setters:"ArraySetter",defaultValue:[">","<"]},{name:"operationStyle",label:"operationStyle",title:"操作栏的自定义样式",setters:"ObjectSetter"},{name:"pagination",label:"pagination",title:"使用分页样式，自定义渲染列表下无效",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"render",label:"render",title:"每行数据渲染函数，该函数的入参为 dataSource 中的项，返回值为 element。或者返回一个普通对象，其中 label 字段为 element，value 字段为 title",setters:"FunctionSetter"},{name:"selectAllLabels",label:"selectAllLabels",title:"自定义顶部多选框标题的集合",setters:"FunctionSetter"},{name:"selectedKeys",label:"selectedKeys",title:"设置哪些项应该被选中",setters:"ArraySetter",defaultValue:[]},{name:"showSearch",label:"showSearch",title:"是否显示搜索框",setters:"BooleanSetter",defaultValue:!1},{name:"showSelectAll",label:"showSelectAll",title:"是否展示全选勾选框",setters:"BooleanSetter",defaultValue:!0},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"targetKeys",label:"targetKeys",title:"显示在右侧框数据的 key 集合",setters:"ArraySetter",defaultValue:[]},{name:"titles",label:"titles",title:"标题集合，顺序从左至右",setters:"ArraySetter",defaultValue:["",""]}],events:["change","scroll","search","selectChange","update:selectedKeys","update:targetKeys"],slots:["default","footer","render","selectAllLabels"],snippet:{props:{dataSource:d,titles:"['Source', 'Target']"}}}],$=[{name:"ATreeSelect",alias:"TreeSelect",label:"树选择",categoryId:"input",doc:"https://www.antdv.com/components/tree-select-cn",props:[{name:"allowClear",label:"allowClear",title:"显示清除按钮",setters:"BooleanSetter",defaultValue:!1},{name:"defaultValue",label:"defaultValue",title:"指定默认选中的条目",setters:["StringSetter","ArraySetter"]},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"popupClassName",label:"popupClassName",title:"下拉菜单的 className 属性",setters:"StringSetter"},{name:"dropdownMatchSelectWidth",label:"dropdownMatchSelectWidth",title:"下拉菜单和选择器同宽。默认将设置 min-width，当值小于选择框宽度时会被忽略。false 时会关闭虚拟滚动",setters:["BooleanSetter","NumberSetter"],defaultValue:!0},{name:"dropdownStyle",label:"dropdownStyle",title:"下拉菜单的样式",setters:"ObjectSetter"},{name:"fieldNames",label:"fieldNames",title:"替换 treeNode 中 label,value,children 字段为 treeData 中对应的字段",setters:"ObjectSetter",defaultValue:{children:"children",label:"title",value:"value"}},{name:"filterTreeNode",label:"filterTreeNode",title:"是否根据输入项进行筛选，默认用 treeNodeFilterProp 的值作为要筛选的 TreeNode 的属性值",setters:["BooleanSetter","FunctionSetter"]},{name:"getPopupContainer",label:"getPopupContainer",title:"菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位",setters:"FunctionSetter"},{name:"labelInValue",label:"labelInValue",title:"是否把每个选项的 label 包装到 value 中，会把 value 类型从 string 变为 {value: string, label: VNode, halfChecked(treeCheckStrictly 时有效): string[] } 的格式",setters:"BooleanSetter",defaultValue:!1},{name:"listHeight",label:"listHeight",title:"设置弹窗滚动高度",setters:"NumberSetter",defaultValue:256},{name:"loadData",label:"loadData",title:"异步加载数据",setters:"FunctionSetter"},{name:"maxTagCount",label:"maxTagCount",title:"最多显示多少个 tag",setters:"NumberSetter"},{name:"maxTagPlaceholder",label:"maxTagPlaceholder",title:"隐藏 tag 时显示的内容",setters:"FunctionSetter"},{name:"multiple",label:"multiple",title:"支持多选（当设置 treeCheckable 时自动变为 true）",setters:"BooleanSetter",defaultValue:!1},{name:"placeholder",label:"placeholder",title:"选择框默认文字",setters:"StringSetter"},{name:"placement",label:"placement",title:"选择框弹出的位置",setters:"SelectSetter",options:["bottomLeft","bottomRight","topLeft","topRight"],defaultValue:"bottomLeft"},{name:"replaceFields",label:"replaceFields",title:"替换 treeNode 中 label,value,key,children 字段为 treeData 中对应的字段",setters:"ObjectSetter",defaultValue:{children:"children",label:"title",key:"key",value:"value"}},{name:"searchPlaceholder",label:"searchPlaceholder",title:"搜索框默认文字",setters:"StringSetter"},{name:"searchValue",label:"searchValue",title:"搜索框的值，可以通过 search 事件获取用户输入",setters:"StringSetter"},{name:"showCheckedStrategy",label:"showCheckedStrategy",title:"定义选中项回填的方式。TreeSelect.SHOW_ALL: 显示所有选中节点(包括父节点). TreeSelect.SHOW_PARENT: 只显示父节点(当父节点下所有子节点都选中时). 默认只显示子节点",setters:"SelectSetter",options:["TreeSelect.SHOW_ALL","TreeSelect.SHOW_PARENT","TreeSelect.SHOW_CHILD"],defaultValue:"TreeSelect.SHOW_CHILD"},{name:"showSearch",label:"showSearch",title:"在下拉中显示搜索框(仅在单选模式下生效)",setters:"BooleanSetter",defaultValue:!1},{name:"size",label:"size",title:"选择框大小，可选 large small",setters:"SelectSetter",options:["larger","small"],defaultValue:"default"},{name:"status",label:"status",title:"设置校验状态",setters:"SelectSetter",options:["error","warning"]},{name:"treeCheckable",label:"treeCheckable",title:"显示 checkbox",setters:"BooleanSetter",defaultValue:!1},{name:"treeCheckStrictly",label:"treeCheckStrictly",title:"checkable 状态下节点选择完全受控（父子节点选中状态不再关联），会使得 labelInValue 强制为 true",setters:"BooleanSetter",defaultValue:!1},{name:"treeData",label:"treeData",title:"treeNodes 数据，如果设置则不需要手动构造 TreeNode 节点（value 在整个树范围内唯一）",setters:"ArraySetter",defaultValue:[]},{name:"treeDataSimpleMode",label:"treeDataSimpleMode",title:'使用简单格式的 treeData，具体设置参考可设置的类型 (此时 treeData 应变为这样的数据结构: [{id:1, pId:0, value:"1", label:"test1",...},...], pId 是父节点的 id)',setters:["BooleanSetter","ArraySetter"],defaultValue:!1},{name:"treeDefaultExpandAll",label:"treeDefaultExpandAll",title:"默认展开所有树节点",setters:"BooleanSetter",defaultValue:!1},{name:"treeDefaultExpandedKeys",label:"treeDefaultExpandedKeys",title:"默认展开的树节点",setters:"ArraySetter"},{name:"treeExpandedKeys",label:"treeExpandedKeys",title:"设置展开的树节点",setters:"ArraySetter"},{name:"treeIcon",label:"treeIcon",title:"是否展示 TreeNode title 前的图标，没有默认样式，如设置为 true，需要自行定义图标相关样式",setters:"BooleanSetter",defaultValue:!1},{name:"treeLine",label:"treeLine",title:"是否展示线条样式",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"treeLoadedKeys",label:"treeLoadedKeys",title:"（受控）已经加载的节点，需要配合 loadData 使用",setters:"ArraySetter",defaultValue:[]},{name:"treeNodeFilterProp",label:"treeNodeFilterProp",title:"输入项过滤对应的 treeNode 属性",setters:"StringSetter",defaultValue:"value"},{name:"treeNodeLabelProp",label:"treeNodeLabelProp",title:"作为显示的 prop 设置",setters:"StringSetter",defaultValue:"title"},{name:"value",label:"value",title:"指定当前选中的条目",setters:["StringSetter","ArraySetter"]},{name:"virtual",label:"virtual",title:"设置 false 时关闭虚拟滚动",setters:"BooleanSetter",defaultValue:!0}],events:["change","dropdownVisibleChange","search","select","treeExpand","update:searchValue","update:treeExpandedKeys","update:value"],slots:[{name:"default"},{name:"maxTagPlaceholder"},{name:"notFoundContent"},{name:"placeholder"},{name:"searchPlaceholder"},{name:"suffixIcon"},{name:"tagRender"},{name:"title"}],snippet:{props:{showSearch:!0,dropdownStyle:{maxHeight:"400px",overflow:"auto"},placeholder:"please select",treeDefaultExpandAll:!0,allowClear:!0,style:{width:"100%"},treeData:[{label:"root 1",value:"root 1",children:[{label:"parent 1",value:"parent 1",children:[{label:"parent 1-0",value:"parent 1-0",children:[{label:"my leaf",value:"leaf1"},{label:"your leaf",value:"leaf2"}]},{label:"parent 1-1",value:"parent 1-1"}]},{label:"parent 2",value:"parent 2"}]}],treeNodeFilterProp:"label"}}}],W=[{name:"AUpload",alias:"Upload",label:"上传",categoryId:"input",doc:"https://www.antdv.com/components/upload-cn",props:[{name:"accept",label:"accept",title:"接受上传的文件类型",setters:"StringSetter"},{name:"action",label:"action",title:"上传的地址",setters:["StringSetter","FunctionSetter"]},{name:"beforeUpload",label:"beforeUpload",title:"上传文件之前的钩子，参数为上传的文件，若返回 false 则停止上传。支持返回一个 Promise 对象，Promise 对象 reject 时则停止上传，resolve 时开始上传（ resolve 传入 File 或 Blob 对象则上传 resolve 传入对象）",setters:"FunctionSetter"},{name:"customRequest",label:"customRequest",title:"通过覆盖默认的上传行为，可以自定义自己的上传实现",setters:"FunctionSetter"},{name:"data",label:"data",title:"上传所需参数或返回上传参数的方法",setters:"FunctionSetter"},{name:"directory",label:"directory",title:"支持上传文件夹",setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter"},{name:"fileList",label:"fileList",title:"已经上传的文件列表（受控）",setters:"ArraySetter"},{name:"headers",label:"headers",title:"设置上传的请求头部",setters:"ObjectSetter"},{name:"isImageUrl",label:"isImageUrl",title:"自定义缩略图是否使用 <img /> 标签进行显示",setters:"FunctionSetter"},{name:"listType",label:"listType",title:"上传列表的内建样式，支持三种基本样式 text, picture 和 picture-card",setters:"SelectSetter",options:["text","picture","picture-card"],defaultValue:"text"},{name:"maxCount",label:"maxCount",title:"限制上传数量。当为 1 时，始终用最新上传的文件代替当前文件",setters:"NumberSetter"},{name:"method",label:"method",title:"上传请求的 http method",setters:"StringSetter",defaultValue:"post"},{name:"multiple",label:"multiple",title:"是否支持多选文件，ie10+ 支持。开启后按住 ctrl 可选择多个文件",setters:"BooleanSetter",defaultValue:!1},{name:"name",label:"name",title:"发到后台的文件参数名",setters:"StringSetter",defaultValue:"file"},{name:"openFileDialogOnClick",label:"openFileDialogOnClick",title:"点击打开文件对话框",setters:"BooleanSetter",defaultValue:!0},{name:"previewFile",label:"previewFile",title:"自定义文件预览逻辑",setters:"FunctionSetter"},{name:"progress",label:"progress",title:"自定义进度条样式",setters:"ObjectSetter",defaultValue:{strokeWidth:2,showInfo:!1}},{name:"removeIcon",label:"removeIcon",title:"自定义删除 icon",setters:'v-slot:iconRender="{file: UploadFile}"'},{name:"showUploadList",label:"showUploadList",title:"是否展示 uploadList, 可设为一个对象，用于单独设定 showPreviewIcon, showRemoveIcon 和 showDownloadIcon",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"supportServerRender",label:"supportServerRender",title:"服务端渲染时需要打开这个",setters:"BooleanSetter",defaultValue:!1},{name:"withCredentials",label:"withCredentials",title:"上传请求时是否携带 cookie",setters:"BooleanSetter",defaultValue:!1}],events:["change","download","drop","preview","reject","remove","modelValue:fileList"],slots:[{name:"default"},{name:"downloadIcon"},{name:"iconRender"},{name:"itemRender"},{name:"previewIcon"},{name:"removeIcon"}],snippet:{props:{fileList:[],name:"file",headers:{authorization:"authorization-text"},action:"https://www.mocky.io/v2/5cc8019d300000980a055e76"},children:[{name:"AButton",children:"Click to Upload"}]}}],G=[{name:"AAvatar",alias:"Avatar",label:"头像",categoryId:"data",doc:"https://www.antdv.com/components/avatar-cn",props:[{name:"alt",label:"alt",title:"图像无法显示时的替代文本",setters:"StringSetter"},{name:"crossOrigin",label:"crossOrigin",title:"cors 属性设置",setters:"SelectSetter",options:["anonymous","use-credentials",""]},{name:"draggable",label:"draggable",title:"图片是否允许拖动",setters:["BooleanSetter","SelectSetter"],options:["true","false"]},{name:"gap",label:"gap",title:"字符类型距离左右两侧边界单位像素",setters:"NumberSetter",defaultValue:4},{name:"loadError",label:"loadError",title:"图片加载失败的事件，返回 false 会关闭组件默认的 fallback 行为",setters:"FunctionSetter"},{name:"shape",label:"shape",title:"指定头像的形状",setters:"SelectSetter",options:["circle","square"],defaultValue:"circle"},{name:"size",label:"size",title:"设置头像的大小",setters:["NumberSetter","SelectSetter","ObjectSetter"],options:["large","small","default"],defaultValue:"default"},{name:"src",label:"src",title:"图片类头像的资源地址",setters:"StringSetter"},{name:"srcset",label:"srcset",title:"设置图片类头像响应式资源地址",setters:"StringSetter"}],slots:["default","icon"],snippet:{props:{size:"64"},children:[{name:"Template",slot:"icon",children:[{name:"UserOutlined"}]}]}},{name:"AAvatarGroup",alias:"Group",parent:"Avatar",label:"头像组",categoryId:"data",doc:"https://www.antdv.com/components/avatar-cn",props:[{name:"maxCount",label:"maxCount",title:"显示的最大头像个数",setters:"NumberSetter"},{name:"maxPopoverPlacement",label:"maxPopoverPlacement",title:"多余头像气泡弹出位置",setters:"SelectSetter",options:["top","bottom"],defaultValue:"top"},{name:"maxPopoverTrigger",label:"maxPopoverTrigger",title:"设置多余头像 Popover 的触发方式",setters:"SelectSetter",options:["hover","focus","click"],defaultValue:"hover"},{name:"maxStyle",label:"maxStyle",title:"多余头像样式",setters:"ObjectSetter"},{name:"size",label:"size",title:"设置头像的大小",setters:["NumberSetter","SelectSetter","ObjectSetter"],options:["large","small","default"],defaultValue:"default"},{name:"shape",label:"shape",title:"设置头像的形状",setters:"SelectSetter",options:["circle","square"],defaultValue:"circle"}],snippet:{children:[{name:"AAvatar",props:{size:64,src:"https://xsgames.co/randomusers/avatar.php?g=pixel&key=1"}},{name:"AAvatar",children:"CC",props:{size:64}},{name:"ATooltip",props:{title:"Ant User",placement:"top",children:[{name:"AAvatar",props:{style:{backgroundColor:"#87d068"},src:"https://xsgames.co/randomusers/avatar.php?g=pixel&key=1"}}]}}]}}],Y=[{name:"ABadge",alias:"Badge",label:"徽标数",categoryId:"data",doc:"https://www.antdv.com/components/badge-cn",props:[{name:"color",label:"color",title:"自定义小圆点的颜色",setters:"StringSetter"},{name:"count",label:"count",title:"展示的数字，大于 overflowCount 时显示为 ${overflowCount}+，为 0 时隐藏",setters:["NumberSetter","StringSetter"]},{name:"dot",label:"dot",title:"不展示数字，只有一个小红点",setters:"BooleanSetter",defaultValue:!1},{name:"numberStyle",label:"numberStyle",title:"设置状态点的样式",setters:"ObjectSetter",defaultValue:""},{name:"offset",label:"offset",title:"设置状态点的位置偏移，格式为 [x, y]",setters:"ArraySetters"},{name:"overflowCount",label:"overflowCount",title:"展示封顶的数字值",setters:"NumberSetter",defaultValue:99},{name:"showZero",label:"showZero",title:"当数值为 0 时，是否展示 Badge",setters:"BooleanSetter",defaultValue:!1},{name:"status",label:"status",title:"设置 Badge 为状态点",setters:"SelectSetter",options:["success","processing","default","error","warning"],defaultValue:""},{name:"text",label:"text",title:"在设置了 status 的前提下有效，设置状态点的文本",setters:"StringSetter",defaultValue:""},{name:"title",label:"title",title:"设置鼠标放在状态点上时显示的文字",setters:"StringSetter",defaultValue:"count"}],slots:["default","count"],snippet:{props:{count:5},children:[{name:"AAvatar",props:{shape:"square",size:"large"}}]}},{name:"ABadgeRibbon",alias:"Ribbon",parent:"Badge",label:"徽标缎带",categoryId:"data",doc:"https://www.antdv.com/components/badge-cn",props:[{name:"color",label:"color",title:"自定义缎带的颜色",setters:"StringSetter"},{name:"placement",label:"placement",title:"缎带的位置，start 和 end 随文字方向（RTL 或 LTR）变动",setters:"SelectSetter",options:["start","end"],defaultValue:"end"},{name:"text",label:"text",title:"缎带中填入的内容",setters:"StringSetter"}],slots:["default","text"],snippet:{props:{text:"Hippies"},children:[{name:"ACard",props:{title:"Pushes open the window",size:"small"},children:"and raises the spyglass."}]}}],U=[{name:"ACalendar",alias:"Calendar",label:"日历",categoryId:"data",doc:"https://www.antdv.com/components/calandar-cn",props:[{name:"disabledDate",label:"disabledDate",title:"不可选择的日期",setters:"FunctionSetter"},{name:"fullscreen",label:"fullscreen",title:"是否全屏显示",setters:"BooleanSetter",defaultValue:!0},{name:"locale",label:"locale",title:"国际化配置",setters:"ObjectSetter"},{name:"mode",label:"mode",title:"初始模式，month/year",setters:"SelectSetter",options:["month","year"],defaultValue:"month"},{name:"validRange",label:"validRange",title:"设置可以显示的日期",setters:"ArraySetter"},{name:"value",label:"value",title:"展示日期",setters:"FunctionSetter"},{name:"valueFormat",label:"valueFormat",title:"可选，绑定值的格式，对 value、defaultValue 起作用。不指定则绑定值为 dayjs 对象",setters:"StringSetter"}],events:["change","panelChange","select","update:value"],slots:["default","dateCellRender","dateFullCellRender","headerRender","monthCellRender","monthFullCellRender"]}],q=[{name:"ACard",alias:"Card",label:"卡片",categoryId:"data",doc:"https://www.antdv.com/components/card-cn",props:[{name:"activeTabKey",label:"activeTabKey",title:"当前激活页签的 key",setters:"StringSetter"},{name:"bodyStyle",label:"bodyStyle",title:"内容区域自定义样式",setters:"ObjectSetter"},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"defaultActiveTabKey",label:"defaultActiveTabKey",title:"初始化选中页签的 key，如果没有设置 activeTabKey",setters:"StringSetter",defaultValue:"第一个页签"},{name:"extra",label:"extra",title:"卡片右上角的操作区域",setters:"StringSetter"},{name:"headStyle",label:"headStyle",title:"自定义标题区域样式",setters:"ObjectSetter"},{name:"hoverable",label:"hoverable",title:"鼠标移过时可浮起",setters:"BooleanSetter",defaultValue:!1},{name:"loading",label:"loading",title:"当卡片内容还在加载中时，可以用 loading 展示一个占位",setters:"BooleanSetter",defaultValue:!1},{name:"size",label:"size",title:"card 的尺寸",setters:"SelectSetter",options:["default","small"],defaultValue:"default	"},{name:"tabList",label:"tabList",title:"页签标题列表, 可以通过 customTab(v3.0) 插槽自定义 tab",setters:"ArraySetter"},{name:"title",label:"title",title:"卡片标题",setters:"StringSetter"},{name:"type",label:"type",title:"卡片类型，可设置为 inner 或 不设置",setters:"StringSetter"}],events:["tabChange"],slots:["default","actions","cover","customTab","extra","tabBarExtraContent","title"],snippet:{props:{title:"Default size card",style:{width:"300px"}},slot:"extra",children:[{name:"a",slot:{name:"extra"},children:"more"},{name:"component",props:{is:"p"},children:"card content"},{name:"component",props:{is:"p"},children:"card content"},{name:"component",props:{is:"p"},children:"card content"}]}},{name:"ACardGrid",alias:"Grid",parent:"Card",label:"网格型内嵌卡片",categoryId:"data",doc:"https://www.antdv.com/components/card-cn",snippet:{props:{style:{width:"25%",textAline:"center"}},children:"content",directives:[{name:"vFor",value:{type:"JSExpression",value:"8"}}]}},{name:"ACardMeta",alias:"Meta",parent:"Card",label:"meta卡片",categoryId:"data",doc:"https://www.antdv.com/components/card-cn",props:[{name:"description",label:"description",title:"描述内容",setters:"StringSetter"},{name:"title",label:"title",title:"标题内容",setters:"StringSetter"}],slots:["avatar","description","title"],snippet:{props:{title:"Europe Street beat"},slot:"description",children:[{name:"span",slot:{name:"description"},children:"www.instagram.com"}]}}],J=[{name:"ACarousel",alias:"Carousel",label:"走马灯",categoryId:"data",doc:"https://www.antdv.com/components/carousel-cn",props:[{name:"autoplay",label:"autoplay",title:"是否自动切换",setters:"BooleanSetter",defaultValue:!1},{name:"dotPosition",label:"dotPosition",title:"面板指示点位置，可选 top bottom left right",setters:"SelectSetter",options:["top","bottom","left","right"],defaultValue:"bottom"},{name:"dots",label:"dots",title:"是否显示面板指示点",setters:"BooleanSetter",defaultValue:!0},{name:"dotsClass",label:"dotsClass",title:"面板指示点类名",setters:"StringSetter",defaultValue:"slick-dots"},{name:"easing",label:"easing",title:"动画效果",setters:"StringSetter",defaultValue:"linear"},{name:"effect",label:"effect",title:"动画效果函数",setters:"SelectSetter",options:["scrollx","fade"],defaultValue:"scrollx"},{name:"afterChange",label:"afterChange",title:"切换面板的回调",setters:"FunctionSetter"},{name:"beforeChange",label:"beforeChange",title:"切换面板的回调",setters:"FunctionSetter"},{name:"arrows",label:"arrows",setters:"BooleanSetter",defaultValue:!1}],slots:[{name:"default"},{name:"customPaging"},{name:"prevArrow"},{name:"nextArrow"}],snippet:{props:{autoplay:!0},children:[{name:"div",children:[{name:"h1",children:"1",props:{style:{backgroundColor:"#364d79",height:"200px",display:"flex",justifyContent:"center",alignItems:"center"}}}]},{name:"div",children:[{name:"h1",children:"2",props:{style:{backgroundColor:"#364d79",height:"200px",display:"flex",justifyContent:"center",alignItems:"center"}}}]},{name:"div",children:[{name:"h1",children:"3",props:{style:{backgroundColor:"#364d79",height:"200px",display:"flex",justifyContent:"center",alignItems:"center"}}}]},{name:"div",children:[{name:"h1",children:"4",props:{style:{backgroundColor:"#364d79",height:"200px",display:"flex",justifyContent:"center",alignItems:"center"}}}]}]}}],_=[{name:"ACollapse",alias:"Collapse",label:"折叠面板",categoryId:"data",doc:"https://www.antdv.com/components/collapse-cn",props:[{name:"accordion",label:"accordion",title:"手风琴模式",setters:"BooleanSetter",defaultValue:!1},{name:"activeKey",label:"activeKey",title:"当前激活 tab 面板的 key",setters:["StringSetter","NumberSetter","ArraySetter"]},{name:"bordered",label:"bordered",title:"带边框风格的折叠面板",setters:"BooleanSetter",defaultValue:!0},{name:"collapsible",label:"collapsible",title:"所有子面板是否可折叠或指定可折叠触发区域",setters:"SelectSetter",options:["header","icon","disabled"]},{name:"destroyInactivePanel",label:"destroyInactivePanel",title:"销毁折叠隐藏的面板",setters:"BooleanSetter",defaultValue:!1},{name:"expandIcon",label:"expandIcon",title:"自定义切换图标",setters:"FunctionSetter"},{name:"expandIconPosition",label:"expandIconPosition",title:"设置图标位置",setters:"SelectSetter",options:["start","end"]},{name:"ghost",label:"ghost",title:"使折叠面板透明且无边框",setters:"BooleanSetter",defaultValue:!1},{name:"defaultActiveKey",label:"defaultActiveKey",title:"默认激活的 Tab 面板",setters:"StringSetter"}],events:["change","update:activeKey"],slots:[{name:"default"},{name:"expandIcon"}],snippet:{props:{activeKey:"1"},children:[{name:"ACollapsePanel",props:{key:"1",header:"This is panel header 1"},children:[{name:"p",children:"A dog is a type of domesticated animal.Known for its loyalty and faithfulness,it can be found as a welcome guest in many households across the world."}]},{name:"ACollapsePanel",props:{key:"2",header:"This is panel header 2"},children:[{name:"p",children:"A dog is a type of domesticated animal.Known for its loyalty and faithfulness,it can be found as a welcome guest in many households across the world."}]},{name:"ACollapsePanel",props:{key:"3",header:"This is panel header 3"},children:[{name:"p",children:"A dog is a type of domesticated animal.Known for its loyalty and faithfulness,it can be found as a welcome guest in many households across the world."}]}]}},{name:"ACollapsePanel",alias:"Panel",parent:"Collapse",label:"折叠面板项",categoryId:"data",doc:"https://www.antdv.com/components/collapse-cn",props:[{name:"collapsible",label:"collapsible",title:"是否可折叠或指定可折叠触发区域",setters:"SelectSetter",options:["header","disabled"]},{name:"forceRender",label:"forceRender",title:"被隐藏时是否渲染 DOM 结构",setters:"BooleanSetter",defaultValue:!1},{name:"header",label:"header",title:"面板头内容",setters:"StringSetter"},{name:"key",label:"key",title:"对应 activeKey",setters:["StringSetter","NumberSetter"]},{name:"showArrow",label:"shoArrow",title:"是否展示当前面板的箭头",setters:"BooleanSetter",defaultValue:!0}],slots:["default","extra","header"],snippet:{props:{header:"This is panel header"},children:[{name:"p",children:"A dog is a type of domesticated animal.Known for its loyalty and faithfulness,it can be found as a welcome guest in many households across the world."}]}}],Q=[{name:"AComment",alias:"Comment",label:"评论",categoryId:"data",doc:"https://www.antdv.com/components/comment-cn",props:[{name:"actions",label:"actions",title:"在评论内容下面呈现的操作项列表",setters:"ArraySetter"},{name:"author",label:"author",title:"要显示为注释作者的元素",setters:"StringSetter"},{name:"avatar",label:"avatar",title:"要显示为评论头像的元素 - 通常是 antd Avatar 或者 src",setters:"StringSetter"},{name:"content",label:"content",title:"评论的主要内容",setters:"StringSetter"},{name:"datetime",label:"datetime",title:"展示时间描述",setters:"StringSetter"}],slots:["default","actions","author","avatar","content","datetime"],snippet:{slot:"actions",children:[{name:"p",slot:{name:"content"},children:"We supply a series of design principles, practical patterns and high quality design resources (Sketch and Axure), to help people create their product prototypes beautifully and efficiently."},{name:"a",slot:{name:"author"},children:"Han Solo"},{name:"AAvatar",slot:{name:"avatar"},props:{src:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",alt:"Han Solo"}},{name:"ATooltip",slot:{name:"datetime"},props:{title:'dayjs().format("YYYY-MM-DD HH:mm:ss")'},children:[{name:"span",children:"几秒前"}]}]}}],Z=[{name:"ADescriptions",alias:"Descriptions",label:"描述列表",categoryId:"data",doc:"https://www.antdv.com/components/descriptions-cn",props:[{name:"bordered",label:"bordered",title:"是否展示边框",setters:"BooleanSetter",defaultValue:!1},{name:"colon",label:"colon",title:"配置 Descriptions.Item 的 colon 的默认值",setters:"BooleanSetter",defaultValue:!0},{name:"column",label:"column",title:"一行的 DescriptionItems 数量，可以写成像素值或支持响应式的对象写法 { xs: 8, sm: 16, md: 24}",setters:"NumberSetter",defaultValue:3},{name:"contentStyle",label:"contentStyle",title:"自定义内容样式",setters:"ObjectSetter"},{name:"extra",label:"extra",title:"描述列表的操作区域，显示在右上方",setters:"StringSetter"},{name:"labelStyle",label:"labelStyle",title:"自定义标签样式",setters:"ObjectSetter"},{name:"layout",label:"layout",title:"描述布局",setters:"SelectSetter",options:["horizontal","vertical"],defaultValue:"horizontal"},{name:"size",label:"size",title:"设置列表的大小。可以设置为 middle 、small, 或不填（只有设置 bordered={true} 生效）",setters:"SelectSetter",options:["default","middle","small"],defaultValue:"default"},{name:"title",label:"title",title:"描述列表的标题，显示在最顶部",setters:"StringSetter"}],slots:["default","extra","title"],snippet:{props:{title:"User Info"},children:[{name:"ADescriptionsItem",props:{label:"UserName"},children:"chenshuishui"},{name:"ADescriptionsItem",props:{label:"Telephone"},children:"18912345678"},{name:"ADescriptionsItem",props:{label:"Live"},children:"HongKong"},{name:"ADescriptionsItem",props:{label:"Remark"},children:"empty"},{name:"ADescriptionsItem",props:{label:"Address"},children:"弥敦道9号, HongKong, China"}]}},{name:"ADescriptionsItem",alias:"DescriptionsItem",label:"描述列表项",categoryId:"data",doc:"https://www.antdv.com/components/descriptions-cn",props:[{name:"contentStyle",label:"contentStyle",title:"自定义内容样式",setters:"ObjectSetter"},{name:"label",label:"label",title:"内容的描述",setters:"StringSetter"},{name:"labelStyle",label:"labelStyle",title:"自定义标签样式",setters:"ObjectSetter"},{name:"span",label:"span",title:"包含列的数量",setters:"NumberSetter",defaultValue:1}],slots:["default","label"],snippet:{props:{label:"Address"},children:"弥敦道9号, HongKong, China"}}],X=[{name:"AEmpty",alias:"Empty",label:"空状态",categoryId:"data",doc:"https://www.antdv.com/components/empty-cn",props:[{name:"description",label:"description",title:"自定义描述内容",setters:"StringSetter"},{name:"image",label:"image",title:"设置显示图片，为 string 时表示自定义图片地址",setters:"StringSetter",defaultValue:!1},{name:"imageStyle",label:"imageStyle",title:"图片样式",setters:"ObjectSetter"}],slots:["default","description","image"]}],ee=[{name:"AImage",alias:"Image",label:"图片",categoryId:"data",doc:"https://www.antdv.com/components/image-cn",props:[{name:"alt",label:"alt",title:"图像描述",setters:"StringSetter"},{name:"fallback",label:"fallback",title:"加载失败容错地址",setters:"StringSetter"},{name:"height",label:"height",title:"图像高度",setters:["NumberSetter","StringSetter"]},{name:"placeholder",label:"placeholder",title:"加载占位, 为 true 时使用默认占位",setters:"BooleanSetter"},{name:"preview",label:"preview",title:"预览参数，为 false 时禁用",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"src",label:"src",title:"图片地址",setters:"StringSetter"},{name:"previewMask",label:"previewMask",title:"自定义 mask",setters:["StringSetter","FunctionSetter"]},{name:"width",label:"width",title:"图像宽度",setters:["NumberSetter","StringSetter"]}],events:["error"],slots:["default","placeholder","previewMask"],snippet:{props:{width:"200px",src:"https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png"}}},{name:"AImagePreviewGroup",alias:"ImagePreviewGroup",label:"预览图片组",categoryId:"data",doc:"https://www.antdv.com/components/image-cn",props:[{name:"preview",label:"preview",title:"预览参数，为 false 时禁用",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0}],snippet:{children:[{name:"AImage",props:{src:"https://gw.alipayobjects.com/zos/antfincdn/LlvErxo8H9/photo-1503185912284-5271ff81b9a8.webp"}},{name:"AImage",props:{src:"https://gw.alipayobjects.com/zos/antfincdn/cV16ZqzMjW/photo-1473091540282-9b846e7965e3.webp"}},{name:"AImage",props:{src:"https://gw.alipayobjects.com/zos/antfincdn/x43I27A55%26/photo-1438109491414-7198515b166b.webp"}}]}}],te=[{name:"AList",alias:"List",label:"列表",categoryId:"data",doc:"https://www.antdv.com/components/list-cn",props:[{name:"bordered",label:"bordered",title:"是否展示边框",setters:"BooleanSetter",defaultValue:!1},{name:"dataSource",label:"dataSource",title:"列表数据源",setters:"ArraySetter"},{name:"footer",label:"footer",title:"列表底部",setters:"StringSetter"},{name:"grid",label:"grid",title:"列表栅格配置",setters:"ObjectSetter"},{name:"header",label:"header",title:"列表头部",setters:"StringSetter"},{name:"itemLayout",label:"itemLayout",title:"设置 List.Item 布局, 设置成 vertical 则竖直样式显示, 默认横排",setters:"StringSetter"},{name:"loading",label:"loading",title:"当卡片内容还在加载中时，可以用 loading 展示一个占位",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"loadMore",label:"loadMore",title:"加载更多",setters:"StringSetter"},{name:"locale",label:"locale",title:"默认文案设置，目前包括空数据文案",setters:"ObjectSetter",defaultValue:{emptyText:"暂无数据"}},{name:"pagination",label:"pagination",title:"对应的 pagination 配置, 设置 false 不显示",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"renderItem",label:"renderItem",title:'自定义Item函数，也可使用 #renderItem="{item, index}"',setters:"FunctionSetter"},{name:"rowKey",label:"rowKey",title:"各项 key 的取值，可以是字符串或一个函数",setters:"FunctionSetter"},{name:"size",label:"size",title:"list 的尺寸",setters:"SelectSetter",options:["default","middle","small"],defaultValue:"default"},{name:"split",label:"split",title:"是否展示分割线",setters:"BooleanSetter",defaultValue:!0}],slots:[{name:"default"},{name:"footer"},{name:"header"},{name:"loadMore"},{name:"renderItem",params:["item","index"]}],snippet:{props:{itemLayout:"horizontal",dataSource:[{title:"Ant Design Title 1"},{title:"Ant Design Title 2"},{title:"Ant Design Title 3"},{title:"Ant Design Title 4"}]},slot:"renderItem",children:[{name:"span",slot:{name:"renderItem",params:["item","index"]},children:[{name:"AListItem",children:[{name:"AListItemMeta",props:{description:"Ant Design, a design language for background applications, is refined by Ant UED Team"},children:[{name:"a",slot:{name:"title"},props:{href:"https://www.antdv.com/"}},{name:"AAvatar",slot:{name:"avatar"},props:{src:"https://joeschmoe.io/api/v1/random"}}]}]}]}]}},{name:"AListItem",alias:"Item",parent:"List",label:"列表项",categoryId:"data",doc:"https://www.antdv.com/components/list-cn",props:[{name:"actions",label:"actions",title:"列表操作组，根据 itemLayout 的不同, 位置在卡片底部或者最右侧",setters:"StringSetter"},{name:"extra",label:"extra",title:"额外内容, 通常用在 itemLayout 为 vertical 的情况下, 展示右侧内容; horizontal 展示在列表元素最右侧",setters:"StringSetter"}],slots:["default","actions","extra"],snippet:{children:[{name:"AListItemMeta",props:{description:"Ant Design, a design language for background applications, is refined by Ant UED Team"},children:[{name:"a",slot:{name:"title"},props:{href:"https://www.antdv.com/"},children:"itemTitle"},{name:"AAvatar",slot:{name:"avatar"},props:{src:"https://joeschmoe.io/api/v1/random"}}]}]}},{name:"AListItemMeta",alias:"Meta",parent:"ListItem",label:"列表项meta",categoryId:"data",doc:"https://www.antdv.com/components/list-cn",props:[{name:"avatar",label:"avatar",title:"列表元素的图标",setters:"StringSetter"},{name:"description",label:"description",title:"列表元素的描述内容",setters:"StringSetter"},{name:"title",label:"title",title:"列表元素的标题",setters:"StringSetter"}],slots:["default","avatar","description","title"],snippet:{props:{description:"Ant Design, a design language for background applications, is refined by Ant UED Team"}}}],le=[{name:"APopover",alias:"Popover",label:"气泡卡片",categoryId:"data",doc:"https://www.antdv.com/components/popover-cn",props:[{name:"content",label:"content",title:"卡片内容",setters:"StringSetter"},{name:"title",label:"title",title:"卡片标题",setters:"StringSetter"},{name:"align",label:"align",title:"该值将合并到 placement 的配置中，设置参考 dom-align",setters:"ObjectSetter"},{name:"arrowPointAtCenter",label:"arrowPointAtCenter",title:"箭头是否指向目标元素中心",setters:"BooleanSetter",defaultValue:!1},{name:"autoAdjustOverflow",label:"autoAdjustOverflow",title:"气泡被遮挡时自动调整位置",setters:"BooleanSetter",defaultValue:!0},{name:"color",label:"color",title:"背景颜色",setters:"StringSetter"},{name:"destroyTooltipOnHide",label:"destroyTooltipOnHide",title:"隐藏后是否销毁 tooltip",setters:"BooleanSetter",defaultValue:!1},{name:"getPopupContainer",label:"getPopupContainer",title:"浮层渲染父节点，默认渲染到 body 上",setters:"FunctionSetter"},{name:"mouseEnterDelay",label:"mouseEnterDelay",title:"鼠标移入后延时多少才显示 Tooltip，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"mouseLeaveDelayy",label:"mouseLeaveDelayy",title:"鼠标移出后延时多少才显示 Tooltip，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"overlayClassName",label:"overlayClassName",title:"卡片类名",setters:"StringSetter"},{name:"overlayStyle",label:"overlayStyle",title:"卡片样式",setters:"ObjectSetter"},{name:"overlayInnerStyle",label:"overlayInnerStyle",title:"卡片内容区域样式",setters:"ObjectSetter"},{name:"placement",label:"placement",title:"气泡框位置",setters:"SelectSetter",options:["top","left","right","bottom","topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"],defaultValue:"top"},{name:"trigger",label:"trigger",title:"触发行为",setters:"SelectSetter",options:["hover","focus","click","contextmenu"],defaultValue:"hover"},{name:"open",label:"open",title:"用于手动控制浮层显隐",setters:"BooleanSetter",defaultValue:!1}],slots:["default","content","title"],events:["update: open","openChange"],snippet:{props:{title:"Title"},slot:"content",children:[{name:"p",slot:{name:"content"},children:"content"},{name:"p",slot:{name:"content"},children:"content"},{name:"AButton",props:{type:"primary"},children:"Hover me"}]}}],ae=[{name:"AQRCode",alias:"QRCode",label:"二维码",categoryId:"data",doc:"https://www.antdv.com/components/qrcode-cn",props:[{name:"value",label:"value",title:"扫描后的地址",setters:"StringSetter"},{name:"type",label:"type",title:"渲染类型",setters:"SelectSetter",options:["canvas","svg"],defaultValue:"canvas"},{name:"icon",label:"icon",title:"二维码中图片的地址（目前只支持图片地址）",setters:"StringSetter"},{name:"size",label:"size",title:"二维码大小",setters:"NumberSetter",defaultValue:160},{name:"iconSize",label:"iconSize",title:"二维码中图片的大小",setters:"NumberSetter",defaultValue:40},{name:"color",label:"color",title:"二维码颜色",setters:"StringSetter",defaultValue:"#000"},{name:"bgColor",label:"bgColor",title:"二维码背景颜色",setters:"StringSetter",defaultValue:"transparent"},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0},{name:"errorLevel",label:"errorLevel",title:"二维码纠错等级",setters:"SelectSetter",options:["L","M","Q","H"],defaultValue:"M"},{name:"status",label:"status",title:"二维码状态",setters:"SelectSetter",options:["active","expired","loading","scanned"],defaultValue:"active"}],events:["refresh"],snippet:{props:{value:"哈哈哈哈"}}}],re=[{name:"ASegmented",alias:"Segmented",label:"分段控制器",categoryId:"data",doc:"https://www.antdv.com/components/segmented-cn",props:[{name:"block",label:"block",title:"将宽度调整为父元素宽度的选项",setters:"BooleanSetter"},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1},{name:"options",label:"options",title:"数据化配置选项内容",setters:"ArraySetter",defaultValue:[]},{name:"size",label:"size",title:"控件尺寸",setters:"SelectSetter",options:["large","middle","small"]},{name:"value",label:"value",title:"当前选中的值",setters:["StringSetter","NumberSetter"]}],events:["change","update:value"],slots:["default","label"],snippet:{props:{value:"Daily",options:["Daily","Weekly","Monthly","Quarterly","Yearly"]}}}],ne=[{name:"AStatistic",alias:"Statistic",label:"统计数值",categoryId:"data",doc:"https://www.antdv.com/components/button-cn",props:[{name:"decimalSeparator",label:"decimalSeparator",title:"设置小数点",setters:"StringSetter",defaultValue:"."},{name:"formatter",label:"formatter",title:"自定义数值展示",setters:"FunctionSetter"},{name:"groupSeparator",label:"groupSeparator",title:"设置千分位标识符",setters:"StringSetter",defaultValue:","},{name:"precision",label:"precision",title:"数值精度",setters:"NumberSetter"},{name:"prefix",label:"prefix",title:"设置数值的前缀",setters:"StringSetter"},{name:"suffix",label:"suffix",title:"设置数值的后缀",setters:"StringSetter"},{name:"title",label:"title",title:"数值的标题",setters:"StringSetter"},{name:"value",label:"value",title:"数值内容",setters:["StringSetter","NumberSetter"]},{name:"valueStyle",label:"valueStyle",title:"设置数值的样式",setters:"ObjectSetter"}],slots:["default","formatter","prefix","suffix","title"],snippet:{props:{title:"Active Users",value:Date.now()+1e3*60*60*20*2}}},{name:"AStatisticCountdown",alias:"Countdown",parent:"Statistic",label:"统计倒计数值",categoryId:"data",doc:"https://www.antdv.com/components/button-cn",props:[{name:"format",label:"format",title:"格式化倒计时展示，参考 dayjs",setters:"StringSetter",defaultValue:"HH:mm:ss"},{name:"prefix",label:"prefix",title:"设置数值的前缀",setters:"StringSetter"},{name:"suffix",label:"suffix",title:"设置数值的后缀",setters:"StringSetter"},{name:"title",label:"title",title:"数值的标题",setters:"StringSetter"},{name:"value",label:"value",title:"数值内容",setters:["NumberSetter","FunctionSetter"]},{name:"valueStyle",label:"valueStyle",title:"设置数值的样式",setters:"ObjectSetter"}],events:["finish"],slots:["default","prefix","suffix","title"],snippet:{props:{title:"countDown",value:Date.now()+1e3*60*60*20*2}}}],se=[{name:"ATable",alias:"Table",label:"表格",categoryId:"data",doc:"https://www.antdv.com/components/table-cn",props:[{name:"bordered",label:"bordered",title:"是否展示外边框和列边框",setters:"BooleanSetter",defaultValue:!1},{name:"childrenColumnName",label:"childrenColumnName",title:"指定树形结构的列名",setters:"StringSetter",defaultValue:"children"},{name:"columns",label:"columns",title:"表格列的配置描述",setters:"ArraySetter"},{name:"components",label:"components",title:"覆盖默认的 table 元素",setters:"ObjectSetter"},{name:"customHeaderRow",label:"customHeaderRow",title:"设置头部行属性",setters:"FunctionSetter"},{name:"customRow",label:"customRow",title:"设置行属性",setters:"FunctionSetter"},{name:"dataSource",label:"dataSource",title:"数据数组",setters:"ArraySetter"},{name:"defaultExpandAllRows",label:"defaultExpandAllRows",title:"初始时，是否展开所有行",setters:"BooleanSetter",defaultValue:!1},{name:"defaultExpandedRowKeys",label:"defaultExpandedRowKeys",title:"默认展开的行",setters:"ArraySetter"},{name:"expandedRowKeys",label:"expandedRowKeys",title:"展开的行，控制属性",setters:"ArraySetter"},{name:"expandedRowRender",label:"expandedRowRender",title:"额外的展开行",setters:"FunctionSetter"},{name:"expandFixed",label:"expandFixed",title:"控制展开图标是否固定，可选 true left right",setters:["BooleanSetter","StringSetter"],defaultValue:!1},{name:"expandIcon",label:"expandIcon",title:"自定义展开图标",setters:"FunctionSetter"},{name:"expandRowByClick",label:"expandRowByClick",title:"通过点击行来展开子行",setters:"BooleanSetter",defaultValue:!1},{name:"footer",label:"footer",title:"表格尾部",setters:"FunctionSetter"},{name:"getPopupContainer",label:"getPopupContainer",title:"设置表格内各类浮层的渲染节点，如筛选菜单",setters:"FunctionSetter"},{name:"indentSize",label:"indentSize",title:"展示树形数据时，每层缩进的宽度，以 px 为单位",setters:"NumberSetter",defaultValue:15},{name:"loading",label:"loading",title:"页面是否加载中",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"locale",label:"locale",title:"默认文案设置，目前包括排序、过滤、空数据文案",setters:"ObjectSetter",defaultValue:{filterConfirm:"确定",filterReset:"重置",emptyText:"暂无数据"}},{name:"pagination",label:"pagination",title:"分页器，参考配置项或 pagination文档，设为 false 时不展示和进行分页",setters:["ObjectSetter","BooleanSetter"]},{name:"rowClassName",label:"rowClassName",title:"表格行的类名",setters:"FunctionSetter"},{name:"rowExpandable",label:"rowExpandable",title:"设置是否允许行展开",setters:"FunctionSetter"},{name:"rowKey",label:"rowKey",title:"表格行 key 的取值，可以是字符串或一个函数",setters:["StringSetter","FunctionSetter"],defaultValue:"key"},{name:"rowSelection",label:"rowSelection",title:"列表项是否可选择",setters:"ObjectSetter",defaultValue:null},{name:"scroll",label:"scroll",title:"表格是否可滚动，也可以指定滚动区域的宽、高",setters:"ObjectSetter"},{name:"showExpandColumn",label:"showExpandColumn",title:"设置是否展示行展开列",setters:"BooleanSetter",defaultValue:!0},{name:"showHeader",label:"showHeader",title:"是否显示表头",setters:"BooleanSetter",defaultValue:!0},{name:"showSorterTooltip",label:"showSorterTooltip",title:"表头是否显示下一次排序的 tooltip 提示。当参数类型为对象时，将被设置为 Tooltip 的属性",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"size",label:"size",title:"表格大小",setters:"SelectSetter",options:["large","middle","small"],defaultValue:"large"},{name:"sortDirections",label:"sortDirections",title:"支持的排序方式，取值为 ascend descend",setters:"ArraySetter",defaultValue:["ascend","descend"]},{name:"sticky",label:"sticky",title:"设置粘性头部和滚动条",setters:["BooleanSetter","ObjectSetter"]},{name:"tableLayout",label:"tableLayout",title:"表格元素的 table-layout 属性，设为 fixed 表示内容不会影响列的布局",setters:"SelectSetter",options:["-","auto","fixed"]},{name:"title",label:"title",title:"表格标题",setters:"FunctionSetter"},{name:"transformCellText",label:"transformCellText",title:"数据渲染前可以再次改变，一般用于空数据的默认配置，可以通过 ConfigProvider 全局统一配置",setters:"FunctionSetter"}],events:["change","expand","expandedRowsChange","resizeColumn","update:expandedRowKeys"],slots:[{name:"default"},{name:"bodyCell",params:["text","record","index","column"]},{name:"customFilterDropdown"},{name:"customFilterIcon",params:["filtered","column"]},{name:"emptyText"},{name:"expandedRowRender",params:["record","index","indent","expanded"]},{name:"expandColumnTitle"},{name:"expandIcon",params:["props"]},{name:"expandIcon",params:["currentPageData"]},{name:"headerCell",params:["title","column"]},{name:"summary"},{name:"title",params:["currentPageData"]},{name:"transformCellText"}],snippet:{props:{columns:[{title:"Name",dataIndex:"name",key:"name"},{title:"Age",dataIndex:"age",key:"age"},{title:"Address",dataIndex:"address",key:"address"},{title:"Tags",key:"tags",dataIndex:"tags"},{title:"Action",key:"action"}],dataSource:[{key:"1",name:"John Brown",age:32,address:"New York No. 1 Lake Park",tags:["nice","developer"]},{key:"2",name:"Jim Green",age:42,address:"London No. 1 Lake Park",tags:["loser"]},{key:"3",name:"Joe Black",age:32,address:"Sidney No. 1 Lake Park",tags:["cool","teacher"]}]}}}],oe=[{name:"ATabs",alias:"Tabs",childIncludes:["ATabPane"],label:"标签页",categoryId:"data",doc:"https://www.antdv.com/components/tabs-cn",props:[{name:"activeKey",label:"activeKey",title:"当前激活 tab 面板的 key",setters:"StringSetter"},{name:"animated",label:"animated",title:'是否使用动画切换 Tabs，在 tabPosition="top" | "bottom" 时有效',setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"centered",label:"centered",title:"标签居中展示",setters:"BooleanSetter",defaultValue:!1},{name:"destroyInactiveTabPane",label:"destroyInactiveTabPane",title:"被隐藏时是否销毁 DOM 结构",setters:"BooleanSetter",defaultValue:!1},{name:"hideAdd",label:"hideAdd",title:'是否隐藏加号图标，在 type="editable-card" 时有效',setters:"BooleanSetter",defaultValue:!1},{name:"size",label:"size",title:"大小，提供 large middle 和 small 三种大小",setters:"SelectSetter",options:["large","middle","small"],defaultValue:"middle"},{name:"tabBarGutter",label:"tabBarGutter",title:"tabs 之间的间隙",setters:"NumberSetter"},{name:"tabBarStyle",label:"tabBarStyle",title:"tab bar 的样式对象",setters:"ObjectSetter"},{name:"tabPosition",label:"tabPosition",title:"页签位置，可选值有 top right bottom left",setters:"SelectSetter",options:["top","right","bottom","left"],defaultValue:"top"},{name:"type",label:"type",title:"页签的基本样式，可选 line、card editable-card 类型",setters:"SelectSetter",options:["line","card","editable-card"],defaultValue:"line"}],events:["change","edit","tabClick","tabScroll","update:activeKey"],slots:["default","addIcon","leftExtra","moreIcon","renderTabBar","rightExtra"],snippet:{props:{activeKey:"1",centered:!0},children:[{name:"ATabPane",props:{key:"1",tab:"Tab 1"},children:"Content of Tab Pane 1"},{name:"ATabPane",props:{key:"2",tab:"Tab 2"},children:"Content of Tab Pane 2"},{name:"ATabPane",props:{key:"3",tab:"Tab 3"},children:"Content of Tab Pane 3"}]}},{name:"ATabPane",alias:"TabPane",parent:"Tabs",label:"标签页签",categoryId:"data",doc:"https://www.antdv.com/components/tabs-cn",props:[{name:"forceRender",label:"forceRender",title:"被隐藏时是否渲染 DOM 结构",setters:"BooleanSetter",defaultValue:!1},{name:"key",label:"key",title:"对应 activeKey",setters:"StringSetter"},{name:"tab",label:"tab",title:"选项卡头显示文字",setters:"StringSetter"},{name:"disabled",label:"disabled",title:"是否禁用",setters:"BooleanSetter",defaultValue:!1}],slots:["default","closeIcon","tab"],snippet:{props:{key:"1",tab:"Tab 1"},children:"Content of Tab Pane 1"}}],ie=[{name:"ATag",alias:"Tag",label:"标签",categoryId:"data",doc:"https://www.antdv.com/components/tag-cn",props:[{name:"closable",label:"closable",title:"标签是否可以关闭",setters:"BooleanSetter",defaultValue:!1},{name:"color",label:"color",title:"标签色",setters:["StringSetter","ColorSetter"]},{name:"bordered",label:"bordered",title:"是否有边框",setters:"BooleanSetter",defaultValue:!0}],events:["close"],slots:["default","closeIcon","icon"],snippet:{children:"Tag"}},{name:"ACheckableTag",alias:"CheckableTag",parent:"Tag",label:"可选择标签",categoryId:"data",doc:"https://www.antdv.com/components/tag-cn",props:[{name:"checked",label:"checked",title:"设置标签的选中状态",setters:"BooleanSetter",defaultValue:!1}],events:["change","update:checked"],snippet:{children:"checkTag",props:{checked:!0},directives:[{name:"vFor",value:{type:"JSExpression",value:"4"}}]}}],de=[{name:"ATimeline",alias:"Timeline",label:"时间轴",categoryId:"data",doc:"https://www.antdv.com/components/timeline-cn",props:[{name:"mode",label:"mode",title:"通过设置 mode 可以改变时间轴和内容的相对位置",setters:"SelectSetter",options:["left","alternate","right"]},{name:"pending",label:"pending",title:"指定最后一个幽灵节点是否存在或内容",setters:["BooleanSetter","StringSetter"],defaultValue:!1},{name:"pendingDot",label:"pendingDot",title:"当最后一个幽灵节点存在時，指定其时间图点",setters:"StringSetter"},{name:"reverse",label:"reverse",title:"节点排序",setters:"BooleanSetter",defaultValue:!1}],slots:["default","pending","pendingDot"],snippet:{children:[{name:"ATimelineItem",children:"Create a services site 2015-09-01"}],directives:[{name:"vFor",value:{type:"JSExpression",value:"4"}}]}},{name:"ATimelineItem",alias:"Item",parent:"Timeline",label:"时间轴项",categoryId:"data",doc:"https://www.antdv.com/components/timeline-cn",props:[{name:"color",label:"color",title:"指定圆圈颜色 blue, red, green，或自定义的色值",setters:["StringSetter","ColorSetter"],defaultValue:"blue"},{name:"dot",label:"dot",title:"自定义时间轴点",setters:"StringSetter"},{name:"label",label:"label",title:"设置标签",setters:"StringSetter"},{name:"position",label:"position",title:"自定义节点位置",setters:"SelectSetter",options:["left","right"]}],slots:["default","dot","label"],snippet:{children:"创建服务现场 2015-09-01"}}],ue=[{name:"ATooltip",alias:"Tooltip",label:"文字提示",categoryId:"data",doc:"https://www.antdv.com/components/button-cn",props:[{name:"title",label:"title",title:"提示文字",setters:"StringSetter"},{name:"align",label:"align",title:"该值将合并到 placement 的配置中，设置参考 dom-align",setters:"ObjectSetter"},{name:"arrowPointAtCenter",label:"arrowPointAtCenter",title:"箭头是否指向目标元素中心",setters:"BooleanSetter",defaultValue:!1},{name:"autoAdjustOverflow",label:"autoAdjustOverflow",title:"气泡被遮挡时自动调整位置",setters:"BooleanSetter",defaultValue:!0},{name:"color",label:"color",title:"背景颜色",setters:"StringSetter"},{name:"destroyTooltipOnHide",label:"destroyTooltipOnHide",title:"隐藏后是否销毁 tooltip",setters:"BooleanSetter",defaultValue:!1},{name:"getPopupContainer",label:"getPopupContainer",title:"浮层渲染父节点，默认渲染到 body 上",setters:"FunctionSetter"},{name:"mouseEnterDelay",label:"mouseEnterDelay",title:"鼠标移入后延时多少才显示 Tooltip，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"mouseLeaveDelayy",label:"mouseLeaveDelayy",title:"鼠标移出后延时多少才显示 Tooltip，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"overlayClassName",label:"overlayClassName",title:"卡片类名",setters:"StringSetter"},{name:"overlayStyle",label:"overlayStyle",title:"卡片样式",setters:"ObjectSetter"},{name:"overlayInnerStyle",label:"overlayInnerStyle",title:"卡片内容区域样式",setters:"ObjectSetter"},{name:"placement",label:"placement",title:"气泡框位置",setters:"SelectSetter",options:["top","left","right","bottom","topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"],defaultValue:"top"},{name:"trigger",label:"trigger",title:"触发行为",setters:"SelectSetter",options:["hover","focus","click","contextmenu"],defaultValue:"hover"},{name:"open",label:"open",title:"用于手动控制浮层显隐",setters:"BooleanSetter",defaultValue:!1}],events:["openChange","update:open"],slots:["default","title"],snippet:{slot:"title",children:[{name:"p",slot:{name:"title"},children:"prompt text"},{name:"p",children:" Tooltip will show when mouse enter."}]}}],ce=[{name:"ATour",alias:"Tour",label:"漫游式引导",categoryId:"data",doc:"https://www.antdv.com/components/tour-cn",props:[{name:"arrow",label:"arrow",title:"是否显示箭头，包含是否指向元素中心的配置",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"placement",label:"placement",title:"引导卡片相对于目标元素的位置",setters:"SelectSetter",options:["left","leftTop","leftBottom","right","rightTop","rightBottom","top","topLeft","topRight","bottom","bottomLeft","bottomRight"],defaultValue:"bottom	"},{name:"mask",label:"mask",title:"是否启用蒙层，也可传入配置改变蒙层样式和填充色",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"type",label:"type",title:"类型，影响底色与文字颜色",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"open",label:"open",title:"打开引导",setters:"BooleanSetter"},{name:"current",label:"current",title:"当前处于哪一步",setters:"NumberSetter"},{name:"scrollIntoViewOptions",label:"scrollIntoViewOptions",title:"是否支持当前元素滚动到视窗内，也可传入配置指定滚动视窗的相关参数",setters:["BooleanSetter","SelectSetter"],options:["ScrollIntoViewOptions"],defaultValue:!0},{name:"zIndex",label:"zIndex",title:"Tour 的层级",setters:"NumberSetter",defaultValue:1001},{name:"steps",label:"steps",title:"Tour 的数据",setters:"ArraySetter"}],events:["close","finish","change","update:current"],slots:["default","indicatorsRender"],snippet:{props:{open:!0,current:1,steps:[{title:"Center",description:"Displayed in the center of screen.",target:null},{title:"Center",description:"On the right of target.",target:null},{title:"Center",description:"On the top of target.",target:null}]}}},{name:"ATourSteps ",alias:"TourSteps",label:"漫游式引导步",categoryId:"data",doc:"https://www.antdv.com/components/tour-cn",props:[{name:"target",label:"target",title:"获取引导卡片指向的元素，为空时居中于屏幕",setters:["FunctionSetter","StringSetter"]},{name:"arrow",label:"arrow",title:"是否显示箭头，包含是否指向元素中心的配置",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"placement",label:"placement",title:"引导卡片相对于目标元素的位置",setters:"SelectSetter",options:["left","leftTop","leftBottom","right","rightTop","rightBottom","top","topLeft","topRight","bottom","bottomLeft","bottomRight","bottom"]},{name:"mask",label:"mask",title:"是否启用蒙层，也可传入配置改变蒙层样式和填充色，默认跟随 Tour 的 mask 属性",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"type",label:"type",title:"类型，影响底色与文字颜色",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"nextButtonProps",label:"nextButtonProps",title:"下一步按钮的属性",setters:"ObjectSetter"},{name:"prevButtonProps",label:"prevButtonProps",title:"上一步按钮的属性",setters:"ObjectSetter"},{name:"scrollIntoViewOptions",label:"scrollIntoViewOptions",title:"是否支持当前元素滚动到视窗内，也可传入配置指定滚动视窗的相关参数，默认跟随 Tour 的 scrollIntoViewOptions 属性",setters:["BooleanSetter","SelectSetter"],options:["ScrollIntoViewOptions"],defaultValue:!0}],events:["close"],slots:["default","cover","title","description"]}],pe=[{name:"ATree",alias:"Tree",label:"树形控件",categoryId:"data",doc:"https://www.antdv.com/components/tree-cn",props:[{name:"allowDrop",label:"allowDrop",title:"是否允许拖拽时放置在该节点",setters:"FunctionSetter"},{name:"autoExpandParent",label:"autoExpandParent",title:"是否自动展开父节点",setters:"BooleanSetter",defaultValue:!1},{name:"blockNode",label:"blockNode",title:"是否节点占据一行",setters:"BooleanSetter",defaultValue:!1},{name:"checkable",label:"checkable",title:"节点前添加 Checkbox 复选框",setters:"BooleanSetter",defaultValue:!1},{name:"checkedKeys",label:"checkedKeys",title:"（受控）选中复选框的树节点（注意：父子节点有关联，如果传入父节点 key，则子节点自动选中；相应当子节点 key 都传入，父节点也自动选中。当设置checkable和checkStrictly，它是一个有checked和halfChecked属性的对象，并且父子节点的选中与否不再关联",setters:["ArraySetter","ObjectSetter"],defaultValue:[]},{name:"checkStrictly",label:"checkStrictly",title:"checkable 状态下节点选择完全受控（父子节点选中状态不再关联）",setters:"BooleanSetter",defaultValue:!1},{name:"defaultExpandAll",label:"defaultExpandAll",title:'默认展开所有树节点, 如果是异步数据，需要在数据返回后再实例化，建议用 v-if="data.length"；当有 expandedKeys 时，defaultExpandAll 将失效',setters:"BooleanSetter",defaultValue:!1},{name:"disabled",label:"disabled",title:"将树禁用",setters:"BooleanSetter",defaultValue:!1},{name:"draggable",label:"draggable",title:"设置节点可拖拽",setters:"BooleanSetter",defaultValue:!1},{name:"expandedKeys",label:"expandedKeys",title:"（受控）展开指定的树节点",setters:"ArraySetter",defaultValue:[]},{name:"fieldNames",label:"fieldNames",title:"替换 treeNode 中 title,key,children 字段为 treeData 中对应的字段",setters:"ObjectSetter",defaultValue:{children:"children",title:"title",key:"key"}},{name:"filterTreeNode",label:"filterTreeNode",title:"按需筛选树节点（高亮），返回 true",setters:"FunctionSetter"},{name:"height",label:"height",title:"设置虚拟滚动容器高度，设置后内部节点不再支持横向滚动",setters:"NumberSetter"},{name:"loadData",label:"loadData",title:"异步加载数据",setters:"FunctionSetter"},{name:"loadedKeys",label:"loadedKeys",title:"（受控）已经加载的节点，需要配合 loadData 使用",setters:"ArraySetter",defaultValue:[]},{name:"multiple",label:"multiple",title:"支持点选多个节点（节点本身）",setters:"BooleanSetter",defaultValue:!1},{name:"selectable",label:"selectable",title:"是否可选中",setters:"BooleanSetter",defaultValue:!0},{name:"selectedKeys",label:"selectedKeys",title:"（受控）设置选中的树节点",setters:"ArraySetter"},{name:"showIcon",label:"showIcon",title:"是否展示 TreeNode title 前的图标，没有默认样式，如设置为 true，需要自行定义图标相关样式",setters:"BooleanSetter",defaultValue:!1},{name:"showLine",label:"showLine",title:"是否展示连接线",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"treeData",label:"treeData",title:"treeNodes 数据，如果设置则不需要手动构造 TreeNode 节点（key 在整个树范围内唯一）",setters:"ArraySetter"},{name:"virtual",label:"virtual",title:"设置 false 时关闭虚拟滚动",setters:"BooleanSetter",defaultValue:!0}],events:["check","dragend","dragenter","dragleave","dragover","dragstart","drop","expand","load","rightClick","select","update:checkedKeys","update:expandedKeys","update:selectedKeys"],slots:["default","switcherIcon","title"],snippet:{props:{expandedKeys:["0-0-0","0-0-1"],selectedKeys:["0-0-0","0-0-1"],checkedKeys:["0-0-0","0-0-1"],checkable:!0,treeData:[{title:"parent 1",key:"0-0",children:[{title:"parent 1-0",key:"0-0-0",disabled:!0,children:[{title:"leaf",key:"0-0-0-0",disableCheckbox:!0},{title:"leaf",key:"0-0-0-1"}]},{title:"parent 1-1",key:"0-0-1",children:[{key:"0-0-1-0",title:"sss"}]}]}]}}}],me=[{name:"AAlert",alias:"Alert",label:"警告提示",categoryId:"feedback",doc:"https://www.antdv.com/components/alert-cn",props:[{name:"afterClose",label:"afterClose",title:"关闭动画结束后触发的回调函数",setters:"FunctionSetter"},{name:"banner",label:"banner",title:"是否用作顶部公告",setters:"BooleanSetter",defaultValue:!1},{name:"closable",label:"closable",title:"默认不显示关闭按钮",setters:"BooleanSetter"},{name:"closeText",label:"closeText",title:"自定义关闭按钮",setters:"StringSetter"},{name:"description",label:"description",title:"警告提示的辅助性文字介绍",setters:"StringSetter"},{name:"message",label:"message",title:"警告提示内容",setters:"StringSetter"},{name:"showIcon",label:"showIcon",title:"是否显示辅助图标",setters:"BooleanSetter",defaultValue:!1},{name:"type",label:"type",title:"指定警告提示的样式，有四种选择 success、info、warning、error",setters:"SelectSetter",options:["success","info","warning","error"],defaultValue:""}],events:["close"],slots:["default","action","closeIcon","closeText","description","icon","message"],snippet:{props:{message:"Success Text",type:"success"}}}],be=[{name:"ADrawer",alias:"Drawer",label:"抽屉",categoryId:"feedback",doc:"https://www.antdv.com/components/drawer-cn",props:[{name:"autofocus",label:"autofocus",title:"抽屉展开后是否将焦点切换至其 Dom 节点",setters:"BooleanSetter",defaultValue:!0},{name:"bodyStyle",label:"bodyStyle",title:"可用于设置 Drawer 内容部分的样式",setters:"ObjectSetter"},{name:"class",label:"class",title:"Drawer 容器外层 className 设置，如果需要设置最外层，请使用 rootClassName",setters:"StringSetter"},{name:"closable",label:"closable",title:"是否显示左上角的关闭按钮",setters:"BooleanSetter",defaultValue:!0},{name:"contentWrapperStyle",label:"contentWrapperStyle",title:"可用于设置 Drawer 包裹内容部分的样式",setters:"ObjectSetter"},{name:"destroyOnClose",label:"destroyOnClose",title:"关闭时销毁 Drawer 里的子元素",setters:"BooleanSetter",defaultValue:!1},{name:"footerStyle",label:"footerStyle",title:"抽屉页脚部件的样式",setters:"ObjectSetter"},{name:"forceRender",label:"forceRender",title:"预渲染 Drawer 内元素",setters:"BooleanSetter",defaultValue:!1},{name:"getContainer",label:"getContainer",title:"指定 Drawer 挂载的节点，并在容器内展现 | () => HTMLElement | Selectors",setters:"StringSetter",defaultValue:"body"},{name:"headerStyle",label:"headerStyle",title:"用于设置 Drawer 头部的样式",setters:"ObjectSetter"},{name:"height",label:"height",title:"高度, 在 placement 为 top 或 bottom 时使用",setters:["StringSetter","NumberSetter"],defaultValue:378},{name:"keyboard",label:"keyboard",title:"是否支持键盘 esc 关闭",setters:"BooleanSetter",defaultValue:!0},{name:"mask",label:"mask",title:"是否展示遮罩",setters:"BooleanSetter",defaultValue:!0},{name:"maskClosable",label:"maskClosable",title:"点击蒙层是否允许关闭",setters:"BooleanSetter",defaultValue:!0},{name:"maskStyle",label:"maskStyle",title:"遮罩样式",setters:"ObjectSetter",defaultValue:{}},{name:"placement",label:"placement",title:"抽屉的方向",setters:"SelectSetter",options:["top","right","bottom","left"],defaultValue:"right"},{name:"push",label:"push",title:"用于设置多层 Drawer 的推动行为",setters:["BooleanSetter","ObjectSetter"],defaultValue:{distance:180}},{name:"rootClassName",label:"rootClassName",title:"对话框外层容器的类名",setters:"StringSetter"},{name:"rootStyle",label:"rootStyle",title:"可用于设置 Drawer 最外层容器的样式，和 style 的区别是作用节点包括 mask",setters:"ObjectSetter"},{name:"size",label:"size",title:"预设抽屉宽度（或高度），default 378px 和 large 736px",setters:"SelectSetter",options:["default","large"],defaultValue:"default"},{name:"style",label:"style",title:"设计 Drawer 容器样式，如果你只需要设置内容部分请使用 bodyStyle",setters:"ObjectSetter"},{name:"title",label:"title",title:"标题",setters:"StringSetter"},{name:"open",label:"open",title:"Drawer 是否可见",setters:"BooleanSetter"},{name:"width",label:"width",title:"宽度",setters:["StringSetter","NumberSetter"],defaultValue:378},{name:"zIndex",label:"zIndex",title:"设置 Drawer 的 z-index",setters:"NumberSetter",defaultValue:1e3}],events:["afterOpenChange","close","update:open"],slots:["default","closeIcon","extra","footer","title"],snippet:{props:{open:!0,style:{color:"red"},title:"Basic Drawer",placement:"right"},children:[{name:"p",children:"Some contents..."},{name:"p",children:"Some contents..."},{name:"p",children:"Some contents..."}]}}],Se=[{name:"AModal",alias:"Modal",label:"对话框",categoryId:"feedback",doc:"https://www.antdv.com/components/modal-cn",props:[{name:"afterClose",label:"afterClose",title:"Modal 完全关闭后的回调",setters:"FunctionSetter"},{name:"bodyStyle",label:"bodyStyle",title:"Modal body 样式",setters:"ObjectSetter",defaultValue:{}},{name:"cancelButtonProps",label:"cancelButtonProps",title:"cancel 按钮 props",setters:"ArraySetter"},{name:"cancelText",label:"cancelText",title:"取消按钮文字",setters:"StringSetter",defaultValue:"取消"},{name:"centered",label:"centered",title:"垂直居中展示 Modal",setters:"BooleanSetter",defaultValue:!1},{name:"closable",label:"closable",title:"是否显示右上角的关闭按钮",setters:"BooleanSetter",defaultValue:!0},{name:"confirmLoading",label:"confirmLoading",title:"确定按钮 loading",setters:"BooleanSetter",defaultValue:!1},{name:"destroyOnClose",label:"destroyOnClose",title:"关闭时销毁 Modal 里的子元素",setters:"BooleanSetter",defaultValue:!1},{name:"footer",label:"footer",title:'底部内容，当不需要默认底部按钮时，可以设为 :footer="null"',setters:"StringSetter",defaultValue:"确定取消按钮"},{name:"forceRender",label:"forceRender",title:"强制渲染 Modal",setters:"BooleanSetter",defaultValue:!1},{name:"getContainer",label:"getContainer",title:"指定 Modal 挂载的 HTML 节点",setters:"FunctionSetter"},{name:"keyboard",label:"keyboard",title:"是否支持键盘 esc 关闭",setters:"BooleanSetter",defaultValue:!0},{name:"mask",label:"mask",title:"是否展示遮罩",setters:"BooleanSetter",defaultValue:!0},{name:"maskClosable",label:"maskClosable",title:"点击蒙层是否允许关闭",setters:"BooleanSetter",defaultValue:!0},{name:"maskStyle",label:"maskStyle",title:"遮罩样式",setters:"ObjectSetter",defaultValue:{}},{name:"okButtonProps",label:"okButtonProps",title:"ok 按钮 props",setters:"ArraySetter"},{name:"okText",label:"okText",title:"确认按钮文字",setters:"StringSetter",defaultValue:"确认"},{name:"okType",label:"okType",title:"确认按钮类型",setters:"StringSetter",defaultValue:"primary"},{name:"title",label:"title",title:"标题",setters:"StringSetter"},{name:"open",label:"open",title:"对话框是否可见",setters:"BooleanSetter",defaultValue:!1},{name:"width",label:"width",title:"宽度",setters:["StringSetter","NumberSetter"],defaultValue:520},{name:"wrapClassName",label:"wrapClassName",title:"对话框外层容器的类名",setters:"StringSetter"},{name:"zIndex",label:"zIndex",title:"设置 Modal 的 z-index",setters:"NumberSetter",defaultValue:1e3}],events:["cancel","ok","update:open"],slots:["default","cancelText","closeIcon","footer","okText","title"],snippet:{props:{open:!0,title:"Basic Modal"},children:[{name:"p",children:"Some contents..."},{name:"p",children:"Some contents..."},{name:"p",children:"Some contents..."}]}}],fe=[{name:"APopconfirm",alias:"Popconfirm",label:"气泡确认框",categoryId:"feedback",doc:"https://www.antdv.com/components/popconfirm-cn",props:[{name:"cancelButtonProps",label:"cancelButtonProps",title:"cancel 按钮 props",setters:"ObjectSetter"},{name:"cancelText",label:"cancelText",title:"取消按钮文字",setters:"StringSetter",defaultValue:"取消"},{name:"disabled",label:"disabled",title:"点击 Popconfirm 子元素是否弹出气泡确认框",setters:"BooleanSetter",defaultValue:!1},{name:"okButtonProps",label:"okButtonProps",title:"ok 按钮 props",setters:"ObjectSetter"},{name:"okText",label:"okText",title:"确认按钮文字",setters:"StringSetter",defaultValue:"确认"},{name:"okType",label:"okType",title:"确认按钮类型",setters:"StringSetter",defaultValue:"primary"},{name:"showCancel",label:"showCancel",title:"是否显示取消按钮",setters:"BooleanSetter",defaultValue:!0},{name:"title",label:"title",title:"确认框的描述",setters:"StringSetter"},{name:"description",label:"description",title:"确认内容的详细描述",setters:"StringSetter"},{name:"open ",label:"open ",title:"是否显示",setters:"BooleanSetter"},{name:"align",label:"align",title:"该值将合并到 placement 的配置中，设置参考 dom-align",setters:"ObjectSetter"},{name:"arrowPointAtCenter",label:"arrowPointAtCenter",title:"箭头是否指向目标元素中心",setters:"BooleanSetter",defaultValue:!1},{name:"autoAdjustOverflow",label:"autoAdjustOverflow",title:"气泡被遮挡时自动调整位置",setters:"BooleanSetter",defaultValue:!0},{name:"color",label:"color",title:"背景颜色",setters:"StringSetter"},{name:"destroyTooltipOnHide",label:"destroyTooltipOnHide",title:"隐藏后是否销毁 tooltip",setters:"BooleanSetter",defaultValue:!1},{name:"getPopupContainer",label:"getPopupContainer",title:"浮层渲染父节点，默认渲染到 body 上",setters:"FunctionSetter"},{name:"mouseEnterDelay",label:"mouseEnterDelay",title:"鼠标移入后延时多少才显示 Tooltip，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"mouseLeaveDelayy",label:"mouseLeaveDelayy",title:"鼠标移出后延时多少才显示 Tooltip，单位：秒",setters:"NumberSetter",defaultValue:.1},{name:"overlayClassName",label:"overlayClassName",title:"卡片类名",setters:"StringSetter"},{name:"overlayStyle",label:"overlayStyle",title:"卡片样式",setters:"ObjectSetter"},{name:"overlayInnerStyle",label:"overlayInnerStyle",title:"卡片内容区域样式",setters:"ObjectSetter"},{name:"placement",label:"placement",title:"气泡框位置",setters:"SelectSetter",options:["top","left","right","bottom","topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"],defaultValue:"top"},{name:"trigger",label:"trigger",title:"触发行为",setters:"SelectSetter",options:["hover","focus","click","contextmenu"],defaultValue:"hover"},{name:"open",label:"open",title:"用于手动控制浮层显隐",setters:"BooleanSetter",defaultValue:!1}],events:["cancel","confirm","openChange","update:open"],slots:["default","cancelButton","cancelText","icon","okButton","okText","title","description"],snippet:{props:{title:"Are you sure delete this task?",okText:"yes",cancelText:"No"},children:[{name:"a",children:"气泡确认框"}]}}],ge=[{name:"AProgress",alias:"Progress",label:"进度条",categoryId:"feedback",doc:"https://www.antdv.com/components/progress-cn",props:[{name:"format",label:"format",title:"内容的模板函数",setters:"FunctionSetter"},{name:"percent",label:"percent",title:"百分比",setters:"NumberSetter",defaultValue:0},{name:"showInfo",label:"showInfo",title:"是否显示进度数值或状态图标",setters:"BooleanSetter",defaultValue:!0},{name:"status",label:"status",title:"状态",setters:"ArraySetter",options:["success","exception","normal","active"]},{name:"strokeColor",label:"strokeColor",title:"进度条的色彩",setters:"StringSetter"},{name:"strokeLinecap",label:"strokeLinecap",title:"进度条的样式",setters:"SelectSetter",options:["round","butt","square"],defaultValue:"round"},{name:"success",label:"success",title:"成功进度条相关配置",setters:"ObjectSetter"},{name:"title",label:"title",title:"html 标签 title",setters:"StringSetter"},{name:"trailColor",label:"trailColor",title:"未完成的分段的颜色",setters:"StringSetter"},{name:"type",label:"type",title:"类型，可选 line circle dashboard",setters:"SelectSetter",options:["line","circle","dashboard"],defaultValue:"line"},{name:"size",label:"size",title:"进度条的尺寸",setters:["NumberSetter","ArraySetter","small","default"],defaultValue:"default"},{name:"steps",label:"steps",title:"进度条总共步数",setters:"NumberSetter"},{name:"strokeColor",label:"strokeColor",title:"进度条的色彩，传入 object 时为渐变。当有 steps 时支持传入一个数组",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"strokeWidth",label:"strokeWidth",title:"圆形进度条线的宽度，单位是进度条画布宽度的百分比",setters:"NumberSetter",defaultValue:6},{name:"gapDegree",label:"gapDegree",title:"仪表盘进度条缺口角度，可取值 0 ~ 295",setters:"NumberSetter",defaultValue:75},{name:"gapPosition",label:"gapPosition",title:"仪表盘进度条缺口位置",setters:"SelectSetter",options:["top","bottom","left","right"],defaultValue:"bottom"}],snippet:{props:{percent:40}}}],he=[{name:"AResult",alias:"Result",label:"结果",categoryId:"feedback",doc:"https://www.antdv.com/components/result-cn",props:[{name:"status",label:"status",title:"结果的状态,决定图标和颜色",setters:"SelectSetter",options:["success","error","info","warning","404","403","500"],defaultValue:"info"},{name:"subTitle",label:"subTitle",title:"subTitle 文字",setters:"StringSetter"},{name:"title",label:"title",title:"title文字",setters:"StringSetter"}],slots:["default","extra","icon","subTitle","title"]}],ye=[{name:"ASkeleton",alias:"Skeleton",label:"骨架屏",categoryId:"feedback",doc:"https://www.antdv.com/components/skeleton-cn",props:[{name:"active",label:"active",title:"是否展示动画效果",setters:"BooleanSetter",defaultValue:!1},{name:"avatar",label:"avatar",title:"是否显示头像占位图",setters:["BooleanSetter","ObjectSetter"],defaultValue:!1},{name:"loading",label:"loading",title:"为 true 时，显示占位图。反之则直接展示子组件",setters:"BooleanSetter"},{name:"paragraph",label:"paragraph",title:"是否显示段落占位图",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0},{name:"title",label:"title",title:"是否显示标题占位图",setters:["BooleanSetter","ObjectSetter"],defaultValue:!0}]},{name:"ASkeletonAvatar",alias:"SkeletonAvatar",label:"头像骨架屏",categoryId:"feedback",doc:"https://www.antdv.com/components/skeleton-cn",props:[{name:"shape",label:"shape",title:"指定头像的形状",setters:"SelectSetter",options:["circle","square"]},{name:"size",label:"size",title:"设置头像占位图的大小",setters:["SelectSetter","NumberSetter"],options:["large","small","default"]}]},{name:"ASkeletonButton",alias:"SkeletonButton",label:"按钮骨架屏",categoryId:"feedback",doc:"https://www.antdv.com/components/skeleton-cn",props:[{name:"active",label:"active",title:"是否展示动画效果",setters:"BooleanSetter",defaultValue:!1},{name:"block",label:"block",title:"将按钮宽度调整为其父宽度的选项",setters:"BooleanSetter",defaultValue:!1},{name:"shape",label:"shape",title:"指定按钮的形状",setters:"SelectSetter",options:["circle","round","default"]},{name:"size",label:"size",title:"设置按钮的大小",setters:"SelectSetter",options:["large","small","default"]}]},{name:"ASkeletonInput",alias:"SkeletonInput",label:"输入框骨架屏",categoryId:"feedback",doc:"https://www.antdv.com/components/skeleton-cn",props:[{name:"active",label:"active",title:"是否展示动画效果",setters:"BooleanSetter",defaultValue:!1},{name:"size",label:"size",title:"设置输入框的大小",setters:"SelectSetter",options:["large","small","default"]}]}],ve=[{name:"ASpin",alias:"Spin",label:"加载中",categoryId:"feedback",doc:"https://www.antdv.com/components/spin-cn",props:[{name:"delay",label:"delay",title:"延迟显示加载效果的时间（防止闪烁）",setters:"NumberSetter"},{name:"indicator",label:"indicator",title:"加载指示符"},{name:"size",label:"size",title:"组件大小，可选值为 small default large",setters:"SelectSetter",options:["small","default","large"],defaultValue:"default"},{name:"spinning",label:"spinning",title:"是否为加载中状态",setters:"BooleanSetter",defaultValue:!0},{name:"tip",label:"tip",title:"当作为包裹元素时，可以自定义描述文案",setters:"StringSetter"},{name:"wrapperClassName",label:"wrapperClassName",title:"包装器的类属性",setters:"StringSetter"}],slots:["default","indicator","tip"]}],we=[{name:"AAffix",alias:"Affix",label:"固钉",categoryId:"other",doc:"https://antdv.com/components/affix-cn",props:[{name:"offsetBottom",label:"offsetBottom",title:"距离窗口底部达到指定偏移量后触发",setters:"NumberSetter"},{name:"offsetTop",label:"offsetTop",title:"距离窗口顶部达到指定偏移量后触发",setters:"NumberSetter",defaultValue:0},{name:"target",label:"target",title:"设置 Affix 需要监听其滚动事件的元素，值为一个返回对应 DOM 元素的函数",setters:"FunctionSetter"}],events:["change"],slots:["default"],snippet:{props:{offsetTop:"30",target:{type:"JSFunction",value:"() => window"}},children:[{name:"AButton",props:{type:"primary"},children:"Affix Button"}]}}],Ve=[{name:"AConfigProvider",alias:"ConfigProvider",label:"全局化配置",categoryId:"other",doc:"https://www.antdv.com/components/config-provider-cn",props:[{name:"autoInsertSpaceInButton",label:"autoInsertSpaceInButton",title:"设置为 false 时，移除按钮中 2 个汉字之间的空格",setters:"BooleanSetter",defaultValue:!0},{name:"componentSize",label:"componentSize",title:"设置 antd 组件大小",setters:"SelectSetter",options:["small","middle","large"]},{name:"csp",label:"csp",title:"设置 Content Security Policy 配置",setters:"ObjectSetter"},{name:"direction",label:"direction",title:"设置文本展示方向",setters:"SelectSetter",options:["ltr","rtl"],defaultValue:"ltr"},{name:"dropdownMatchSelectWidth",label:"dropdownMatchSelectWidth",title:"下拉菜单和选择器同宽。默认将设置 min-width，当值小于选择框宽度时会被忽略。false 时会关闭虚拟滚动",setters:["BooleanSetter","NumberSetter"]},{name:"form",label:"form",title:"设置 Form 组件的通用属性",setters:"ObjectSetter"},{name:"getPopupContainer",label:"getPopupContainer",title:"弹出框（Select, Tooltip, Menu 等等）渲染父节点，默认渲染到 body 上",setters:"FunctionSetter"},{name:"getTargetContainer",label:"getTargetContainer",title:"配置 Affix、Anchor 滚动监听容器",setters:"FunctionSetter"},{name:"input",label:"input",title:"设置 Input 组件的通用属性",setters:"ObjectSetter"},{name:"locale",label:"locale",title:"语言包配置",setters:"ObjectSetter"},{name:"pageHeader",label:"pageHeader",title:"统一设置 pageHeader 的 ghost",setters:"ObjectSetter",defaultValue:{ghost:!0}},{name:"prefixCls",label:"prefixCls",title:"设置统一样式前缀。注意：需要配合 less 变量 @ant-prefix 使用",setters:"StringSetter",defaultValue:"ant"},{name:"renderEmpty",label:"renderEmpty",title:"自定义组件空状态。参考 空状态",setters:"FunctionSetter"},{name:"space",label:"space",title:"设置 Space 的 size，参考 Space",setters:"ObjectSetter"},{name:"transformCellText",label:"transformCellText",title:"Table 数据渲染前可以再次改变，一般用户空数据的默认配置",setters:"FunctionSetter"},{name:"virtual",label:"virtual",title:"设置 false 时关闭虚拟滚动",setters:"BooleanSetter"},{name:"wave",label:"wave",title:"设置水波纹特效",setters:"ObjectSetter"}],slots:["default","renderEmpty"]}],Be=[{name:"AFloatButton",alias:"FloatButton",label:"悬浮按钮",categoryId:"other",doc:"https://www.antdv.com/components/float-button-cn",props:[{name:"description",label:"description",title:"文字及其它内容",setters:"StringSetter"},{name:"tooltip",label:"tooltip",title:"气泡卡片的内容",setters:"StringSetter"},{name:"type",label:"type",title:"设置按钮类型",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"shape",label:"shape",title:"设置按钮形状",setters:"SelectSetter",options:["circle","square"],defaultValue:"circle"},{name:"onClick",label:"onClick",title:"点击按钮时的回调",setters:"FunctionSetter"},{name:"href",label:"href",title:"点击跳转的地址，指定此属性 button 的行为和 a 链接一致",setters:"StringSetter"},{name:"target",label:"target",title:"相当于 a 标签的 target 属性，href 存在时生效",setters:"StringSetter"},{name:"badge",label:"badge",title:"带徽标数字的悬浮按钮（不支持 status 以及相关属性）",setters:"ObjectSetter"}],events:["click"],slots:["default","icon","description","tooltip"]},{name:"AFloatButtonGroup",alias:"Group",parent:"FloatButton",label:"悬浮按钮组",categoryId:"other",doc:"https://www.antdv.com/components/float-button-cn",props:[{name:"description",label:"description",title:"文字及其它内容",setters:"StringSetter"},{name:"tooltip",label:"tooltip",title:"气泡卡片的内容",setters:"StringSetter"},{name:"type",label:"type",title:"设置按钮类型",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"onClick",label:"onClick",title:"点击按钮时的回调",setters:"FunctionSetter"},{name:"href",label:"href",title:"点击跳转的地址，指定此属性 button 的行为和 a 链接一致",setters:"StringSetter"},{name:"target",label:"target",title:"相当于 a 标签的 target 属性，href 存在时生效",setters:"StringSetter"},{name:"badge",label:"badge",title:"带徽标数字的悬浮按钮（不支持 status 以及相关属性）",setters:"ObjectSetter"},{name:"shape",label:"shape",title:"设置包含的 FloatButton 按钮形状",setters:"SelectSetter",options:["circle","square"],defaultValue:"circle"},{name:"trigger",label:"trigger",title:"触发方式（有触发方式为菜单模式）",setters:"SelectSetter",options:["click","hover"]},{name:"open",label:"open",title:"受控展开",setters:"BooleanSetter"}],events:["openChange","click","update:open"],slots:["default","icon","description","tooltip"],snippet:{props:{style:{right:"164px"}},children:[{name:"AFloatButton",props:{badge:{count:5,color:"blue"}}},{name:"AFloatButton",props:{badge:{count:5}}}]}},{name:"ABackTop",alias:"BackTop",parent:"FloatButton",label:"悬浮回到顶部",categoryId:"other",doc:"https://www.antdv.com/components/float-button-cn",props:[{name:"description",label:"description",title:"文字及其它内容",setters:"StringSetter"},{name:"tooltip",label:"tooltip",title:"气泡卡片的内容",setters:"StringSetter"},{name:"type",label:"type",title:"设置按钮类型",setters:"SelectSetter",options:["default","primary"],defaultValue:"default"},{name:"shape",label:"shape",title:"设置按钮形状",setters:"SelectSetter",options:["circle","square"],defaultValue:"circle"},{name:"onClick",label:"onClick",title:"点击按钮时的回调",setters:"FunctionSetter"},{name:"href",label:"href",title:"点击跳转的地址，指定此属性 button 的行为和 a 链接一致",setters:"StringSetter"},{name:"target",label:"target",title:"相当于 a 标签的 target 属性，href 存在时生效",setters:"StringSetter"},{name:"badge",label:"badge",title:"带徽标数字的悬浮按钮（不支持 status 以及相关属性）",setters:"ObjectSetter"}],events:["click"],slots:["default","icon","description","tooltip"],snippet:{props:{visibilityHeight:"0"}}}],Ae=[{name:"AWatermark",alias:"Watermark",label:"水印",categoryId:"other",doc:"https://www.antdv.com/components/watermark-cn",props:[{name:"width",label:"width",title:"水印的宽度，content 的默认值为自身的宽度",setters:"NumberSetter",defaultValue:120},{name:"height",label:"height",title:"水印的高度，content 的默认值为自身的高度",setters:"NumberSetter",defaultValue:64},{name:"rotate",label:"rotate",title:"水印绘制时，旋转的角度，单位 °",setters:"NumberSetter",defaultValue:-22},{name:"zIndex",label:"zIndex",title:"追加的水印元素的 z-index",setters:"NumberSetter",defaultValue:9},{name:"image",label:"image",title:"图片源，建议导出 2 倍或 3 倍图，优先级高",setters:"StringSetter"},{name:"content",label:"content",title:"水印文字内容",setters:["StringSetter","ArraySetter"]},{name:"font",label:"font",title:"文字样式",setters:"ObjectSetter",defaultValue:"ObjectSetter"},{name:"gap",label:"gap",title:"水印之间的间距",setters:"ArraySetter",defaultValue:[100,100]},{name:"offset",label:"offset",title:"水印距离容器左上角的偏移量，默认为 gap/2",setters:"ArraySetter",defaultValue:[50,50]}],snippet:{props:{content:"VTJ"},children:[{name:"div",props:{style:{height:"500px"}}}]}}],ke=[{name:"AApp",alias:"App",label:"包裹组件",categoryId:"other",doc:"https://www.antdv.com/components/app-cn",props:[{name:"message",label:"message",title:"App 内 Message 的全局配置",setters:""},{name:"notification",label:"notification",title:"App 内 Notification 的全局配置",setters:""}]}],u="ant-design-vue",xe=[...b,...f,...g,...S,...h,...y,...v,...w,...V,...B,...A,...k,...x,...C,...I,...T,...O,...N,...P,...F,...j,...D,...z,...R,...L,...M,...H,...E,...K,...$,...W,...G,...Y,...U,...q,...J,..._,...Q,...Z,...X,...ee,...te,...le,...ae,...re,...ne,...se,...oe,...ie,...de,...ue,...ce,...pe,...me,...be,...Se,...fe,...ge,...he,...ye,...ve,...we,...Ve,...Be,...Ae,...ke];return{name:u,version:t,label:"Antdv",library:"AntdvMaterial",order:4,categories:[{id:"base",category:"通用"},{id:"layout",category:"布局"},{id:"nav",category:"导航"},{id:"input",category:"数据录入"},{id:"data",category:"数据展示"},{id:"feedback",category:"反馈"},{id:"other",category:"其他"}],components:e(xe,u)}});
