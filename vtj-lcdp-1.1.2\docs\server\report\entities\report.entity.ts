import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  Index
} from 'typeorm';
import { ReportType } from '../../shared';

@Entity({
  name: 'report',
  comment: '引擎运行报告'
})
export class Report {
  @PrimaryGeneratedColumn('uuid', { comment: '唯一标识' })
  id: string;

  @Index()
  @Column({ name: 'project_id', comment: '项目id', nullable: true })
  projectId: string;

  @Index()
  @Column({ name: 'project_uid', comment: '项目唯一标识', nullable: true })
  projectUid: string;

  @Index()
  @CreateDateColumn({ name: 'created_at', comment: '数据插入时间' })
  createdAt: Date;

  @Column({ name: 'session_id', comment: '引擎运行会话id' })
  sessionId: string;

  @Index()
  @Column({ name: 'user_id', comment: '用户id', nullable: true })
  userId: string;

  @Column({ name: 'user_name', comment: '用户名', nullable: true })
  userName: string;

  @Index()
  @Column('enum', {
    enum: ReportType,
    comment: '报告类型'
  })
  type: string;

  @Index()
  @Column({ name: 'engine_version', comment: '引擎版本' })
  engineVersion: string;

  @Column({ comment: '来源Ip地址', nullable: true })
  ip: string;

  @Index()
  @Column({ comment: '来源主机', nullable: true })
  host: string;

  @Column({ comment: '来源URL', nullable: true, length: 1000 })
  url: string;

  @Column({ comment: '上一个页面URL', nullable: true, length: 1000 })
  referrer: string;

  @Column({ comment: '客户端系统', nullable: true })
  os: string;

  @Column({ name: 'os_version', comment: '客户端系统版本', nullable: true })
  osVersion: string;

  @Column({ comment: '浏览器', nullable: true })
  browser: string;

  @Column({ name: 'browser_version', comment: '浏览器版本', nullable: true })
  browserVersion: string;

  @Column({ type: 'text', comment: '错误信息', nullable: true })
  message: string;

  @Column({ type: 'text', comment: '错误栈信息', nullable: true })
  stack: string;

  @Column('json', { comment: '源信息', nullable: true })
  source: string;
}
