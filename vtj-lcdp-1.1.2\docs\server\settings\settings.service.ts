import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Setting } from './entities/setting.entity';
import { SettingDto } from './dto/setting.dto';
import { CacheService } from '../cache/cache.service';
@Injectable()
export class SettingsService {
  private cacheKey: string = '__VTJ_SETTINGS__';
  constructor(
    @InjectRepository(Setting) private repo: Repository<Setting>,
    private cache: CacheService
  ) {}
  async save(dto: SettingDto) {
    const row = await this.repo.findOneBy({});
    this.cache.removeByKey(this.cacheKey);
    if (row) {
      Object.assign(row, dto);
      return await this.repo.save(row);
    } else {
      return await this.repo.save(dto);
    }
  }
  async get() {
    const cache = await this.cache.get(this.cacheKey);
    if (cache) {
      return cache.value as Setting;
    } else {
      const row = await this.repo.findOneBy({});
      if (row) {
        this.cache.set(this.cacheKey, row, 24 * 3600 * 1000);
        return row;
      }
      return null;
    }
  }
  async getSafeSettings() {
    const settings = await this.get();
    if (settings) {
      delete settings.promptTemplate;
      delete settings.imagePromptTemplate;
      delete settings.imageApiKey;
      delete settings.mailPass;
      delete settings.jsonApiKey;
      delete settings.jsonPromptTemplate;
    }
    return settings;
  }
}
