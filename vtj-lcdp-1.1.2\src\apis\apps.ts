import { createApi } from '@vtj/utils';
import { REMOTE, type ApiListResponse, type LowCodeAppVO } from '@/shared';

export interface SaveLowCodeAppReq {
  platform: string;
  name: string;
  label: string;
}

export interface FindLowCodeAppsReq {
  page: number;
  limit: number;
}

export type FindLowCodeAppsRes = {
  id: string;
} & SaveLowCodeAppReq;

/**
 * 新建、更新应用
 */
export const saveLowCodeApp = createApi<LowCodeAppVO, SaveLowCodeAppReq>({
  baseURL: REMOTE,
  url: '/api/apps',
  method: 'post',
  settings: {
    type: 'json'
  }
});

/**
 * 获取我的创建的应用
 */
export const findLowCodeApps = createApi<
  ApiListResponse<FindLowCodeAppsRes>,
  FindLowCodeAppsReq
>({
  baseURL: REMOTE,
  url: '/api/apps/action/find-my-apps'
});

/**
 * 删除我的应用
 * @param id
 * @returns
 */
export const removeLowCodeApp = (id: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/api/apps/:id',
    method: 'delete'
  });
  return api(null, { params: { id } });
};

/**
 * 获取应用详情
 * @param name
 * @returns
 */
export const getLowCodeApp = (name: string) => {
  const api = createApi<LowCodeAppVO>({
    baseURL: REMOTE,
    url: '/api/apps/:name',
    method: 'get'
  });
  return api(null, { params: { name } });
};
