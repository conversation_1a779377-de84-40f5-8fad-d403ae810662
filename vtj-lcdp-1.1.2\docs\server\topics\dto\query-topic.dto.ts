import { QueryDto } from '../../shared';
import { Topic } from '../entities/topic.entity';
import { IsOptional, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
export class QueryTopicDto extends QueryDto<Topic> {
  title?: string;
  model?: string;

  @IsOptional()
  @IsBoolean()
  @Transform((v) => v.value === true || v.value === 'true')
  isHot?: boolean;

  platform?: string;
  fileId: string;
  appId?: string;
  userId?: string;
  userName?: string;
}
