<template>
  <ElConfigProvider :locale="zhCn">
    <Suspense>
      <XMask
        :title="title"
        :logo="logo || _logo"
        :menus="menus"
        :disabled="true"
        :actions="actions"
        @action-click="onActionClick"
        :theme="themeSwitchable"
        :home="homePath"></XMask>
    </Suspense>
  </ElConfigProvider>
</template>
<script setup lang="ts">
import { Suspense, computed } from 'vue';
import { useRoute } from 'vue-router';
import { ElConfigProvider, ElMessage } from 'element-plus';
import {
  XMask,
  useMask,
  Bell,
  Lock,
  SwitchButton,
  type ActionBarItems,
  type ActionProps
} from '@vtj/web';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import _logo from './assets/logo.svg';
import { PROJECT_ID } from '@/contants';
const route = useRoute();
const { disabled, title, menus, logo, themeSwitchable } = useMask({
  menuPathPrefix: `/${PROJECT_ID}`,
  disableMenusFilter: true
});

const actions: ActionBarItems = [
  {
    name: 'message',
    icon: Bell,
    badge: 1
  },
  {
    name: 'lock',
    icon: Lock
  },
  {
    name: 'logout',
    icon: SwitchButton
  }
];

const homePath = computed(() => `/${route.params.app}`);

const onActionClick = (action: ActionProps) => {
  ElMessage.success(`click: ${action.name}`);
};
</script>
