import { adoptedStyleSheets as S } from "@vtj/renderer";
/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/uni 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */
const ee = "0.12.70", N = [
  "onLaunch",
  "onShow",
  "onHide",
  "onError",
  "onPageNotFound",
  "onUnhandledRejection",
  "onThemeChange",
  "onPageNotFound",
  "onUniNViewMessage",
  "onExit"
], h = [
  "beforeCreate",
  "created",
  "beforeMount",
  "mounted",
  "beforeUpdate",
  "updated",
  "beforeUnmount",
  "unmounted"
], ne = [
  "onLoad",
  "onShow",
  "onReady",
  "onHide",
  "onUnload",
  "onResize",
  "onPullDownRefresh",
  "onReachBottom",
  "onTabItemTap",
  "onShareAppMessage",
  "onPageScroll",
  "onNavigationBarButtonTap",
  "onBackPress",
  "onNavigationBarSearchInputChanged",
  "onNavigationBarSearchInputConfirmed",
  "onNavigationBarSearchInputClicked",
  "onShareTimeline",
  "onAddToFavorites",
  ...h
], f = "/pages", A = "VTJ", w = "__UNI__1FC118B", I = "VTJ移动跨端项目", U = "1.0.0", R = "100", B = !1, C = {}, y = { enable: !1 }, b = "3", v = {
  name: A,
  appid: w,
  description: I,
  versionName: U,
  versionCode: R,
  transformPx: B,
  "app-plus": { usingComponents: !0, nvueStyleCompiler: "uni-app", compilerVersion: 3, splashscreen: { alwaysShowBeforeRender: !0, waiting: !0, autoclose: !0, delay: 0 }, modules: {}, distribute: { android: { permissions: ['<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>', '<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>', '<uses-permission android:name="android.permission.VIBRATE"/>', '<uses-permission android:name="android.permission.READ_LOGS"/>', '<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>', '<uses-feature android:name="android.hardware.camera.autofocus"/>', '<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>', '<uses-permission android:name="android.permission.CAMERA"/>', '<uses-permission android:name="android.permission.GET_ACCOUNTS"/>', '<uses-permission android:name="android.permission.READ_PHONE_STATE"/>', '<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>', '<uses-permission android:name="android.permission.WAKE_LOCK"/>', '<uses-permission android:name="android.permission.FLASHLIGHT"/>', '<uses-feature android:name="android.hardware.camera"/>', '<uses-permission android:name="android.permission.WRITE_SETTINGS"/>'] }, ios: {}, sdkConfigs: {}, icons: { android: { hdpi: "src/static/logo.png", xhdpi: "src/static/logo.png", xxhdpi: "src/static/logo.png", xxxhdpi: "src/static/logo.png" } } } },
  quickapp: C,
  "mp-weixin": { appid: "", setting: { urlCheck: !1 }, usingComponents: !0 },
  "mp-alipay": { usingComponents: !0 },
  "mp-baidu": { usingComponents: !0 },
  "mp-toutiao": { usingComponents: !0 },
  uniStatistics: y,
  vueVersion: b
}, F = [], P = { navigationBarTextStyle: "black", navigationBarTitleText: "uni-app", navigationBarBackgroundColor: "#F8F8F8", backgroundColor: "#F8F8F8" }, O = { autoscan: !0, custom: { "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue" } }, L = {
  pages: F,
  globalStyle: P,
  easycom: O
};
function te(e) {
  const [t, ...n] = e;
  return [t.toLowerCase(), ...n].join("").replace(/[A-Z]/g, (i) => `-${i.toLowerCase()}`);
}
function x(e) {
  let { manifestJson: t = {}, pagesJson: n = {}, routes: o = [] } = e;
  const i = o.map((s) => {
    const { path: a, style: r, needLogin: c } = s;
    return {
      path: a,
      style: r,
      needLogin: c
    };
  });
  return {
    ...e,
    manifestJson: Object.assign({}, v, t),
    pagesJson: Object.assign({}, L, n, { pages: i })
  };
}
const g = {
  navigationBarBackgroundColor: "backgroundColor",
  navigationBarTextStyle: "titleColor",
  navigationBarTitleText: "titleText",
  navigationStyle: "style",
  titleImage: "titleImage",
  titlePenetrate: "titlePenetrate",
  transparentTitle: "transparentTitle"
};
function E(e) {
  const t = {}, n = {
    black: "#000000",
    white: "#ffffff"
  };
  for (const o in e)
    g[o] && (t[g[o]] = o === "navigationBarTextStyle" ? n[e[o]] || n.black : e[o]);
  return t;
}
function V(e) {
  const t = {};
  for (const n in e)
    g[n] || (t[n] = e[n]);
  return t;
}
function oe(e) {
  return e.split("?")[0].match(/\#\/pages\/([\w\W]*)/i)?.[1] ?? "";
}
function k(e) {
  return e.h5?.router?.mode || "hash";
}
function W(e) {
  return {
    topWindow: !!e.topWindow?.path,
    leftWindow: !!e.leftWindow?.path,
    rightWindow: !!e.rightWindow?.path
  };
}
function M(e) {
  const { pagesJson: t = {} } = e, { globalStyle: n, pages: o = [] } = t;
  return !!(n?.enablePullDownRefresh || o.find((i) => !!i.style?.enablePullDownRefresh));
}
function D(e) {
  const { pagesJson: t = {} } = e, { globalStyle: n, pages: o = [] } = t;
  let i = !1;
  return n?.navigationStyle === "custom" ? (i = !0, o.find((s) => s.style?.navigationStyle === "default") && (i = !1)) : o.every((s) => s.style?.navigationStyle === "custom") && (i = !0), i;
}
function G(e) {
  const t = D(e), n = {
    navigationBar: !1,
    navigationBarButtons: !1,
    navigationBarSearchInput: !1,
    navigationBarTransparent: !1
  }, { pagesJson: o = {} } = e, { globalStyle: i, pages: s = [] } = o;
  if (t)
    return n;
  n.navigationBar = !0;
  const a = i?.h5?.titleNView;
  return s.find((r) => !!r.style?.h5?.titleNView?.buttons.length) && (n.navigationBarButtons = !0), (a?.searchInput || s.find((r) => !!r.style?.h5?.titleNView?.searchInput)) && (n.navigationBarSearchInput = !0), (a?.type === "transparent" || s.find((r) => r.style?.h5?.titleNView?.type === "transparent")) && (n.navigationBarTransparent = !0), n;
}
function J(e, t = window) {
  const { pagesJson: n = {}, manifestJson: o = {} } = e, { topWindow: i, leftWindow: s, rightWindow: a } = W(n), {
    navigationBar: r,
    navigationBarButtons: c,
    navigationBarSearchInput: u,
    navigationBarTransparent: _
  } = G(e), p = {
    // vue
    __VUE_OPTIONS_API__: !0,
    // enable/disable Options API support, default: true
    __VUE_PROD_DEVTOOLS__: !1,
    // enable/disable devtools support in production, default: false
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: !1,
    // uni
    __UNI_FEATURE_WX__: !1,
    // 是否启用小程序的组件实例 API，如：selectComponent 等（uni-core/src/service/plugin/appConfig）
    __UNI_FEATURE_WXS__: !1,
    // 是否启用 wxs 支持，如：getComponentDescriptor 等（uni-core/src/view/plugin/appConfig）
    __UNI_FEATURE_RPX__: !1,
    // 是否启用运行时 rpx 支持
    __UNI_FEATURE_PROMISE__: !1,
    // 是否启用旧版本的 promise 支持（即返回[err,res]的格式）,默认返回标准
    __UNI_FEATURE_LONGPRESS__: !1,
    // 是否启用longpress
    __UNI_FEATURE_I18N_EN__: !1,
    // 是否启用en
    __UNI_FEATURE_I18N_ES__: !1,
    // 是否启用es
    __UNI_FEATURE_I18N_FR__: !1,
    // 是否启用fr
    __UNI_FEATURE_I18N_ZH_HANS__: !0,
    // 是否启用zh_Hans
    __UNI_FEATURE_I18N_ZH_HANT__: !1,
    // 是否启用zh_Hant
    // 以下特性，编译器已自动识别是否需要启用
    __UNI_FEATURE_UNI_CLOUD__: !1,
    // 是否启用uniCloud
    __UNI_FEATURE_I18N_LOCALE__: !1,
    // 是否启用i18n
    __UNI_FEATURE_NVUE__: !1,
    // 是否启用nvue
    __UNI_FEATURE_ROUTER_MODE__: k(o),
    // 路由模式
    __UNI_FEATURE_PAGES__: !!n.pages?.length,
    // 是否多页面
    __UNI_FEATURE_TABBAR__: !!n.tabBar?.list?.length,
    // 是否包含tabBar
    __UNI_FEATURE_TABBAR_MIDBUTTON__: !!n.tabBar?.midButton,
    // 是否包含midButton
    __UNI_FEATURE_TOPWINDOW__: i,
    // 是否包含topWindow
    __UNI_FEATURE_LEFTWINDOW__: s,
    // 是否包含leftWindow
    __UNI_FEATURE_RIGHTWINDOW__: a,
    // 是否包含rightWindow
    __UNI_FEATURE_RESPONSIVE__: !1,
    // 是否启用响应式
    __UNI_FEATURE_NAVIGATIONBAR__: r,
    // 是否启用标题栏
    __UNI_FEATURE_PULL_DOWN_REFRESH__: M(e),
    // 是否启用下拉刷新
    __UNI_FEATURE_NAVIGATIONBAR_BUTTONS__: c,
    // 是否启用标题栏按钮
    __UNI_FEATURE_NAVIGATIONBAR_SEARCHINPUT__: u,
    // 是否启用标题栏搜索框
    __UNI_FEATURE_NAVIGATIONBAR_TRANSPARENT__: _
    // 是否启用透明标题栏
  };
  Object.entries(p).forEach(([d, l]) => {
    t[d] = l;
  });
}
const H = "4.57";
function j(e, t = window) {
  const { pagesJson: n = {}, manifestJson: o = {} } = e, { easycom: i = {} } = n, {
    appid: s = "",
    name: a = "",
    versionCode: r = "",
    versionName: c = ""
  } = o, u = n.globalStyle, _ = o.h5?.router || {};
  t.__uniConfig = {
    easycom: i,
    globalStyle: {
      ...V(u),
      navigationBar: E(u),
      isNVue: !1
    },
    compilerVersion: H,
    appId: s,
    appName: a,
    appVersion: c,
    appVersionCode: String(r),
    async: {
      loading: "AsyncLoading",
      error: "AsyncError",
      delay: 200,
      timeout: 6e4,
      suspensible: !0,
      ...o.h5?.async || {}
    },
    debug: !1,
    networkTimeout: {
      request: 6e4,
      connectSocket: 6e4,
      uploadFile: 6e4,
      downloadFile: 6e4,
      ...o.networkTimeout || {}
    },
    sdkConfigs: {},
    nvue: {
      "flex-direction": "column"
    },
    locale: "",
    fallbackLocale: "",
    locales: {},
    router: {
      mode: "hash",
      base: "/",
      assets: "assets",
      routerBase: _.base || "/",
      ..._
    },
    darkmode: !1,
    themeConfig: {},
    tabBar: n.tabBar ? {
      position: "bottom",
      color: "#7A7E83",
      selectedColor: "#3cc51f",
      borderStyle: "black",
      blurEffect: "none",
      fontSize: "10px",
      iconWidth: "24px",
      spacing: "3px",
      height: "50px",
      backgroundColor: "#ffffff",
      selectedIndex: 0,
      shown: !0,
      ...n.tabBar
    } : void 0
    // tabBar: pagesJson.tabBar
  };
}
function $(e, t = window) {
  const {
    UniServiceJSBridge: n,
    UniViewJSBridge: o,
    getApp: i,
    uni: s,
    getCurrentPages: a,
    upx2px: r,
    setupPage: c
  } = e;
  t.UniServiceJSBridge = n, t.UniViewJSBridge = o, t.getApp = i, t.uni = s, t.wx = s, t.getCurrentPages = a, t.upx2px = r, t.__setupPage = (u) => c(u);
}
function K(e, t, n) {
  const { openBlock: o, createBlock: i, withCtx: s, createVNode: a } = e, { PageComponent: r, setupPage: c, getApp: u } = t;
  return {
    mpType: "page",
    async setup() {
      const p = u()?.$route?.query || {}, { loader: d, component: l } = n, T = d ? await d(n) : l || {};
      return () => (o(), i(r, null, {
        page: s(() => [
          a(
            c(T),
            Object.assign({}, p, { ref: "page" }),
            null,
            512
          )
        ]),
        _: 1
      }));
    }
  };
}
function Y(e, t, n) {
  const { path: o, style: i = {}, meta: s = {}, home: a, id: r } = t, c = e.tabBar?.list || [], u = c.findIndex((l) => l.pagePath === o || l.pagePath?.endsWith(r)), _ = c[u], p = !!_, d = n === 0;
  return {
    isTabBar: p,
    tabBarIndex: u,
    isQuit: a ?? d,
    isEntry: a ?? d,
    navigationBar: {
      type: "default",
      ...E(i)
    },
    isNVue: !1,
    route: _ ? _.pagePath : o,
    ...s
  };
}
function q(e, t, n, o, i = window) {
  const s = n.map((a, r) => {
    const c = Y(o, a, r), u = K(e, t, a), { path: _ } = a;
    return {
      path: _,
      alias: _,
      meta: c,
      component: {
        render() {
          return e.h(e.Suspense, [e.h(u)]);
        }
      }
    };
  });
  i.__uniRoutes = s, i.__uniLayout = {};
}
function z(e, t, n = window) {
  S(n, String(e), t);
}
function X(e, t) {
  if (!t) return;
  [
    "View",
    "ScrollView",
    "Swiper",
    "MovableArea",
    "MovableView",
    "CoverView",
    "CoverImage",
    "Icon",
    "Text",
    "RichText",
    "Progress",
    "Button",
    "CheckboxGroup",
    "Checkbox",
    "Editor",
    "Form",
    "Input",
    "Label",
    "Picker",
    "PickerView",
    "RadioGroup",
    "Radio",
    "Slider",
    "Switch",
    "Textarea",
    "Navigator",
    "Image",
    "Video",
    "Map",
    "Canvas",
    "WebView",
    "PickerViewColumn",
    "ResizeSensor",
    "SwiperItem"
  ].forEach((o) => {
    const i = t[o];
    e.component(o, i);
  });
}
function ie(e) {
  const t = x(e), {
    Vue: n,
    App: o,
    UniH5: i,
    routes: s = [],
    pagesJson: a = {},
    manifestJson: r = {},
    window: c,
    css: u = ""
  } = t;
  if (!i)
    return n.createApp(o);
  const { plugin: _, setupApp: p } = i;
  J(t, c), j(t, c), $(i, c), q(n, i, s, a, c), z(r.appid || Date.now(), u, c);
  const d = n.createApp(p(o));
  return d.use(X, i), d.use(_), d;
}
function ae(e, t) {
  const n = {};
  return Object.entries(e).forEach(([o, i]) => {
    N.includes(o) && i && (n[o] = t(i));
  }), n;
}
function m(e, t) {
  return async () => {
    const n = await e.getDsl(t);
    if (n) {
      const { renderer: o } = e.createDslRenderer(n);
      return o;
    }
    return null;
  };
}
async function se(e, t = !1, n = f) {
  const o = e.project?.pages || [], i = [];
  for (const a of o) {
    const r = e.project?.homepage === a.id;
    i.push({
      id: a.id,
      path: `${n}/${a.id}`,
      loader: m(e, a.id),
      style: {
        navigationBarTitleText: a.title,
        ...a.style
      },
      needLogin: a.needLogin,
      home: r
    });
  }
  const s = i.find((a) => !!a.home) || i[0];
  if (s) {
    const a = n === f ? "/" : n;
    i.unshift({
      ...s,
      path: a
    });
  }
  if (t) {
    const a = e.project?.blocks || [];
    for (const r of a)
      i.push({
        id: r.id,
        path: `${n}/${r.id}`,
        loader: m(e, r.id),
        style: {
          navigationStyle: "custom"
        }
      });
  }
  return i;
}
function re() {
  return window.uni?.showLoading && window.uni.showLoading({
    title: "加载中...",
    mask: !0
  }), {
    close: () => {
      window.uni?.hideLoading && window.uni.hideLoading();
    }
  };
}
async function Z(e, t = "", n = "warning") {
  return window.uni?.showModal ? window.uni.showModal({
    title: t,
    content: e,
    showCancel: !1
  }) : Promise.reject(new Error("window.uni.showModal is undefined"));
}
async function ce(e) {
  return Z(e);
}
export {
  N as APP_LIFE_CYCLE,
  h as COMPONENT_LIFE_CYCLES_LIST,
  v as MANIFEST_JSON,
  L as PAGES_JSON,
  ne as PAGE_LIFE_CYCLES_LIST,
  f as ROUTE_PAGE_BASE_PATH,
  ce as alert,
  ae as createUniAppComponent,
  m as createUniLoader,
  se as createUniRoutes,
  oe as getFileId,
  V as getGobalStyle,
  E as getNavigationBar,
  z as injectUniCSS,
  j as injectUniConfig,
  J as injectUniFeatures,
  $ as injectUniGlobal,
  q as injectUniRoutes,
  X as install,
  re as loading,
  x as mergeOptions,
  g as navigationBarMaps,
  Z as notify,
  ie as setupUniApp,
  te as toKebabCase,
  ee as version
};
