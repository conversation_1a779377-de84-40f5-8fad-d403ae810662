{"name": "@vtj/uni-app", "private": false, "version": "0.12.70", "type": "module", "sideEffects": false, "keywords": ["低代码引擎", "LowCode Engine", "Vue3低代码", "低代码渲染器", "低代码设计器", "代码生成器", "代码可视化"], "description": "VTJ 是一款基于 Vue3 + Typescript 的低代码页面可视化设计器。内置低代码引擎、渲染器和代码生成器，面向前端开发者，开箱即用。 无缝嵌入本地开发工程，不改变前端开发流程和编码习惯。", "repository": {"type": "git", "url": "https://gitee.com/newgateway/vtj.git"}, "homepage": "https://gitee.com/newgateway/vtj", "author": "chenhuachun", "license": "MIT", "scripts": {"check": "vue-tsc --noEmit", "build": "npm run check"}, "dependencies": {"@vtj/core": "workspace:~", "@vtj/renderer": "workspace:~", "@vtj/uni": "workspace:~", "@vtj/utils": "workspace:~"}, "devDependencies": {"@vtj/cli": "workspace:~"}, "exports": {"./src/index.scss": "./src/index.scss", ".": {"types": "./src/index.ts", "import": "./src/index.ts", "require": "./src/index.ts"}, "./*": ["./*"]}, "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "files": ["src"], "gitHead": "d03843144f07c2d98c1e0c72c8c6eb1117c01722", "publishConfig": {"access": "public"}}