import { Controller, Post, Body, Get } from '@nestjs/common';
import { SettingsService } from './settings.service';
import { SettingDto } from './dto/setting.dto';

@Controller('settings')
export class SettingsController {
  constructor(private readonly service: SettingsService) {}

  @Post()
  save(@Body() dto: SettingDto) {
    return this.service.save(dto);
  }

  @Get()
  get() {
    return this.service.get();
  }

  @Get('safe')
  getSafe() {
    return this.service.getSafeSettings();
  }
}
