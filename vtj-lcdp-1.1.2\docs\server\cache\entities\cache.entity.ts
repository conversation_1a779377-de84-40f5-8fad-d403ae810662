import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn
} from 'typeorm';

@Entity({
  name: 'caches',
  comment: '缓存记录',
  orderBy: {
    createdAt: 'DESC'
  }
})
export class Cache {
  @PrimaryGeneratedColumn('increment', { comment: '递增主键' })
  id: number;

  /**
   * 缓存 key
   */
  @Column({ comment: '缓存键名', unique: true })
  key: string;

  /**
   * 缓存值
   */
  @Column('json', { comment: '值' })
  value: any;
  /**
   * 过期时间
   */
  @Column({ default: 0, comment: '过期时间，单位毫秒，0 为永不过期' })
  expired: number;

  /**
   * 命中次数
   */
  @Column({ default: 0, comment: '命中次数' })
  hits: number;

  /**
   *  数据插入时间
   */
  @CreateDateColumn({ name: 'created_at', comment: '数据插入时间' })
  createdAt: Date;
}
