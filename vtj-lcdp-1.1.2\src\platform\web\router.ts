import { createRouter, createWebHashHistory } from 'vue-router';
import Page from './Page.vue';
import NotFound from './NotFound.vue';
const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      component: NotFound
    },
    {
      path: '/:app',
      component: Page
    },
    {
      path: '/:app/page/:id',
      component: Page
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: NotFound
    }
  ]
});

export default router;
