import { computed } from 'vue';
import { useAccess } from '@vtj/renderer';
import type { LowCodeAppVO } from '@/shared';

export function usePrefix(data?: LowCodeAppVO | null) {
  const access = useAccess();

  const prefixReg = new RegExp(`^${access.getData()?.name}\.`);

  const hasPrefix = computed(() => {
    const name = access.getData()?.name;
    if (data && name) {
      return prefixReg.test(data.name);
    }
    return true;
  });

  const prefix = computed(() => {
    const name = access.getData()?.name;
    return name ? `${name}.` : '';
  });

  const skipPrefix = (name: string) => {
    return hasPrefix.value ? name.replace(prefixReg, '') : name;
  };

  const withPrefix = (name: string) => {
    return hasPrefix.value ? prefix.value + name : name;
  };

  return {
    prefixReg,
    hasPrefix,
    prefix,
    skipPrefix,
    withPrefix
  };
}
