import {
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn
} from 'typeorm';

export class BaseEntity {
  /**
   * 主键id
   */
  @PrimaryGeneratedColumn('uuid', { comment: '唯一标识' })
  id: string;

  /**
   *  数据插入时间
   */
  @CreateDateColumn({ name: 'created_at', comment: '数据插入时间' })
  createdAt: Date;

  /**
   *  数据更新时间
   */
  @UpdateDateColumn({ name: 'updated_at', comment: '数据更新时间' })
  updatedAt: Date;

  /**
   * 逻辑删除时间
   */
  @DeleteDateColumn({
    name: 'deleted_at',
    select: false,
    comment: '逻辑删除时间'
  })
  deletedAt: Date;
}
