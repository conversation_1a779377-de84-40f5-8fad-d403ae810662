/*
 Navicat Premium Dump SQL

 Source Server         : vtj_dev
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : test.vtj.pro
 Source Schema         : vtj_dev

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 24/06/2025 08:12:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for apps
-- ----------------------------
DROP TABLE IF EXISTS `apps`;
CREATE TABLE `apps` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称编码',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称描述',
  `order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Logo URL',
  `platform` enum('Web','H5','UniApp') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'Web' COMMENT '平台类型',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
  `is_base` tinyint NOT NULL DEFAULT '0' COMMENT '是否基础应用',
  `notes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'UserId',
  `is_access` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启权限控制',
  `scope` enum('public','private','protected') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'protected' COMMENT '应用访问权限范围',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_c1a24df1d51c2748d97561b77d` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='应用表';

-- ----------------------------
-- Table structure for chats
-- ----------------------------
DROP TABLE IF EXISTS `chats`;
CREATE TABLE `chats` (
  `id` varchar(36) NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `topic_id` varchar(255) NOT NULL COMMENT '话题Id',
  `user_id` varchar(255) NOT NULL COMMENT '用户Id',
  `user_name` varchar(255) NOT NULL COMMENT '用户名',
  `prompt` text NOT NULL COMMENT '用户提示词',
  `status` enum('Pending','Success','Failed','Error','Canceled') NOT NULL COMMENT '对话状态',
  `content` text COMMENT '回复内容',
  `reasoning` text COMMENT '推理内容',
  `dsl` json DEFAULT NULL COMMENT '代码DSL',
  `tokens` int NOT NULL DEFAULT '0' COMMENT '消耗token数',
  `message` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `thinking` int NOT NULL DEFAULT '0' COMMENT '深度思考用时，单位秒',
  PRIMARY KEY (`id`),
  KEY `IDX_ccc4d26d9ad10e907a76055df3` (`topic_id`),
  KEY `IDX_b6c92d818d42e3e298e84d9441` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='对话记录';

-- ----------------------------
-- Table structure for dicts
-- ----------------------------
DROP TABLE IF EXISTS `dicts`;
CREATE TABLE `dicts` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称编码',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典名称描述',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '字典值',
  `order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `notes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分组',
  PRIMARY KEY (`id`),
  UNIQUE KEY `IDX_cfa42c049dd860132e65104b4e` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据字典';

-- ----------------------------
-- Table structure for schemas
-- ----------------------------
DROP TABLE IF EXISTS `schemas`;
CREATE TABLE `schemas` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储Key',
  `type` enum('project','material','file','history','history-item') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据类型',
  `app` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `content` json NOT NULL COMMENT '数据内容',
  PRIMARY KEY (`id`),
  KEY `IDX_f36300bbbcd671ad81ae5843f7` (`app`),
  KEY `IDX_123bfad70095e093be1c0a7abd` (`name`),
  KEY `IDX_8b7a503ca9ada735ada8608658` (`type`),
  KEY `IDX_5b4373e6a260d2608608241fd1` (`app`,`name`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='低代码DSL';

-- ----------------------------
-- Table structure for settings
-- ----------------------------
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `id` varchar(36) NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `mode` enum('1','2','3') NOT NULL DEFAULT '1' COMMENT '运营模式',
  `limit` int NOT NULL DEFAULT '0' COMMENT '免费体验次数',
  `prompt_template` text COMMENT '系统提示词模版',
  `pay_qr` varchar(255) DEFAULT NULL COMMENT '支付二维码',
  `contact_qr` varchar(255) DEFAULT NULL COMMENT '联系人二维码',
  `max` decimal(4,1) NOT NULL DEFAULT '0.0' COMMENT '月最大token数量，单位百万',
  `price` decimal(6,2) NOT NULL DEFAULT '0.00' COMMENT '月订阅价格',
  `image_prompt_template` text COMMENT '图片识别系统提示词模版',
  `group_qr` varchar(255) DEFAULT NULL COMMENT '微信群二维码',
  `image_model` varchar(255) DEFAULT NULL COMMENT '图片识别大模型名称',
  `image_api_key` varchar(255) DEFAULT NULL COMMENT '图片识别大模型apiKey',
  `mail_pass` varchar(255) DEFAULT NULL COMMENT '邮件服务登登录码',
  `json_prompt_template` text COMMENT '元数据识别系统提示词模版',
  `json_model` varchar(255) DEFAULT NULL COMMENT '元数据识别大模型名称',
  `json_api_key` varchar(255) DEFAULT NULL COMMENT '元数据识别大模型apiKey',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='AI配置信息';

-- ----------------------------
-- Table structure for template_dsl
-- ----------------------------
DROP TABLE IF EXISTS `template_dsl`;
CREATE TABLE `template_dsl` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联id',
  `version` int NOT NULL DEFAULT '1' COMMENT '自增版本号',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '语义化版本号：0.0.0',
  `changelog` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '更新日志',
  `content` json NOT NULL COMMENT '内容',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='DSL';

-- ----------------------------
-- Table structure for templates
-- ----------------------------
DROP TABLE IF EXISTS `templates`;
CREATE TABLE `templates` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称编码',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板描述',
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板分类',
  `cover` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图片',
  `vip` tinyint NOT NULL DEFAULT '0' COMMENT '是否vip',
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '简介详情',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '作者',
  `share` tinyint NOT NULL DEFAULT '0' COMMENT '是否公开',
  `installed` int NOT NULL DEFAULT '0' COMMENT '安装次数',
  `latest` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最新版本',
  `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'UserId',
  `platform` enum('Web','H5','UniApp') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'Web' COMMENT '支持平台',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板';

-- ----------------------------
-- Table structure for topics
-- ----------------------------
DROP TABLE IF EXISTS `topics`;
CREATE TABLE `topics` (
  `id` varchar(36) NOT NULL COMMENT '唯一标识',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '数据插入时间',
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '数据更新时间',
  `deleted_at` datetime(6) DEFAULT NULL COMMENT '逻辑删除时间',
  `model` varchar(255) NOT NULL COMMENT '大模型名称',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `prompt` text NOT NULL COMMENT '用户提示词',
  `content` text NOT NULL COMMENT '系统提示词',
  `is_hot` tinyint NOT NULL DEFAULT '0' COMMENT '热门',
  `project_id` varchar(255) NOT NULL COMMENT '项目标识',
  `project_name` varchar(255) NOT NULL COMMENT '项目名',
  `user_id` varchar(255) NOT NULL COMMENT '用户标识',
  `user_name` varchar(255) NOT NULL COMMENT '用户名',
  `app_id` varchar(255) NOT NULL COMMENT '应用标识',
  `file_id` varchar(255) NOT NULL COMMENT '文件Id',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `platform` enum('web','h5','uniapp') NOT NULL COMMENT '平台',
  `image` varchar(255) DEFAULT NULL COMMENT '图片文件',
  `type` enum('text','image','json') NOT NULL DEFAULT 'text' COMMENT '话题类型',
  `json` varchar(255) DEFAULT NULL COMMENT 'JSON文件',
  `data_type` enum('sketch','figma','unknown') DEFAULT NULL COMMENT '数据类型',
  PRIMARY KEY (`id`),
  KEY `IDX_ee45f1ebeb8832247663976545` (`is_hot`),
  KEY `IDX_9aafb0be7561598870bfa081d4` (`file_id`),
  KEY `IDX_5b2338c0b0e571339d9b1aeff4` (`platform`),
  KEY `IDX_d1433a34680e95734091a98e06` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='话题';

SET FOREIGN_KEY_CHECKS = 1;
