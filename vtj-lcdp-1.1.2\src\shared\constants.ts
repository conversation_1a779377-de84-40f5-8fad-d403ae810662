import { ACCESS, REMOTE as defaultRemote } from '@vtj/renderer';

// VTJ官方的远程服务主机, 默认：https://lcdp.vtj.pro
export const REMOTE = process.env.REMOTE || defaultRemote;

// lcdp.vtj.pro 静默登录授权码，登录 https://lcdp.vtj.pro 获取
export const AUTH_CODE = process.env.AUTH_CODE;

// 用户登录信息解密私钥
export const ACCESS_PRIVATE_KEY = ACCESS.privateKey;

// 用户信息存储本地localStorage的键名
export const STORAGE_KEY = ACCESS.storageKey;

// 站点部署目录
export const BASE_PATH = process.env.BASE_PATH || '/';

// 内置物料库的目录路径
export const MATERIAL_PATH = BASE_PATH;

// 本地登录页面路径
export const AUTH_PATH = `${BASE_PATH}#/auth`;
