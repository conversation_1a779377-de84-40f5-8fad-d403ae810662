import {
  __esm,
  __toCommonJS
} from "./chunk-LK32TJAX.js";

// vite:dep-pre-bundle:external-conversion:D:/project/work/vtj-kaiyuan/vtj-lcdp-1.1.2/node_modules/vant/lib/style/base.css
var base_exports = {};
import "D:/project/work/vtj-kaiyuan/vtj-lcdp-1.1.2/node_modules/vant/lib/style/base.css";
var init_base = __esm({
  "vite:dep-pre-bundle:external-conversion:D:/project/work/vtj-kaiyuan/vtj-lcdp-1.1.2/node_modules/vant/lib/style/base.css"() {
  }
});

// vite:dep-pre-bundle:external-conversion:D:/project/work/vtj-kaiyuan/vtj-lcdp-1.1.2/node_modules/vant/lib/empty/index.css
var empty_exports = {};
import "D:/project/work/vtj-kaiyuan/vtj-lcdp-1.1.2/node_modules/vant/lib/empty/index.css";
var init_empty = __esm({
  "vite:dep-pre-bundle:external-conversion:D:/project/work/vtj-kaiyuan/vtj-lcdp-1.1.2/node_modules/vant/lib/empty/index.css"() {
  }
});

// node_modules/vant/lib/empty/style/index.js
init_base();
init_empty();
//# sourceMappingURL=vant_lib_empty_style.js.map
