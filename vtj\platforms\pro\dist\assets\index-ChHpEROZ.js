var N=Object.defineProperty;var v=Object.getOwnPropertySymbols;var V=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable;var P=(o,e,t)=>e in o?N(o,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):o[e]=t,y=(o,e)=>{for(var t in e||(e={}))V.call(e,t)&&P(o,t,e[t]);if(v)for(var t of v(e))W.call(e,t)&&P(o,t,e[t]);return o};var h=(o,e,t)=>new Promise((_,r)=>{var l=n=>{try{i(t.next(n))}catch(c){r(c)}},a=n=>{try{i(t.throw(n))}catch(c){r(c)}},i=n=>n.done?_(n.value):Promise.resolve(n.value).then(l,a);i((t=t.apply(o,e)).next())});import{useRoute as z}from"./vue-router-CezURGfh.js";import{s as I}from"./@vueuse-WO_0ftym.js";import{b as J,j as U,v as D,y as G,h as C,q as K,o as E}from"./Editor-Dgw5r9tb-C0tzAbRO.js";import"./element-plus-COProxbp.js";import"./vxe-Bet8YtVU.js";import{E as L,a as S,n as A,l as M}from"./utils-Bopp5ewb.js";import{d as O,e as Q,az as B,f as X,c as Y,o as Z}from"./vue-ipWmmxHk.js";import{_ as ee}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./monaco-editor-B8sWZqMY.js";import"./@element-plus-icons-vue-0BR09xN9.js";import"./html2canvas-CIV5arX1.js";import"./mockjs-DG3OW7C0.js";import"./dayjs-DO-COJPZ.js";import"./marked-BQZxLJfc.js";import"./lodash-es-BL-d_OSa.js";import"./shared-Bnc4f-Fv.js";const te=O({__name:"index",setup(o){return h(this,null,function*(){let e,t;const _=z(),r=Q(),l=new J(U(A)),a=([e,t]=B(()=>l.getExtension().catch(()=>null)),e=yield e,t(),e||{}),i=D({loading:M,notify:A,useTitle:I,alert:S,access:a==null?void 0:a.access}),n=G(y({alert:S},a==null?void 0:a.__ACCESS__)),c=a?([e,t]=B(()=>new L(a).load()),e=yield e,t(),e):{},{__BASE_PATH__:d="/",history:k="hash",base:$="/",pageRouteName:g="page",remote:q,auth:b,checkVersion:F=!0,enhance:R}=a||{},T=()=>k==="hash",j=s=>s==="/"?"":(s.startsWith("/")&&(s=s.substring(1)),s.endsWith("/")||(s=s+"/"),s);C.set("Switcher",{props:{onClick:s=>{const u=s.platform==="uniapp",p=location.pathname;let m=p===`${d}__vtj__/`?d:p;const f=s.currentFile;if(f&&f.type==="page"&&s.homepage!==f.id){const H=a.pageBasePath?`/${g}`:"",x=`${j($)}${u?`pages${H}`:g}/${f.id}`;m=T()?`${m}#/${x}`:`${m}${x}`}window.open(m,"VTJProject")}}}),C.set("Previewer",{props:{path:(s,u)=>{const p=location.pathname;return u.platform==="uniapp"?`${p}uni/#/pages/${s.id}`:`${p}#/preview/${s.id}`}}});const w=new K(y({container:r,service:l,materialPath:d,pageBasePath:$==="/"?"":$,adapter:i,access:n,remote:q,auth:b,checkVersion:F,enhance:R},c));return w.ready(()=>h(null,null,function*(){yield E(300),w.openFile(_.query.id)})),X(()=>_.query.id,s=>h(null,null,function*(){yield E(300),w.openFile(s)})),(s,u)=>(Z(),Y("div",{class:"designer",ref_key:"container",ref:r},null,512))})}}),ye=ee(te,[["__scopeId","data-v-f24039f1"]]);export{ye as default};
