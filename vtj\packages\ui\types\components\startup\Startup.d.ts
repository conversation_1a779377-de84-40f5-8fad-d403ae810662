import { DefineComponent, ExtractPropTypes, ComponentOptionsMixin, PublicProps, ComponentProvideOptions } from 'vue';
declare const _default: DefineComponent<ExtractPropTypes<{
    name: {
        type: StringConstructor;
        default: string;
    };
    tagline: {
        type: StringConstructor;
        default: string;
    };
    actionText: {
        type: StringConstructor;
        default: string;
    };
    actionLink: {
        type: StringConstructor;
        default: string;
    };
}>, {}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly< ExtractPropTypes<{
    name: {
        type: StringConstructor;
        default: string;
    };
    tagline: {
        type: StringConstructor;
        default: string;
    };
    actionText: {
        type: StringConstructor;
        default: string;
    };
    actionLink: {
        type: StringConstructor;
        default: string;
    };
}>> & Readonly<{}>, {
    name: string;
    tagline: string;
    actionText: string;
    actionLink: string;
}, {}, {}, {}, string, ComponentProvideOptions, true, {}, HTMLDivElement>;
export default _default;
