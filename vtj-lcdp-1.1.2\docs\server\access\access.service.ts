import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull } from 'typeorm';
import { AccessDto } from './dto/access.dto';
import { ApiDto } from './dto/api.dto';
import { Access, Api } from './entities/access.entity';
import { AccessType, arrayToTree, pager, QueryDto } from '../shared';

@Injectable()
export class AccessService {
  constructor(
    @InjectRepository(Access) private repo: Repository<Access>,
    @InjectRepository(Api) private apis: Repository<Api>
  ) {}

  save(dto: AccessDto) {
    const access = new Access();
    return this.repo.save(Object.assign(access, dto));
  }

  findOne(id: string) {
    return this.repo.findOne({
      relations: {
        apis: true
      },
      where: {
        id
      }
    });
  }

  async saveApis(
    accessId: string,
    records: ApiDto[] = [],
    removed: string[] = []
  ) {
    const access = await this.findOne(accessId);
    if (access) {
      if (records.length) {
        const apis = records.map((item) => {
          const api = new Api();
          Object.assign(api, item);
          api.access = access;
          return api;
        });
        this.apis.save(apis);
      }

      if (removed.length) {
        await this.apis.delete({
          id: In(removed)
        });
      }
    }
    return true;
  }

  async findForRoot(dto?: QueryDto) {
    const { page, limit, skip, take } = pager(dto);
    const [list, total] = await this.repo.findAndCount({
      skip,
      take,
      where: {
        type: AccessType.App,
        root: IsNull()
      },
      order: {
        order: 'ASC',
        createdAt: 'ASC'
      }
    });
    return {
      page,
      limit,
      total,
      list
    };
    // return this.repo.find({
    //   relations: {
    //     apis: true
    //   },
    //   where: {
    //     type: AccessType.App,
    //     root: IsNull()
    //   },
    //   order: {
    //     order: 'ASC',
    //     createdAt: 'ASC'
    //   }
    // });
  }

  findByParent(id: string) {
    return this.repo.find({
      relations: {
        apis: true
      },
      where: {
        parentId: id
      },
      order: {
        order: 'ASC'
      }
    });
  }

  async findByRoot(root: string) {
    const rootAccess = await this.getAccessByCode(root);
    const rows = await this.repo.find({
      relations: {
        apis: true
      },
      where: {
        root
      },
      order: {
        order: 'ASC',
        createdAt: 'ASC'
      }
    });
    return arrayToTree(rows || [], 'id', 'parentId', 'children', rootAccess.id);
  }

  async getApis(ids: string[]) {
    const rows = await this.repo.find({
      relations: {
        apis: true
      },
      where: {
        id: In(ids)
      }
    });
    const apis: Api[] = [];
    for (const row of rows) {
      apis.push(...row.apis);
    }
    return apis;
  }

  getAccessByCode(code: string) {
    return this.repo.findOne({
      relations: {
        apis: true
      },
      where: {
        code
      }
    });
  }

  getAccessByCodes(codes: string[]) {
    return this.repo.find({
      relations: {
        apis: true
      },
      where: {
        code: In(codes)
      }
    });
  }

  async isExitCode(code: string) {
    return !!(await this.getAccessByCode(code));
  }

  async remove(code: string) {
    const model = await this.getAccessByCode(code);
    const children = await this.repo.find({
      where: {
        root: code
      },
      order: {
        order: 'ASC'
      }
    });
    if (children.length) {
      const accessIds = children.map((n) => n.id);
      await this.apis.delete(accessIds);
      await this.repo.remove(children);
    }
    return await this.repo.remove(model);
  }
}
