{"name": "demo", "description": "测试出码示例", "private": true, "version": "0.9.0", "type": "module", "scripts": {"setup": "npm install --unsafe-perm --registry=https://registry.npmmirror.com", "clean": "node ./scripts/clean.mjs", "dev": "cross-env ENV_TYPE=local vite", "build": "npm run build:prod", "build:sit": "vue-tsc && cross-env ENV_TYPE=sit vite build", "build:prod": "vue-tsc && cross-env ENV_TYPE=live vite build", "preview": "vite preview"}, "dependencies": {"@vtj/plugin-ckeditor": "~0.2.1", "@vtj/web": "latest", "vue": "~3.5.0", "vue-router": "~4.5.0"}, "devDependencies": {"@vtj/cli": "latest", "@vtj/pro": "latest"}, "vtj": {"history": "hash", "base": "/demo", "pageRouteName": "page"}}