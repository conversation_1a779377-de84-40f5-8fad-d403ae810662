import { <PERSON>er<PERSON><PERSON>um<PERSON>, <PERSON><PERSON><PERSON>ield<PERSON>, <PERSON><PERSON><PERSON>oa<PERSON> } from './types';
import { DefineComponent, Ref, ComponentOptionsMixin, PublicProps, ComponentProvideOptions, ComponentInternalInstance, VNodeProps, AllowedComponentProps, ComponentCustomProps, Slot, ComponentPublicInstance, ComponentOptionsBase, ExtractPropTypes, PropType, VNode, GlobalComponents, GlobalDirectives, DebuggerEvent, nextTick, WatchOptions, WatchStopHandle, ShallowUnwrapRef, ComponentCustomProperties } from 'vue';
import { GridColumns, GridCustomInfo, GridLoader, GridCellRenders, GridEditRenders, GridFilterRenders, GridSortableEvent, GridSortableOptions } from '..';
import { Options } from 'sortablejs';
import { VxeTableProps, VxeGridEventProps, VxeGridSlots, VxeTableDataRow, SlotVNodeType, VxeGridInstance, VxeTable<PERSON>onstructor, VxeGridConstructor } from 'vxe-table';
import { MessageBoxData } from 'element-plus';
import { OnCleanup } from '@vue/reactivity';
export interface Props {
    gridProps?: any;
    formProps?: any;
    columns?: PickerColumns;
    fields?: PickerFields;
    loader?: PickerLoader;
    formModel?: Record<string, any>;
    multiple?: boolean;
    onPick: (rows: any[]) => void;
}
declare const _default: DefineComponent<Props, {
    pick: () => void;
    gridRef: Ref<any, any>;
}, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {}, string, PublicProps, Readonly<Props> & Readonly<{}>, {}, {}, {}, {}, string, ComponentProvideOptions, false, {
    gridRef: ({
        $: ComponentInternalInstance;
        $data: {};
        $props: Partial<{
            auto: boolean;
            resizable: boolean;
            editable: boolean;
            page: number;
            pageSize: number;
            columns: GridColumns;
            rowSortable: boolean | Options;
            columnSortable: boolean | Options;
            customable: boolean;
            pager: boolean;
            pageSizes: number[];
            virtual: boolean;
        }> & Omit<{
            readonly auto: boolean;
            readonly resizable: boolean;
            readonly editable: boolean;
            readonly page: number;
            readonly pageSize: number;
            readonly columns: GridColumns;
            readonly rowSortable: boolean | Options;
            readonly columnSortable: boolean | Options;
            readonly customable: boolean;
            readonly pager: boolean;
            readonly pageSizes: number[];
            readonly virtual: boolean;
            readonly getCustom?: ((id: string) => Promise< GridCustomInfo>) | undefined;
            readonly saveCustom?: ((info: GridCustomInfo) => Promise<any>) | undefined;
            readonly id?: string | undefined;
            readonly loader?: GridLoader | undefined;
            readonly cellRenders?: GridCellRenders | undefined;
            readonly editRenders?: GridEditRenders | undefined;
            readonly filterRenders?: GridFilterRenders | undefined;
            readonly sumFields?: string[] | undefined;
            readonly avgFields?: string[] | undefined;
            readonly sumAllFields?: Record<string, number> | undefined;
            readonly onRowSort?: ((e: GridSortableEvent) => any) | undefined;
            readonly onColumnSort?: ((e: GridSortableEvent) => any) | undefined;
            readonly onCellSelected?: ((params: any) => any) | undefined;
            readonly onEditChange?: ((data: any[]) => any) | undefined;
            readonly onLoaded?: ((rows: any[]) => any) | undefined;
        } & VNodeProps & AllowedComponentProps & ComponentCustomProps, "auto" | "resizable" | "editable" | "page" | "pageSize" | "columns" | "rowSortable" | "columnSortable" | "customable" | "pager" | "pageSizes" | "virtual">;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        } & {
            vxeRef: {
                $props: VxeTableProps<any> & {
                    layouts?: import("vxe-table").VxeGridPropTypes.Layouts;
                    columns?: import("vxe-table").VxeGridPropTypes.Columns<any> | undefined;
                    pagerConfig?: import("vxe-table").VxeGridPropTypes.PagerConfig;
                    proxyConfig?: import("vxe-table").VxeGridPropTypes.ProxyConfig<any> | undefined;
                    toolbarConfig?: import("vxe-table").VxeGridPropTypes.ToolbarConfig;
                    formConfig?: import("vxe-table").VxeGridPropTypes.FormConfig;
                    zoomConfig?: import("vxe-table").VxeGridPropTypes.ZoomConfig;
                } & VxeGridEventProps<any>;
                $slots: VxeGridSlots<any>;
            } | null;
        };
        $slots: Readonly<{
            [name: string]: Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: ((event: "rowSort", e: GridSortableEvent) => void) & ((event: "columnSort", e: GridSortableEvent) => void) & ((event: "cellSelected", params: any) => void) & ((event: "editChange", data: any[]) => void) & ((event: "loaded", rows: any[]) => void);
        $el: any;
        $options: ComponentOptionsBase<Readonly< ExtractPropTypes<{
            id: {
                type: StringConstructor;
            };
            columns: {
                type: PropType<GridColumns>;
                default(): GridColumns;
            };
            loader: {
                type: PropType<GridLoader>;
            };
            rowSortable: {
                type: PropType<boolean | GridSortableOptions>;
                default: boolean;
            };
            columnSortable: {
                type: PropType<boolean | GridSortableOptions>;
                default: boolean;
            };
            customable: {
                type: BooleanConstructor;
            };
            getCustom: {
                type: PropType<(id: string) => Promise< GridCustomInfo>>;
            };
            saveCustom: {
                type: PropType<(info: GridCustomInfo) => Promise<any>>;
            };
            resizable: {
                type: BooleanConstructor;
                default: boolean;
            };
            pager: {
                type: BooleanConstructor;
            };
            page: {
                type: NumberConstructor;
                default: number;
            };
            pageSize: {
                type: NumberConstructor;
                default: number;
            };
            pageSizes: {
                type: PropType<number[]>;
                default: () => number[];
            };
            auto: {
                type: BooleanConstructor;
                default: boolean;
            };
            virtual: {
                type: BooleanConstructor;
                default: boolean;
            };
            cellRenders: {
                type: PropType<GridCellRenders>;
            };
            editRenders: {
                type: PropType<GridEditRenders>;
            };
            filterRenders: {
                type: PropType<GridFilterRenders>;
            };
            editable: {
                type: BooleanConstructor;
                default: boolean;
            };
            sumFields: {
                type: PropType<string[]>;
            };
            avgFields: {
                type: PropType<string[]>;
            };
            sumAllFields: {
                type: PropType<Record<string, number>>;
            };
        }>> & Readonly<{
            onRowSort?: ((e: GridSortableEvent) => any) | undefined;
            onColumnSort?: ((e: GridSortableEvent) => any) | undefined;
            onCellSelected?: ((params: any) => any) | undefined;
            onEditChange?: ((data: any[]) => any) | undefined;
            onLoaded?: ((rows: any[]) => any) | undefined;
        }>, {
            state: {
                [x: string]: any;
                page?: number | undefined;
                pageSize?: number | undefined;
                total?: number | undefined;
                filters?: {
                    column: {
                        property: import("vxe-table").VxeColumnPropTypes.Field;
                        type: import("vxe-table").VxeColumnPropTypes.Type;
                        field: import("vxe-table").VxeColumnPropTypes.Field;
                        title: import("vxe-table").VxeColumnPropTypes.Title;
                        width: import("vxe-table").VxeColumnPropTypes.Width;
                        minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                        maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                        resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                        fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                        align: import("vxe-table").VxeColumnPropTypes.Align;
                        headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                        footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                        showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                        showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                        showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                        className: import("vxe-table").VxeColumnPropTypes.ClassName;
                        headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                        footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                        formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                        sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                        sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                        sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                        filters: {
                            label?: string | number | undefined;
                            value?: any;
                            data?: any;
                            resetValue?: any;
                            checked?: boolean | undefined;
                        }[];
                        filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                        filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                        filterRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                        visible: import("vxe-table").VxeColumnPropTypes.Visible;
                        exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                        footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                        titleHelp: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titlePrefix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titleSuffix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                        };
                        cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                        cellRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                            } | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        editRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                            } | undefined;
                            enabled?: boolean | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            autofocus?: string | undefined;
                            autoselect?: boolean | undefined;
                            defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                                column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                            }) => any) | null | undefined;
                            immediate?: boolean | undefined;
                            content?: string | undefined;
                            placeholder?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        contentRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        params: import("vxe-table").VxeColumnPropTypes.Params;
                        slots: {
                            title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        };
                        id: string;
                        parentId: string;
                        level: number;
                        rowSpan: number;
                        colSpan: number;
                        halfVisible: boolean;
                        defaultVisible: any;
                        defaultFixed: any;
                        checked: boolean;
                        halfChecked: boolean;
                        disabled: boolean;
                        order: import("vxe-table").VxeTablePropTypes.SortOrder;
                        sortTime: number;
                        sortNumber: number;
                        renderSortNumber: number;
                        renderWidth: number;
                        renderHeight: number;
                        resizeWidth: number;
                        model: {
                            update: boolean;
                            value: any;
                        };
                        children: /*elided*/ any[];
                        renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                        renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                        renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                        renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                        getTitle: () => string;
                        getKey: () => string;
                    };
                    field: import("vxe-table").VxeColumnPropTypes.Field;
                    property: import("vxe-table").VxeColumnPropTypes.Field;
                    values: any[];
                    datas: any[];
                }[] | undefined;
                sorts?: {
                    column: {
                        property: import("vxe-table").VxeColumnPropTypes.Field;
                        type: import("vxe-table").VxeColumnPropTypes.Type;
                        field: import("vxe-table").VxeColumnPropTypes.Field;
                        title: import("vxe-table").VxeColumnPropTypes.Title;
                        width: import("vxe-table").VxeColumnPropTypes.Width;
                        minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                        maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                        resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                        fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                        align: import("vxe-table").VxeColumnPropTypes.Align;
                        headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                        footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                        showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                        showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                        showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                        className: import("vxe-table").VxeColumnPropTypes.ClassName;
                        headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                        footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                        formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                        sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                        sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                        sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                        filters: {
                            label?: string | number | undefined;
                            value?: any;
                            data?: any;
                            resetValue?: any;
                            checked?: boolean | undefined;
                        }[];
                        filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                        filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                        filterRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                        visible: import("vxe-table").VxeColumnPropTypes.Visible;
                        exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                        footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                        titleHelp: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titlePrefix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                            message?: string | undefined;
                        };
                        titleSuffix: {
                            useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                            content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                            enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                            theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                            icon?: string | undefined;
                        };
                        cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                        cellRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                            } | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            content?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        editRender: {
                            events?: {
                                [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                            } | undefined;
                            enabled?: boolean | undefined;
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            autofocus?: string | undefined;
                            autoselect?: boolean | undefined;
                            defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                                column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                            }) => any) | null | undefined;
                            immediate?: boolean | undefined;
                            content?: string | undefined;
                            placeholder?: string | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        contentRender: {
                            options?: any[] | undefined;
                            optionProps?: {
                                value?: string | undefined;
                                label?: string | undefined;
                                disabled?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            optionGroups?: any[] | undefined;
                            optionGroupProps?: {
                                options?: string | undefined;
                                label?: string | undefined;
                                key?: string | undefined;
                            } | undefined;
                            name?: string | undefined;
                            props?: {
                                [key: string]: any;
                            } | undefined;
                            attrs?: {
                                [key: string]: any;
                            } | undefined;
                            events?: {
                                [key: string]: (...args: any[]) => any;
                            } | undefined;
                            children?: any[] | undefined;
                            cellType?: "string" | "number" | undefined;
                        };
                        params: import("vxe-table").VxeColumnPropTypes.Params;
                        slots: {
                            title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                            icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        };
                        id: string;
                        parentId: string;
                        level: number;
                        rowSpan: number;
                        colSpan: number;
                        halfVisible: boolean;
                        defaultVisible: any;
                        defaultFixed: any;
                        checked: boolean;
                        halfChecked: boolean;
                        disabled: boolean;
                        order: import("vxe-table").VxeTablePropTypes.SortOrder;
                        sortTime: number;
                        sortNumber: number;
                        renderSortNumber: number;
                        renderWidth: number;
                        renderHeight: number;
                        resizeWidth: number;
                        model: {
                            update: boolean;
                            value: any;
                        };
                        children: /*elided*/ any[];
                        renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                        renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                        renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                        renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                        getTitle: () => string;
                        getKey: () => string;
                    };
                    field: import("vxe-table").VxeColumnPropTypes.Field;
                    property: import("vxe-table").VxeColumnPropTypes.Field;
                    order: import("vxe-table").VxeTablePropTypes.SortOrder;
                    sortTime: number;
                }[] | undefined;
                form?: Record<string, any> | undefined;
            };
            load: (reset?: boolean) => Promise<void>;
            search: (reset?: boolean) => void;
            vxeRef: Ref< VxeGridInstance | undefined, VxeGridInstance | undefined>;
            rowSortable: Ref<any, any>;
            columnSortable: Ref<any[], any[]>;
            insertActived: (record?: any, row?: any) => Promise<void>;
            validate: () => Promise<import("vxe-table").VxeTableDefines.ValidatorErrorMapParams<any> | undefined>;
            getSelected: () => any;
            remove: (rows: any) => Promise< MessageBoxData | undefined>;
            getRows: () => any[];
            setActived: (row: any) => Promise<void | undefined>;
            doLayout: () => void;
            getRecords: () => {
                insertRecords: any[];
                removeRecords: any[];
                updateRecords: any[];
                pendingRecords: any[];
            } | undefined;
            setSelectCell: (row?: any, column?: any) => void;
            $vtjDynamicSlots: () => string[];
        }, {}, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            rowSort: (e: GridSortableEvent) => any;
            columnSort: (e: GridSortableEvent) => any;
            cellSelected: (params: any) => any;
            editChange: (data: any[]) => any;
            loaded: (rows: any[]) => any;
        }, string, {
            auto: boolean;
            resizable: boolean;
            editable: boolean;
            page: number;
            pageSize: number;
            columns: GridColumns;
            rowSortable: boolean | Options;
            columnSortable: boolean | Options;
            customable: boolean;
            pager: boolean;
            pageSizes: number[];
            virtual: boolean;
        }, {}, string, {}, GlobalComponents, GlobalDirectives, string, ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            renderTriggered?: ((e: DebuggerEvent) => void) | ((e: DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, OnCleanup]) => any : (...args: [any, any, OnCleanup]) => any, options?: WatchOptions): WatchStopHandle;
    } & Readonly<{
        auto: boolean;
        resizable: boolean;
        editable: boolean;
        page: number;
        pageSize: number;
        columns: GridColumns;
        rowSortable: boolean | Options;
        columnSortable: boolean | Options;
        customable: boolean;
        pager: boolean;
        pageSizes: number[];
        virtual: boolean;
    }> & Omit<Readonly< ExtractPropTypes<{
        id: {
            type: StringConstructor;
        };
        columns: {
            type: PropType<GridColumns>;
            default(): GridColumns;
        };
        loader: {
            type: PropType<GridLoader>;
        };
        rowSortable: {
            type: PropType<boolean | GridSortableOptions>;
            default: boolean;
        };
        columnSortable: {
            type: PropType<boolean | GridSortableOptions>;
            default: boolean;
        };
        customable: {
            type: BooleanConstructor;
        };
        getCustom: {
            type: PropType<(id: string) => Promise< GridCustomInfo>>;
        };
        saveCustom: {
            type: PropType<(info: GridCustomInfo) => Promise<any>>;
        };
        resizable: {
            type: BooleanConstructor;
            default: boolean;
        };
        pager: {
            type: BooleanConstructor;
        };
        page: {
            type: NumberConstructor;
            default: number;
        };
        pageSize: {
            type: NumberConstructor;
            default: number;
        };
        pageSizes: {
            type: PropType<number[]>;
            default: () => number[];
        };
        auto: {
            type: BooleanConstructor;
            default: boolean;
        };
        virtual: {
            type: BooleanConstructor;
            default: boolean;
        };
        cellRenders: {
            type: PropType<GridCellRenders>;
        };
        editRenders: {
            type: PropType<GridEditRenders>;
        };
        filterRenders: {
            type: PropType<GridFilterRenders>;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        sumFields: {
            type: PropType<string[]>;
        };
        avgFields: {
            type: PropType<string[]>;
        };
        sumAllFields: {
            type: PropType<Record<string, number>>;
        };
    }>> & Readonly<{
        onRowSort?: ((e: GridSortableEvent) => any) | undefined;
        onColumnSort?: ((e: GridSortableEvent) => any) | undefined;
        onCellSelected?: ((params: any) => any) | undefined;
        onEditChange?: ((data: any[]) => any) | undefined;
        onLoaded?: ((rows: any[]) => any) | undefined;
    }>, "search" | "load" | "state" | "remove" | "setSelectCell" | "validate" | "vxeRef" | ("auto" | "resizable" | "editable" | "page" | "pageSize" | "columns" | "rowSortable" | "columnSortable" | "customable" | "pager" | "pageSizes" | "virtual") | "doLayout" | "insertActived" | "getSelected" | "getRows" | "setActived" | "getRecords" | "$vtjDynamicSlots"> & ShallowUnwrapRef<{
        state: {
            [x: string]: any;
            page?: number | undefined;
            pageSize?: number | undefined;
            total?: number | undefined;
            filters?: {
                column: {
                    property: import("vxe-table").VxeColumnPropTypes.Field;
                    type: import("vxe-table").VxeColumnPropTypes.Type;
                    field: import("vxe-table").VxeColumnPropTypes.Field;
                    title: import("vxe-table").VxeColumnPropTypes.Title;
                    width: import("vxe-table").VxeColumnPropTypes.Width;
                    minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                    maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                    resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                    fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                    align: import("vxe-table").VxeColumnPropTypes.Align;
                    headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                    footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                    showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                    showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                    showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                    className: import("vxe-table").VxeColumnPropTypes.ClassName;
                    headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                    footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                    formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                    sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                    sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                    sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                    filters: {
                        label?: string | number | undefined;
                        value?: any;
                        data?: any;
                        resetValue?: any;
                        checked?: boolean | undefined;
                    }[];
                    filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                    filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                    filterRender: {
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        content?: string | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        events?: {
                            [key: string]: (...args: any[]) => any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                    visible: import("vxe-table").VxeColumnPropTypes.Visible;
                    exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                    footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                    titleHelp: {
                        useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                        content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                        enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                        theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                        icon?: string | undefined;
                        message?: string | undefined;
                    };
                    titlePrefix: {
                        useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                        content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                        enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                        theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                        icon?: string | undefined;
                        message?: string | undefined;
                    };
                    titleSuffix: {
                        useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                        content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                        enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                        theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                        icon?: string | undefined;
                    };
                    cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                    cellRender: {
                        events?: {
                            [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                        } | undefined;
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        content?: string | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    editRender: {
                        events?: {
                            [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                        } | undefined;
                        enabled?: boolean | undefined;
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        autofocus?: string | undefined;
                        autoselect?: boolean | undefined;
                        defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                            column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                        }) => any) | null | undefined;
                        immediate?: boolean | undefined;
                        content?: string | undefined;
                        placeholder?: string | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    contentRender: {
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        events?: {
                            [key: string]: (...args: any[]) => any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    params: import("vxe-table").VxeColumnPropTypes.Params;
                    slots: {
                        title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                    };
                    id: string;
                    parentId: string;
                    level: number;
                    rowSpan: number;
                    colSpan: number;
                    halfVisible: boolean;
                    defaultVisible: any;
                    defaultFixed: any;
                    checked: boolean;
                    halfChecked: boolean;
                    disabled: boolean;
                    order: import("vxe-table").VxeTablePropTypes.SortOrder;
                    sortTime: number;
                    sortNumber: number;
                    renderSortNumber: number;
                    renderWidth: number;
                    renderHeight: number;
                    resizeWidth: number;
                    model: {
                        update: boolean;
                        value: any;
                    };
                    children: /*elided*/ any[];
                    renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                    renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                    renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                    renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                    getTitle: () => string;
                    getKey: () => string;
                };
                field: import("vxe-table").VxeColumnPropTypes.Field;
                property: import("vxe-table").VxeColumnPropTypes.Field;
                values: any[];
                datas: any[];
            }[] | undefined;
            sorts?: {
                column: {
                    property: import("vxe-table").VxeColumnPropTypes.Field;
                    type: import("vxe-table").VxeColumnPropTypes.Type;
                    field: import("vxe-table").VxeColumnPropTypes.Field;
                    title: import("vxe-table").VxeColumnPropTypes.Title;
                    width: import("vxe-table").VxeColumnPropTypes.Width;
                    minWidth: import("vxe-table").VxeColumnPropTypes.MinWidth;
                    maxWidth: import("vxe-table").VxeColumnPropTypes.MaxWidth;
                    resizable: import("vxe-table").VxeColumnPropTypes.Resizable;
                    fixed: import("vxe-table").VxeColumnPropTypes.Fixed;
                    align: import("vxe-table").VxeColumnPropTypes.Align;
                    headerAlign: import("vxe-table").VxeColumnPropTypes.HeaderAlign;
                    footerAlign: import("vxe-table").VxeColumnPropTypes.FooterAlign;
                    showOverflow: import("vxe-table").VxeColumnPropTypes.ShowOverflow;
                    showHeaderOverflow: import("vxe-table").VxeColumnPropTypes.ShowHeaderOverflow;
                    showFooterOverflow: import("vxe-table").VxeColumnPropTypes.ShowFooterOverflow;
                    className: import("vxe-table").VxeColumnPropTypes.ClassName;
                    headerClassName: import("vxe-table").VxeColumnPropTypes.HeaderClassName;
                    footerClassName: import("vxe-table").VxeColumnPropTypes.FooterClassName;
                    formatter: import("vxe-table").VxeColumnPropTypes.Formatter<VxeTableDataRow>;
                    sortable: import("vxe-table").VxeColumnPropTypes.Sortable;
                    sortBy: import("vxe-table").VxeColumnPropTypes.SortBy;
                    sortType: import("vxe-table").VxeColumnPropTypes.SortType;
                    filters: {
                        label?: string | number | undefined;
                        value?: any;
                        data?: any;
                        resetValue?: any;
                        checked?: boolean | undefined;
                    }[];
                    filterMultiple: import("vxe-table").VxeColumnPropTypes.FilterMultiple;
                    filterMethod: import("vxe-table").VxeColumnPropTypes.FilterMethod<VxeTableDataRow>;
                    filterRender: {
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        content?: string | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        events?: {
                            [key: string]: (...args: any[]) => any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    treeNode: import("vxe-table").VxeColumnPropTypes.TreeNode;
                    visible: import("vxe-table").VxeColumnPropTypes.Visible;
                    exportMethod: import("vxe-table").VxeColumnPropTypes.ExportMethod<VxeTableDataRow>;
                    footerExportMethod: import("vxe-table").VxeColumnPropTypes.FooterExportMethod;
                    titleHelp: {
                        useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                        content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                        enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                        theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                        icon?: string | undefined;
                        message?: string | undefined;
                    };
                    titlePrefix: {
                        useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                        content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                        enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                        theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                        icon?: string | undefined;
                        message?: string | undefined;
                    };
                    titleSuffix: {
                        useHTML?: import("vxe-table").VxeTooltipPropTypes.UseHTML | undefined;
                        content?: import("vxe-table").VxeTooltipPropTypes.Content | undefined;
                        enterable?: import("vxe-table").VxeTooltipPropTypes.Enterable | undefined;
                        theme?: import("vxe-table").VxeTooltipPropTypes.Theme | undefined;
                        icon?: string | undefined;
                    };
                    cellType: import("vxe-table").VxeColumnPropTypes.CellType;
                    cellRender: {
                        events?: {
                            [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>, ...args: any[]) => any;
                        } | undefined;
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        content?: string | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    editRender: {
                        events?: {
                            [key: string]: (cellParams: import("vxe-table").VxeColumnSlotTypes.EditSlotParams, ...args: any[]) => any;
                        } | undefined;
                        enabled?: boolean | undefined;
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        autofocus?: string | undefined;
                        autoselect?: boolean | undefined;
                        defaultValue?: string | number | object | RegExp | any[] | Date | ((params: {
                            column: import("vxe-table").VxeTableDefines.ColumnInfo<VxeTableDataRow>;
                        }) => any) | null | undefined;
                        immediate?: boolean | undefined;
                        content?: string | undefined;
                        placeholder?: string | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    contentRender: {
                        options?: any[] | undefined;
                        optionProps?: {
                            value?: string | undefined;
                            label?: string | undefined;
                            disabled?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        optionGroups?: any[] | undefined;
                        optionGroupProps?: {
                            options?: string | undefined;
                            label?: string | undefined;
                            key?: string | undefined;
                        } | undefined;
                        name?: string | undefined;
                        props?: {
                            [key: string]: any;
                        } | undefined;
                        attrs?: {
                            [key: string]: any;
                        } | undefined;
                        events?: {
                            [key: string]: (...args: any[]) => any;
                        } | undefined;
                        children?: any[] | undefined;
                        cellType?: "string" | "number" | undefined;
                    };
                    params: import("vxe-table").VxeColumnPropTypes.Params;
                    slots: {
                        title?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        radio?: string | ((params: import("vxe-table").VxeColumnSlotTypes.RadioSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        checkbox?: string | ((params: import("vxe-table").VxeColumnSlotTypes.CheckboxSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        default?: string | ((params: import("vxe-table").VxeColumnSlotTypes.DefaultSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        header?: string | ((params: import("vxe-table").VxeColumnSlotTypes.HeaderSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        footer?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FooterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        content?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ContentSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        filter?: string | ((params: import("vxe-table").VxeColumnSlotTypes.FilterSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        edit?: string | ((params: import("vxe-table").VxeColumnSlotTypes.EditSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        valid?: string | ((params: import("vxe-table").VxeColumnSlotTypes.ValidSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                        icon?: string | ((params: import("vxe-table").VxeColumnSlotTypes.IconSlotParams<VxeTableDataRow>) => SlotVNodeType[] | SlotVNodeType) | null | undefined;
                    };
                    id: string;
                    parentId: string;
                    level: number;
                    rowSpan: number;
                    colSpan: number;
                    halfVisible: boolean;
                    defaultVisible: any;
                    defaultFixed: any;
                    checked: boolean;
                    halfChecked: boolean;
                    disabled: boolean;
                    order: import("vxe-table").VxeTablePropTypes.SortOrder;
                    sortTime: number;
                    sortNumber: number;
                    renderSortNumber: number;
                    renderWidth: number;
                    renderHeight: number;
                    resizeWidth: number;
                    model: {
                        update: boolean;
                        value: any;
                    };
                    children: /*elided*/ any[];
                    renderHeader: (params: import("vxe-table").VxeTableDefines.CellRenderHeaderParams<VxeTableDataRow>) => VNode[];
                    renderCell: (params: import("vxe-table").VxeTableDefines.CellRenderCellParams<VxeTableDataRow>) => VNode[];
                    renderData: (params: import("vxe-table").VxeTableDefines.CellRenderDataParams<VxeTableDataRow>) => VNode[];
                    renderFooter: (params: import("vxe-table").VxeTableDefines.CellRenderFooterParams<VxeTableDataRow>) => VNode[];
                    getTitle: () => string;
                    getKey: () => string;
                };
                field: import("vxe-table").VxeColumnPropTypes.Field;
                property: import("vxe-table").VxeColumnPropTypes.Field;
                order: import("vxe-table").VxeTablePropTypes.SortOrder;
                sortTime: number;
            }[] | undefined;
            form?: Record<string, any> | undefined;
        };
        load: (reset?: boolean) => Promise<void>;
        search: (reset?: boolean) => void;
        vxeRef: Ref< VxeGridInstance | undefined, VxeGridInstance | undefined>;
        rowSortable: Ref<any, any>;
        columnSortable: Ref<any[], any[]>;
        insertActived: (record?: any, row?: any) => Promise<void>;
        validate: () => Promise<import("vxe-table").VxeTableDefines.ValidatorErrorMapParams<any> | undefined>;
        getSelected: () => any;
        remove: (rows: any) => Promise< MessageBoxData | undefined>;
        getRows: () => any[];
        setActived: (row: any) => Promise<void | undefined>;
        doLayout: () => void;
        getRecords: () => {
            insertRecords: any[];
            removeRecords: any[];
            updateRecords: any[];
            pendingRecords: any[];
        } | undefined;
        setSelectCell: (row?: any, column?: any) => void;
        $vtjDynamicSlots: () => string[];
    }> & {} & ComponentCustomProperties & {} & {
        $slots: Partial<Record<string, (_: {
            [key: string]: any;
            $table: VxeTableConstructor<any>;
            $grid: VxeGridConstructor<any> | null | undefined;
            row: any;
            rowIndex: number;
            $rowIndex: number;
            _rowIndex: number;
            column: import("vxe-table").VxeTableDefines.ColumnInfo<any>;
            columnIndex: number;
            $columnIndex: number;
            _columnIndex: number;
            checked?: boolean;
            indeterminate?: boolean;
            items: any[];
        }) => any>> & {
            empty?(_: {}): any;
            pager__left?(_: {}): any;
        };
    }) | null;
}, any>;
export default _default;
