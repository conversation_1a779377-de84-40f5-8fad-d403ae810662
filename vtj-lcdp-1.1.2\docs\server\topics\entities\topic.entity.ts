import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../shared';
import { PlatformType, TopicType, TopicDataType } from '../types/constants';

@Entity({
  name: 'topics',
  comment: '话题',
  orderBy: {
    createdAt: 'DESC'
  }
})
export class Topic extends BaseEntity {
  @Column({ comment: '大模型名称' })
  model: string;

  @Column({ comment: '标题' })
  title: string;

  @Column({ type: 'text', comment: '用户提示词' })
  prompt: string;

  @Column({ type: 'text', comment: '系统提示词' })
  content: string;

  @Index()
  @Column({ name: 'is_hot', comment: '热门', default: false })
  isHot: boolean;

  @Column({ name: 'project_id', comment: '项目标识' })
  projectId: string;

  @Column({ name: 'project_name', comment: '项目名' })
  projectName: string;

  @Column({ name: 'user_id', comment: '用户标识' })
  userId: string;

  @Column({ name: 'user_name', comment: '用户名' })
  userName: string;

  @Column({ name: 'app_id', comment: '应用标识' })
  appId: string;

  @Index()
  @Column({ name: 'file_id', comment: '文件Id' })
  fileId: string;

  @Column({ name: 'file_name', comment: '文件名称' })
  fileName: string;

  @Index()
  @Column({ type: 'enum', enum: PlatformType, comment: '平台' })
  platform: PlatformType;

  @Column({ comment: '图片文件', nullable: true })
  image: string;

  @Index()
  @Column({
    type: 'enum',
    enum: TopicType,
    comment: '话题类型',
    default: TopicType.Text
  })
  type: TopicType;

  @Column({ comment: 'JSON文件', nullable: true })
  json: string;

  @Column({
    name: 'data_type',
    type: 'enum',
    enum: TopicDataType,
    comment: '数据类型',
    nullable: true
  })
  dataType: TopicDataType;
}
