const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./vue-router-CezURGfh.js","./vue-ipWmmxHk.js"])))=>i.map(i=>d[i]);
var x=Object.defineProperty;var w=Object.getOwnPropertySymbols;var P=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable;var A=(o,t,e)=>t in o?x(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,v=(o,t)=>{for(var e in t||(t={}))P.call(t,e)&&A(o,e,t[e]);if(w)for(var e of w(t))R.call(t,e)&&A(o,e,t[e]);return o};var c=(o,t,e)=>new Promise((_,r)=>{var u=s=>{try{p(e.next(s))}catch(m){r(m)}},d=s=>{try{p(e.throw(s))}catch(m){r(m)}},p=s=>s.done?_(s.value):Promise.resolve(s.value).then(u,d);p((e=e.apply(o,t)).next())});import{_ as C}from"./monaco-editor-B8sWZqMY.js";import{useRoute as g}from"./vue-router-CezURGfh.js";import"./element-plus-COProxbp.js";import"./vxe-Bet8YtVU.js";import{b as V,j as S,v as T,S as k,w as B,A as D,m as I}from"./Editor-Dgw5r9tb-C0tzAbRO.js";import{a as O,n as E,l as b,E as j}from"./utils-Bopp5ewb.js";import{s as L}from"./@vueuse-WO_0ftym.js";import{d as z,az as h,e as H,f as K,K as N,O as q,P as F,o as G,h as J}from"./vue-ipWmmxHk.js";import"./lodash-es-BL-d_OSa.js";import"./@element-plus-icons-vue-0BR09xN9.js";import"./shared-Bnc4f-Fv.js";import"./dayjs-DO-COJPZ.js";import"./html2canvas-CIV5arX1.js";import"./mockjs-DG3OW7C0.js";import"./marked-BQZxLJfc.js";const mt=z({__name:"page",setup(o){return c(this,null,function*(){let t,e;const _=new V(S(E)),r=([t,e]=h(()=>_.getExtension().catch(()=>null)),t=yield t,e(),t),u=T({loading:b,notify:E,useTitle:L,alert:O,access:r==null?void 0:r.access}),d=r?([t,e]=h(()=>new j(r).load()),t=yield t,e(),t):{},{__BASE_PATH__:p="/"}=r||{},{provider:s,onReady:m}=k({mode:B.Runtime,service:_,materialPath:p,adapter:v(v({},u),d.adapter),dependencies:{Vue:()=>C(()=>import("./vue-ipWmmxHk.js").then(a=>a.aA),[],import.meta.url),VueRouter:()=>C(()=>import("./vue-router-CezURGfh.js"),__vite__mapDeps([0,1]),import.meta.url)}}),l=g(),f=H(),n=J(),y=a=>c(null,null,function*(){f.value=yield s.getRenderComponent(l.params.id.toString(),i=>{I(a,l,i)})});return m(()=>c(null,null,function*(){const a=n==null?void 0:n.appContext.app;a&&(a.use(D),a.use(s),y(a))})),K(()=>l.params.id,a=>c(null,null,function*(){if(a){const i=n==null?void 0:n.appContext.app;if(!i)return;y(i)}})),(a,i)=>f.value?(G(),N(F(f.value),{key:0})):q("",!0)})}});export{mt as default};
