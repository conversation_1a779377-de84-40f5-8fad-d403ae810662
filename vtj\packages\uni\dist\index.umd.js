(function(r,m){typeof exports=="object"&&typeof module<"u"?m(exports,require("@vtj/renderer")):typeof define=="function"&&define.amd?define(["exports","@vtj/renderer"],m):(r=typeof globalThis<"u"?globalThis:r||self,m(r.VtjUni={},r.renderer))})(this,function(r,m){"use strict";/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/uni 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const L="0.12.70",I=["onLaunch","onShow","onHide","onError","onPageNotFound","onUnhandledRejection","onThemeChange","onPageNotFound","onUniNViewMessage","onExit"],U=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeUnmount","unmounted"],k=["onLoad","onShow","onReady","onHide","onUnload","onResize","onPullDownRefresh","onReachBottom","onTabItemTap","onShareAppMessage","onPageScroll","onNavigationBarButtonTap","onBackPress","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputClicked","onShareTimeline","onAddToFavorites",...U],S="/pages",h={name:"VTJ",appid:"__UNI__1FC118B",description:"VTJ移动跨端项目",versionName:"1.0.0",versionCode:"100",transformPx:!1,"app-plus":{usingComponents:!0,nvueStyleCompiler:"uni-app",compilerVersion:3,splashscreen:{alwaysShowBeforeRender:!0,waiting:!0,autoclose:!0,delay:0},modules:{},distribute:{android:{permissions:['<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>','<uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>','<uses-permission android:name="android.permission.VIBRATE"/>','<uses-permission android:name="android.permission.READ_LOGS"/>','<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>','<uses-feature android:name="android.hardware.camera.autofocus"/>','<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>','<uses-permission android:name="android.permission.CAMERA"/>','<uses-permission android:name="android.permission.GET_ACCOUNTS"/>','<uses-permission android:name="android.permission.READ_PHONE_STATE"/>','<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>','<uses-permission android:name="android.permission.WAKE_LOCK"/>','<uses-permission android:name="android.permission.FLASHLIGHT"/>','<uses-feature android:name="android.hardware.camera"/>','<uses-permission android:name="android.permission.WRITE_SETTINGS"/>']},ios:{},sdkConfigs:{},icons:{android:{hdpi:"src/static/logo.png",xhdpi:"src/static/logo.png",xxhdpi:"src/static/logo.png",xxxhdpi:"src/static/logo.png"}}}},quickapp:{},"mp-weixin":{appid:"",setting:{urlCheck:!1},usingComponents:!0},"mp-alipay":{usingComponents:!0},"mp-baidu":{usingComponents:!0},"mp-toutiao":{usingComponents:!0},uniStatistics:{enable:!1},vueVersion:"3"},w={pages:[],globalStyle:{navigationBarTextStyle:"black",navigationBarTitleText:"uni-app",navigationBarBackgroundColor:"#F8F8F8",backgroundColor:"#F8F8F8"},easycom:{autoscan:!0,custom:{"^uni-(.*)":"@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}}};function V(e){const[t,...n]=e;return[t.toLowerCase(),...n].join("").replace(/[A-Z]/g,i=>`-${i.toLowerCase()}`)}function C(e){let{manifestJson:t={},pagesJson:n={},routes:o=[]}=e;const i=o.map(u=>{const{path:a,style:d,needLogin:_}=u;return{path:a,style:d,needLogin:_}});return{...e,manifestJson:Object.assign({},h,t),pagesJson:Object.assign({},w,n,{pages:i})}}const T={navigationBarBackgroundColor:"backgroundColor",navigationBarTextStyle:"titleColor",navigationBarTitleText:"titleText",navigationStyle:"style",titleImage:"titleImage",titlePenetrate:"titlePenetrate",transparentTitle:"transparentTitle"};function A(e){const t={},n={black:"#000000",white:"#ffffff"};for(const o in e)T[o]&&(t[T[o]]=o==="navigationBarTextStyle"?n[e[o]]||n.black:e[o]);return t}function R(e){const t={};for(const n in e)T[n]||(t[n]=e[n]);return t}function W(e){var o;const n=e.split("?")[0].match(/\#\/pages\/([\w\W]*)/i);return(o=n==null?void 0:n[1])!=null?o:""}function j(e){var n;const t=e.h5;return((n=t==null?void 0:t.router)==null?void 0:n.mode)||"hash"}function M(e){var t,n,o;return{topWindow:!!((t=e.topWindow)!=null&&t.path),leftWindow:!!((n=e.leftWindow)!=null&&n.path),rightWindow:!!((o=e.rightWindow)!=null&&o.path)}}function G(e){const{pagesJson:t={}}=e,{globalStyle:n,pages:o=[]}=t;return!!(n!=null&&n.enablePullDownRefresh||o.find(i=>{var u;return!!((u=i.style)!=null&&u.enablePullDownRefresh)}))}function D(e){const{pagesJson:t={}}=e,{globalStyle:n,pages:o=[]}=t;let i=!1;return(n==null?void 0:n.navigationStyle)==="custom"?(i=!0,o.find(u=>{var a;return((a=u.style)==null?void 0:a.navigationStyle)==="default"})&&(i=!1)):o.every(u=>{var a;return((a=u.style)==null?void 0:a.navigationStyle)==="custom"})&&(i=!0),i}function J(e){var d;const t=D(e),n={navigationBar:!1,navigationBarButtons:!1,navigationBarSearchInput:!1,navigationBarTransparent:!1},{pagesJson:o={}}=e,{globalStyle:i,pages:u=[]}=o;if(t)return n;n.navigationBar=!0;const a=(d=i==null?void 0:i.h5)==null?void 0:d.titleNView;return u.find(_=>{var s,c,l;return!!((l=(c=(s=_.style)==null?void 0:s.h5)==null?void 0:c.titleNView)!=null&&l.buttons.length)})&&(n.navigationBarButtons=!0),(a!=null&&a.searchInput||u.find(_=>{var s,c,l;return!!((l=(c=(s=_.style)==null?void 0:s.h5)==null?void 0:c.titleNView)!=null&&l.searchInput)}))&&(n.navigationBarSearchInput=!0),((a==null?void 0:a.type)==="transparent"||u.find(_=>{var s,c,l;return((l=(c=(s=_.style)==null?void 0:s.h5)==null?void 0:c.titleNView)==null?void 0:l.type)==="transparent"}))&&(n.navigationBarTransparent=!0),n}function B(e,t=window){var p,g,E,f;const{pagesJson:n={},manifestJson:o={}}=e,{topWindow:i,leftWindow:u,rightWindow:a}=M(n),{navigationBar:d,navigationBarButtons:_,navigationBarSearchInput:s,navigationBarTransparent:c}=J(e),l={__VUE_OPTIONS_API__:!0,__VUE_PROD_DEVTOOLS__:!1,__VUE_PROD_HYDRATION_MISMATCH_DETAILS__:!1,__UNI_FEATURE_WX__:!1,__UNI_FEATURE_WXS__:!1,__UNI_FEATURE_RPX__:!1,__UNI_FEATURE_PROMISE__:!1,__UNI_FEATURE_LONGPRESS__:!1,__UNI_FEATURE_I18N_EN__:!1,__UNI_FEATURE_I18N_ES__:!1,__UNI_FEATURE_I18N_FR__:!1,__UNI_FEATURE_I18N_ZH_HANS__:!0,__UNI_FEATURE_I18N_ZH_HANT__:!1,__UNI_FEATURE_UNI_CLOUD__:!1,__UNI_FEATURE_I18N_LOCALE__:!1,__UNI_FEATURE_NVUE__:!1,__UNI_FEATURE_ROUTER_MODE__:j(o),__UNI_FEATURE_PAGES__:!!((p=n.pages)!=null&&p.length),__UNI_FEATURE_TABBAR__:!!((E=(g=n.tabBar)==null?void 0:g.list)!=null&&E.length),__UNI_FEATURE_TABBAR_MIDBUTTON__:!!((f=n.tabBar)!=null&&f.midButton),__UNI_FEATURE_TOPWINDOW__:i,__UNI_FEATURE_LEFTWINDOW__:u,__UNI_FEATURE_RIGHTWINDOW__:a,__UNI_FEATURE_RESPONSIVE__:!1,__UNI_FEATURE_NAVIGATIONBAR__:d,__UNI_FEATURE_PULL_DOWN_REFRESH__:G(e),__UNI_FEATURE_NAVIGATIONBAR_BUTTONS__:_,__UNI_FEATURE_NAVIGATIONBAR_SEARCHINPUT__:s,__UNI_FEATURE_NAVIGATIONBAR_TRANSPARENT__:c};Object.entries(l).forEach(([Z,Q])=>{t[Z]=Q})}const H="4.57";function y(e,t=window){var l,p;const{pagesJson:n={},manifestJson:o={}}=e,{easycom:i={}}=n,{appid:u="",name:a="",versionCode:d="",versionName:_=""}=o,s=n.globalStyle,c=((l=o.h5)==null?void 0:l.router)||{};t.__uniConfig={easycom:i,globalStyle:{...R(s),navigationBar:A(s),isNVue:!1},compilerVersion:H,appId:u,appName:a,appVersion:_,appVersionCode:String(d),async:{loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0,...((p=o.h5)==null?void 0:p.async)||{}},debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4,...o.networkTimeout||{}},sdkConfigs:{},nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:{},router:{mode:"hash",base:"/",assets:"assets",routerBase:c.base||"/",...c},darkmode:!1,themeConfig:{},tabBar:n.tabBar?{position:"bottom",color:"#7A7E83",selectedColor:"#3cc51f",borderStyle:"black",blurEffect:"none",fontSize:"10px",iconWidth:"24px",spacing:"3px",height:"50px",backgroundColor:"#ffffff",selectedIndex:0,shown:!0,...n.tabBar}:void 0}}function v(e,t=window){const{UniServiceJSBridge:n,UniViewJSBridge:o,getApp:i,uni:u,getCurrentPages:a,upx2px:d,setupPage:_}=e;t.UniServiceJSBridge=n,t.UniViewJSBridge=o,t.getApp=i,t.uni=u,t.wx=u,t.getCurrentPages=a,t.upx2px=d,t.__setupPage=s=>_(s)}function x(e,t,n){const{openBlock:o,createBlock:i,withCtx:u,createVNode:a}=e,{PageComponent:d,setupPage:_,getApp:s}=t;return{mpType:"page",async setup(){var f;const c=s(),l=((f=c==null?void 0:c.$route)==null?void 0:f.query)||{},{loader:p,component:g}=n,E=p?await p(n):g||{};return()=>(o(),i(d,null,{page:u(()=>[a(_(E),Object.assign({},l,{ref:"page"}),null,512)]),_:1}))}}}function $(e,t,n){var g;const{path:o,style:i={},meta:u={},home:a,id:d}=t,_=((g=e.tabBar)==null?void 0:g.list)||[],s=_.findIndex(E=>{var f;return E.pagePath===o||((f=E.pagePath)==null?void 0:f.endsWith(d))}),c=_[s],l=!!c,p=n===0;return{isTabBar:l,tabBarIndex:s,isQuit:a!=null?a:p,isEntry:a!=null?a:p,navigationBar:{type:"default",...A(i)},isNVue:!1,route:c?c.pagePath:o,...u}}function F(e,t,n,o,i=window){const u=n.map((a,d)=>{const _=$(o,a,d),s=x(e,t,a),{path:c}=a;return{path:c,alias:c,meta:_,component:{render(){return e.h(e.Suspense,[e.h(s)])}}}});i.__uniRoutes=u,i.__uniLayout={}}function P(e,t,n=window){m.adoptedStyleSheets(n,String(e),t)}function b(e,t){if(!t)return;["View","ScrollView","Swiper","MovableArea","MovableView","CoverView","CoverImage","Icon","Text","RichText","Progress","Button","CheckboxGroup","Checkbox","Editor","Form","Input","Label","Picker","PickerView","RadioGroup","Radio","Slider","Switch","Textarea","Navigator","Image","Video","Map","Canvas","WebView","PickerViewColumn","ResizeSensor","SwiperItem"].forEach(o=>{const i=t[o];e.component(o,i)})}function Y(e){const t=C(e),{Vue:n,App:o,UniH5:i,routes:u=[],pagesJson:a={},manifestJson:d={},window:_,css:s=""}=t;if(!i)return n.createApp(o);const{plugin:c,setupApp:l}=i;B(t,_),y(t,_),v(i,_),F(n,i,u,a,_),P(d.appid||Date.now(),s,_);const p=n.createApp(l(o));return p.use(b,i),p.use(c),p}function q(e,t){const n={};return Object.entries(e).forEach(([o,i])=>{I.includes(o)&&i&&(n[o]=t(i))}),n}function N(e,t){return async()=>{const n=await e.getDsl(t);if(n){const{renderer:o}=e.createDslRenderer(n);return o}return null}}async function K(e,t=!1,n=S){var a,d,_;const o=((a=e.project)==null?void 0:a.pages)||[],i=[];for(const s of o){const c=((d=e.project)==null?void 0:d.homepage)===s.id;i.push({id:s.id,path:`${n}/${s.id}`,loader:N(e,s.id),style:{navigationBarTitleText:s.title,...s.style},needLogin:s.needLogin,home:c})}const u=i.find(s=>!!s.home)||i[0];if(u){const s=n===S?"/":n;i.unshift({...u,path:s})}if(t){const s=((_=e.project)==null?void 0:_.blocks)||[];for(const c of s)i.push({id:c.id,path:`${n}/${c.id}`,loader:N(e,c.id),style:{navigationStyle:"custom"}})}return i}function z(){var e;return(e=window.uni)!=null&&e.showLoading&&window.uni.showLoading({title:"加载中...",mask:!0}),{close:()=>{var t;(t=window.uni)!=null&&t.hideLoading&&window.uni.hideLoading()}}}async function O(e,t="",n="warning"){var o;return(o=window.uni)!=null&&o.showModal?window.uni.showModal({title:t,content:e,showCancel:!1}):Promise.reject(new Error("window.uni.showModal is undefined"))}async function X(e){return O(e)}r.APP_LIFE_CYCLE=I,r.COMPONENT_LIFE_CYCLES_LIST=U,r.MANIFEST_JSON=h,r.PAGES_JSON=w,r.PAGE_LIFE_CYCLES_LIST=k,r.ROUTE_PAGE_BASE_PATH=S,r.alert=X,r.createUniAppComponent=q,r.createUniLoader=N,r.createUniRoutes=K,r.getFileId=W,r.getGobalStyle=R,r.getNavigationBar=A,r.injectUniCSS=P,r.injectUniConfig=y,r.injectUniFeatures=B,r.injectUniGlobal=v,r.injectUniRoutes=F,r.install=b,r.loading=z,r.mergeOptions=C,r.navigationBarMaps=T,r.notify=O,r.setupUniApp=Y,r.toKebabCase=V,r.version=L,Object.defineProperty(r,Symbol.toStringTag,{value:"Module"})});
