import {
  Controller,
  Get,
  Query,
  Delete,
  Body,
  Param,
  ParseBoolPipe
} from '@nestjs/common';
import { TopicsService } from './topics.service';
import { QueryTopicDto } from './dto/query-topic.dto';
import { QueryChatDto } from './dto/query-chat.dto';
import { User } from 'src/shared';

@Controller('topics')
export class TopicsController {
  constructor(private readonly topics: TopicsService) {}

  @Get()
  findTopics(@Query() dto: QueryTopicDto) {
    return this.topics.findTopics(dto);
  }

  @Get('info/:id')
  findOneTopic(@Param('id') id: string) {
    return this.topics.findOneTopic(id);
  }

  @Delete()
  removeTopics(@Body() ids: string[]) {
    return this.topics.removeTopics(ids);
  }

  @Get('hot/:id')
  toggleHotTopic(
    @Param('id') id: string,
    @Query('hot', ParseBoolPipe) hot: boolean
  ) {
    return this.topics.toggleHotTopic(id, hot);
  }

  @Get('chats')
  findChats(@Query() dto: QueryChatDto) {
    return this.topics.findChats(dto);
  }

  @Delete('chats')
  removeChats(@Body() ids: string[]) {
    return this.topics.removeChats(ids);
  }

  @Get('tokens')
  getUsedTokens(@User('id') userId: string) {
    return this.topics.userTokensUsed(userId);
  }
}
