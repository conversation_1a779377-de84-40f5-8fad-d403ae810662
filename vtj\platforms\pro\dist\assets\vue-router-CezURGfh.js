import{s as ut,d as Ve,u as Y,ay as ft,k as I,D as le,e as ht,f as pt,ae as Ue,n as dt,v as $,W as mt,a6 as gt,$ as vt,_ as yt}from"./vue-ipWmmxHk.js";/*!
  * vue-router v4.5.1
  * (c) 2025 <PERSON>
  * @license MIT
  */const V=typeof document!="undefined";function De(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ke(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&De(e.default)}const P=Object.assign;function ue(e,t){const n={};for(const r in t){const o=t[r];n[r]=L(o)?o.map(e):e(o)}return n}const X=()=>{},L=Array.isArray,We=/#/g,Rt=/&/g,Et=/\//g,wt=/=/g,Pt=/\?/g,Qe=/\+/g,St=/%5B/g,Ct=/%5D/g,Fe=/%5E/g,kt=/%60/g,Ye=/%7B/g,bt=/%7C/g,Xe=/%7D/g,At=/%20/g;function ve(e){return encodeURI(""+e).replace(bt,"|").replace(St,"[").replace(Ct,"]")}function Ot(e){return ve(e).replace(Ye,"{").replace(Xe,"}").replace(Fe,"^")}function de(e){return ve(e).replace(Qe,"%2B").replace(At,"+").replace(We,"%23").replace(Rt,"%26").replace(kt,"`").replace(Ye,"{").replace(Xe,"}").replace(Fe,"^")}function _t(e){return de(e).replace(wt,"%3D")}function xt(e){return ve(e).replace(We,"%23").replace(Pt,"%3F")}function Mt(e){return e==null?"":xt(e).replace(Et,"%2F")}function Z(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Tt=/\/$/,Lt=e=>e.replace(Tt,"");function fe(e,t,n="/"){let r,o={},a="",h="";const d=t.indexOf("#");let s=t.indexOf("?");return d<s&&d>=0&&(s=-1),s>-1&&(r=t.slice(0,s),a=t.slice(s+1,d>-1?d:t.length),o=e(a)),d>-1&&(r=r||t.slice(0,d),h=t.slice(d,t.length)),r=jt(r!=null?r:t,n),{fullPath:r+(a&&"?")+a+h,path:r,query:o,hash:Z(h)}}function It(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function be(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Nt(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&U(t.matched[r],n.matched[o])&&Ze(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function U(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ze(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!$t(e[n],t[n]))return!1;return!0}function $t(e,t){return L(e)?Ae(e,t):L(t)?Ae(t,e):e===t}function Ae(e,t){return L(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function jt(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let a=n.length-1,h,d;for(h=0;h<r.length;h++)if(d=r[h],d!==".")if(d==="..")a>1&&a--;else break;return n.slice(0,a).join("/")+"/"+r.slice(h).join("/")}const H={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var D;(function(e){e.pop="pop",e.push="push"})(D||(D={}));var q;(function(e){e.back="back",e.forward="forward",e.unknown=""})(q||(q={}));const he="";function Je(e){if(!e)if(V){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Lt(e)}const Ht=/^[^#]+#/;function et(e,t){return e.replace(Ht,"#")+t}function Bt(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const te=()=>({left:window.scrollX,top:window.scrollY});function Gt(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Bt(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Oe(e,t){return(history.state?history.state.position-t:-1)+e}const me=new Map;function qt(e,t){me.set(e,t)}function zt(e){const t=me.get(e);return me.delete(e),t}let Vt=()=>location.protocol+"//"+location.host;function tt(e,t){const{pathname:n,search:r,hash:o}=t,a=e.indexOf("#");if(a>-1){let d=o.includes(e.slice(a))?e.slice(a).length:1,s=o.slice(d);return s[0]!=="/"&&(s="/"+s),be(s,"")}return be(n,e)+r+o}function Ut(e,t,n,r){let o=[],a=[],h=null;const d=({state:u})=>{const f=tt(e,location),w=n.value,S=t.value;let b=0;if(u){if(n.value=f,t.value=u,h&&h===w){h=null;return}b=S?u.position-S.position:0}else r(f);o.forEach(A=>{A(n.value,w,{delta:b,type:D.pop,direction:b?b>0?q.forward:q.back:q.unknown})})};function s(){h=n.value}function l(u){o.push(u);const f=()=>{const w=o.indexOf(u);w>-1&&o.splice(w,1)};return a.push(f),f}function p(){const{history:u}=window;u.state&&u.replaceState(P({},u.state,{scroll:te()}),"")}function c(){for(const u of a)u();a=[],window.removeEventListener("popstate",d),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",d),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:s,listen:l,destroy:c}}function _e(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?te():null}}function Dt(e){const{history:t,location:n}=window,r={value:tt(e,n)},o={value:t.state};o.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(s,l,p){const c=e.indexOf("#"),u=c>-1?(n.host&&document.querySelector("base")?e:e.slice(c))+s:Vt()+e+s;try{t[p?"replaceState":"pushState"](l,"",u),o.value=l}catch(f){console.error(f),n[p?"replace":"assign"](u)}}function h(s,l){const p=P({},t.state,_e(o.value.back,s,o.value.forward,!0),l,{position:o.value.position});a(s,p,!0),r.value=s}function d(s,l){const p=P({},o.value,t.state,{forward:s,scroll:te()});a(p.current,p,!0);const c=P({},_e(r.value,s,null),{position:p.position+1},l);a(s,c,!1),r.value=s}return{location:r,state:o,push:d,replace:h}}function Kt(e){e=Je(e);const t=Dt(e),n=Ut(e,t.state,t.location,t.replace);function r(a,h=!0){h||n.pauseListeners(),history.go(a)}const o=P({location:"",base:e,go:r,createHref:et.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Rn(e=""){let t=[],n=[[he,{}]],r=0;e=Je(e);function o(d,s={}){r++,r!==n.length&&n.splice(r),n.push([d,s])}function a(d,s,{direction:l,delta:p}){const c={direction:l,delta:p,type:D.pop};for(const u of t)u(d,s,c)}const h={location:he,state:{},base:e,createHref:et.bind(null,e),replace(d,s){n.splice(r--,1),o(d,s)},push(d,s){o(d,s)},listen(d){return t.push(d),()=>{const s=t.indexOf(d);s>-1&&t.splice(s,1)}},destroy(){t=[],n=[[he,{}]],r=0},go(d,s=!0){const l=this.location,p=d<0?q.back:q.forward;r=Math.max(0,Math.min(r+d,n.length-1)),s&&a(this.location,l,{direction:p,delta:d})}};return Object.defineProperty(h,"location",{enumerable:!0,get:()=>n[r][0]}),Object.defineProperty(h,"state",{enumerable:!0,get:()=>n[r][1]}),h}function En(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),Kt(e)}function Wt(e){return typeof e=="string"||e&&typeof e=="object"}function nt(e){return typeof e=="string"||typeof e=="symbol"}const rt=Symbol("");var xe;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(xe||(xe={}));function K(e,t){return P(new Error,{type:e,[rt]:!0},t)}function N(e,t){return e instanceof Error&&rt in e&&(t==null||!!(e.type&t))}const Me="[^/]+?",Qt={sensitive:!1,strict:!1,start:!0,end:!0},Ft=/[.+*?^${}()[\]/\\]/g;function Yt(e,t){const n=P({},Qt,t),r=[];let o=n.start?"^":"";const a=[];for(const l of e){const p=l.length?[]:[90];n.strict&&!l.length&&(o+="/");for(let c=0;c<l.length;c++){const u=l[c];let f=40+(n.sensitive?.25:0);if(u.type===0)c||(o+="/"),o+=u.value.replace(Ft,"\\$&"),f+=40;else if(u.type===1){const{value:w,repeatable:S,optional:b,regexp:A}=u;a.push({name:w,repeatable:S,optional:b});const E=A||Me;if(E!==Me){f+=10;try{new RegExp(`(${E})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${w}" (${E}): `+T.message)}}let C=S?`((?:${E})(?:/(?:${E}))*)`:`(${E})`;c||(C=b&&l.length<2?`(?:/${C})`:"/"+C),b&&(C+="?"),o+=C,f+=20,b&&(f+=-8),S&&(f+=-20),E===".*"&&(f+=-50)}p.push(f)}r.push(p)}if(n.strict&&n.end){const l=r.length-1;r[l][r[l].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const h=new RegExp(o,n.sensitive?"":"i");function d(l){const p=l.match(h),c={};if(!p)return null;for(let u=1;u<p.length;u++){const f=p[u]||"",w=a[u-1];c[w.name]=f&&w.repeatable?f.split("/"):f}return c}function s(l){let p="",c=!1;for(const u of e){(!c||!p.endsWith("/"))&&(p+="/"),c=!1;for(const f of u)if(f.type===0)p+=f.value;else if(f.type===1){const{value:w,repeatable:S,optional:b}=f,A=w in l?l[w]:"";if(L(A)&&!S)throw new Error(`Provided param "${w}" is an array but it is not repeatable (* or + modifiers)`);const E=L(A)?A.join("/"):A;if(!E)if(b)u.length<2&&(p.endsWith("/")?p=p.slice(0,-1):c=!0);else throw new Error(`Missing required param "${w}"`);p+=E}}return p||"/"}return{re:h,score:r,keys:a,parse:d,stringify:s}}function Xt(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ot(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const a=Xt(r[n],o[n]);if(a)return a;n++}if(Math.abs(o.length-r.length)===1){if(Te(r))return 1;if(Te(o))return-1}return o.length-r.length}function Te(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Zt={type:0,value:""},Jt=/[a-zA-Z0-9_]/;function en(e){if(!e)return[[]];if(e==="/")return[[Zt]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(f){throw new Error(`ERR (${n})/"${l}": ${f}`)}let n=0,r=n;const o=[];let a;function h(){a&&o.push(a),a=[]}let d=0,s,l="",p="";function c(){l&&(n===0?a.push({type:0,value:l}):n===1||n===2||n===3?(a.length>1&&(s==="*"||s==="+")&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:l,regexp:p,repeatable:s==="*"||s==="+",optional:s==="*"||s==="?"})):t("Invalid state to consume buffer"),l="")}function u(){l+=s}for(;d<e.length;){if(s=e[d++],s==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:s==="/"?(l&&c(),h()):s===":"?(c(),n=1):u();break;case 4:u(),n=r;break;case 1:s==="("?n=2:Jt.test(s)?u():(c(),n=0,s!=="*"&&s!=="?"&&s!=="+"&&d--);break;case 2:s===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+s:n=3:p+=s;break;case 3:c(),n=0,s!=="*"&&s!=="?"&&s!=="+"&&d--,p="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${l}"`),c(),h(),o}function tn(e,t,n){const r=Yt(en(e.path),n),o=P(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function nn(e,t){const n=[],r=new Map;t=$e({strict:!1,end:!0,sensitive:!1},t);function o(c){return r.get(c)}function a(c,u,f){const w=!f,S=Ie(c);S.aliasOf=f&&f.record;const b=$e(t,c),A=[S];if("alias"in c){const T=typeof c.alias=="string"?[c.alias]:c.alias;for(const G of T)A.push(Ie(P({},S,{components:f?f.record.components:S.components,path:G,aliasOf:f?f.record:S})))}let E,C;for(const T of A){const{path:G}=T;if(u&&G[0]!=="/"){const j=u.record.path,M=j[j.length-1]==="/"?"":"/";T.path=u.record.path+(G&&M+G)}if(E=tn(T,u,b),f?f.alias.push(E):(C=C||E,C!==E&&C.alias.push(E),w&&c.name&&!Ne(E)&&h(c.name)),st(E)&&s(E),S.children){const j=S.children;for(let M=0;M<j.length;M++)a(j[M],E,f&&f.children[M])}f=f||E}return C?()=>{h(C)}:X}function h(c){if(nt(c)){const u=r.get(c);u&&(r.delete(c),n.splice(n.indexOf(u),1),u.children.forEach(h),u.alias.forEach(h))}else{const u=n.indexOf(c);u>-1&&(n.splice(u,1),c.record.name&&r.delete(c.record.name),c.children.forEach(h),c.alias.forEach(h))}}function d(){return n}function s(c){const u=sn(c,n);n.splice(u,0,c),c.record.name&&!Ne(c)&&r.set(c.record.name,c)}function l(c,u){let f,w={},S,b;if("name"in c&&c.name){if(f=r.get(c.name),!f)throw K(1,{location:c});b=f.record.name,w=P(Le(u.params,f.keys.filter(C=>!C.optional).concat(f.parent?f.parent.keys.filter(C=>C.optional):[]).map(C=>C.name)),c.params&&Le(c.params,f.keys.map(C=>C.name))),S=f.stringify(w)}else if(c.path!=null)S=c.path,f=n.find(C=>C.re.test(S)),f&&(w=f.parse(S),b=f.record.name);else{if(f=u.name?r.get(u.name):n.find(C=>C.re.test(u.path)),!f)throw K(1,{location:c,currentLocation:u});b=f.record.name,w=P({},u.params,c.params),S=f.stringify(w)}const A=[];let E=f;for(;E;)A.unshift(E.record),E=E.parent;return{name:b,path:S,params:w,matched:A,meta:on(A)}}e.forEach(c=>a(c));function p(){n.length=0,r.clear()}return{addRoute:a,resolve:l,removeRoute:h,clearRoutes:p,getRoutes:d,getRecordMatcher:o}}function Le(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Ie(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:rn(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function rn(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Ne(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function on(e){return e.reduce((t,n)=>P(t,n.meta),{})}function $e(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function sn(e,t){let n=0,r=t.length;for(;n!==r;){const a=n+r>>1;ot(e,t[a])<0?r=a:n=a+1}const o=cn(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function cn(e){let t=e;for(;t=t.parent;)if(st(t)&&ot(e,t)===0)return t}function st({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function an(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const a=r[o].replace(Qe," "),h=a.indexOf("="),d=Z(h<0?a:a.slice(0,h)),s=h<0?null:Z(a.slice(h+1));if(d in t){let l=t[d];L(l)||(l=t[d]=[l]),l.push(s)}else t[d]=s}return t}function je(e){let t="";for(let n in e){const r=e[n];if(n=_t(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(L(r)?r.map(a=>a&&de(a)):[r&&de(r)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+n,a!=null&&(t+="="+a))})}return t}function ln(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=L(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const ye=Symbol(""),He=Symbol(""),ne=Symbol(""),Re=Symbol(""),ge=Symbol("");function F(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function it(e,t,n){const r=()=>{e[t].delete(n)};mt(r),gt(r),vt(()=>{e[t].add(n)}),e[t].add(n)}function wn(e){const t=$(ye,{}).value;t&&it(t,"leaveGuards",e)}function Pn(e){const t=$(ye,{}).value;t&&it(t,"updateGuards",e)}function B(e,t,n,r,o,a=h=>h()){const h=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((d,s)=>{const l=u=>{u===!1?s(K(4,{from:n,to:t})):u instanceof Error?s(u):Wt(u)?s(K(2,{from:t,to:u})):(h&&r.enterCallbacks[o]===h&&typeof u=="function"&&h.push(u),d())},p=a(()=>e.call(r&&r.instances[o],t,n,l));let c=Promise.resolve(p);e.length<3&&(c=c.then(l)),c.catch(u=>s(u))})}function pe(e,t,n,r,o=a=>a()){const a=[];for(const h of e)for(const d in h.components){let s=h.components[d];if(!(t!=="beforeRouteEnter"&&!h.instances[d]))if(De(s)){const p=(s.__vccOpts||s)[t];p&&a.push(B(p,n,r,h,d,o))}else{let l=s();a.push(()=>l.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${d}" at "${h.path}"`);const c=Ke(p)?p.default:p;h.mods[d]=p,h.components[d]=c;const f=(c.__vccOpts||c)[t];return f&&B(f,n,r,h,d,o)()}))}}return a}function Sn(e){return e.matched.every(t=>t.redirect)?Promise.reject(new Error("Cannot load a route that redirects.")):Promise.all(e.matched.map(t=>t.components&&Promise.all(Object.keys(t.components).reduce((n,r)=>{const o=t.components[r];return typeof o=="function"&&!("displayName"in o)&&n.push(o().then(a=>{if(!a)return Promise.reject(new Error(`Couldn't resolve component "${r}" at "${t.path}". Ensure you passed a function that returns a promise.`));const h=Ke(a)?a.default:a;t.mods[r]=a,t.components[r]=h})),n},[])))).then(()=>e)}function Be(e){const t=$(ne),n=$(Re),r=I(()=>{const s=Y(e.to);return t.resolve(s)}),o=I(()=>{const{matched:s}=r.value,{length:l}=s,p=s[l-1],c=n.matched;if(!p||!c.length)return-1;const u=c.findIndex(U.bind(null,p));if(u>-1)return u;const f=Ge(s[l-2]);return l>1&&Ge(p)===f&&c[c.length-1].path!==f?c.findIndex(U.bind(null,s[l-2])):u}),a=I(()=>o.value>-1&&dn(n.params,r.value.params)),h=I(()=>o.value>-1&&o.value===n.matched.length-1&&Ze(n.params,r.value.params));function d(s={}){if(pn(s)){const l=t[Y(e.replace)?"replace":"push"](Y(e.to)).catch(X);return e.viewTransition&&typeof document!="undefined"&&"startViewTransition"in document&&document.startViewTransition(()=>l),l}return Promise.resolve()}return{route:r,href:I(()=>r.value.href),isActive:a,isExactActive:h,navigate:d}}function un(e){return e.length===1?e[0]:e}const fn=Ve({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Be,setup(e,{slots:t}){const n=yt(Be(e)),{options:r}=$(ne),o=I(()=>({[qe(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[qe(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const a=t.default&&un(t.default(n));return e.custom?a:Ue("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},a)}}}),hn=fn;function pn(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function dn(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!L(o)||o.length!==r.length||r.some((a,h)=>a!==o[h]))return!1}return!0}function Ge(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const qe=(e,t,n)=>e!=null?e:t!=null?t:n,mn=Ve({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=$(ge),o=I(()=>e.route||r.value),a=$(He,0),h=I(()=>{let l=Y(a);const{matched:p}=o.value;let c;for(;(c=p[l])&&!c.components;)l++;return l}),d=I(()=>o.value.matched[h.value]);le(He,I(()=>h.value+1)),le(ye,d),le(ge,o);const s=ht();return pt(()=>[s.value,d.value,e.name],([l,p,c],[u,f,w])=>{p&&(p.instances[c]=l,f&&f!==p&&l&&l===u&&(p.leaveGuards.size||(p.leaveGuards=f.leaveGuards),p.updateGuards.size||(p.updateGuards=f.updateGuards))),l&&p&&(!f||!U(p,f)||!u)&&(p.enterCallbacks[c]||[]).forEach(S=>S(l))},{flush:"post"}),()=>{const l=o.value,p=e.name,c=d.value,u=c&&c.components[p];if(!u)return ze(n.default,{Component:u,route:l});const f=c.props[p],w=f?f===!0?l.params:typeof f=="function"?f(l):f:null,b=Ue(u,P({},w,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(c.instances[p]=null)},ref:s}));return ze(n.default,{Component:b,route:l})||b}}});function ze(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const gn=mn;function Cn(e){const t=nn(e.routes,e),n=e.parseQuery||an,r=e.stringifyQuery||je,o=e.history,a=F(),h=F(),d=F(),s=ut(H);let l=H;V&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=ue.bind(null,i=>""+i),c=ue.bind(null,Mt),u=ue.bind(null,Z);function f(i,g){let m,v;return nt(i)?(m=t.getRecordMatcher(i),v=g):v=i,t.addRoute(v,m)}function w(i){const g=t.getRecordMatcher(i);g&&t.removeRoute(g)}function S(){return t.getRoutes().map(i=>i.record)}function b(i){return!!t.getRecordMatcher(i)}function A(i,g){if(g=P({},g||s.value),typeof i=="string"){const y=fe(n,i,g.path),_=t.resolve({path:y.path},g),Q=o.createHref(y.fullPath);return P(y,_,{params:u(_.params),hash:Z(y.hash),redirectedFrom:void 0,href:Q})}let m;if(i.path!=null)m=P({},i,{path:fe(n,i.path,g.path).path});else{const y=P({},i.params);for(const _ in y)y[_]==null&&delete y[_];m=P({},i,{params:c(y)}),g.params=c(g.params)}const v=t.resolve(m,g),k=i.hash||"";v.params=p(u(v.params));const O=It(r,P({},i,{hash:Ot(k),path:v.path})),R=o.createHref(O);return P({fullPath:O,hash:k,query:r===je?ln(i.query):i.query||{}},v,{redirectedFrom:void 0,href:R})}function E(i){return typeof i=="string"?fe(n,i,s.value.path):P({},i)}function C(i,g){if(l!==i)return K(8,{from:g,to:i})}function T(i){return M(i)}function G(i){return T(P(E(i),{replace:!0}))}function j(i){const g=i.matched[i.matched.length-1];if(g&&g.redirect){const{redirect:m}=g;let v=typeof m=="function"?m(i):m;return typeof v=="string"&&(v=v.includes("?")||v.includes("#")?v=E(v):{path:v},v.params={}),P({query:i.query,hash:i.hash,params:v.path!=null?{}:i.params},v)}}function M(i,g){const m=l=A(i),v=s.value,k=i.state,O=i.force,R=i.replace===!0,y=j(m);if(y)return M(P(E(y),{state:typeof y=="object"?P({},k,y.state):k,force:O,replace:R}),g||m);const _=m;_.redirectedFrom=g;let Q;return!O&&Nt(r,v,m)&&(Q=K(16,{to:_,from:v}),Ce(v,v,!0,!1)),(Q?Promise.resolve(Q):Ee(_,v)).catch(x=>N(x)?N(x,2)?x:ie(x):se(x,_,v)).then(x=>{if(x){if(N(x,2))return M(P({replace:R},E(x.to),{state:typeof x.to=="object"?P({},k,x.to.state):k,force:O}),g||_)}else x=Pe(_,v,!0,R,k);return we(_,v,x),x})}function ct(i,g){const m=C(i,g);return m?Promise.reject(m):Promise.resolve()}function re(i){const g=ee.values().next().value;return g&&typeof g.runWithContext=="function"?g.runWithContext(i):i()}function Ee(i,g){let m;const[v,k,O]=vn(i,g);m=pe(v.reverse(),"beforeRouteLeave",i,g);for(const y of v)y.leaveGuards.forEach(_=>{m.push(B(_,i,g))});const R=ct.bind(null,i,g);return m.push(R),z(m).then(()=>{m=[];for(const y of a.list())m.push(B(y,i,g));return m.push(R),z(m)}).then(()=>{m=pe(k,"beforeRouteUpdate",i,g);for(const y of k)y.updateGuards.forEach(_=>{m.push(B(_,i,g))});return m.push(R),z(m)}).then(()=>{m=[];for(const y of O)if(y.beforeEnter)if(L(y.beforeEnter))for(const _ of y.beforeEnter)m.push(B(_,i,g));else m.push(B(y.beforeEnter,i,g));return m.push(R),z(m)}).then(()=>(i.matched.forEach(y=>y.enterCallbacks={}),m=pe(O,"beforeRouteEnter",i,g,re),m.push(R),z(m))).then(()=>{m=[];for(const y of h.list())m.push(B(y,i,g));return m.push(R),z(m)}).catch(y=>N(y,8)?y:Promise.reject(y))}function we(i,g,m){d.list().forEach(v=>re(()=>v(i,g,m)))}function Pe(i,g,m,v,k){const O=C(i,g);if(O)return O;const R=g===H,y=V?history.state:{};m&&(v||R?o.replace(i.fullPath,P({scroll:R&&y&&y.scroll},k)):o.push(i.fullPath,k)),s.value=i,Ce(i,g,m,R),ie()}let W;function at(){W||(W=o.listen((i,g,m)=>{if(!ke.listening)return;const v=A(i),k=j(v);if(k){M(P(k,{replace:!0,force:!0}),v).catch(X);return}l=v;const O=s.value;V&&qt(Oe(O.fullPath,m.delta),te()),Ee(v,O).catch(R=>N(R,12)?R:N(R,2)?(M(P(E(R.to),{force:!0}),v).then(y=>{N(y,20)&&!m.delta&&m.type===D.pop&&o.go(-1,!1)}).catch(X),Promise.reject()):(m.delta&&o.go(-m.delta,!1),se(R,v,O))).then(R=>{R=R||Pe(v,O,!1),R&&(m.delta&&!N(R,8)?o.go(-m.delta,!1):m.type===D.pop&&N(R,20)&&o.go(-1,!1)),we(v,O,R)}).catch(X)}))}let oe=F(),Se=F(),J;function se(i,g,m){ie(i);const v=Se.list();return v.length?v.forEach(k=>k(i,g,m)):console.error(i),Promise.reject(i)}function lt(){return J&&s.value!==H?Promise.resolve():new Promise((i,g)=>{oe.add([i,g])})}function ie(i){return J||(J=!i,at(),oe.list().forEach(([g,m])=>i?m(i):g()),oe.reset()),i}function Ce(i,g,m,v){const{scrollBehavior:k}=e;if(!V||!k)return Promise.resolve();const O=!m&&zt(Oe(i.fullPath,0))||(v||!m)&&history.state&&history.state.scroll||null;return dt().then(()=>k(i,g,O)).then(R=>R&&Gt(R)).catch(R=>se(R,i,g))}const ce=i=>o.go(i);let ae;const ee=new Set,ke={currentRoute:s,listening:!0,addRoute:f,removeRoute:w,clearRoutes:t.clearRoutes,hasRoute:b,getRoutes:S,resolve:A,options:e,push:T,replace:G,go:ce,back:()=>ce(-1),forward:()=>ce(1),beforeEach:a.add,beforeResolve:h.add,afterEach:d.add,onError:Se.add,isReady:lt,install(i){const g=this;i.component("RouterLink",hn),i.component("RouterView",gn),i.config.globalProperties.$router=g,Object.defineProperty(i.config.globalProperties,"$route",{enumerable:!0,get:()=>Y(s)}),V&&!ae&&s.value===H&&(ae=!0,T(o.location).catch(k=>{}));const m={};for(const k in H)Object.defineProperty(m,k,{get:()=>s.value[k],enumerable:!0});i.provide(ne,g),i.provide(Re,ft(m)),i.provide(ge,s);const v=i.unmount;ee.add(i),i.unmount=function(){ee.delete(i),ee.size<1&&(l=H,W&&W(),W=null,s.value=H,ae=!1,J=!1),v()}}};function z(i){return i.reduce((g,m)=>g.then(()=>re(m)),Promise.resolve())}return ke}function vn(e,t){const n=[],r=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let h=0;h<a;h++){const d=t.matched[h];d&&(e.matched.find(l=>U(l,d))?r.push(d):n.push(d));const s=e.matched[h];s&&(t.matched.find(l=>U(l,s))||o.push(s))}return[n,r,o]}function kn(){return $(ne)}function bn(e){return $(Re)}export{xe as NavigationFailureType,hn as RouterLink,gn as RouterView,H as START_LOCATION,Rn as createMemoryHistory,Cn as createRouter,nn as createRouterMatcher,En as createWebHashHistory,Kt as createWebHistory,N as isNavigationFailure,Sn as loadRouteLocation,ye as matchedRouteKey,wn as onBeforeRouteLeave,Pn as onBeforeRouteUpdate,an as parseQuery,Re as routeLocationKey,ne as routerKey,ge as routerViewLocationKey,je as stringifyQuery,Be as useLink,bn as useRoute,kn as useRouter,He as viewDepthKey};
