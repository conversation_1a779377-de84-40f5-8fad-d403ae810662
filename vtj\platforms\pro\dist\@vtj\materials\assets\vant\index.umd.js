(function(t,e){typeof exports=="object"&&typeof module!="undefined"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis!="undefined"?globalThis:t||self,t.VantMaterial=e())})(this,function(){"use strict";var Fe=Object.defineProperty,De=Object.defineProperties;var Le=Object.getOwnPropertyDescriptors;var r=Object.getOwnPropertySymbols;var Re=Object.prototype.hasOwnProperty,qe=Object.prototype.propertyIsEnumerable;var l=(t,e,a)=>e in t?Fe(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,n=(t,e)=>{for(var a in e||(e={}))Re.call(e,a)&&l(t,a,e[a]);if(r)for(var a of r(e))qe.call(e,a)&&l(t,a,e[a]);return t},s=(t,e)=>De(t,Le(e));function t(Oe,Pe){return Oe.map(Ee=>s(n({},Ee),{package:Pe}))}/**!
 * Copyright (c) 2025, VTJ.PRO All rights reserved.
 * @name @vtj/materials 
 * @<NAME_EMAIL> 
 * @version 0.12.70
 * @license <a href="https://vtj.pro/license.html">MIT License</a>
 */const e="0.12.70",a={name:"VanButton",alias:"Button",label:"按钮",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/button",props:[{name:"type",title:"类型，可选值为 primary success warning danger",defaultValue:"default",setters:"SelectSetter",options:["primary","success","warning","danger","default"]},{name:"size",title:"尺寸，可选值为 large small mini",defaultValue:"normal",setters:"SelectSetter",options:["large","small","mini","normal"]},{name:"text",title:"按钮文字",setters:"StringSetter"},{name:"color",title:"按钮颜色，支持传入 linear-gradient 渐变色",setters:"StringSetter"},{name:"icon",title:"左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",setters:"StringSetter"},{name:"icon-position",title:"图标展示位置，可选值为 right",setters:"SelectSetter",defaultValue:"left",options:["right","left"]},{name:"tag",title:"按钮根节点的 HTML 标签",defaultValue:"button",setters:"StringSetter"},{name:"native-type",title:"原生 button 标签的 type 属性",defaultValue:"button",setters:"StringSetter"},{name:"block",title:"是否为块级元素",setters:"BooleanSetter"},{name:"plain",title:"是否为朴素按钮",setters:"BooleanSetter"},{name:"square",title:"是否为方形按钮",setters:"BooleanSetter"},{name:"round",title:"是否为圆形按钮",setters:"BooleanSetter"},{name:"disabled",title:"是否禁用按钮",setters:"BooleanSetter"},{name:"hairline",title:"是否使用 0.5px 边框",setters:"BooleanSetter"},{name:"loading",title:"是否显示为加载状态",setters:"BooleanSetter"},{name:"loading-text",title:"加载状态提示文字",setters:"StringSetter"},{name:"loading-type",title:"加载图标类型",setters:"SelectSetter",defaultValue:"circular",options:["spinner","circular"]},{name:"loading-size",title:"加载图标大小，默认单位为 px",defaultValue:"20px",setters:["StringSetter","NumberSetter"]},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:"StringSetter"},{name:"replace",title:"是否在跳转时替换当前页面历史",setters:"BooleanSetter"}],events:["click","touchstart"],slots:["default","icon","loading"],snippet:{children:"按钮"}},i=[{name:"VanCell",alias:"Cell",label:"单元格",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/cell",props:[{name:"title",title:"左侧标题",setters:["StringSetter"]},{name:"value",title:"右侧标题",setters:["StringSetter"]},{name:"label",title:"标题下方的描述信息",setters:["StringSetter"]},{name:"size",title:"单元格大小，可选值为 large normal",setters:"SelectSetter",options:["large","normal"]},{name:"icon",title:"左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"tag",title:"根节点对应的 HTML 标签名",defaultValue:"div",setters:"StringSetter"},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"border",title:"是否显示内边框",defaultValue:!0,setters:"BooleanSetter"},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"},{name:"clickable",title:"是否开启点击反馈",defaultValue:null,setters:"BooleanSetter"},{name:"is-link",title:"是否展示右侧箭头并开启点击反馈",defaultValue:!1,setters:"BooleanSetter"},{name:"required",title:"是否显示表单必填星号",defaultValue:!1,setters:"BooleanSetter"},{name:"center",title:"是否使内容垂直居中",defaultValue:!1,setters:"BooleanSetter"},{name:"arrow-direction",title:"箭头方向，可选值为 left up down",defaultValue:"right",setters:"SelectSetter",options:["left","right","up","down"]},{name:"title-style",title:"左侧标题额外样式",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"title-class",title:"左侧标题额外类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"value-class",title:"右侧内容额外类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"label-class",title:"描述信息额外类名",setters:["StringSetter","ArraySetter","ObjectSetter"]}],events:["click"],slots:["title","value","label","icon","right-icon","extra"],snippet:{props:{title:"单元格",value:"内容"}}},{name:"VanCellGroup",alias:"CellGroup",label:"单元格组",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/cell",props:[{name:"title",title:"分组标题",setters:"StringSetter"},{name:"inset",title:"是否展示为圆角卡片风格",setters:"BooleanSetter",defaultValue:!1},{name:"border",title:"是否显示外边框",setters:"BooleanSetter",defaultValue:!0}],slots:["default","title"],snippet:{children:[{name:"VanCell",props:{title:"单元格",value:"内容"}},{name:"VanCell",props:{title:"单元格",value:"内容",label:"描述信息"}}]}}],o={name:"VanIcon",alias:"Icon",label:"图标",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/icon",props:[{name:"name",title:"图标名称或图片链接",setters:["VanIconSetter","StringSetter"]},{name:"dot",title:"是否显示图标右上角小红点",setters:"BooleanSetter",defaultValue:!1},{name:"badge",title:"图标右上角徽标的内容",setters:["NumberSetter","StringSetter"]},{name:"badge-props",title:"自定义徽标的属性，传入的对象会被透传给 Badge 组件的 props",setters:"ObjectSetter"},{name:"color",title:"图标颜色",defaultValue:"inherit",setters:"StringSetter"},{name:"size",title:"图标大小，如 20px 2em，默认单位为 px",defaultValue:"inherit",setters:["NumberSetter","StringSetter"]},{name:"class-prefix",title:"类名前缀，用于使用自定义图标",defaultValue:"van-icon",setters:"StringSetter"},{name:"tag",title:"根节点对应的 HTML 标签名",defaultValue:"i",setters:"StringSetter"}],events:["click"],snippet:{props:{name:"https://fastly.jsdelivr.net/npm/@vant/assets/icon-demo.png",size:30}}},u={name:"VanImage",alias:"Image",label:"图片",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/image",props:[{name:"src",title:"图片链接",setters:"StringSetter"},{name:"fit",title:"图片填充模式，等同于原生的 object-fit 属性",defaultValue:"fill",setters:"StringSetter"},{name:"position",title:"图片位置，等同于原生的 object-position 属性",defaultValue:"center",setters:["SelectSetter","StringSetter"],options:["top","right","bottom","left","center"]},{name:"alt",title:"替代文本",setters:"StringSetter"},{name:"width",title:"宽度，默认单位为 px",setters:["NumberSetter","StringSetter"]},{name:"height",title:"高度，默认单位为 px",setters:["NumberSetter","StringSetter"]},{name:"radius",title:"圆角大小，默认单位为 px",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"round",title:"是否显示为圆形",defaultValue:!1,setters:"BooleanSetter"},{name:"block",title:"是否将根节点设置为块级元素，默认情况下为 inline-block 元素",defaultValue:!1,setters:"BooleanSetter"},{name:"show-error",title:"是否展示图片加载失败提示",defaultValue:!0,setters:"BooleanSetter"},{name:"show-loading",title:"是否展示图片加载中提示",defaultValue:!0,setters:"BooleanSetter"},{name:"error-icon",title:"失败时提示的图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"photo-fail",setters:["VanIconSetter","StringSetter"]},{name:"loading-icon",title:"加载时提示的图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"photo",setters:["VanIconSetter","StringSetter"]},{name:"icon-size",title:"加载图标和失败图标的大小",defaultValue:32,setters:["NumberSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"crossorigin",title:"等同于原生的 crossorigin 属性",setters:"StringSetter"},{name:"referrerpolicy",title:"等同于原生的 referrerpolicy 属性",setters:"StringSetter"}],events:["click","load","error"],slots:["default","loading","error"],snippet:{props:{width:"10rem",height:"10rem",fit:"contain",src:"https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg"}}},m=[{name:"VanRow",alias:"Row",label:"布局行",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/col",props:[{name:"gutter",title:"列元素之间的间距(单位为 px)",setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"tag",title:"自定义元素标签",defaultValue:"div",setters:"StringSetter"},{name:"justify",title:"主轴对齐方式",defaultValue:"start",setters:"SelectSetter",options:["start","end","center","space-around","space-between"]},{name:"align",title:"交叉轴对齐方式",defaultValue:"top",setters:"SelectSetter",options:["top","center","bottom"]},{name:"wrap",title:"是否自动换行",defaultValue:!0,setters:"BooleanSetter"}],events:["click"],snippet:{children:[{name:"VanCol",children:"span:8",props:{span:"8"}},{name:"VanCol",children:"span:8",props:{span:"8"}}]}},{name:"VanCol",alias:"Col",label:"布局列",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/col",props:[{name:"span",title:"列元素宽度",setters:["NumberSetter","StringSetter"]},{name:"offset",title:"列元素偏移距离",setters:["NumberSetter","StringSetter"]},{name:"tag",title:"自定义元素标签",defaultValue:"div",setters:"StringSetter"}],events:["click"],snippet:{name:"VanCol",children:"span:8",props:{span:"8"}}}],d={name:"VanPopup",alias:"Popup",label:"弹出层",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/popup",props:[{name:"show",title:"是否显示弹出层",defaultValue:!1,setters:"BooleanSetter"},{name:"overlay",title:"是否显示遮罩层",defaultValue:!0,setters:"BooleanSetter"},{name:"position",title:"弹出位置，可选值为 top bottom right left",defaultValue:"center",setters:"SelectSetter",options:["top","bottom","right","left","center"]},{name:"overlay-class",title:"自定义遮罩层类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-style",title:"自定义遮罩层样式",setters:"ObjectSetter"},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"z-index",title:"将弹窗的 z-index 层级设置为一个固定值",defaultValue:2e3,setters:["NumberSetter","StringSetter"]},{name:"round",title:"是否显示圆角",defaultValue:!1,setters:"BooleanSetter"},{name:"lock-scroll",title:"是否锁定背景滚动",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-render",title:"是否在显示弹层时才渲染节点",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-popstate",title:"是否在页面回退时自动关闭",defaultValue:!1,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"closeable",title:"是否显示关闭图标",defaultValue:!1,setters:"BooleanSetter"},{name:"close-icon",title:"关闭图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"cross",setters:["VanIconSetter","StringSetter"]},{name:"close-icon-position",title:"关闭图标位置，可选值为 top-left bottom-left bottom-right",defaultValue:"top-right",setters:"SelectSetter",options:["top-left","top-right","bottom-left","bottom-right"]},{name:"before-close",title:"关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise",setters:"FunctionSetter"},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"transition",title:"动画类名，等价于 transition 的 name 属性",setters:"StringSetter"},{name:"transition-appear",title:"是否在初始渲染时启用过渡动画",defaultValue:!1,setters:"BooleanSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]},{name:"safe-area-inset-top",title:"是否开启顶部安全区适配",defaultValue:!1,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!1,setters:"BooleanSetter"}],events:["update:show","click","clickOverlay","clickCloseIcon","open","close","opened","closed"],slots:["default","overlay-content"],snippet:{props:{position:"right",show:!0,style:{width:"30%",height:"100%"}},children:"内容"}},S={name:"VanSpace",alias:"Space",label:"间距",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/space",props:[{name:"direction",title:"间距方向",defaultValue:"horizontal",setters:"SelectSetter",options:["vertical","horizontal"]},{name:"size",title:"间距大小，如 20px 2em，默认单位为 px，支持数组形式来分别设置横向和纵向间距",defaultValue:"8px",setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"align",title:"设置子元素的对齐方式",setters:"SelectSetter",options:["start","end","center","baseline"]},{name:"wrap",title:"是否自动换行，仅适用于水平方向排列",defaultValue:!1,setters:"BooleanSetter"},{name:"fill",title:"是否让 Space 变为一个块级元素，填充整个父元素",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{props:{},children:[{name:"VanButton",props:{type:"primary"},children:"按钮"},{name:"VanButton",props:{type:"primary"},children:"按钮"},{name:"VanButton",props:{type:"primary",style:{padding:"30px"}},children:"按钮"}]}},c={name:"VanToast",alias:"Toast",label:"轻提示",categoryId:"base",doc:"https://vant-ui.github.io/vant/#/zh-CN/toast",props:[{name:"show",title:"是否显示",defaultValue:!1,setters:"BooleanSetter"},{name:"type",title:"提示类型",defaultValue:"text",setters:"SelectSetter",options:["loading","success","fail","html","text"]},{name:"position",title:"位置",defaultValue:"middle",setters:"SelectSetter",options:["top","middle","bottom"]},{name:"message",title:`文本内容，支持通过
换行`,defaultValue:"",setters:"StringSetter"},{name:"word-break",title:"文本内容的换行方式",defaultValue:"break-all",setters:"",options:["normal","break-all","break-word"]},{name:"icon",title:"自定义图标，支持传入图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"icon-size",title:"图标大小，如 20px 2em，默认单位为 px",defaultValue:"36px",setters:["StringSetter","NumberSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"overlay",title:"是否显示背景遮罩层",defaultValue:!1,setters:"BooleanSetter"},{name:"forbid-click",title:"是否禁止背景点击",defaultValue:!1,setters:"BooleanSetter"},{name:"close-on-click",title:"是否在点击后关闭",defaultValue:!1,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭",defaultValue:!1,setters:"BooleanSetter"},{name:"loading-type",title:"加载图标类型, 可选值为 spinner",defaultValue:"circular",setters:"SelectSetter",options:["circular","spinner"]},{name:"duration",title:"展示时长(ms)，值为 0 时，toast 不会消失",defaultValue:2e3,setters:"NumberSetter"},{name:"class-name",title:"自定义类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-class",title:"自定义遮罩层类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-style",title:"自定义遮罩层样式",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"transition",title:"动画类名，等价于 transition 的name属性",defaultValue:"van-fade",setters:"StringSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",defaultValue:"body",setters:["StringSetter","ExpressionSetter"]},{name:"z-index",title:"将组件的 z-index 层级设置为一个固定值",defaultValue:"2000",setters:["StringSetter","NumberSetter"]}],events:["close","opened","update:show"],slots:["message"],snippet:{props:{show:!0},children:[{name:"VanImage",slot:"message",props:{src:"https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg",style:{height:"100px",width:"100px"}}}]}},p={name:"VanCalendar",alias:"Calendar",label:"日历",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/calendar",props:[{name:"type",title:"选择类型",defaultValue:"single",setters:"SelectSetter",options:["single","multiple","range"]},{name:"switch-mode",title:"切换模式",defaultValue:"none",setters:"SelectSetter",options:["none","month","year-month"]},{name:"title",title:"日历标题",defaultValue:"日期选择",setters:"StringSetter"},{name:"color",title:"主题色，对底部按钮和选中日期生效",defaultValue:"#1989fa",setters:"StringSetter"},{name:"min-date",title:"可选择的最小日期",defaultValue:"",setters:"ExpressionSetter"},{name:"max-date",title:"可选择的最大日期",defaultValue:"",setters:"ExpressionSetter"},{name:"default-date",title:"默认选中的日期，type 为 multiple 或 range 时为数组，传入 null 表示默认不选择",setters:["ExpressionSetter","ArraySetter","StringSetter"]},{name:"row-height",title:"日期行高",defaultValue:64,setters:["NumberSetter","StringSetter"]},{name:"formatter",title:"日期格式化函数",setters:"FunctionSetter"},{name:"poppable",title:"是否以弹层的形式展示日历",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-render",title:"是否只渲染可视区域的内容",defaultValue:!0,setters:"BooleanSetter"},{name:"show-mark",title:"是否显示月份背景水印",defaultValue:!0,setters:"BooleanSetter"},{name:"show-title",title:"是否展示日历标题",defaultValue:!0,setters:"BooleanSetter"},{name:"show-subtitle",title:"是否展示日历副标题（年月）",defaultValue:!0,setters:"BooleanSetter"},{name:"show-confirm",title:"是否展示确认按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下不能选择日期",defaultValue:!1,setters:"BooleanSetter"},{name:"confirmText",title:"确认按钮的文字",defaultValue:"确定",setters:"StringSetter"},{name:"confirm-disabled-text",title:"确认按钮处于禁用状态时的文字",defaultValue:"确定",setters:"StringSetter"},{name:"first-day-of-week",title:"设置周起始日",defaultValue:0,setters:"SelectSetter",options:[{label:"0",value:0},{label:"1",value:1},{label:"2",value:2},{label:"3",value:3},{label:"4",value:4},{label:"5",value:5},{label:"6",value:6}]},{name:"show",title:"是否显示日历弹窗",defaultValue:!1,setters:"BooleanSetter"},{name:"position",title:"弹出位置",defaultValue:"bottom",setters:"SelectSetter",options:["top","bottom","right","left"]},{name:"round",title:"是否显示圆角弹窗",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-popstate",title:"是否在页面回退时自动关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"safe-area-inset-top",title:"是否开启顶部安全区适配",defaultValue:!1,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启顶部安全区适配",defaultValue:!0,setters:"BooleanSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]},{name:"max-range",title:"日期区间最多可选天数",setters:["NumberSetter","StringSetter"]},{name:"range-prompt",title:"范围选择超过最多可选天数时的提示文案",defaultValue:"	最多选择 xx 天",setters:"StringSetter"},{name:"show-range-prompt",title:"范围选择超过最多可选天数时，是否展示提示文案",defaultValue:!0,setters:"BooleanSetter"},{name:"allow-same-day",title:"是否允许日期范围的起止时间为同一天",defaultValue:!1,setters:"BooleanSetter"}],events:["update:show","select","confirm","open","close","opened","closed","unselect","monthShow","overRange","clickSubtitle","clickDisabledDate","panelChange"],slots:["title","subtitle","month-title","footer","confirm-text","top-info","bottom-info","prev-month","prev-year","next-month","next-year"],snippet:{props:{show:!0,switchMode:"month"}}},f={name:"VanCascader",alias:"Cascader",label:"级联选择",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/cascader",props:[{name:"modelValue",title:"选中项的值",setters:["StringSetter","NumberSetter"]},{name:"title",title:"顶部标题",setters:"StringSetter"},{name:"options",title:"可选项数据源",defaultValue:[],setters:"ArraySetter"},{name:"placeholder",title:"未选中时的提示文案",defaultValue:"请选择",setters:"StringSetter"},{name:"active-color",title:"选中状态的高亮颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"swipeable",title:"是否开启手势左右滑动切换",defaultValue:!0,setters:"BooleanSetter"},{name:"closeable",title:"是否显示关闭图标",defaultValue:!0,setters:"BooleanSetter"},{name:"show-header",title:"是否展示标题栏",defaultValue:!0,setters:"BooleanSetter"},{name:"close-icon",title:"关闭图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"cross",setters:["VanIconSetter","StringSetter"]},{name:"field-names",title:"自定义 options 结构中的字段",defaultValue:{text:"text",value:"value",children:"children"},setters:"ObjectSetter"}],events:["update:modelValue","change","finish","close","clickTab"],slots:["title","option","options-top","options-bottom"],snippet:{props:{modelValue:"",title:"请选择所在地区",options:[{text:"浙江省",value:"330000",children:[{text:"杭州市",value:"330100"}]},{text:"江苏省",value:"320000",children:[{text:"南京市",value:"320100"}]}]}}},g=[{name:"VanCheckbox",alias:"Checkbox",label:"复选框",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/checkbox",props:[{name:"modelValue",title:"是否为选中状态",defaultValue:!1,setters:"BooleanSetter"},{name:"name",title:"标识符，通常为一个唯一的字符串或数字",setters:"ExpressionSetter"},{name:"shape",title:"形状，可选值为 square",defaultValue:"round",setters:"SelectSetter",options:["round","square"]},{name:"disabled",title:"是否禁用复选框",defaultValue:!1,setters:"BooleanSetter"},{name:"label-disabled",title:"是否禁用复选框文本点击",defaultValue:!1,setters:"BooleanSetter"},{name:"label-position",title:"文本位置，可选值为 left",defaultValue:"right",setters:"SelectSetter",options:["left","right"]},{name:"icon-size",title:"图标大小，默认单位为 px",defaultValue:"20px",setters:["StringSetter","NumberSetter"]},{name:"checked-color",title:"选中状态颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"bind-group",title:"是否与复选框组绑定",defaultValue:!0,setters:"BooleanSetter"},{name:"indeterminate",title:"是否为不确定状态",defaultValue:!1,setters:"BooleanSetter"}],events:["update:modelValue","change","click"],slots:["default","icon"],snippet:{children:"复选框"}},{name:"VanCheckboxGroup",alias:"CheckboxGroup",label:"复选框组",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/checkbox",props:[{name:"modelValue",title:"所有选中项的标识符",setters:"ArraySetter"},{name:"disabled",title:"是否禁用所有复选框",defaultValue:!1,setters:"BooleanSetter"},{name:"max",title:"最大可选数，0 为无限制",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"direction",title:"排列方向，可选值为 horizontal",defaultValue:"vertical",setters:"SelectSetter",options:["vertical","horizontal"]},{name:"icon-size",title:"所有复选框的图标大小，默认单位为 px",defaultValue:"20px",setters:["StringSetter","NumberSetter"]},{name:"checked-color",title:"所有复选框的选中状态颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"shape",title:"形状，可选值为 square",defaultValue:"round",setters:"SelectSetter",options:["round","square"]}],events:["update:modelValue","change"],slots:["default","toggleAll"],snippet:{props:{modelValue:["a","b"]},children:[{name:"VanCheckbox",props:{name:"a"},children:"复选框 a"},{name:"VanCheckbox",props:{name:"b"},children:"复选框 b"}]}}],V={name:"VanDatePicker",alias:"DatePicker",label:"日期选择",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/date-picker",props:[{name:"modelValue",title:"当前选中的日期",defaultValue:[],setters:"ArraySetter"},{name:"columns-type",title:"选项类型，由 year、month 和 day 组成的数组",defaultValue:["year","month","day"],setters:"ArraySetter"},{name:"min-date",title:"可选的最小时间，精确到日",setters:"ExpressionSetter"},{name:"max-date",title:"可选的最大时间，精确到日",setters:"ExpressionSetter"},{name:"title",title:"顶部栏标题",defaultValue:"",setters:"StringSetter"},{name:"confirm-button-text",title:"确认按钮文字",defaultValue:"确认",setters:"StringSetter"},{name:"cancel-button-text",title:"取消按钮文字",defaultValue:"取消",setters:"StringSetter"},{name:"show-toolbar",title:"是否显示顶部栏",defaultValue:!0,setters:"BooleanSetter"},{name:"loading",title:"是否显示加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法切换选项",defaultValue:!1,setters:"BooleanSetter"},{name:"filter",title:"选项过滤函数",setters:"FunctionSetter"},{name:"formatter",title:"选项格式化函数",setters:"FunctionSetter"},{name:"option-height",title:"选项高度，支持 px vw vh rem 单位，默认 px",defaultValue:44,setters:["NumberSetter","StringSetter"]},{name:"visible-option-num",title:"可见的选项个数",defaultValue:6,setters:["NumberSetter","StringSetter"]},{name:"swipe-duration",title:"快速滑动时惯性滚动的时长，单位 ms",defaultValue:1e3,setters:["NumberSetter","StringSetter"]}],events:[{name:"update:modelValue"},{name:"confirm",params:["data"]},{name:"cancel",params:["data"]},{name:"change",params:["data"]}],slots:["toolbar","title","confirm","cancel","option","columns-top","columns-bottom"],snippet:{props:{modelValue:[]}}},h={name:"VanField",alias:"Field",label:"输入框",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/field",props:[{name:"modelValue",title:"当前输入的值",setters:["StringSetter","NumberSetter"]},{name:"label",title:"输入框左侧文本",setters:"StringSetter"},{name:"name",title:"名称，作为提交表单时的标识符",setters:"StringSetter"},{name:"id",title:"输入框 id，同时会设置 label 的 for 属性",defaultValue:"van-field-n-input",setters:"StringSetter"},{name:"type",title:"输入框类型, 支持原生 input 标签的所有 type 属性，额外支持了 digit 类型",defaultValue:"text",setters:"StringSetter"},{name:"size",title:"大小，可选值为 large normal",setters:"SelectSetter",options:["large","normal"]},{name:"maxlength",title:"输入的最大字符数",setters:["NumberSetter","StringSetter"]},{name:"placeholder",title:"输入框占位提示文字",setters:"StringSetter"},{name:"border",title:"是否显示内边框",defaultValue:!0,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用输入框",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法输入内容",defaultValue:!1,setters:"BooleanSetter"},{name:"colon",title:"是否在 label 后面添加冒号",defaultValue:!1,setters:"BooleanSetter"},{name:"required",title:"是否显示表单必填星号",defaultValue:null,setters:["BooleanSetter","StringSetter"]},{name:"center",title:"是否使内容垂直居中",defaultValue:!1,setters:"BooleanSetter"},{name:"clearable",title:"是否启用清除图标，点击清除图标后会清空输入框",defaultValue:!1,setters:"BooleanSetter"},{name:"clear-icon",title:"清除图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"clear",setters:["VanIconSetter","StringSetter"]},{name:"clear-trigger",title:"显示清除图标的时机，always 表示输入框不为空时展示，focus 表示输入框聚焦且不为空时展示",defaultValue:"focus",setters:"SelectSetter",options:["focus","always"]},{name:"clickable",title:"是否开启点击反馈",defaultValue:!1,setters:"BooleanSetter"},{name:"is-link",title:"是否展示右侧箭头并开启点击反馈",defaultValue:!1,setters:"BooleanSetter"},{name:"autofocus",title:"是否自动聚焦，iOS 系统不支持该属性",defaultValue:!1,setters:"BooleanSetter"},{name:"show-word-limit",title:"是否显示字数统计，需要设置 maxlength 属性",defaultValue:!1,setters:"BooleanSetter"},{name:"error",title:"是否将输入内容标红",defaultValue:!1,setters:"BooleanSetter"},{name:"error-message",title:"底部错误提示文案，为空时不展示",setters:"StringSetter"},{name:"error-message-align",title:"错误提示文案对齐方式，可选值为 center right",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"formatter",title:"输入内容格式化函数",setters:"FunctionSetter"},{name:"format-trigger",title:"格式化函数触发的时机，可选值为 onBlur",defaultValue:"onChange",setters:"SelectSetter",options:["onChange","onBlur"]},{name:"arrow-direction",title:"箭头方向，可选值为 left up down",defaultValue:"right",setters:"SelectSetter",options:["left","right","up","down"]},{name:"label-class",title:"左侧文本额外类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"label-width",title:"左侧文本宽度，默认单位为 px",defaultValue:"6.2em",setters:["StringSetter","NumberSetter"]},{name:"label-align",title:"左侧文本对齐方式，可选值为 center right top",defaultValue:"left",setters:"SelectSetter",options:["left","center","right","top"]},{name:"input-align",title:"输入框对齐方式，可选值为 center right",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"autosize",title:"	是否自适应内容高度，只对 textarea 有效，可传入对象,如 { maxHeight: 100, minHeight: 50 }，单位为px",defaultValue:!1,setters:["BooleanSetter","ObjectSetter"]},{name:"rows",title:"输入框行数，仅 type 为 textarea 时有效",defaultValue:"2",setters:"StringSetter"},{name:"left-icon",title:"左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"right-icon",title:"右侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"rules",title:"表单校验规则，详见 Form 组件",setters:"ArraySetter"},{name:"autocomplete",title:"HTML 原生属性，用于控制自动完成功能，详见 MDN - autocomplete",setters:"StringSetter"},{name:"autocapitalize",title:"HTML 原生属性，用于控制文本输入时是否自动大写，此 API 仅在部分浏览器支持，详见 MDN - autocapitalize",setters:"StringSetter"},{name:"enterkeyhint",title:"HTML 原生属性，用于控制回车键样式，此 API 仅在部分浏览器支持，详见 MDN - enterkeyhint",setters:"StringSetter"},{name:"spellcheck ",title:"HTML 原生属性，用于检查元素的拼写错误，此 API 仅在部分浏览器支持，详见 MDN - spellcheck",setters:"BooleanSetter"},{name:"autocorrect ",title:"仅 Safari 适用，用于自动更正输入的文本，详见 MDN - autocorrect",setters:"StringSetter"}],events:["update:modelValue","focus","blur","clear","click","clickInput","clickLeftIcon","clickRightIcon","startValidate","endValidate"],slots:["default","label","input","left-icon","right-icon","button","error-message","extra"],snippet:{props:{label:"文本",placeholder:"请输入用户名"}}},b={name:"VanForm",alias:"Form",label:"表单",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/form",props:[{name:"label-width",title:"表单项 label 宽度，默认单位为px",defaultValue:"6.2em",setters:["StringSetter","NumberSetter"]},{name:"label-align",title:"表单项 label 对齐方式，可选值为 center right top",defaultValue:"left",setters:"SelectSetter",options:["left","center","right","top"]},{name:"input-align",title:"输入框对齐方式，可选值为 center right",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"error-message-align",title:"错误提示文案对齐方式，可选值为 center right",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"validate-trigger",title:"表单校验触发时机，可选值为 onChange、onSubmit 支持通过数组同时设置多个值",defaultValue:"onBlur",setters:["StringSetter","ArraySetter"]},{name:"colon",title:"是否在 label 后面添加冒号",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用表单中的所有输入框",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否将表单中的所有输入框设置为只读状态",defaultValue:!1,setters:"BooleanSetter"},{name:"required",title:"是否显示表单必填星号",defaultValue:null,setters:["BooleanSetter","StringSetter"]},{name:"validate-first",title:"是否在某一项校验不通过时停止校验",defaultValue:!1,setters:"BooleanSetter"},{name:"scroll-to-error",title:"是否在提交表单且校验不通过时滚动至错误的表单项",defaultValue:!1,setters:"BooleanSetter"},{name:"scroll-to-error-position",title:"滚动至错误的表单项时的位置，可选值为 center | end | nearest | start",setters:"SelectSetter",options:["center","end","nearest","start"]},{name:"show-error",title:"是否在校验不通过时标红输入框",defaultValue:!1,setters:"BooleanSetter"},{name:"show-error-message",title:"是否在校验不通过时在输入框下方展示错误提示",defaultValue:!0,setters:"BooleanSetter"},{name:"submit-on-enter",title:"是否在按下回车键时提交表单",defaultValue:!0,setters:"BooleanSetter"}],events:["submit","failed"],slots:["default"],snippet:{children:[{name:"VanCellGroup",props:{inset:!0},children:[{name:"VanField",props:{name:"title",label:"标题",placeholder:"标题"}}]}]}},v={name:"VanNumberKeyboard",alias:"NumberKeyboard",label:"数字键盘",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/number-keyboard",props:[{name:"modelValue",title:"当前输入值",setters:"StringSetter"},{name:"show",title:"是否显示键盘",setters:"BooleanSetter"},{name:"title",title:"键盘标题",setters:"StringSetter"},{name:"theme",title:"样式风格，可选值为 custom",defaultValue:"default",setters:"SelectSetter",options:["default","custom"]},{name:"maxlength",title:"输入值最大长度",defaultValue:"Infinity",setters:["StringSetter","NumberSetter"]},{name:"transition",title:"是否开启过场动画",defaultValue:!0,setters:"BooleanSetter"},{name:"z-index",title:"键盘 z-index 层级",defaultValue:"100",setters:["StringSetter","NumberSetter"]},{name:"extra-key",title:"底部额外按键的内容",defaultValue:"",setters:["StringSetter","ArraySetter"]},{name:"close-button-text",title:"关闭按钮文字，空则不展示",setters:"StringSetter"},{name:"delete-button-text",title:"删除按钮文字，空则展示删除图标",setters:"StringSetter"},{name:"close-button-loading",title:'是否将关闭按钮设置为加载中状态，仅在 theme="custom" 时有效',defaultValue:!1,setters:"BooleanSetter"},{name:"show-delete-key",title:"是否展示删除图标",defaultValue:!0,setters:"BooleanSetter"},{name:"blur-on-close",title:"是否在点击关闭按钮时触发 blur 事件",defaultValue:!0,setters:"BooleanSetter"},{name:"hide-on-click-outside",title:"是否在点击外部时收起键盘",defaultValue:!0,setters:"BooleanSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!0,setters:"BooleanSetter"},{name:"random-key-order",title:"是否将通过随机顺序展示按键",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"input",params:["key"]},{name:"delete",params:["key"]},{name:"close",params:["key"]},{name:"blur",params:["key"]},{name:"show",params:["key"]},{name:"hide",params:["key"]},{name:"update:modelValue"}],slots:["delete","extra-key","title-left"],snippet:{props:{show:!0}}},x={name:"VanPasswordInput",alias:"PasswordInput",label:"密码输入框",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/password-input",props:[{name:"value",title:"密码值",setters:"StringSetter"},{name:"info",title:"输入框下方文字提示",setters:"StringSetter"},{name:"error-info",title:"输入框下方错误提示",setters:"StringSetter"},{name:"length",title:"密码最大长度",defaultValue:6,setters:["NumberSetter","StringSetter"]},{name:"gutter",title:"输入框格子之间的间距，如 20px 2em，默认单位为px",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"mask",title:"是否隐藏密码内容",defaultValue:!0,setters:"BooleanSetter"},{name:"focused",title:"是否已聚焦，聚焦时会显示光标",defaultValue:!1,setters:"BooleanSetter"}],events:["focus"],snippet:{props:{value:"123456"}}},y={name:"VanPicker",alias:"Picker",label:"选择器",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/picker",props:[{name:"modelValue",title:"当前选中项对应的值",setters:"ArraySetter"},{name:"columns",title:"对象数组，配置每一列显示的数据",defaultValue:[],setters:"ArraySetter"},{name:"columns-field-names",title:"自定义 columns 结构中的字段",defaultValue:{text:"text",value:"value",children:"children"},setters:"ObjectSetter"},{name:"title",title:"顶部栏标题",setters:"StringSetter"},{name:"confirm-button-text",title:"确认按钮文字，设置为空字符串可以隐藏按钮",defaultValue:"确认",setters:"StringSetter"},{name:"cancel-button-text",title:"取消按钮文字，设置为空字符串可以隐藏按钮",defaultValue:"取消",setters:"StringSetter"},{name:"toolbar-position",title:"顶部栏位置，可选值为 bottom",defaultValue:"top",setters:"SelectSetter",options:["top","bottom"]},{name:"loading",title:"是否显示加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法切换选项",defaultValue:!1,setters:"BooleanSetter"},{name:"show-toolbar",title:"是否显示顶部栏",defaultValue:!0,setters:"BooleanSetter"},{name:"allow-html",title:"是否允许选项内容中渲染 HTML",defaultValue:!1,setters:"BooleanSetter"},{name:"option-height",title:"选项高度，支持 px vw vh rem 单位，默认 px",defaultValue:44,setters:["NumberSetter","StringSetter"]},{name:"visible-option-num",title:"可见的选项个数",defaultValue:6,setters:["NumberSetter","StringSetter"]},{name:"swipe-duration",title:"快速滑动时惯性滚动的时长，单位 ms",defaultValue:1e3,setters:["NumberSetter","StringSetter"]}],events:[{name:"confirm",params:["data"]},{name:"cancel",params:["data"]},{name:"change",params:["data"]},{name:"clickOption",params:["data"]},{name:"scrollInto",params:["data"]},{name:"update:modelValue"}],slots:["toolbar","title","confirm","cancel","option","columns-top","columns-bottom"],snippet:{props:{title:"标题",columns:[{text:"杭州",value:"Hangzhou"},{text:"宁波",value:"Ningbo"},{text:"温州",value:"Wenzhou"},{text:"绍兴",value:"Shaoxing"},{text:"湖州",value:"Huzhou"}]}}},B={name:"VanPickerGroup",alias:"PickerGroup",label:"选择器组",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/picker-group",props:[{name:"active-tab",title:"设置当前选中的标签",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"tabs",title:"设置标签页的标题",defaultValue:[],setters:"ArraySetter"},{name:"title",title:"顶部栏标题",defaultValue:"",setters:"StringSetter"},{name:"show-toolbar",title:"是否显示顶部栏",defaultValue:!0,setters:"BooleanSetter"},{name:"next-step-text",title:"下一步按钮的文字",defaultValue:"",setters:"StringSetter"},{name:"confirm-button-text",title:"确认按钮的文字",defaultValue:"确认",setters:"StringSetter"},{name:"cancel-button-text",title:"取消按钮的文字",defaultValue:"取消",setters:"StringSetter"}],events:["confirm","cancel","update:activeTab"],slots:["default","toolbar","title","confirm","cancel"],snippet:{props:{title:"预约日期",tabs:["选择日期","选择时间"]},children:[{name:"VanDatePicker"},{name:"VanTimePicker"}]}},N=[{name:"VanRadio",alias:"Radio",label:"单选框",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/radio",props:[{name:"name",title:"标识符，通常为一个唯一的字符串或数字",setters:"ExpressionSetter"},{name:"shape",title:"形状，可选值为 square dot",defaultValue:"round",setters:"SelectSetter",options:["square","dot","round"]},{name:"disabled",title:"是否为禁用状态",defaultValue:!1,setters:"BooleanSetter"},{name:"label-disabled",title:"是否禁用文本内容点击",defaultValue:!1,setters:"BooleanSetter"},{name:"label-position",title:"文本位置，可选值为 left",defaultValue:"right",setters:"SelectSetter",options:["left","right"]},{name:"icon-size",title:"图标大小，默认单位为 px",defaultValue:"20px",setters:["StringSetter","NumberSetter"]},{name:"checked-color",title:"选中状态颜色",defaultValue:"#1989fa",setters:"StringSetter"}],events:[{name:"click",params:["event"]}],slots:[{name:"default"},{name:"icon"}],snippet:{props:{modelValue:"checked",name:"checked"},children:"单选框 1"}},{name:"VanRadioGroup",alias:"RadioGroup",label:"单选框组",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/radio",props:[{name:"modelValue",title:"当前选中项的标识符",setters:"ExpressionSetter"},{name:"disabled",title:"是否禁用所有单选框",defaultValue:!1,setters:"BooleanSetter"},{name:"direction",title:"排列方向",defaultValue:"vertical",setters:"SelectSetter",options:["horizontal","vertical"]},{name:"icon-size  ",title:"所有单选框的图标大小，默认单位为 px",defaultValue:"20px",setters:["StringSetter","NumberSetter"]},{name:"checked-color",title:"所有单选框的选中状态颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"shape",title:"形状",defaultValue:"round",setters:"SelectSetter",options:["square","dot"]}],events:[{name:"update:modelValue"},{name:"change",params:["name"]}],snippet:{props:{modelValue:["1","2"]},children:[{name:"VanRadio",props:{name:"1"},children:"单选框 1"},{name:"VanRadio",props:{name:"2"},children:"单选框 2"}]}}],w={name:"VanRate",alias:"Rate",label:"评分",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/rate",props:[{name:"modelValue",title:"当前分值",setters:"NumberSetter"},{name:"count",title:"图标总数",defaultValue:5,setters:["NumberSetter","StringSetter"]},{name:"size",title:"图标大小，默认单位为px",defaultValue:20,setters:["NumberSetter","StringSetter"]},{name:"gutter",title:"图标间距，默认单位为px",defaultValue:4,setters:["NumberSetter","StringSetter"]},{name:"color",title:"选中时的颜色",defaultValue:"#ee0a24",setters:"StringSetter"},{name:"void-color",title:"未选中时的颜色",defaultValue:"#c8c9cc",setters:"StringSetter"},{name:"disabled-color",title:"禁用时的颜色",defaultValue:"#c8c9cc",setters:"StringSetter"},{name:"icon",title:"选中时的图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"start",setters:["VanIconSetter","StringSetter"]},{name:"void-icon",title:"未选中时的图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"star-o",setters:["VanIconSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"allow-half",title:"是否允许半选",defaultValue:!1,setters:"BooleanSetter"},{name:"clearable",title:"是否允许再次点击后清除",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法修改评分",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用评分",defaultValue:!1,setters:"BooleanSetter"},{name:"touchable",title:"是否可以通过滑动手势选择评分",defaultValue:!0,setters:"BooleanSetter"}],events:["update:modelValue","change"],snippet:{props:{modelValue:3}}},I={name:"VanSearch",alias:"Search",label:"搜索",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/search",props:[{name:"modelValue",title:"当前输入的值",setters:["NumberSetter","StringSetter"]},{name:"label",title:"搜索框左侧文本",setters:"StringSetter"},{name:"name",title:"名称，作为提交表单时的标识符",setters:"StringSetter"},{name:"shape",title:"搜索框形状",defaultValue:"square",setters:"SelectSetter",options:["square","round"]},{name:"id",title:"搜索框 id，同时会设置 label 的 for 属性",defaultValue:"van-search-n-input",setters:"StringSetter"},{name:"background",title:"搜索框外部背景色",defaultValue:"	#f2f2f2",setters:"StringSetter"},{name:"maxlength",title:"输入的最大字符数",setters:["NumberSetter","StringSetter"]},{name:"placeholder",title:"占位提示文字",setters:"StringSetter"},{name:"clearable",title:"是否启用清除图标，点击清除图标后会清空输入框",defaultValue:!0,setters:"BooleanSetter"},{name:"clear-icon",title:"清除图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"clear",setters:["VanIconSetter","StringSetter"]},{name:"clear-trigger",title:"显示清除图标的时机，always 表示输入框不为空时展示，focus 表示输入框聚焦且不为空时展示",defaultValue:"focus",setters:"SelectSetter",options:["focus","always"]},{name:"autofocus",title:"是否自动聚焦，iOS 系统不支持该属性",defaultValue:!1,setters:"BooleanSetter"},{name:"show-action",title:"是否在搜索框右侧显示取消按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"action-text",title:"取消按钮文字",defaultValue:"取消",setters:"StringSetter"},{name:"disabled",title:"是否禁用输入框",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否将输入框设为只读状态，只读状态下无法输入内容",defaultValue:!1,setters:"BooleanSetter"},{name:"error",title:"是否将输入内容标红",defaultValue:!1,setters:"BooleanSetter"},{name:"error-message",title:"底部错误提示文案，为空时不展示",setters:"StringSetter"},{name:"formatter",title:"输入内容格式化函数",setters:"FunctionSetter"},{name:"format-trigger",title:"格式化函数触发的时机",defaultValue:"onChange",setters:"SelectSetter",options:["onChange","onBlur"]},{name:"input-align",title:"输入框内容对齐方式",defaultValue:"left",setters:"SelectSetter",options:["left","center","right"]},{name:"left-icon",title:"输入框左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"search",setters:["VanIconSetter","StringSetter"]},{name:"right-icon",title:"输入框右侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"autocomplete",title:"input 标签原生的自动完成属性",setters:"StringSetter"}],events:[{name:"search",params:["value"]},{name:"update:modelValue",params:["value"]},{name:"focus",params:["event"]},{name:"blur",params:["event"]},{name:"clickInput",params:["event"]},{name:"clickLeftIcon",params:["event"]},{name:"clickRightIcon",params:["event"]},{name:"clear",params:["event"]},{name:"cancel"}],slots:["left","action","label","left-icon","right-icon"],snippet:{props:{placeholder:"请输入搜索关键词"}}},k={name:"VanSlider",alias:"Slider",label:"滑块",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/slider",props:[{name:"modelValue",title:"当前进度百分比，在双滑块模式下为数组格式",defaultValue:0,setters:["NumberSetter","ArraySetter"]},{name:"max",title:"最大值",defaultValue:100,setters:["NumberSetter","StringSetter"]},{name:"min",title:"最小值",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"step",title:"步长",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"bar-height",title:"进度条高度，默认单位为 px",defaultValue:2,setters:["NumberSetter","StringSetter"]},{name:"button-size",title:"滑块按钮大小，默认单位为 px",defaultValue:24,setters:["NumberSetter","StringSetter"]},{name:"active-color",title:"进度条激活态颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"inactive-color",title:"进度条非激活态颜色",defaultValue:"#e5e5e5",setters:"StringSetter"},{name:"range",title:"是否开启双滑块模式",defaultValue:!1,setters:"BooleanSetter"},{name:"reverse",title:"是否将进度条反转",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用滑块",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法修改滑块的值",defaultValue:!1,setters:"BooleanSetter"},{name:"vertical",title:"vertical",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"update:modelValue",params:["value"]},{name:"change",params:["value"]},{name:"dragStart",params:["event"]},{name:"dragEnd",params:["event"]}],slots:["button","left-button","right-button"],snippet:{props:{modelValue:50}}},z={name:"VanSignature",alias:"Signature",label:"签名",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/signature",props:[{name:"type",title:"导出图片类型",defaultValue:"png",setters:"StringSetter"},{name:"pen-color",title:"笔触颜色，默认黑色",defaultValue:"#000000",setters:"StringSetter"},{name:"line-width",title:"线条宽度",defaultValue:3,setters:"NumberSetter"},{name:"background-color",title:"背景颜色",setters:"StringSetter"},{name:"tips",title:"当不支持 Canvas 的时候出现的提示文案",setters:"StringSetter"},{name:"clear-button-text",title:"清除按钮文案",defaultValue:"清空",setters:"StringSetter"},{name:"confirm-button-text",title:"	确认按钮文案",defaultValue:"确认",setters:"StringSetter"}],events:[{name:"start"},{name:"end"},{name:"signing",params:["event"]},{name:"submit",params:["data"]},{name:"clear"}]},C={name:"VanStepper",alias:"Stepper",label:"步进器",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/stepper",props:[{name:"modelValue",title:"当前输入的值",setters:["NumberSetter","StringSetter"]},{name:"min",title:"最小值",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"max",title:"最大值",setters:["NumberSetter","StringSetter"]},{name:"auto-fixed",title:"是否自动校正超出限制范围的数值，设置为 false 后输入超过限制范围的数值将不会自动校正",defaultValue:!0,setters:"BooleanSetter"},{name:"default-value",title:"初始值，当 v-model 为空时生效",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"step",title:"步长，每次点击时改变的值",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"name",title:"标识符，通常为一个唯一的字符串或数字，可以在 change 事件回调参数中获取",setters:["NumberSetter","StringSetter"]},{name:"input-width",title:"输入框宽度，默认单位为 px",defaultValue:32,setters:["NumberSetter","StringSetter"]},{name:"button-size",title:"按钮大小以及输入框高度，默认单位为 px",defaultValue:28,setters:["NumberSetter","StringSetter"]},{name:"decimal-length",title:"固定显示的小数位数",setters:["NumberSetter","StringSetter"]},{name:"theme",title:"样式风格",setters:"SelectSetter",options:["round"]},{name:"placeholder",title:"输入框占位提示文字",setters:"StringSetter"},{name:"integer",title:"是否只允许输入整数",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用步进器",defaultValue:!1,setters:"BooleanSetter"},{name:"disable-plus",title:"是否禁用增加按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"disable-minus",title:"是否禁用减少按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"disable-input",title:"是否禁用输入框",defaultValue:!1,setters:"BooleanSetter"},{name:"before-change",title:"输入值变化前的回调函数，返回 false 可阻止输入，支持返回 Promise",defaultValue:!1,setters:"FunctionSetter"},{name:"show-plus",title:"是否显示增加按钮",defaultValue:!0,setters:"FunctionSetter"},{name:"show-minus",title:"是否显示减少按钮",defaultValue:!0,setters:"FunctionSetter"},{name:"show-input",title:"是否显示输入框",defaultValue:!0,setters:"FunctionSetter"},{name:"long-press",title:"是否开启长按手势，开启后可以长按增加和减少按钮",defaultValue:!0,setters:"FunctionSetter"},{name:"allow-empty",title:"是否允许输入的值为空，设置为 true 后允许传入空字符串",defaultValue:!1,setters:"FunctionSetter"}],events:[{name:"update:modelValue"},{name:"change",params:["value","details"]},{name:"overlimit"},{name:"plus"},{name:"minus"},{name:"focus",params:["event"]},{name:"blur",params:["event"]}],snippet:{props:{modelValue:1}}},A={name:"VanSwitch",alias:"Switch",label:"开关",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/switch",props:[{name:"modelValue",title:"开关选中状态",defaultValue:!1,setters:"ExpressionSetter"},{name:"loading",title:"是否为加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否为禁用状态",defaultValue:!1,setters:"BooleanSetter"},{name:"size",title:"开关按钮的尺寸，默认单位为 px",defaultValue:26,setters:["NumberSetter","StringSetter"]},{name:"active-color",title:"打开时的背景色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"inactive-color",title:"关闭时的背景色",defaultValue:"rgba(120, 120, 128, 0.16)",setters:"StringSetter"},{name:"active-value",title:"打开时对应的值",defaultValue:!0,setters:"ExpressionSetter"},{name:"inactive-value",title:"关闭时对应的值",defaultValue:!1,setters:"ExpressionSetter"}],events:[{name:"update:modelValue"},{name:"change",params:["value"]},{name:"click",params:["event"]}],slots:["node","background"],snippet:{}},T={name:"VanTimePicker",alias:"TimePicker",label:"时间选择",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/time-picker",props:[{name:"modelValue",title:"当前选中的时间",setters:"ArraySetter"},{name:"columns-type",title:"选项类型，由 hour、minute 和 second 组成的数组",defaultValue:["hour","minute"],setters:"ArraySetter"},{name:"min-hour",title:"可选的最小小时",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"max-hour",title:"可选的最大小时",defaultValue:23,setters:["NumberSetter","StringSetter"]},{name:"min-minute",title:"可选的最小分钟",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"max-minute",title:"可选的最大分钟",defaultValue:59,setters:["NumberSetter","StringSetter"]},{name:"min-second",title:"可选的最小秒数",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"max-second",title:"可选的最大秒数",defaultValue:59,setters:["NumberSetter","StringSetter"]},{name:"min-time",title:"可选的最小时间，格式参考 07:40:00，使用时 min-hour min-minute min-second 不会生效",setters:"StringSetter"},{name:"max-time",title:"可选的最大时间，格式参考 10:20:00，使用时 max-hour max-minute max-second 不会生效",setters:"StringSetter"},{name:"title",title:"顶部栏标题",defaultValue:"",setters:"StringSetter"},{name:"confirm-button-text",title:"确认按钮文字",defaultValue:"确认",setters:"StringSetter"},{name:"cancel-button-text",title:"取消按钮文字",defaultValue:"取消",setters:"StringSetter"},{name:"show-toolbar",title:"是否显示顶部栏",defaultValue:!0,setters:"BooleanSetter"},{name:"loading",title:"是否显示加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法切换选项",defaultValue:!1,setters:"BooleanSetter"},{name:"filter",title:"选项过滤函数",setters:"FunctionSetter"},{name:"formatter",title:"选项格式化函数",setters:"FunctionSetter"},{name:"option-height",title:"选项高度，支持 px vw vh rem 单位，默认 px",defaultValue:44,setters:["NumberSetter","StringSetter"]},{name:"visible-option-num",title:"可见的选项个数",defaultValue:6,setters:["NumberSetter","StringSetter"]},{name:"swipe-duration",title:"快速滑动时惯性滚动的时长，单位 ms",defaultValue:1e3,setters:["NumberSetter","StringSetter"]}],events:[{name:"confirm",params:["data"]},{name:"cancel",params:["data"]},{name:"change",params:["data"]},{name:"update:modelValue"}],slots:[{name:"toolbar"},{name:"title"},{name:"confirm"},{name:"cancel"},{name:"toolbar"},{name:"option",params:["option","index"]},{name:"toolbar"},{name:"columns-top"},{name:"columns-bottom"}],snippet:{props:{title:"选择时间"}}},j={name:"VanUploader",alias:"Uploader",label:"文件上传",categoryId:"form",doc:"https://vant-ui.github.io/vant/#/zh-CN/uploader",props:[{name:"modelValue",title:"已上传的文件列表",setters:"ArraySetter"},{name:"accept",title:"允许上传的文件类型",defaultValue:"image/*",setters:"StringSetter"},{name:"name",title:"标识符，通常为一个唯一的字符串或数字，可以在回调函数的第二项参数中获取",setters:["NumberSetter","StringSetter"]},{name:"preview-size",title:"预览图和上传区域的尺寸，默认单位为 px",defaultValue:80,setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"preview-image",title:"是否在上传完成后展示预览图",defaultValue:!0,setters:"BooleanSetter"},{name:"preview-full-image",title:"是否在点击预览图后展示全屏图片预览",defaultValue:!0,setters:"BooleanSetter"},{name:"preview-options",title:"全屏图片预览的配置项",setters:"ObjectSetter"},{name:"multiple",title:"是否开启图片多选，部分安卓机型不支持",defaultValue:!1,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用文件上传",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否将上传区域设置为只读状态",defaultValue:!1,setters:"BooleanSetter"},{name:"deletable",title:"是否展示删除按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"reupload",title:"是否开启覆盖上传，开启后会关闭图片预览",defaultValue:!1,setters:"BooleanSetter"},{name:"show-upload",title:"是否展示上传区域",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-load",title:"是否开启图片懒加载，须配合 Lazyload 组件使用",defaultValue:!1,setters:"BooleanSetter"},{name:"capture",title:"图片选取模式，可选值为 camera (直接调起摄像头)",setters:"StringSetter"},{name:"after-read",title:"文件读取完成后的回调函数",setters:"FunctionSetter"},{name:"before-read",title:"文件读取前的回调函数，返回 false 可终止文件读取，支持返回 Promise",setters:"FunctionSetter"},{name:"before-delete",title:"文件删除前的回调函数，返回 false 可终止文件读取，支持返回 Promise",setters:"FunctionSetter"},{name:"max-size",title:"文件大小限制，单位为 byte",defaultValue:1/0,setters:["NumberSetter","StringSetter","FunctionSetter"]},{name:"max-count",title:"文件上传数量限制",defaultValue:1/0,setters:["NumberSetter","StringSetter"]},{name:"result-type",title:"文件读取结果类型，可选值为 file text",defaultValue:"dataUrl",setters:"SelectSetter",options:["dataUrl","file","text"]},{name:"upload-text",title:"上传区域文字提示",setters:"StringSetter"},{name:"image-fit",title:"预览图裁剪模式",defaultValue:"cover",setters:"SelectSetter",options:["contain","cover","fill","none","scale-down"]},{name:"upload-icon",title:"上传区域图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"photograph",setters:["VanIconSetter","StringSetter"]}],events:[{name:"oversize"},{name:"clickUpload",params:["event"]},{name:"clickPreview"},{name:"clickReupload"},{name:"closePreview"},{name:"delete"},{name:"update:modelValue"}],slots:["default","preview-delete","preview-cover"],snippet:{}},O={name:"VanActionSheet",alias:"ActionSheet",label:"动作面板",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/action-sheet",props:[{name:"show",title:"是否显示动作面板",defaultValue:!1,setters:"BooleanSetter"},{name:"actions",title:"面板选项列表",defaultValue:[],setters:"ArraySetter"},{name:"title",title:"顶部标题",setters:"StringSetter"},{name:"cancel-text",title:"取消按钮文字",setters:"StringSetter"},{name:"description",title:"选项上方的描述信息",setters:"StringSetter"},{name:"closeable",title:"是否显示关闭图标",defaultValue:!0,setters:"BooleanSetter"},{name:"close-icon",title:"关闭图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"cross",setters:["VanIconSetter","StringSetter"]},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"z-index",title:"将面板的 z-index 层级设置为一个固定值",defaultValue:2e3,setters:["NumberSetter","StringSetter"]},{name:"round",title:"是否显示圆角",defaultValue:!0,setters:"BooleanSetter"},{name:"overlay",title:"是否显示遮罩层",defaultValue:!0,setters:"BooleanSetter"},{name:"overlay-class",title:"自定义遮罩层类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-style",title:"自定义遮罩层样式",setters:"ObjectSetter"},{name:"lock-scroll",title:"是否锁定背景滚动",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-render",title:"lazy-render",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-popstate",title:"是否在页面回退时自动关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-action",title:"是否在点击选项后关闭",defaultValue:!1,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!0,setters:"BooleanSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]},{name:"before-close",title:"关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise",setters:"FunctionSetter"}],events:[{name:"update:show"},{name:"select",params:["action","index"]},{name:"cancel"},{name:"open"},{name:"close"},{name:"opened"},{name:"closed"},{name:"clickOverlay",params:["event"]}],slots:["default","description","cancel","action"],snippet:{props:{show:!0,actions:[{name:"选项一"},{name:"选项二"},{name:"选项三"}]}}},P={name:"VanBarrage",alias:"Barrage",label:"弹幕",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/barrage",props:[{name:"modelValue",title:"弹幕数据",setters:"ArraySetter"},{name:"auto-play",title:"是否自动播放弹幕",defaultValue:!0,setters:"BooleanSetter"},{name:"rows",title:"弹幕文字行数",defaultValue:4,setters:["NumberSetter","StringSetter"]},{name:"top",title:"弹幕文字区域顶部间距，单位 px",defaultValue:10,setters:["NumberSetter","StringSetter"]},{name:"duration",title:"弹幕文字滑过容器的时间，单位 ms",defaultValue:4e3,setters:["NumberSetter","StringSetter"]},{name:"delay",title:"弹幕动画延时，单位 ms",defaultValue:300,setters:"NumberSetter"}],events:[{name:"update:modelValue"}],slots:["default"],snippet:{props:{modelValue:[{id:100,text:"轻量"},{id:101,text:"可定制的"},{id:102,text:"移动端"},{id:103,text:"Vue"},{id:104,text:"组件库"},{id:105,text:"VantUI"},{id:106,text:"666"}]},children:[{name:"div",props:{style:{width:"100%",height:"150px"}}}]}},E={name:"VanDialog",alias:"Dialog",label:"弹出框",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/dialog",props:[{name:"show",title:"是否显示弹窗",setters:"BooleanSetter"},{name:"title",title:"标题",setters:"StringSetter"},{name:"width",title:"弹窗宽度，默认单位为 px",defaultValue:320,setters:["NumberSetter","StringSetter"]},{name:"message",title:`文本内容，支持通过 
 换行`,setters:["StringSetter","FunctionSetter"]},{name:"message-align",title:"内容水平对齐方式，可选值为 left right justify",defaultValue:"center",setters:"SelectSetter",options:["left","right","justify","center"]},{name:"theme",title:"样式风格，可选值为 round-button",defaultValue:"default",setters:"SelectSetter",options:["round-button","default"]},{name:"show-confirm-button",title:"是否展示确认按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"show-cancel-button",title:"是否展示取消按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"confirm-button-text",title:"确认按钮文案",defaultValue:"确认",setters:"StringSetter"},{name:"confirm-button-color",title:"确认按钮颜色",defaultValue:"#ee0a24",setters:"StringSetter"},{name:"confirm-button-disabled",title:"是否禁用确认按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"cancel-button-text",title:"取消按钮文案",defaultValue:"取消",setters:"StringSetter"},{name:"cancel-button-color",title:"取消按钮颜色",defaultValue:"#000000",setters:"StringSetter"},{name:"cancel-button-disabled",title:"是否禁用取消按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"z-index",title:"将弹窗的 z-index 层级设置为一个固定值",defaultValue:"2000+",setters:["StringSetter","NumberSetter"]},{name:"overlay",title:"是否展示遮罩层",defaultValue:!0,setters:"BooleanSetter"},{name:"overlay-class",title:"自定义遮罩层类名",setters:"StringSetter"},{name:"overlay-style",title:"自定义遮罩层样式",setters:"ObjectSetter"},{name:"close-on-popstate",title:"是否在页面回退时自动关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭弹窗",defaultValue:!1,setters:"BooleanSetter"},{name:"lazy-render",title:"是否在显示弹层时才渲染节点",defaultValue:!0,setters:"BooleanSetter"},{name:"lock-scroll",title:"是否锁定背景滚动",defaultValue:!0,setters:"BooleanSetter"},{name:"allow-html",title:"是否允许 message 内容中渲染 HTML",defaultValue:!1,setters:"BooleanSetter"},{name:"before-close",title:"关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise",setters:"FunctionSetter"},{name:"transition",title:"动画类名，等价于 transition 的 name 属性",setters:"StringSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]}],events:["update:show","confirm","cancel","open","close","opened","closed"],slots:["default","title","footer"],snippet:{props:{show:!0,title:"标题"},children:[{name:"VanImage",props:{src:"https://fastly.jsdelivr.net/npm/@vant/assets/apple-3.jpeg"}}]}},F=[{name:"VanDropdownMenu",alias:"DropdownMenu",label:"下拉菜单",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/dropdown-menu",props:[{name:"active-color",title:"菜单标题和选项的选中态颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"direction",title:"菜单展开方向，可选值为up",defaultValue:"down",setters:"SelectSetter",options:["up","down"]},{name:"z-index",title:"菜单栏 z-index 层级",defaultValue:10,setters:["NumberSetter","StringSetter"]},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.2,setters:["NumberSetter","StringSetter"]},{name:"overlay",title:"是否显示遮罩层",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭菜单",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-outside",title:"是否在点击外部元素后关闭菜单",defaultValue:!0,setters:"BooleanSetter"},{name:"swipe-threshold",title:"滚动阈值，选项数量超过阈值且总宽度超过菜单栏宽度时，可以横向滚动",setters:["NumberSetter","StringSetter"]},{name:"auto-locate",title:"当祖先元素设置了 transform 时，自动调整下拉菜单的位置",defaultValue:!1,setters:"BooleanSetter"}],snippet:{children:[{name:"VanDropdownItem",props:{modelValue:0,options:[{text:"全部商品",value:0},{text:"新款商品",value:1},{text:"活动商品",value:2}]}},{name:"VanDropdownItem",props:{modelValue:"a",options:[{text:"默认排序",value:"a"},{text:"好评排序",value:"b"},{text:"销量排序",value:"c"}]}}]}},{name:"VanDropdownItem",alias:"DropdownItem",label:"下拉菜单项",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/dropdown-menu",props:[{name:"modelValue",title:"当前选中项对应的 value",setters:["NumberSetter","StringSetter"]},{name:"title",title:"菜单项标题",setters:"StringSetter"},{name:"options",title:"选项数组",defaultValue:[],setters:"ArraySetter"},{name:"disabled",title:"是否禁用菜单",defaultValue:!1,setters:"BooleanSetter"},{name:"lazy-render",title:"是否在首次展开时才渲染菜单内容",defaultValue:!0,setters:"BooleanSetter"},{name:"title-class",title:"标题额外类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]}],events:[{name:"update:modelValue"},{name:"change",params:["value"]},{name:"open"},{name:"close"},{name:"opened"},{name:"closed"}],slots:["default","title"],snippet:{props:{modelValue:"a",options:[{text:"默认排序",value:"a"},{text:"好评排序",value:"b"},{text:"销量排序",value:"c"}]}}}],D={name:"VanFloatingPanel",alias:"FloatingPanel",label:"浮动面板",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/floating-panel",props:[{name:"height",title:"当前面板的显示高度",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"anchors",title:"设置自定义锚点, 单位 px",defaultValue:[100,window.innerWidth*.6],setters:"ArraySetter"},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"content-draggable",title:"允许拖拽内容容器",defaultValue:!0,setters:"BooleanSetter"},{name:"lock-scroll",title:"当不拖拽时，是否锁定背景滚动",defaultValue:!1,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"heightChange",params:["height"]}],slots:["default","header"],snippet:{children:[{name:"VanCellGroup",children:[{name:"VanCell",props:{size:"large",title:{type:"JSExpression",value:"this.context.index"}},directives:[{name:"vFor",value:{type:"JSExpression",value:"20"}},{name:"vBind",value:{type:"JSExpression",value:"this.context.index"}}]}]}]}},L={name:"VanFloatingBubble",alias:"FloatingBubble",label:"浮动气泡",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/floating-bubble",props:[{name:"offset",title:"控制气泡位置",setters:"ObjectSetter"},{name:"axis",title:"拖拽的方向",defaultValue:"y",setters:"SelectSetter",options:["x","y","xy","lock"]},{name:"magnetic",title:"自动磁吸的方向",setters:"SelectSetter",options:["x","y"]},{name:"icon",title:"气泡图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"gap",title:"气泡与窗口的最小间距，单位为 px",defaultValue:24,setters:"NumberSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",defaultValue:"body",setters:["StringSetter","ExpressionSetter"]}],events:[{name:"update:offset"},{name:"click",params:["event"]},{name:"offsetChange",params:["data"]}],slots:["default"],snippet:{props:{icon:"chat"}}},R={name:"VanLoading",alias:"Loading",label:"加载",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/loading",props:[{name:"color",title:"颜色",defaultValue:"#c9c9c9",setters:"StringSetter"},{name:"type",title:"类型",defaultValue:"circular",setters:"SelectSetter",options:["circular","spinner"]},{name:"size",title:"加载图标大小，默认单位为 px",defaultValue:30,setters:["NumberSetter","StringSetter"]},{name:"text-size",title:"文字大小，默认单位为 px",defaultValue:14,setters:["NumberSetter","StringSetter"]},{name:"text-color",title:"文字颜色",defaultValue:"#c9c9c9",setters:"StringSetter"},{name:"vertical",title:"是否垂直排列图标和文字内容",defaultValue:!1,setters:"BooleanSetter"}],slots:["default","icon"]},q={name:"VanNotify",alias:"Notify",label:"消息提示",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/notify",props:[{name:"show",title:"是否显示通知",defaultValue:!1,setters:"BooleanSetter"},{name:"type",title:"类型",defaultValue:"danger",setters:"SelectSetter",options:["primary","success","warning","danger"]},{name:"message",title:`展示文案，支持通过
换行`,setters:"StringSetter"},{name:"z-index",title:"将组件的 z-index 层级设置为一个固定值",defaultValue:"2000+",setters:["StringSetter","NumberSetter"]},{name:"position",title:"弹出位置",defaultValue:"top",setters:"SelectSetter",options:["top","bottom"]},{name:"color",title:"字体颜色",defaultValue:"#ffffff",setters:"StringSetter"},{name:"background",title:"背景颜色",setters:"StringSetter"},{name:"class-name",title:"自定义类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"lock-scroll",title:"是否锁定背景滚动",defaultValue:!1,setters:"BooleanSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]}],events:[{name:"click",params:["event"]},{name:"close"},{name:"opened"},{name:"update:show"}],slots:["default"],snippet:{props:{show:!0,type:"success"},children:[{name:"VanIcon",props:{name:"bell",style:{marginRight:"4px"}}},{name:"span",children:"通知内容"}]}},M={name:"VanOverlay",alias:"Overlay",label:"遮罩层",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/overlay",props:[{name:"show",title:"是否展示遮罩层",defaultValue:!1,setters:"BooleanSetter"},{name:"z-index",title:"	z-index 层级",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"class-name",title:"自定义类名",setters:"StringSetter"},{name:"custom-style",title:"自定义样式",setters:"ObjectSetter"},{name:"lock-scroll",title:"是否锁定背景滚动，锁定时蒙层里的内容也将无法滚动",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-render",title:"是否在显示时才渲染节点",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"click",params:["event"]}],slots:["default"],snippet:{props:{show:!0}}},H={name:"VanPullRefresh",alias:"PullRefresh",label:"下拉刷新",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/pull-refresh",props:[{name:"modelValue",title:"是否处于加载中状态",setters:"BooleanSetter"},{name:"pulling-text",title:"下拉过程提示文案",defaultValue:"下拉即可刷新...",setters:"StringSetter"},{name:"loosing-text",title:"释放过程提示文案",defaultValue:"释放即可刷新...",setters:"StringSetter"},{name:"loading-text",title:"加载过程提示文案",defaultValue:"加载中...",setters:"StringSetter"},{name:"success-text",title:"刷新成功提示文案",setters:"StringSetter"},{name:"success-duration",title:"刷新成功提示展示时长(ms)",defaultValue:500,setters:["NumberSetter","StringSetter"]},{name:"animation-duration",title:"动画时长",defaultValue:300,setters:["NumberSetter","StringSetter"]},{name:"head-height",title:"顶部内容高度",defaultValue:50,setters:["NumberSetter","StringSetter"]},{name:"pull-distance",title:"触发下拉刷新的距离",defaultValue:50,setters:["NumberSetter","StringSetter"]},{name:"disabled",title:"	是否禁用下拉刷新",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"update:modelValue"},{name:"refresh"},{name:"change",params:["data"]}],slots:["default","normal","pulling","loosing","loading","success"],snippet:{props:{modelValue:!0},children:[{name:"p",children:"下拉刷新"}]}},G={name:"VanShareSheet",alias:"ShareSheet",label:"分享面板",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/share-sheet",props:[{name:"show",title:"是否显示分享面板",defaultValue:!1,setters:"BooleanSetter"},{name:"options",title:"分享选项",defaultValue:[],setters:"ArraySetter"},{name:"title",title:"顶部标题",setters:"StringSetter"},{name:"cancel-text",title:"取消按钮文字，传入空字符串可以隐藏按钮",defaultValue:"取消",setters:"StringSetter"},{name:"description",title:"标题下方的辅助描述文字",setters:"StringSetter"},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"z-index",title:"将面板的 z-index 层级设置为一个固定值",defaultValue:"2000+",setters:["StringSetter","NumberSetter"]},{name:"round",title:"是否显示圆角",defaultValue:!0,setters:"BooleanSetter"},{name:"overlay",title:"是否显示遮罩层",defaultValue:!0,setters:"BooleanSetter"},{name:"overlay-class",title:"自定义遮罩层类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-style",title:"自定义遮罩层样式",setters:"ObjectSetter"},{name:"lock-scroll",title:"是否锁定背景滚动",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-render",title:"是否在显示弹层时才渲染内容",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-popstate",title:"是否在页面回退时自动关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!0,setters:"BooleanSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]},{name:"before-close",title:"关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise",setters:"FunctionSetter"}],events:[{name:"update:show"},{name:"select",params:["option","index"]},{name:"cancel"},{name:"open"},{name:"close"},{name:"opened"},{name:"closed"},{name:"clickOverlay"}],slots:["title","description","cancel"],snippet:{props:{show:!0,title:"立即分享给好友",options:[[{name:"微信",icon:"wechat"},{name:"朋友圈",icon:"wechat-moments"},{name:"微博",icon:"weibo"},{name:"QQ",icon:"qq"}],[{name:"复制链接",icon:"link"},{name:"分享海报",icon:"poster"},{name:"二维码",icon:"qrcode"},{name:"小程序码",icon:"weapp-qrcode"}]]}}},J={name:"VanSwipeCell",alias:"SwipeCell",label:"滑动单元格",categoryId:"feedback",doc:"https://vant-ui.github.io/vant/#/zh-CN/swipe-cell",props:[{name:"name",title:"标识符，通常为一个唯一的字符串或数字，可以在事件参数中获取到",defaultValue:'""',setters:["StringSetter","NumberSetter"]},{name:"left-width",title:"指定左侧滑动区域宽度，单位为 px",defaultValue:"auto",setters:["StringSetter","NumberSetter"]},{name:"right-width",title:"指定右侧滑动区域宽度，单位为 px",defaultValue:"auto",setters:["StringSetter","NumberSetter"]},{name:"before-close",title:"关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise",setters:"FunctionSetter"},{name:"disabled",title:"是否禁用滑动",defaultValue:!1,setters:"BooleanSetter"},{name:"stop-propagation",title:"是否阻止滑动事件冒泡",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"click",params:["position"]},{name:"open",params:["name","position"]},{name:"close",params:["name","position"]}],slots:["default","left","right"],snippet:{children:[{name:"VanButton",slot:"left",props:{square:!0,type:"primary",text:"选择"}},{name:"VanCell",props:{border:!1,title:"单元格",value:"内容"}},{name:"template",slot:"right",children:[{name:"VanButton",props:{square:!0,type:"danger",text:"删除"}},{name:"VanButton",props:{square:!0,type:"primary",text:"收藏"}}]}]}},U={name:"VanBadge",alias:"Badge",label:"徽标",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/button",props:[{name:"content",title:"徽标内容（dot 为 fasle 时生效）",setters:["StringSetter","NumberSetter"]},{name:"color",title:"徽标背景颜色",defaultValue:"#ee0a24",setters:"StringSetter"},{name:"dot",title:"是否展示为小红点",defaultValue:!1,setters:"BooleanSetter"},{name:"max",title:"最大值，超过最大值会显示 {max}+，仅当 content 为数字时有效",setters:["NumberSetter","StringSetter"]},{name:"offset",title:"设置徽标的偏移量，数组的两项分别对应水平向右和垂直向下方向的偏移量，默认单位为 px",defaultValue:[],setters:"ArraySetter"},{name:"show-zero",title:'当 content 为数字 0 或字符串 "0" 时，是否展示徽标',defaultValue:!0,setters:"BooleanSetter"},{name:"position",title:"徽标位置",defaultValue:"top-right",setters:"SelectSetter",options:["top-left","top-right","bottom-left","bottom-right"]}],slots:["default","content"],snippet:{props:{content:"5"},children:[{name:"div",props:{style:{width:"40px",height:"40px",background:"#f2f3f5",borderRadius:"4px"}}}]}},W={name:"VanCircle",alias:"Circle",label:"环形进度条",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/circle",props:[{name:"currentRate",title:"当前进度",setters:"NumberSetter"},{name:"rate",title:"目标进度",defaultValue:100,setters:["NumberSetter","StringSetter"]},{name:"size",title:"圆环直径，默认单位为 px",defaultValue:"100px",setters:["NumberSetter","StringSetter"]},{name:"color",title:"进度条颜色，传入对象格式可以定义渐变色",defaultValue:"#1989fa",setters:["ColorSetter","StringSetter","ObjectSetter"]},{name:"layer-color",title:"轨道颜色",defaultValue:"white",setters:["StringSetter","StringSetter"]},{name:"fill",title:"填充颜色",defaultValue:"none",setters:["StringSetter","StringSetter"]},{name:"speed",title:"动画速度（单位为 rate/s）",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"text",title:"文字",setters:"StringSetter"},{name:"stroke-width",title:"进度条宽度",defaultValue:40,setters:["NumberSetter","StringSetter"]},{name:"stroke-linecap",title:"进度条端点的形状，可选值为 square butt",defaultValue:"round",setters:"SelectSetter",options:["round","square","butt"]},{name:"clockwise",title:"是否顺时针增加",defaultValue:!0,setters:"BooleanSetter"},{name:"start-position",title:"进度起始位置",defaultValue:"top",setters:"SelectSetter",options:["left","right","top","bottom"]}],events:["update:currentRate"],slots:["default"],snippet:{props:{currentRate:30,rate:100,text:"30%"}}},K=[{name:"VanCollapse",alias:"Collapse",label:"折叠面板",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/collapse",props:[{name:"modelValue",title:"当前展开面板的 name",setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"accordion",title:"是否开启手风琴模式",defaultValue:!1,setters:"BooleanSetter"},{name:"border	",title:"是否显示外边框",defaultValue:!0,setters:"BooleanSetter"}],events:["update:modelValue","change"],snippet:{props:{modelValue:["1"]},children:[{name:"VanCollapseItem",props:{title:"标题1",name:"1"},children:"代码是写出来给人看的，附带能在机器上运行。"},{name:"VanCollapseItem",props:{title:"标题2",name:"2"},children:"技术无非就是那些开发它的人的共同灵魂。。"},{name:"VanCollapseItem",props:{title:"标题3",name:"3"},children:"在代码阅读过程中人们说脏话的频率是衡量代码质量的唯一标准。"}]}},{name:"VanCollapseItem",alias:"CollapseItem",label:"折叠面板",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/collapse",props:[{name:"name",title:"唯一标识符，默认为索引值",setters:["NumberSetter","StringSetter"]},{name:"icon",title:"标题栏左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"size",title:"标题栏大小，可选值为 large",setters:"SelectSetter",options:["large"]},{name:"title",title:"标题栏左侧内容",setters:["NumberSetter","StringSetter"]},{name:"value",title:"标题栏右侧内容",setters:["NumberSetter","StringSetter"]},{name:"label",title:"标题栏描述信息",setters:["NumberSetter","StringSetter"]},{name:"border",title:"是否显示内边框",defaultValue:!0,setters:"BooleanSetter"},{name:"is-link",title:"是否展示标题栏右侧箭头并开启点击反馈",defaultValue:!0,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用面板",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法操作面板",defaultValue:!1,setters:"BooleanSetter"},{name:"lazy-render",title:"是否在首次展开时才渲染面板内容",defaultValue:!0,setters:"BooleanSetter"},{name:"title-class",title:"左侧标题额外类名",setters:"StringSetter"},{name:"value-class",title:"右侧内容额外类名",setters:"StringSetter"},{name:"label-class",title:"label-class",setters:"StringSetter"}],events:["toggle"],slots:["default","title","value","label","icon","right-icon"],snippet:{props:{title:"这是个标题"},children:"代码是写出来给人看的，附带能在机器上运行。"}}],_={name:"VanCountDown",alias:"CountDown",label:"倒计时",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/count-down",props:[{name:"time",title:"倒计时时长，单位毫秒",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"format",title:"时间格式",defaultValue:"HH:ss:mm",setters:"StringSetter"},{name:"auto-start",title:"是否自动开始倒计时",defaultValue:!0,setters:"BooleanSetter"},{name:"millisecond",title:"是否开启毫秒级渲染",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"finish"},{name:"change",params:["currentTime"]}],slots:["default"],snippet:{props:{time:1800*60*1e3}}},Q={name:"VanDivider",alias:"Divider",label:"分割线",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/divider",props:[{name:"dashed",title:"是否使用虚线",defaultValue:!1,setters:"BooleanSetter"},{name:"hairline",title:"是否使用 0.5px 线",defaultValue:!0,setters:"BooleanSetter"},{name:"content-position",title:"内容位置",defaultValue:"center",setters:"SelectSetter",options:["left","center","right"]},{name:"vertical",title:"是否使用垂直",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],snippet:{children:"文本"}},$={name:"VanEmpty",alias:"Empty",label:"空状态",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/empty",props:[{name:"image",title:"图片类型, 支持传入图片 URL",defaultValue:"default",setters:"SelectSetter",options:["error","network","search","default"]},{name:"image-size",title:"图片大小，默认单位为 px",setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"description",title:"图片下方的描述文字",setters:"StringSetter"}],slots:["default","image","description"],snippet:{props:{description:"描述文字"}}},X={name:"VanHighlight",alias:"Highlight",label:"高亮文本",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/highlight",props:[{name:"auto-escape",title:"是否自动转义",defaultValue:!0,setters:"BooleanSetter"},{name:"case-sensitive",title:"是否区分大小写",defaultValue:!1,setters:"BooleanSetter"},{name:"highlight-class",title:"高亮元素的类名",setters:"StringSetter"},{name:"highlight-tag",title:"高亮元素对应的 HTML 标签名",defaultValue:"span",setters:"StringSetter"},{name:"keywords",title:"期望高亮的文本",setters:["StringSetter","ArraySetter"]},{name:"source-string",title:"源文本",setters:"StringSetter"},{name:"tag",title:"根节点对应的 HTML 标签名",defaultValue:"div",setters:"StringSetter"},{name:"unhighlight-class",title:"非高亮元素的类名",setters:"StringSetter"},{name:"unhighlight-tag",title:"非高亮元素对应的 HTML 标签名",defaultValue:"span",setters:"StringSetter"}],snippet:{props:{"source-string":"慢慢来，不要急，生活给你出了难题，可也终有一天会给出答案。",keywords:"难题"}}},Y={name:"VanImagePreview",alias:"ImagePreview",label:"图片预览",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/image-preview",props:[{name:"show",title:"是否展示图片预览",defaultValue:!1,setters:"BooleanSetter"},{name:"images",title:"需要预览的图片 URL 数组",defaultValue:[],setters:"ArraySetter"},{name:"start-position",title:"图片预览起始位置索引",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"swipe-duration",title:"动画时长，单位为 ms",defaultValue:300,setters:["NumberSetter","StringSetter"]},{name:"show-index",title:"是否显示页码",defaultValue:!0,setters:"BooleanSetter"},{name:"show-indicators",title:"是否显示轮播指示器",defaultValue:!1,setters:"BooleanSetter"},{name:"loop",title:"是否开启循环播放",defaultValue:!0,setters:"BooleanSetter"},{name:"double-scale",title:"是否启用双击缩放手势，禁用后，点击时会立即关闭图片预览",defaultValue:!0,setters:"BooleanSetter"},{name:"before-close",title:"关闭前的回调函数，返回 false 可阻止关闭，支持返回 Promise",setters:"FunctionSetter"},{name:"close-on-popstate",title:"是否在页面回退时自动关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-image",title:"是否在点击图片后关闭图片预览",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭图片预览",defaultValue:!0,setters:"BooleanSetter"},{name:"vertical",title:"是否开启纵向手势滑动",defaultValue:!1,setters:"BooleanSetter"},{name:"class-name",title:"自定义类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"max-zoom",title:"手势缩放时，最大缩放比例",defaultValue:3,setters:["NumberSetter","StringSetter"]},{name:"min-zoom",title:"手势缩放时，最小缩放比例",defaultValue:1/3,setters:["NumberSetter","StringSetter"]},{name:"closeable",title:"是否显示关闭图标",defaultValue:!1,setters:"BooleanSetter"},{name:"close-icon",title:"关闭图标名称或图片链接",defaultValue:"clear",setters:"StringSetter"},{name:"close-icon-position",title:"关闭图标位置",defaultValue:"top-right",setters:"SelectSetter",options:["top-left","top-right","bottom-left","bottom-right"]},{name:"transition",title:"动画类名，等价于 transition 的 name 属性",defaultValue:"van-fade",setters:"StringSetter"},{name:"overlay-class",title:"自定义遮罩层类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-style",title:"自定义遮罩层样式",setters:"ObjectSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",setters:["StringSetter","ExpressionSetter"]}],events:[{name:"update:show"},{name:"close",params:["data"]},{name:"closed"},{name:"change",params:["index"]},{name:"scale",params:["data"]},{name:"longPress",params:["data"]}],slots:["index","cover","image"],snippet:{props:{show:!0,images:["https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg","https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg"]},children:[{name:"template",slot:"index"}]}},Z={name:"VanImageLazyload",alias:"Lazyload",label:"懒加载",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/lazyload",snippet:{}},ee={name:"VanList",alias:"List",label:"列表",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/list",props:[{name:"loading",title:"是否处于加载状态，加载过程中不触发 load 事件",defaultValue:!1,setters:"BooleanSetter"},{name:"error",title:"是否加载失败，加载失败后点击错误提示可以重新触发 load 事件",defaultValue:!1,setters:"BooleanSetter"},{name:"finished",title:"是否已加载完成，加载完成后不再触发 load 事件",defaultValue:!1,setters:"BooleanSetter"},{name:"offset",title:"滚动条与底部距离小于 offset 时触发 load 事件",defaultValue:300,setters:["NumberSetter","StringSetter"]},{name:"loading-text",title:"加载过程中的提示文案",defaultValue:"加载中...",setters:"StringSetter"},{name:"finished-text",title:"加载完成后的提示文案",setters:"StringSetter"},{name:"error-text",title:"加载失败后的提示文案",setters:"StringSetter"},{name:"immediate-check",title:"是否在初始化时立即执行滚动位置检查",defaultValue:!0,setters:"BooleanSetter"},{name:"disabled",title:"是否禁用滚动加载",defaultValue:!1,setters:"BooleanSetter"},{name:"direction",title:"滚动触发加载的方向，可选值为 up",defaultValue:"down",setters:"SelectSetter",options:["up","down"]},{name:"scroller",title:"指定需要监听滚动事件的节点，默认为最近的父级滚动节点",setters:"ExpressionSetter"}],events:["update:loading","update:error","load"],snippet:{props:{loading:{type:"JSExpression",value:"this.state.loading"},finished:{type:"JSExpression",value:"this.state.finished"},"finished-text":"没有更多了"},children:[{name:"VanCell",props:{key:{type:"JSExpression",value:"this.context.item"},title:{type:"JSExpression",value:"this.context.item"}},directives:[{name:"vFor",value:{type:"JSExpression",value:"20"}}]}],events:{load:{name:"load",handler:{type:"JSFunction",value:`() => {\r
    setTimeout(() => {\r
        if (this.state.refreshing) {\r
            this.state.list = [];\r
            this.state.refreshing = false;\r
        }\r
\r
        for (let i = 0; i < 10; i++) {\r
            this.state.list.push(this.state.list.length + 1);\r
        }\r
        this.loading = false;\r
\r
        if (this.state.list.length >= 40) {\r
            this.finished = true;\r
        }\r
    }, 1000);\r
};`},modifiers:{}}}}},te={name:"VanNoticeBar",alias:"NoticeBar",label:"通知栏",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/notice-bar",props:[{name:"mode",title:"通知栏模式，可选值为 closeable link",defaultValue:'""',setters:"SelectSetter",options:["closeable","link",""]},{name:"text",title:"通知文本内容",defaultValue:'""',setters:"StringSetter"},{name:"color",title:"通知文本颜色",defaultValue:"#ed6a0c",setters:"StringSetter"},{name:"background",title:"滚动条背景",defaultValue:"#fffbe8",setters:"StringSetter"},{name:"left-icon",title:"左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"delay",title:"动画延迟时间 (s)",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"speed",title:"滚动速率 (px/s)",defaultValue:60,setters:["NumberSetter","StringSetter"]},{name:"scrollable",title:"是否开启滚动播放，内容长度溢出时默认开启",setters:"BooleanSetter"},{name:"wrapable",title:"是否开启文本换行，只在禁用滚动时生效",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"click",params:["event"]},{name:"close",params:["event"]},{name:"replay"}],slots:["default","left-icon","right-icon"],snippet:{props:{"left-icon":"volume-o",text:"无论我们能活多久，我们能够享受的只有无法分割的此刻，此外别无其他。"}}},ae={name:"VanPopover",alias:"Popover",label:"气泡弹出框",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/popover",props:[{name:"show",title:"是否展示气泡弹出层",defaultValue:!1,setters:"BooleanSetter"},{name:"actions",title:"选项列表",defaultValue:[],setters:"ArraySetter"},{name:"actions-direction",title:"选项列表的排列方向，可选值为 horizontal",defaultValue:"vertical",setters:"SelectSetter",options:["vertical","horizontal"]},{name:"placement",title:"弹出位置",defaultValue:"bottom",setters:"SelectSetter",options:["top","top-start","top-end","left","left-start","left-end","right","right-start","right-end","bottom","bottom-start","bottom-end"]},{name:"theme",title:"主题风格",defaultValue:"light",setters:"SelectSetter",options:["light","dark"]},{name:"trigger",title:"触发方式",defaultValue:"click",setters:"SelectSetter",options:["click","manual"]},{name:"duration",title:"动画时长，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"offset",title:"出现位置的偏移量",defaultValue:[0,8],setters:"ArraySetter"},{name:"overlay",title:"是否显示遮罩层",defaultValue:!1,setters:"BooleanSetter"},{name:"overlay-class",title:"自定义遮罩层类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"overlay-style",title:"自定义遮罩层样式",setters:"ObjectSetter"},{name:"show-arrow",title:"是否展示小箭头",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-action",title:"	是否在点击选项后关闭",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-outside",title:"是否在点击外部元素后关闭菜单",defaultValue:!0,setters:"BooleanSetter"},{name:"close-on-click-overlay",title:"是否在点击遮罩层后关闭菜单",defaultValue:!0,setters:"BooleanSetter"},{name:"teleport",title:"	指定挂载的节点，等同于 Teleport 组件的 to 属性",defaultValue:"body",setters:["StringSetter","ExpressionSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"vant-icon",setters:"StringSetter"}],events:[{name:"update:show"},{name:"select",params:["action","index"]},{name:"open"},{name:"close"},{name:"opened"},{name:"closed"},{name:"clickOverlay",params:["event"]}],slots:["default","reference","action"],snippet:{props:{show:!0,actions:[{text:"选项一"},{text:"选项二"},{text:"选项三"}]},events:{select:{name:"select",handler:{type:"JSFunction",value:`(action) => {\r
    this.$libs.vant.showToast(action.text)\r
}`},modifiers:{}}},children:[{name:"VanButton",slot:"reference",props:{type:"primary"},children:"浅色风格"}]}},re={name:"VanProgress",alias:"Progress",label:"进度条",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/progress",props:[{name:"percentage",title:"进度百分比",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"stroke-width",title:"进度条粗细，默认单位为px",defaultValue:4,setters:["NumberSetter","StringSetter"]},{name:"color",title:"进度条颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"track-color",title:"轨道颜色",defaultValue:"#e5e5e5",setters:"StringSetter"},{name:"pivot-text",title:"进度文字内容",setters:"StringSetter"},{name:"pivot-color",title:"进度文字背景色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"text-color",title:"进度文字颜色",defaultValue:"white",setters:"StringSetter"},{name:"inactive",title:"是否置灰",defaultValue:!1,setters:"BooleanSetter"},{name:"show-pivot",title:"是否显示进度文字",defaultValue:!0,setters:"BooleanSetter"}],snippet:{props:{percentage:50}}},le={name:"VanRollingText",alias:"RollingText",label:"翻滚文本动效",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/rolling-text",props:[{name:"start-num",title:"起始数值",defaultValue:0,setters:"NumberSetter"},{name:"target-num",title:"目标数值",setters:"NumberSetter"},{name:"text-list",title:"内容数组，用于翻转非数字内容",defaultValue:[],setters:"ArraySetter"},{name:"duration",title:"动画时长，单位为秒",defaultValue:2,setters:"NumberSetter"},{name:"direction",title:"文本翻滚方向，值为 down 和 up",defaultValue:"down",setters:"SelectSetter",options:["up","down"]},{name:"auto-start",title:"是否自动开始动画",defaultValue:!0,setters:"BooleanSetter"},{name:"stop-order",title:"各个数位动画停止先后顺序，值为 ltr 和 rtl",defaultValue:"ltr",setters:"SelectSetter",options:["ltr","rtl"]},{name:"height",title:"数字高度，单位为 px",defaultValue:40,setters:"NumberSetter"}],snippet:{props:{"start-num":0,"target-num":123}}},ne=[{name:"VanSkeleton",alias:"Skeleton",label:"骨架屏",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/skeleton",props:[{name:"row",title:"段落占位图行数",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"row-width",title:"段落占位图宽度，可传数组来设置每一行的宽度",defaultValue:"100%",setters:["StringSetter","NumberSetter","ArraySetter"]},{name:"title",title:"是否显示标题占位图",defaultValue:!1,setters:"BooleanSetter"},{name:"avatar",title:"是否显示头像占位图",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",title:"是否显示骨架屏，传 false 时会展示子组件内容",defaultValue:!0,setters:"BooleanSetter"},{name:"animate",title:"是否开启动画",defaultValue:!0,setters:"BooleanSetter"},{name:"round",title:"是否将标题和段落显示为圆角风格",defaultValue:!1,setters:"BooleanSetter"},{name:"title-width",title:"标题占位图宽度",defaultValue:"40%",setters:["StringSetter","NumberSetter"]},{name:"avatar-size",title:"头像占位图大小",defaultValue:32,setters:["NumberSetter","StringSetter"]},{name:"avatar-shape",title:"头像占位图形状",defaultValue:"round",setters:"SelectSetter",options:["round","square"]}],slots:["default","template"],snippet:{props:{title:!0,avatar:!0,row:3}}},{name:"VanSkeletonParagraph",alias:"SkeletonParagraph",label:"骨架屏文本",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/skeleton",props:[{name:"round",title:"是否将段落显示为圆角风格",defaultValue:!1,setters:"BooleanSetter"},{name:"row-width",title:"段落占位图宽度",defaultValue:"100%",setters:"StringSetter"}]},{name:"VanSkeletonTitle",alias:"SkeletonTitle",label:"骨架屏标题",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/skeleton",props:[{name:"round",title:"是否将段落显示为圆角风格",defaultValue:!1,setters:"BooleanSetter"},{name:"row-width",title:"段落占位图宽度",defaultValue:"40%",setters:["StringSetter","NumberSetter"]}]},{name:"VanSkeletonAvatar",alias:"SkeletonAvatar",label:"骨架屏头像",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/skeleton",props:[{name:"avatar-size",title:"头像占位图大小",defaultValue:"32px",setters:["StringSetter","NumberSetter"]},{name:"avatar-shape",title:"头像占位图形状",defaultValue:"round",setters:"SelectSetter",options:["round","square"]}]},{name:"VanSkeletonImage",alias:"SkeletonImage",label:"骨架屏图片",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/skeleton",props:[{name:"image-size",title:"图片占位图大小",defaultValue:"32px",setters:["StringSetter","NumberSetter"]},{name:"image-shape",title:"图片占位图形状",defaultValue:"round",setters:"SelectSetter",options:["round","square"]}]}],se=[{name:"VanSteps",alias:"Steps",label:"步骤条",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/steps",props:[{name:"active",title:"当前步骤对应的索引值",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"direction",title:"步骤条方向",defaultValue:"horizontal",setters:"SelectSetter",options:["horizontal","vertical"]},{name:"active-icon",title:"当前步骤对应的底部图标",defaultValue:"checked",setters:["VanIconSetter","StringSetter"]},{name:"inactive-icon",title:"非当前步骤对应的底部图标",setters:["VanIconSetter","StringSetter"]},{name:"finish-icon",title:"已完成步骤对应的底部图标，优先级高于 inactive-icon",setters:"StringSetter"},{name:"active-color",title:"当前步骤和已完成步骤的颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"inactive-color",title:"未激活步骤的颜色",defaultValue:"#969799",setters:"StringSetter"},{name:"icon-prefix",title:"图标类名前缀",defaultValue:"van-icon",setters:"StringSetter"}],events:[{name:"clickStep",params:["index"]}],snippet:{props:{active:1},children:[{name:"VanStep",children:"买家下单"},{name:"VanStep",children:"商家接单"},{name:"VanStep",children:"买家提货"},{name:"VanStep",children:"交易完成"}]}},{name:"VanStep",alias:"Step",label:"步骤条项",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/steps",slots:["default","active-icon","inactive-icon","finish-icon"],snippet:{children:"步骤条项"}}],ie={name:"VanSticky",alias:"Sticky",label:"粘性布局",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/sticky",props:[{name:"position",title:"吸附位置",defaultValue:"top",setters:"SelectSetter",options:["top","bottom"]},{name:"offset-top",title:"吸顶时与顶部的距离，支持 px vw vh rem 单位，默认 px",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"offset-bottom",title:"吸底时与底部的距离，支持 px vw vh rem 单位，默认 px",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"z-index",title:"吸顶时的 z-index",defaultValue:99,setters:["NumberSetter","StringSetter"]},{name:"container",title:"容器对应的 HTML 节点",setters:"ExpressionSetter"}],events:[{name:"change",params:["isFixed"]},{name:"scroll",params:["data"]}],snippet:{children:[{name:"VanButton",props:{type:"primary"},children:"基础用法"}]}},oe=[{name:"VanSwipe",alias:"Swipe",label:"轮播",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/swipe",props:[{name:"autoplay",title:"自动轮播间隔，单位为 ms",setters:["NumberSetter","StringSetter"]},{name:"duration",title:"动画时长，单位为 ms",defaultValue:500,setters:["NumberSetter","StringSetter"]},{name:"initial-swipe",title:"初始位置索引值",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"width",title:"滑块宽度，单位为 px",defaultValue:"auto",setters:["StringSetter","NumberSetter"]},{name:"height",title:"滑块高度，单位为 px",defaultValue:"auto",setters:["StringSetter","NumberSetter"]},{name:"loop",title:"是否开启循环播放",defaultValue:!0,setters:"BooleanSetter"},{name:"show-indicators",title:"是否显示指示器",defaultValue:!0,setters:"BooleanSetter"},{name:"vertical",title:"是否为纵向滚动",defaultValue:!1,setters:"BooleanSetter"},{name:"touchable",title:"是否可以通过手势滑动",defaultValue:!0,setters:"BooleanSetter"},{name:"stop-propagation",title:"是否阻止滑动事件冒泡",defaultValue:!0,setters:"BooleanSetter"},{name:"lazy-render",title:"是否延迟渲染未展示的轮播",defaultValue:!1,setters:"BooleanSetter"},{name:"indicator-color",title:"指示器颜色",defaultValue:"#1989fa",setters:"StringSetter"}],events:[{name:"change",params:["index"]},{name:"dragStart",params:["data"]},{name:"dragEnd",params:["data"]}],snippet:{props:{autoplay:"3000","indicator-color":"white",style:{color:"#fff",fontSize:"20px",lineHeight:"150px",textAlign:"center",backgroundColor:"#39a9ed"}},children:[{name:"VanSwipeItem",children:"1"},{name:"VanSwipeItem",children:"2"},{name:"VanSwipeItem",children:"3"}]}},{name:"VanSwipeItem",alias:"SwipeItem",label:"轮播项",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/swipe",snippet:{props:{style:{color:"#fff",fontSize:"20px",lineHeight:"150px",textAlign:"center",backgroundColor:"#39a9ed"}},children:"1"}}],ue={name:"VanTag",alias:"Tag",label:"标签",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/tag",props:[{name:"type",title:"类型",defaultValue:"default",setters:"SelectSetter",options:["primary","success","danger","warning","default"]},{name:"size",title:"大小",setters:"SelectSetter",options:["large","medium"]},{name:"color",title:"标签颜色",setters:"StringSetter"},{name:"show",title:"是否展示标签",defaultValue:!0,setters:"BooleanSetter"},{name:"plain",title:"是否为空心样式",defaultValue:!1,setters:"BooleanSetter"},{name:"round",title:"是否为圆角样式",defaultValue:!1,setters:"BooleanSetter"},{name:"mark",title:"是否为标记样式",defaultValue:!1,setters:"BooleanSetter"},{name:"text-color",title:"文本颜色，优先级高于 color 属性",defaultValue:"white",setters:"StringSetter"},{name:"closeable",title:"是否为可关闭标签",defaultValue:!1,setters:"BooleanSetter"}],slots:["default"],events:[{name:"click",params:["event"]},{name:"close",params:["event"]}],snippet:{props:{type:"primary"},children:"标签"}},me={name:"VanTextEllipsis",alias:"TextEllipsis",label:"文本省略",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/text-ellipsis",props:[{name:"rows",title:"展示的行数",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"content",title:"需要展示的文本",setters:"StringSetter"},{name:"expand-text",title:"展开操作的文案",setters:"StringSetter"},{name:"collapse-text",title:"收起操作的文案",setters:"StringSetter"},{name:"dots",title:"省略号的文本内容",defaultValue:"...",setters:"StringSetter"},{name:"position",title:"省略位置",defaultValue:"end",setters:"SelectSetter",options:["start","middle","end"]}],events:[{name:"lickAction",params:["event"]}],slots:["action"],snippet:{props:{content:"慢慢来，不要急，生活给你出了难题，可也终有一天会给出答案。"}}},de={name:"VanWatermark",alias:"Watermark",label:"水印",categoryId:"view",doc:"https://vant-ui.github.io/vant/#/zh-CN/watermark",props:[{name:"width",title:"水印宽度",defaultValue:100,setters:"NumberSetter"},{name:"height",title:"水印高度",defaultValue:100,setters:"NumberSetter"},{name:"z-index",title:"水印的 z-index",defaultValue:100,setters:["NumberSetter","StringSetter"]},{name:"content",title:"文字水印的内容",setters:"StringSetter"},{name:"image",title:"图片水印的内容，如果与 content 同时传入，优先使用图片水印",setters:"StringSetter"},{name:"rotate",title:"水印的旋转角度",defaultValue:-22,setters:["NumberSetter","StringSetter"]},{name:"full-page",title:"水印是否全屏显示",defaultValue:!1,setters:"BooleanSetter"},{name:"gap-x",title:"水印之间的水平间隔",defaultValue:0,setters:"NumberSetter"},{name:"gap-y",title:"水印之间的垂直间隔",defaultValue:0,setters:"NumberSetter"},{name:"text-color",title:"文字水印的颜色",defaultValue:"#dcdee0",setters:"StringSetter"},{name:"opacity",title:"水印的透明度",setters:["NumberSetter","StringSetter"]}],slots:["content"],snippet:{props:{content:"VTJ"}}},Se=[{name:"VanActionBar",alias:"ActionBar",label:"动作栏",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/action-bar",props:[{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!0,setters:"BooleanSetter"},{name:"placeholder",title:"是否在标签位置生成一个等高的占位元素",defaultValue:!1,setters:"BooleanSetter"}],snippet:{children:[{name:"VanActionBarIcon",props:{icon:"chat-o",text:"客服"}},{name:"VanActionBarIcon",props:{icon:"cart-o",text:"购物车"}},{name:"VanActionBarIcon",props:{icon:"shop-o",text:"店铺"}},{name:"VanActionBarButton",props:{type:"danger",text:"立即购买"}}]}},{name:"VanActionBarIcon",alias:"ActionBarIcon",label:"动作栏图标",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/action-bar",props:[{name:"text",title:"按钮文字",setters:"StringSetter"},{name:"icon",title:"图标",setters:["VanIconSetter","StringSetter"]},{name:"color",title:"图标颜色",defaultValue:"#323233",setters:"StringSetter"},{name:"icon-class",title:"图标额外类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"dot",title:"是否显示图标右上角小红点",defaultValue:!1,setters:"BooleanSetter"},{name:"badge",title:"图标右上角徽标的内容",setters:["NumberSetter","StringSetter"]},{name:"badge-props",title:"自定义徽标的属性，传入的对象会被透传给 Badge 组件的 props",setters:"ObjectSetter"},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"}],slots:["default","icon"],snippet:{props:{icon:"chat-o",text:"客服"}}},{name:"VanActionBarButton",alias:"ActionBarButton",label:"动作栏按钮",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/action-bar",props:[{name:"text",title:"按钮文字",setters:"StringSetter"},{name:"type",title:"按钮类型",defaultValue:"default",setters:"SelectSetter",options:["default","primary","success","warning","danger"]},{name:"color",title:"按钮颜色，支持传入 linear-gradient 渐变色",setters:"StringSetter"},{name:"icon",title:"左侧图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"disabled",title:"是否禁用按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",title:"是否显示为加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"}],snippet:{props:{type:"danger",text:"立即购买"}}}],ce={name:"VanBackTop",alias:"BackTop",label:"回到顶部",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/back-top",props:[{name:"target",title:"触发滚动的目标对象，支持传入选择器或 DOM 元素，默认最近的父级滚动容器",setters:["StringSetter","ExpressionSetter"]},{name:"right",title:"距离页面右侧的距离，默认单位为 px",defaultValue:30,setters:["NumberSetter","StringSetter"]},{name:"bottom",title:"距离页面底部的距离，默认单位为 px",defaultValue:40,setters:["NumberSetter","StringSetter"]},{name:"offset",title:"滚动高度达到此参数值时才显示组件",defaultValue:200,setters:"NumberSetter"},{name:"teleport",title:"指定挂载的节点，等同于 Teleport 组件的 to 属性",defaultValue:"body",setters:["StringSetter","ExpressionSetter"]},{name:"immediate",title:"是否瞬间滚动到顶部",defaultValue:!1,setters:"BooleanSetter"},{name:"z-index",title:"设置组件的 z-index 层级",defaultValue:100,setters:["NumberSetter","StringSetter"]}],events:[{name:"click",params:["event"]}],slots:["default"]},pe=[{name:"VanGrid",alias:"Grid",label:"宫格",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/grid",props:[{name:"column-num",title:"列数",defaultValue:4,setters:["NumberSetter","StringSetter"]},{name:"icon-size",title:"图标大小，默认单位为px",defaultValue:28,setters:["NumberSetter","StringSetter"]},{name:"gutter",title:"格子之间的间距，默认单位为px",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"border",title:"是否显示边框",defaultValue:!0,setters:"BooleanSetter"},{name:"center",title:"是否将格子内容居中显示",defaultValue:!0,setters:"BooleanSetter"},{name:"square",title:"是否将格子固定为正方形",defaultValue:!1,setters:"BooleanSetter"},{name:"clickable",title:"是否开启格子点击反馈",defaultValue:!1,setters:"BooleanSetter"},{name:"direction",title:"格子内容排列的方向",defaultValue:"vertical",setters:"SelectSetter",options:["vertical","horizontal"]},{name:"reverse",title:"是否调换图标和文本的位置",defaultValue:!1,setters:"BooleanSetter"}],snippet:{children:[{name:"VanGridItem",props:{icon:"photo-o",text:"文字"},directives:[{name:"vFor",value:{type:"JSExpression",value:"8"}}]}]}},{name:"VanGridItem",alias:"GridItem",label:"宫格项",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/grid",props:[{name:"text",title:"文字",setters:"StringSetter"},{name:"icon",title:"图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"icon-color",title:"图标颜色，等同于 Icon 组件的 color 属性",setters:"StringSetter"},{name:"dot",title:"是否显示图标右上角小红点",defaultValue:!1,setters:"BooleanSetter"},{name:"badge",title:"图标右上角徽标的内容",setters:["NumberSetter","StringSetter"]},{name:"badge-props",title:"自定义徽标的属性，传入的对象会被透传给 Badge 组件的 props",setters:"ObjectSetter"},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"click",params:["event"]}],slots:["default","icon","text"],snippet:{props:{icon:"photo-o",text:"文字"}}}],fe=[{name:"VanIndexBar",alias:"IndexBar",label:"索引栏",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/index-bar",props:[{name:"index-list",title:"索引字符列表",setters:"ArraySetter"},{name:"z-index",title:"z-index 层级",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"sticky",title:"是否开启锚点自动吸顶",defaultValue:!0,setters:"BooleanSetter"},{name:"sticky-offset-top",title:"锚点自动吸顶时与顶部的距离",defaultValue:0,setters:"NumberSetter"},{name:"highlight-color",title:"索引字符高亮颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"teleport",title:"指定索引栏挂载的节点",setters:["StringSetter","ExpressionSetter"]}],events:[{name:"select",params:["index"]},{name:"change",params:["index"]}],snippet:{children:[{name:"VanIndexAnchor",props:{index:"A"}},{name:"VanCell",props:{title:"文本"}},{name:"VanCell",props:{title:"文本"}},{name:"VanCell",props:{title:"文本"}},{name:"VanIndexAnchor",props:{index:"B"}},{name:"VanCell",props:{title:"文本"}},{name:"VanCell",props:{title:"文本"}},{name:"VanCell",props:{title:"文本"}}]}},{name:"VanIndexAnchor",alias:"IndexAnchor",label:"索引栏锚点",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/index-bar",props:[{name:"index",title:"索引字符",setters:["StringSetter","NumberSetter"]}],slots:["default"],snippet:{props:{index:"C"}}}],ge={name:"VanNavBar",alias:"NavBar",label:"导航栏",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/nav-bar",props:[{name:"title",title:"标题",defaultValue:"",setters:"StringSetter"},{name:"left-text",title:"左侧文案",defaultValue:"",setters:"StringSetter"},{name:"right-text",title:"右侧文案",defaultValue:"",setters:"StringSetter"},{name:"left-disabled",title:"是否禁用左侧按钮，禁用时透明度降低，且无法点击",defaultValue:!1,setters:"BooleanSetter"},{name:"right-disabled",title:"是否禁用右侧按钮，禁用时透明度降低，且无法点击",defaultValue:!1,setters:"BooleanSetter"},{name:"left-arrow",title:"是否显示左侧箭头",defaultValue:!1,setters:"BooleanSetter"},{name:"border",title:"是否显示下边框",defaultValue:!0,setters:"BooleanSetter"},{name:"fixed",title:"是否固定在顶部",defaultValue:!1,setters:"BooleanSetter"},{name:"placeholder",title:"固定在顶部时，是否在标签位置生成一个等高的占位元素",defaultValue:!1,setters:"BooleanSetter"},{name:"z-index",title:"导航栏 z-index",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"safe-area-inset-top",title:"是否开启顶部安全区适配",defaultValue:!1,setters:"BooleanSetter"},{name:"clickable",title:"是否开启两侧按钮的点击反馈",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"clickLeft",params:["event"]},{name:"clickRight",params:["event"]}],slots:["title","left","right"],snippet:{props:{title:" 标题"}}},Ve={name:"VanPagination",alias:"Pagination",label:"分页",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/pagination",props:[{name:"modelValue",title:"当前页码",setters:"NumberSetter"},{name:"mode",title:"显示模式",defaultValue:"multi",setters:"SelectSetter",options:["simple","multi"]},{name:"prev-text",title:"上一页按钮文字",defaultValue:"上一页",setters:"StringSetter"},{name:"next-text",title:"下一页按钮文字",defaultValue:"下一页",setters:"StringSetter"},{name:"page-count",title:"总页数",setters:["NumberSetter","StringSetter"]},{name:"total-items",title:"总记录数",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"items-per-page",title:"每页记录数",defaultValue:10,setters:["NumberSetter","StringSetter"]},{name:"show-page-size",title:"显示的页码个数",defaultValue:5,setters:["NumberSetter","StringSetter"]},{name:"force-ellipses",title:"是否显示省略号",defaultValue:!1,setters:"BooleanSetter"},{name:"show-prev-button",title:"是否展示上一页按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"show-next-button",title:"是否展示下一页按钮",defaultValue:!0,setters:"BooleanSetter"}],events:["update:modelValue","change"],slots:["page","prev-text","next-text"],snippet:{props:{modelValue:1,"total-items":24,"items-per-page":5}}},he=[{name:"VanSidebar",alias:"Sidebar",label:"侧边导航",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/sidebar",props:[{name:"modelValue",title:"当前导航项的索引",defaultValue:0,setters:["NumberSetter","StringSetter"]}],events:["update:modelValue","change"],snippet:{props:{modelValue:0},children:[{name:"VanSidebarItem",props:{title:"标签名称"}},{name:"VanSidebarItem",props:{title:"标签名称"}},{name:"VanSidebarItem",props:{title:"标签名称"}}]}},{name:"VanSidebarItem",alias:"SidebarItem",label:"侧边导航项",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/sidebar",props:[{name:"title",title:"内容",defaultValue:'""',setters:"StringSetter"},{name:"dot",title:"是否显示右上角小红点",defaultValue:!1,setters:"BooleanSetter"},{name:"badge",title:"图标右上角徽标的内容",setters:["NumberSetter","StringSetter"]},{name:"badge-props",title:"自定义徽标的属性，传入的对象会被透传给 Badge 组件的 props",setters:"ObjectSetter"},{name:"disabled",title:"是否禁用该项",defaultValue:!1,setters:"BooleanSetter"},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"}],events:["click"],slots:["title"],snippet:{props:{title:"标签名称"}}}],be=[{name:"VanTabs",alias:"Tabs",label:"标签页",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/tap",props:[{name:"active",title:"绑定当前选中标签的标识符",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"type",title:"样式风格类型，可选值为 card",defaultValue:"line",setters:"SelectSetter",options:["line","card"]},{name:"color",title:"标签主题色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"background",title:"标签栏背景色",defaultValue:"white",setters:"StringSetter"},{name:"duration",title:"动画时间，单位秒，设置为 0 可以禁用动画",defaultValue:.3,setters:["NumberSetter","StringSetter"]},{name:"line-width",title:"底部条宽度，默认单位 px",defaultValue:40,setters:["NumberSetter","StringSetter"]},{name:"line-height",title:"底部条高度，默认单位 px",defaultValue:3,setters:["NumberSetter","StringSetter"]},{name:"animated",title:"是否开启切换标签内容时的转场动画（开启该属性后，内容区如果有粘性布局将会不达预期）",defaultValue:!1,setters:"BooleanSetter"},{name:"border",title:'是否显示标签栏外边框，仅在 type="line" 时有效',defaultValue:!1,setters:"BooleanSetter"},{name:"ellipsis",title:"是否省略过长的标题文字（仅在 shrink 为 false 且 tab 数量小于等于 swipe-threshold 时生效）",defaultValue:!0,setters:"BooleanSetter"},{name:"sticky",title:"是否使用粘性布局",defaultValue:!1,setters:"BooleanSetter"},{name:"shrink",title:"是否开启左侧收缩布局",defaultValue:!1,setters:"BooleanSetter"},{name:"swipeable",title:"是否开启手势左右滑动切换（开启该属性后，内容区如果有粘性布局将会不达预期）",defaultValue:!1,setters:"BooleanSetter"},{name:"lazy-render",title:"是否开启延迟渲染（首次切换到标签时才触发内容渲染）",defaultValue:!0,setters:"BooleanSetter"},{name:"scrollspy",title:"是否开启滚动导航",defaultValue:!1,setters:"BooleanSetter"},{name:"show-header",title:"是否显示标题栏",defaultValue:!0,setters:"BooleanSetter"},{name:"offset-top",title:"粘性布局下吸顶时与顶部的距离，支持 px vw vh rem 单位，默认 px",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"swipe-threshold",title:"滚动阈值，标签数量超过阈值且总宽度超过标签栏宽度时开始横向滚动（仅在 shrink 为 false 且 ellipsis 为 true 时生效）",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"title-active-color",title:"标题选中态颜色",setters:"StringSetter"},{name:"title-inactive-color",title:"标题默认态颜色",setters:"StringSetter"},{name:"before-change",title:"切换标签前的回调函数，返回 false 可阻止切换，支持返回 Promise",setters:"FunctionSetter"}],events:[{name:"update:active"},{name:"clickTab",params:["data"]},{name:"change",params:["name","title"]},{name:"rendered",params:["name","title"]},{name:"scroll",params:["data"]}],slots:["default","nav-left","nav-right","nav-bottom"],snippet:{props:{active:0},children:[{name:"VanTab",props:{title:"内容1"},children:"标题1"},{name:"VanTab",props:{title:"内容2"},children:"标题2"},{name:"VanTab",props:{title:"内容3"},children:"标题3"}]}},{name:"VanTab",alias:"Tab",label:"标签页项",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/tap",props:[{name:"title",title:"标题",setters:"StringSetter"},{name:"disabled",title:"是否禁用标签",defaultValue:!1,setters:"BooleanSetter"},{name:"dot",title:"是否在标题右上角显示小红点",defaultValue:!1,setters:"BooleanSetter"},{name:"badge",title:"图标右上角徽标的内容（dot 为 fasle 时生效）",setters:["NumberSetter","StringSetter"]},{name:"name",title:"标签名称，作为匹配的标识符",setters:["NumberSetter","StringSetter"]},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"},{name:"title-style",title:"自定义标题样式",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"title-class",title:"自定义标题类名",setters:["StringSetter","ArraySetter","ObjectSetter"]},{name:"show-zero-badge",title:"当 badge 为数字 0 时，是否展示徽标",defaultValue:!0,setters:"BooleanSetter"}],slots:["default","title"],snippet:{props:{title:"内容-标题项"},children:"内容-标题项"}}],ve=[{name:"VanTabbar",alias:"Tabbar",label:"标签栏",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/tabbar",props:[{name:"modelValue",title:"当前选中标签的名称或索引值",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"fixed",title:"是否固定在底部",defaultValue:!0,setters:"BooleanSetter"},{name:"border",title:"是否显示外边框",defaultValue:!0,setters:"BooleanSetter"},{name:"z-index",title:"元素 z-index",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"active-color",title:"选中标签的颜色",defaultValue:"#1989fa",setters:"StringSetter"},{name:"inactive-color",title:"未选中标签的颜色",defaultValue:"#7d7e80",setters:"StringSetter"},{name:"route",title:"是否开启路由模式",defaultValue:!1,setters:"BooleanSetter"},{name:"placeholder",title:"固定在底部时，是否在标签位置生成一个等高的占位元素",defaultValue:!1,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配，设置 fixed 时默认开启",defaultValue:!1,setters:"BooleanSetter"},{name:"before-change",title:"切换标签前的回调函数，返回 false 可阻止切换，支持返回 Promise",setters:"FunctionSetter"}],events:["update:modelValue","change"],snippet:{props:{modelValue:0},children:[{name:"VanTabbarItem",props:{icon:"home-o"},children:"home"},{name:"VanTabbarItem",props:{icon:"search"},children:"search"},{name:"VanTabbarItem",props:{icon:"friends-o"},children:"friends"},{name:"VanTabbarItem",props:{icon:"setting-o"},children:"setting"}]}},{name:"VanTabbarItem",alias:"TabbarItem",label:"标签栏项",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/tabbar",props:[{name:"name",title:"标签名称，作为匹配的标识符",setters:["NumberSetter","StringSetter"]},{name:"icon",title:"图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"icon-prefix",title:"图标类名前缀，等同于 Icon 组件的 class-prefix 属性",defaultValue:"van-icon",setters:"StringSetter"},{name:"dot",title:"是否显示图标右上角小红点",defaultValue:!1,setters:"BooleanSetter"},{name:"badge",title:"图标右上角徽标的内容",setters:["NumberSetter","StringSetter"]},{name:"badge-props",title:"自定义徽标的属性，传入的对象会被透传给 Badge 组件的 props",setters:"ObjectSetter"},{name:"url",title:"点击后跳转的链接地址",setters:"StringSetter"},{name:"to",title:"点击后跳转的目标路由对象，等同于 Vue Router 的 to 属性",setters:["StringSetter","ObjectSetter"]},{name:"replace",title:"是否在跳转时替换当前页面历史",defaultValue:!1,setters:"BooleanSetter"}],slots:["default","icon"],snippet:{props:{icon:"home-o"},children:"home"}}],xe={name:"VanTreeSelect",alias:"TreeSelect",label:"分类选择",categoryId:"nav",doc:"https://vant-ui.github.io/vant/#/zh-CN/tree-select",props:[{name:"main-active-index",title:"左侧选中项的索引",defaultValue:0,setters:["NumberSetter","StringSetter"]},{name:"active-id",title:"右侧选中项的 id，支持传入数组",defaultValue:0,setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"items",title:"分类显示所需的数据",defaultValue:[],setters:"ArraySetter"},{name:"height",title:"高度，默认单位为px",defaultValue:300,setters:["NumberSetter","StringSetter"]},{name:"max",title:"右侧项最大选中个数",defaultValue:1/0,setters:["NumberSetter","StringSetter"]},{name:"selected-icon",title:"自定义右侧栏选中状态的图标",defaultValue:"success",setters:"StringSetter"}],events:[{name:"update:mainActiveIndex"},{name:"update:activeId"},{name:"clickNav",params:["index"]},{name:"clickItem",params:["item"]}],slots:["nav-text","content"],snippet:{props:{"active-id":1,items:[{text:"浙江",children:[{text:"杭州",id:1},{text:"温州",id:2},{text:"宁波",id:3,disabled:!0}]},{text:"江苏",children:[{text:"南京",id:4},{text:"无锡",id:5},{text:"徐州",id:6}]},{text:"福建",disabled:!0}]}}},ye={name:"VanAddressEdit",alias:"AddressEdit",label:"地址编辑",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/address-edit",props:[{name:"area-list",title:"地区列表",setters:"ObjectSetter"},{name:"area-columns-placeholder",title:"地区选择列占位提示文字",defaultValue:[],setters:"ArraySetter"},{name:"area-placeholder",title:"地区输入框占位提示文字",defaultValue:"选择省 / 市 / 区",setters:"StringSetter"},{name:"address-info",title:"地址信息初始值",defaultValue:{},setters:"ObjectSetter"},{name:"search-result",title:"详细地址搜索结果",defaultValue:[],setters:"ArraySetter"},{name:"show-delete",title:"是否显示删除按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"show-set-default",title:"是否显示默认地址栏",defaultValue:!1,setters:"BooleanSetter"},{name:"show-search-result",title:"是否显示搜索结果",defaultValue:!1,setters:"BooleanSetter"},{name:"show-area",title:"是否显示地区",defaultValue:!0,setters:"BooleanSetter"},{name:"show-detail",title:"是否显示详细地址",defaultValue:!0,setters:"BooleanSetter"},{name:"disable-area",title:"是否禁用地区选择",defaultValue:!1,setters:"BooleanSetter"},{name:"save-button-text",title:"保存按钮文字",defaultValue:"保存",setters:"StringSetter"},{name:"delete-button-text",title:"删除按钮文字",defaultValue:"删除",setters:"StringSetter"},{name:"detail-rows",title:"详细地址输入框行数",defaultValue:1,setters:["NumberSetter","StringSetter"]},{name:"detail-maxlength",title:"详细地址最大长度",defaultValue:200,setters:["NumberSetter","StringSetter"]},{name:"is-saving",title:"是否显示保存按钮加载动画",defaultValue:!1,setters:"BooleanSetter"},{name:"is-deleting",title:"是否显示删除按钮加载动画",defaultValue:!1,setters:"BooleanSetter"},{name:"tel-validator",title:"手机号格式校验函数",setters:"FunctionSetter"},{name:"tel-maxlength",title:"手机号最大长度",setters:["NumberSetter","StringSetter"]},{name:"validator",title:"自定义校验函数",setters:"FunctionSetter"}],events:[{name:"save",params:["info"]},{name:"focus",params:["key"]},{name:"change",params:["data"]},{name:"delete",params:["info"]},{name:"selectSearch",params:["value"]},{name:"clickArea"},{name:"changeArea",params:["selectedOptions"]},{name:"changeDetail",params:["value"]},{name:"changeDefault",params:["checked"]}],slots:["default"],snippet:{props:{"show-delete":!0,"show-set-default":!0,"show-search-result":!0,"area-list":[],"area-columns-placeholder":["请选择","请选择","请选择"],"search-result":[{name:"黄龙万科中心",address:"杭州市西湖区"}]}}},Be={name:"VanAddressList",alias:"AddressList",label:"地址列表",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/address-list",props:[{name:"modelValue",title:"当前选中地址的 id，支持多选（类型为 []）",setters:["NumberSetter","StringSetter","ArraySetter"]},{name:"list",title:"地址列表",defaultValue:[],setters:"ArraySetter"},{name:"disabled-list",title:"不可配送地址列表",defaultValue:[],setters:"ArraySetter"},{name:"disabled-text",title:"不可配送提示文案",setters:"StringSetter"},{name:"switchable",title:"是否允许切换地址",defaultValue:!0,setters:"BooleanSetter"},{name:"show-add-button",title:"是否显示底部按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"add-button-text",title:"底部按钮文字",defaultValue:"	新增地址",setters:"StringSetter"},{name:"default-tag-text",title:"默认地址标签文字",setters:"StringSetter"},{name:"right-icon",title:"右侧图标名称或图片链接，等同于 Icon 组件的 name 属性",defaultValue:"edit",setters:["VanIconSetter","StringSetter"]}],events:[{name:"update:modelValue"},{name:"add",params:["item","index"]},{name:"edit",params:["item","index"]},{name:"select",params:["item","index"]},{name:"editDisabled",params:["item","index"]},{name:"selectDisabled",params:["item","index"]},{name:"clickItem",params:["item","index","data"]}],slots:["default","top","item-bottom","tag"],snippet:{props:{"disabled-text":"以下地址超出配送范围","default-tag-text":"默认",modelValue:1,list:[{id:"1",name:"张三",tel:"***********",address:"浙江省杭州市西湖区文三路 138 号东方通信大厦 7 楼 501 室",isDefault:!0},{id:"2",name:"李四",tel:"1310000000",address:"浙江省杭州市拱墅区莫干山路 50 号"}],disabledList:[{id:"3",name:"王五",tel:"1320000000",address:"浙江省杭州市滨江区江南大道 15 号"}]}}},Ne={name:"VanArea",alias:"Area",label:"省市区选择",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/area",props:[{name:"modelValue",title:"当前选中项对应的地区码",setters:"StringSetter"},{name:"title",title:"顶部栏标题",setters:"StringSetter"},{name:"confirm-button-text",title:"确认按钮文字",defaultValue:"确认",setters:"StringSetter"},{name:"cancel-button-text",title:"取消按钮文字",defaultValue:"取消",setters:"StringSetter"},{name:"area-list",title:"省市区数据",setters:"ObjectSetter"},{name:"columns-placeholder",title:"列占位提示文字",defaultValue:[],setters:"ArraySetter"},{name:"loading",title:"是否显示加载状态",defaultValue:!1,setters:"BooleanSetter"},{name:"readonly",title:"是否为只读状态，只读状态下无法切换选项",defaultValue:!1,setters:"BooleanSetter"},{name:"option-height",title:"选项高度，支持 px vw vh rem 单位，默认 px",defaultValue:44,setters:["NumberSetter","StringSetter"]},{name:"columns-num",title:"显示列数，3-省市区，2-省市，1-省",defaultValue:3,setters:"SelectSetter",options:[{label:"1",value:1},{label:"2",value:2},{label:"3",value:3}]},{name:"visible-option-num",title:"可见的选项个数",defaultValue:6,setters:["NumberSetter","StringSetter"]},{name:"swipe-duration",title:"快速滑动时惯性滚动的时长，单位 ms",defaultValue:1e3,setters:["NumberSetter","StringSetter"]}],events:[{name:"update:modelValue"},{name:"confirm",params:["data"]},{name:"cancel",params:["data"]},{name:"change",params:["data"]}],slots:["toolbar","title","confirm","cancel","columns-top","columns-bottom"],snippet:{props:{title:"标题","area-list":{province_list:{11e4:"北京市",12e4:"天津市"},city_list:{110100:"北京市",120100:"天津市"},county_list:{110101:"东城区",110102:"西城区"}}}}},we={name:"VanCard",alias:"Card",label:"卡片",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/card",props:[{name:"thumb",title:"左侧图片 URL",setters:"StringSetter"},{name:"title",title:"标题",setters:"StringSetter"},{name:"desc",title:"描述",setters:"StringSetter"},{name:"tag",title:"图片角标",setters:"StringSetter"},{name:"num",title:"商品数量",setters:["NumberSetter","StringSetter"]},{name:"price",title:"商品价格",setters:["NumberSetter","StringSetter"]},{name:"origin-price",title:"商品划线原价",setters:["NumberSetter","StringSetter"]},{name:"centered",title:"内容是否垂直居中",defaultValue:!1,setters:"BooleanSetter"},{name:"currency",title:"货币符号",defaultValue:"￥",setters:"StringSetter"},{name:"thumb-link",title:"点击左侧图片后跳转的链接地址",setters:"StringSetter"},{name:"lazy-load",title:"是否开启图片懒加载，须配合 Lazyload 组件使用",defaultValue:!1,setters:"BooleanSetter"}],events:[{name:"click",params:["event"]},{name:"clickThumb",params:["event"]}],slots:["title","desc","num","price","origin-price","price-top","bottom","thumb","tag","tags","footer"],snippet:{props:{num:2,price:"2.00",desc:"描述信息",title:"商品标题",thumb:"https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg"}}},Ie={name:"VanContactCard",alias:"ContactCard",label:"联系人卡片",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/contact-card",props:[{name:"type",title:"卡片类型",defaultValue:"add",setters:"SelectSetter",options:["add","edit"]},{name:"name",title:"联系人姓名",setters:"StringSetter"},{name:"tel",title:"联系人手机号",setters:"StringSetter"},{name:"add-text",title:"添加时的文案提示",defaultValue:"添加联系人",setters:"StringSetter"},{name:"editable",title:"是否可以编辑联系人",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"click",params:["event"]}],snippet:{}},ke={name:"VanContactEdit",alias:"ContactEdit",label:"联系人编辑",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/contact-edit",props:[{name:"contact-info",title:"联系人信息",defaultValue:{},setters:"ObjectSetter"},{name:"is-edit",title:"是否为编辑联系人",defaultValue:!1,setters:"BooleanSetter"},{name:"is-saving",title:"是否显示保存按钮加载动画",defaultValue:!1,setters:"BooleanSetter"},{name:"is-deleting",title:"是否显示删除按钮加载动画",defaultValue:!1,setters:"BooleanSetter"},{name:"tel-validator",title:"手机号格式校验函数",setters:"FunctionSetter"},{name:"show-set-default",title:"是否显示默认联系人栏",defaultValue:!1,setters:"BooleanSetter"},{name:"set-default-label",title:"默认联系人栏文案",setters:"StringSetter"}],events:[{name:"save",params:["content"]},{name:"delete",params:["content"]},{name:"changeDefault",params:["checked"]}],snippet:{props:{"is-edit":!0,"show-set-default":!0,"contact-info":{tel:"",name:""},"set-default-label":"设为默认联系人"}}},ze={name:"VanContactList",alias:"ContactList",label:"联系人列表",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/contact-list",props:[{name:"modelValue",title:"当前选中联系人的 id",setters:["NumberSetter","StringSetter"]},{name:"list",title:"联系人列表",defaultValue:[],setters:"ArraySetter"},{name:"add-text",title:"新建按钮文案",defaultValue:"新建联系人",setters:"StringSetter"},{name:"default-tag-text",title:"默认联系人标签文案",setters:"StringSetter"}],events:[{name:"add"},{name:"edit",params:["contact","index"]},{name:"select",params:["contact","index"]},{name:"update:modelValue"}],snippet:{props:{modelValue:"1",list:[{id:"1",name:"张三",tel:"***********",isDefault:!0},{id:"2",name:"李四",tel:"1310000000"}],"default-tag-text":"默认"}}},Ce=[{name:"VanCouponList",alias:"CouponList",label:"优惠券列表",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/coupon",props:[{name:"code",title:"当前输入的兑换码",setters:"StringSetter"},{name:"chosen-coupon",title:"当前选中优惠券的索引,支持多选",defaultValue:-1,setters:["NumberSetter","ArraySetter"]},{name:"coupons",title:"可用优惠券列表",defaultValue:[],setters:"ArraySetter"},{name:"disabled-coupons",title:"不可用优惠券列表",defaultValue:[],setters:"ArraySetter"},{name:"enabled-title",title:"可用优惠券列表标题",defaultValue:"可使用优惠券",setters:"StringSetter"},{name:"disabled-title",title:"不可用优惠券列表标题",defaultValue:"不可使用优惠券",setters:"StringSetter"},{name:"exchange-button-text",title:"兑换按钮文字",defaultValue:"兑换",setters:"StringSetter"},{name:"exchange-button-loading",title:"是否显示兑换按钮加载动画",defaultValue:!1,setters:"BooleanSetter"},{name:"exchange-button-disabled",title:"是否禁用兑换按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"exchange-min-length",title:"兑换码最小长度",defaultValue:1,setters:"NumberSetter"},{name:"displayed-coupon-index",title:"滚动至特定优惠券位置",setters:"NumberSetter"},{name:"show-close-button",title:"是否显示列表底部按钮",defaultValue:!0,setters:"BooleanSetter"},{name:"close-button-text",title:"列表底部按钮文字",defaultValue:"不使用优惠",setters:"StringSetter"},{name:"input-placeholder",title:"输入框文字提示",defaultValue:"请输入优惠码",setters:"StringSetter"},{name:"show-exchange-bar",title:"是否展示兑换栏",defaultValue:!0,setters:"BooleanSetter"},{name:"currency",title:"货币符号",defaultValue:"￥",setters:"StringSetter"},{name:"empty-image",title:"列表为空时的占位图",setters:"StringSetter"},{name:"show-count",title:"是否展示可用 / 不可用数量",defaultValue:!0,setters:"BooleanSetter"}],events:[{name:"change",params:["index"]},{name:"exchange",params:["code"]}],slots:["list-footer","disabled-list-footer","list-button"],snippet:{props:{coupons:[{available:1,condition:`无门槛
最多优惠12元`,reason:"",value:150,name:"优惠券名称",startAt:1489104e3,endAt:1514592e3,valueDesc:"1.5",unitDesc:"元"}],"chosen-coupon":-1,"disabled-coupons":[{available:1,condition:`无门槛
最多优惠12元`,reason:"",value:150,name:"优惠券名称",startAt:1489104e3,endAt:1514592e3,valueDesc:"1.5",unitDesc:"元"}]}}},{name:"VanCouponCell",alias:"CouponCell",label:"优惠券单元格",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/coupon",props:[{name:"title",title:"单元格标题",defaultValue:"优惠券",setters:"StringSetter"},{name:"chosen-coupon",title:"当前选中优惠券的索引",defaultValue:-1,setters:["NumberSetter","ArraySetter"]},{name:"coupons",title:"可用优惠券列表",defaultValue:[],setters:"ArraySetter"},{name:"editable",title:"能否切换优惠券",defaultValue:!0,setters:"BooleanSetter"},{name:"border",title:"是否显示内边框",defaultValue:!0,setters:"BooleanSetter"},{name:"currency",title:"货币符号",defaultValue:"￥",setters:"StringSetter"}],snippet:{props:{coupons:[{available:1,condition:`无门槛
最多优惠12元`,reason:"",value:150,name:"优惠券名称",startAt:1489104e3,endAt:1514592e3,valueDesc:"1.5",unitDesc:"元"}],"chosen-coupon":-1}}}],Ae={name:"VanSubmitBar",alias:"SubmitBar",label:"提交订单栏",categoryId:"business",doc:"https://vant-ui.github.io/vant/#/zh-CN/submit-bar",props:[{name:"price",title:"金额（单位分）",setters:"NumberSetter"},{name:"decimal-length",title:"金额小数点位数",defaultValue:2,setters:["NumberSetter","StringSetter"]},{name:"label",title:"金额左侧文案",defaultValue:"	合计：",setters:"StringSetter"},{name:"suffix-label",title:"金额右侧文案",setters:"StringSetter"},{name:"text-align",title:"金额文案对齐方向，可选值为 left",defaultValue:"right",setters:"SelectSetter",options:["left","right"]},{name:"button-text",title:"按钮文字",setters:"StringSetter"},{name:"button-type",title:"按钮类型",defaultValue:"danger",setters:"StringSetter"},{name:"button-color",title:"自定义按钮颜色",setters:"StringSetter"},{name:"tip",title:"	在订单栏上方的提示文案",setters:"StringSetter"},{name:"tip-icon",title:"提示文案左侧的图标名称或图片链接，等同于 Icon 组件的 name 属性",setters:["VanIconSetter","StringSetter"]},{name:"currency",title:"货币符号",defaultValue:"￥",setters:"StringSetter"},{name:"disabled",title:"是否禁用按钮",defaultValue:!1,setters:"BooleanSetter"},{name:"loading",title:"是否显示将按钮显示为加载中状态",defaultValue:!1,setters:"BooleanSetter"},{name:"safe-area-inset-bottom",title:"是否开启底部安全区适配",defaultValue:!0,setters:"BooleanSetter"},{name:"placeholder",title:"是否在标签位置生成一个等高的占位元素",defaultValue:!1,setters:"BooleanSetter"}],events:["submit"],slots:["default","button","top","tip"],snippet:{props:{price:3050,"button-text":"提交订单"}}},Te="vant",je=[a,i,o,u,m,d,S,c,p,f,g,V,h,b,v,x,y,B,N,w,I,k,z,C,A,T,j,O,P,E,F,D,L,R,q,M,H,G,J,U,W,K,_,Q,$,X,Y,Z,ee,te,ae,re,le,ne,se,ie,oe,ue,me,de,Se,ce,pe,fe,ge,Ve,he,be,ve,xe,ye,Be,Ne,we,Ie,ke,ze,Ce,Ae].flat();return{name:"vant",version:e,label:"Vant",library:"VantMaterial",order:5,categories:[{id:"base",category:"基础组件"},{id:"form",category:"表单组件"},{id:"feedback",category:"反馈组件"},{id:"view",category:"展示组件"},{id:"nav",category:"导航组件"},{id:"business",category:"业务组件"}],components:t(je,Te)}});
