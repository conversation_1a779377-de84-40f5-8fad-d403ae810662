import { Plugin, InjectionKey } from 'vue';
import { GridCustomInfo, BuiltinFieldEditor } from './components';
import { VXETableConfigOptions } from 'vxe-table';
export declare const ADAPTER_KEY: InjectionKey<Adapter>;
export interface UploaderResponse {
    url: string;
    name?: string;
    [index: string]: any;
}
export type Uploader = (file: File) => Promise<string | UploaderResponse | null>;
export declare function useAdapter(): Adapter;
export interface Adapter {
    components?: any[];
    fieldEditors?: Record<string, BuiltinFieldEditor>;
    uploader?: Uploader;
    vxeConfig?: VXETableConfigOptions;
    vxePlugin?: any;
    getCustom?: (id: string) => Promise<GridCustomInfo>;
    saveCustom?: (info: GridCustomInfo) => Promise<any>;
    [index: string]: any;
}
export declare const AdapterPlugin: Plugin;
