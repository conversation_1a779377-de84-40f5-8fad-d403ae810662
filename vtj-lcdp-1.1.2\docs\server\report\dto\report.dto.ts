import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ReportType } from '../../shared';
export class ReportDto {
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  sessionId: string;

  @IsOptional()
  projectId?: string;

  @IsOptional()
  projectUid?: string;

  @IsOptional()
  userId?: string;

  @IsOptional()
  userName?: string;

  @IsNotEmpty()
  @IsEnum(ReportType)
  type: ReportType;

  @IsNotEmpty()
  engineVersion: string;

  @IsOptional()
  ip?: string;

  @IsOptional()
  host?: string;

  @IsOptional()
  url?: string;

  @IsOptional()
  referrer?: string;

  @IsOptional()
  os?: string;

  @IsOptional()
  osVersion?: string;

  @IsOptional()
  browser?: string;

  @IsOptional()
  browserVersion?: string;

  @IsOptional()
  message?: string;

  @IsOptional()
  stack?: string;

  @IsOptional()
  @IsJSON()
  source?: object;
}
