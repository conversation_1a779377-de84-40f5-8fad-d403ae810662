import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccessService } from './access.service';
import { AccessController } from './access.controller';
import { Access, Api } from './entities/access.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Access, Api])],
  controllers: [AccessController],
  providers: [TypeOrmModule, AccessService],
  exports: [TypeOrmModule, AccessService]
})
export class AccessModule {}
